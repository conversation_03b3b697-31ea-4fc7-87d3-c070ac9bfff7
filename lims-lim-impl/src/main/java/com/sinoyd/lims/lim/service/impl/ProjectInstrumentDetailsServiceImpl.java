package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.criteria.ProjectInstrumentDetailsCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoProjectInstrument;
import com.sinoyd.lims.lim.dto.lims.DtoProjectInstrumentDetails;
import com.sinoyd.lims.lim.dto.customer.DtoInstrumentCheckOut;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.lim.repository.lims.ProjectInstrumentDetailsRepository;
import com.sinoyd.lims.lim.repository.lims.ProjectInstrumentRepository;
import com.sinoyd.lims.lim.service.ProjectInstrumentDetailsService;
import com.sinoyd.lims.lim.service.ProjectInstrumentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * ProjectInstrumentDetails操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
@Service
public class ProjectInstrumentDetailsServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoProjectInstrumentDetails, String, ProjectInstrumentDetailsRepository> implements ProjectInstrumentDetailsService {

    private ProjectInstrumentRepository projectInstrumentRepository;

    private ProjectInstrumentService projectInstrumentService;

    private PersonRepository personRepository;

    private JdbcTemplate jdbcTemplate;


    @Override
    public void findByPage(PageBean<DtoProjectInstrumentDetails> pb, BaseCriteria projectInstrumentDetailsCriteria) {
        pb.setEntityName("DtoProjectInstrumentDetails a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, projectInstrumentDetailsCriteria);
    }


    @Override
    @Transactional
    public int addDetailsBatch(DtoProjectInstrumentDetails projectInstrumentDetails) {
        String projectInstrumentId = projectInstrumentDetails.getProjectInstrumentId();
        DtoProjectInstrument projectInstrument = projectInstrumentRepository.findOne(projectInstrumentId);
        if (StringUtil.isNull(projectInstrument) || projectInstrument.getIsDeleted()) {
            throw new BaseException("仪器出入库记录不存在！");
        }
        //出库日期
        Date outDate = projectInstrumentDetails.getOutDate();
        //出库情况
        Integer outQualified = projectInstrumentDetails.getOutQualified();
        //出库人
        String outPerson = projectInstrumentDetails.getOutPerson();
        List<String> instrumentIdList = projectInstrumentDetails.getInstrumentIdList();
        List<DtoProjectInstrumentDetails> insertDetailList = new ArrayList<>();
        for (String instrumentId : instrumentIdList) {
            DtoProjectInstrumentDetails insertDetails = new DtoProjectInstrumentDetails();
            insertDetails.setProjectInstrumentId(projectInstrumentId);
            insertDetails.setInstrumentId(instrumentId);
            insertDetails.setIsStorage(false);
            insertDetails.setOutDate(outDate);
            insertDetails.setOutQualified(outQualified);
            insertDetails.setOutPerson(outPerson);
            insertDetailList.add(insertDetails);
        }
        int insertCnt = 0;
        if (StringUtil.isNotEmpty(insertDetailList)) {
            List<DtoProjectInstrumentDetails> resList = repository.save(insertDetailList);
            insertCnt = insertDetailList.size();
            //如果出库情况为不合格，且仪器出入库记录的出库情况为合格，则需要修改出入库记录主表的出库情况为不合格
            if (insertCnt > 0 && outQualified == 0 && projectInstrument.getOutQualified() == 1) {
                projectInstrument.setOutQualified(0);
                projectInstrumentRepository.save(projectInstrument);
            } else {
                projectInstrument.setOutQualified(1);
                projectInstrumentRepository.save(projectInstrument);
            }
        }
        return insertCnt;
    }

    @Override
    @Transactional
    public DtoProjectInstrumentDetails updateDetail(DtoProjectInstrumentDetails projectInstrumentDetails) {
        String detailsId = projectInstrumentDetails.getId();
        DtoProjectInstrumentDetails oriDetails = repository.findOne(detailsId);
        if (StringUtil.isNull(oriDetails)) {
            throw new BaseException("出入库明细不存在！");
        }
        DtoProjectInstrument projectInstrument = projectInstrumentRepository.findOne(oriDetails.getProjectInstrumentId());
        if (StringUtil.isNull(projectInstrument) || projectInstrument.getIsDeleted()) {
            throw new BaseException("出入库主表信息不存在！");
        }
        //判断是否需要做入库操作，（该条明细未入库，并且修改了入库日期或者入库情况则需要做入库操作）
        boolean inFlag = false;
        //需求改动，修改入库日期或者入库情况时，不用考虑做入库操作
//        if (!oriDetails.getIsStorage() && (StringUtil.isNotNull(projectInstrumentDetails.getInDate()) || StringUtil.isNotNull(projectInstrumentDetails.getInQualified()))) {
//            inFlag = true;
//        }
        //从前端参数中载入需要更新的出入库明细属性
        loadDetailsForUpdate(projectInstrumentDetails, oriDetails);
        DtoProjectInstrumentDetails resDetails = super.update(oriDetails);
        if (inFlag) {
            //入库
            oriDetails.setInstrumentIdList(Collections.singletonList(oriDetails.getInstrumentId()));
            projectInstrumentService.instrumentIn(oriDetails);
        }
        //获取所有明细
        List<DtoProjectInstrumentDetails> oriDetailsList = repository.findByProjectInstrumentId(projectInstrument.getId());
        //过滤掉当前修改的这条明细
        oriDetailsList = oriDetailsList.stream().filter(p -> !detailsId.equals(p.getId())).collect(Collectors.toList());
        //统计出入库情况的不合格数量以及合格数量
        int[] cntArr = calcInOutNotQualifiedCnt(oriDetailsList);
        if (oriDetails.getInQualified() == 0) {
            cntArr[1]++;
        } else if (oriDetails.getInQualified() == 1) {
            cntArr[3]++;
        }
        if (oriDetails.getOutQualified() == 0) {
            cntArr[0]++;
        } else {
            cntArr[2]++;
        }
        //判断是否需要修改出入库主表的出入库情况
        if (checkInOutQualify(projectInstrument, cntArr[0], cntArr[1], cntArr[3], cntArr[2])) {
            projectInstrumentRepository.save(projectInstrument);
        }
        return resDetails;
    }

    @Override
    @Transactional
    public int deleteDetailsBatch(List<String> ids) {
        List<DtoProjectInstrumentDetails> oriDetailsList = repository.findAll(ids);
        if (StringUtil.isNotEmpty(oriDetailsList)) {
            String projectInstrumentId = oriDetailsList.get(0).getProjectInstrumentId();
            repository.delete(oriDetailsList);
            DtoProjectInstrument projectInstrument = projectInstrumentRepository.findOne(projectInstrumentId);
            if (StringUtil.isNotNull(projectInstrument)) {
                //找到所有明细
                List<DtoProjectInstrumentDetails> detailsList = repository.findByProjectInstrumentId(projectInstrumentId);
                //统计出入库情况的不合格数量
                int[] cntArr = calcInOutNotQualifiedCnt(detailsList);
                //判断是否需要修改出入库主表的出入库情况
                if (checkInOutQualify(projectInstrument, cntArr[0], cntArr[1], cntArr[3], cntArr[2])) {
                    projectInstrumentRepository.save(projectInstrument);
                }
            }
        }
        return oriDetailsList.size();
    }

    /**
     * 查询所有的出库数据
     *
     * @param pageBean 分页数据
     * @param criteria 分页查询条件
     */
    @Override
    public void findInstrumentStorage(PageBean<DtoInstrumentCheckOut> pageBean, BaseCriteria criteria) {
        //获取查询条件
        ProjectInstrumentDetailsCriteria detailsCriteria = (ProjectInstrumentDetailsCriteria) criteria;
        //声明查询语句
        StringBuilder sql = new StringBuilder("select a.id,d.instrumentName,a.instrumentId,d.model,a.outDate,a.outQualified,b.projectId,b.projectName as insProjectName," +
                "b.projectId as insProjectId, c.projectName,c.projectCode,e.leaderId,a.isConfirm,a.outPerson,d.instrumentsCode,d.serialNo");
        sql.append(" from TB_LIM_ProjectInstrumentDetails a")
                .append(" left join TB_LIM_ProjectInstrument b on a.projectInstrumentId = b.id")
                .append(" left join TB_PRO_Project c on b.projectId = c.id")
                .append(" left join TB_BASE_Instrument d on a.instrumentId = d.id")
                .append(" left join TB_PRO_ProjectPlan e on e.projectId = c.id")
                .append(" where 1=1");
        List<Object> values = new ArrayList<>();
        if (StringUtil.isNotEmpty(detailsCriteria.getId())) {
            sql.append(" and a.id = ? ");
            values.add(detailsCriteria.getId());
        }
        if (StringUtils.isNotNullAndEmpty(detailsCriteria.getDtBegin())) {
            Date from = DateUtil.stringToDate(detailsCriteria.getDtBegin(), DateUtil.YEAR);
            sql.append(" and a.outDate >= ? ");
            values.add(DateUtil.dateToString(from,DateUtil.FULL));
        }
        if (StringUtils.isNotNullAndEmpty(detailsCriteria.getDtEnd())) {
            Date to = DateUtil.stringToDate(detailsCriteria.getDtEnd(), DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            sql.append(" and a.outDate < ? ");
            values.add(DateUtil.dateToString(c.getTime(),DateUtil.FULL));
        }
        if (StringUtil.isNotEmpty(detailsCriteria.getOutPerson())){
            sql.append(" and a.outPerson like ? ");
            values.add("%"+detailsCriteria.getOutPerson()+"%");
        }
        if (StringUtil.isNotEmpty(detailsCriteria.getInstrumentKey())){
            sql.append(" and (d.instrumentName like ?  or d.model like ?) ");
            values.add("%"+detailsCriteria.getInstrumentKey()+"%");
            values.add("%"+detailsCriteria.getInstrumentKey()+"%");
        }
        if (StringUtils.isNotNullAndEmpty(detailsCriteria.getIsConfirm())){
            sql.append(" and a.isConfirm = ? ");
            values.add(detailsCriteria.getIsConfirm()?1:0);
        }
        //排序：日期倒序，项目编号，仪器名称顺序
        sql.append(" order by a.outDate desc,c.projectCode asc,d.instrumentName asc");
        //获取分页页数以及每页行数
        int pageNo = pageBean.getPageNo();
        int rowsCount = pageBean.getRowsPerPage();
        //查询数据
        List<DtoInstrumentCheckOut> checkOuts = jdbcTemplate.query(sql.toString(), values.toArray(), (resultSet, i) -> new DtoInstrumentCheckOut(
                resultSet.getString("id"),
                resultSet.getString("instrumentName"),
                resultSet.getString("instrumentId"),
                resultSet.getString("model"),
                resultSet.getDate("outDate"),
                resultSet.getInt("outQualified"),
                resultSet.getString("projectId"),
                resultSet.getString("insProjectName"),
                resultSet.getString("insProjectId"),
                resultSet.getString("projectName"),
                resultSet.getString("projectCode"),
                resultSet.getString("leaderId"),
                resultSet.getInt("isConfirm"),
                resultSet.getString("outPerson"),
                resultSet.getString("instrumentsCode"),
                resultSet.getString("serialNo")
        ));
        //获取所有人员信息并按id分组
        List<DtoPerson> personList = personRepository.findAll();
        Map<String, List<DtoPerson>> personGroup = personList.stream().collect(Collectors.groupingBy(DtoPerson::getId));

        Map<String, String> id2CodeMap = getProjectId2CodeMap(checkOuts);
        //处理人员名称以及其他数据
        for (DtoInstrumentCheckOut checkOut : checkOuts) {
            //处理项目名称
//            if (StringUtil.isEmpty(checkOut.getProjectName())) {
//                checkOut.setProjectName(checkOut.getInsProjectName());
//            }
            checkOut.setProjectName(checkOut.getInsProjectName());
            //处理项目编号
            if (StringUtil.isEmpty(checkOut.getProjectCode()) && StringUtil.isNotEmpty(checkOut.getInsProjectId())) {
                List<String> ids = new ArrayList<>(Arrays.asList(checkOut.getInsProjectId().split(";")));
                ids = ids.stream().filter(StringUtil::isNotEmpty).collect(Collectors.toList());
                checkOut.setProjectCode(getProjectCodeStr(ids, id2CodeMap));
            }
            checkOut.setOutDateStr(DateUtil.dateToString(checkOut.getOutDate(), DateUtil.YEAR));
            if (StringUtil.isNotEmpty(personGroup)) {
                //项目负责人
                List<DtoPerson> personOfProject = personGroup.get(checkOut.getLeaderId());
                Optional<DtoPerson> personOp = StringUtil.isNotEmpty(personOfProject) ? personOfProject.stream().findFirst() : Optional.empty();
                checkOut.setLeaderName(personOp.isPresent() ? personOp.get().getCName() : "");
                //出库人
                List<DtoPerson> personOfOut = personGroup.get(checkOut.getOutPersonId());
                Optional<DtoPerson> outPersonOp = StringUtil.isNotEmpty(personOfOut) ? personOfOut.stream().findFirst() : Optional.empty();
                checkOut.setOutPerson(outPersonOp.isPresent() ? outPersonOp.get().getCName() : "");
            }
            //处理出库合格情况
            if (StringUtil.isNotNull(checkOut.getOutQualified())) {
                checkOut.setOutQualifiedName(EnumLIM.EnumQualified.getName(checkOut.getOutQualified()));
                //如果出库情况为-1，则处理为合格状态
                if (checkOut.getOutQualified() == -1) {
                    checkOut.setOutQualified(EnumLIM.EnumQualified.合格.getValue());
                    checkOut.setOutQualifiedName(EnumLIM.EnumQualified.合格.name());
                }
            }
        }
        //项目名称，项目编号检索  上面的sql有问题，TB_LIM_ProjectInstrument的projectId是拼接出来的
        if(StringUtil.isNotEmpty(detailsCriteria.getProjectKey())){
            checkOuts = checkOuts.stream().filter(v->(StringUtil.isNotEmpty(v.getProjectCode())&&v.getProjectCode().contains(detailsCriteria.getProjectKey()))
                    ||(StringUtil.isNotEmpty(v.getProjectName())&&v.getProjectName().contains(detailsCriteria.getProjectKey()))).collect(Collectors.toList());
        }
        //放入总数量
        pageBean.setRowsCount(checkOuts.size());
        //取出分页数据
        if (StringUtil.isNotNull(pageNo) && StringUtil.isNotNull(rowsCount)) {
            checkOuts = checkOuts.stream().skip((long) (pageNo - 1) * rowsCount).limit(rowsCount).collect(Collectors.toList());
        }
        //放入返回数据
        pageBean.setData(checkOuts);
    }

    /**
     * 获取项目id和项目编号的映射关系
     */
    private Map<String, String> getProjectId2CodeMap(List<DtoInstrumentCheckOut> checkOuts) {
        Set<String> projectIdSet = new HashSet<>();
        for (DtoInstrumentCheckOut checkOut : checkOuts) {
            if (StringUtil.isNotEmpty(checkOut.getInsProjectId())) {
                String[] ids = checkOut.getInsProjectId().split(";");
                projectIdSet.addAll(new ArrayList<>(Arrays.asList(ids)));
            }
        }
        Map<String, String> id2CodeMap = new HashMap<>();
        projectIdSet = projectIdSet.stream().filter(StringUtil::isNotEmpty).collect(Collectors.toSet());
        if (StringUtil.isNotEmpty(projectIdSet)) {
            List<Object[]> objects = comRepository.find("select id, projectCode from DtoProject where id in :ids and isDeleted = 0",
                    Collections.singletonMap("ids", projectIdSet));
            for (Object[] obj : objects) {
                id2CodeMap.put(obj[0].toString(), obj[1].toString());
            }
        }
        return id2CodeMap;
    }

    /**
     * 获取项目编号字符串列表
     */
    private String getProjectCodeStr(List<String> ids, Map<String, String> id2CodeMap) {
        List<String> codeList = new ArrayList<>();
        for (String id : ids) {
            if (id2CodeMap.containsKey(id)) {
                codeList.add(id2CodeMap.get(id));
            }
        }
        return String.join("、", codeList);
    }

    /**
     * 批量出库确认/取消确认
     *
     * @param isConfirm 是否为确认操作
     * @param ids       出入库明细id
     */
    @Override
    @Transactional
    public void batchOperationConfirm(Boolean isConfirm, List<String> ids) {
        if (StringUtil.isEmpty(ids)) {
            throw new BaseException("未选择删除数据!");
        }
        List<DtoProjectInstrumentDetails> detailsList = repository.findAll(ids);
        if (StringUtil.isNotEmpty(detailsList)) {
            for (DtoProjectInstrumentDetails dto : detailsList) {
                dto.setIsConfirm(isConfirm ? 1 : 0);
            }
        }
        super.update(detailsList);
    }


    /**
     * 统计出入库情况的不合格数量
     *
     * @param detailsList 出入库明细列表
     * @return 第一个元素为出库不合格数量, 第二个元素为入库不合格数量,第三个元素为出库合格数量, 第四个元素为入库合格数量,
     */
    private static int[] calcInOutNotQualifiedCnt(List<DtoProjectInstrumentDetails> detailsList) {
        int outNotQualify = 0;
        int inNotQualify = 0;
        int outQualify = 0;
        int inQualify = 0;
        for (DtoProjectInstrumentDetails details : detailsList) {
            if (details.getOutQualified() == 0) {
                outNotQualify++;
            } else {
                outQualify++;
            }
            if (details.getInQualified() == 0) {
                inNotQualify++;
            } else if (details.getInQualified() == 1) {
                inQualify++;
            }
        }
        int[] resArr = new int[4];
        resArr[0] = outNotQualify;
        resArr[1] = inNotQualify;
        resArr[2] = outQualify;
        resArr[3] = inQualify;
        return resArr;
    }

    /**
     * 检查并修改出入库记录主表的出入库情况
     *
     * @param projectInstrument 出入库主表对象
     * @param outNotCnt         出库情况为不合格的数量
     * @param inNotCnt          入库情况为不合格的数量
     * @param inCnt             入库情况为合格的数量
     * @param outCnt            出库情况为合格的数量
     * @return 是否需要修改出入库情况
     */
    private boolean checkInOutQualify(DtoProjectInstrument projectInstrument, int outNotCnt, int inNotCnt, int inCnt, int outCnt) {
        //判断是否需要修改出入库主表的出入库情况
        boolean updateFlag = false;
        //检查出库情况
        int outQualified = projectInstrument.getOutQualified();
        if (checkQuality(projectInstrument, outQualified, outNotCnt, outCnt, 1)) {
            updateFlag = true;
        }
        //检查入库情况
        int inQualified = projectInstrument.getIntQualified();
        if (checkQuality(projectInstrument, inQualified, inNotCnt, inCnt, 2)) {
            updateFlag = true;
        }
        return updateFlag;
    }

    /**
     * 判断并修改出入库记录主表的出/入库情况
     *
     * @param projectInstrument 出入库主表对象
     * @param oriQualified      原本的出/入库情况
     * @param notCnt            出/入库情况为不合格的数量
     * @param cnt               出/入库情况为合格的数量
     * @param type              1:出库情况 2：入库情况
     * @return 是否需要修改出入库情况
     */
    private boolean checkQuality(DtoProjectInstrument projectInstrument, int oriQualified, int notCnt, int cnt, int type) {
        boolean updateFlag = false;
        //出/入库情况根据出/入库情况为不合格的仪器数量进行判断
        if (notCnt > 0) {
            if (oriQualified != 0) {
                updateFlag = true;
                setInOutQualified(projectInstrument, 0, type);
            }
        } else if (cnt > 0) {
            if (oriQualified != 1) {
                updateFlag = true;
                setInOutQualified(projectInstrument, 1, type);
            }
        } else {
            if (oriQualified != -1) {
                updateFlag = true;
                setInOutQualified(projectInstrument, -1, type);
            }
        }
        return updateFlag;
    }

    /**
     * 设置出入库情况
     *
     * @param projectInstrument 出入库主表对象
     * @param qualified         合格情况
     * @param type              1:出库情况 2：入库情况
     */
    private void setInOutQualified(DtoProjectInstrument projectInstrument, int qualified, int type) {
        if (type == 1) {
            projectInstrument.setOutQualified(qualified);
        } else if (type == 2) {
            projectInstrument.setIntQualified(qualified);
        }
    }

    /**
     * 从前端参数中载入需要更新的出入库明细属性
     *
     * @param projectInstrumentDetails 前端传递的对象
     * @param oriDetails               原有的对象
     */
    private void loadDetailsForUpdate(DtoProjectInstrumentDetails projectInstrumentDetails, DtoProjectInstrumentDetails oriDetails) {
        if (StringUtil.isNotNull(projectInstrumentDetails.getInQualified())) {
            oriDetails.setInQualified(projectInstrumentDetails.getInQualified());
        }
        if (StringUtil.isNotNull(projectInstrumentDetails.getOutQualified())) {
            oriDetails.setOutQualified(projectInstrumentDetails.getOutQualified());
        }
        if (StringUtil.isNotNull(projectInstrumentDetails.getInDate()) && !DateUtil.dateToString(oriDetails.getInDate(), DateUtil.FULL)
                .equals(DateUtil.dateToString(projectInstrumentDetails.getInDate(), DateUtil.FULL))) {
            oriDetails.setInDate(projectInstrumentDetails.getInDate());
        }
        if (StringUtil.isNotNull(projectInstrumentDetails.getOutDate()) && !DateUtil.dateToString(oriDetails.getOutDate(), DateUtil.FULL).equals(DateUtil.dateToString(projectInstrumentDetails.getOutDate(), DateUtil.FULL))) {
            oriDetails.setOutDate(projectInstrumentDetails.getOutDate());
        }
        if (StringUtil.isNotNull(projectInstrumentDetails.getInPerson())) {
            oriDetails.setInPerson(projectInstrumentDetails.getInPerson());
        }
        if (StringUtil.isNotNull(projectInstrumentDetails.getOutPerson())) {
            oriDetails.setOutPerson(projectInstrumentDetails.getOutPerson());
        }
        if (StringUtil.isNotNull(projectInstrumentDetails.getIsConfirm())) {
            oriDetails.setIsConfirm(projectInstrumentDetails.getIsConfirm());
        }
    }

    @Autowired
    public void setProjectInstrumentRepository(ProjectInstrumentRepository projectInstrumentRepository) {
        this.projectInstrumentRepository = projectInstrumentRepository;
    }

    @Autowired
    @Lazy
    public void setProjectInstrumentService(ProjectInstrumentService projectInstrumentService) {
        this.projectInstrumentService = projectInstrumentService;
    }

    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }

    @Autowired
    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }
}