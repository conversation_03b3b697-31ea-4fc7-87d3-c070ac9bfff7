package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

/**
 * 首页样品统计
 * <AUTHOR>
 * @version V1.0.0 2020/5/9
 * @since V100R001
 */
@Data
public class DtoHomeSampleStat {
    /**
     * 状态
     */
    private String status;

    /**
     * 个数
     */
    private Integer count = 0;

    /**
     * 排序值
     */
    private Integer orderNum;

    /**
     * 构造
     */
    public DtoHomeSampleStat(){

    }

    /**
     * 构造函数
     * @param status 状态
     * @param count 个数
     */
    public DtoHomeSampleStat(String status,Integer count,Integer orderNum){
        this.status = status;
        this.count = count;
        this.orderNum = orderNum;
    }
}
