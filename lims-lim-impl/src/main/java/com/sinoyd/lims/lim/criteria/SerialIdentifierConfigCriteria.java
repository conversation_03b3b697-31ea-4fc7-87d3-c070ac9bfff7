package com.sinoyd.lims.lim.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * SerialIdentifierConfig查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SerialIdentifierConfigCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 配置类型 项目编号=1，样品编号=2，质控样编号=3，送样单编号=4，报告编号=5，检测单编号=6
     */
    private Integer configType;


    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();

        if (StringUtil.isNotNull(this.configType)) {
            condition.append(" and configType = :configType");
            values.put("configType", this.configType);
        }
        return condition.toString();
    }
}