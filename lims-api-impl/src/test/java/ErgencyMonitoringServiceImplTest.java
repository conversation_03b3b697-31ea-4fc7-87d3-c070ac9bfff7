//import com.sinoyd.base.dto.lims.DtoDocument;
//import com.sinoyd.base.dto.rcc.DtoSampleType;
//import com.sinoyd.base.service.DocumentService;
//import com.sinoyd.base.service.SampleTypeService;
//import com.sinoyd.boot.frame.sys.model.DepartmentModel;
//import com.sinoyd.frame.dto.DtoDepartment;
//import com.sinoyd.frame.service.DepartmentService;
//import com.sinoyd.frame.util.UUIDHelper;
//import com.sinoyd.lims.api.dto.DtoPersonPhone;
//import com.sinoyd.lims.api.dto.DtoReceiveSampleRecordPhone;
//import com.sinoyd.lims.api.service.impl.ErgencyMonitoringServiceImpl;
//import com.sinoyd.lims.lim.dto.customer.DtoGenerateSN;
//import com.sinoyd.lims.lim.dto.lims.DtoPerson;
//import com.sinoyd.lims.lim.dto.lims.DtoTest;
//import com.sinoyd.lims.lim.dto.rcc.*;
//import com.sinoyd.lims.lim.repository.lims.PersonRepository;
//import com.sinoyd.lims.lim.repository.lims.TestRepository;
//import com.sinoyd.lims.lim.repository.rcc.VersionInfoRepository;
//import com.sinoyd.lims.lim.service.ProjectTypeService;
//import com.sinoyd.lims.pro.dto.*;
//import com.sinoyd.lims.pro.dto.customer.DtoLoadScheme;
//import com.sinoyd.lims.pro.dto.customer.DtoReceiveSampleRecordTemp;
//import com.sinoyd.lims.pro.dto.customer.DtoSampleFolderTemp;
//import com.sinoyd.lims.pro.repository.*;
//import com.sinoyd.lims.pro.service.*;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.mockito.MockitoAnnotations;
//import org.mockito.runners.MockitoJUnitRunner;
//import org.springframework.jdbc.datasource.DataSourceTransactionManager;
//
//import java.util.*;
//
//import static org.mockito.Mockito.when;
//
//@RunWith(MockitoJUnitRunner.class)
//public class ErgencyMonitoringServiceImplTest {
//
//    // @InjectMock 注入被测对象, 一般写在最前面
//    @InjectMocks
//    ErgencyMonitoringServiceImpl ergencyMonitoringServiceImpl;
//
//    @Mock
//    private ProjectService projectService;
//
//    @Mock
//    private VersionInfoRepository versionInfoRepository;
//
//    @Mock
//    private SampleFolderService sampleFolderService;
//
//    @Mock
//    private SampleRepository sampleRepository;
//
//    @Mock
//    private DocumentService documentService;
//
//    @Mock
//    private ReceiveSampleRecordRepository receiveSampleRecordRepository;
//
//    @Mock
//    private TestRepository testRepository;
//
//    @Mock
//    private SampleTypeService sampleTypeService;
//
//    @Mock
//    private ProjectTestService projectTestService;
//
//    @Mock
//    private ProjectRepository projectRepository;
//
//    @Mock
//    private SchemeService schemeService;
//
//    @Mock
//    private ProService proService;
//
//    @Mock
//    private AnalyseDataService analyseDataService;
//
//    @Mock
//    private AnalyseDataRepository analyseDataRepository;
//
//    @Mock
//    private SampleFolderRepository sampleFolderRepository;
//
//    @Mock
//    private NewLogService newLogService;
//
//    @Mock
//    private SampleService sampleService;
//
//    @Mock
//    private ProjectTypeService projectTypeService;
//
//    @Mock
//    private ReceiveSubSampleRecordRepository receiveSubSampleRecordRepository;
//
//    @Mock
//    private PersonRepository personRepository;
//
//    @Mock
//    private DepartmentService departmentService;
//
//    @Mock
//    private DataSourceTransactionManager dataSourceTransactionManager;
//
//    // 执行测试之前执行
//    @Before
//    public void setup() {
//        // 初始化 mock 注入环境
//        MockitoAnnotations.initMocks(this);
//    }
//
//    @Test
//    public void testSaveOutsideProject() {
//        // 断言：ergencyMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(ergencyMonitoringServiceImpl);
//
//        DtoReceiveSampleRecordPhone receiveSampleRecordPhone = new DtoReceiveSampleRecordPhone();
//        DtoReceiveSampleRecordTemp recordTemp = new DtoReceiveSampleRecordTemp();
//        DtoReceiveSampleRecordTemp receiveSampleRecordTemp = new DtoReceiveSampleRecordTemp();
//        recordTemp.setProjectCode("testCode");
//        String inceptPersonId = "personId";
//
//        when(projectService.saveOutsideSendSample(Mockito.any(DtoReceiveSampleRecordTemp.class))).thenReturn(recordTemp);
//
//
//        DtoReceiveSampleRecordPhone dtoReceiveSampleRecordPhone = ergencyMonitoringServiceImpl.saveOutsideProject(receiveSampleRecordPhone);
//        // 断言：判断成功
//        Assert.assertEquals("streamConfig not match", "testCode", dtoReceiveSampleRecordPhone.getProjectCode());
//    }
//
//    @Test
//    public void testUpdateOutsideProject() {
//        // 断言：ergencyMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(ergencyMonitoringServiceImpl);
//
//        DtoReceiveSampleRecordPhone receiveSampleRecordPhone = new DtoReceiveSampleRecordPhone();
//        receiveSampleRecordPhone.setProjectCode("testCode");
//        DtoReceiveSampleRecordTemp recordTemp = new DtoReceiveSampleRecordTemp();
//        DtoReceiveSampleRecordTemp receiveSampleRecordTemp = new DtoReceiveSampleRecordTemp();
//        recordTemp.setProjectCode("testCode");
//        String inceptPersonId = "personId";
//
//        when(projectService.updateOutsideSendSample(Mockito.any(DtoReceiveSampleRecordTemp.class))).thenReturn(receiveSampleRecordTemp);
//
//
//        DtoReceiveSampleRecordPhone dtoReceiveSampleRecordPhone = ergencyMonitoringServiceImpl.updateOutsideProject(receiveSampleRecordPhone);
//        // 断言：判断成功
//        Assert.assertEquals("project not match", "testCode", dtoReceiveSampleRecordPhone.getProjectCode());
//    }
//
//
//    @Test
//    public void testGetVersionInfo() {
//        // 断言：ergencyMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(ergencyMonitoringServiceImpl);
//
//        DtoVersionInfo versionInfo = new DtoVersionInfo();
//        List<DtoVersionInfo> versionInfoList = new ArrayList<>();
//        versionInfoList.add(versionInfo);
//        versionInfo.setVerTime(new Date());
//        versionInfo.setId("tstId");
//        String type = "tstType";
//        DtoDocument document = new DtoDocument();
//        List<DtoDocument> dtoDocumentList = new ArrayList<>();
//        dtoDocumentList.add(document);
//
//        when(versionInfoRepository.findByVerType(type)).thenReturn(versionInfoList);
//        when(documentService.findByObjectId(versionInfo.getId())).thenReturn(dtoDocumentList);
//
//
//        DtoVersionInfo resVersionInfo = ergencyMonitoringServiceImpl.getVersionInfo(type);
//        // 断言：判断成功
//        Assert.assertEquals("versionInfo not match", "tstId", resVersionInfo.getId());
//    }
//
//    @Test
//    public void testSaveSampleList() {
//
//        // 断言：ergencyMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(ergencyMonitoringServiceImpl);
//
//        DtoSampleFolder dtoSampleFolder = new DtoSampleFolder();
//        dtoSampleFolder.setProjectId("tstProjectId");
//        dtoSampleFolder.setPeriodCount(1);
//        dtoSampleFolder.setTimePerPeriod(1);
//        dtoSampleFolder.setId("tstFolderId");
//        List<DtoTest> testList = new ArrayList<>();
//        DtoTest test = new DtoTest();
//        testList.add(test);
//        DtoReceiveSampleRecord receiveSampleRecord = new DtoReceiveSampleRecord();
//        receiveSampleRecord.setId("tstId");
//        List<DtoReceiveSampleRecord> dtoReceiveSampleRecordList = new ArrayList<>();
//        dtoReceiveSampleRecordList.add(receiveSampleRecord);
//
//        when(testRepository.findAll(dtoSampleFolder.getAnalyseItemIds())).thenReturn(testList);
//        when(receiveSampleRecordRepository.findByProjectId(dtoSampleFolder.getProjectId())).thenReturn(dtoReceiveSampleRecordList);
//
//
//        //addSchemeFolder
//        DtoProject project = new DtoProject();
//        project.setProjectTypeId("tstProjectTypeId");
//        when(projectRepository.findOne(dtoSampleFolder.getProjectId())).thenReturn(project);
//        DtoProjectType projectType = new DtoProjectType();
//        when(projectTypeService.findOne(project.getProjectTypeId())).thenReturn(projectType);
//        DtoGenerateSN targetGenerateSN = new DtoGenerateSN();
//        DtoSerialNumberConfig dtoSerialNumberConfig = new DtoSerialNumberConfig();
////        targetGenerateSN.setSerialNumberConfigCreate(dtoSerialNumberConfig);
//        targetGenerateSN.setCode("tstCode");
//        when(sampleService.createSampleCode(Mockito.any(DtoProject.class), Mockito.any(DtoProjectType.class), Mockito.eq(dtoSampleFolder.getSampleTypeId()),
//                Mockito.eq(dtoSampleFolder.getId()), Mockito.any(Date.class), UUIDHelper.GUID_EMPTY, UUIDHelper.GUID_EMPTY, Mockito.eq(true), Mockito.eq(null), Mockito.eq(false))).thenReturn(targetGenerateSN);
//        when(schemeService.getFolderName(dtoSampleFolder, 1, 1, 1)).thenReturn("tstFolderName");
//        when(proService.getAnalyzeItemsByTest(testList)).thenReturn("tstAnalyzeItems");
//        DtoSample temp = new DtoSample();
//        when(sampleRepository.save(Mockito.any(DtoSample.class))).thenReturn(temp);
//        when(schemeService.getFolderName(dtoSampleFolder, 1, 1, 1)).thenReturn("tstFolderName");
//        DtoGenerateSN targetGenerateSN2 = new DtoGenerateSN();
//        DtoSerialNumberConfig dtoSerialNumberConfig2 = new DtoSerialNumberConfig();
////        targetGenerateSN2.setSerialNumberConfigCreate(dtoSerialNumberConfig2);
//        targetGenerateSN2.setCode("tstCode2");
//        when(sampleService.createSampleCode(project, projectType, dtoSampleFolder.getSampleTypeId(), dtoSampleFolder.getId(), new Date(),
//                UUIDHelper.GUID_EMPTY, UUIDHelper.GUID_EMPTY, true, "tstUserId", false)).thenReturn(targetGenerateSN2);
//        List<DtoAnalyseData> sourceAnalyseDatas = new ArrayList<>();
//        DtoAnalyseData dtoAnalyseData = new DtoAnalyseData();
//        sourceAnalyseDatas.add(dtoAnalyseData);
//        when(analyseDataRepository.findBySampleIdAndIsDeletedFalse(temp.getId())).thenReturn(sourceAnalyseDatas);
//        DtoAnalyseData targetAnalyseData = new DtoAnalyseData();
//        when(schemeService.getSchemeCloneAnalyseData(Mockito.any(DtoAnalyseData.class))).thenReturn(targetAnalyseData);
//        DtoSampleType sampleType = new DtoSampleType();
//        when(sampleTypeService.findOne(dtoSampleFolder.getSampleTypeId())).thenReturn(sampleType);
//
//        //addFolder
//        DtoSampleType samType = new DtoSampleType();
//        when(sampleTypeService.findOne(dtoSampleFolder.getSampleTypeId())).thenReturn(samType);
//        List<DtoSampleFolderTemp> temps = new ArrayList<>();
//        DtoSampleFolderTemp dtoSampleFolderTemp = new DtoSampleFolderTemp();
//        dtoSampleFolderTemp.setSampleFolderId("tstSampleFolderId");
//        temps.add(dtoSampleFolderTemp);
//        DtoLoadScheme targetScheme = new DtoLoadScheme();
//        Map<String, DtoSampleType> samTypeMap = new HashMap<>();
//        when(sampleFolderService.getFolderSchemes(Mockito.eq(dtoSampleFolder.getProjectId()),
//                Mockito.any(List.class), Mockito.any(List.class), Mockito.any(List.class), Mockito.any(List.class), Mockito.any(Map.class))).thenReturn(temps);
//
//        String sampleFolderId = ergencyMonitoringServiceImpl.saveSampleList(dtoSampleFolder);
//        // 断言：判断成功
//        Assert.assertEquals("sampleFolderId not match", "tstSampleFolderId", sampleFolderId);
//
//    }
//
//    @Test
//    public void testCheckSample() {
//        // 断言：ergencyMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(ergencyMonitoringServiceImpl);
//
//        DtoSampleFolder dtoSampleFolder = new DtoSampleFolder();
//        dtoSampleFolder.setId("tstId");
//        dtoSampleFolder.setProjectId("tstProjectId");
//        String folderId = dtoSampleFolder.getId();
//        String projectId = dtoSampleFolder.getProjectId();
//
//        List<DtoSample> sampleList = new ArrayList<>();
//        when(sampleRepository.findBySampleFolderId(folderId)).thenReturn(sampleList);
//        DtoProject project = new DtoProject();
//        when(projectRepository.findOne(projectId)).thenReturn(project);
//        DtoReceiveSampleRecord receiveSampleRecord = new DtoReceiveSampleRecord();
//        receiveSampleRecord.setId("tstRecId");
//        List<DtoReceiveSampleRecord> dtoReceiveSampleRecordList = new ArrayList<>();
//        dtoReceiveSampleRecordList.add(receiveSampleRecord);
//        when(receiveSampleRecordRepository.findByProjectId(projectId)).thenReturn(dtoReceiveSampleRecordList);
//        List<DtoReceiveSubSampleRecord> subSampleRecordList = new ArrayList<>();
//        DtoReceiveSubSampleRecord dtoReceiveSubSampleRecord = new DtoReceiveSubSampleRecord();
//        dtoReceiveSubSampleRecord.setCode("XC");
//        dtoReceiveSubSampleRecord.setId("tstSubRecId");
//        subSampleRecordList.add(dtoReceiveSubSampleRecord);
//        when(receiveSubSampleRecordRepository.findByReceiveId(receiveSampleRecord.getId())).thenReturn(subSampleRecordList);
//
//
//
//        String xcSubId = ergencyMonitoringServiceImpl.checkSample(dtoSampleFolder.getId(), dtoSampleFolder.getProjectId());
//        // 断言：判断成功
//        Assert.assertEquals("xcSubId not match", "tstSubRecId", xcSubId);
//    }
//
//    @Test
//    public void testGetPersonById() {
//        // 断言：ergencyMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(ergencyMonitoringServiceImpl);
//
//        String id = "tstId";
//
//        DtoPerson person = new DtoPerson();
//        person.setDeptId("tstDepId");
//        when(personRepository.findOne(id)).thenReturn(person);
//        DtoDepartment department = new DtoDepartment(new DepartmentModel());
//        department.setDeptName("tstDepName");
//        when(departmentService.findOne(person.getDeptId())).thenReturn(department);
//
//
//        DtoPersonPhone personPhone = ergencyMonitoringServiceImpl.getPersonById(id);
//        // 断言：判断成功
//        Assert.assertEquals("personPhone not match", "tstDepName", personPhone.getDomainName());
//    }
//}
