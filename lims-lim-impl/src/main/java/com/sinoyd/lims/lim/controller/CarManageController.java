package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.CarManageCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoCarManage;
import com.sinoyd.lims.lim.service.CarManageService;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 车辆管理接口定义
 * 
 * <AUTHOR>
 * @version V1.0.0 2019/05/10
 * @since V100R001
 */
@RestController
@RequestMapping("/api/lim/carManage")
@Validated
public class CarManageController extends BaseJpaController<DtoCarManage, String, CarManageService> {

    /**
     * 根据id获取车辆信息
     * 
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id获取车辆信息", notes = "根据id获取车辆信息")
    @GetMapping("/{id}")
    public RestResponse<DtoCarManage> find(@PathVariable(name = "id") String id) {

        RestResponse<DtoCarManage> restResp = new RestResponse<>();
        DtoCarManage contract = service.findOne(id);
        restResp.setData(contract);

        restResp.setRestStatus(StringUtil.isNull(contract) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }

    /**
     * 分页动态条件查询车辆信息
     * 
     * @param criteria
     * @return
     */
    @ApiOperation(value = "分页动态条件查询车辆信息", notes = "分页动态条件查询车辆信息")
    @GetMapping
    public RestResponse<List<DtoCarManage>> findByPage(CarManageCriteria criteria) {

        RestResponse<List<DtoCarManage>> restResp = new RestResponse<>();

        PageBean<DtoCarManage> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 新增车辆信息
     * 
     * @param entity
     * @return
     */
    @ApiOperation(value = "新增车辆信息", notes = "新增车辆信息")
    @PostMapping
    public RestResponse<DtoCarManage> save(@Validated @RequestBody DtoCarManage entity) {

        RestResponse<DtoCarManage> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoCarManage data = service.save(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 修改车辆信息
     * 
     * @param entity
     * @return
     */
    @ApiOperation(value = "修改车辆信息", notes = "修改车辆信息")
    @PutMapping
    public RestResponse<DtoCarManage> update(@Validated @RequestBody DtoCarManage entity) {

        RestResponse<DtoCarManage> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoCarManage data = service.update(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 刪除车辆信息
     * 
     * @param id
     * @return
     */
    @ApiOperation(value = "刪除车辆信息", notes = "刪除车辆信息")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable(name = "id") String id) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);

        return restResp;
    }

    /**
     * 批量删除车辆信息
     * 
     * @param ids
     * @return
     */
    @ApiOperation(value = "批量删除车辆信息", notes = "批量删除车辆信息")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);

        return restResp;
    }
}