package com.sinoyd.base.strategy.htmlParse;

import com.sinoyd.base.utils.html.HtmlParseUtil;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 排污许可证自行监测要求Html数据解析
 *
 * <AUTHOR>
 * @version V5.2.0 2025/04/21
 * @since V100R001
 */
@Component
public class HtmlTableParse extends AbstractHtmlParse {

    /**
     * 转换html表格Table标签内容为实体
     *
     * @param html html数据
     * @return 实体集合
     */
    @Override
    public <T> List<T> parse(String html, Class<T> c) {
        List<T> result = new ArrayList<>();
        // 转换html为DOM树
        Document doc = Jsoup.parse(html, "UTF-8");
        Elements rows = doc.select("table#mainTable tbody tr");

        if (rows.isEmpty()) {
            return result;
        }

        // 获取表头信息
        Elements headers = rows.get(0).select("th");
        Map<Integer, String> headerMap = new HashMap<>();
        for (int i = 0; i < headers.size(); i++) {
            headerMap.put(i, headers.get(i).text());
        }

        // 缓存 rowspan 的列索引和对应的值
        Map<Integer, String> rowspanCache = new HashMap<>();

        // 遍历每一行数据
        for (int rowIndex = 1; rowIndex < rows.size(); rowIndex++) {
            Element row = rows.get(rowIndex);
            Elements cells = row.select("td");
            //实例化对象
            T entity = getInstance(c);

            //当前列偏移量
            int offset = headerMap.size() - cells.size();
            //如果不存在列偏移量、则表示此行不存在行合并数据、则清空rowspanChache缓存，用于下次计数
            if (offset <= 0) {
                rowspanCache.clear();
            }
            //如果列偏移量大于0且小于上一次记录的rowspan合并数、则表示存在新合并区域、需要对前合并区域缓存进行刷新
            else if (offset < rowspanCache.size()) {
                //刷新缓存
                int lastRowspanSize = rowspanCache.size();
                for (int i = offset; i < lastRowspanSize; i++) {
                    rowspanCache.remove(i);
                }
            }

            //对列偏移量之前的列数据进行赋值、获取合并列数据
            for (int i = 0; i < offset; i++) {
                // 设置字段值
                HtmlParseUtil.setFieldValue(entity, headerMap.get(i), rowspanCache.get(i));
            }


            // 索引定位到列偏移量位置
            int colIndex = offset;
            // 处理合并数据外剩下的列数据
            for (Element cell : cells) {
                String cellText = getCellStr(cell);
                // 判断当前列是否存在rowspan行合并数据，如果存在，则根据列合并偏移量对rowspanCache进行刷新
                if (cell.hasAttr("rowspan")) {
                    int rowspan = Integer.parseInt(cell.attr("rowspan"));
                    //当rowspan大于1时才刷新到rowspanCache中
                    if (rowspan > 1) {
                        rowspanCache.put(colIndex, cellText);
                    }
                }
                // 设置字段值
                HtmlParseUtil.setFieldValue(entity, headerMap.get(colIndex), cellText);
                colIndex++;
            }

            result.add(entity);
        }

        return result;
    }

    /**
     * 获取单元格文本内容
     * 1.处理input标签内容
     * 2.处理select>option标签内容
     *
     * @param cell 单元格元素
     * @return 单元格文本内容
     */
    private static String getCellStr(Element cell) {
        String cellStr = "";
        // 判断是否存在input标签
        if (cell.select("input").size() > 0) {
            if (cell.selectFirst("input[type!=hidden][type!=button]") != null) {
                cellStr = cell.selectFirst("input[type!=hidden][type!=button]").attr("value");
            } else {
                cellStr = cell.text();
            }
        }
        //判断是否存在option标签
        if (cell.select("option").size() > 0) {
            if (cell.selectFirst("option[selected]") != null) {
                cellStr = cell.selectFirst("option[selected]").text();
            } else {
                cellStr = "";
            }
        }
        if (cell.select("input").isEmpty() && cell.select("option").isEmpty()) {
            cellStr = cell.text();
        }
        return cellStr;
    }
}
