package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.pro.dto.DtoQCData;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.customer.DtoQCDataConfig;


/**
 * QCData操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface QCDataService extends IBaseJpaService<DtoQCData, String> {

    /**
     * 保存选择的配置数据
     *
     * @param dtoQCDataConfig 质控数据配置
     */
    void saveConfigQCData(DtoQCDataConfig dtoQCDataConfig);

    /**
     * 获取配置数据
     *
     * @param testId   测试项目id
     * @param personId 人员id
     * @param type     类型
     * @param  paramsName 参数名称
     * @return 返回配置数据
     */
    DtoQCData findConfigQCData(String testId, String personId, Integer type,String paramsName);
}