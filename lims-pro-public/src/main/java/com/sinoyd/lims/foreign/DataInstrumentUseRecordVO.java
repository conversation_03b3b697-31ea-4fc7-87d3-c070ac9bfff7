package com.sinoyd.lims.foreign;

import lombok.Data;

import java.util.Date;

/**
 * 仪器信息
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Data
public class DataInstrumentUseRecordVO {

    /**
     * id
     */
    private String id;

    /**
     * 仪器id
     */
    private String instrumentId;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 仪器型号
     */
    private String instrumentModel;

    /**
     * 仪器编号
     */
    private String instrumentCode;

    /**
     * 仪器本站编号
     */
    private String instrumentSerialNo;

    /**
     * 关联id
     */
    private String objectId;

    /**
     * 使用类型
     */
    private Integer objectType;

    /**
     * 使用人名称
     */
    private String usePersonName;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 测试项目ids
     */
    private String testIds;

    /**
     * 温度
     */
    private String temperature;

    /**
     * 湿度
     */
    private String humidity;

    /**
     * 大气压
     */
    private String pressure;

    /**
     * 使用前情况
     */
    private String beforeUseSituation;

    /**
     * 使用后情况
     */
    private String beforeAfterSituation;

    /**
     * 是否辅助仪器
     */
    private Boolean isAssistInstrument;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;

}