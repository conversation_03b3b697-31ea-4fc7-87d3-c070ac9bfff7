package com.sinoyd.lims.monitor.dto.rcc;

import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.monitor.dto.lims.DtoProperty2Point;
import com.sinoyd.lims.monitor.entity.Fixedpoint;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;
import java.util.Map;


/**
 * DtoFixedpoint实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_MONITOR_Fixedpoint")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoFixedpoint extends Fixedpoint {
    private static final long serialVersionUID = 1L;

    /**
     * 用于点位排序的排序值
     */
    @Transient
    private int orderNumForSort;

    /**
     * 企业名称
     */
    @Transient
    private String enterpriseName;

    /**
     * 检测类型名称
     */
    @Transient
    private String sampleTypeName;

    /**
     * 评价标准名称
     */
    @Transient
    private String evaluationName;

    /**
     * 评价等级名称
     */
    @Transient
    private String evaluationLvlName;

    /**
     * 所属水体名称
     */
    @Transient
    private String waterName;

    /**
     * 等级名称
     */
    @Transient
    private String levelName;

    /**
     * 点位类型名称
     */
    @Transient
    private String folderTypeName;

    /**
     * 测试项目名称
     */
    @Transient
    private String testName;

    /**
     * 因子个数
     */
    @Transient
    private Integer factorNum;

    /**
     * 样品个数
     */
    @Transient
    private Integer samplePeriod;

    /**
     * 点位扩展信息
     */
    @Transient
    private DtoFixedPointExpend fixedPointExpend;

    /**
     * 分析项目名称
     */
    @Transient
    private String analyzeItems;

    /**
     * 分析项目个数
     */
    @Transient
    private Integer analyzeItemsCount;

    /**
     * 是否与项目关联
     */
    @Transient
    private Boolean projectRelevance;

    /**
     * 测试项目表集合
     */
    @Transient
    private List<DtoTest> dtoTests;

    @Transient
    private List<Map<String, Object>> testList;

    /**
     * 测试项目id数组
     */
    @Transient
    private List<String> testIds;

    /**
     * 关联点位id列表
     */
    @Transient
    private List<String> relationPointIds;

    /**
     * 关联点位
     */
    @Transient
    private List<DtoFixedPoint2Point> relationPointList;

    /**
     * 关联监测计划id列表
     */
    @Transient
    private List<String> relationPropertyIds;

    /**
     * 关联点位类型拓展字段数据
     */
    @Transient
    private List<DtoPointExtendConfig> pointExtendConfigs;

    /**
     * 经纬度（合并）
     */
    @Transient
    private String location;

    /**
     * 所属区域（名称）
     */
    @Transient
    private String areaName;

    /**
     * 所属水体id
     */
    @Transient
    private String waterId;

    /**
     * 关联监测计划列表
     */
    @Transient
    private List<DtoProperty2Point> relationPropertyList;

    /**
     * 选中的点位id，复制点位使用
     */
    @Transient
    private List<String> fixedPointIds;

    /**
     * 是否测试项目提示（用作例行点位，存在”停用”、“作废”、“删除”的测试项目时，进行提示）
     */
    @Transient
    private Boolean isTestTip = false;

    public DtoFixedpoint() {
    }

    /**
     * @param id          主键id
     * @param name        企业名称
     * @param folderType  点位类型
     * @param pointCode   点位编号
     * @param pointName   点位名称
     * @param stationName 所属测站
     * @param villageCode 位置
     * @param isEnabled   启用状态
     * @param cycleOrder  周期
     * @param timesOrder  频次
     */
    public DtoFixedpoint(String id, String name, String folderType, String pointCode, String pointName, String stationName,
                         String villageCode, Boolean isEnabled, Integer cycleOrder, Integer timesOrder, String evaluationId, Integer orderNum, String sampleTypeId) {
        this.setId(id);
        this.setEnterpriseName(name);
        this.setFolderType(folderType);
        this.setPointCode(pointCode);
        this.setPointName(pointName);
        this.setStationName(stationName);
        this.setVillageCode(villageCode);
        this.setIsEnabled(isEnabled);
        this.setCycleOrder(cycleOrder);
        this.setTimesOrder(timesOrder);
        this.setEvaluationId(evaluationId);
        this.setOrderNum(orderNum);
        this.setSampleTypeId(sampleTypeId);
    }
}