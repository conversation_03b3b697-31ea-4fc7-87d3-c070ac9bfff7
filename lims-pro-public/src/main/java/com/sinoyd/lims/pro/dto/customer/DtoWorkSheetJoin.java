package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 检测单加入的dto
 * <AUTHOR>
 * @version V1.0.0 2019/12/02
 * @since V100R001
 */
@Data
public class DtoWorkSheetJoin {


    /**
     * 分析数据id
     */
    private List<String> analyseDataIds = new ArrayList<>();

    /**
     * 检测单id
     */
    private String workSheetFolderId;

    /**
     * 排序id
     */
    private String sortId;

    /**
     * 是否需要制备
     */
    private Boolean isPreparation;

    /**
     * 是否同方法批量加入
     */
    private Boolean isSameMethodBatchAdd;
}
