package com.sinoyd.lims.lim.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

import javax.persistence.Table;

/**
 * 检测人员导出实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/6
 * @since V100R001
 */
@Table(name = "TB_LIM_Person2Test")
@Data
public class DtoExportPerson2Test {

    /**
     * 主键id
     */
    @Excel(name = "主键id", orderNum = "10")
    private String id = UUIDHelper.NewID();

    /**
     * 人员Id
     */
    @Excel(name = "人员Id", orderNum = "20")
    private String personId;

    /**
     * 测试项目id
     */
    @Excel(name = "测试项目id", orderNum = "30")
    private String testId;

    /**
     * 排序值
     */
    @Excel(name = "排序值", orderNum = "40")
    private Integer orderNum;

    /**
     * 样品类型id
     */
    @Excel(name = "样品类型id", orderNum = "50")
    private String sampleTypeId;

    /**
     * 是否默认分析人员
     */
    @Excel(name = "是否默认分析人员", orderNum = "60")
    private Boolean isDefaultPerson;

    /**
     * 是否默认复核人员
     */
    @Excel(name = "是否默认复核人员", orderNum = "70")
    private Boolean isDefaultAuditPerson;

    /**
     * 组织机构id
     */
    @Excel(name = "组织机构id", orderNum = "80")
    private String orgId;
}
