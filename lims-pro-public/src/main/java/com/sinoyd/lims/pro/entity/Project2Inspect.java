package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;

/**
 * Project2Inspect实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/2/8
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "Project2Inspect")
@Data
@EntityListeners(AuditingEntityListener.class)
public class Project2Inspect implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 项目id
     */
    @Column(length = 50)
    @ApiModelProperty("项目id")
    private String projectId;

    /**
     * 期间核查类型
     */
    @ApiModelProperty("核查类型")
    private Integer inspectType;

    /**
     * 核查对象ID
     */
    @Column(length = 50)
    @ApiModelProperty("核查对象id")
    private String objectId;

    /**
     * 对象名称
     */
    @Column(length = 50)
    @ApiModelProperty("对象名称")
    private String objName;

    /**
     * 对象编号
     */
    @Column(length = 50)
    @ApiModelProperty("对象编号")
    private String code;

    /**
     * 对象型号
     */
    @Column(length = 50)
    @ApiModelProperty("对象型号")
    private String model;

    /**
     * 核查内容
     */
    @ApiModelProperty("核查内容")
    private String inspectContent;

}
