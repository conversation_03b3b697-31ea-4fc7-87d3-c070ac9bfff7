-- 年度计划
CREATE TABLE TB_QA_YearlyPlan
(
    id             varchar(50)             NOT NULL,
    planName       varchar(50)             NOT NULL DEFAULT '',
    planType       int                     NOT NULL ,
    planYear       int                     NOT NULL ,
    compilePeople  varchar(500),
    remark         varchar(1000),
    status         int                     NOT NULL DEFAULT 0,
    isDeleted      bit                     NOT NULL DEFAULT 0,
    orgId          varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    creator        varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    createDate     TIMESTAMP(0)            NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    domainId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifier       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifyDate     TIMESTAMP(0)            NOT NULL DEFAULT CURRENT_TIMESTAMP()
);
ALTER TABLE TB_QA_YearlyPlan ADD CONSTRAINT PRIMARY KEY ("id");
comment on table TB_QA_YearlyPlan is '年度计划';
comment on column TB_QA_YearlyPlan.planName is '计划名称';
comment on column TB_QA_YearlyPlan.planType is '计划类型，关联枚举EnumYearlyPlanType';
comment on column TB_QA_YearlyPlan.planYear is '计划年份';
comment on column TB_QA_YearlyPlan.compilePeople is '编制人员';
comment on column TB_QA_YearlyPlan.remark is '备注';
comment on column TB_QA_YearlyPlan.status is '状态,0未提交/1已提交';
comment on column TB_QA_YearlyPlan.isDeleted is '是否删除';
comment on column TB_QA_YearlyPlan.orgId is '组织机构id';
comment on column TB_QA_YearlyPlan.creator is '创建人';
comment on column TB_QA_YearlyPlan.createDate is '创建时间';
comment on column TB_QA_YearlyPlan.domainId is '所属实验室';
comment on column TB_QA_YearlyPlan.modifier is '修改人';
comment on column TB_QA_YearlyPlan.modifyDate is '修改时间';

-- 质量监督计划
CREATE TABLE TB_QA_YearlyQualitySupervisionPlan
(
    id             varchar(50)             NOT NULL,
    planId         varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    detailId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    content        varchar(500),
    target         varchar(50),
    timeRequire    varchar(50),
    deptId         varchar(50),
    personId       varchar(50),
    completion     varchar(500),
    status         int                     NOT NULL DEFAULT 0,
    planType       int                     NOT NULL DEFAULT 0,
    executeType    int                     NOT NULL DEFAULT 0,
    isDeleted      bit                     NOT NULL DEFAULT 0,
    orgId          varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    creator        varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    createDate     TIMESTAMP(0)            NOT NULL DEFAULT CURRENT_TIMESTAMP (),
    domainId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifier       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifyDate     TIMESTAMP(0)            NOT NULL DEFAULT CURRENT_TIMESTAMP ()
);
ALTER TABLE TB_QA_YearlyQualitySupervisionPlan ADD CONSTRAINT PRIMARY KEY ("id");
comment on table TB_QA_YearlyQualitySupervisionPlan is '质量监督计划';
comment on column TB_QA_YearlyQualitySupervisionPlan.planId is '年度计划标识';
comment on column TB_QA_YearlyQualitySupervisionPlan.detailId is '计划标识';
comment on column TB_QA_YearlyQualitySupervisionPlan.content is '监督内容';
comment on column TB_QA_YearlyQualitySupervisionPlan.target is '监督对象';
comment on column TB_QA_YearlyQualitySupervisionPlan.timeRequire is '时间要求/完成时间';
comment on column TB_QA_YearlyQualitySupervisionPlan.deptId is '责任部门标识';
comment on column TB_QA_YearlyQualitySupervisionPlan.personId is '监督员标识';
comment on column TB_QA_YearlyQualitySupervisionPlan.completion is '完成情况';
comment on column TB_QA_YearlyQualitySupervisionPlan.status is '执行状态,0执行中/1完成';
comment on column TB_QA_YearlyQualitySupervisionPlan.planType is '计划状态,0计划内/1计划外';
comment on column TB_QA_YearlyQualitySupervisionPlan.executeType is '计划类型,0计划/1执行';
comment on column TB_QA_YearlyQualitySupervisionPlan.isDeleted is '是否删除';
comment on column TB_QA_YearlyQualitySupervisionPlan.orgId is '组织机构id';
comment on column TB_QA_YearlyQualitySupervisionPlan.creator is '创建人';
comment on column TB_QA_YearlyQualitySupervisionPlan.createDate is '创建时间';
comment on column TB_QA_YearlyQualitySupervisionPlan.domainId is '所属实验室';
comment on column TB_QA_YearlyQualitySupervisionPlan.modifier is '修改人';
comment on column TB_QA_YearlyQualitySupervisionPlan.modifyDate is '修改时间';

-- 质量控制计划
CREATE TABLE TB_QA_YearlyQualityControlPlan
(
    id             varchar(50)             NOT NULL,
    planId         varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    detailId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    content        varchar(500),
    analyzeItem    varchar(50),
    target         varchar(50),
    timeRequire    varchar(50),
    method         varchar(50),
    point          varchar(50),
    personId       varchar(50),
    completion     varchar(500),
    status         int                     NOT NULL DEFAULT 0,
    planType       int                     NOT NULL DEFAULT 0,
    executeType    int                     NOT NULL DEFAULT 0,
    isDeleted      bit                     NOT NULL DEFAULT 0,
    orgId          varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    creator        varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    createDate     TIMESTAMP(0)            NOT NULL DEFAULT CURRENT_TIMESTAMP (),
    domainId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifier       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifyDate     TIMESTAMP(0)            NOT NULL DEFAULT CURRENT_TIMESTAMP ()
);
ALTER TABLE TB_QA_YearlyQualityControlPlan ADD CONSTRAINT PRIMARY KEY ("id");
comment on table TB_QA_YearlyQualityControlPlan is '质量控制计划';
comment on column TB_QA_YearlyQualityControlPlan.planId is '年度计划标识';
comment on column TB_QA_YearlyQualityControlPlan.detailId is '计划标识';
comment on column TB_QA_YearlyQualityControlPlan.content is '质控内容';
comment on column TB_QA_YearlyQualityControlPlan.analyzeItem is '分析项目';
comment on column TB_QA_YearlyQualityControlPlan.target is '质控对象';
comment on column TB_QA_YearlyQualityControlPlan.timeRequire is '时间要求/完成时间';
comment on column TB_QA_YearlyQualityControlPlan.method is '质控方法';
comment on column TB_QA_YearlyQualityControlPlan.point is '评价指标';
comment on column TB_QA_YearlyQualityControlPlan.personId is '负责人标识/责任人标识';
comment on column TB_QA_YearlyQualityControlPlan.completion is '完成情况';
comment on column TB_QA_YearlyQualityControlPlan.status is '执行状态,0执行中/1完成';
comment on column TB_QA_YearlyQualityControlPlan.planType is '计划状态,0计划内/1计划外';
comment on column TB_QA_YearlyQualityControlPlan.executeType is '计划类型,0计划/1执行';
comment on column TB_QA_YearlyQualityControlPlan.isDeleted is '是否删除';
comment on column TB_QA_YearlyQualityControlPlan.orgId is '组织机构id';
comment on column TB_QA_YearlyQualityControlPlan.creator is '创建人';
comment on column TB_QA_YearlyQualityControlPlan.createDate is '创建时间';
comment on column TB_QA_YearlyQualityControlPlan.domainId is '所属实验室';
comment on column TB_QA_YearlyQualityControlPlan.modifier is '修改人';
comment on column TB_QA_YearlyQualityControlPlan.modifyDate is '修改时间';

-- 内审计划
CREATE TABLE TB_QA_YearlyInnerAuditPlan
(
    id             varchar(50)             NOT NULL,
    planId         varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    chargePersonId varchar(50),
    auditTime      TIMESTAMP(0),
    basis          varchar(500),
    purpose        varchar(500),
    form           varchar(500),
    content        varchar(500),
    people         varchar(500),
    progress       varchar(500),
    completion     varchar(500),
    status         int                     NOT NULL DEFAULT 0,
    executeType    int                     NOT NULL DEFAULT 0,
    isDeleted      bit                     NOT NULL DEFAULT 0,
    orgId          varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    creator        varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    createDate     TIMESTAMP(0)            NOT NULL DEFAULT CURRENT_TIMESTAMP (),
    domainId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifier       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifyDate     TIMESTAMP(0)            NOT NULL DEFAULT CURRENT_TIMESTAMP ()
);
ALTER TABLE TB_QA_YearlyInnerAuditPlan ADD CONSTRAINT PRIMARY KEY ("id");
comment on table TB_QA_YearlyInnerAuditPlan is '内审计划';
comment on column TB_QA_YearlyInnerAuditPlan.planId is '年度计划标识';
comment on column TB_QA_YearlyInnerAuditPlan.chargePersonId is '负责人标识';
comment on column TB_QA_YearlyInnerAuditPlan.auditTime is '评审时间';
comment on column TB_QA_YearlyInnerAuditPlan.basis is '评审依据';
comment on column TB_QA_YearlyInnerAuditPlan.purpose is '评审目的';
comment on column TB_QA_YearlyInnerAuditPlan.form is '评审形式';
comment on column TB_QA_YearlyInnerAuditPlan.content is '评审内容';
comment on column TB_QA_YearlyInnerAuditPlan.people is '参与评审人员';
comment on column TB_QA_YearlyInnerAuditPlan.progress is '会议议程';
comment on column TB_QA_YearlyInnerAuditPlan.completion is '完成情况';
comment on column TB_QA_YearlyInnerAuditPlan.status is '执行状态,0执行中/1完成';
comment on column TB_QA_YearlyInnerAuditPlan.executeType is '计划类型,0计划/1执行';
comment on column TB_QA_YearlyInnerAuditPlan.isDeleted is '是否删除';
comment on column TB_QA_YearlyInnerAuditPlan.orgId is '组织机构id';
comment on column TB_QA_YearlyInnerAuditPlan.creator is '创建人';
comment on column TB_QA_YearlyInnerAuditPlan.createDate is '创建时间';
comment on column TB_QA_YearlyInnerAuditPlan.domainId is '所属实验室';
comment on column TB_QA_YearlyInnerAuditPlan.modifier is '修改人';
comment on column TB_QA_YearlyInnerAuditPlan.modifyDate is '修改时间';

-- 管理评审计划
CREATE TABLE TB_QA_YearlyManagementReviewPlan
(
    id             varchar(50)             NOT NULL,
    planId         varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    chargePersonId varchar(50),
    auditTime      TIMESTAMP(0),
    basis          varchar(500),
    purpose        varchar(500),
    form           varchar(500),
    content        varchar(500),
    people         varchar(500),
    progress       varchar(500),
    completion     varchar(500),
    status         int                     NOT NULL DEFAULT 0,
    executeType    int                     NOT NULL DEFAULT 0,
    isDeleted      bit                     NOT NULL DEFAULT 0,
    orgId          varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    creator        varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    createDate     TIMESTAMP(0)            NOT NULL DEFAULT CURRENT_TIMESTAMP (),
    domainId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifier       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifyDate     TIMESTAMP(0)            NOT NULL DEFAULT CURRENT_TIMESTAMP ()
);
ALTER TABLE TB_QA_YearlyManagementReviewPlan ADD CONSTRAINT PRIMARY KEY ("id");
comment on table TB_QA_YearlyManagementReviewPlan is '管理评审计划';
comment on column TB_QA_YearlyManagementReviewPlan.planId is '年度计划标识';
comment on column TB_QA_YearlyManagementReviewPlan.chargePersonId is '负责人标识/责任人标识';
comment on column TB_QA_YearlyManagementReviewPlan.auditTime is '评审时间/完成时间';
comment on column TB_QA_YearlyManagementReviewPlan.basis is '评审依据';
comment on column TB_QA_YearlyManagementReviewPlan.purpose is '评审目的';
comment on column TB_QA_YearlyManagementReviewPlan.form is '评审形式';
comment on column TB_QA_YearlyManagementReviewPlan.content is '评审内容';
comment on column TB_QA_YearlyManagementReviewPlan.people is '参与评审人员';
comment on column TB_QA_YearlyManagementReviewPlan.progress is '会议议程';
comment on column TB_QA_YearlyManagementReviewPlan.completion is '完成情况';
comment on column TB_QA_YearlyManagementReviewPlan.status is '执行状态,0执行中/1完成';
comment on column TB_QA_YearlyManagementReviewPlan.executeType is '计划类型,0计划/1执行';
comment on column TB_QA_YearlyManagementReviewPlan.isDeleted is '是否删除';
comment on column TB_QA_YearlyManagementReviewPlan.orgId is '组织机构id';
comment on column TB_QA_YearlyManagementReviewPlan.creator is '创建人';
comment on column TB_QA_YearlyManagementReviewPlan.createDate is '创建时间';
comment on column TB_QA_YearlyManagementReviewPlan.domainId is '所属实验室';
comment on column TB_QA_YearlyManagementReviewPlan.modifier is '修改人';
comment on column TB_QA_YearlyManagementReviewPlan.modifyDate is '修改时间';
