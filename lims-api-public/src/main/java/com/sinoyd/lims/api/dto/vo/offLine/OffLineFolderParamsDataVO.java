package com.sinoyd.lims.api.dto.vo.offLine;

import lombok.Data;

/**
 * 离线数据录入（离线数据表）：点位参数表实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/09/23
 */
@Data
public class OffLineFolderParamsDataVO {

    /**
     * 主键ID，UUID格式
     */
    private String id;

    /**
     * 点位id（不按周期分组的点位id）
     */
    private String sampleFolderId;

    /**
     * 参数数据JSON字符串
     */
    private String paramBucket;

    /**
     * 所属机构
     */
    private String orgId;
}
