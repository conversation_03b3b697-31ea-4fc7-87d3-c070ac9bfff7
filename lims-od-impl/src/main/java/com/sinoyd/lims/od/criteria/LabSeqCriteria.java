package com.sinoyd.lims.od.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

/**
 * LabSeq查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LabSeqCriteria extends BaseCriteria {


    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 实验组
     */
    private Integer groupSn;

    /**
     * 实验次序
     */
    private Integer seqSn;


    @Override
    public String getCondition() {
        values.clear(); // 清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.groupId = g.id");
        if (StringUtil.isNotNull(sampleId)) {
            condition.append(" and g.sampleId = :sampleId");
            values.put("sampleId", sampleId);
        }
        if (StringUtil.isNotNull(groupSn)) {
            condition.append(" and g.sn = :groupSn");
            values.put("groupSn", groupSn);
        }
        if (StringUtil.isNotNull(seqSn)) {
            condition.append(" and a.sn = :seqSn");
            values.put("seqSn", seqSn);
        }
        return condition.toString();

    }
}
