package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoReport;
import com.sinoyd.lims.pro.dto.DtoReportNumberPool;

import java.util.List;
import java.util.Map;

/**
 * ReportNumberPool 数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/8/1
 * @since V100R001
 */
public interface ReportNumberPoolService extends IBaseJpaService<DtoReportNumberPool, String> {

    /**
     * 批量新增报告编号
     *
     * @param reportNumberPool 实体
     * @return List<DtoReportNumberPool>
     */
    List<DtoReportNumberPool> batchCreate(DtoReportNumberPool reportNumberPool);

    /**
     * 查询报告详情
     * @param code
     * @return
     */
    DtoReport queryReport(String code);

    /**
     * 获取当前最大流水号
     *
     * @param reportNumberPool
     * @return
     */
    Map<String,Object> queryMaxNumber(DtoReportNumberPool reportNumberPool);

    /**
     * 批量作废
     *
     * @param ids ids
     * @return 数量
     */
    int cancel(List<String> ids);

    /**
     * 取消作废
     *
     * @param ids ids
     * @return 数量
     */
    int cancelVoid(List<String> ids);

    static void test(){}

}
