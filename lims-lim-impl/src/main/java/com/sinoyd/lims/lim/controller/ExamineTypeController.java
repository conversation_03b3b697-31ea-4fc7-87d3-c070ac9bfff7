package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.dto.lims.DtoExamine;
import com.sinoyd.lims.lim.dto.lims.DtoExamineType;
import com.sinoyd.lims.lim.service.ExamineTypeService;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 考核类型controller
 *
 * <AUTHOR>
 * @version V1.0.0 2023/09/14
 * @since V100R001
 */
@Api(tags = "DtoExamineType服务")
@RestController
@RequestMapping("api/lim/examineType")
@Validated
public class ExamineTypeController extends BaseJpaController<DtoExamineType, String, ExamineTypeService> {


    /**
     * 新增考核大项
     * @param entity 考核类型实体
     * @return  考核实体
     */
    @PostMapping
    public RestResponse<DtoExamineType>  addExamineType(@Validated @RequestBody DtoExamineType entity){
        RestResponse<DtoExamineType> response = new RestResponse<>();
        response.setData(service.save(entity));
        return response;
    }

    /**
     * 复制考核项目
     * @param id 考核项目id
     * @return  考核项目
     */
    @PostMapping("copy/{id}")
    public RestResponse<DtoExamineType>  copyExamineType(@PathVariable("id")String id){
        RestResponse<DtoExamineType> response = new RestResponse<>();
        response.setData(service.copyExamineType(id));
        return response;
    }

    /**
     * 获取考核项目详情
     * @param id 考核项目标标识
     * @return  考核实体
     */
    @GetMapping("/{id}")
    public RestResponse<DtoExamineType>  getExamineType(@PathVariable("id")String id){
        RestResponse<DtoExamineType> response = new RestResponse<>();
        response.setData(service.findOne(id));
        return response;
    }

    /**
     * 修改考核项目
     * @param entity 考核类型实体
     * @return  考核实体
     */
    @PutMapping
    public RestResponse<DtoExamineType>  updateExamineType(@Validated @RequestBody DtoExamineType entity){
        RestResponse<DtoExamineType> response = new RestResponse<>();
        DtoExamineType rt = service.update(entity);
        response.setData(rt);
        return response;
    }


    /**
     * 删除考核项目
     * @param id 考核标识
     * @return  假删数量
     */
    @DeleteMapping("/{id}")
    public RestResponse<DtoExamineType>  delExamineTypeById(@PathVariable("id")String id){
        RestResponse<DtoExamineType> response = new RestResponse<>();
        response.setCount(service.logicDeleteById(id));
        return response;
    }

    /**
     * 批量删除考核项目
     * @param ids 考核项目标识列表
     * @return  假删数量
     */
    @DeleteMapping
    public RestResponse<DtoExamine>  batchDelExamineByIds(@RequestBody List<String> ids){
        RestResponse<DtoExamine> response = new RestResponse<>();
        response.setCount(service.logicDeleteById(ids));
        return response;
    }
}
