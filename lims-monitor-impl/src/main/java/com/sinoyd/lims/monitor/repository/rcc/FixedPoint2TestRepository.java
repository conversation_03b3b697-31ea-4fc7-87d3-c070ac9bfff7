package com.sinoyd.lims.monitor.repository.rcc;

import com.sinoyd.lims.monitor.dto.rcc.DtoFixedPoint2Test;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

import java.util.Collection;
import java.util.List;


/**
 * FixedPoint2Test数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
public interface FixedPoint2TestRepository extends IBaseJpaPhysicalDeleteRepository<DtoFixedPoint2Test, String> {
    /**
     * 通过点位信息id查询集合
     *
     * @param fixedPointId 点位信息id
     * @return List<DtoFixedPoint2Test>
     */
    List<DtoFixedPoint2Test> findByFixedPointId(String fixedPointId);

    /**
     * 通过点位信息id集合查询
     *
     * @param fixedPointId 点位信息id
     * @return List<DtoFixedPoint2Test>
     */
    List<DtoFixedPoint2Test> findByFixedPointIdIn(Collection<String> fixedPointId);

    /**
     * 根据点位id删除所有绑定数据
     *
     * @param fixedPointId 点位id
     */
    void deleteAllByFixedPointId(String fixedPointId);

}