package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoReportConfig;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;


/**
 * ReportConfig操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/10/13
 * @since V100R001
 */
public interface ReportConfigService extends IBaseJpaService<DtoReportConfig, String> {
    /**
     * 根据编号查询相应的配置数据
     *
     * @param code 编号
     * @return 返回数据
     */
    DtoReportConfig findByCode(String code);

    /**
     * 获取附件路径
     * @param configId 配置id
     * @return 路径
     */
    String getDocumentAttachPath(String configId);

    /**
     * 附件下载
     * @param configId 报表配置id
     * @param response 响应流
     * @return 附件
     */
    String download(String configId, HttpServletResponse response) throws IOException;

    /**
     * 下载报表模板
     * @param configId 报表配置id
     * @param response 响应流
     * @return 附件
     */
    String downloadReport(String configId,HttpServletResponse response) throws IOException;

    /**
     * 获取上传模板路径
     * @param id 配置id
     * @param path 路径
     * @return 上传模板路径
     */
    String getTempPath(String id, String path);

    /**
     * 复制报表模板
     * @param reportConfigId 报表模板标识
     * @param code           新code
     * @return 新模板
     */
    DtoReportConfig copyReportConfig(String reportConfigId, String code);
}