package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.customer.DtoInstrumentGatherDataVo;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentGatherData;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 仪器接入数据操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/17
 * @since V100R001
 */
public interface InstrumentGatherDataService extends IBaseJpaService<DtoInstrumentGatherData, String> {
    /**
     * 查询实时数据
     *
     * @param instrumentGatherDataVo 传输实体
     * @return 实时数据
     */
    Map<String, List<Map<String, Object>>> findRealTimeData(DtoInstrumentGatherDataVo instrumentGatherDataVo);

    /**
     * 查询参数数据
     *
     * @param instrumentGatherDataVo 传输实体
     * @return 实时数据
     */
    Map<String, List<Map<String, Object>>> findParamData(DtoInstrumentGatherDataVo instrumentGatherDataVo);


    /**
     * 数据刷新
     *
     * @param instrumentGatherDataVo 传输实体
     */
    void refreshData(DtoInstrumentGatherDataVo instrumentGatherDataVo);

    /**
     * 分页查询结果数据
     *
     * @param pageBean                     分页条件
     * @param instrumentGatherDataCriteria 查询条件
     * @return
     */
    DtoInstrumentGatherDataVo findResultDataPage(PageBean<DtoInstrumentGatherData> pageBean, BaseCriteria instrumentGatherDataCriteria);

    /**
     * 分页查询日志数据
     *
     * @param pageBean                     分页条件
     * @param instrumentGatherDataCriteria 查询条件
     */
    void findLogsPage(PageBean<DtoInstrumentGatherData> pageBean, BaseCriteria instrumentGatherDataCriteria);

    /**
     * 导出结果数据
     *
     * @param criteria 查询条件
     * @param response 响应
     */
    void exportResultData(BaseCriteria criteria, HttpServletResponse response);

    /**
     * 导出日志数据
     *
     * @param criteria 查询条件
     * @param response 响应
     */
    void exportLogs(BaseCriteria criteria, HttpServletResponse response);

}