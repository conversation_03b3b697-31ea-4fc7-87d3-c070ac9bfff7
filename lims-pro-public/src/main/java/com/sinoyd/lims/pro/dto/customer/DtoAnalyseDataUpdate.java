package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DtoAnalyseDataUpdate {

    private String id;

    private String sampleId;

    /**
     * 分析数据ids
     */
    private List<String> analyseDataIds;
    /**
     * 测试项目id
     */
    private String testIds;

    /**
     * 领样时间
     */
    private Date sampleReceiveDate;

    /**
     * 是否同方法
     */
    private Boolean isSameMethod;

    /**
     * 领取人id
     */
    private String reservePersonId;

    public DtoAnalyseDataUpdate(String id, String sampleId) {
        this.id = id;
        this.sampleId = sampleId;
    }
}
