package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.boot.frame.sys.model.OrgModel;
import com.sinoyd.boot.frame.sys.model.UserRoleModel;
import com.sinoyd.boot.frame.sys.service.IOrgService;
import com.sinoyd.boot.frame.sys.service.IUserRoleService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.*;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.DepartmentService;
import com.sinoyd.frame.service.RoleService;
import com.sinoyd.frame.service.UserService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.service.impl.RoleInfoServiceImpl;
import com.sinoyd.frame.util.PinYinUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.core.LimCodeHelper;
import com.sinoyd.lims.lim.criteria.PersonCriteria;
import com.sinoyd.lims.lim.dto.customer.DtoImportPersonExtend;
import com.sinoyd.lims.lim.dto.customer.expimp.DtoExpImpPerson;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.lim.service.transform.ExpImpPersonService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import com.sinoyd.lims.lim.verify.expimp.ImpModifyPersonVerify;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 人员导入导出接口实现
 *
 * @version V1.0.0 2023/12/27
 * @author: hukq
 * @since V100R001
 */
@Service
public class ExpImpPersonServiceImpl extends BaseJpaServiceImpl<DtoPerson, String, PersonRepository> implements ExpImpPersonService {

    private PersonService personService;

    private CodeService codeService;

    private DepartmentService departmentService;

    private RoleInfoServiceImpl roleInfoService;

    private ImportUtils importUtils;

    private UserService userService;

    private IUserRoleService userRoleService;

    private RoleService roleService;
    private IOrgService orgService;

    @Override
    public void export(BaseCriteria baseCriteria, HttpServletResponse response, Map<String, String> sheetNames, String fileName) {
        PageBean<DtoPerson> page = new PageBean<>();
        page.setPageNo(1);
        page.setRowsPerPage(Integer.MAX_VALUE);
        PersonCriteria criteria = (PersonCriteria) baseCriteria;
        personService.findByPage(page, criteria);
        List<DtoPerson> personList = page.getData();

        // 查询部门
        List<DtoDepartment> dtoDepartments = departmentService.findAll();
        //职称
        List<DtoCode> technicalTitleList = codeService.findCodes(LimCodeHelper.TechnicalTitle);
        // 学历
        List<DtoCode> degrees = codeService.findCodes("LIM_Degree");
        // 用户角色信息
        List<DtoUser> userList = userService.findAll();
        List<UserRoleModel> userRoleModels = userRoleService.selectList(new EntityWrapper<>());
        List<DtoRole> roleList = roleService.findAll();

        List<DtoExpImpPerson> dtoExpImpPeople = new ArrayList<>();
        for (DtoPerson person : personList) {
            DtoExpImpPerson expImpPerson = new DtoExpImpPerson();
            BeanUtils.copyProperties(person, expImpPerson);
            expImpPerson.setChineseName(person.getCName());
            // 用户名
            Optional<DtoUser> userOptional = userList.stream().filter(p -> p.getId().equals(person.getId())).findFirst();
            userOptional.ifPresent(p -> expImpPerson.setUserName(p.getUserName()));
            // 部门名称
            String deptName = dtoDepartments.stream().filter(item -> item.getId().equals(person.getDeptId())).map(DtoDepartment::getDeptName).findFirst().orElse("");
            expImpPerson.setDeptId(deptName);
            expImpPerson.setSex(EnumLIM.EnumSex.getName(person.getSex()));
            expImpPerson.setBirthDay(dateToStr(person.getBirthDay()));
            expImpPerson.setJoinCompanyTime(dateToStr(person.getJoinCompanyTime()));
            expImpPerson.setTechnicalTitleDate(dateToStr(person.getTechnicalTitleDate()));
            expImpPerson.setJoinPartyDate(dateToStr(person.getJoinPartyDate()));
            //离职日期
            expImpPerson.setLeaveCompanyTime(dateToStr(person.getLeaveCompanyTime()));
            // 员工状态
            expImpPerson.setStatus(EnumLIM.EnumPersonStatus.getName(person.getStatus()));
            // 放入职称名称
            String technicalTitleName = technicalTitleList.stream().filter(item -> item.getDictCode().equals(person.getTechnicalTitleId())).map(DtoCode::getDictName).findFirst().orElse("");
            expImpPerson.setTechnicalTitleId(technicalTitleName);

            // 角色
            List<UserRoleModel> roleModels = userRoleModels.stream().filter(p -> p.getUserGuid().equals(person.getId())).collect(Collectors.toList());
            List<String> roleIds = roleModels.stream().map(UserRoleModel::getRoleGuid).distinct().collect(Collectors.toList());
            List<DtoRole> roles = roleList.stream().filter(p -> roleIds.contains(p.getRoleId())).collect(Collectors.toList());
            expImpPerson.setRoleNames(roles.stream().map(DtoRole::getRoleName).collect(Collectors.joining(",")));
            dtoExpImpPeople.add(expImpPerson);
        }

        //获取所有的职务
        List<String> postNames = codeService.findCodes("LIM_Post").stream().map(DtoCode::getDictName).collect(Collectors.toList());
        //获取所有的职称
        List<String> titleNames = technicalTitleList.stream().map(DtoCode::getDictName).collect(Collectors.toList());
        //获取所有的学历
        List<String> educationNames = degrees.stream().map(DtoCode::getDictName).collect(Collectors.toList());
        //获取所有的角色
        List<String> roleNames = roleInfoService.findAll().stream().map(DtoRole::getRoleName).collect(Collectors.toList());
        // 获取所有的部门
        List<String> deptNames = dtoDepartments.stream().map(DtoDepartment::getDeptName).collect(Collectors.toList());
        // 获取关联数据
        List<DtoImportPersonExtend> personExtendList = getExtendData(deptNames, roleNames, postNames, titleNames, educationNames);

        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoExpImpPerson.class, DtoImportPersonExtend.class, dtoExpImpPeople, personExtendList);

        importUtils.selectList(workBook,5,5, deptNames.toArray(new String[0]));
        importUtils.selectList(workBook,6,6, postNames.toArray(new String[0]));
        importUtils.selectList(workBook,7,7, titleNames.toArray(new String[0]));
        importUtils.selectList(workBook,9,9, new String[]{"男","女"});
        importUtils.selectList(workBook,18,18, educationNames.toArray(new String[0]));
        // 下载Excel返回响应流
        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
    }


    @Override
    @Transactional
    public List<DtoPerson> importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception {
        PoiExcelUtils.verifyFileType(file);
        //region 业务参数
        Boolean isImportPost = (Boolean) objectMap.get(0);
        Boolean isImportTitle = (Boolean) objectMap.get(1);
        Boolean isImportEducation = (Boolean) objectMap.get(2);
        Boolean isCreateAccount = (Boolean) objectMap.get(3);
        // 系统中的所有人员
        List<DtoPerson> personListAll = repository.findAll();
        //获取所有的职务
        List<DtoCode> dbPostList = codeService.findCodes("LIM_Post");
        //获取所有的职务
        List<DtoCode> dbTitleList = codeService.findCodes("LIM_TechnicalTitle");
        //获取所有的职务
        List<DtoCode> dbEducationList = codeService.findCodes("LIM_Degree");
        //获取所有的部门
        List<DtoDepartment> dbDepartment = departmentService.findAll();
        //需要导入的常量
        List<DtoImportPersonExtend> importPersonExtends = importUtils.getImportNames(file, DtoImportPersonExtend.class);
        // 角色
        List<DtoRole> roleList = roleInfoService.findAll();
        // 初始化校验器
        ImpModifyPersonVerify verifyHandler = getVerifyHandler(isImportPost, isImportTitle, isImportEducation, isCreateAccount,
                personListAll, dbPostList, dbTitleList, dbEducationList, dbDepartment, roleList, importPersonExtends);
        ExcelImportResult<DtoExpImpPerson> importResult = getExcelData(verifyHandler, file, response);
        //获取校验成功得导入数据
        List<DtoExpImpPerson> importList = importResult.getList();
        //移除空行
        importList.removeIf(p -> StringUtil.isEmpty(p.getChineseName()));
        if (StringUtil.isEmpty(importList)) {
            throw new BaseException("文件中无数据，请检查后导入");
        }

        List<DtoUser> userList = userService.findAll();

        //是否关联导入职务
        if (isImportPost) {
            importPost(dbPostList, importPersonExtends);
        }
        //是否关联导入职称
        if (isImportTitle) {
            importTitle(dbTitleList, importPersonExtends);
        }
        //是否关联导入学历
        if (isImportEducation) {
            importEducation(dbEducationList, importPersonExtends);
        }
        //添加数据
        List<DtoPerson> personList = importToEntity(dbDepartment, importList, personListAll);
        //判断最大用户数
        judgeMaxUserNum(personList, userList);
        //保存数据
        if (StringUtil.isNotEmpty(importList)) {
            addData(personList);
        }
        if (isCreateAccount) {
            //开通账户
            importCreateAccount(personList, roleList, userList);
        }
        return personService.findAll();
    }


    /**
     * 获取人员导入校验器
     *
     * @param isImportPost      是否导入职务
     * @param isImportTitle     是否导入职称
     * @param isImportEducation 是否导入学历
     * @param isCreateAccount   是否创建账号
     * @return 校验器
     */
    private ImpModifyPersonVerify getVerifyHandler(Boolean isImportPost,
                                                   Boolean isImportTitle,
                                                   Boolean isImportEducation,
                                                   Boolean isCreateAccount,
                                                   List<DtoPerson> personListAll,
                                                   List<DtoCode> posts,
                                                   List<DtoCode> technicalTitles,
                                                   List<DtoCode> degrees,
                                                   List<DtoDepartment> deptList,
                                                   List<DtoRole> roleList,
                                                   List<DtoImportPersonExtend> importPersonExtends) {
        Map<String, Boolean> relationMap = new HashMap<>();
        relationMap.put("isImportPost", isImportPost);
        relationMap.put("isImportTitle", isImportTitle);
        relationMap.put("isImportEducation", isImportEducation);
        relationMap.put("isCreateAccount", isCreateAccount);
        return new ImpModifyPersonVerify(
                relationMap, posts,
                technicalTitles, degrees,
                deptList, roleList,
                personListAll, importPersonExtends);
    }

    @Override
    @Transactional
    public void addData(List<DtoPerson> data) {
        if (StringUtil.isNotEmpty(data)) {
            repository.save(data);
        }
    }

    @Override
    public ExcelImportResult<DtoExpImpPerson> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        return null;
    }

    /**
     * 获取文件需要导入的数据
     *
     * @param verifyHandler 校验器
     * @param file          传入的文件
     * @param response      响应体
     * @return 导入结果
     */
    @Override
    public ExcelImportResult<DtoExpImpPerson> getExcelData(IExcelVerifyHandler<DtoExpImpPerson> verifyHandler, MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        //设置表头区域
        params.setTitleRows(0);
        //设置表头开始行
        params.setHeadRows(1);
        //设置开始工作簿
        params.setStartSheetIndex(0);
        //设置是否校验
        params.setNeedVerify(true);
        //设置校验handle
        params.setVerifyHandler(verifyHandler);
        ExcelImportResult<DtoExpImpPerson> result = ExcelImportUtil.importExcelMore(file.getInputStream(), DtoExpImpPerson.class, params);
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "人员导入错误信息");
            failWorkbook.removeSheetAt(1);
            PoiExcelUtils.downLoadExcel("人员导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }

    /**
     * 获取关联数据
     *
     * @param deptNames      部门
     * @param roleNames      角色
     * @param postNames      职务
     * @param titleNames     职称
     * @param educationNames 学历
     * @return 关联数据
     */
    private List<DtoImportPersonExtend> getExtendData(List<String> deptNames, List<String> roleNames, List<String> postNames, List<String> titleNames, List<String> educationNames) {
        //返回的数据集合
        List<DtoImportPersonExtend> personExtendList = new ArrayList<>();

        // 获取最长
        List<Integer> size = new ArrayList<>();
        size.add(deptNames.size());
        size.add(roleNames.size());
        size.add(postNames.size());
        size.add(titleNames.size());
        size.add(educationNames.size());
        Integer max = Collections.max(size);
        // 循环赋值第二种Sheet内容
        for (int i = 0; i < max; i++) {
            DtoImportPersonExtend personExtend = new DtoImportPersonExtend();
            personExtend.setDeptName(deptNames.size() < i + 1 ? null : deptNames.get(i));
            personExtend.setRoleName(roleNames.size() < i + 1 ? null : roleNames.get(i));
            personExtend.setPost(postNames.size() < i + 1 ? null : postNames.get(i));
            personExtend.setTitle(titleNames.size() < i + 1 ? null : titleNames.get(i));
            personExtend.setEducation(educationNames.size() < i + 1 ? null : educationNames.get(i));
            personExtendList.add(personExtend);
        }
        return personExtendList;
    }

    /**
     * 日期格式转字符串
     *
     * @param date 日期
     * @return 字符串日期
     */
    private String dateToStr(Date date) {
        Date year1753 = DateUtil.stringToDate("1753-01-01", DateUtil.YEAR);
        String result = "";
        if (StringUtil.isNotNull(date) && date.compareTo(year1753) != 0) {
            result = DateUtil.dateToString(date, DateUtil.YEAR);
        }
        return result;
    }


    /**
     * 判断导入人员是否超过最大用户数
     *
     * @param personList 人员导入数据
     */
    private void judgeMaxUserNum(List<DtoPerson> personList, List<DtoUser> userList) {
        //查询当前导入的机构
        String orgId = PrincipalContextUser.getPrincipal().getOrgId();
        //查询机构数据，获取最大用户数
        OrgModel orgModel = orgService.selectById(orgId);
        //获取机构下用户
        List<DtoUser> orgUsers = userList.stream().filter(p -> orgId.equals(p.getOrgId())).collect(Collectors.toList());
        // 根据导入数据的id 与系统中用户的id汇总，去重后进行比较
        Set<String> ids = personList.stream().map(DtoPerson::getId).collect(Collectors.toSet());
        ids.addAll(orgUsers.stream().map(DtoUser::getId).collect(Collectors.toSet()));
        //判断导入人员数量与机构下用户数量之和是否大于最大用户数
        if (ids.size() > orgModel.getMaxUserNum()) {
            throw new BaseException("导入的人员与系统中人员数量和超过最大用户数, 导入失败!");
        }
    }

    /**
     * 导入实体转换为人员实体
     *
     * @param ImportDeptList 导入的新部门
     * @param importPersons  导入数据
     * @return 人员实体
     */
    private List<DtoPerson> importToEntity(List<DtoDepartment> ImportDeptList, List<DtoExpImpPerson> importPersons, List<DtoPerson> personListAll) {
        List<DtoPerson> personList = new ArrayList<>();
        Map<String, DtoPerson> personMap = personListAll.stream().collect(Collectors.toMap(DtoPerson::getId, p -> p));
        for (DtoExpImpPerson importPerson : importPersons) {
            List<String> deptIds = ImportDeptList.stream().filter(p -> importPerson.getDeptId().equals(p.getDeptName())).map(DtoDepartment::getId).collect(Collectors.toList());
            DtoPerson person = new DtoPerson();
            if (personMap.containsKey(importPerson.getId())) {
                person = personMap.get(importPerson.getId());
            }else {
                importPerson.setId(UUIDHelper.NewID());
            }
            BeanUtils.copyProperties(importPerson, person);
            person.setCName(importPerson.getChineseName());
            person.setFullPinYin(PinYinUtil.getFullSpell(importPerson.getChineseName()));
            person.setPinYin(PinYinUtil.getFirstSpell(importPerson.getChineseName()));
            person.setSex(importPerson.getSex().trim().equals("男") ? 1 : 2);
            person.setTechnicalTitleDate(importPerson.getTechnicalTitleDate() == null ? importUtils.stringToDateAllFormat("1753-01-01") : importUtils.stringToDateAllFormat(importPerson.getTechnicalTitleDate()));
            person.setBirthDay(importPerson.getBirthDay() == null ? importUtils.stringToDateAllFormat("1753-01-01") : importUtils.stringToDateAllFormat(importPerson.getBirthDay()));
            person.setJoinPartyDate(importPerson.getJoinPartyDate() == null ? importUtils.stringToDateAllFormat("1753-01-01") : importUtils.stringToDateAllFormat(importPerson.getJoinPartyDate()));
            person.setJoinCompanyTime(importPerson.getJoinCompanyTime() == null ? importUtils.stringToDateAllFormat("1753-01-01") : importUtils.stringToDateAllFormat(importPerson.getJoinCompanyTime()));
            person.setLeaveCompanyTime(importPerson.getLeaveCompanyTime() == null ? importUtils.stringToDateAllFormat("1753-01-01") : importUtils.stringToDateAllFormat(importPerson.getLeaveCompanyTime()));
            person.setDeptId(StringUtil.isEmpty(deptIds) ? UUIDHelper.GUID_EMPTY : deptIds.get(0));
            person.setYearsInThePosition(BigDecimal.ZERO);
            personList.add(person);
        }
        return personList;
    }

    /**
     * 创建账号
     *
     * @param persons  人员集合
     * @param roleList 角色集合
     * @param userList 系统中已存在的用户
     */
    @Transactional
    public void importCreateAccount(List<DtoPerson> persons, List<DtoRole> roleList, List<DtoUser> userList) {

        Map<String, Object> userMap = userList.stream().collect(Collectors.toMap(DtoUser::getId, p -> p));
        //获取导入时的角色
        for (DtoPerson person : persons) {
            // 根据系统中已存在的用户进行过滤，只开通不存在账户的人员
            if (!userMap.containsKey(person.getId())) {
                List<String> roleNames = new ArrayList<>();
                splitRoleName(roleNames, person);

                //获取需要导入的角色ID
                List<String> roleIds;
                if (StringUtil.isNotEmpty(roleNames)) {
                    roleIds = roleList.stream().filter(p -> roleNames.contains(p.getRoleName())).map(DtoRole::getRoleId).collect(Collectors.toList());
                } else {
                    roleIds = new ArrayList<>();
                }
                List<DtoLoginExpand> loginExpands = new ArrayList<>();
                if (StringUtil.isNotEmpty(person.getUserName())) {
                    writeExpandLogin(loginExpands, person.getUserName(), "userName");
                }
                if (StringUtil.isNotEmpty(person.getEmail())) {
                    writeExpandLogin(loginExpands, person.getEmail(), "email");
                }

                //开通账号
                personService.openAccount(person.getId(), person.getMobile(), null, roleIds, loginExpands);
            }
        }

    }

    /**
     * 获取拓展账号创建
     *
     * @param loginExpands 拓展账号集合
     * @param loginId      开通的Id
     * @param loginType    账号类型
     */
    private void writeExpandLogin(List<DtoLoginExpand> loginExpands, String loginId, String loginType) {
        DtoLoginExpand loginExpandName = new DtoLoginExpand();
        loginExpandName.setLoginId(loginId);
        loginExpandName.setLoginType(loginType);
        loginExpands.add(loginExpandName);
    }

    /**
     * 分割角色
     *
     * @param roleNames 角色集合
     * @param person    需要分割的实体
     */
    private void splitRoleName(List<String> roleNames, DtoPerson person) {
        if (StringUtil.isNotEmpty(person.getRoleNames())) {
            roleNames.addAll(importUtils.personStrToList(person.getRoleNames()));
        }
    }

    /**
     * 导入职务
     *
     * @param dbPost              数据库的常量类型
     * @param importPersonExtends 导入拓展数据实体
     */
    private void importPost(List<DtoCode> dbPost, List<DtoImportPersonExtend> importPersonExtends) {

        //数据库中所有的职务Id
        List<String> postNames = dbPost.stream().map(DtoCode::getDictName).collect(Collectors.toList());
        //不包含的部门(需要导入的部门)
        List<String> isImportPost = importPersonExtends.stream().map(p -> p.getPost() == null ? null : p.getPost()).collect(Collectors.toList());
        importUtils.createCodes(isImportPost, "LIM_Post", postNames);
    }

    /**
     * 导入职称
     *
     * @param dbTitle             职称常量
     * @param importPersonExtends 导入拓展数据实体
     */
    private void importTitle(List<DtoCode> dbTitle, List<DtoImportPersonExtend> importPersonExtends) {
        //数据库中所有的职称Id
        List<String> titleNames = dbTitle.stream().map(DtoCode::getDictName).collect(Collectors.toList());
        //不包含的职称(需要导入的职称)
        List<String> isImportPost = importPersonExtends.stream().map(p -> p.getTitle() == null ? null : p.getTitle()).collect(Collectors.toList());
        importUtils.createCodes(isImportPost, "LIM_TechnicalTitle", titleNames);
    }

    /**
     * 导入学历
     *
     * @param dbEducationList     学历常量
     * @param importPersonExtends 导入拓展数据实体
     */
    private void importEducation(List<DtoCode> dbEducationList, List<DtoImportPersonExtend> importPersonExtends) {
        //数据库中所有的学历Id
        List<String> educationNames = dbEducationList.stream().map(DtoCode::getDictName).collect(Collectors.toList());
        //不包含的学历(需要导入的学历)
        List<String> isImportPost = importPersonExtends.stream().map(p -> p.getEducation() == null ? null : p.getEducation()).collect(Collectors.toList());
        importUtils.createCodes(isImportPost, "LIM_Degree", educationNames);
    }

    @Autowired
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }

    @Autowired
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }

    @Autowired
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    public void setDepartmentService(DepartmentService departmentService) {
        this.departmentService = departmentService;
    }

    @Autowired
    public void setRoleInfoService(RoleInfoServiceImpl roleInfoService) {
        this.roleInfoService = roleInfoService;
    }

    @Autowired
    public void setUserService(UserService userService) {
        this.userService = userService;
    }

    @Autowired
    public void setRoleService(RoleService roleService) {
        this.roleService = roleService;
    }

    @Autowired
    public void setUserRoleService(IUserRoleService userRoleService) {
        this.userRoleService = userRoleService;
    }

    @Autowired
    public void setOrgService(IOrgService orgService) {
        this.orgService = orgService;
    }
}
