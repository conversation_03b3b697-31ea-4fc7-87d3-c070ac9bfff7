package com.sinoyd.base.controller;

import java.util.Collection;
import java.util.List;

import com.sinoyd.base.criteria.DimensionCriteria;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.service.DimensionService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;

import com.sinoyd.frame.controller.BaseJpaController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 量纲管理
 * <AUTHOR>
 * @version V1.0.0 2018/12/12
 * @since V100R001
 */
@RestController
@RequestMapping("/api/base/dimension")
@Validated
public class DimensionController extends BaseJpaController<DtoDimension, String, DimensionService> {

    @Autowired
    DimensionService dimensionService;

    /**
     * 根据rowGuid返回量纲
     *
     * @param rowGuid 量纲RowGuid
     * @return 量纲实体
     */
    @GetMapping("/{rowGuid}")
    public  RestResponse<DtoDimension> getById(@PathVariable String rowGuid) {
        RestResponse<DtoDimension> restResponse = new RestResponse<>();
        DtoDimension entity=dimensionService.findOne(rowGuid);
        restResponse.setData(entity);
        restResponse.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 分页查询
     * 
     * @param dimensionCriteria 参数列表：关键字，量纲类型，排序，分页
     * @return 量纲List
     */
    @GetMapping("")
    public RestResponse<List<DtoDimension>> findByPage(DimensionCriteria dimensionCriteria) {
        RestResponse<List<DtoDimension>>  restResponse = new RestResponse<>();

        PageBean<DtoDimension> page = super.getPageBean();
        service.findByPage(page, dimensionCriteria);

        restResponse.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(page.getData());
        restResponse.setCount(page.getRowsCount());

        return restResponse;
    }

    /**
     * 新增量纲
     * 
     * @param dimension 量纲实体
     * @return 新增的带RowGuid的量纲实体
     */
    @PostMapping("")
    public RestResponse<DtoDimension> create(@Validated @RequestBody DtoDimension dimension) {
        RestResponse<DtoDimension> restResponse = new RestResponse<>();
        restResponse.setData(dimensionService.save(dimension));
        return restResponse;
    }

    /**
     * 批量新增量纲
     *
     * @param dimensionList 量纲实体
     * @return 新增的带RowGuid的量纲实体
     */
    @PostMapping("/batch")
    public RestResponse<List<DtoDimension>> create(@RequestBody List<DtoDimension> dimensionList) {
        RestResponse<List<DtoDimension>> restResponse = new RestResponse<>();
        restResponse.setData(dimensionService.save(dimensionList));
        return restResponse;
    }

    /**
     * 更新量纲
     * 
     * @param dimension 量纲实体
     * @return 更新后的量纲实体
     */
    @PutMapping("")
    public RestResponse<DtoDimension> update(@Validated @RequestBody DtoDimension dimension) {
        RestResponse<DtoDimension> restResponse = new RestResponse<>();
        restResponse.setData(dimensionService.update(dimension));
        return restResponse;
    }

    /**
     * 单个删除量纲
     *
     * @param rowGuid 量纲RowGuid
     * @return 是否删除成功
     */
    @DeleteMapping("/{rowGuid}")
    public RestResponse<Integer> delete(@PathVariable String rowGuid) {
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(dimensionService.logicDeleteById(rowGuid));
        return restResponse;
    }

    /**
     * 批量删除量纲
     *
     * @param rowGuidList 要删除的记录的rowGuid记录List
     * @return 删除的记录数
     */
    @DeleteMapping("")
    public RestResponse<Integer> delete(@RequestBody Collection<String> rowGuidList) {
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(dimensionService.logicDeleteById(rowGuidList));
        return restResponse;
    }

    /**
     * 量纲转换（按Guid）
     *
     * @param toConvertValue 待转换值
     * @param fromDimesionId 原量纲Id
     * @param toDimesionId   目标量纲Id
     * @return 返回转换后结果
     */
    @PostMapping("/convertById")
    public RestResponse<String> convertById(String toConvertValue, String fromDimesionId, String toDimesionId) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setData(dimensionService.convertById(toConvertValue, fromDimesionId, toDimesionId));
        return restResponse;
    }

    /**
     * 量纲转换（按名称）
     * 
     * @param toConvertValue   待转换值
     * @param fromDimesionName 原量纲名称
     * @param toDimesionName   目标量纲名称
     * @return 返回转换后结果
     */
    @PostMapping("/convertByName")
    public RestResponse<String> convertByName(String toConvertValue, String fromDimesionName, String toDimesionName) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setData(dimensionService.convertByName(toConvertValue, fromDimesionName, toDimesionName));
        return restResponse;
    }

    /**
     * 量纲转换（按Guid）
     *
     * @param toConvertValue 待转换值
     * @param fromDimensionId 原量纲Id
     * @param toDimensionId   目标量纲Id
     * @return 返回转换后结果
     */
    @PostMapping("/convertById/v2")
    public RestResponse<String> convertByIdNew(String toConvertValue, String fromDimensionId, String toDimensionId) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setData(dimensionService.convertByIdNew(toConvertValue, fromDimensionId, toDimensionId));
        return restResponse;
    }
}