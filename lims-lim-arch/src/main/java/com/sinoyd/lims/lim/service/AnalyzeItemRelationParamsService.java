package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeItemRelationParams;

import java.util.Collection;
import java.util.List;

/**
 * 分析项目关系
 * <AUTHOR>
 * @version V1.0.0 2019/1/14
 * @since V100R001
 */
public interface AnalyzeItemRelationParamsService extends IBaseJpaService<DtoAnalyzeItemRelationParams, String> {

    /**
     * 分析项目关系参数列表
     *
     * @param relationId 分析项目关系id
     * @param sort       排序
     * @return 分析项目关系参数列表
     */
    List<DtoAnalyzeItemRelationParams> getList(String relationId, String sort);

    /**
     * 创建分析项目关系配置  关联 分析项目
     *
     * @param relationId     关系配置
     * @param analyzeItemIds 多个分析项目ids
     * @return 返回分析项目个数
     */
    Integer create(String relationId, Collection<String> analyzeItemIds);

    /**
     * 删除分析项目关系配置  关联 分析项目
     *
     * @param relationId     关系配置
     * @param analyzeItemIds 删除的分析项目ids
     * @return 返回分析项目个数
     */
    Integer deleteParams(String relationId, Collection<String> analyzeItemIds);

    /**
     * 查询分析项目相关数据
     *
     * @param relationId 关系id
     * @return 返回提醒数据
     */
    List<DtoAnalyzeItemRelationParams> findByRelationId(String relationId);
}