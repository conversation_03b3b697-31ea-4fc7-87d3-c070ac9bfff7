package com.sinoyd.base.enums;

import com.sinoyd.base.dto.vo.HtmlMonitorDataVO;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 枚举类
 *
 * <AUTHOR>
 * @version V1.0.0 2019/3/14
 * @since V100R001
 */
public class EnumBase {

    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumInstrumentStatus {
        报废(0),
        正常(1),
        停用(2),
        过期(3),
        即将过期(4);

        private Integer value;

        public static String EnumInstrumentStatus(Integer value) {
            for (EnumInstrumentStatus c : EnumInstrumentStatus.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }

    /**
     * 溯源方式(枚举：EnumOriginType：1检定、2校准、3自校)
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumOriginType {
        无(-1),
        检定(1),
        校准(2),
        自校(3);

        private Integer value;

        public static EnumOriginType getEnumOriginType(Integer value) {
            for (EnumOriginType c : EnumOriginType.values()) {
                if (c.value.equals(value)) {
                    return c;
                }
            }
            return null;
        }
    }

    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumEnterpriseType {
        污染源(1),
        客户(2),
        供应商(4),
        分包商(8);

        private Integer value;

        public static String EnumEnterpriseType(Integer value) {
            for (EnumEnterpriseType c : EnumEnterpriseType.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }

    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumPollutionSourceType {
        工业污染源(1),
        污水处理厂(2),
        固废处理厂(3);

        private Integer value;

        public static String EnumPollutionSourceType(Integer value) {
            for (EnumPollutionSourceType c : EnumPollutionSourceType.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }

    /**
     * 比对类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumJudgingType {
        实际水样比对(100, 20),
        质控样比对(2, 10),
        替代样比对(3, 30),
        烟气比对(101, 20);
        private Integer value;

        private Integer orderNum;

        public static String getName(Integer value) {
            for (EnumJudgingType enumJudgingType : EnumJudgingType.values()) {
                if (enumJudgingType.getValue().equals(value)) {
                    return enumJudgingType.toString();
                }
            }
            return "";
        }

        public static Integer getOrderNum(Integer value) {
            for (EnumJudgingType enumJudgingType : EnumJudgingType.values()) {
                if (enumJudgingType.getValue().equals(value)) {
                    return enumJudgingType.getOrderNum();
                }
            }
            return 0;
        }
    }

    /**
     * 质控限值判定方式
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumJudgingMethod {
        限值判定(1),
        小于检出限(2),
        回收率(3),
        相对偏差(4),
        相对误差(5),
        绝对偏差(6),
        穿透率(7),
        小于测定下限(8),
        相对准确度(9),
        绝对误差(10),
        范围判定(11),
        标曲A0波动范围(12),
        比值(13);
        private Integer value;

        /**
         * 根据值获取名称
         *
         * @param value 值
         * @return 直接返回名称
         */
        public static String getName(Integer value) {
            for (EnumJudgingMethod enumJudgingMethod : EnumJudgingMethod.values()) {
                if (enumJudgingMethod.getValue().equals(value)) {
                    return enumJudgingMethod.toString();
                }
            }
            return "";
        }
    }

    /**
     * 比对相关检测类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumCheckType {
        废水比对(0, "waterJudgeData"),
        废气比对(1, "gasJudgeData");

        private Integer value;

        private String beanName;

        public static String getName(Integer value) {
            for (EnumCheckType enumCheckType : EnumCheckType.values()) {
                if (enumCheckType.getValue().equals(value)) {
                    return enumCheckType.toString();
                }
            }
            return "";
        }

        public static String getBeanName(Integer value) {
            for (EnumCheckType enumCheckType : EnumCheckType.values()) {
                if (enumCheckType.getValue().equals(value)) {
                    return enumCheckType.getBeanName();
                }
            }
            return "";
        }
    }

    /**
     * 方案调整
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumSchemeSynchronization {
        新增方案(1, "add"),
        修改方案(2, "update"),
        删除方案(3, "deleted");

        private Integer value;

        private String beanName;

        public static String getName(Integer value) {
            for (EnumSchemeSynchronization enumSchemeSynchronization : EnumSchemeSynchronization.values()) {
                if (enumSchemeSynchronization.getValue().equals(value)) {
                    return enumSchemeSynchronization.toString();
                }
            }
            return "";
        }

        public static String getBeanName(Integer value) {
            for (EnumSchemeSynchronization enumSchemeSynchronization : EnumSchemeSynchronization.values()) {
                if (enumSchemeSynchronization.getValue().equals(value)) {
                    return enumSchemeSynchronization.getBeanName();
                }
            }
            return "";
        }
    }

    /**
     * 操作运算符
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumSymbol {
        大于(">"),
        大于等于(">="),
        小于("<"),
        小于等于("<="),
        并且("and");
        private String value;
    }

    /**
     * 样品类型分类
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumSampleTypeCategory {

        检测类型大类(1),
        检测类型小类(2),
        模板(3);

        private Integer value;

        public static String EnumSampleTypeCategory(Integer value) {
            for (EnumSampleTypeCategory c : EnumSampleTypeCategory.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }

    /**
     * 系统类型（预留）
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumSystemType {

        LIMS(1),
        环境质量(2),
        污染源(3);

        private Integer value;

        public static String EnumSystemType(Integer value) {
            for (EnumSystemType c : EnumSystemType.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }

    /**
     * 报表类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumTypeCode {

        报表("LIMReportForms"),
        分析记录单("WorkSheet"),
        现场记录单("Sampling"),
        报告("Report"),
        标签("Sample"),
        方案及合同("Statistic"),
        空("{typeCode}");

        private String value;

        public static String EnumTypeCode(String value) {
            for (EnumTypeCode c : EnumTypeCode.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }

    /**
     * 文檔存儲类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumDocType {
        项目登记附件(1),
        现场踏勘附件(2),
        编制方案合同附件(3),
        二维码(4),
        项目方案合同历史文件(5),
        送样单相关文档(6),
        送样单照片(7),
        样品照片(8),
        采样单(9),
        采样单历史文件(10),
        移动端采样单附件(11),
        原始记录单(12),
        原始记录单历史文件(13),
        仪器解析文件(14),
        报告(15),
        报告历史文件(16),
        分包管理上传(17),
        人员照片(18),
        图片签名(19),
        人员证书(20),
        仪器照片(21),
        仪器相关附件(22),
        仪器使用记录(23),
        仪器维修附件(24),
        仪器报废附件(25),
        消耗品附件(26),
        领料申请附件(27),
        分析方法(28),
        标准查询文档附件(29),
        文件管理(30),
        档案管理(31),
        实验室环境附件(32),
        车辆管理附件(33),
        仪器采购申请附件(34),
        仪器采购计划附件(35),
        消耗品采购申请附件(36),
        消耗品采购计划附件(37),
        系统简介上传附件(38),
        评价标准附件(39),
        申请文件附件(40),
        正式有效版本(41),
        未受控文件(42),
        年度计划附件(43),
        管理评审附件(44),
        内审附件(45),
        监督记录附件(46),
        客户投诉附件(47),
        不合格项报告附件(48),
        潜在不合格项报告附件(49),
        改进措施报告附件(50),
        空白曲线生成图(51),
        加标曲线生成图(52),
        请假申请附件(53),
        加班申请附件(54),
        DLY文件管理(55),
        发送消息附件(56),
        公告附件(57);

        private Integer value;

        public static String EnumDocType(Integer value) {
            for (EnumDocType c : EnumDocType.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }

    /**
     * BAS相关的redis key定义
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumBASRedis {

        /**
         * 量纲的redis key,定义hash 根据量纲id获取对应json信息
         */
        BAS_OrgId_Dimension("BAS:{0}:Dimension"),

        /**
         * 分析项目的redis key,定义hash 根据分析项目id获取对应json信息
         */
        BAS_OrgId_AnalyzeItem("BAS:{0}:AnalyzeItem"),

        /**
         * 评价标准的redis key,定义hash 根据评价标准id获取对应评价标准的信息，及对应的等级信息和等下下的因子限值信息，以及评价下的指标配置信息
         */
        BAS_OrgId_EvaluationCriteria("BAS:{0}:EvaluationCriteria"),

        /**
         * 检测类型的redis key,定义hash 根据检测id获取对应json信息
         */
        BAS_OrgId_SampleType("BAS:{0}:SampleType"),

        /**
         * 偏差公式的redis key,定义hash 根据质控等级质控类型获取对应json信息
         */
        BAS_OrgId_DeviationFormula("BAS:{0}:DeviationFormula");

        private String value;

        public static String EnumBASRedis(String value) {
            for (EnumBASRedis c : EnumBASRedis.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }

        /**
         * 将组织机构id替换成真实key
         *
         * @param value 枚举传入的key
         * @return 返回相应的key值
         */
        public static String getRedisKey(String value) {
            String orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
            return value.replace("{0}", orgId);
        }
    }

    /**
     * 评价标准的标准状态
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumEvaluateCriteriaStatus {
        有效(1),
        废止(2);
        private Integer value;
    }

    /**
     * 评价标准的标准状态
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumIsCheckItem {
        是(1),
        否(2);
        private Integer value;
    }

    /**
     * BAS相关的redis key 通道的定义
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumBASRedisChannel {

        //分析项目修改的通道
        BAS_AnalyzeItem_Update,
        //消耗品详情新增的通道
        BAS_ConsumableDetail_Create,
        //量纲修改后的通道通知
        BAS_Dimension_Update,
    }

    /**
     * 消耗品验收合格的枚举
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumCheckerResult {
        合格(1),
        不合格(2),
        过期(3);
        private Integer value;
    }

    /**
     * 小于检出限值的计算
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumLessExamLimit {
        检出限一半("检出限一半"),
        检出限("检出限"),
        零("0"),
        检测结果("检测结果");
        private String value;
    }

    /**
     * 置信系数对应表
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumCoefficientTable {
        置信五(5, "2.571"),
        置信六(6, "2.447"),
        置信七(7, "2.365"),
        置信八(8, "2.306"),
        置信九(9, "2.262"),
        置信十(10, "2.228"),
        置信十一(11, "2.201"),
        置信十二(12, "1.179"),
        置信十三(13, "2.160"),
        置信十四(14, "2.145"),
        置信十五(15, "2.131"),
        置信十六(16, "2.120");
        private int value;

        private String reValue;

        public static String EnumReValue(int value) {
            for (EnumCoefficientTable c : EnumCoefficientTable.values()) {
                if (c.value == value) {
                    return c.reValue;
                }
            }
            return "";
        }
    }

    /**
     * 修约方式
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumReviseType {
        先修约再比较(1),
        先比较再修约(2);
        private Integer value;
    }

    /**
     * 计算方式
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumComputeMode {
        均值(1),
        最大值(2),
        最小值(3),
        数据均值评价最大值(4);
        private Integer value;
    }

    /**
     * 评价标准 标准类别
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumEvaluationCriteriaType {
        环境质量标准(1),
        污染源排放标准(2);
        private Integer value;
    }

    /**
     * 电子签名状态
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumSignStatus {
        所有(-1),
        未推送(0),
        未签(1),
        已签(2);
        private Integer value;
    }

    /**
     * 不确定度类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumUncertainType {
        浓度(10),
        百分比(20),
        区间(30);

        private final Integer value;

        public static String getNameByValue(Integer value) {
            for(EnumUncertainType c : EnumUncertainType.values()) {
                if (c.getValue().equals(value)) {
                    return c.name();
                }
            }
            return "";
        }

        public static Integer getValueByName(String name) {
            for(EnumUncertainType c : EnumUncertainType.values()) {
                if (c.name().equals(name)) {
                    return c.getValue();
                }
            }
            return 浓度.getValue();
        }

        public static String[] getNames(){
            return Arrays.stream(EnumUncertainType.values()).map(Enum::name).toArray(String[]::new);
        }
    }

    /**
     * 样品分组样品类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumSampleGroupType {
        按分组(1),
        全因子(2),
        单因子(3);
        private Integer value;
    }
    /**
     * 量纲计算类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumDimensionValue {
        微克每立方上标("μg/m³","1000000000"),
        微克每立方("μg/m3","1000000000");

        private String code;

        private String value;

        /**
         * 根据编号返回枚举值
         *
         * @param code 编号
         * @return 返回枚举值
         */
        public static EnumBase.EnumDimensionValue getByCode(String code) {
            for (EnumBase.EnumDimensionValue c : EnumBase.EnumDimensionValue.values()) {
                if (c.getCode().equals(code)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * html解析类型
     */
    @Getter
    @AllArgsConstructor
    public enum EnumHtmlParseType{

        /**
         * 表格标签解析
         */
        TABLE(1, "htmlTableParse");

        /**
         * 枚举值
         */
        private final Integer value;

        /**
         * 解析类型策略类名
         */
        private final String beanName;

        /**
         * 根据枚举值返回枚举项
         *
         * @param value 枚举值
         * @return 返回枚举项
         */
        public static EnumHtmlParseType getEnumItem(Integer value) {
            for (EnumHtmlParseType c : EnumHtmlParseType.values()) {
                if (c.getValue().equals(value)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 文件状态
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumDocumentStatus {
        参照(1),
        受控(2),
        作废(3);
        private final Integer value;

        public static String getNameByValue(Integer value) {
            for(EnumDocumentStatus c : EnumDocumentStatus.values()) {
                if (c.getValue().equals(value)) {
                    return c.name();
                }
            }
            return "";
        }

        public static EnumDocumentStatus getByValue(Integer value) {
            for(EnumDocumentStatus c : EnumDocumentStatus.values()) {
                if (c.getValue().equals(value)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 标准物质领取状态(枚举：EnumConsumableLogStatus：0.待确认 1.已确认  2.已撤销)
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumConsumableLogStatus {
        待确认(0),
        已确认(1),
        已撤销(2);

        private Integer value;

        public static EnumConsumableLogStatus getEnumOriginType(Integer value) {
            for (EnumConsumableLogStatus c : EnumConsumableLogStatus.values()) {
                if (c.value.equals(value)) {
                    return c;
                }
            }
            return null;
        }
    }
}