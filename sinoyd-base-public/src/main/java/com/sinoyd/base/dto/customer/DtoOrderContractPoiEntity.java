package com.sinoyd.base.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合同导入导出  poi实体
 * <AUTHOR>
 * @version V1.0.0 2023/11/30
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DtoOrderContractPoiEntity extends PoiBaseEntity{
    /**
     * 序号
     */
    @Excel(name = "序号",orderNum = "10",width = 20)
    private Integer index;

    /**
     * 合同编号
     */
    @Excel(name = "合同编号",orderNum = "20",width = 40)
    private String contractCode;

    /**
     * 合同名称
     */
    @Excel(name = "合同名称",orderNum = "30",width = 40)
    private String contractName;

    /**
     * 甲方名称
     */
    @Excel(name = "甲方名称",orderNum = "40",width = 40)
    private String firstEntName;

    /**
     * 合同金额
     */
    @Excel(name = "合同金额",orderNum = "50",width = 40)
    private String totalAmount;

    /**
     * 已收款金额
     */
    @Excel(name = "已收款金额",orderNum = "60",width = 40)
    private String recAmountStr;

    /**
     * 未收款金额
     */
    @Excel(name = "未收款金额",orderNum = "70",width = 40)
    private String notRecAmountStr;

    /**
     * 合同开始日期
     */
    @Excel(name = "合同开始日期",orderNum = "80",width = 40)
    private String executeStartTimeStr;

    /**
     * 合同结束日期
     */
    @Excel(name = "合同结束日期",orderNum = "90",width = 40)
    private String executeEndTimeStr;

    /**
     * 签订人
     */
    @Excel(name = "签订人",orderNum = "100",width = 40)
    private String signPerson;

    /**
     * 签订日期
     */
    @Excel(name = "签订日期",orderNum = "110",width = 40)
    private String signDateStr;

    /**
     * 签订状态
     */
    @Excel(name = "签订状态",orderNum = "120",width = 40)
    private String contractStatusStr;
}
