//import com.sinoyd.base.dto.customer.TreeNode;
//import com.sinoyd.base.dto.rcc.DtoSampleType;
//import com.sinoyd.base.enums.EnumBase;
//import com.sinoyd.base.repository.rcc.SampleTypeRepository;
//import com.sinoyd.boot.common.util.StringUtil;
//import com.sinoyd.frame.base.repository.CommonRepository;
//import com.sinoyd.frame.base.util.PageBean;
//import com.sinoyd.frame.util.UUIDHelper;
//import com.sinoyd.lims.api.criteria.ProjectPhoneCriteria;
//import com.sinoyd.lims.api.dto.*;
//import com.sinoyd.lims.api.dto.customer.DtoParamsApiPhoneInfo;
//import com.sinoyd.lims.api.dto.customer.DtoSampleParamsApiPhone;
//import com.sinoyd.lims.api.service.impl.FieldMonitoringServiceImpl;
//import com.sinoyd.lims.lim.criteria.TestCriteria;
//import com.sinoyd.lims.lim.dto.customer.DtoGenerateSN;
//import com.sinoyd.lims.lim.dto.lims.DtoPerson;
//import com.sinoyd.lims.lim.dto.lims.DtoTest;
//import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
//import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
//import com.sinoyd.lims.lim.dto.rcc.DtoSampleTypeGroup2Test;
//import com.sinoyd.lims.lim.repository.lims.PersonRepository;
//import com.sinoyd.lims.lim.repository.rcc.SampleTypeGroup2TestRepository;
//import com.sinoyd.lims.lim.service.ParamsConfigService;
//import com.sinoyd.lims.lim.service.ProjectTypeService;
//import com.sinoyd.lims.lim.service.TestService;
//import com.sinoyd.lims.pro.dto.*;
//import com.sinoyd.lims.pro.enums.EnumPRO;
//import com.sinoyd.lims.pro.repository.*;
//import com.sinoyd.lims.pro.service.*;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.mockito.MockitoAnnotations;
//import org.mockito.runners.MockitoJUnitRunner;
//
//import java.util.*;
//
//import static org.mockito.Mockito.doAnswer;
//import static org.mockito.Mockito.when;
//
//@RunWith(MockitoJUnitRunner.class)
//public class FieldMonitoringServiceImplTest {
//
//    // @InjectMock 注入被测对象, 一般写在最前面
//    @InjectMocks
//    FieldMonitoringServiceImpl fieldMonitoringServiceImpl;
//
//    @Mock
//    private ProjectService projectService;
//
//    @Mock
//    private CommonRepository comRepository;
//
//    @Mock
//    private ReceiveSampleRecordRepository receiveSampleRecordRepository;
//
//    @Mock
//    private SampleRepository sampleRepository;
//
//    @Mock
//    private ProjectTypeService projectTypeService;
//
//    @Mock
//    private PersonRepository personRepository;
//
//    @Mock
//    private SampleTypeRepository sampleTypeRepository;
//
//    @Mock
//    private AnalyseDataRepository analyseDataRepository;
//
//    @Mock
//    private ReceiveSampleRecordService receiveSampleRecordService;
//
//    @Mock
//    private SampleFolderRepository sampleFolderRepository;
//
//    @Mock
//    private FolderSignRepository folderSignRepository;
//
//    @Mock
//    private SchemeService schemeService;
//
//    @Mock
//    private SampleService sampleService;
//
//    @Mock
//    private ReceiveSubSampleRecordRepository receiveSubSampleRecordRepository;
//
//    @Mock
//    private SampleTypeGroup2TestRepository sampleTypeGroup2TestRepository;
//
//    @Mock
//    private TestService testService;
//
//    @Mock
//    private ProService proService;
//
//    @Mock
//    private SampleGroupRepository sampleGroupRepository;
//
//    @Mock
//    private ParamsConfigService paramsConfigService;
//
//    @Mock
//    private ParamsDataRepository paramsDataRepository;
//
//    @Mock
//    private ParamsDataService paramsDataService;
//
//    @Mock
//    private NewLogService newLogService;
//
//    @Mock
//    private SampleGroupService sampleGroupService;
//
//    @Mock
//    private SamplingPersonConfigRepository samplingPersonConfigRepository;
//
//
//    // 执行测试之前执行
//    @Before
//    public void setup() {
//        // 初始化 mock 注入环境
//        MockitoAnnotations.initMocks(this);
//    }
//
//    @Test
//    public void testGetProjectList() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//
//        ProjectPhoneCriteria projectPhoneCriteria = new ProjectPhoneCriteria();
//        PageBean<DtoProject> pageBean = new PageBean<>();
//        List<String> projectIds = new ArrayList<>();
//        List<Object[]> datas = new ArrayList<>();
//        DtoProject dtoProject = new DtoProject();
//        dtoProject.setId("tstId");
//        dtoProject.setProjectTypeId("tstTypeId");
//        Object[] objects = new Object[2];
//        objects[0] = "tstId";
//        objects[1] = "tstTypeId";
//        datas.add(objects);
//        projectIds.add(dtoProject.getId());
//        List<String> typeIds = new ArrayList<>();
//        typeIds.add(dtoProject.getProjectTypeId());
//
//        doAnswer(invocation -> {
//            PageBean<DtoProject> arg2 = invocation.getArgumentAt(0, PageBean.class);
//            arg2.setData(new ArrayList<>());
//            return null;
//        }).when(comRepository).findByPage(pageBean, projectPhoneCriteria);
//        List<DtoReceiveSampleRecord> receiveSampleRecordList = new ArrayList<>();
//        List<DtoSample> sampleList = new ArrayList<>();
//        when(receiveSampleRecordRepository.findByProjectIdIn(Mockito.any(List.class))).thenReturn(receiveSampleRecordList);
//        when(sampleRepository.findByProjectIdIn(Mockito.any(List.class))).thenReturn(sampleList);
//        List<DtoProjectType> projectTypeList = new ArrayList<>();
//        when(projectTypeService.findAll(Mockito.any(List.class))).thenReturn(projectTypeList);
//
//
//        List<DtoProjectPhone> projectPhoneList = fieldMonitoringServiceImpl.getProjectList(pageBean, projectPhoneCriteria);
//        // 断言：判断成功
//        Assert.assertTrue(projectPhoneList.isEmpty());
//    }
//
//
//    @Test
//    public void testGetProjectTypeList() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//
//        String code = "tstCode";
//        String[] values = new String[]{};
//
//        when(projectTypeService.getProjectTypeByCode(code, values)).thenReturn(new ArrayList<>());
//
//        List<Map<String, Object>> list = fieldMonitoringServiceImpl.getProjectTypeList(code, values);
//        // 断言：判断成功
//        Assert.assertTrue(list.isEmpty());
//    }
//
//
//    @Test
//    public void testGetProjectDetailById() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//
//        String id = "tstId";
//        DtoProject project = new DtoProject();
//        project.setLeaderId("tstLeaderId");
//        when(projectService.findOne(id)).thenReturn(project);
//        DtoPerson person = new DtoPerson();
//        when(personRepository.findOne(project.getLeaderId())).thenReturn(person);
//        DtoPerson person2 = new DtoPerson();
//        when(personRepository.findOne(project.getReportMakerId())).thenReturn(person2);
//        DtoReceiveSampleRecord record = new DtoReceiveSampleRecord();
//        List<DtoReceiveSampleRecord> receiveSampleRecordList = new ArrayList<>();
//        receiveSampleRecordList.add(record);
//        when(receiveSampleRecordRepository.findByProjectId(id)).thenReturn(receiveSampleRecordList);
//        List<DtoReceiveSubSampleRecord> subSampleRecordList = new ArrayList<>();
//        DtoReceiveSubSampleRecord dtoReceiveSubSampleRecord = new DtoReceiveSubSampleRecord();
//        dtoReceiveSubSampleRecord.setCode("XC");
//        dtoReceiveSubSampleRecord.setId("subId");
//        subSampleRecordList.add(dtoReceiveSubSampleRecord);
//        when(receiveSubSampleRecordRepository.findByReceiveId(record.getId())).thenReturn(subSampleRecordList);
//        DtoReceiveSampleRecord receiveSampleRecord = new DtoReceiveSampleRecord();
//        when(receiveSampleRecordService.findOne(record.getId())).thenReturn(receiveSampleRecord);
//
//        DtoProjectDetailPhone res = fieldMonitoringServiceImpl.getProjectDetailById(id);
//        // 断言：判断成功
//        Assert.assertEquals("projectDetail not match", "subId", res.getSubXCId());
//    }
//
//    @Test
//    public void testGetFolderList() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//
//        DtoFolderInfo folder = new DtoFolderInfo();
//        folder.setProjectId("tstProId");
//        folder.setTimesOrder(1);
//        folder.setFolderName("tstFoldName");
//        folder.setSampleIds(new ArrayList<>());
//        folder.setCycleOrder(1);
//
//        List<DtoSampleFolder> dataList = new ArrayList<>();
//        DtoSampleFolder dtoSampleFolder = new DtoSampleFolder();
//        dtoSampleFolder.setId("tstId");
//        dtoSampleFolder.setSampleTypeId("tstTypeId");
//        dataList.add(dtoSampleFolder);
//        String select = "tstSql";
//        Map<String, Object> values = new HashMap<>();
//        when(comRepository.find(select.toString(), values)).thenReturn(dataList);
//        List<DtoSampleType> sampleTypeList = new ArrayList<>();
//        when(sampleTypeRepository.findAll(Mockito.any(List.class))).thenReturn(sampleTypeList);
//        List<DtoSample> sampleList = new ArrayList<>();
//        DtoSample dtoSample = new DtoSample();
//        dtoSample.setId("sampleId");
//        dtoSample.setReceiveId("recId");
//        sampleList.add(dtoSample);
//        when(sampleRepository.findBySampleFolderIdIn(Mockito.any(List.class))).thenReturn(sampleList);
//        List<DtoReceiveSampleRecord> receiveSampleRecordList = new ArrayList<>();
//        when(receiveSampleRecordRepository.findAll(Mockito.any(List.class))).thenReturn(receiveSampleRecordList);
//        List<DtoSample> qcSampleList = new ArrayList<>();
//        when(sampleRepository.findByAssociateSampleIdIn(Mockito.any(List.class))).thenReturn(qcSampleList);
//        List<DtoAnalyseData> analyseDataList = new ArrayList<>();
//        when(analyseDataRepository.findBySampleIdInAndIsDeletedFalse(Mockito.any(List.class))).thenReturn(analyseDataList);
//
//
//        //getFolderInfo
//
//
//        List<DtoSampleFolderPhone> folderPhoneList = fieldMonitoringServiceImpl.getFolderList(folder.getProjectId(),
//                folder.getFolderName(), folder.getSampleTypeIds(), folder.getCycleOrder(), folder.getTimesOrder());
//        // 断言：判断成功
//        Assert.assertTrue(folderPhoneList.isEmpty());
//    }
//
//
//    @Test
//    public void testCreateReceiveSampleRecord() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//
//        DtoRecordInfo recordInfo = new DtoRecordInfo();
//        recordInfo.setProjectId("proId");
//        recordInfo.setFolderInfo(new ArrayList<>());
//        recordInfo.setSamplingPersonIds(new ArrayList<>());
//        recordInfo.setSamplingLeaderId("leaderId");
//        recordInfo.setSamplingLeaderName("leaderName");
//        recordInfo.setSamplingTime(new Date());
//
//        DtoProject project = new DtoProject();
//        when(projectService.findOne(recordInfo.getProjectId())).thenReturn(project);
//
//        //getSampleList
//        List<DtoSample> sampleList = new ArrayList<>();
//        DtoSample dtoSample = new DtoSample();
//        dtoSample.setId("sampleId");
//        sampleList.add(dtoSample);
//        when(sampleRepository.findByProjectId(recordInfo.getProjectId())).thenReturn(sampleList);
//        List<DtoSample> qcSampleList = new ArrayList<>();
//        DtoSample qcSample = new DtoSample();
//        qcSampleList.add(qcSample);
//        when(sampleRepository.findByAssociateSampleIdIn(Mockito.any(List.class))).thenReturn(qcSampleList);
//
//        DtoReceiveSampleRecord record = new DtoReceiveSampleRecord();
//        when(receiveSampleRecordService.createReceiveRecordToPhone(Mockito.any(DtoProject.class), Mockito.any(List.class),
//                Mockito.any(Date.class), Mockito.eq("leaderId"), Mockito.eq("leaderName"), Mockito.any(List.class))).thenReturn(record);
//
//        fieldMonitoringServiceImpl.createReceiveSampleRecord(recordInfo.getProjectId(),
//                recordInfo.getFolderInfo(), recordInfo.getSamplingPersonIds(), recordInfo.getSamplingLeaderId(),
//                recordInfo.getSamplingLeaderName(), recordInfo.getSamplingTime());
//    }
//
//    @Test
//    public void testJoinToReceiveSampleRecord() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//
//        DtoRecordInfo recordInfo = new DtoRecordInfo();
//        recordInfo.setProjectId("projectId");
//        recordInfo.setReceiveId("recId");
//        recordInfo.setFolderInfo(new ArrayList<>());
//
//        //getSampleList
//        List<DtoSample> sampleList = new ArrayList<>();
//        DtoSample dtoSample = new DtoSample();
//        dtoSample.setId("sampleId");
//        sampleList.add(dtoSample);
//        when(sampleRepository.findByProjectId(recordInfo.getProjectId())).thenReturn(sampleList);
//        List<DtoSample> qcSampleList = new ArrayList<>();
//        DtoSample qcSample = new DtoSample();
//        qcSampleList.add(qcSample);
//        when(sampleRepository.findByAssociateSampleIdIn(Mockito.any(List.class))).thenReturn(qcSampleList);
//
//        DtoProject project = new DtoProject();
//        when(projectService.findOne(recordInfo.getProjectId())).thenReturn(project);
//        fieldMonitoringServiceImpl.joinToReceiveSampleRecord(recordInfo.getProjectId(), recordInfo.getReceiveId(), recordInfo.getFolderInfo());
//
//    }
//
//    @Test
//    public void testGetReceiveSampleRecordByProjectId() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//
//        String projectId = "projectId";
//        List<String> sampleTypeIds = new ArrayList<>();
//        List<DtoReceiveSampleRecord> receiveSampleRecordList = new ArrayList<>();
//        DtoReceiveSampleRecord receiveSampleRecord = new DtoReceiveSampleRecord();
//        receiveSampleRecord.setId("tstId");
//        receiveSampleRecord.setSamplingTime(new Date());
//        receiveSampleRecordList.add(receiveSampleRecord);
//        when(receiveSampleRecordRepository.findByProjectId(projectId)).thenReturn(receiveSampleRecordList);
//        List<DtoSample> sampleList = new ArrayList<>();
//        DtoSample dtoSample = new DtoSample();
//        dtoSample.setReceiveId("recId");
//        sampleList.add(dtoSample);
//        when(sampleRepository.findByProjectId(projectId)).thenReturn(sampleList);
//        List<DtoSampleFolder> sampleFolderList = new ArrayList<>();
//        DtoSampleFolder dtoSampleFolder = new DtoSampleFolder();
//        dtoSampleFolder.setId("tstFolderId");
//        sampleFolderList.add(dtoSampleFolder);
//        when(sampleFolderRepository.findByProjectId(projectId)).thenReturn(sampleFolderList);
//        List<DtoSamplingPersonConfig> personCfgs = new ArrayList<>();
//        DtoSamplingPersonConfig dtoSamplingPersonConfig = new DtoSamplingPersonConfig();
//        dtoSamplingPersonConfig.setObjectId("tstId");
//        personCfgs.add(dtoSamplingPersonConfig);
//        when(samplingPersonConfigRepository.findByObjectIdIn(Mockito.any(List.class))).thenReturn(personCfgs);
//        List<DtoSampleType> sampleTypeList = new ArrayList<>();
//        DtoSampleType dtoSampleType = new DtoSampleType();
//        dtoSampleType.setId("typeId");
//        sampleTypeList.add(dtoSampleType);
//        when(sampleTypeRepository.findAll(Mockito.any(List.class))).thenReturn(sampleTypeList);
//
//        List<DtoReceiveSampleRecordPhone> recordList = fieldMonitoringServiceImpl.getReceiveSampleRecordByProjectId(projectId, sampleTypeIds,false);
//        // 断言：判断成功
//        Assert.assertTrue(!recordList.isEmpty());
//    }
//
//    @Test
//    public void testGetFolderById() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//
//        DtoFolderInfo folder = new DtoFolderInfo();
//        folder.setFolderId("folderId");
//        folder.setCycleOrder(1);
//        DtoSampleFolder folder2 = new DtoSampleFolder();
//        folder2.setSampleTypeId("typeId");
//        when(sampleFolderRepository.findOne(folder.getFolderId())).thenReturn(folder2);
//        List<DtoSample> sampleList = new ArrayList<>();
//        DtoSample dtoSample = new DtoSample();
//        dtoSample.setId("sampleId");
//        sampleList.add(dtoSample);
//        when(sampleRepository.findBySampleFolderId(folder.getFolderId())).thenReturn(sampleList);
//        List<String> qcSampleIds = new ArrayList<>();
//        when(sampleRepository.findByAssociateSampleIdIn(Mockito.any(List.class))).thenReturn(qcSampleIds);
//        DtoSampleType sampleType = new DtoSampleType();
//        when(sampleTypeRepository.findOne(folder2.getSampleTypeId())).thenReturn(sampleType);
//        List<DtoAnalyseData> analyseDataList = new ArrayList<>();
//        when(analyseDataRepository.findBySampleIdInAndIsDeletedFalse(Mockito.any(List.class))).thenReturn(analyseDataList);
//        List<DtoFolderSign> folderSignList = new ArrayList<>();
//        when(folderSignRepository.findBySampleFolderIdAndCycleOrder(folder.getFolderId(), folder.getCycleOrder())).thenReturn(folderSignList);
//
//        DtoSampleFolderPhone folderPhone = fieldMonitoringServiceImpl.getFolderById(folder.getFolderId(), folder.getCycleOrder());
//        // 断言：判断成功
//        Assert.assertTrue(StringUtil.isNotNull(folderPhone));
//    }
//
//    @Test
//    public void testGetTestList() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//
//        PageBean<DtoTest> pageBean = new PageBean<>();
//        TestCriteria testCriteria = new TestCriteria();
//
//        doAnswer(invocation -> {
//            PageBean<DtoTest> arg2 = invocation.getArgumentAt(0, PageBean.class);
//            arg2.setData(new ArrayList<>());
//            return null;
//        }).when(comRepository).findByPage(pageBean, testCriteria);
//
//        List<DtoAnalyseDataPhone> testPhoneList = fieldMonitoringServiceImpl.getTestList(pageBean, testCriteria);
//        // 断言：判断成功
//        Assert.assertTrue(testPhoneList.isEmpty());
//    }
//
//    @Test
//    public void testSaveSignInfo() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//        DtoFolderInfo folder = new DtoFolderInfo();
//        folder.setFolderId("folderId");
//        folder.setCycleOrder(1);
//        folder.setLon("1");
//        folder.setLat("1");
//        folder.setSignTime(new Date());
//        DtoSampleFolder sampleFolder = new DtoSampleFolder();
//        when(sampleFolderRepository.findOne(folder.getFolderId())).thenReturn(sampleFolder);
//
//        fieldMonitoringServiceImpl.saveSignInfo(folder.getFolderId(), folder.getCycleOrder(), folder.getLon(),
//                folder.getLat(), folder.getSignTime(), folder.getSignTip(), folder.getIsVerify(), folder.getVoiceTip());
//        // 断言：判断成功
////        Assert.assertTrue(null);
//    }
//
//    @Test
//    public void testGetSampleList() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//        DtoFolderInfo folder = new DtoFolderInfo();
//        folder.setProjectId("projectId");
//        folder.setFolderId("folderId");
//        folder.setCycleOrder(1);
//
//        //getSampleList(string,string,string,integer)
//        List<DtoSample> sampleList = new ArrayList<>();
//        DtoSample dtoSample = new DtoSample();
//        dtoSample.setId("sampleId");
//        sampleList.add(dtoSample);
//        when(sampleRepository.findBySampleFolderId(folder.getFolderId())).thenReturn(sampleList);
//        List<DtoSample> qcSampleList = new ArrayList<>();
//        DtoSample qcSample = new DtoSample();
//        qcSampleList.add(qcSample);
//        when(sampleRepository.findByAssociateSampleIdIn(Mockito.any(List.class))).thenReturn(qcSampleList);
//        when(sampleService.sortPrepareSample(Mockito.any(List.class), Mockito.eq(false))).thenReturn(sampleList);
//
//        List<DtoSample> samList = fieldMonitoringServiceImpl.getSampleList(folder.getFolderId(), folder.getProjectId(), folder.getReceiveId(), folder.getCycleOrder());
//
//        // 断言：判断成功
//        Assert.assertTrue(samList.isEmpty());
//    }
//
//    @Test
//    public void testRemoveQcSample() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//
//        DtoFolderInfo folder = new DtoFolderInfo();
//        folder.setProjectId("projectId");
//        folder.setSampleIds(new ArrayList<>());
//
//        List<DtoSample> sampleList = new ArrayList<>();
//        DtoSample dtoSample = new DtoSample();
//        dtoSample.setSampleCategory(1);
//        sampleList.add(dtoSample);
//        when(sampleRepository.findByIds(Mockito.any(List.class))).thenReturn(sampleList);
//
//        fieldMonitoringServiceImpl.removeQcSample(folder.getProjectId(), folder.getSampleIds());
//    }
//
//    @Test
//    public void testCreateSampleCode() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//        DtoFolderInfo folder = new DtoFolderInfo();
//        folder.setProjectId("projectId");
//        folder.setSampleIds(new ArrayList<>());
//        folder.setSamplingTime(new Date());
//
//        //createSampleCode(List<String>, String, Date,String)
//        List<DtoSample> samples = new ArrayList<>();
//        DtoSample dtoSample = new DtoSample();
//        dtoSample.setSampleTypeId("typeId");
//        when(sampleRepository.findAll(Mockito.any(List.class))).thenReturn(samples);
//        DtoProject project = new DtoProject();
//        when(projectService.findOne(folder.getProjectId())).thenReturn(project);
//        DtoProjectType projectType = new DtoProjectType();
//        when(projectTypeService.findOne(project.getProjectTypeId())).thenReturn(projectType);
//        List<DtoSample> samList = new ArrayList<>();
//        when(sampleService.sortPrepareSample(Mockito.any(List.class), Mockito.eq(true))).thenReturn(samList);
//        DtoGenerateSN targetGenerateSN = new DtoGenerateSN();
//        when(sampleService.createSampleCode(Mockito.any(DtoProject.class), Mockito.any(DtoProjectType.class), Mockito.eq("typeId"),
//                Mockito.eq(null), Mockito.any(Date.class), UUIDHelper.GUID_EMPTY, UUIDHelper.GUID_EMPTY, Mockito.eq(true), Mockito.eq(null), Mockito.eq(true), Mockito.eq(null), Mockito.eq(null),
//                Mockito.eq(null), Mockito.eq(false), Mockito.eq(""))).thenReturn(targetGenerateSN);
//
//
//        fieldMonitoringServiceImpl.createSampleCode(folder.getSampleIds(), folder.getProjectId(), folder.getSamplingTime());
//
//    }
//
//    @Test
//    public void testGetSampleDetail() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//        DtoFolderInfo folder = new DtoFolderInfo();
//        folder.setSampleId("sampleId");
//        DtoSample sample = new DtoSample();
//        when(sampleRepository.findOne(folder.getSampleId())).thenReturn(sample);
//        DtoSampleType sampleType = new DtoSampleType();
//        when(sampleTypeRepository.findOne(sample.getSampleTypeId())).thenReturn(sampleType);
//        List<DtoAnalyseData> analyseDataList = new ArrayList<>();
//        when(analyseDataRepository.findBySampleIdAndIsDeletedFalse(folder.getSampleId())).thenReturn(analyseDataList);
//
//        DtoSamplePhone sample2 = fieldMonitoringServiceImpl.getSampleDetail(folder.getSampleId());
//
//        // 断言：判断成功
//        Assert.assertTrue(StringUtil.isNotNull(sample2));
//    }
//
//    @Test
//    public void testGetFolderById3() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//        String receiveId = "recId";
//        DtoReceiveSampleRecord receiveSampleRecord = new DtoReceiveSampleRecord();
//        receiveSampleRecord.setJson("{}");
//        when(receiveSampleRecordService.findOne(receiveId)).thenReturn(receiveSampleRecord);
//        List<DtoReceiveSubSampleRecord> subSampleRecordList = new ArrayList<>();
//        when(receiveSubSampleRecordRepository.findByReceiveId(receiveId)).thenReturn(subSampleRecordList);
//        List<String> sampleTypeIds = new ArrayList<>();
//        List<DtoSample> sampleList = new ArrayList<>();
//        when(sampleRepository.findByReceiveId(receiveId)).thenReturn(sampleList);
//        when(sampleTypeRepository.findAll(sampleTypeIds)).thenReturn(new ArrayList<>());
//        DtoPerson person = new DtoPerson();
//        when(personRepository.findOne(receiveSampleRecord.getSenderId())).thenReturn(person);
//
//        DtoReceiveSampleRecordPhone record = fieldMonitoringServiceImpl.getRecordById(receiveId);
//        // 断言：判断成功
//        Assert.assertTrue(StringUtil.isNotNull(record));
//    }
//
//    @Test
//    public void testSubmitReceive() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//        DtoRecordInfo recordInfo = new DtoRecordInfo();
//        recordInfo.setReceiveIds(new ArrayList<>());
//        List<DtoReceiveSampleRecord> receiveSampleRecordList = new ArrayList<>();
//        when(receiveSampleRecordRepository.findAll(recordInfo.getReceiveIds())).thenReturn(receiveSampleRecordList);
//        when(receiveSampleRecordRepository.findByProjectIdIn(recordInfo.getReceiveIds())).thenReturn(receiveSampleRecordList);
//
//        fieldMonitoringServiceImpl.submitReceive(recordInfo.getReceiveIds());
//
//        // 断言：判断成功
////        Assert.assertTrue(null);
//    }
//
//    @Test
//    public void testSaveRecordInfo() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//        DtoReceiveSampleRecordPhone receiveSampleRecordPhone = new DtoReceiveSampleRecordPhone();
//        receiveSampleRecordPhone.setSamplingPersonNames("a,b");
//        fieldMonitoringServiceImpl.saveRecordInfo(receiveSampleRecordPhone);
//
//        // 断言：判断成功
////        Assert.assertTrue(null);
//    }
//
//    @Test
//    public void testGetFolderInfoByReceiveId() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//        DtoFolderInfo folder = new DtoFolderInfo();
//        folder.setReceiveId("recId");
//        List<DtoSample> sampleList = new ArrayList<>();
//        when(sampleRepository.findByReceiveId(folder.getReceiveId())).thenReturn(sampleList);
//        List<DtoSampleFolder> sampleFolderList = new ArrayList<>();
//        when(sampleFolderRepository.findAll(Mockito.any(List.class))).thenReturn(sampleFolderList);
//        List<DtoSampleType> sampleTypeList = new ArrayList<>();
//        when(sampleTypeRepository.findAll(Mockito.any(List.class))).thenReturn(sampleTypeList);
//        List<DtoAnalyseData> analyseDataList = new ArrayList<>();
//        when(analyseDataRepository.findBySampleIdInAndIsDeletedFalse(Mockito.any(List.class))).thenReturn(analyseDataList);
//        DtoReceiveSampleRecord receiveSampleRecord = new DtoReceiveSampleRecord();
//        when(receiveSampleRecordRepository.findOne(folder.getReceiveId())).thenReturn(receiveSampleRecord);
//
//        fieldMonitoringServiceImpl.getFolderInfoByReceiveId(folder.getReceiveId());
//
//        // 断言：判断成功
////        Assert.assertTrue(null);
//    }
//
//    @Test
//    public void testRemoveSample() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//        DtoRecordInfo recordInfo = new DtoRecordInfo();
//        recordInfo.setFolderInfo(new ArrayList<>());
//        recordInfo.setReceiveId("recId");
//
//        //getSampleList
//        List<DtoSample> sampleList = new ArrayList<>();
//        DtoSample dtoSample = new DtoSample();
//        dtoSample.setId("sampleId");
//        sampleList.add(dtoSample);
//        when(sampleRepository.findByProjectId(recordInfo.getProjectId())).thenReturn(sampleList);
//        List<DtoSample> qcSampleList = new ArrayList<>();
//        DtoSample qcSample = new DtoSample();
//        qcSampleList.add(qcSample);
//        when(sampleRepository.findByAssociateSampleIdIn(Mockito.any(List.class))).thenReturn(qcSampleList);
//
//        DtoReceiveSampleRecord record = new DtoReceiveSampleRecord();
//        when(receiveSampleRecordService.createReceiveRecordToPhone(Mockito.any(DtoProject.class), Mockito.any(List.class),
//                Mockito.any(Date.class), Mockito.eq("leaderId"), Mockito.eq("leaderName"), Mockito.any(List.class))).thenReturn(record);
//
//        fieldMonitoringServiceImpl.removeSample(recordInfo.getReceiveId(), recordInfo.getFolderInfo());
//
//        // 断言：判断成功
////        Assert.assertTrue(null);
//    }
//
//    @Test
//    public void testRemoveOutSample() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//        DtoRecordInfo recordInfo = new DtoRecordInfo();
//        recordInfo.setReceiveId("recId");
//        recordInfo.setFolderInfo(new ArrayList<>());
//
//        //getSampleList
//        List<DtoSample> sampleList = new ArrayList<>();
//        DtoSample dtoSample = new DtoSample();
//        dtoSample.setId("sampleId");
//        sampleList.add(dtoSample);
//        when(sampleRepository.findByProjectId(recordInfo.getProjectId())).thenReturn(sampleList);
//        List<DtoSample> qcSampleList = new ArrayList<>();
//        DtoSample qcSample = new DtoSample();
//        qcSampleList.add(qcSample);
//        when(sampleRepository.findByAssociateSampleIdIn(Mockito.any(List.class))).thenReturn(qcSampleList);
//
//        DtoReceiveSampleRecord record = new DtoReceiveSampleRecord();
//        when(receiveSampleRecordService.createReceiveRecordToPhone(Mockito.any(DtoProject.class), Mockito.any(List.class),
//                Mockito.any(Date.class), Mockito.eq("leaderId"), Mockito.eq("leaderName"), Mockito.any(List.class))).thenReturn(record);
//
//        fieldMonitoringServiceImpl.removeOutSample(recordInfo.getReceiveId(), recordInfo.getFolderInfo());
//
//        // 断言：判断成功
////        Assert.assertTrue(null);
//    }
//
//    @Test
//    public void testGetFolderParams() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//        DtoRecordInfo recordInfo = new DtoRecordInfo();
//        DtoParamsApiPhoneInfo paramsInfo = new DtoParamsApiPhoneInfo();
//        paramsInfo.setSampleFolderId("sampleFolderId");
//        paramsInfo.setProjectId("projectId");
//        paramsInfo.setReceiveId("recId");
//        paramsInfo.setSampleTypeId("typeId");
//        paramsInfo.setCycleOrder(1);
//
////        getSampleList(string,string,string,integer)
//        List<DtoSample> sampleList = new ArrayList<>();
//        DtoSample sample = new DtoSample();
//        sampleList.add(sample);
//        when(sampleRepository.findBySampleFolderId(paramsInfo.getSampleFolderId())).thenReturn(sampleList);
//        List<DtoSample> qcSamList = new ArrayList<>();
//        when(sampleRepository.findByAssociateSampleIdIn(Mockito.any(List.class))).thenReturn(qcSamList);
//
//        when(sampleService.sortPrepareSample(Mockito.any(List.class), Mockito.eq(false))).thenReturn(sampleList);
//        List<DtoAnalyseData> analyseDataList = new ArrayList<>();
//        when(analyseDataRepository.findBySampleIdInAndIsDeletedFalse(Mockito.any(List.class))).thenReturn(analyseDataList);
//        List<DtoParamsConfig> paramsConfigs = new ArrayList<>();
//        when(paramsConfigService.findBySampleTypeId(Mockito.eq(paramsInfo.getSampleTypeId()), Mockito.any(List.class))).thenReturn(paramsConfigs);
//        when(paramsConfigService.findBySampleTypeId(paramsInfo.getSampleTypeId())).thenReturn(paramsConfigs);
//        List<DtoParamsData> paramsDataList = new ArrayList<>();
//        when(paramsDataRepository.findByObjectIdInAndObjectType(Mockito.any(List.class), Mockito.eq(EnumPRO.EnumParamsDataType.样品.getValue()))).thenReturn(paramsDataList);
//
//
//        fieldMonitoringServiceImpl.getFolderParams(paramsInfo.getSampleFolderId(), paramsInfo.getProjectId(), paramsInfo.getReceiveId(), paramsInfo.getSampleTypeId(), paramsInfo.getCycleOrder());
//
//        // 断言：判断成功
//        Assert.assertTrue(!sampleList.isEmpty());
//    }
//
//
//    @Test
//    public void testGetSampleParams() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//        DtoParamsApiPhoneInfo paramsInfo = new DtoParamsApiPhoneInfo();
//        paramsInfo.setSampleId("sampleId");
//        paramsInfo.setSampleTypeId("typeId");
//        paramsInfo.setIsEnterValue(1);
//
//        DtoSample sample = new DtoSample();
//        when(sampleRepository.findOne(paramsInfo.getSampleId())).thenReturn(sample);
//        List<DtoAnalyseData> analyseDataList = new ArrayList<>();
//        when(analyseDataRepository.findBySampleIdAndIsDeletedFalse(paramsInfo.getSampleId())).thenReturn(analyseDataList);
//        List<DtoParamsConfig> paramsConfigs = new ArrayList<>();
//        when(paramsConfigService.findBySampleTypeId(Mockito.eq(paramsInfo.getSampleTypeId()), Mockito.any(List.class))).thenReturn(paramsConfigs);
//        when(paramsConfigService.findBySampleTypeId(paramsInfo.getSampleTypeId())).thenReturn(paramsConfigs);
//        List<DtoParamsData> paramsDataList = new ArrayList<>();
//        when(paramsDataRepository.findByObjectIdInAndObjectType(Mockito.any(List.class), Mockito.eq(EnumPRO.EnumParamsDataType.样品.getValue()))).thenReturn(paramsDataList);
//
//        DtoSampleParamsApiPhone sampleParamsPhone = fieldMonitoringServiceImpl.getSampleParams(paramsInfo.getSampleId(), paramsInfo.getSampleTypeId(), paramsInfo.getIsEnterValue());
//
//        // 断言：判断成功
//        Assert.assertTrue(StringUtil.isNotNull(sampleParamsPhone));
//    }
//
//    @Test
//    public void testSaveFolderParamsValue() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//        DtoParamsApiPhoneInfo recordInfo = new DtoParamsApiPhoneInfo();
//        recordInfo.setSampleFolderId("folderId");
//        recordInfo.setProjectId("projectId");
//        recordInfo.setReceiveId("recId");
//        recordInfo.setCycleOrder(1);
//        recordInfo.setParamsConfigList(new ArrayList<>());
//
//        //getSampleList(string,string,string,integer)
//        List<DtoSample> sampleList = new ArrayList<>();
//        DtoSample sample = new DtoSample();
//        sampleList.add(sample);
//        when(sampleRepository.findBySampleFolderId(recordInfo.getSampleFolderId())).thenReturn(sampleList);
//        List<DtoSample> qcSamList = new ArrayList<>();
//        when(sampleRepository.findByAssociateSampleIdIn(Mockito.any(List.class))).thenReturn(qcSamList);
//
//        List<DtoParamsConfig> paramsConfigList = new ArrayList<>();
//        when(paramsConfigService.findAll(Mockito.any(List.class))).thenReturn(paramsConfigList);
//        DtoSampleFolder sampleFolder = new DtoSampleFolder();
//        when(sampleFolderRepository.findOne(recordInfo.getSampleFolderId())).thenReturn(sampleFolder);
//        List<DtoParamsData> paramsDataList = new ArrayList<>();
//        when(paramsDataRepository.findByObjectTypeAndObjectIdInAndParamsConfigIdIn(Mockito.eq(EnumPRO.EnumParamsDataType.样品.getValue()),
//                Mockito.any(List.class), Mockito.any(List.class))).thenReturn(paramsDataList);
//
//
//        fieldMonitoringServiceImpl.saveFolderParamsValue(recordInfo.getSampleFolderId(), recordInfo.getProjectId(),
//                recordInfo.getReceiveId(), recordInfo.getCycleOrder(), recordInfo.getParamsConfigList());
//
//        // 断言：判断成功
////        Assert.assertTrue(null);
//    }
//
//    @Test
//    public void testSaveSampleParamsValue() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//        DtoParamsApiPhoneInfo recordInfo = new DtoParamsApiPhoneInfo();
//        DtoSampleParamsApiPhone sampleParamsPhone = new DtoSampleParamsApiPhone();
//        sampleParamsPhone.setSamId("samId");
//        sampleParamsPhone.setParamsDataPhoneList(new ArrayList<>());
//        recordInfo.setSampleParamsPhone(sampleParamsPhone);
//
//        DtoSample sample = new DtoSample();
//        when(sampleRepository.findOne(sampleParamsPhone.getSamId())).thenReturn(sample);
//        List<DtoParamsData> paramsDataList = new ArrayList<>();
//        when(paramsDataRepository.findByObjectTypeAndObjectIdInAndParamsConfigIdIn(Mockito.eq(EnumPRO.EnumParamsDataType.样品.getValue()),
//                Mockito.any(List.class), Mockito.any(List.class))).thenReturn(paramsDataList);
//        List<DtoParamsConfig> paramsConfigs = new ArrayList<>();
//        when(paramsConfigService.findAll(Mockito.any(List.class))).thenReturn(paramsConfigs);
//        List<DtoAnalyseData> analyseDataList = new ArrayList<>();
//        DtoAnalyseData dtoAnalyseData = new DtoAnalyseData();
//        dtoAnalyseData.setMostSignificance(1);
//        dtoAnalyseData.setMostDecimal(2);
//        when(analyseDataRepository.findAll(Mockito.any(List.class))).thenReturn(analyseDataList);
//        String strValue = "tstvalue";
//        when(proService.getDecimal(dtoAnalyseData.getMostSignificance(),dtoAnalyseData.getMostDecimal(),null)).thenReturn(strValue);
//
//
//        fieldMonitoringServiceImpl.saveSampleParamsValue(recordInfo.getSampleParamsPhone());
//
//        // 断言：判断成功
////        Assert.assertTrue(null);
//    }
//
//    @Test
//    public void testGetMaxCount() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//        DtoFolderInfo folder = new DtoFolderInfo();
//        folder.setProjectId("projectId");
//
//        List<DtoSample> sampleList = new ArrayList<>();
//        when(sampleRepository.findByProjectId(folder.getProjectId())).thenReturn(sampleList);
//
//        Map<String,Object> map = fieldMonitoringServiceImpl.getMaxCount(folder.getProjectId());
//
//        // 断言：判断成功
//        Assert.assertTrue(!map.isEmpty());
//    }
//
//    @Test
//    public void testGetSampleTypeList() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//
//        List<DtoSampleType> dtoSampleTypeList = new ArrayList<>();
//        DtoSampleType dtoSampleType = new DtoSampleType();
//        dtoSampleTypeList.add(dtoSampleType);
//        when(sampleTypeRepository.findByCategory(EnumBase.EnumSampleTypeCategory.检测类型小类.getValue())).thenReturn(dtoSampleTypeList);
//        List<TreeNode> resList = fieldMonitoringServiceImpl.getSampleTypeList();
//
//        // 断言：判断成功
//        Assert.assertTrue(!resList.isEmpty());
//    }
//
//    @Test
//    public void testcreateSampleLabelData() {
//        // 断言：fieldMonitoringServiceImpl 对象不为 null
//        Assert.assertNotNull(fieldMonitoringServiceImpl);
//        Map<String, Object> map = new HashMap<>();
//
//        List<DtoSample> samples = new ArrayList<>();
//        when(sampleRepository.findByReceiveIdIn(Mockito.any(List.class))).thenReturn(samples);
//
//        List<DtoSampleLabelPhone> sampleLabelDataList = new ArrayList<>();
//        when(comRepository.find(Mockito.any(String.class), Mockito.any(Map.class))).thenReturn(sampleLabelDataList);
//        List<DtoSampleTypeGroup2Test> group2Tests = new ArrayList<>();
//        when(sampleTypeGroup2TestRepository.findBySampleTypeGroupIds(Mockito.any(List.class))).thenReturn(group2Tests);
//        List<DtoTest> testList = new ArrayList<>();
//        when(testService.findRedisByIds(Mockito.any(List.class))).thenReturn(testList);
//        List<DtoSample> sampleList = new ArrayList<>();
//        when(sampleRepository.findByIds(Mockito.any(List.class))).thenReturn(sampleList);
//        List<DtoSampleGroup> oldSampleGroupList = new ArrayList<>();
//        when(sampleGroupRepository.findBySampleIdIn(Mockito.any(List.class))).thenReturn(oldSampleGroupList);
//        DtoReceiveSampleRecord receiveSampleRecord = new DtoReceiveSampleRecord();
//        when(receiveSampleRecordRepository.findOne(Mockito.anyString())).thenReturn(receiveSampleRecord);
//
//        Object obj = fieldMonitoringServiceImpl.createSampleLabelData(map);
//
//        // 断言：判断成功
//        Assert.assertTrue(StringUtil.isNotNull(obj));
//    }
//
//
//}
