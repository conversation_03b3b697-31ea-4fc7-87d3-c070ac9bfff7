package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoSolutionCalibration;
import com.sinoyd.lims.pro.dto.DtoSolutionCalibrationRecord;

/**
 * 溶液标定操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2024/04/19
 * @since V100R001
 */
public interface SolutionCalibrationService extends IBaseJpaService<DtoSolutionCalibration, String> {

    /**
     * 数据计算
     *
     * @param entity 溶液标定
     * @return 计算结果
     */
    DtoSolutionCalibration calculate(DtoSolutionCalibration entity);

    /**
     * 根据检测单标识查询
     *
     * @param workSheetFolderId 检测单标识
     * @return 溶液标定
     */
    DtoSolutionCalibration findByWorkSheetFolderId(String workSheetFolderId);

    /**
     * 自动计算
     *
     * @param entity 溶液标定
     * @return 计算结果
     */
    DtoSolutionCalibrationRecord automaticCalculate(DtoSolutionCalibrationRecord entity);
}
