package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 详细数据的查询条件（用到项目进度上的）
 * <AUTHOR>
 * @version V1.0.0 2020年01月16日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DetailDataProjectCriteria extends BaseCriteria implements Serializable {

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 排序方式
     */
    private String sort;

    /**
     * 检测类型id
     */
    private String sampleTypeId;


    /**
     * 是否显示分析数据
     */
    private Boolean isShowAnaData = false;

    /**
     * 是否显示参数数据
     */
    private Boolean isShowParamData = false;

    /**
     * 是否只显示已检毕数据
     */
    private Boolean isCompleted = false;

    /**
     * 是否进行数据显示权限控制
     */
    private Boolean isAction = false;

    /**
     * 是否显示现场质控（具体项目用）
     */
    private Boolean isShowQc = true;

    /**
     * 送样单ids
     */
    private List<String> receiveIds;

    /**
     * 送样单Id
     */
    private String receiveId;

    /**
     * 是否项目进度导出
     */
    private Boolean isInquiry = false;

    /**
     * 项目id集合
     */
    private List<String> projectIds;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        //condition.append(" and a.projectId = b.id");
        //condition.append(" and a.sampleFolderId=c.id ");
        //项目必传，不传就按空进行过滤
        //condition.append(" and a.projectId = :projectId");
        condition.append(" and a.isDeleted=0");
        condition.append(" and a.isDeleted=0");
        //values.put("projectId", this.projectId);
        if (StringUtils.isNotNullAndEmpty(this.sampleTypeId) && !UUIDHelper.GUID_EMPTY.equals(this.sampleTypeId)) {
            if (this.sampleTypeId.replace("，",",").contains(",")){
                List<String> sampleTypeIds = Arrays.stream(this.sampleTypeId.split(",")).distinct().collect(Collectors.toList());
                condition.append(" and a.sampleTypeId in :sampleTypeId");
                values.put("sampleTypeId", sampleTypeIds);
            }else{
                condition.append(" and a.sampleTypeId = :sampleTypeId");
                values.put("sampleTypeId", this.sampleTypeId);
            }
        }
        if (StringUtils.isNotNullAndEmpty(this.receiveId) && !UUIDHelper.GUID_EMPTY.equals(this.receiveId)) {
            condition.append(" and a.receiveId = :receiveId");
            values.put("receiveId", this.receiveId);
        }
        if (StringUtil.isNotEmpty(this.projectId) && !UUIDHelper.GUID_EMPTY.equals(this.projectId)) {
            if (isShowQc) {
                if (this.receiveIds.size() > 0) {
                    condition.append(" and (a.projectId = :projectId or a.receiveId in :receiveIds )");
                    values.put("receiveIds", this.receiveIds);
                } else {
                    condition.append(" and a.projectId = :projectId");
                }
            } else {
                condition.append(" and a.projectId = :projectId");
            }
            values.put("projectId", this.projectId);
        }
        return condition.toString();
    }
}
