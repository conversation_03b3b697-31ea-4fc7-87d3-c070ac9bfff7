package com.sinoyd.lims.monitor.dto.rcc;

import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.lims.monitor.entity.PointExtendConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * TB_MONITOR_PointExtendConfig实体
 *
 * <AUTHOR>
 * @version V5.2.0 2023/06/12
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_MONITOR_PointExtendConfig")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoPointExtendConfig extends PointExtendConfig {

    /**
     * 常量数据源
     */
    @Transient
    private List<DtoCode> codeDataSourceList;

    /**
     * 点位类型名称
     */
    @Transient
    private String pointTypeName;

    /**
     * 字段值
     */
    @Transient
    private String filedValue;

    /**
     * 字段值显示文本(特殊控件时文本与值不一致
     */
    @Transient
    private String filedValueDisplay;
}
