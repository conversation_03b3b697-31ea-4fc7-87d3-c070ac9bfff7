package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.SurveyService;
import com.sinoyd.lims.pro.criteria.SurveyCriteria;
import com.sinoyd.lims.pro.dto.DtoSurvey;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * Survey服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: Survey服务")
 @RestController
 @RequestMapping("api/pro/survey")
 public class SurveyController extends BaseJpaController<DtoSurvey, String,SurveyService> {


    /**
     * 分页动态条件查询Survey
     * @param surveyCriteria 条件参数
     * @return RestResponse<List<Survey>>
     */
     @ApiOperation(value = "分页动态条件查询Survey", notes = "分页动态条件查询Survey")
     @GetMapping
     public RestResponse<List<DtoSurvey>> findByPage(SurveyCriteria surveyCriteria) {
         PageBean<DtoSurvey> pageBean = super.getPageBean();
         RestResponse<List<DtoSurvey>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, surveyCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询Survey
     * @param id 主键id
     * @return RestResponse<DtoSurvey>
     */
     @ApiOperation(value = "按主键查询Survey", notes = "按主键查询Survey")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoSurvey> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoSurvey> restResponse = new RestResponse<>();
         DtoSurvey survey = service.findOne(id);
         restResponse.setData(survey);
         restResponse.setRestStatus(StringUtil.isNull(survey) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增Survey
     * @param survey 实体列表
     * @return RestResponse<DtoSurvey>
     */
     @ApiOperation(value = "新增Survey", notes = "新增Survey")
     @PostMapping
     public RestResponse<DtoSurvey> create(@RequestBody @Validated DtoSurvey survey) {
         RestResponse<DtoSurvey> restResponse = new RestResponse<>();
         restResponse.setData(service.save(survey));
         return restResponse;
      }

     /**
     * 新增Survey
     * @param survey 实体列表
     * @return RestResponse<DtoSurvey>
     */
     @ApiOperation(value = "修改Survey", notes = "修改Survey")
     @PutMapping
     public RestResponse<DtoSurvey> update(@RequestBody @Validated DtoSurvey survey) {
         RestResponse<DtoSurvey> restResponse = new RestResponse<>();
         restResponse.setData(service.update(survey));
         return restResponse;
      }

    /**
     * "根据id批量删除Survey
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除Survey", notes = "根据id批量删除Survey")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }