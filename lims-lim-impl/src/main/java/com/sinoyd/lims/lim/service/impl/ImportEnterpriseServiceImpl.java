package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import com.sinoyd.base.dto.customer.DtoImportEnterprise;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.lims.DtoEnterpriseExtend;
import com.sinoyd.base.dto.rcc.DtoIndustryType;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.lims.EnterpriseExtendRepository;
import com.sinoyd.base.repository.lims.EnterpriseRepository;
import com.sinoyd.base.service.EnterpriseService;
import com.sinoyd.base.service.IndustryTypeService;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.AreaService;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.lims.lim.constants.LimConstants;
import com.sinoyd.lims.lim.service.ImportEnterpriseService;
import com.sinoyd.lims.lim.verify.EnterpriseVerifyHandle;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;



/**
 * 企业导入实现类
 *
 * <AUTHOR>
 * @version V1.0.0 2022/9/26
 * @since V100R001
 */
@Service
public class ImportEnterpriseServiceImpl implements ImportEnterpriseService {

    private EnterpriseService enterpriseService;

    private EnterpriseRepository enterpriseRepository;

    private AreaService areaService;

    private EnterpriseVerifyHandle enterpriseVerifyHandle;

    private EnterpriseExtendRepository enterpriseExtendRepository;

    private CodeService codeService;

    private IndustryTypeService industryTypeService;

    /**
     * 人员导入
     *
     * @param file      传入的文件
     * @param objectMap 业务数据Map
     * @return List<DtoPerson>
     * @throws Exception 异常抛出
     */
    @Override
    @Transactional
    public List<DtoEnterprise> importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception {
        PoiExcelUtils.verifyFileType(file);
        //region 业务参数
        //获取所有区域信息
        List<DtoArea> dbAreaList = areaService.findAll();
        //获取所有的企业信息
        List<DtoEnterprise> dbEnterpriseList = enterpriseRepository.findAll();
        //endregion

        //region 线程变量赋值
        enterpriseVerifyHandle.getDbEnterpriseTl().set(dbEnterpriseList);
        enterpriseVerifyHandle.getDbAreaTl().set(dbAreaList);
        List<DtoImportEnterprise> enterpriseTemp = new ArrayList<>();
        enterpriseVerifyHandle.getEnterpriseTl().set(enterpriseTemp);
        List<DtoCode> pollutionTypeCodes = codeService.findCodes(LimConstants.ImportConstants.LIM_POLLUTION_SOURCE_TYPE);
        Map<String,List<DtoCode>> codeMap = new HashMap<>();
        codeMap.put(LimConstants.ImportConstants.LIM_POLLUTION_SOURCE_TYPE,pollutionTypeCodes);
        enterpriseVerifyHandle.getCodeTl().set(codeMap);
        //endregion

        //region 数据参数
        ExcelImportResult<DtoImportEnterprise> importResult = getExcelData(file, response);

        //region 线程变量清理
        enterpriseVerifyHandle.getDbEnterpriseTl().remove();
        enterpriseVerifyHandle.getDbAreaTl().remove();
        enterpriseVerifyHandle.getEnterpriseTl().remove();
        enterpriseVerifyHandle.getCodeTl().remove();
        //endregion


        //获取校验成功的数据
        List<DtoImportEnterprise> importList = importResult.getList();

        //移除空行
        importList.removeIf(p -> StringUtil.isEmpty(p.getName()));

        if (StringUtil.isEmpty(importList)) {
            throw new BaseException("文件中无数据或模板不正确，请检查后导入");
        }

        //获取所有的部门
        //endregion

        //region 添加数据
        List<DtoEnterpriseExtend> extendList = new ArrayList<>();

        List<DtoEnterprise> enterpriseList = importToEntity(importList,extendList);

        this.addData(enterpriseList);

        this.addExtendList(extendList);

        //endregion

        return enterpriseService.findAll();
    }

    /**
     * 添加数据库数据
     *
     * @param data 需要导入的数据
     */
    @Override
    public void addData(List<DtoEnterprise> data) {
        if (StringUtil.isNotEmpty(data)) {
            enterpriseService.save(data);
        }
    }

    /**
     * 获取导入数据
     *
     * @param file 传入的文件
     * @return ExcelImportResult
     * @throws Exception 异常信息
     */
    @Override
    public ExcelImportResult<DtoImportEnterprise> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        //设置表头区域
        params.setTitleRows(0);
        //设置表头开始行
        params.setHeadRows(1);
        //设置开始工作簿
        params.setStartSheetIndex(0);
        //设置是否校验
        params.setNeedVerify(true);
        //设置校验handle
        params.setVerifyHandler(enterpriseVerifyHandle);
        ExcelImportResult<DtoImportEnterprise> result = ExcelImportUtil.importExcelMore(file.getInputStream(), DtoImportEnterprise.class, params);
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "企业信息");
            PoiExcelUtils.downLoadExcel("企业导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }

    /**
     * 导入实体转换为企业实体
     *
     * @param importEnterprises 导入数据
     * @return 人员实体
     */
    private List<DtoEnterprise> importToEntity(List<DtoImportEnterprise> importEnterprises, List<DtoEnterpriseExtend> extendList) {
        List<DtoEnterprise> enterpriseList = new ArrayList<>();
        BiMap<String,String> industryNameMap = HashBiMap.create();
        List<DtoIndustryType> industryTypes = industryTypeService.findAll();
        for (DtoIndustryType industryType : industryTypes) {
            industryNameMap.put(industryType.getId(), industryType.getIndustryName());
        }
        for (DtoImportEnterprise importEnterprise : importEnterprises) {
            DtoEnterprise enterprise = new DtoEnterprise();
            DtoEnterpriseExtend enterpriseExtend = new DtoEnterpriseExtend();
            enterpriseExtend.setEntId(enterprise.getId());
            enterpriseExtend.setIsUsed(enterprise.getIsUsed());
            enterpriseExtend.setIsBreak(enterprise.getIsBreak());
            enterpriseExtend.setBreakInfo(enterprise.getBreakInfo());
            enterpriseExtend.setAttentionDegree(enterprise.getAttentionDegree());
            enterpriseExtend.setSubRate(enterprise.getSubRate());
            BeanUtils.copyProperties(importEnterprise, enterprise, "businessTypeId");
            enterprise.setBusinessTypeId(industryNameMap.inverse().get(importEnterprise.getBusinessTypeId()));
            //根据所属区域名称获取区域数据
            String areaName = "";
            String areaId = "";
            if (StringUtil.isNotEmpty(importEnterprise.getProvinceAreaName()) && StringUtil.isNotEmpty(importEnterprise.getProvinceId())) {
                areaName = importEnterprise.getProvinceAreaName();
                areaId = importEnterprise.getProvinceId();
            }
            if (StringUtil.isNotEmpty(importEnterprise.getCityAreaName()) && StringUtil.isNotEmpty(importEnterprise.getCityId())) {
                areaName = importEnterprise.getCityAreaName();
                areaId = importEnterprise.getCityId();
            }
            if (StringUtil.isNotEmpty(importEnterprise.getAreaName()) && StringUtil.isNotEmpty(importEnterprise.getAreaId())) {
                areaName = importEnterprise.getAreaName();
                areaId = importEnterprise.getAreaId();
            }
            if (StringUtil.isNotEmpty(importEnterprise.getIsPollutionStr()) && "是".equals(importEnterprise.getIsPollutionStr())) {
                enterprise.setType(EnumBase.EnumEnterpriseType.污染源.getValue());
                enterpriseExtend.setPollutionCode(importEnterprise.getPollutionCode());
                enterpriseExtend.setPollutionSourceType(getPollutionTypeValue(importEnterprise.getPollutionType()));
            }else{
                enterpriseExtend.setPollutionSourceType("");
            }
            enterprise.setAreaName(areaName);
            enterprise.setIndustryKind(importEnterprise.getIndustryKind());
            enterprise.setAreaId(areaId);
            enterprise.setRegTypeId("");
            enterpriseList.add(enterprise);
            extendList.add(enterpriseExtend);
        }
        return enterpriseList;
    }

    /**
     * 获取污染源类型的value值
     *
     * @param typeName 污染源类型名称
     * @return 污染源类型值
     */
    private String getPollutionTypeValue(String typeName) {
        List<String> resList = new ArrayList<>();
        Map<String, String> pollutionTypeMap = new HashMap<>();
        List<DtoCode> pollutionSourceTypeList = codeService.findCodes("LIM_PollutionSourceType");
        for (DtoCode code : pollutionSourceTypeList) {
            pollutionTypeMap.put(code.getDictName(), code.getDictValue());
        }
        if (StringUtil.isNotEmpty(typeName)) {
            String replace = typeName.replace("，", ",");
            String[] typeNameList = replace.split(",");
            for (String name : typeNameList) {
                if (pollutionTypeMap.containsKey(name)) {
                    resList.add(pollutionTypeMap.get(name));
                }
            }
        }
        return StringUtil.isNotEmpty(resList) ? String.join(";", resList) : "";
    }

    /**
     * 保存企业拓展数据
     *
     * @param extendList 企业拓展数据
     */
    private void addExtendList(List<DtoEnterpriseExtend> extendList){
        if (StringUtil.isNotEmpty(extendList)){
            enterpriseExtendRepository.save(extendList);
        }
    }

    @Autowired
    public void setEnterpriseService(EnterpriseService enterpriseService) {
        this.enterpriseService = enterpriseService;
    }

    @Autowired
    public void setEnterpriseRepository(EnterpriseRepository enterpriseRepository) {
        this.enterpriseRepository = enterpriseRepository;
    }

    @Autowired
    public void setAreaService(AreaService areaService) {
        this.areaService = areaService;
    }

    @Autowired
    public void setEnterpriseVerifyHandle(EnterpriseVerifyHandle enterpriseVerifyHandle) {
        this.enterpriseVerifyHandle = enterpriseVerifyHandle;
    }

    @Autowired
    public void setEnterpriseExtendRepository(EnterpriseExtendRepository enterpriseExtendRepository) {
        this.enterpriseExtendRepository = enterpriseExtendRepository;
    }

    @Autowired
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    public void setIndustryTypeService(IndustryTypeService industryTypeService) {
        this.industryTypeService = industryTypeService;
    }

}
