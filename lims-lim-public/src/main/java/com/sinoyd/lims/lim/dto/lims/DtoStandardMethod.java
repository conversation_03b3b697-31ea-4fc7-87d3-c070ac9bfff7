package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.StandardMethod;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * DtoStandardMethod实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/13
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_StandardMethod")
@Data
@DynamicInsert
public class DtoStandardMethod extends StandardMethod {

    /**
     * 配对数量
     */
    @Transient
    private Integer matchNum = 0;

}