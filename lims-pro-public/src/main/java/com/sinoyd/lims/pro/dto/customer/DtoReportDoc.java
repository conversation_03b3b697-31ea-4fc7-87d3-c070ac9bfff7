package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.base.dto.lims.DtoDocument;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 报告文档
 * <AUTHOR>
 * @version V1.0.0 2019/11/14
 * @since V100R001
 */
@Data
public class DtoReportDoc {
    /**
     * 报告id
     */
    private String reportId;

    /**
     * 报告编号
     */
    private String code;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建时间
     */
    private List<DtoDocument> document = new ArrayList<>();
}
