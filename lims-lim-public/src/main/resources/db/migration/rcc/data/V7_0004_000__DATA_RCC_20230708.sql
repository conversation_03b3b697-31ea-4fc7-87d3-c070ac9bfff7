-- 容器清单报表应用配置
INSERT INTO TB_LIM_Reportapply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('3e0112a4-ca2f-4356-9d9c-df407b118bad', '140f68e4-fcfa-46cd-9faa-4d119866a42b', 'LocalTask', '现场任务',
        'ContainerList', '容器清单', 0, 0, 1, '', '现场任务:数据:样品信息', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-07-08 22:41:09', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-07-08 22:41:09');

-- 现场任务交接单报表应用配置
INSERT INTO TB_LIM_Reportapply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('be361ce1-016f-47f9-b53e-2d9aae4c12cb', '4b512e79-64f5-4033-a45a-22044a953983', 'LocalTask', '现场任务',
        'SampleReceiveXC', '交接单', 0, 0, 1, '', '现场任务:数据:样品信息', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-07-08 22:36:03', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-07-08 22:36:03');
