package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.utils.CalendarUtil;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.frame.service.DepartmentService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.pro.criteria.*;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ReportAchievement2Person操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/03/14
 * @since V100R001
 */
@Service
public class ReportAchievement2PersonServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoReportAchievement2Person, String, ReportAchievement2PersonRepository> implements ReportAchievement2PersonService {

    private ReportAchievementDetailsService reportAchievementDetailsService;

    private ReportAchievementDetailsRepository reportAchievementDetailsRepository;

    private DepartmentService departmentService;

    private PersonService personService;

    private CommonRepository commonRepository;

    private ReportDetailRepository reportDetailRepository;

    private AnalyseDataRepository analyseDataRepository;

    @Override
    public void findByPage(PageBean<DtoReportAchievement2Person> page, BaseCriteria criteria) {
        ReportAchievement2PersonCriteria achievement2PersonCriteria = (ReportAchievement2PersonCriteria) criteria;
        page.setEntityName("DtoReportAchievement2Person a");
        page.setSelect("select a");
        super.findByPage(page, criteria);
        fillingTransientFields(page.getData(), achievement2PersonCriteria);
        page.getData().sort(Comparator.comparing(DtoReportAchievement2Person::getPersonName,Comparator.nullsLast(Comparator.naturalOrder())));
    }

    @Override
    @Transactional
    public void selectPerson(List<String> personIds) {
        List<DtoReportAchievement2Person> saveList = new ArrayList<>();
        personIds.forEach(p -> {
            DtoReportAchievement2Person reportAchievement2Person = new DtoReportAchievement2Person();
            reportAchievement2Person.setPersonId(p);
            saveList.add(reportAchievement2Person);
        });
        if (StringUtil.isNotEmpty(saveList)) {
            super.save(saveList);
        }
    }

    @Override
    public void verifySelectPerson(List<String> personIds) {
        List<DtoReportAchievement2Person> achievements = repository.findByPersonIdIn(personIds);
        List<DtoPerson> personList = personService.findAll(personIds);
        List<String> personNames = new ArrayList<>();
        personList.forEach(p -> {
            Optional<DtoReportAchievement2Person> achievement = achievements.stream().filter(a -> a.getPersonId().equals(p.getId())).findFirst();
            achievement.ifPresent(a -> personNames.add(p.getCName()));
        });
        if (StringUtil.isNotEmpty(personNames)) {
            throw new BaseException(String.join(",", personNames) + "的绩效已存在");
        }
    }

    @Override
    @Transactional
    public void updateData(Integer year, List<String> ids) {
        List<DtoReportAchievement2Person> achievements = repository.findAll(ids);
        List<String> personIds = achievements.stream().map(DtoReportAchievement2Person::getPersonId).collect(Collectors.toList());
        Date startTime = DateUtil.stringToDate(CalendarUtil.getCurrentYearBegin(year), DateUtil.YEAR);
        Date endTime = DateUtil.stringToDate(CalendarUtil.getCurrentYearEnd(year), DateUtil.YEAR);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(endTime);
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        endTime = calendar.getTime();
        StringBuilder condition = new StringBuilder();
        condition.append("select p.projectCode,p.projectName,p.customerId,p.customerName,r.id,r.reportTypeId,r.code,pl.reportMakerId,r.createTime ");
        condition.append("from DtoProject p, DtoReport r, DtoProjectPlan pl where p.id = r.projectId and pl.projectId = p.id and p.isDeleted = 0 and r.isDeleted = 0 ");
        condition.append("and r.status = '已签发' and r.createTime >= :startTime and r.createTime < :endTime and pl.reportMakerId in :personIds ");
        Map<String, Object> values = new HashMap<>();
        values.put("startTime", startTime);
        values.put("endTime", endTime);
        values.put("personIds", personIds);
        List<Object[]> datas = commonRepository.find(condition.toString(), values);
        List<DtoReportAchievementDetails> details = new ArrayList<>();
        for (Object[] data : datas) {
            DtoReportAchievementDetails reportAchievementDetails = new DtoReportAchievementDetails();
            reportAchievementDetails.setProjectCode(data[0].toString());
            reportAchievementDetails.setProjectName(data[1].toString());
            reportAchievementDetails.setEntId(data[2].toString());
            reportAchievementDetails.setEntName(data[3].toString());
            reportAchievementDetails.setReportId(data[4].toString());
            reportAchievementDetails.setReportTypeId(data[5].toString());
            reportAchievementDetails.setReportCode(data[6].toString());
            reportAchievementDetails.setReportPersonId(data[7].toString());
            reportAchievementDetails.setReportTime((Date) data[8]);
            details.add(reportAchievementDetails);
        }
        reportAchievementDetailsRepository.deleteByAchievementIdInAndReportTimeBetween(ids, startTime, endTime);
        List<DtoReportAchievementDetails> saveList = new ArrayList<>();
        achievements.forEach(a -> {
            List<DtoReportAchievementDetails> details2Achievement = details.stream().filter(d -> d.getReportPersonId().equals(a.getPersonId())).collect(Collectors.toList());
            details2Achievement.forEach(d -> d.setAchievementId(a.getId()));
            saveList.addAll(details2Achievement);
        });
        if (StringUtil.isNotEmpty(saveList)) {
            reportAchievementDetailsRepository.save(saveList);
        }
    }

    @Override
    public Map<String, Long> chartForPerMonth() {
        List<DtoReportAchievementDetails> details = reportAchievementDetailsRepository.findAll();
        Map<String, Long> map = details.stream().collect(Collectors.groupingBy(d -> DateUtil.dateToString(d.getReportTime(), "yyyy-MM"),
                Collectors.counting()));
        return map;
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<?> ids) {
        reportAchievementDetailsRepository.deleteByAchievementIdIn((List<String>) ids);
        return super.logicDeleteById(ids);
    }

    /**
     * 填充冗余字段
     * @param dataList 数据
     * @param criteria 查询条件
     */
    private void fillingTransientFields(List<DtoReportAchievement2Person> dataList, ReportAchievement2PersonCriteria criteria) {
        PageBean<DtoReportAchievementDetails> pb = new PageBean<>();
        pb.setPageNo(1);
        pb.setRowsPerPage(Integer.MAX_VALUE);
        ReportAchievementDetailsCriteria achievementDetailsCriteria = new ReportAchievementDetailsCriteria();
        achievementDetailsCriteria.setStartTime(criteria.getStartTime());
        achievementDetailsCriteria.setEndTime(criteria.getEndTime());
        reportAchievementDetailsService.findByPage(pb, achievementDetailsCriteria);
        List<DtoDepartment> departments = departmentService.findAll();
        List<String> personIds = dataList.stream().map(DtoReportAchievement2Person::getPersonId).collect(Collectors.toList());
        List<DtoPerson> personList = StringUtil.isNotEmpty(personIds) ? personService.findAll(personIds) : new ArrayList<>();
        List<String> reportIds = pb.getData().stream().map(DtoReportAchievementDetails::getReportId).collect(Collectors.toList());
        List<DtoReportDetail> reportDetails = StringUtil.isNotEmpty(reportIds) ? reportDetailRepository.findByReportIdIn(reportIds) : new ArrayList<>();
        List<String> sampleIds = reportDetails.stream().map(DtoReportDetail::getObjectId).collect(Collectors.toList());
        List<DtoAnalyseData> analyseDataList = StringUtil.isNotEmpty(sampleIds) ? analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds) : new ArrayList<>();
        dataList.forEach(data -> {
            List<DtoReportAchievementDetails> achievementDetails = pb.getData().stream().filter(a -> a.getAchievementId().equals(data.getId())).collect(Collectors.toList());
            data.setReportNum(achievementDetails.size());
            Optional<DtoPerson> person = personList.stream().filter(p -> p.getId().equals(data.getPersonId())).findFirst();
            person.ifPresent(p -> {
                data.setPersonName(p.getCName());
                Optional<DtoDepartment> dtoDepartment = departments.stream().filter(d -> d.getId().equals(p.getDeptId())).findFirst();
                dtoDepartment.ifPresent(d -> data.setDeptName(d.getDeptName()));
            });
            List<String> reportIds2Achievement = achievementDetails.stream().map(DtoReportAchievementDetails::getReportId).collect(Collectors.toList());
            List<String> sampleIds2Achievement = reportDetails.stream().filter(r -> reportIds2Achievement.contains(r.getReportId())).map(DtoReportDetail::getObjectId).collect(Collectors.toList());
            data.setSampleNum(sampleIds2Achievement.size());
            List<DtoAnalyseData> analyseData2Achievement = analyseDataList.stream().filter(a -> sampleIds2Achievement.contains(a.getSampleId())).collect(Collectors.toList());
            data.setDataNum(analyseData2Achievement.size());
        });
    }

    @Autowired
    @Lazy
    public void setDepartmentService(DepartmentService departmentService) {
        this.departmentService = departmentService;
    }

    @Autowired
    @Lazy
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }

    @Autowired
    public void setReportAchievementDetailsRepository(ReportAchievementDetailsRepository reportAchievementDetailsRepository) {
        this.reportAchievementDetailsRepository = reportAchievementDetailsRepository;
    }

    @Autowired
    public void setCommonRepository(CommonRepository commonRepository) {
        this.commonRepository = commonRepository;
    }

    @Autowired
    public void setReportDetailRepository(ReportDetailRepository reportDetailRepository) {
        this.reportDetailRepository = reportDetailRepository;
    }

    @Autowired
    public void setAnalyseDataRepository(AnalyseDataRepository analyseDataRepository) {
        this.analyseDataRepository = analyseDataRepository;
    }

    @Autowired
    @Lazy
    public void setReportAchievementDetailsService(ReportAchievementDetailsService reportAchievementDetailsService) {
        this.reportAchievementDetailsService = reportAchievementDetailsService;
    }
}
