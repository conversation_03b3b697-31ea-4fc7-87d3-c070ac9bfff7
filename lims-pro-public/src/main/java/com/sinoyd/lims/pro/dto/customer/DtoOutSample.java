package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.dto.DtoQualityManage;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 外部样品数据
 * <AUTHOR>
 * @version V1.0.0 2020/2/5
 * @since V100R001
 */
@Data
public class DtoOutSample {
    /**
     * 样品id
     */
    private String id = UUIDHelper.NewID();

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 检测类型名称
     */
    private String sampleTypeName;

    /**
     * 点位id
     */
    private String sampleFolderId;

    /**
     * 父类id
     */
    private String parentSampleId;

    /**
     * 父类编号
     */
    private String parentSampleCode;

    /**
     * 盲样类型
     */
    private Integer blindType;

    /**
     * 送样单号
     */
    private String recordCode;

    /**
     * 送样人
     */
    private String senderName;

    /**
     * 送样时间
     */
    private Date sendTime;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 委托单位
     */
    private String customerName;

    /**
     * 采样开始时间
     */
    private Date samplingTimeBegin;

    //#region DtoOutSampleSave字段（需一致）

    /**
     * 样品编号
     */
    @Length(message = "样品编号{validation.message.length}", max = 50)
    private String code;

    /**
     * 点位名称
     */
    @Length(message = "冗余-点位{validation.message.length}", max = 100)
    private String redFolderName;

    /**
     * 受检单位id
     */
    private String inspectedEntId;

    /**
     * 受检单位名称
     */
    @Length(message = "受检单位{validation.message.length}", max = 100)
    private String inspectedEnt;

    /**
     * 周期
     */
    private Integer cycleOrder;

    /**
     * 次数
     */
    private Integer timesOrder;

    /**
     * 批次
     */
    private Integer sampleOrder;

    /**
     * 样品颜色
     */
    @Length(message = "颜色{validation.message.length}", max = 50)
    private String samColor;

    /**
     * 样品性状
     */
    @Length(message = "样品特征{validation.message.length}", max = 1000)
    private String sampleExplain;

    /**
     * 样品包装
     */
    @Length(message = "包装/规格{validation.message.length}", max = 50)
    private String pack;

    //#endregion

    /**
     * 参数数据
     */
    private List<DtoParamsConfig> paramsData = new ArrayList<>();

    /**
     * 质控信息
     */
    private List<DtoQualityManage> qualityManage = new ArrayList<>();

    /**
     * 样品对应测试项目列表
     */
    private List<DtoTest> test = new ArrayList<>();

    /**
     * 经度
     */
    @Length(message = "经度（实际）{validation.message.length}", max = 20)
    private String lon;

    /**
     * 纬度
     */
    @Length(message = "纬度（实际）{validation.message.length}", max = 20)
    private String lat;

    /**
     * 原样id(密码样的时候需要使用)
     */
    private String associateSampleId;

    /**
     * 样品质控类型 {@link com.sinoyd.lims.pro.enums.EnumPRO.EnumQMType} 1.标样 2.加标样 3.其他 4.空白样
     * 用于质控任务的样品质控类型标记，没有业务逻辑作用
     */
    private Integer sampleQmType;

    /**
     * 扩展字段
     */
    protected Map<String, Object> dbExtendMap;

    /**
     * 例行点位id
     */
    private String fixedPointId = UUIDHelper.GUID_EMPTY;
}
