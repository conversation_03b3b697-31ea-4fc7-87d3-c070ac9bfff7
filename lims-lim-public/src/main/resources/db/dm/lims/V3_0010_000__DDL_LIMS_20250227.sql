CREATE TABLE tb_pro_project2inspect
(
    ID             VARCHAR(50) NOT NULL,
    projectId      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    inspectType    int         NOT NULL,
    objectId       varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    objName        varchar(100)         DEFAULT NULL,
    code           varchar(100)         DEFAULT NULL,
    model          varchar(100)         DEFAULT NULL,
    inspectContent varchar(100)         DEFAULT NULL,
    NOT            CLUSTER PRIMARY KEY(ID)
) STORAGE(ON MAIN, CLUSTERBTR);

COMMENT
ON TABLE tb_pro_project2inspect IS '内审计划信息';
COMMENT
ON COLUMN tb_pro_project2inspect.id IS 'id';
    COMMENT
ON COLUMN tb_pro_project2inspect.projectId IS '项目id';
    COMMENT
ON COLUMN tb_pro_project2inspect.inspectType IS '期间核查类型';
     COMMENT
ON COLUMN tb_pro_project2inspect.objectId IS '核查对象ID';
   COMMENT
ON COLUMN tb_pro_project2inspect.objName IS '核查对象名称';
    COMMENT
ON COLUMN tb_pro_project2inspect.code IS '核查对象编号';
    COMMENT
ON COLUMN tb_pro_project2inspect.model IS '核查对象型号';
    COMMENT
ON COLUMN tb_pro_project2inspect.inspectContent IS '核查内容';


