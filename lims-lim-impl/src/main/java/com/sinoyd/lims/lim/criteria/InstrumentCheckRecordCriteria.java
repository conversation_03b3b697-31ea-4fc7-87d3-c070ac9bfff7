package com.sinoyd.lims.lim.criteria;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 仪器检定校准查询条件
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-29
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InstrumentCheckRecordCriteria extends BaseCriteria {

    /**
     * 仪器id
     */
    private String instrumentId;

    /**
     * 仪器id
     */
    private List<String> instrumentIds;

    /**
     * 检定校准开始时间
     */
    private String dtBegin;

    /**
     * 检定校准结束时间
     */
    private String dtEnd;

    /**
     * 检定校准有效期开始时间
     */
    private String checkEndDateFrom;

    /**
     * 检定校准有效期结束时间
     */
    private String checkEndDateTo;

    /**
     * 检定校准Id
     */
    private List<String> checkRecordIds;

    /**
     * 查询条件（仪器名称、规格型号、出厂编号、编号）
     */
    private String key;

    /**
     * 仪器类型（空Guid代表所有）
     */
    private String instrumentTypeId;

    /**
     * 检测器id
     */
    private String instrument2DetectorId;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(dtBegin)) {
            Date from = DateUtil.stringToDate(this.dtBegin, DateUtil.YEAR);
            condition.append(" and a.checkTime >= :dtBegin");
            values.put("dtBegin", from);
        }
        if (StringUtils.isNotNullAndEmpty(dtEnd)) {
            Date to = DateUtil.stringToDate(this.dtEnd, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and a.checkTime < :dtEnd");
            values.put("dtEnd", c.getTime());
        }
        if (StringUtils.isNotNullAndEmpty(checkEndDateFrom)) {
            Date from = DateUtil.stringToDate(this.checkEndDateFrom, DateUtil.YEAR);
            condition.append(" and a.checkEndDate >= :checkEndDateFrom");
            values.put("checkEndDateFrom", from);
        }
        if (StringUtils.isNotNullAndEmpty(checkEndDateTo)) {
            Date to = DateUtil.stringToDate(this.checkEndDateTo, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and a.checkEndDate < :checkEndDateTo");
            values.put("checkEndDateTo", c.getTime());
        }
        if (StringUtils.isNotNullAndEmpty(instrumentId)
                && !UUIDHelper.GUID_EMPTY.equals(this.instrumentId)) {
            condition.append(" and (a.instrumentId = :instrumentId)");
            values.put("instrumentId", this.instrumentId);
        }
        if (StringUtil.isNotEmpty(instrumentIds)) {
            condition.append(" and (a.instrumentId in :instrumentIds)");
            values.put("instrumentIds", this.instrumentIds);
        }
        if (StringUtil.isNotEmpty(this.checkRecordIds)){
            condition.append(" and a.id in :checkRecordIds");
            values.put("checkRecordIds", this.checkRecordIds);
        }
        if (StringUtils.isNotNullAndEmpty(key)) {
            condition.append(
                    " and exists(select 1 from DtoInstrument i where i.id = a.instrumentId and (i.instrumentName like :key or i.model like :key or i.instrumentsCode like :key or i.serialNo like :key))");
            values.put("key", "%" + this.key + "%");
        }
        if (StringUtils.isNotNullAndEmpty(instrumentTypeId)){
            condition.append(
                    " and exists(select 1 from DtoInstrument i where i.id = a.instrumentId and i.instrumentTypeId = :instrumentTypeId)");
            values.put("instrumentTypeId", this.instrumentTypeId);
        }
        if (StringUtils.isNotNullAndEmpty(instrument2DetectorId)
                && !UUIDHelper.GUID_EMPTY.equals(this.instrument2DetectorId)) {
            condition.append(" and (a.instrument2DetectorId = :instrument2DetectorId)");
            values.put("instrument2DetectorId", this.instrument2DetectorId);
        }
        condition.append(" and  b.isDeleted = 0 and a.instrumentId=b.id");
        return condition.toString();
    }
}