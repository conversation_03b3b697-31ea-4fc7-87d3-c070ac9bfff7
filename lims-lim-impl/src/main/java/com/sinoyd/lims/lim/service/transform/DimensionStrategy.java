package com.sinoyd.lims.lim.service.transform;

import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.repository.rcc.DimensionRepository;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.data.mapper.DimensionMapper;
import com.sinoyd.lims.lim.dto.customer.*;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.dto.rcc.DtoParams;
import com.sinoyd.lims.lim.enums.EnumLIM;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Order(20)
public class DimensionStrategy implements TransformImportStrategy {

    private DimensionMapper dimensionMapper;
    private DimensionRepository dimensionRepository;

    @Override
    public void importSheetData(InputStream inputStream, HttpServletResponse response, ImportParams params) throws Exception {

    }

    @Override
    public void getAddData(Map<String, DtoBaseData> substituteMap, Map<String, DtoBaseData> sampleTypeBindMap, DtoDataSyncParams dtoDataSyncParams, DtoTestDependentData testDependentData, DtoImportTestTemp importTestTemp, DtoTestDependentData exportData) {

        List<DtoExportDimension> dimensionList = testDependentData.getDimensionList();
        // 获取导入前检查的待新增量纲
        List<DtoDataCheck> dimensionCheck = dtoDataSyncParams.getImportChecks().stream().filter(p-> p.getCheckItem().equals(EnumLIM.EnumImportTestType.量纲表.getCheckItem()))
                .flatMap(p-> p.getDataChecks().stream()).collect(Collectors.toList());
        List<String> addName = dimensionCheck.stream().filter(p -> BASE_DATA_TYPE[0].equals(p.getType())).map(DtoDataCheck::getName).collect(Collectors.toList());
        // 根据名称筛选出待新增数据
        List<DtoDimension> dtoDimensionList = dimensionList.stream()
                .filter(p -> addName.contains(p.getDimensionName()))
                .map(p -> dimensionMapper.toDtoDimension(p)).collect(Collectors.toList());
        importTestTemp.setDimensionTemps(dtoDimensionList);

        List<DtoExportTest> testList = exportData.getTestList();
        if (StringUtil.isNotEmpty(testList)) {
            List<String> dimensionIds = testList.stream().map(DtoExportTest::getDimensionId).distinct().collect(Collectors.toList());
            List<DtoExportDimension> exportDimensions = dimensionList.stream().filter(p -> dimensionIds.contains(p.getDimensionName())).collect(Collectors.toList());
            exportData.setDimensionList(exportDimensions);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importData(DtoImportTestTemp importTestTemp, WebSocketServer webSocketServer) {
        List<DtoDimension> dimensionTemps = importTestTemp.getDimensionTemps();
        if (StringUtil.isNotEmpty(dimensionTemps)) {
            //已同步记录数
            int i = 0;
            for (DtoDimension dimension : dimensionTemps) {
                dimensionRepository.save(dimension);
                webSocketServer.sendMessage(getMessage(dimensionTemps.size(), ++i));
            }
        } else {
            webSocketServer.sendMessage(getMessage(dimensionTemps.size(),0));
        }
    }

    @Override
    public int getAddDataOrderNum() {
        return EnumLIM.EnumImportTestType.量纲表.getValue();
    }

    @Override
    public String getTableName() {
        return EnumLIM.EnumImportTestType.量纲表.getSource();
    }


    @Override
    public String getTableRemark() {
        return EnumLIM.EnumImportTestType.量纲表.name();
    }

    @Override
    public List<DtoImportCheck> check(DtoDataSyncParams dtoDataSyncParams, DtoTestDependentData testDependentData) {
        List<DtoExportDimension> dimensionList = testDependentData.getDimensionList();
        List<DtoImportCheck> importChecks = new ArrayList<>();
        List<DtoDataCheck> dimensionCheckList = new ArrayList<>();
        if (StringUtil.isNotEmpty(dimensionList)){
            List<DtoDimension> dimensions= dimensionRepository.findAll();
            dimensionCheckList = dimensionList.parallelStream()
                    .map(p -> {
                        DtoDataCheck dtoDataCheck = new DtoDataCheck();
                        dtoDataCheck.setName(p.getDimensionName());
                        Map<String,Object> otherField = new HashMap<>();
                        otherField.put("dimensionName",p.getDimensionName());
                        dtoDataCheck.setOtherField(otherField);
                        Optional<DtoDimension> dimensionOptional = dimensions.stream().filter(r -> r.getDimensionName().equals(p.getDimensionName())).findFirst();
                        if (dimensionOptional.isPresent()) {
                            dtoDataCheck.setType(BASE_DATA_TYPE[1]);
                            dtoDataCheck.setId(dimensionOptional.get().getId());
                        } else {
                            dtoDataCheck.setType(BASE_DATA_TYPE[0]);
                            dtoDataCheck.setId(p.getId());
                        }
                        return dtoDataCheck;
                    }).collect(Collectors.toList());
        }
        List<DtoDataCheck> existsList = dimensionCheckList.stream().filter(p -> BASE_DATA_TYPE[1].equals(p.getType())).collect(Collectors.toList());
        List<DtoDataCheck> noExistsList = dimensionCheckList.stream().filter(p -> BASE_DATA_TYPE[0].equals(p.getType())).collect(Collectors.toList());
        importChecks.add(new DtoImportCheck(EnumLIM.EnumImportTestType.量纲表.getCheckItem(),
                BASE_DATA_TYPE[1],existsList.size(),"所有外键ID关联改为系统内的ID，不进行导入。",
                existsList));
        importChecks.add(new DtoImportCheck(EnumLIM.EnumImportTestType.量纲表.getCheckItem(),
                BASE_DATA_TYPE[0],noExistsList.size(),"新增量纲，导入时将进行插入。",
                noExistsList));
        return importChecks;
    }

    @Autowired
    @Lazy
    public void setDimensionMapper(DimensionMapper dimensionMapper) {
        this.dimensionMapper = dimensionMapper;
    }

    @Autowired
    @Lazy
    public void setDimensionRepository(DimensionRepository dimensionRepository) {
        this.dimensionRepository = dimensionRepository;
    }
}
