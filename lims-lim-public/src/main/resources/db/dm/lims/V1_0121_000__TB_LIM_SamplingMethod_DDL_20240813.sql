CREATE TABLE TB_LIM_SAMPLINGMETHOD
(
    id           VARCHAR(50)                                                  NOT NULL,
    sampleTypeId VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    methodName   VARCHAR(255)  DEFAULT '' NULL,
    alias        VARCHAR(100)  DEFAULT '' NULL,
    standardCode VARCHAR(100)  DEFAULT '' NULL,
    applyDate    TIMESTAMP(0)  DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    status       INT           DEFAULT -1                                     NOT NULL,
    remark       VARCHAR(1000) DEFAULT '' NULL,
    isDeleted    BIT           DEFAULT 0
                                                                              NOT NULL,
    orgId        VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                                                              NOT NULL,
    creator      VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                                                              NOT NULL,
    createDate   TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()
                                                                              NOT NULL,
    domainId     VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                                                              NOT NULL,
    modifier     VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                                                              NOT NULL,
    modifyDate   TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()
                                                                              NOT NULL
);
ALTER TABLE TB_LIM_SAMPLINGMETHOD ADD CONSTRAINT  PRIMARY KEY(id) ;
COMMENT ON COLUMN TB_LIM_SAMPLINGMETHOD.id IS 'id';
COMMENT ON COLUMN TB_LIM_SAMPLINGMETHOD.sampleTypeId IS '检测类型id';
COMMENT ON COLUMN TB_LIM_SAMPLINGMETHOD.methodName IS '方法名称';
COMMENT ON COLUMN TB_LIM_SAMPLINGMETHOD.alias IS '别名';
COMMENT ON COLUMN TB_LIM_SAMPLINGMETHOD.standardCode IS '标准编号';
COMMENT ON COLUMN TB_LIM_SAMPLINGMETHOD.applyDate IS '实施日期';
COMMENT ON COLUMN TB_LIM_SAMPLINGMETHOD.status IS '启用状态';
COMMENT ON COLUMN TB_LIM_SAMPLINGMETHOD.remark IS '备注';
COMMENT ON COLUMN TB_LIM_SAMPLINGMETHOD.isDeleted IS '是否删除（0不删除 1删除）';
COMMENT ON COLUMN TB_LIM_SAMPLINGMETHOD.orgId IS '组织机构id';
COMMENT ON COLUMN TB_LIM_SAMPLINGMETHOD.creator IS '创建人';
COMMENT ON COLUMN TB_LIM_SAMPLINGMETHOD.createDate IS '创建时间';
COMMENT ON COLUMN TB_LIM_SAMPLINGMETHOD.domainId IS '所属实验室';
COMMENT ON COLUMN TB_LIM_SAMPLINGMETHOD.modifier IS '修改人';
COMMENT ON COLUMN TB_LIM_SAMPLINGMETHOD.modifyDate IS '修改时间';