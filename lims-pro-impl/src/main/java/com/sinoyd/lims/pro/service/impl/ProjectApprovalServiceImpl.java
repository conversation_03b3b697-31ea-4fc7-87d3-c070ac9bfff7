package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.ProjectTypeRepository;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.ProjectApprovalService;
import com.sinoyd.lims.pro.service.SampleFolderTemplateService;
import com.sinoyd.lims.strategy.context.schemeSynchronization.SampleContext;
import com.sinoyd.lims.strategy.context.schemeSynchronization.SampleFolderContext;
import com.sinoyd.lims.strategy.context.schemeSynchronization.TestContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ProjectApproval操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/02
 * @since V100R001
 */
@Service
public class ProjectApprovalServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoProjectApproval, String, ProjectApprovalRepository> implements ProjectApprovalService {

    private PersonService personService;

    private ProjectPlanRepository projectPlanRepository;

    private ProjectTypeRepository projectTypeRepository;

    private SampleFolderTemplateService sampleFolderTemplateService;

    private SampleFolderTemplateRepository sampleFolderTemplateRepository;

    private ProjectRepository projectRepository;

    private SampleRepository sampleRepository;

    private AnalyseDataRepository analyseDataRepository;

    private WorkSheetFolderRepository workSheetFolderRepository;

    private SamplingFrequencyTestTempRepository samplingFrequencyTestTempRepository;

    private SamplingFrequencyTempRepository samplingFrequencyTempRepository;

    private SampleContext sampleContext;

    private SampleFolderContext sampleFolderContext;

    private TestContext testContext;

    @Override
    public void findByPage(PageBean<DtoProjectApproval> page, BaseCriteria criteria) {
        page.setEntityName("DtoProject p, DtoProjectApproval a");
        page.setSelect("select new com.sinoyd.lims.pro.dto.DtoProjectApproval(a.id, a.projectId, a.approveDate, a.modifyStatus, a.approvePersonId, " +
                "a.comment, p.projectName, p.projectCode, p.inputTime, p.projectTypeId, p.customerName, p.inspectedEnt, p.status)");
        super.findByPage(page, criteria);
        fillingTransientFields(page.getData());
    }

    @Override
    @Transactional
    public DtoProjectApproval selectProject(String projectId) {
        verify(projectId);
        DtoProjectApproval projectApproval = new DtoProjectApproval();
        projectApproval.setProjectId(projectId);
        projectApproval.setApproveDate(new Date());
        projectApproval.setModifyStatus(EnumLIM.EnumPojectApproveStatus.登记中.name());
        return super.save(projectApproval);
    }

    @Override
    @Transactional
    public void commit(List<String> approveIds, String personId, String comment) {
        List<DtoProjectApproval> projectApprovals = repository.findAll(approveIds);
        projectApprovals.forEach(p -> {
            p.setModifyStatus(EnumLIM.EnumPojectApproveStatus.审核中.name());
            p.setApprovePersonId(personId);
            p.setComment(comment);
        });
        repository.save(projectApprovals);
    }

    @Override
    @Transactional
    public void audit(Boolean isPass, List<String> approveIds, String comment) {
        List<DtoProjectApproval> projectApprovals = repository.findAll(approveIds);
        if (isPass) {
            projectApprovals.forEach(p -> p.setModifyStatus(EnumLIM.EnumPojectApproveStatus.已完成.name()));
            //审核通过自动更新项目方案
            List<DtoSampleFolderTemplate> folderTemplateList = sampleFolderTemplateRepository
                    .findByApproveIdIn(approveIds).stream()
                    .filter(p -> !p.getOperateType().equals(EnumLIM.EnumOperateType.无修改.getValue()))
                    .collect(Collectors.toList());
            //更新点位
            changeFolder(folderTemplateList, projectApprovals.get(0).getProjectId());
            //更新样品
            List<String> updateFolderIds = folderTemplateList.stream()
                    .filter(p -> p.getOperateType().equals(EnumLIM.EnumOperateType.修改.getValue()))
                    .map(DtoSampleFolderTemplate::getId).collect(Collectors.toList());
            List<DtoSamplingFrequencyTemp> frequencyTempList = samplingFrequencyTempRepository.findBySampleFolderTempIdIn(updateFolderIds);
            List<DtoSamplingFrequencyTemp> addDeleteTempList = frequencyTempList.stream()
                    .filter(p -> !p.getOperateType().equals(EnumLIM.EnumOperateType.修改.getValue())
                            && !p.getOperateType().equals(EnumLIM.EnumOperateType.无修改.getValue())).collect(Collectors.toList());
            changeSample(addDeleteTempList);
            //更新测试项目
            List<String> updateTempIds = frequencyTempList.stream()
                    .filter(p -> p.getOperateType().equals(EnumLIM.EnumOperateType.修改.getValue())
                            && !p.getOperateType().equals(EnumLIM.EnumOperateType.无修改.getValue()))
                    .map(DtoSamplingFrequencyTemp::getId).collect(Collectors.toList());
            List<DtoSamplingFrequencyTestTemp> testTempList = samplingFrequencyTestTempRepository
                    .findBySamplingFrequencyTempIdIn(updateTempIds).stream()
                    .filter(p -> !p.getOperateType().equals(EnumLIM.EnumOperateType.无修改.getValue()))
                    .collect(Collectors.toList());
            testTempList.forEach(p -> {
                Optional<DtoSamplingFrequencyTemp> temp = frequencyTempList.stream()
                        .filter(f -> p.getSamplingFrequencyTempId().equals(f.getId())).findFirst();
                temp.ifPresent(t -> p.setSamplingFrequencyId(t.getSamplingFrequencyId()));
            });
            changeTest(testTempList);
        } else {
            projectApprovals.forEach(p -> {
                p.setModifyStatus(EnumLIM.EnumPojectApproveStatus.审核不通过.name());
                p.setComment(comment);
            });
        }
        repository.save(projectApprovals);
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> folderIds = sampleFolderTemplateRepository.findByApproveIdIn((List<String>) ids).stream().map(DtoSampleFolderTemplate::getId).collect(Collectors.toList());
        sampleFolderTemplateService.cancelFolderTemplate(folderIds);
        return super.logicDeleteById(ids);
    }

    @Override
    public void checkCondition(List<String> projectIds) {
        List<DtoProjectApproval> projectApprovals = repository.findByProjectIdInAndModifyStatusIn(projectIds, Arrays.asList(EnumLIM.EnumPojectApproveStatus.审核中.name(), EnumLIM.EnumPojectApproveStatus.登记中.name(), EnumLIM.EnumPojectApproveStatus.审核不通过.name()));
        if (StringUtil.isNotEmpty(projectApprovals)) {
            projectIds = projectApprovals.stream().map(DtoProjectApproval::getProjectId).distinct().collect(Collectors.toList());
            List<DtoProject> projectList = projectRepository.findAll(projectIds);
            throw new BaseException(String.format("项目%s在方案申请修改流程中", projectList.stream().map(DtoProject::getProjectName).collect(Collectors.joining(","))));
        }
    }

    private void changeFolder(List<DtoSampleFolderTemplate> folderTemplateList, String projectId) {
        Map<Integer, List<DtoSampleFolderTemplate>> folderMap = folderTemplateList.stream()
                .collect(Collectors.groupingBy(DtoSampleFolderTemplate::getOperateType));
        for (Integer type : folderMap.keySet()) {
            sampleFolderContext.synchronizationSampleFolder(type, projectId, folderMap.get(type));
        }
    }

    private void changeSample(List<DtoSamplingFrequencyTemp> frequencyTempList) {
        Map<Integer, List<DtoSamplingFrequencyTemp>> frequencyMap = frequencyTempList.stream()
                .collect(Collectors.groupingBy(DtoSamplingFrequencyTemp::getOperateType));
        for (Integer type : frequencyMap.keySet()) {
            sampleContext.synchronizationSample(type, frequencyMap.get(type));
        }
    }

    private void changeTest(List<DtoSamplingFrequencyTestTemp> testTempList) {
        Map<Integer, List<DtoSamplingFrequencyTestTemp>> testMap = testTempList.stream()
                .collect(Collectors.groupingBy(DtoSamplingFrequencyTestTemp::getOperateType));
        for (Integer type : testMap.keySet()) {
            testContext.synchronizationTest(type, testMap.get(type));
        }
    }

    /**
     * 判断当前项目是否存在检测中的数据
     *
     * @param projectId 项目id
     */
    private void verify(String projectId) {
        List<String> sampleIds = sampleRepository.findByProjectId(projectId).stream().map(DtoSample::getId).collect(Collectors.toList());
        List<String> workSheetFolderIds = StringUtil.isNotEmpty(sampleIds) ? analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds)
                .stream().map(DtoAnalyseData::getWorkSheetFolderId).filter(w -> !UUIDHelper.GUID_EMPTY.equals(w)).distinct().collect(Collectors.toList()) : new ArrayList<>();
        List<DtoWorkSheetFolder> workSheetFolders = StringUtil.isNotEmpty(workSheetFolderIds) ? workSheetFolderRepository.findByIdIn(workSheetFolderIds) : new ArrayList<>();
        if (StringUtil.isNotEmpty(workSheetFolders)) {
            List<Integer> workStatus = Arrays.asList(EnumPRO.EnumWorkSheetStatus.检测单拒绝.getValue(),
                    EnumPRO.EnumWorkSheetStatus.新建.getValue(),
                    EnumPRO.EnumWorkSheetStatus.已经保存.getValue());
            if (workSheetFolders.stream().anyMatch(w -> workStatus.contains(w.getWorkStatus()))) {
                throw new BaseException("该项目存在数据在检测中，无法进行方案变更");
            }
        }
    }

    /**
     * 方案提交校验
     *
     * @param approveIds 方案id
     */
    @Override
    public Boolean verifyCommit(List<String> approveIds) {
        List<DtoSampleFolderTemplate> templates = sampleFolderTemplateRepository.findByApproveIdIn(approveIds);
        approveIds.forEach(a -> {
            List<DtoSampleFolderTemplate> templateList = templates.stream().filter(t -> t.getApproveId().equals(a) && !t.getOperateType().equals(0)).collect(Collectors.toList());
            if (StringUtil.isEmpty(templateList)) {
                throw new BaseException("提交的方案变更申请中没有待处理点位");
            }
        });
        List<DtoSampleFolderTemplate> delTemplateList = templates.stream().filter(t -> EnumLIM.EnumOperateType.删除.getValue().equals(t.getOperateType())).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(delTemplateList)) {
            return true;
        }
        return false;
    }

    /**
     * 填充冗余字段
     *
     * @param dataList 数据列表
     */
    private void fillingTransientFields(List<DtoProjectApproval> dataList) {
        List<String> projectIds = dataList.stream().map(DtoProjectApproval::getProjectId).distinct().collect(Collectors.toList());
        List<DtoProjectPlan> projectPlans = StringUtil.isNotEmpty(projectIds) ? projectPlanRepository.findByProjectIdIn(projectIds) : new ArrayList<>();
        Set<String> approvePersonIds = dataList.stream().map(DtoProjectApproval::getApprovePersonId).filter(a -> !UUIDHelper.GUID_EMPTY.equals(a)).collect(Collectors.toSet());
        Set<String> leaderIds = projectPlans.stream().map(DtoProjectPlan::getLeaderId).filter(a -> !UUIDHelper.GUID_EMPTY.equals(a)).collect(Collectors.toSet());
        approvePersonIds.addAll(leaderIds);
        List<DtoPerson> persons = StringUtil.isNotEmpty(approvePersonIds) ? personService.findAll(approvePersonIds) : new ArrayList<>();
        List<String> projectTypeIds = dataList.stream().map(DtoProjectApproval::getProjectTypeId).distinct().collect(Collectors.toList());
        List<DtoProjectType> projectTypes = StringUtil.isNotEmpty(projectTypeIds) ? projectTypeRepository.findAll(projectTypeIds) : new ArrayList<>();
        dataList.forEach(data -> {
            persons.stream().filter(p -> p.getId().equals(data.getApprovePersonId())).findFirst().ifPresent(p -> data.setApprovePersonName(p.getCName()));
            projectPlans.stream().filter(p -> p.getProjectId().equals(data.getProjectId())).findFirst().ifPresent(p -> {
                persons.stream().filter(per -> per.getId().equals(p.getLeaderId())).findFirst().ifPresent(per -> data.setLeaderName(per.getCName()));
            });
            projectTypes.stream().filter(p -> p.getId().equals(data.getProjectTypeId())).findFirst().ifPresent(p -> data.setProjectTypeName(p.getName()));
        });
    }

    @Autowired
    @Lazy
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }

    @Autowired
    public void setProjectPlanRepository(ProjectPlanRepository projectPlanRepository) {
        this.projectPlanRepository = projectPlanRepository;
    }

    @Autowired
    public void setProjectTypeRepository(ProjectTypeRepository projectTypeRepository) {
        this.projectTypeRepository = projectTypeRepository;
    }

    @Autowired
    @Lazy
    public void setSampleFolderTemplateService(SampleFolderTemplateService sampleFolderTemplateService) {
        this.sampleFolderTemplateService = sampleFolderTemplateService;
    }

    @Autowired
    public void setSampleFolderTemplateRepository(SampleFolderTemplateRepository sampleFolderTemplateRepository) {
        this.sampleFolderTemplateRepository = sampleFolderTemplateRepository;
    }

    @Autowired
    public void setProjectRepository(ProjectRepository projectRepository) {
        this.projectRepository = projectRepository;
    }

    @Autowired
    public void setSampleRepository(SampleRepository sampleRepository) {
        this.sampleRepository = sampleRepository;
    }

    @Autowired
    public void setAnalyseDataRepository(AnalyseDataRepository analyseDataRepository) {
        this.analyseDataRepository = analyseDataRepository;
    }

    @Autowired
    public void setWorkSheetFolderRepository(WorkSheetFolderRepository workSheetFolderRepository) {
        this.workSheetFolderRepository = workSheetFolderRepository;
    }

    @Autowired
    public void setSampleContext(SampleContext sampleContext) {
        this.sampleContext = sampleContext;
    }

    @Autowired
    public void setTestContext(TestContext testContext) {
        this.testContext = testContext;
    }

    @Autowired
    public void setSampleFolderContext(SampleFolderContext sampleFolderContext) {
        this.sampleFolderContext = sampleFolderContext;
    }

    @Autowired
    public void setSamplingFrequencyTempRepository(SamplingFrequencyTempRepository samplingFrequencyTempRepository) {
        this.samplingFrequencyTempRepository = samplingFrequencyTempRepository;
    }

    @Autowired
    public void setSamplingFrequencyTestTempRepository(SamplingFrequencyTestTempRepository samplingFrequencyTestTempRepository) {
        this.samplingFrequencyTestTempRepository = samplingFrequencyTestTempRepository;
    }
}
