package com.sinoyd.lims.order.service.impl;

import com.sinoyd.base.utils.CalendarUtil;
import com.sinoyd.lims.order.service.CustomerOrderAnaService;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.vo.BaseOrderCustomerAnalyzeVO;
import com.sinoyd.lims.pro.vo.OrderStatisticsVo;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Service(CustomerOrderAnaService.CUSTOMER_ORDER_STATISTICS)
public class CustomerOrderStatisticsImpl extends AbsCustomerOrder {
    /**
     * 销售人员订单额分析
     *
     * @param year 年份
     * @return List<OrderStatisticsVo>
     */
    @Override
    public List<BaseOrderCustomerAnalyzeVO> statisticsList(Integer year) {
        String currentYearBegin = CalendarUtil.getCurrentYearBegin(year);
        String currentYearEnd = CalendarUtil.getCurrentYearEnd(year);
        String lastYearBegin = CalendarUtil.getLastYearBegin(year);
        String lastYearEnd = CalendarUtil.getLastYearEnd(year);
        String currentMonthBegin = CalendarUtil.getCurrentMonthBegin(year);
        String currentMonthEnd = CalendarUtil.getCurrentMonthEnd(year);
        String lastMonthBegin = CalendarUtil.getLastMonthBegin(year);
        String lastMonthEnd = CalendarUtil.getLastMonthEnd(year);

        StringBuilder sb = new StringBuilder();
        sb.append("SELECT count( * ) orderNum,t1.salesPersonId FROM TB_PRO_OrderForm t1,TB_LIM_Person t2 ");
        sb.append("WHERE t1.isDeleted = 0 and t2.isDeleted = 0 ");
        sb.append("and t1.salesPersonId = t2.id ");
        sb.append("and signDate BETWEEN ");
        sb.append(" ? ");
        sb.append(" AND ");
        sb.append(" ? ");
        sb.append(" and ");
        sb.append("grantStatus = ? ");
        sb.append(" GROUP BY salesPersonId order by orderNum DESC");
        Object[] params = new Object[]{currentYearBegin, currentYearEnd, EnumPRO.EnumOrderSignStatus.已签订.getValue()};
        //根据sql查询年度签单数
        List<Map<String, Object>> sbMapList = super.getCustomerOrderData(sb.toString(), params);
        if (sbMapList == null) {
            return null;
        }
        String salesPersonIds = "";
        for (int i = 0; i < sbMapList.size(); i++) {
            if (i == 0) {
                salesPersonIds = "'" + sbMapList.get(i).get("salesPersonId").toString() + "'";
            } else {
                salesPersonIds = String.join(",", salesPersonIds, "'" + sbMapList.get(i).get("salesPersonId").toString() + "'");
            }
        }
        //根据销售员id查询人员头像url和人员名称
        StringBuilder sbPicAndName = new StringBuilder();
        sbPicAndName.append(" SELECT id,cName,photoUrl  FROM TB_LIM_Person WHERE id IN ( ");
        sbPicAndName.append("" + salesPersonIds + ")");
        List<Map<String, Object>> picAndNameList = super.getCustomerOrderData(sbPicAndName.toString());

        //将数量及人员头像和名称拼接
        for (Map<String, Object> map : sbMapList) {
            for (Map<String, Object> nameMap : picAndNameList) {
                if (map.get("salesPersonId").toString().equals(nameMap.get("id").toString())) {
                    map.put("salesPersonName",nameMap.get("cName"));
                    map.put("photoUrl",nameMap.get("photoUrl"));
                }
            }
        }

        //获取今年的签单金额
        Map<String, Object> orderDataCurrentYear = getFinalQuotation(salesPersonIds, currentYearBegin, currentYearEnd, true);

        //获取去年的签单金额
        Map<String, Object> orderDataLastYear = getFinalQuotation(salesPersonIds, lastYearBegin, lastYearEnd, true);

        //获取当月的签单金额
        Map<String, Object> signDataCurrentMonth = getFinalQuotation(salesPersonIds, currentMonthBegin, currentMonthEnd, true);

        //获取当月的签单个数
        List<Map<String, Object>> currentMonthSignList = getMonthCount(salesPersonIds, currentMonthBegin, currentMonthEnd, true);

        //获取前一个月的签单金额
        Map<String, Object> signDataLastMonth = getFinalQuotation(salesPersonIds, lastMonthBegin, lastMonthEnd, true);

        //获取当月的订单金额
        Map<String, Object> orderDataCurrentMonth = getFinalQuotation(salesPersonIds, currentMonthBegin, currentMonthEnd, false);

        //获取当月的订单个数
        List<Map<String, Object>> currentMonthOrderList = getMonthCount(salesPersonIds, currentMonthBegin, currentMonthEnd, false);

        //获取前一个月的订单金额
        Map<String, Object> orderDataLastMonth = getFinalQuotation(salesPersonIds, lastMonthBegin, lastMonthEnd, false);

        List<OrderStatisticsVo> orderStatisticsVoList = new ArrayList<>();
        for (Map<String, Object> map : sbMapList) {
            OrderStatisticsVo vo = new OrderStatisticsVo();
            String salesPersonId = map.get("salesPersonId").toString();
            vo.setSaleName(map.get("salesPersonName").toString());
            vo.setOrderNum(Integer.parseInt(map.get("orderNum").toString()));
            if (map.get("photoUrl") != null) {
                vo.setPhotoUrl(map.get("photoUrl").toString());
            }
            //组装今年签单金额
            int signBillMoneyTotal = getData(orderDataCurrentYear, salesPersonId);
            vo.setSignBillMoneyTotal(signBillMoneyTotal);
            //组装去年签单金额
            int lastYearMoneyTotal = getData(orderDataLastYear, salesPersonId);
            vo.setSignBillMoneyCompare(signBillMoneyTotal - lastYearMoneyTotal);

            //组装当月签单数据
            int monthSignBillMoney = getCurrentMonthData(signDataCurrentMonth, currentMonthSignList, salesPersonId)[0];
            int monthSignBillCount = getCurrentMonthData(signDataCurrentMonth, currentMonthSignList, salesPersonId)[1];
            vo.setMonthSignBillMoney(monthSignBillMoney);
            vo.setMonthSignBillCount(monthSignBillCount);

            //组装上个月签单数据
            int lastMonthTotalMoney = getData(signDataLastMonth, salesPersonId);
            vo.setMonthSignBillMoneyCompare(monthSignBillMoney - lastMonthTotalMoney);
            //组装当月订单数据
            int monthOrderBillMoney = getCurrentMonthData(orderDataCurrentMonth, currentMonthOrderList, salesPersonId)[0];
            int monthOrderBillCount = getCurrentMonthData(orderDataCurrentMonth, currentMonthOrderList, salesPersonId)[1];
            vo.setMonthOrderBillMoney(monthOrderBillMoney);
            vo.setMonthOrderBillCount(monthOrderBillCount);

            //组装上个月订单数据
            int lastMonthTotalOrderMoney = getData(orderDataLastMonth, salesPersonId);
            vo.setMonthOrderBillMoneyCompare(monthOrderBillMoney - lastMonthTotalOrderMoney);
            orderStatisticsVoList.add(vo);
        }
        //先根据订单金额，再根据订单数进行排序
        orderStatisticsVoList = orderStatisticsVoList.stream().sorted(Comparator.comparing(OrderStatisticsVo::getSignBillMoneyTotal,Comparator.reverseOrder()).
                thenComparing(OrderStatisticsVo::getOrderNum,Comparator.reverseOrder())).collect(Collectors.toList());
        if (orderStatisticsVoList.size()>4) {
            orderStatisticsVoList = orderStatisticsVoList.subList(0,4);
        }
        return new ArrayList<>(orderStatisticsVoList);
    }

    /**
     * 组装获取除了当月数据的单子数据
     *
     * @param map map 单子数据
     * @param salesPersonId 销售人员id
     * @return 总共的单子金额
     */
    private int getData(Map<String, Object> map, String salesPersonId) {
        int totalMoney = 0;
        if (map != null) {
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                if (salesPersonId.equals(entry.getKey())) {
                    BigDecimal value = (BigDecimal) entry.getValue();
                    totalMoney = Integer.parseInt(value.toString());
                }
            }
        }
        return totalMoney;
    }

    /**
     * 组装获取当月的单子数据
     *
     * @param map           当月的单子金额
     * @param mapList       当月的单个个数
     * @param salesPersonId 销售员id
     * @return int []
     */
    private int[] getCurrentMonthData(Map<String, Object> map, List<Map<String, Object>> mapList, String salesPersonId) {
        int billMoney = 0;
        int billCount = 0;
        if (map != null) {
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                if (salesPersonId.equals(entry.getKey())) {
                    BigDecimal value = (BigDecimal) entry.getValue();
                    billMoney = Integer.parseInt(value.toString());

                }
            }
            billCount = mapList.stream().filter(item -> salesPersonId.equals(item.get("salesPersonId"))).collect(Collectors.toList()).size();
        }
        return new int[]{billMoney, billCount};
    }

    /**
     * 根据订单数据返回订单总金额及对应的销售人员
     *
     * @param order 订单数据
     * @return 订单总金额及对应的销售人员id
     */
    private Map<String, Object> getOrderData(List<Map<String, Object>> order) {
        if (order == null) {
            return null;
        }
        //根据销售人员id去重
        List<String> salesPersonIdList = order.stream().map(item -> (String) item.get("salesPersonId")).distinct().collect(Collectors.toList());
        Map<String, Object> map = new HashMap<>();
        for (String id : salesPersonIdList) {
            BigDecimal total = new BigDecimal(0);
            for (Map<String, Object> objectMap : order) {
                if (id.equals(objectMap.get("salesPersonId").toString()))
                    total =  total.add(new BigDecimal(objectMap.get("finalQuotation").toString()));
            }
            total =  total.setScale(0,BigDecimal.ROUND_DOWN);
            map.put(id, total);
        }
        return map;
    }

    /**
     * 获取金额数据
     *
     * @param ids       销售人员ids
     * @param timeBegin 开始时间
     * @param timeEnd   结束时间
     * @param b         是签单数据还是订单数据
     * @return Map<String, Object>
     */
    private Map<String, Object> getFinalQuotation(String ids, String timeBegin, String timeEnd, boolean b) {
        return getOrderData(getFinalQuotationSql(ids, timeBegin, timeEnd, b));
    }

    /**
     * 获取月度单子数List
     *
     * @param ids       销售人员ids
     * @param timeBegin 开始时间
     * @param timeEnd   结束时间
     * @param b         是签单数据还是订单数据
     * @return List<Map < String, Object>>
     */
    private List<Map<String, Object>> getMonthCount(String ids, String timeBegin, String timeEnd, boolean b) {
        return getFinalQuotationSql(ids, timeBegin, timeEnd, b);
    }
}
