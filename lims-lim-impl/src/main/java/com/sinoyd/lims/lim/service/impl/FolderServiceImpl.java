package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoFolder;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.repository.lims.FolderRepository;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.lim.service.DocAuthorityListService;
import com.sinoyd.lims.lim.service.FolderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 文件管理接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/5
 * @since V100R001
 */
@Service
public class FolderServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoFolder, String, FolderRepository> implements FolderService {

    private DocAuthorityListService docAuthorityListService;

    private PersonRepository personRepository;


    @Override
    @Transactional
    public void findByPage(PageBean<DtoFolder> pageBean, BaseCriteria folderCriteria) {
        pageBean.setEntityName("DtoFolder p");
        pageBean.setSelect("select p");

        super.findByPage(pageBean, folderCriteria);
        List<DtoFolder> folders = pageBean.getData();
        for (DtoFolder folder : folders) {
            Integer count = repository.countByParentId(folder.getId());
            if (count > 0) {
                folder.setLeaf(false);
            } else {
                folder.setLeaf(true);
            }
        }
        docAuthorityListService.processOldAuthorityConfigData(folders.stream().map(DtoFolder::getId).collect(Collectors.toList()));
        pageBean.setData(folders);
    }

    @Override
    @Transactional
    public DtoFolder save(DtoFolder folder) {
        if (repository.countByNameAndParentId(folder.getFolderName(), folder.getId(),
                folder.getParentId()) > 0) {
            throw new BaseException("已存在相同名称的文件夹！");
        }
        if (!folder.getParentId().equals(UUIDHelper.GUID_EMPTY)
                && repository.getCount(folder.getParentId()) < 1) {
            throw new BaseException("无法在该文件夹下新增文件夹,该文件夹可能被删除");
        }
        DtoFolder save = super.save(folder);
        // 初始化文件夹权限信息
        docAuthorityListService.initAuthorityConfig(save.getId(), save.getCreator());

        return save;
    }

    @Transactional
    @Override
    public DtoFolder update(DtoFolder folder) {
        Integer count = repository.countByNameAndParentId(folder.getFolderName(), folder.getId(), folder.getParentId());
        if (count > 0) {
            throw new BaseException("已存在相同名称的文件夹！");
        }
        return super.update(folder);
    }

    @Override
    public String getDocumentAttachPath(String folderId) {
        String subPath = "";
        DtoFolder dtoFolder = super.findOne(folderId);
        if (StringUtil.isNotNull(dtoFolder)) {
            subPath = dtoFolder.getFolderName();
            DtoFolder parentFolder = super.findOne(dtoFolder.getParentId());
            while (StringUtil.isNotNull(parentFolder)) {
                subPath = parentFolder.getFolderName() + "/" + subPath;
                parentFolder = super.findOne(parentFolder.getParentId());
            }
        }
        return subPath;
    }

    @Override
    @Transactional
    public List<TreeNode> folderTree() {
        List<DtoFolder> folders = findAll();
        List<String> personIds = folders.stream().map(DtoFolder::getCreator).distinct().collect(Collectors.toList());
        List<DtoPerson> personList = StringUtil.isNotEmpty(personIds) ? personRepository.findAllDeleted(personIds) : new ArrayList<>();
        docAuthorityListService.processOldAuthorityConfigData(folders.stream().map(DtoFolder::getId).collect(Collectors.toList()));
        List<TreeNode> nodeList = new ArrayList<>();
        for (DtoFolder folder : folders) {
            TreeNode treeNode = new TreeNode();
            treeNode.setId(folder.getId());
            treeNode.setParentId(folder.getParentId());
            treeNode.setLabel(folder.getFolderName());
            treeNode.setOrderNum(folder.getOrderNum());
            treeNode.setExtent1(folder.getCreator());
            treeNode.setExtent2(folder.getOrgId());
            personList.stream().filter(p -> p.getId().equals(folder.getCreator())).findFirst().ifPresent(p -> treeNode.setExtent3(p.getCName()));
            nodeList.add(treeNode);
        }
        return buildTree(nodeList);
    }

    private List<TreeNode> buildTree(List<TreeNode> treeNodeList) {
        List<TreeNode> trees = new ArrayList<>();
        sortTreeNode(treeNodeList);
        for (TreeNode treeNode : treeNodeList) {
            if (StringUtil.isEmpty(treeNode.getParentId()) || UUIDHelper.GUID_EMPTY.equals(treeNode.getParentId()) || "0".equals(treeNode.getParentId())) {
                trees.add(findChildren(treeNode, treeNodeList));
            }
        }
        return trees;
    }

    /**
     * 递归查找子节点
     *
     * @param currentTreeNode 当前树节点
     * @param treeNodeList    树节点列表
     * @return 完整结构的树节点
     */
    private TreeNode findChildren(TreeNode currentTreeNode, List<TreeNode> treeNodeList) {
        for (TreeNode treeNode : treeNodeList) {
            if (currentTreeNode.getId().equals(treeNode.getParentId())) {
                if (currentTreeNode.getChildren() == null) {
                    currentTreeNode.setChildren(new ArrayList<>());
                }
                //是否还有子节点，如果有的话继续往下遍历，如果没有则直接返回
                currentTreeNode.getChildren().add(findChildren(treeNode, treeNodeList));
            }
        }
        return currentTreeNode;
    }

    /**
     * 排序方法
     *
     * @param treeNodeList 需要排序的节点list
     */
    private void sortTreeNode(List<TreeNode> treeNodeList) {
        treeNodeList.sort(Comparator.comparing(TreeNode::getOrderNum, Comparator.reverseOrder()));
    }

    @Autowired
    @Lazy
    public void setDocAuthorityListService(DocAuthorityListService docAuthorityListService) {
        this.docAuthorityListService = docAuthorityListService;
    }

    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }
}