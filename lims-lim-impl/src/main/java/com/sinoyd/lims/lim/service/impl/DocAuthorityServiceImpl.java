package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.repository.lims.DocumentRepository;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.dto.DtoRole;
import com.sinoyd.frame.service.AuthorizeService;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.RoleService;
import com.sinoyd.frame.service.UserService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.core.LimCodeHelper;
import com.sinoyd.lims.lim.criteria.DocAuthorityCriteria;
import com.sinoyd.lims.lim.dto.customer.DtoDocAuthorityTemp;
import com.sinoyd.lims.lim.dto.customer.DtoDocAuthorityValidate;
import com.sinoyd.lims.lim.dto.lims.DtoDocAuthority;
import com.sinoyd.lims.lim.dto.lims.DtoDocAuthorityConfig;
import com.sinoyd.lims.lim.dto.lims.DtoDocAuthorityList;
import com.sinoyd.lims.lim.dto.lims.DtoFolder;
import com.sinoyd.lims.lim.repository.lims.DocAuthorityConfigRepository;
import com.sinoyd.lims.lim.repository.lims.DocAuthorityListRepository;
import com.sinoyd.lims.lim.repository.lims.DocAuthorityRepository;
import com.sinoyd.lims.lim.repository.lims.FolderRepository;
import com.sinoyd.lims.lim.service.DocAuthorityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
/**
 * 文件夹权限接口实现
 * <AUTHOR>
 * @version V1.0.0 2019/5/14
 * @since V100R001
 */
@Service
public class DocAuthorityServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoDocAuthority, String, DocAuthorityRepository> implements DocAuthorityService {


    @Autowired
    private RoleService roleService;

    @Autowired
    private UserService userService;

    @Autowired
    private CodeService codeService;

    @Autowired
    private AuthorizeService authorizeService;

    @Autowired
    private DocumentRepository documentRepository;

    @Autowired
    private FolderRepository folderRepository;

    @Autowired
    private DocAuthorityListRepository docAuthorityListRepository;

    @Autowired
    private DocAuthorityConfigRepository docAuthorityConfigRepository;

    /**
     * 角色和角色拥有的权限列表
     */
    @Override
    public Map<String, Object> getAuthorityList(BaseCriteria baseCriteria) {
        // 角色列表
        List<DtoRole> roleList = roleService.findAll();
        // 权限常量列表
        List<DtoCode> authList = codeService.findCodes(LimCodeHelper.LIM_DocumentAuthorityType);
        DocAuthorityCriteria docAuthorityCriteria = (DocAuthorityCriteria) baseCriteria;
        // 对应文件夹/文件配置的角色权限列表
        List<DtoDocAuthority> docAuthList = repository.findByObjectId(docAuthorityCriteria.getObjectId());
        Map<String, Object> returnMap = new HashMap<>(); //返回的结果集
        List<Map<String, Object>> mapList = new ArrayList<>();
        List<Map<String, Object>> columnMaps = new ArrayList<>();
        Integer totalCount = 0;
        //所有的角色信息
        for (DtoRole role : roleList) {
            Map<String, Object> map = new HashMap<>();
            map.put("roleId", role.getRoleId());
            map.put("roleName", role.getRoleName());
            DtoDocAuthorityTemp dtoDocAuthorityTemp = new DtoDocAuthorityTemp();
            dtoDocAuthorityTemp.setRole(role);
            //找到这个角色已有的编码
            List<DtoDocAuthority> authorities = docAuthList.stream().filter(p -> p.getRoleId().equals(role.getRoleId())).collect(Collectors.toList());


            for (DtoCode auth : authList) {
                Map<String, Object> columnMap = new HashMap<>();
                columnMap.put("label", auth.getDictName());
                columnMap.put("value", auth.getDictCode());
                if (!columnMaps.contains(columnMap)) { //组装列表的列
                    columnMaps.add(columnMap);
                }
                Map<String, Object> authMap = new HashMap<>();
                authMap.put("authId", auth.getDictCode());
                authMap.put("objectId", docAuthorityCriteria.getObjectId());
                authMap.put("authName", auth.getDictName());
                Optional<DtoDocAuthority> optional = authorities.stream().filter(p -> p.getAuthId().equals(auth.getDictCode())).findFirst();

                if (StringUtil.isNotNull(optional) && optional.isPresent()) {
                    DtoDocAuthority dtoDocAuthority = optional.get();
                    authMap.put("id", dtoDocAuthority.getId());
                    authMap.put("authState", dtoDocAuthority.getAuthState());
                } else {
                    authMap.put("authState", false);
                }
                map.put(auth.getDictCode(), authMap);
            }
            mapList.add(map);
        }
        returnMap.put("columns", columnMaps);
        returnMap.put("rows", mapList);
        returnMap.put("totalCount", mapList.size());
        return returnMap;
    }

    /**
     * 文件夹、文件权限保存
     */
    @Transactional
    @Override
    public void saveAll(List<DtoDocAuthorityTemp> temps) {
        List<DtoDocAuthority> docAuthoritiesList = new ArrayList<>();
        for (DtoDocAuthorityTemp dtoDocAuthorityTemp : temps) {
            DtoRole dtoRole = dtoDocAuthorityTemp.getRole();
            List<DtoDocAuthority> docAuthorities = dtoDocAuthorityTemp.getAuthList();
            if (StringUtil.isNotNull(dtoRole) && StringUtil.isNotNull(docAuthorities)) {
                for (DtoDocAuthority dtoDocAuthority : docAuthorities) {
                    dtoDocAuthority.setRoleId(dtoRole.getRoleId());
                    dtoDocAuthority.setRoleName(dtoRole.getRoleName());
                    dtoDocAuthority.setOrgId(PrincipalContextUser.getPrincipal().getOrgId());
                    docAuthoritiesList.add(dtoDocAuthority);
                }
            }
        }
        List<String> objIds = docAuthoritiesList.stream().map(DtoDocAuthority::getObjectId).distinct().collect(Collectors.toList());
        repository.deleteByObjectIdIn(objIds);
        super.save(docAuthoritiesList);
//        // 用来存放实体，用于新增
//        List<DtoDocAuthority> docAuthorities = new ArrayList<>();
//        // 遍历List获取map
//        for (Map<String, Object> list : maps) {
//            // 新建map方便获取数据
//            Map<String, Object> role = new HashMap<>();
//            // 遍历map获取相关数据
//            for (Map.Entry<String, Object> map : list.entrySet()) {
//                // 获取role相关数据
//                if (map.getKey() == "role") {
//                    role = (Map<String, Object>) map.getValue();
//                }
//                // 获取authList相关数据
//                if (map.getKey() == "authList") {
//                    // 新建map方便获取数据
//                    List<Map<String, Object>> docList = new ArrayList<>();
//                    docList = (List) map.getValue();
//                    // 遍历List来获取数据
//                    for (Map<String, Object> obj : docList) {
//                        // 判断role是否有数据
//                        if (role != null) {
//                            List<String> folderIdList = new ArrayList<>();
//                            folderIdList.add((String) obj.get("objectId"));
//                            for (String folderId : folderIdList) {
//                                DtoDocAuthority doc = new DtoDocAuthority();
//                                doc.setRoleId((String)role.get("id"));
//                                doc.setRoleName((String) role.get("roleName"));
//                                doc.setAuthState((boolean) obj.get("authState"));
//                                doc.setAuthId((String)obj.get("authId"));
//                                doc.setAuthName((String) obj.get("authName"));
//                                doc.setId(UUID.randomUUID().toString());
//                                doc.setObjectId(folderId);
//                                // 将实体添加到集合中
//                                docAuthorities.add(doc);
//                            }
//                        }
//                    }
//                }
//            }
        //}

//        List<String> objIds =  docAuthorities.stream().map(p -> p.getObjectId()).distinct().collect(Collectors.toList());
//        repository.logicDeleteById(objIds);
//        // 新增
//        repository.save(docAuthorities);
    }

    /**
     * 权限
     */
    @Override
    public Boolean validateAuth(DtoDocAuthorityValidate dtoDocAuthorityValidate) {
        //只验证文件夹新增、文件夹删除、文件夹修改、文件上传、文件下载、文件删除权限
        String userId = PrincipalContextUser.getPrincipal().getUserId();

        if (StringUtil.isNotNull(dtoDocAuthorityValidate)) {

            DtoFolder dtoFolder = dtoDocAuthorityValidate.getFolder();

            if (StringUtil.isNotNull(dtoFolder)) {


                //文件夹ID
                String folderId = dtoFolder.getId();

                //创建人员
                String creator = dtoFolder.getCreator();

                if (creator.equals(userId)) { //如果是当前人员创建的，默认是有权限的，否则需要进行验证
                    return true;
                }
                //权限编码
                String docAuthorityCode = dtoDocAuthorityValidate.getDocAuthorityCode();
                if (docAuthorityCode.equals(LimCodeHelper.LIM_DocAuthority_Show)) { //如果是文件管理员的权限编码，用框架的权限验证
                    return authorizeService.haveActionPermission(userId, docAuthorityCode);
                }

                List<DtoRole> roles = userService.findRoleById(userId);

                if (StringUtil.isNotNull(roles)) {
                    List<String> roleIds = roles.stream().map(DtoRole::getRoleId).distinct().collect(Collectors.toList());
                    //文档权限
                    List<DtoDocAuthority> dtoDocAuthorities = repository.validateAuth(folderId, docAuthorityCode, roleIds);

                    if (StringUtil.isNotNull(dtoDocAuthorities) && dtoDocAuthorities.size() > 0) {
                        return true;
                    }
                }
                return false;
            }
            return false;
        }
        return false;


//        //[TODO:]文件批量删除的权限不确定是否控制好
//        List<DtoRole> roleList =userService.findByRoles(PrincipalContextUser.getPrincipal().getUserId());
//        List<String> objectIdList = Arrays.asList(objectId.replaceAll("\\[", "").replaceAll("]", "").split(","));
//         // 对应文件夹/文件配置的角色权限列表
//        List<DtoDocAuthority> docAuthList = repository.findByObjectId(objectIdList);
//        //判断该人员是否包含文件管理员的角色
//        if(roleList.stream().filter(p -> p.getRoleId().equals(LimCodeHelper.DOC_AUTH_PARENTID)).collect(Collectors.toList()).size() > 0){
//            return true;
//        }
//        //判断如果是创建人，返回true
//        List<DtoDocument> documentList = documentService.findAll(objectIdList);
//        List<DtoFolder> folderList = folderService.findAll(objectIdList);
//        //找到当前人员创建的文件夹、文件
//        List<DtoDocument> userDocumentList = documentList.stream().filter(p -> p.getUploadPersonId().equals(userId)).collect(Collectors.toList());
//        List<DtoFolder> userFolderList = folderList.stream().filter(p -> p.getCreator().equals(userId)).collect(Collectors.toList());
//        //当前人员创建的文件夹或者文件与勾选的文件夹或文件的个数是一致的 ，表明勾选的文件夹、文件都是当前人员新建的
//        if(documentList.size() > 0 && userDocumentList.size() == documentList.size()){
//            return true;
//        }else if(folderList.size() > 0  && userFolderList.size() == folderList.size() ){
//            return true;
//        }
//
//        //如果不是点击的配置，配置按钮约定传空Id
//        if(!docAuthorityId.equals(BaseCodeHelper.GUID_EMPTY)){
//            //获取拥有该文件夹权限的集合
//            List<String> roleIds = docAuthList.stream().filter(p -> p.getAuthId().equals(docAuthorityId))
//            .map(p -> p.getRoleId()).collect(Collectors.toList());
//            List<DtoRole> permissionRole = roleList.stream().filter(p -> roleIds.contains(p.getRoleId())).collect(Collectors.toList());
//            if(permissionRole.size() > 0){
//                return true;
//            }
//        }
    }

    @Override
    public List<String> validateFileBatchDownload(List<String> ids) {
        List<String> result = new ArrayList<>();
        if(StringUtil.isNotEmpty(ids)){
            List<DtoDocument> documentList = documentRepository.findAll(ids);
            String userId = PrincipalContextUser.getPrincipal().getUserId();
            List<String> folderIds = documentList.stream().map(DtoDocument::getFolderId).collect(Collectors.toList());
            List<DtoFolder> folderList = StringUtil.isNotEmpty(folderIds)? folderRepository.findAll(folderIds) : new ArrayList<>();
            List<DtoDocAuthorityList> docAuthorityLists =  StringUtil.isNotEmpty(folderIds)
                    ? docAuthorityListRepository.findByObjectIdInAndAuthCodeIn(folderIds, Collections.singletonList("LIM_DocumentAuthorityType_Download"))
                    : new ArrayList<>();
            List<DtoDocAuthorityConfig> docAuthorityConfigList = StringUtil.isNotEmpty(folderIds)
                    ? docAuthorityConfigRepository.findByObjectIdInAndAuthCodeAndUserId(folderIds,"LIM_DocumentAuthorityType_Download",userId)
                    : new ArrayList<>();
            for (DtoDocument document:documentList) {
                DtoFolder folder = folderList.stream().filter(v->v.getId().equals(document.getFolderId())).findFirst().orElse(null);
                if(folder!=null){
                    if(userId.equals(folder.getCreator())){
                        result.add(document.getId());
                        continue;
                    }
                    DtoDocAuthorityList authorityList = docAuthorityLists.stream().filter(v->folder.getId().equals(v.getObjectId())).findFirst().orElse(null);
                    if (StringUtil.isNotNull(authorityList) && authorityList.getDefaultOpenInd()) {
                        result.add(document.getId());
                        continue;
                    }
                    int count = (int) docAuthorityConfigList.stream().filter(v->folder.getId().equals(v.getObjectId())).count();
                    if (count > 0) {
                        result.add(document.getId());
                    }
                }
            }
        }
        return result;
    }
}