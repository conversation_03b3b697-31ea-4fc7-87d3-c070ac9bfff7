package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 样品参数核对实体
 * <AUTHOR>
 * @version V1.0.0 2019/12/26
 * @since V100R001
 */
@Data
public class DtoParamsCheckTemp {
    public DtoParamsCheckTemp(Integer rows,Integer columns) {
        this.flagArr = new Boolean[rows][columns];
        for (Integer i = 0; i < rows; i++) {
            for (Integer j = 0; j < columns; j++) {
                this.flagArr[i][j] = false;
            }
        }
        this.paramsNameArr = new String[rows][columns];
    }

    /**
     * 二维数组
     */
    private Boolean[][] flagArr;

    /**
     * 参数名称二维数组
     */
    private String[][] paramsNameArr;

    /**
     * 配置map
     */
    Map<String, Integer> cfgMap = new HashMap<>();

    /**
     * 指标map
     */
    Map<String, Integer> itemMap = new HashMap<>();
}
