package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoTestQCRemindConfig2Test;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.List;


/**
 * TestQCRemindConfig2Test数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/10/8
 * @since V100R001
 */
public interface TestQCRemindConfig2TestRepository extends IBaseJpaPhysicalDeleteRepository<DtoTestQCRemindConfig2Test, String> {

    /**
     * 根据配置id删除
     *
     * @param configId
     * @return
     */
    @Modifying
    @Query("delete DtoTestQCRemindConfig2Test d  where  d.configId = :configId")
    Integer deleteByConfigId(@Param("configId") String configId);


    /**
     * 根据测试项目id集合获取配置信息
     *
     * @param testIds 测试项目id集合
     * @return 配置信息集合
     */
    List<DtoTestQCRemindConfig2Test> findByTestIdIn(Collection<String> testIds);
}