package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * OAFileRevision实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="OAFileRevision")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class OAFileRevision implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  OAFileRevision() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 文件名称
    */
    @Column(length=100,nullable=false)
    @ApiModelProperty("文件名称")
    @Length(message = "文件名称{validation.message.length}", max = 100)
	private String fileName;
    
    /**
    * 文件标识关联
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("文件标识关联")
    private String fileId;
    
    /**
    * 文件编号
    */
    @Column(length=100)
    @ApiModelProperty("文件编号")
    @Length(message = "文件编号{validation.message.length}", max = 100)
    private String fileCode;
    
    /**
    * 文件类型（常量LIM_FileControlType：程序文件、标准文件、质量手册、作业指导书、记录单）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("文件类型（常量LIM_FileControlType：程序文件、标准文件、质量手册、作业指导书、记录单）")
    private String fileTypeId;
    
    /**
    * 编制人id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("编制人id")
    private String makerId;
    
    /**
    * 编制人名称
    */
    @Column(length=50)
    @ApiModelProperty("编制人名称")
	private String makerName;
    
    /**
    * 编制时间
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("编制时间")
    private Date compileTime;
    
    /**
    * 原受控编号
    */
    @Column(length=100)
    @ApiModelProperty("原受控编号")
    @Length(message = "原受控编号{validation.message.length}", max = 100)
    private String sourceControlCode;
    
    /**
    * 原版本号
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("原版本号")
    @Length(message = "原版本号{validation.message.length}", max = 50)
    private String sourceVersion;
    
    /**
    * 受控编号
    */
    @Column(length=100)
    @ApiModelProperty("受控编号")
    @Length(message = "受控编号{validation.message.length}", max = 100)
    private String controlCode;
    
    /**
    * 版本号
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("版本号")
    @Length(message = "版本号{validation.message.length}", max = 50)
    private String version;
    
    /**
    * 修订日期
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("修订日期")
    private Date reviseDate;
    
    /**
    * 修订内容
    */
    @Column(length=1000)
    @ApiModelProperty("修订内容")
    @Length(message = "修订内容{validation.message.length}", max = 1000)
    private String reviseContent;
    
    /**
    * 假删
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
	private Boolean isDeleted=false;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }