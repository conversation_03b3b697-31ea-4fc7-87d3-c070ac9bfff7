package com.sinoyd.lims.monitor.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.monitor.criteria.FixedPointPropertyCriteria;
import com.sinoyd.lims.monitor.dto.lims.DtoFixedPointProperty;
import com.sinoyd.lims.monitor.service.FixedPointPropertyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * FixedPointProperty服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@Api(tags = "示例: FixedPointProperty服务")
@RestController
@RequestMapping("api/monitor/fixedPointProperty")
public class FixedPointPropertyController extends BaseJpaController<DtoFixedPointProperty, String, FixedPointPropertyService> {


    /**
     * 分页动态条件查询FixedPointProperty
     *
     * @param fixedPointPropertyCriteria 条件参数
     * @return RestResponse<List < FixedPointProperty>>
     */
    @ApiOperation(value = "分页动态条件查询FixedPointProperty", notes = "分页动态条件查询FixedPointProperty")
    @GetMapping
    public RestResponse<List<DtoFixedPointProperty>> findByPage(FixedPointPropertyCriteria fixedPointPropertyCriteria) {
        PageBean<DtoFixedPointProperty> pageBean = super.getPageBean();
        RestResponse<List<DtoFixedPointProperty>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, fixedPointPropertyCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询FixedPointProperty
     *
     * @param id 主键id
     * @return RestResponse<DtoFixedPointProperty>
     */
    @ApiOperation(value = "按主键查询FixedPointProperty", notes = "按主键查询FixedPointProperty")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoFixedPointProperty> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoFixedPointProperty> restResponse = new RestResponse<>();
        DtoFixedPointProperty fixedPointProperty = service.findOne(id);
        restResponse.setData(fixedPointProperty);
        restResponse.setRestStatus(StringUtil.isNull(fixedPointProperty) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增FixedPointProperty
     *
     * @param fixedPointProperty 实体列表
     * @return RestResponse<DtoFixedPointProperty>
     */
    @ApiOperation(value = "新增FixedPointProperty", notes = "新增FixedPointProperty")
    @PostMapping
    public RestResponse<List<DtoFixedPointProperty>> create(@RequestBody @Validated DtoFixedPointProperty fixedPointProperty) {
        RestResponse<List<DtoFixedPointProperty>> restResponse = new RestResponse<>();
        restResponse.setData(service.batchSaveByMonths(fixedPointProperty));
        return restResponse;
    }

    /**
     * 新增FixedPointProperty
     *
     * @param fixedPointProperty 实体列表
     * @return RestResponse<DtoFixedPointProperty>
     */
    @ApiOperation(value = "修改FixedPointProperty", notes = "修改FixedPointProperty")
    @PutMapping
    public RestResponse<DtoFixedPointProperty> update(@RequestBody @Validated DtoFixedPointProperty fixedPointProperty) {
        RestResponse<DtoFixedPointProperty> restResponse = new RestResponse<>();
        restResponse.setData(service.update(fixedPointProperty));
        return restResponse;
    }

    /**
     * "根据id批量删除FixedPointProperty
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除FixedPointProperty", notes = "根据id批量删除FixedPointProperty")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 加载树
     *
     * @param year 年份
     * @return RestResponse<List < FixedPointProperty>>
     */
    @ApiOperation(value = "加载树", notes = "加载树")
    @GetMapping("/tree")
    public RestResponse<List<DtoFixedPointProperty>> loadTree(@RequestParam("year") Integer year) {
        RestResponse<List<DtoFixedPointProperty>> restResponse = new RestResponse<>();
        restResponse.setData(service.loadTree(year));
        return restResponse;
    }

    /**
     * 选择关联点位
     *
     * @param dtoFixedPointProperty 实体
     * @return RestResponse<Boolean> 响应内容
     */
    @ApiOperation(value = "选择关联点位", notes = "选择关联点位")
    @PostMapping("/relation")
    public RestResponse<Boolean> selectRelationPoints(@RequestBody DtoFixedPointProperty dtoFixedPointProperty) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.selectRelationPoints(dtoFixedPointProperty);
        restResponse.setData(Boolean.TRUE);
        return restResponse;
    }

    /**
     * 加载点位上的关联测试项目信息
     *
     * @param pointIds 点位主键列表
     * @return RestResponse<List < DtoTest>>
     */
    @ApiOperation(value = "加载监测计划的关联点位列表", notes = "加载监测计划的关联点位列表")
    @PostMapping(path = "/points/test")
    public RestResponse<List<DtoTest>> loadPointTest(@RequestBody List<String> pointIds) {
        RestResponse<List<DtoTest>> restResponse = new RestResponse<>();
        List<DtoTest> dataList = service.loadPointTest(pointIds);
        restResponse.setData(dataList);
        restResponse.setCount(StringUtil.isEmpty(dataList) ? 0 : dataList.size());
        return restResponse;
    }

    /**
     * 复制监测计划
     *
     * @param parameter 参数
     * @return RestResponse<Boolean> 响应内容
     */
    @ApiOperation(value = "复制监测计划", notes = "复制监测计划")
    @PostMapping("/copy")
    public RestResponse<Boolean> copyProperty(@RequestBody Map<String, Object> parameter) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.copyProperty(parameter);
        restResponse.setData(Boolean.TRUE);
        return restResponse;
    }
}