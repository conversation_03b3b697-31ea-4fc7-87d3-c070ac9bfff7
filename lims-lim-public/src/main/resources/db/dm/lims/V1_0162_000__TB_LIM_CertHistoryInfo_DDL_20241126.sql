-- 证书历史记录表
CREATE TABLE TB_LIM_CertHistoryInfo
(
    id           varchar(50) NOT NULL,
    detail       text,
    infoType     int,
    orgId        varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    creator      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    createDate   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    domainId     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifier     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifyDate   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP
);
ALTER TABLE TB_LIM_CertHistoryInfo ADD CONSTRAINT PRIMARY KEY (id);
comment on table TB_LIM_CertHistoryInfo is '证书历史记录表';
comment on column TB_LIM_CertHistoryInfo.detail is '详细信息';
comment on column TB_LIM_CertHistoryInfo.infoType is '记录类型';
comment on column TB_LIM_CertHistoryInfo.orgId is '组织机构id';
comment on column TB_LIM_CertHistoryInfo.creator is '创建人';
comment on column TB_LIM_CertHistoryInfo.createDate is '创建时间';
comment on column TB_LIM_CertHistoryInfo.domainId is '所属实验室';
comment on column TB_LIM_CertHistoryInfo.modifier is '修改人';
comment on column TB_LIM_CertHistoryInfo.modifyDate is '修改时间';

-- 证书历史记录附件表
CREATE TABLE TB_LIM_CertHistoryFile
(
    id           varchar(50) NOT NULL,
    documentId   varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    referenceId  varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
);
ALTER TABLE TB_LIM_CertHistoryFile ADD CONSTRAINT PRIMARY KEY (id);
comment on table TB_LIM_CertHistoryFile is '证书历史记录附件表';
comment on column TB_LIM_CertHistoryFile.id is '标识';
comment on column TB_LIM_CertHistoryFile.documentId is '新附件标识';
comment on column TB_LIM_CertHistoryFile.referenceId is '原附件标识';