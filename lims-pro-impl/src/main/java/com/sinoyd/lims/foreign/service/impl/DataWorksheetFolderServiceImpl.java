package com.sinoyd.lims.foreign.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.foreign.DataSampleVO;
import com.sinoyd.lims.foreign.DataWorksheetFolderVO;
import com.sinoyd.lims.foreign.service.DataWorksheetFolderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工作单明细接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Service
public class DataWorksheetFolderServiceImpl extends AbsQueryDataServiceImpl<DataWorksheetFolderVO> implements DataWorksheetFolderService {

    @Override
    public StringBuilder getSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("select id,workSheetCode,analystName,analyzeMethodId,");
        sql.append("backOpinion,finishTime,remark,orgId,domainId");
        sql.append(" from TB_PRO_WorkSheetFolder  ");
        sql.append(" WHERE 1 = 1 ");
        return sql;
    }

    @Override
    protected void appendCondition(StringBuilder sql, Map<String, Object> map, String paramName, String fieldName) {
        //  同步时间
        super.appendCondition(sql, map, " AND modifyDate > :syncTime", "syncTime");
        //  工作单ids
        super.appendCondition(sql, map, " AND id in (:workSheetFolderIds) ", "workSheetFolderIds");
    }

    @Override
    protected RowMapper getEntityRowMapper() {
        return (rs, i) -> {
            DataWorksheetFolderVO worksheetFolderVO = new DataWorksheetFolderVO();
            worksheetFolderVO.setId(UUIDHelper.NewID());
            worksheetFolderVO.setWorkSheetFolderId(rs.getString("id"));
            worksheetFolderVO.setWorkSheetCode(rs.getString("workSheetCode"));
            worksheetFolderVO.setAnalystName(rs.getString("analystName"));
            worksheetFolderVO.setAnalyzeMethodId(rs.getString("analyzeMethodId"));
            worksheetFolderVO.setBackOpinion(rs.getString("backOpinion"));
            worksheetFolderVO.setFinishTime(rs.getDate("finishTime"));
            worksheetFolderVO.setRemark(rs.getString("remark"));
            worksheetFolderVO.setOrgId(rs.getString("orgId"));
            worksheetFolderVO.setDomainId(rs.getString("domainId"));
            worksheetFolderVO.setSyncTime(new Date());
            return worksheetFolderVO;
        };
    }
}
