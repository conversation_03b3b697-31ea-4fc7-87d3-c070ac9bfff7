package com.sinoyd.base.utils.reflection;

import org.springframework.cglib.beans.BeanGenerator;
import org.springframework.cglib.beans.BeanMap;

import java.util.Map;

/**
 * 动态bean
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/9/24
 */
public class DynamicBean {

    /**
     * 目标对象
     */
    private Object target;

    /**
     * 属性集合
     */
    private BeanMap beanMap;

    /**
     * 默认构造方法
     */
    public DynamicBean() {
    }

    /**
     * 构造方法
     *
     * @param superclass  父类
     * @param propertyMap 扩展属性映射
     */
    public DynamicBean(Class superclass, Map<String, Class> propertyMap) {
        this.target = generateBean(superclass, propertyMap);
        this.beanMap = BeanMap.create(this.target);
    }

    /**
     * bean 添加属性和值
     *
     * @param property 属性名
     * @param value    值
     */
    public void setValue(String property, Object value) {
        beanMap.put(property, value);
    }

    /**
     * 获取属性值
     *
     * @param property 属性名
     * @return 属性值
     */
    public Object getValue(String property) {
        return beanMap.get(property);
    }

    /**
     * 获取对象
     *
     * @return 实例
     */
    public Object getTarget() {
        return this.target;
    }

    /**
     * 根据属性生成对象
     *
     * @param superclass  父类
     * @param propertyMap 扩展属性映射
     * @return 实例
     */
    private Object generateBean(Class superclass, Map<String, Class> propertyMap) {
        BeanGenerator generator = new BeanGenerator();
        if (null != superclass) {
            generator.setSuperclass(superclass);
        }
        BeanGenerator.addProperties(generator, propertyMap);
        return generator.create();
    }
}