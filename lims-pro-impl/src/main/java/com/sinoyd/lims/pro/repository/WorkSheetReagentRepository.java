package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoWorkSheetReagent;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;


/**
 * WorkSheetReagent数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface WorkSheetReagentRepository extends IBaseJpaRepository<DtoWorkSheetReagent, String> {

    /**
     * 按照试剂配置id集合返回试剂配置记录
     *
     * @param reagentConfigIds 试剂配置id集合
     * @return 试剂配置记录
     */
    List<DtoWorkSheetReagent> findByReagentConfigIdIn(List<String> reagentConfigIds);

    /**
     * 按照检测单id返回试剂配置记录
     *
     * @param workSheetFolderId 检测单id
     * @return 试剂配置记录
     */
    List<DtoWorkSheetReagent> findByWorksheetFolderId(String workSheetFolderId);

    /**
     * 根据检测单删除相应数据
     *
     * @param worksheetFolderIds 检测单id
     * @return 返回更新数据行
     */
    @Transactional
    @Modifying
    @Query("update DtoWorkSheetReagent a set a.isDeleted = true,a.modifier = :modifier,a.modifyDate= :modifyDate where a.worksheetFolderId in :worksheetFolderIds")
    Integer deleteByWorksheetFolderIdIn(@Param("worksheetFolderIds") List<String> worksheetFolderIds,
                                        @Param("modifier") String modifier,
                                        @Param("modifyDate") Date modifyDate);
}