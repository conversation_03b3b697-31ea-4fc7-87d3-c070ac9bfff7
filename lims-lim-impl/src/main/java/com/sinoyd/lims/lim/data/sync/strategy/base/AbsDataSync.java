package com.sinoyd.lims.lim.data.sync.strategy.base;

import com.jsoniter.JsonIterator;
import com.sinoyd.SpringContextAware;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.AESUtil;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.lim.data.sync.configuration.StandardDataConfig;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.lims.lim.data.sync.dto.DtoDataCompareResult;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 数据同步基类
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/4/20
 */
@Slf4j
public abstract class AbsDataSync<T extends BaseEntity> {

    protected final StandardDataConfig standardDataConfig;

    private final RestTemplate restTemplate;

    private static final String STANDARD_LOGIN_URL = "/api/proxy/auth/login";

    public static final String[] COMPARE_CATEGORY = new String[]{"新增", "差异", "相同"};

    public AbsDataSync() {
        standardDataConfig = SpringContextAware.getBean(StandardDataConfig.class);
        restTemplate = SpringContextAware.getBean(RestTemplate.class);
    }

    /**
     * 将远程调用结果集合转换成实体集合
     *
     * @param resultMapList 远程调用结果集合
     * @return 实体集合
     */
    public List<T> convertObject(List<Map<String, Object>> resultMapList) {
        if (StringUtil.isEmpty(resultMapList)) {
            return null;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        List<T> list = new ArrayList<>();
        try {
            for (Map<String, Object> map : resultMapList) {
                String json = objectMapper.writeValueAsString(map);
                T t = JsonIterator.deserialize(json, getGenericClass());
                list.add(t);
            }
        } catch (IOException e) {
            throw new BaseException("转换实体发生错误");
        }
        return list;
    }

    /**
     * 比较共同库和项目库数据
     *
     * @param sourceDataList 公共库数据集合
     * @param targetDataList 项目库数据集合
     * @return 比较结果集合
     */
    protected List<DtoDataCompareResult<T>> compareData(List<T> sourceDataList, List<T> targetDataList) {
        //初始化返回结果
        List<DtoDataCompareResult<T>> resultList = new ArrayList<>();
        //初始化相同记录数、差异记录数、新增记录数
        int sameCount = 0, editCount = 0, addCount = 0;
        //待新增的数据集合
        List<T> addDataList = new ArrayList<>();
        if (StringUtil.isNotEmpty(sourceDataList)) {
            for (T source : sourceDataList) {
                Optional<T> targetOptional = targetDataList.parallelStream().filter(p -> p.getId().equals(source.getId())).findFirst();
                if (targetOptional.isPresent()) {
                    //项目中存在该数据，需要判断是否有差异
                    if (compareEntity(source, targetOptional.get())) {
                        //项目中数据和共同库相同，属于相同记录
                        sameCount++;
                    } else {
                        //项目中数据和共同库不相同，属于待更新记录
                        editCount++;
                    }
                } else {
                    //项目中不存在该数据，属于待新增记录
                    addCount++;
                    addDataList.add(source);
                }
            }
        }
        resultList.add(new DtoDataCompareResult<>(COMPARE_CATEGORY[2], getItemName(), sameCount, mustSync(), getOrderNum(), null));
        resultList.add(new DtoDataCompareResult<>(COMPARE_CATEGORY[1], getItemName(), editCount, mustSync(), getOrderNum(), null));
        resultList.add(new DtoDataCompareResult<>(COMPARE_CATEGORY[0], getItemName(), addCount, mustSync(), getOrderNum(), addDataList));
        return resultList;
    }

    /**
     * 实体比较
     *
     * @param source 源实体
     * @param target 目标实体
     * @return true: 相同， false: 不同
     */
    protected boolean compareEntity(T source, T target) {
        //对于公共库未假删，项目上假删的数据，认为项目上不需要，所以忽略比较isDeleted属性
        final List<String> ignoreFields = Stream.of("orgId", "creator", "createDate", "domainId", "modifier",
                "modifyDate", "serialVersionUID", "isDeleted").collect(Collectors.toList());
        Class<T> clazz = getGenericClass();
        Field[] fields = clazz.getSuperclass().getDeclaredFields();
        try {
            for (Field field : fields) {
                field.setAccessible(true);
                //忽略属性不进行更新
                Object sourceValue = field.get(source);
                Object targetValue = field.get(target);
                if(targetValue != null && targetValue instanceof Timestamp){
                    targetValue = new Date(((Timestamp) targetValue).getTime());
                }
                if (!ignoreFields.contains(field.getName())) {
                    if (sourceValue == null && targetValue != null) {
                        return false;
                    } else if (sourceValue != null && targetValue == null) {
                        return false;
                    } else if (sourceValue != null && !sourceValue.equals(targetValue)) {
                        return false;
                    } else if (targetValue != null && !targetValue.equals(sourceValue)) {
                        return false;
                    }
                }
            }
        } catch (IllegalAccessException e) {
            log.error(e.getMessage(), e);
            throw new BaseException("比较对象数据发生错误");
        }
        return true;
    }

    /**
     * 数据比较
     *
     * @param testIds 需要同步的测试项目id
     * @return 比较结果
     */
    public abstract List<DtoDataCompareResult<T>> compareData(List<String> testIds);

    /**
     * 同步数据
     *
     * @param testIds         需要同步的测试项目id集合
     * @param webSocketServer websockets服务端
     */
    public abstract void syncData(List<String> testIds, WebSocketServer webSocketServer);

    /**
     * 查询所有公共库数据
     *
     * @return 公共库数据集合
     */
    public List<T> queryStandardData() {
        String url = standardDataConfig.getHost() + getStandardDataQueryUrl() +
                "?page=1&rows=" + Integer.MAX_VALUE;
        List<Map<String, Object>> resultMapList = (List<Map<String, Object>>) queryStandardData(url).getBody().get("data");
        return convertObject(resultMapList);
    }

    /**
     * 远程分页查询公共库数据
     *
     * @param criteria 查询条件
     * @param page     页码
     * @param rows     每页记录数
     * @return 结果
     */
    public ResponseEntity<JSONObject> queryStandardData(BaseCriteria criteria, int page, int rows, String sort) {
        String url = standardDataConfig.getHost() + getStandardDataQueryUrl() +
                "?page=" + page + "&rows=" + rows + "&sort=" + sort;
        return queryStandardData(url);
    }

    /**
     * 是否必须同步
     *
     * @return true: 必须同步， false：不用必须同步
     */
    public abstract boolean mustSync();

    /**
     * 同步的数据项名称
     *
     * @return 数据项名称
     */
    public abstract String getItemName();

    /**
     * 获取排序值
     *
     * @return 排序值
     */
    public abstract Integer getOrderNum();

    /**
     * 获取标准库数据查询url
     *
     * @return url
     */
    public abstract String getStandardDataQueryUrl();

    /**
     * 数据类型
     *
     * @return 数据类型
     */
    public abstract Integer getSyncDataType();

    /**
     * 获取需要同步的依赖项数据类型
     *
     * @return 需要同步的依赖项数据类型
     */
    public List<Integer> getDependDataType() {
        return Arrays.asList(getSyncDataType());
    }

    /**
     * 远程登录标准库
     *
     * @return 登录后的token
     */
    protected String getStandardToken() {
        String token = "";
        try {
            String loginUrl = standardDataConfig.getHost() + STANDARD_LOGIN_URL;
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            Map<String, String> params = new HashMap<>();
            params.put("uid", AESUtil.encrypt(standardDataConfig.getLoginId()));
            params.put("pid", AESUtil.encrypt(standardDataConfig.getPassword()));
            HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(params, headers);
            ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(loginUrl, requestEntity, JSONObject.class);
            if (ERestStatus.SUCCESS.getCode() != responseEntity.getStatusCodeValue()) {
                throw new BaseException("远程登录标准库发生错误");
            }
            token = ((Map<String, Object>) responseEntity.getBody().get("data")).get("token").toString();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("远程登录标准库发生错误");
        }
        return token;
    }

    /**
     * 远程查询公共库数据
     *
     * @param url 查询url
     * @return 查询结果
     */
    protected ResponseEntity<JSONObject> queryStandardData(String url) {
        ResponseEntity<JSONObject> responseEntity;
        try {
            Map<String, Object> params = new HashMap<>();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            headers.set("Authorization", getStandardToken());
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(params, headers);
            responseEntity = restTemplate.exchange(url, HttpMethod.GET, requestEntity, JSONObject.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("获取公共库中数据发生错误");
        }
        return responseEntity;
    }

    /**
     * 获取 T.class
     *
     * @return T.class
     */
    private Class<T> getGenericClass() {
        return (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
    }


    /**
     * 获取子项需要同步的数据
     *
     * @return 子项需要同步的数据
     */
    public Map<Integer, List<String>> getChildItemMap(List<String> dataIds) {
        return new HashMap<>();
    }

}