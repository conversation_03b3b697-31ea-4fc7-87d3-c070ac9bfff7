FROM registry.dev.yd/sinoyd/registry/maven:3.8.4-openjdk-8

ADD ./bin /app

WORKDIR /app

ENV JAVA_OPTS=""
ENTRYPOINT ["java", "-server", "-Duser.timezone=GMT+08", "-XX:+UseG1GC", "-XX:MaxGCPauseMillis=200", "-XX:InitiatingHeapOccupancyPercent=45", "-Xms4096m","-Xmx4096m", "-Xmn1536m","-XX:CompressedClassSpaceSize=512m","-XX:MetaspaceSize=512m","-XX:MaxMetaspaceSize=512m","-Djava.security.egd=file:/dev/./urandom", "-Dfile.encoding=utf-8","-jar","/app/app.jar"]

# Service listens on port 80
EXPOSE 80
