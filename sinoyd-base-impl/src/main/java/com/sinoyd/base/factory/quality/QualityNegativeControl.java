package com.sinoyd.base.factory.quality;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.dto.customer.DtoQualityConfig;
import com.sinoyd.base.dto.vo.OrderReviseVO;
import com.sinoyd.base.factory.task.QualityControlKind;
import com.sinoyd.frame.service.CalculationService;

import java.util.List;
import java.util.Map;

public class QualityNegativeControl extends QualityControlKind {

    @Override
    public Integer qcTypeValue() {
        return 16384;
    }

    @Override
    public String qcTypeName() {
        return "阴性对照试验";
    }

    @Override
    public String getSampleProperty(Integer qcGrade) {
        return "阴性对照试验";
    }

    @Override
    public DtoQualityConfig getQualityConfig() {
        return new DtoQualityConfig();
    }

    @Override
    public DtoQualityConfig getQualityConfig(List<OrderReviseVO> orderReviseVOList) {
        return new DtoQualityConfig();
    }

    @Override
    public String getFormt(Map<String, String> map) {
        return "增加了阴性对照试验样qcCode，原样为oldCode。";
    }

    @Override
    public String getRedFolderName(String folderName, Integer qcGrade) {
        return "阴性对照试验样";
    }

    @Override
    public Boolean deviationQualified(DtoQualityControlLimit controlLimit, String value) {
        CalculationService calculationService = SpringContextAware.getBean(CalculationService.class);
        return deviationQualified(controlLimit, value, calculationService);
    }
}
