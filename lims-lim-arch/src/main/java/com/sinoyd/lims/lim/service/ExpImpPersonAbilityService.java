package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.lim.dto.customer.expimp.DtoExpImpPersonAbility;
import com.sinoyd.lims.lim.dto.lims.DtoPersonAbility;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 人员检测能力导出接口实现
 *
 * @version V1.0.0 2023/12/27
 * @author: hukq
 * @since V100R001
 */
public interface ExpImpPersonAbilityService extends ImportExcelService<DtoExpImpPersonAbility, DtoPersonAbility> {

    /**
     * 导出
     *
     * @param criteria   请求参数
     * @param response   响应体
     * @param sheetNames sheet页名称
     * @param fileName   附件名称
     */
    void export(BaseCriteria criteria, HttpServletResponse response, Map<String, String> sheetNames, String fileName);
}
