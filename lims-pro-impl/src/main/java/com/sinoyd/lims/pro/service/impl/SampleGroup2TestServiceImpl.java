package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.pro.dto.DtoSampleGroup2Test;
import com.sinoyd.lims.pro.repository.SampleGroup2TestRepository;
import com.sinoyd.lims.pro.service.SampleGroup2TestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * SampleGroup2Test操作接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/28
 * @since V100R001
 */
@Slf4j
@Service
public class SampleGroup2TestServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoSampleGroup2Test, String, SampleGroup2TestRepository> implements SampleGroup2TestService {

    @Override
    public List<DtoSampleGroup2Test> findByGroupId(String groupId) {
        return repository.findBySampleGroupIdIn(Collections.singletonList(groupId));
    }
}
