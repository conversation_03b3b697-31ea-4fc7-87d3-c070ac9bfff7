package com.sinoyd.lims.probase.service;

import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoReceiveSubSampleRecord;

import java.util.List;

public interface CheckRecRecordService {

    /**
     * 送样单状态纠正
     *
     * @param id
     */
    void checkReceiveSampleRecord(String id);

    /**
     * 核对送样单状态
     * @param objrsr
     */
    void  checkReceiveSampleRecord(DtoReceiveSampleRecord objrsr);

    /**
     * 创建送样单
     *
     * @param objrsr
     * @param asids
     * @param type
     * @return
     */
    DtoReceiveSubSampleRecord createReceiveSubSampleRecord(DtoReceiveSampleRecord
                                                                   objrsr, List<String> asids, String type);
}
