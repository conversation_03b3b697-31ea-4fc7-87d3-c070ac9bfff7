package com.sinoyd.lims.lim.dto.customer;

import lombok.Data;

import java.util.Date;

/**
 * 仪器出库情况
 *
 * <AUTHOR>
 * @version V1.0.0 2022/11/07
 * @since V100R001
 */
@Data
public class DtoInstrumentCheckOut {

    /**
     * 出库信息id
     */
    private String id;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 仪器id
     */
    private String instrumentId;

    /**
     * 仪器规格型号
     */
    private String instrumentModel;

    /**
     * 仪器规格型号
     */
    private String instrumentsCode;

    /**
     * 仪器规格型号
     */
    private String serialNo;

    /**
     * 出库日期
     */
    private Date outDate;

    /**
     * 出库日期显示字段
     */
    private String outDateStr;

    /**
     * 出库人
     */
    private String outPerson;

    /**
     * 出库人id
     */
    private String outPersonId;

    /**
     * 出库情况
     */
    private Integer outQualified;

    /**
     * 出库情况显示名称
     */
    private String outQualifiedName;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 出入库记录中的项目id（可能为多个id，用";"隔开）
     */
    private String insProjectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 多项目名称
     */
    private String insProjectName;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目负责人
     */
    private String leaderId;

    /**
     * 项目负责人名称
     */
    private String leaderName;

    /**
     * 是否确认
     */
    private Integer isConfirm;

    public DtoInstrumentCheckOut(String id, String instrumentName, String instrumentId, String instrumentModel, Date outDate,
                                 Integer outQualified, String projectId,String insProjectName, String projectName, String projectCode,
                                 String leaderId,Integer isConfirm,String outPersonId) {
        this.id = id;
        this.instrumentName = instrumentName;
        this.instrumentId = instrumentId;
        this.instrumentModel = instrumentModel;
        this.outDate = outDate;
        this.outQualified = outQualified;
        this.projectId = projectId;
        this.insProjectName = insProjectName;
        this.projectName = projectName;
        this.projectCode = projectCode;
        this.leaderId = leaderId;
        this.isConfirm = isConfirm;
        this.outPersonId = outPersonId;
    }

    public DtoInstrumentCheckOut(String id, String instrumentName, String instrumentId, String instrumentModel, Date outDate,
                                 Integer outQualified, String projectId, String insProjectName, String insProjectId, String projectName, String projectCode,
                                 String leaderId,Integer isConfirm,String outPersonId) {
        this.id = id;
        this.instrumentName = instrumentName;
        this.instrumentId = instrumentId;
        this.instrumentModel = instrumentModel;
        this.outDate = outDate;
        this.outQualified = outQualified;
        this.projectId = projectId;
        this.insProjectName = insProjectName;
        this.insProjectId = insProjectId;
        this.projectName = projectName;
        this.projectCode = projectCode;
        this.leaderId = leaderId;
        this.isConfirm = isConfirm;
        this.outPersonId = outPersonId;
    }

    public DtoInstrumentCheckOut(String id, String instrumentName, String instrumentId, String instrumentModel, Date outDate,
                                 Integer outQualified, String projectId, String insProjectName, String insProjectId, String projectName, String projectCode,
                                 String leaderId,Integer isConfirm,String outPersonId,String instrumentsCode,String serialNo) {
        this.id = id;
        this.instrumentName = instrumentName;
        this.instrumentId = instrumentId;
        this.instrumentModel = instrumentModel;
        this.outDate = outDate;
        this.outQualified = outQualified;
        this.projectId = projectId;
        this.insProjectName = insProjectName;
        this.insProjectId = insProjectId;
        this.projectName = projectName;
        this.projectCode = projectCode;
        this.leaderId = leaderId;
        this.isConfirm = isConfirm;
        this.outPersonId = outPersonId;
        this.instrumentsCode = instrumentsCode;
        this.serialNo = serialNo;
    }
}
