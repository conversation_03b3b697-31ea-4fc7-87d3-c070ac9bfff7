package com.sinoyd.lims.pro.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * Comment查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CommentCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 评论关联id
    */
    private String objectId;

    /**
    * 对象类型
    */
    private Integer objectType;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.objectId)) {
            condition.append(" and objectId = :objectId");
            values.put("objectId", this.objectId);
        }
        if (StringUtil.isNotNull(this.objectType)) {
            condition.append(" and objectType = :objectType");
            values.put("objectType", this.objectType);
        }
        return condition.toString();
    }
}