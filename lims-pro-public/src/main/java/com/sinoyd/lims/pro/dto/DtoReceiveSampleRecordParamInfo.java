package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.ReceiveSampleRecordParamInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * ReceiveSampleRecordParam实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/09/19
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_ReceiveSampleRecordParamInfo")
@Data
@DynamicInsert
public class DtoReceiveSampleRecordParamInfo extends ReceiveSampleRecordParamInfo {

}