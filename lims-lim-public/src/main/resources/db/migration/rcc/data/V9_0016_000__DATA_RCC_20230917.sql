-- 送样类报告应用配置
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('3c131708-dcd3-4560-969f-38e60153e25f', '94218570-513e-4347-9af7-1534eaf36b78', 'ReportEdit', '报告编制',
        'NormalWaterStd', '标准版送样类报告', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-05-30 13:54:53', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-05-30 13:54:53');

-- 送样类报告表头组件配置
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('92d5fafa-47b7-4eae-9492-f36be43b381a', 'dtSyHeadStdTable', '标准版送样类报告表头组件', 'dtSyHeadStdTable', '', 0, 0, '',
        b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2023-09-17 19:16:25',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2023-09-17 19:16:25');

-- 送样类报告检测结果表组件配置
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('3988a0aa-0df5-4a7e-91e2-cd5cb7c23c13', 'dtSyStdTable', '标准版送样检测结果表组件', 'dtSyStdTable', 'dtSySource', 4, 10, '',
        b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2023-09-17 19:31:30',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2023-09-17 19:31:30');

-- 送样类报告检测数据主表组件配置
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('1078837a-9b0c-4b6a-aa42-22768a65643e', 'sYStdDataSource', '标准版送样类报告检测数据主表', 'dtDataSource', '', 0, 0,
        '{\"headModule\":\"dtSyHeadStdTable\", \"bodyModule\":\"dtSyStdTable\", \"secondBodyModule\":\"dtCompoundStdTable\", \"thirdBodyModule\":\"\"}',
        b'1', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2023-09-17 19:37:21',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2023-09-17 19:37:21');

-- 送样类报告模板组件关系配置
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('00ca1b4f-32d2-42bd-9256-86fbb917e5e5', '94218570-513e-4347-9af7-1534eaf36b78',
        'ba52a4c7-960f-4ede-ad34-6a4ea0335ba4');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('6953c905-540e-43dc-994a-d88f3af51f59', '94218570-513e-4347-9af7-1534eaf36b78',
        '55fe89e1-dc8c-4b86-b03b-e6dbf3143340');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('8e62747c-ed5f-461e-a569-9c4ff042dfff', '94218570-513e-4347-9af7-1534eaf36b78',
        '9b557e2c-bd2c-4c73-8a6c-4a55e38f52ba');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('9845b3da-f6a2-4ddd-b6a8-9c0385969134', '94218570-513e-4347-9af7-1534eaf36b78',
        '44c0ef90-1cee-4379-bdec-e502a875c0a7');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('9a4717bf-1488-4106-b44f-ea40c48378bd', '94218570-513e-4347-9af7-1534eaf36b78',
        '1078837a-9b0c-4b6a-aa42-22768a65643e');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('b226988f-42a8-4adf-b595-ecb563910e41', '94218570-513e-4347-9af7-1534eaf36b78',
        '76d9545f-8eb1-4ab2-8aa4-f9decfe2a54e');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('b6d9fe9b-dfc3-4b09-9397-db768a7439ba', '94218570-513e-4347-9af7-1534eaf36b78',
        'fe05546e-47f5-436a-a99f-970a96bcd590');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('df1c57a8-41e3-4b3a-b773-24909f1a9093', '94218570-513e-4347-9af7-1534eaf36b78',
        'fff21a0d-5e6f-4608-b966-d09bf86cc5f9');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('fb82d5d6-5821-4eb2-ac11-a30c318f5ef4', '94218570-513e-4347-9af7-1534eaf36b78',
        '3477d948-c1eb-418f-8fbd-81fc1295cee6');