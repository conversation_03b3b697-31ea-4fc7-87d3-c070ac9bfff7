package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoSampleTypeGroup2Test;

import java.util.Collection;
import java.util.List;

/**
 * 收/付款记录管理
 * <AUTHOR>
 * @version V1.0.0 2019/2/25
 * @since V100R001
 */
public interface SampleTypeGroup2TestService extends IBaseJpaService<DtoSampleTypeGroup2Test, String> {
    
    /**
     * 获取样品分组列表
     * @param sampleTypeGroupId 分组id
     * @param testIds 测试项目
     * @return 样品分组列表
     */
    List<DtoSampleTypeGroup2Test> getList ( String sampleTypeGroupId , Collection<String> testIds);
    
    /**
     * 根据sampleTypeGroupId查询关联数据
     * @param sampleTypeGroupId 样品分组id
     * @return 返回关联数据集合
     */
    List<DtoSampleTypeGroup2Test> findBySampleTypeGroupId(String sampleTypeGroupId);

    /**
     * 根据sampleTypeGroupId查询关联数据
     * @param sampleTypeGroupIds 样品分组id数组
     * @return 返回关联数据集合
     */
    List<DtoSampleTypeGroup2Test> findBySampleTypeGroupIds(List<String> sampleTypeGroupIds);


}