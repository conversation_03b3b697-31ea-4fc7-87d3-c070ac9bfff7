package com.sinoyd.lims.pro.service.impl;

import com.google.gson.Gson;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.rcc.DtoEvaluationLevel;
import com.sinoyd.base.dto.rcc.DtoEvaluationValue;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.rcc.EvaluationLevelRepository;
import com.sinoyd.base.repository.rcc.EvaluationValueRepository;
import com.sinoyd.base.service.EnterpriseService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.boot.frame.sys.model.OrgModel;
import com.sinoyd.boot.frame.sys.service.IOrgService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.ProjectTypeRepository;
import com.sinoyd.lims.monitor.criteria.StatisticCriteria;
import com.sinoyd.lims.monitor.dto.lims.DtoFixedPointProperty;
import com.sinoyd.lims.monitor.dto.lims.DtoProperty2Point;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;
import com.sinoyd.lims.monitor.dto.rcc.DtoStation;
import com.sinoyd.lims.monitor.repository.lims.FixedPointPropertyRepository;
import com.sinoyd.lims.monitor.repository.lims.Property2PointRepository;
import com.sinoyd.lims.monitor.repository.rcc.FixedpointRepository;
import com.sinoyd.lims.monitor.repository.rcc.StationRepository;
import com.sinoyd.lims.pro.criteria.DetailDataCriteria;
import com.sinoyd.lims.pro.criteria.DetailDataProjectCriteria;
import com.sinoyd.lims.pro.criteria.EnvironmentStatisticsExportCriteria;
import com.sinoyd.lims.pro.criteria.SampleJudgeDataCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoDataDetail;
import com.sinoyd.lims.pro.dto.customer.DtoDetailDataColumn;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 统计表数据处理类
 *
 * <AUTHOR>
 * @version V1.0.0 2023/06/06
 * @since V100R001
 */
@Service
@Slf4j
@SuppressWarnings({"unchecked"})
public class StatisticDataHandleServiceImpl implements StatisticDataHandleService {

    private SampleRepository sampleRepository;

    private QualityControlRepository qualityControlRepository;

    private ParamsDataRepository paramsDataRepository;

    private SampleFolderRepository sampleFolderRepository;

    private FixedpointRepository fixedpointRepository;

    private StationRepository stationRepository;

    private Property2PointRepository property2PointRepository;

    private FixedPointPropertyRepository fixedPointPropertyRepository;

    private Project2FixedPropertyRepository project2FixedPropertyRepository;

    private EvaluationRecordRepository evaluationRecordRepository;

    private EvaluationLevelRepository evaluationLevelRepository;

    private EvaluationValueRepository evaluationValueRepository;

    private ProjectRepository projectRepository;

    private ProjectTypeRepository projectTypeRepository;

    private SamplingPersonConfigRepository samplingPersonConfigRepository;

    private AnalyseDataRepository analyseDataRepository;

    private DetailDataService detailDataService;

    private EnvironmentStatisticsService environmentStatisticsService;

    private IOrgService orgService;

    private EnterpriseService enterpriseService;

    private SampleJudgeDataService sampleJudgeDataService;

    private SampleFolderEvaluateRepository sampleFolderEvaluateRepository;

    private AnalyseDataFutureService analyseDataFutureService;

    private FolderSignRepository folderSignRepository;

    /**
     * 初始化参数Map
     *
     * @param baseCriteria 查询条件
     * @param sort         排序
     * @return 参数字典
     */
    @Override
    public Map<String, Object> initDataMap(BaseCriteria baseCriteria, Boolean isOverRed, String sort) {
        StatisticCriteria tzStatisticCriteria = (StatisticCriteria) baseCriteria;
        List<String> projectIds = tzStatisticCriteria.getProjectIds();
        Map<String, Object> resultDataMap = findDetailDataMap(tzStatisticCriteria, isOverRed, sort);
        //获取返回数据
        List<Map<String, Object>> analyseDataMapList = (List<Map<String, Object>>) resultDataMap.get("analyseData");
        //初始化样品数据
        this.findSampleListInMap(resultDataMap, projectIds, analyseDataMapList);
        //初始化分析数据
        this.findAnaDataInMap(resultDataMap, analyseDataMapList);
        //初始化项目数据
        this.findProjectDataInMap(resultDataMap);
        //初始化参数数据
        this.findParamsDataListInMap(resultDataMap);
        //初始化点位数据
        this.findFolderDataInMap(resultDataMap);
        //初始化评价标准
        this.findEvaluationRecordDataInMap(resultDataMap);
        //初始化机构数据
        this.loadOrgMsg(analyseDataMapList);
        //初始化企业区域数据
        this.loadEnterpriseArea(analyseDataMapList);
        // 初始化比对数据
        this.findSampleJudgeListInMap(baseCriteria, resultDataMap);
        return resultDataMap;
    }

    /**
     * 初始化企业区域数据
     *
     * @param analyseDataMapList 查询的数据
     */
    protected void loadEnterpriseArea(List<Map<String, Object>> analyseDataMapList) {
        if (StringUtils.isNotNull(analyseDataMapList) && analyseDataMapList.size() > 0) {
            List<String> inspectedEntIdList = analyseDataMapList.stream().map(p -> p.get("inspectedEntId").toString()).collect(Collectors.toList());
            //查询所有受检单位
            List<DtoEnterprise> inspectedEntList = StringUtil.isNotEmpty(inspectedEntIdList) ? enterpriseService.findAll(inspectedEntIdList) : new ArrayList<>();
            //区域名称
            String areaName = "";
            //处理受检单位区域数据
            for (Map<String, Object> map : analyseDataMapList) {
                if (StringUtil.isNotNull(map.get("inspectedEntId"))) {
                    String inspectedEntId = map.get("inspectedEntId").toString();
                    DtoEnterprise inspectedEnt = inspectedEntList.stream().filter(p -> inspectedEntId.equals(p.getId())).findFirst().orElse(null);
                    if (inspectedEnt != null) {
                        areaName = inspectedEnt.getAreaName();
                    }
                }
                map.put("areaName", areaName);
            }
        }
    }

    /**
     * 获取机构名称数据
     *
     * @param analyseDataMapList 查询到的统计数据
     */
    protected void loadOrgMsg(List<Map<String, Object>> analyseDataMapList) {
        OrgModel orgModel = null;
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            orgModel = orgService.selectById(PrincipalContextUser.getPrincipal().getOrgId());
        }
        if (orgModel != null) {
            if (StringUtils.isNotNull(analyseDataMapList) && analyseDataMapList.size() > 0) {
                for (Map<String, Object> map : analyseDataMapList) {
                    map.put("orgName", orgModel.getOrgName());
                }
            }
        }
    }

    /**
     * 数据源获取
     *
     * @param baseCriteria 查询条件
     * @param isOverRed    查询条件
     * @param sort         查询条件
     * @return 查询结果
     */
    protected Map<String, Object> findDetailDataMap(BaseCriteria baseCriteria, Boolean isOverRed, String sort) {
        Map<String, Object> retMap = new HashMap<>();
        StatisticCriteria tzStatisticCriteria = (StatisticCriteria) baseCriteria;
        // 是否为项目进度列表直接导出
        if (tzStatisticCriteria.getIsInquiry()) {
            List<String> projectIds = tzStatisticCriteria.getProjectIds();
            List<DtoProject> projectList = projectRepository.findAll(projectIds);
            List<String> projectTypeIds = projectList.stream().map(DtoProject::getProjectTypeId).distinct().collect(Collectors.toList());
            tzStatisticCriteria.setProjectTypeIds(projectTypeIds);
            return findDetailDataList(tzStatisticCriteria, isOverRed, sort);
        }
        //判断是否为环境质量统计生成
        if (StringUtils.isNotNullAndEmpty(tzStatisticCriteria.getProjectIds())) {
            EnvironmentStatisticsExportCriteria statisticsExportCriteria = new EnvironmentStatisticsExportCriteria();
            BeanUtils.copyProperties(tzStatisticCriteria, statisticsExportCriteria);
            DtoDataDetail dataDetail = new DtoDataDetail();
            BeanUtils.copyProperties(tzStatisticCriteria, dataDetail);
            dataDetail.setEvaluationType(statisticsExportCriteria.getEvaluationType());
            try {
                retMap = environmentStatisticsService.findDetailDataByProjectList(dataDetail);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return null;
            }
            handleDataDetail(retMap);

        }
        //判断是否为数据查询统计生成
        else if (StringUtils.isNotNullAndEmpty(tzStatisticCriteria.getProjectTypeIds())) {
            retMap = findDetailDataList(tzStatisticCriteria, isOverRed, sort);
        }
        //判断是否为项目进度生成
        else if (StringUtils.isNotNullAndEmpty(tzStatisticCriteria.getProjectId())) {
            DetailDataProjectCriteria detailDataProjectCriteria = new DetailDataProjectCriteria();
            BeanUtils.copyProperties(tzStatisticCriteria, detailDataProjectCriteria);
//            detailDataProjectCriteria.setIsCompleted(true);
            detailDataProjectCriteria.setIsAction(detailDataProjectCriteria.getIsAction());
            retMap = detailDataService.findProjectDetailData(detailDataProjectCriteria);
        }

        return retMap;
    }

    /**
     * 处理已经审核的数据
     *
     * @param retMap 数据
     */
    private void handleDataDetail(Map<String, Object> retMap) {
        //处理未审核通过的数据为空值
        List<Map<String, Object>> analyseDataMapList = (List<Map<String, Object>>) retMap.get("analyseData");
        List<DtoDetailDataColumn> dtoDetailDataTests = (List<DtoDetailDataColumn>) retMap.get("test");
        if (StringUtil.isNotEmpty(analyseDataMapList)) {
            //获取所有的样品id
            List<String> sampleIds = analyseDataMapList.stream().map(p -> p.get("id").toString()).distinct().collect(Collectors.toList());
            List<DtoAnalyseData> analyseDataList = new ArrayList<>();
            try {
//                List<DtoAnalyseData> analyseDataList = StringUtil.isNotEmpty(sampleIds) ? analyseDataRepository.findBySampleIdIn(sampleIds) : new ArrayList<>();
                analyseDataList = analyseDataRepository.findBySampleIdIn(sampleIds);
            } catch (Exception e) {
                log.error(e.getMessage());
            }

            Map<String, List<DtoAnalyseData>> anaDataGroupBySampleId = analyseDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleId));
            for (Map<String, Object> map : analyseDataMapList) {
                //获取到样品下的测试项目
                List<DtoAnalyseData> analyseDataOfSample = anaDataGroupBySampleId.get(map.get("id").toString());
                for (DtoDetailDataColumn test : dtoDetailDataTests) {
                    //根据测试项目id找到对应的数据
                    DtoAnalyseData analyseData = analyseDataOfSample.stream().filter(p -> test.getTestId().equals(p.getTestId())).findFirst().orElse(null);
                    if (analyseData != null) {
                        //判断是否为审核通过的数据
                        if (!EnumPRO.EnumAnalyseDataStatus.复核通过.getValue().equals(analyseData.getDataStatus())
                                && !EnumPRO.EnumAnalyseDataStatus.已确认.getValue().equals(analyseData.getDataStatus())) {
                            map.put(test.getTestId(), "");
                        }
                    }
                }
            }
        }
    }

    /**
     * 数据查询生成统计报表
     *
     * @param tzStatisticCriteria 查询条件
     * @param isOverRed           是否标红显示
     * @param sort                排序
     * @return 统计报表数据
     */
    private Map<String, Object> findDetailDataList(StatisticCriteria tzStatisticCriteria, Boolean isOverRed, String sort) {
        Map<String, Object> retMap;
        DetailDataCriteria detailDataCriteria = new DetailDataCriteria();
        BeanUtils.copyProperties(tzStatisticCriteria, detailDataCriteria);
        detailDataCriteria.setIsOverRed(isOverRed);
        detailDataCriteria.setIsComplete(tzStatisticCriteria.getIsComplete());
        detailDataCriteria.setNoneDataShow("--");
        //判断是否为环境质量统计
        PageBean<Object[]> pageBean = new PageBean<>();
        pageBean.setSort(sort);
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        retMap = detailDataService.findDetailDataByPage(pageBean, detailDataCriteria);
        return retMap;
    }


    /**
     * 处理样品数据
     *
     * @param resultDataMap      数据字典
     * @param projectIds         查询条件中的项目Id
     * @param analyseDataMapList 数据查询结果集
     */
    private void findSampleListInMap(Map<String, Object> resultDataMap, List<String> projectIds, List<Map<String, Object>> analyseDataMapList) {
        List<String> sampleIds;
        List<DtoSample> sampleList = new ArrayList<>();
        if (StringUtil.isNotEmpty(projectIds)) {
            sampleList = sampleRepository.findByProjectIdIn(projectIds);
        } else {
            if (StringUtil.isNotEmpty(analyseDataMapList)) {
                sampleIds = analyseDataMapList.stream().map(p -> p.get("id").toString()).collect(Collectors.toList());
                sampleList = StringUtil.isNotEmpty(sampleIds) ? sampleRepository.findByIds(sampleIds) : new ArrayList<>();
            }
        }
        sampleIds = StringUtil.isNotEmpty(sampleList) ? sampleList.stream().map(DtoSample::getId).collect(Collectors.toList()) : new ArrayList<>();
        //添加所有的质控样
        sampleList.addAll(StringUtil.isNotEmpty(sampleIds) ? sampleRepository.findByAssociateSampleIdIn(sampleIds) : new ArrayList<>());
        //获取样品的质控数据
        List<String> qcIds = sampleList.stream().map(DtoSample::getQcId).collect(Collectors.toList());
        List<DtoQualityControl> qualityControls = StringUtil.isNotEmpty(qcIds) ? qualityControlRepository.findByIds(qcIds) : new ArrayList<>();
        //设置所有样品的质控类型
        this.setSamplesQcType(sampleList, qualityControls);
        //移除样品Id为null的
        sampleIds.removeIf(String::isEmpty);
        //获取所有的送样单数据
        List<String> receiveIds = sampleList.stream().map(DtoSample::getReceiveId).distinct().collect(Collectors.toList());
        //查询所有的采样人数据
        List<DtoSamplingPersonConfig> samplingPersons = StringUtil.isNotEmpty(receiveIds) ? samplingPersonConfigRepository.findByObjectIdIn(receiveIds) : new ArrayList<>();
        resultDataMap.put(EnumLIM.EnumNTStatisticDataType.样品编号.getCode(), sampleIds);
        resultDataMap.put(EnumLIM.EnumNTStatisticDataType.样品数据.getCode(), sampleList);
        resultDataMap.put(EnumLIM.EnumNTStatisticDataType.质控数据.getCode(), qualityControls);
        resultDataMap.put(EnumLIM.EnumNTStatisticDataType.采样人数据.getCode(), samplingPersons);
    }

    /**
     * 处理参数相关数据
     *
     * @param resultDataMap 数据字段
     */
    private void findParamsDataListInMap(Map<String, Object> resultDataMap) {
        List<String> sampleIds = (List<String>) resultDataMap.get(EnumLIM.EnumNTStatisticDataType.样品编号.getCode());
        List<DtoParamsData> paramsDataList = StringUtil.isNotEmpty(sampleIds) ? paramsDataRepository.findByObjectIdIn(sampleIds) : new ArrayList<>();
        resultDataMap.put(EnumLIM.EnumNTStatisticDataType.参数数据.getCode(), paramsDataList);
    }

    /**
     * 处理点位相关数据
     *
     * @param resultDataMap 数据字段
     */
    private void findFolderDataInMap(Map<String, Object> resultDataMap) {
        List<DtoSample> sampleList = (List<DtoSample>) resultDataMap.get(EnumLIM.EnumNTStatisticDataType.样品数据.getCode());
        //获取样品下的点位
        List<String> sampleFolderIds = sampleList.stream().map(DtoSample::getSampleFolderId).collect(Collectors.toList());
        resultDataMap.put(EnumLIM.EnumNTStatisticDataType.点位编号.getCode(), sampleFolderIds);
        List<DtoSampleFolder> sampleFolders = StringUtil.isNotEmpty(sampleFolderIds) ? sampleFolderRepository.findAll(sampleFolderIds) : new ArrayList<>();
        resultDataMap.put(EnumLIM.EnumNTStatisticDataType.点位数据.getCode(), sampleFolders);
        //获取环境质量污染源点位
        List<String> fixedPointIds = sampleFolders.stream().map(DtoSampleFolder::getFixedPointId).collect(Collectors.toList());
        fixedPointIds.removeIf(String::isEmpty);
        resultDataMap.put(EnumLIM.EnumNTStatisticDataType.例行点位编号.getCode(), fixedPointIds);
        List<DtoFixedpoint> fixedPoints = fixedpointRepository.findByIdIn(fixedPointIds);
        resultDataMap.put(EnumLIM.EnumNTStatisticDataType.例行点位数据.getCode(), fixedPoints);
        //获取所有的测站数据
        List<String> stationIds = fixedPoints.stream().map(DtoFixedpoint::getStationId).collect(Collectors.toList());
        resultDataMap.put(EnumLIM.EnumNTStatisticDataType.测站编号.getCode(), stationIds);
        List<DtoStation> stationList = StringUtil.isNotEmpty(stationIds) ?
                stationRepository.findAll(stationIds) : new ArrayList<>();
        resultDataMap.put(EnumLIM.EnumNTStatisticDataType.测站数据.getCode(), stationList);
        //根据项目id获取所有监测计划
        List<DtoProperty2Point> property2PointsByPoint = StringUtil.isNotEmpty(fixedPointIds) ?
                property2PointRepository.findByFixedPointIdIn(fixedPointIds) : new ArrayList<>();
        resultDataMap.put(EnumLIM.EnumNTStatisticDataType.监测计划关联点位.getCode(), property2PointsByPoint);
        List<String> propertyIds = property2PointsByPoint.stream()
                .map(DtoProperty2Point::getPropertyId)
                .collect(Collectors.toList());
        resultDataMap.put(EnumLIM.EnumNTStatisticDataType.监测计划编号.getCode(), propertyIds);
        List<DtoFixedPointProperty> properties = StringUtil.isNotEmpty(propertyIds) ?
                fixedPointPropertyRepository.findByIdIn(propertyIds) : new ArrayList<>();
        resultDataMap.put(EnumLIM.EnumNTStatisticDataType.监测计划数据.getCode(), properties);
        //获取监测计划关联的项目
        List<DtoProject2FixedProperty> project2FixedProperties = StringUtil.isNotEmpty(propertyIds) ?
                project2FixedPropertyRepository.findByFixedPropertyIdIn(propertyIds) : new ArrayList<>();
        resultDataMap.put(EnumLIM.EnumNTStatisticDataType.监测计划关联项目.getCode(), project2FixedProperties);
        // 点位实际签到数据
        List<DtoFolderSign> folderSignList = StringUtil.isNotEmpty(sampleFolderIds) ?
                folderSignRepository.findBySampleFolderIdIn(sampleFolderIds) : new ArrayList<>();
        resultDataMap.put(EnumLIM.EnumNTStatisticDataType.点位签到数据.getCode(), folderSignList);
    }

    /**
     * 处理评价标准数据
     *
     * @param resultDataMap 数据字典
     */
    private void findEvaluationRecordDataInMap(Map<String, Object> resultDataMap) {
        List<String> sampleFolderIds = (List<String>) resultDataMap.get(EnumLIM.EnumNTStatisticDataType.点位编号.getCode());
        //根据点位id获取点位下的评价标准记录
        List<DtoEvaluationRecord> evaluationRecords = evaluationRecordRepository.findByObjectIdIn(sampleFolderIds);
        resultDataMap.put(EnumLIM.EnumNTStatisticDataType.评价标准记录数据.getCode(), evaluationRecords);
        List<String> evaluationIds = evaluationRecords.stream().map(DtoEvaluationRecord::getEvaluationId).collect(Collectors.toList());
        //获取评价等级数据
        List<DtoEvaluationLevel> evaluationLevelList = StringUtil.isNotEmpty(evaluationIds) ? evaluationLevelRepository.findByEvaluationIdIn(evaluationIds) : new ArrayList<>();
        resultDataMap.put(EnumLIM.EnumNTStatisticDataType.评价标准等级数据.getCode(), evaluationLevelList);
        //获取评价限值的数据
        List<String> evaLevelIds = evaluationLevelList.stream().map(DtoEvaluationLevel::getId).collect(Collectors.toList());
        List<DtoEvaluationValue> evaluationValueList = StringUtil.isNotEmpty(evaLevelIds) ? evaluationValueRepository.findByLevelIdIn(evaLevelIds) : new ArrayList<>();
        resultDataMap.put(EnumLIM.EnumNTStatisticDataType.评价标准限值数据.getCode(), evaluationValueList);
    }

    /**
     * 处理分析数据
     *
     * @param resultDataMap 数据字典
     */
    private void findAnaDataInMap(Map<String, Object> resultDataMap, List<Map<String, Object>> analyseDataMapList) {
        List<String> sampleIds = (List<String>) resultDataMap.get(EnumLIM.EnumNTStatisticDataType.样品编号.getCode());
        //获取样品下的所有测试项目数据
        List<DtoAnalyseData> analyseDataList = new ArrayList<>();
        List<Future<List<DtoAnalyseData>>> analyseDataResultList = new ArrayList<>();
        List<String> list = null;
        final int batchSize = 30;
        for (String sampleId : sampleIds) {
            if (list == null) {
                list = new ArrayList<>();
            }
            if (list.size() < batchSize) {
                list.add(sampleId);
            } else if (list.size() == batchSize) {
                //多线程处理排序
                analyseDataResultList.add(analyseDataFutureService.getListBySampleIdIn(list));
                list = new ArrayList<>();
                list.add(sampleId);
            }
        }
        //如果存在最后一批样，需要单独去排序处理
        if (StringUtil.isNotEmpty(list)) {
            analyseDataResultList.add(analyseDataFutureService.getListBySampleIdIn(list));
        }
        //处理多线程处理的结果
        try {
            for (Future<List<DtoAnalyseData>> analyseDataResult : analyseDataResultList) {
                while (true) {
                    if (analyseDataResult.isDone() && !analyseDataResult.isCancelled()) {
                        analyseDataList.addAll(analyseDataResult.get());
                        break;
                    } else {
                        //防止CPU高速轮询被耗空
                        Thread.sleep(1);
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("......多线程处理分析数据出错......");
        }
        resultDataMap.put(EnumLIM.EnumNTStatisticDataType.分析数据.getCode(), analyseDataList);
        if (analyseDataList.size() > 0) {
            //根据当前的所有数据获取样品id
            List<String> detailDataIds = analyseDataMapList.stream().map(p -> p.get("id").toString()).distinct().collect(Collectors.toList());
            //根据样品id获取所有测试项目数据
            List<DtoDetailAnalyseData> detailAnalyseDataList = detailDataService.getDetailAnalyseDataByIds(detailDataIds);
            resultDataMap.put(EnumLIM.EnumNTStatisticDataType.分析详细数据.getCode(), detailAnalyseDataList);
        }
    }


    /**
     * 处理项目数据
     *
     * @param resultDataMap 数据字典
     */
    private void findProjectDataInMap(Map<String, Object> resultDataMap) {
        List<DtoSample> sampleList = (List<DtoSample>) resultDataMap.get(EnumLIM.EnumNTStatisticDataType.样品数据.getCode());
        List<String> projectIds = StringUtil.isNotEmpty(sampleList) ? sampleList.stream().map(DtoSample::getProjectId).collect(Collectors.toList()) : new ArrayList<>();
        resultDataMap.put(EnumLIM.EnumNTStatisticDataType.项目编号.getCode(), projectIds);
        //获取所有的项目
        List<DtoProject> projectList = StringUtil.isNotEmpty(projectIds) ? projectRepository.findAll(projectIds) : new ArrayList<>();
        resultDataMap.put(EnumLIM.EnumNTStatisticDataType.项目数据.getCode(), projectList);
        //获取所有的项目类型id
        Set<String> projectTypeIds = projectList.stream().map(DtoProject::getProjectTypeId).collect(Collectors.toSet());
        resultDataMap.put(EnumLIM.EnumNTStatisticDataType.项目类型编号.getCode(), projectTypeIds);
        //获取所有的项目类型
        List<DtoProjectType> projectTypes = StringUtil.isNotEmpty(projectTypeIds) ? projectTypeRepository.findAll(projectTypeIds) : new ArrayList<>();
        resultDataMap.put(EnumLIM.EnumNTStatisticDataType.项目类型数据.getCode(), projectTypes);
    }

    /**
     * 设置样品的质控类型与质控等级
     *
     * @param samples         样品数据
     * @param qualityControls 质控数据
     */
    private void setSamplesQcType(List<DtoSample> samples, List<DtoQualityControl> qualityControls) {
        Map<String, List<DtoQualityControl>> qcMap = qualityControls.stream().collect(Collectors.groupingBy(DtoQualityControl::getId));
        for (DtoSample sample : samples) {
            List<DtoQualityControl> qcOfSample = qcMap.get(sample.getQcId());
            if (StringUtil.isNotEmpty(qcOfSample)) {
                Optional<DtoQualityControl> qcListOfSample = qcOfSample.stream().findFirst();
                qcListOfSample.ifPresent(p -> {
                    sample.setQcType(p.getQcType());
                    sample.setQcGrade(p.getQcGrade());
                });
            }
        }
    }

    /**
     * 获取比对数据
     *
     * @param baseCriteria  查询条件实体
     * @param resultDataMap 数据字典
     */
    private void findSampleJudgeListInMap(BaseCriteria baseCriteria, Map<String, Object> resultDataMap) {
        List<DtoSampleJudgeData> dataList;
        if (baseCriteria instanceof StatisticCriteria) {
            List<DtoSample> sampleList = (List<DtoSample>) resultDataMap.get(EnumLIM.EnumNTStatisticDataType.样品数据.getCode());
            List<DtoSampleFolder> sampleFolders = (List<DtoSampleFolder>) resultDataMap.get(EnumLIM.EnumNTStatisticDataType.点位数据.getCode());
            dataList = getJudgeData(sampleList, sampleFolders);
        } else {
            // 过滤不参与评价的数据
            SampleJudgeDataCriteria dataCriteria = (SampleJudgeDataCriteria) baseCriteria;
            dataCriteria.setIsFilterNotEvaluate(Boolean.TRUE);
            dataList = sampleJudgeDataService.getData(baseCriteria);
        }
        resultDataMap.put(EnumLIM.EnumNTStatisticDataType.比对数据.getCode(), dataList);
        Map<String, Object> map = new HashMap<>();
        if (baseCriteria instanceof SampleJudgeDataCriteria) {
            SampleJudgeDataCriteria sampleJudgeDataCriteria = (SampleJudgeDataCriteria) baseCriteria;
            if (EnumBase.EnumCheckType.废水比对.getValue().equals(sampleJudgeDataCriteria.getCheckType())) {
                map.put("headName", "废水比对监测结果统计表");
                map.put("totalPass", dataList.stream().filter(d -> "是".equals(d.getResultEvaluate())).count());
            } else {
                map.put("headName", "废气比对监测结果统计表");
                map.put("totalPass", dataList.stream().filter(d -> "是".equals(d.getResultEvaluate())).collect(Collectors.groupingBy(d -> d.getFolderId() + "_" + d.getAnalyzeItemName())).keySet().size());
            }
            Gson gson = new Gson();
            String mapStr = gson.toJson(map);
            resultDataMap.put(EnumLIM.EnumNTStatisticDataType.比对数据类型.getCode(), Collections.singletonList(mapStr));
        }
    }

    /**
     * 获取比对评价数据
     *
     * @param sampleList    样品数据
     * @param sampleFolders 点位数据
     * @return 比对评价数据
     */
    private List<DtoSampleJudgeData> getJudgeData(List<DtoSample> sampleList, List<DtoSampleFolder> sampleFolders) {
        List<String> receiveIds = sampleList.stream().map(DtoSample::getReceiveId).filter(r -> !UUIDHelper.GUID_EMPTY.equals(r)).distinct().collect(Collectors.toList());
        List<DtoSample> bdSamples = StringUtil.isNotEmpty(receiveIds) ? sampleRepository.findByReceiveIdIn(receiveIds).stream().filter(s -> EnumPRO.EnumSampleCategory.比对评价样.getValue().equals(s.getSampleCategory())).collect(Collectors.toList()) : new ArrayList<>();
        sampleList.addAll(bdSamples);
        SampleJudgeDataCriteria sampleJudgeDataCriteria = new SampleJudgeDataCriteria();
        Map<String, DtoSample> sampleMap = sampleList.stream().collect(Collectors.toMap(DtoSample::getId, dto -> dto, (s1, s2) -> s1));
        List<Integer> types = Arrays.asList(EnumPRO.EnumSampleCategory.原样.getValue(), EnumPRO.EnumSampleCategory.比对评价样.getValue());
        List<String> sampleIds = sampleList.stream().filter(p -> types.contains(p.getSampleCategory())).map(DtoSample::getId).collect(Collectors.toList());
        //获取点位下的评价结果
        List<String> folderIds = sampleList.stream().map(DtoSample::getSampleFolderId).filter(sampleFolderId -> !UUIDHelper.GUID_EMPTY.equals(sampleFolderId)).distinct().collect(Collectors.toList());
        List<DtoSampleFolderEvaluate> sampleFolderEvaluates = sampleFolderEvaluateRepository.findBySampleFolderIdIn(folderIds);
        Map<String, DtoSampleFolder> folderMap = sampleFolders.stream().collect(Collectors.toMap(DtoSampleFolder::getId, dto -> dto));
        //获取评价数据
        sampleJudgeDataCriteria.setSampleIds(sampleIds);
        PageBean<DtoSampleJudgeData> pb = new PageBean<>();
        pb.setPageNo(1);
        pb.setRowsPerPage(Integer.MAX_VALUE);
        sampleJudgeDataService.findByPage(pb, sampleJudgeDataCriteria);
        List<DtoSampleJudgeData> sampleJudgeDataList = pb.getData();
        sampleJudgeDataList.forEach(data -> {
            data.setResultEvaluate(data.getPass());
            DtoSample sample = sampleMap.get(data.getSampleId());
            if (sample != null) {
                DtoSampleFolder sampleFolder = folderMap.get(sample.getSampleFolderId());
                if (sampleFolder != null) {
                    data.setFolderId(sampleFolder.getId());
                    data.setFolderName(sampleFolder.getWatchSpot());
                    Optional<DtoSampleFolderEvaluate> sampleFolderEvaluate = sampleFolderEvaluates.stream()
                            .filter(s -> s.getSampleFolderId().equals(sampleFolder.getId()) && data.getTestId().equals(s.getTestId()))
                            .findFirst();
                    if (sampleFolderEvaluate.isPresent()) {
                        data.setFolderPass(sampleFolderEvaluate.get().getFolderPass());
                        data.setRemark(sampleFolderEvaluate.get().getRemark());
                        data.setResultEvaluate(data.getPass());
                        if (EnumBase.EnumCheckType.废气比对.getValue().equals(data.getCheckType())) {
                            data.setResultEvaluate(sampleFolderEvaluate.get().getResultEvaluate());
                            data.setQcRateValue(sampleFolderEvaluate.get().getQcRateValue());
                        }
                    }
                } else {
                    data.setFolderId("");
                    data.setFolderName("");
                }
            }
        });
        sampleJudgeDataService.fillingTransientFields(sampleJudgeDataList);
        return sampleJudgeDataList;
    }


    @Autowired
    public void setSampleRepository(SampleRepository sampleRepository) {
        this.sampleRepository = sampleRepository;
    }

    @Autowired
    public void setQualityControlRepository(QualityControlRepository qualityControlRepository) {
        this.qualityControlRepository = qualityControlRepository;
    }

    @Autowired
    public void setParamsDataRepository(ParamsDataRepository paramsDataRepository) {
        this.paramsDataRepository = paramsDataRepository;
    }

    @Autowired
    public void setSampleFolderRepository(SampleFolderRepository sampleFolderRepository) {
        this.sampleFolderRepository = sampleFolderRepository;
    }

    @Autowired
    public void setFixedpointRepository(FixedpointRepository fixedpointRepository) {
        this.fixedpointRepository = fixedpointRepository;
    }

    @Autowired
    public void setStationRepository(StationRepository stationRepository) {
        this.stationRepository = stationRepository;
    }

    @Autowired
    public void setProperty2PointRepository(Property2PointRepository property2PointRepository) {
        this.property2PointRepository = property2PointRepository;
    }

    @Autowired
    public void setFixedPointPropertyRepository(FixedPointPropertyRepository fixedPointPropertyRepository) {
        this.fixedPointPropertyRepository = fixedPointPropertyRepository;
    }

    @Autowired
    public void setProject2FixedPropertyRepository(Project2FixedPropertyRepository project2FixedPropertyRepository) {
        this.project2FixedPropertyRepository = project2FixedPropertyRepository;
    }

    @Autowired
    public void setEvaluationRecordRepository(EvaluationRecordRepository evaluationRecordRepository) {
        this.evaluationRecordRepository = evaluationRecordRepository;
    }

    @Autowired
    public void setEvaluationLevelRepository(EvaluationLevelRepository evaluationLevelRepository) {
        this.evaluationLevelRepository = evaluationLevelRepository;
    }

    @Autowired
    public void setEvaluationValueRepository(EvaluationValueRepository evaluationValueRepository) {
        this.evaluationValueRepository = evaluationValueRepository;
    }

    @Autowired
    public void setProjectRepository(ProjectRepository projectRepository) {
        this.projectRepository = projectRepository;
    }

    @Autowired
    public void setProjectTypeRepository(ProjectTypeRepository projectTypeRepository) {
        this.projectTypeRepository = projectTypeRepository;
    }

    @Autowired
    public void setSamplingPersonConfigRepository(SamplingPersonConfigRepository samplingPersonConfigRepository) {
        this.samplingPersonConfigRepository = samplingPersonConfigRepository;
    }

    @Autowired
    public void setAnalyseDataRepository(AnalyseDataRepository analyseDataRepository) {
        this.analyseDataRepository = analyseDataRepository;
    }

    @Autowired
    public void setDetailDataService(DetailDataService detailDataService) {
        this.detailDataService = detailDataService;
    }

    @Autowired
    public void setEnvironmentStatisticsService(EnvironmentStatisticsService environmentStatisticsService) {
        this.environmentStatisticsService = environmentStatisticsService;
    }

    @Autowired
    public void setOrgService(IOrgService orgService) {
        this.orgService = orgService;
    }

    @Autowired
    public void setEnterpriseService(EnterpriseService enterpriseService) {
        this.enterpriseService = enterpriseService;
    }

    @Autowired
    public void setSampleJudgeDataService(SampleJudgeDataService sampleJudgeDataService) {
        this.sampleJudgeDataService = sampleJudgeDataService;
    }

    @Autowired
    public void setSampleFolderEvaluateRepository(SampleFolderEvaluateRepository sampleFolderEvaluateRepository) {
        this.sampleFolderEvaluateRepository = sampleFolderEvaluateRepository;
    }

    @Autowired
    public void setAnalyseDataFutureService(AnalyseDataFutureService analyseDataFutureService) {
        this.analyseDataFutureService = analyseDataFutureService;
    }

    @Autowired
    public void setFolderSignRepository(FolderSignRepository folderSignRepository) {
        this.folderSignRepository = folderSignRepository;
    }
}
