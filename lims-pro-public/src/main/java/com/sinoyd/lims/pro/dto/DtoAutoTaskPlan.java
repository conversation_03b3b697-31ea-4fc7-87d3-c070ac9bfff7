package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.AutoTaskPlan;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * DtoAutoTaskPlan实体
 * <AUTHOR>
 * @version V1.0.0 2023/08/08
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_AutoTaskPlan")
@Data
@DynamicInsert
public class DtoAutoTaskPlan extends AutoTaskPlan {

    /**
     * 监测计划名称
     */
    @Transient
    private String propertyNames;

    /**
     * 监测计划id集合
     */
    @Transient
    private List<String> propertyIds;

}
