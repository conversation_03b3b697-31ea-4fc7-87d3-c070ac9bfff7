package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoSerialNumberConfig;

import java.util.Collection;
import java.util.List;


/**
 * SerialNumberConfig操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
public interface SerialNumberConfigService extends IBaseJpaService<DtoSerialNumberConfig, String> {

    /**
     * 根据序列号类型获取相应数据
     *
     * @param serialNumberType 序列号值
     * @return 返回相应数据
     */
    DtoSerialNumberConfig findBySerialNumberType(String serialNumberType);

    /**
     * 根据序列号类型获取相应数据
     *
     * @param serialNumberTypeList 序列号类型集合
     * @return 返回相应数据
     */
    List<DtoSerialNumberConfig> findBySerialNumberType(Collection<String> serialNumberTypeList);

    /**
     * 根据序列号类型和参数2获取相应数据,该API截止2023-07-10产品未使用到，南通一体化项目使用
     *
     * @param serialNumberType 序列号值
     * @param para2            参数2的值
     * @return 返回相应数据
     */
    DtoSerialNumberConfig findBySerialNumberTypeAndPara2(String serialNumberType, String para2);
}