package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.DtoSubmitRecord;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import io.swagger.models.auth.In;

import java.util.List;


/**
 * SubmitRecord数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface SubmitRecordRepository extends IBaseJpaPhysicalDeleteRepository<DtoSubmitRecord, String> {
    /**
     * 根据关联类型及关联id返回提交记录
     *
     * @param objectType 关联类型
     * @param objectId   关联id
     * @return 提交记录
     */
    List<DtoSubmitRecord> findByObjectTypeAndObjectId(Integer objectType, String objectId);

    /**
     * 根据关联id返回提交记录
     *
     * @param objectIds 关联id
     * @return 提交记录
     */
    List<DtoSubmitRecord> findByObjectIdIn(List<String> objectIds);
}