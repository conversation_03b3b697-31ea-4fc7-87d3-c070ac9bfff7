ALTER TABLE TB_BASE_Consumable ADD COLUMN uncertainType int NOT NULL DEFAULT 10;
comment on column TB_BASE_Consumable.uncertainType is '不确定度类型,关联EnumUncertainType';
ALTER TABLE tb_pro_qualitycontrol ADD COLUMN uncertainType int NOT NULL DEFAULT 10;
comment on column tb_pro_qualitycontrol.uncertainType is '不确定度类型,关联EnumUncertainType';
ALTER TABLE tb_pro_qualitycontrolevaluate  ADD COLUMN uncertainType int NOT NULL DEFAULT 10;
comment on column tb_pro_qualitycontrolevaluate.uncertainType is '不确定度类型,关联EnumUncertainType';
ALTER TABLE tb_pro_qualitymanage ADD COLUMN uncertainType int NOT NULL DEFAULT 10;
comment on column tb_pro_qualitymanage.uncertainType is '不确定度类型,关联EnumUncertainType';
ALTER TABLE tb_base_qualitycontrollimit ADD COLUMN uncertainType int NOT NULL DEFAULT 10;
comment on column tb_base_qualitycontrollimit.uncertainType is '不确定度类型,关联EnumUncertainType';

ALTER TABLE tb_base_sampletype ADD COLUMN isOpenGroupTag bit not null default 0;
comment on column tb_base_sampletype.isOpenGroupTag is '是否启用样品编号标识（0不启用 1启用）';
ALTER TABLE tb_lim_sampletypegroup ADD COLUMN sampleCodeTag varchar(50);
comment on column tb_lim_sampletypegroup.sampleCodeTag is '样品编号标识';

CREATE INDEX UIX_tb_lim_sampletypegroup2test ON tb_lim_sampletypegroup2test ( sampleTypeGroupId, testId );