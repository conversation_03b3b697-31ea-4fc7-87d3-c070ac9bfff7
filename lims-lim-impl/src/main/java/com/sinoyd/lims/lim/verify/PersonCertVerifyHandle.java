package com.sinoyd.lims.lim.verify;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.customer.DtoImportPersonCert;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoPersonCert;
import com.sinoyd.lims.lim.dto.lims.DtoStandardMethod;
import com.sinoyd.lims.lim.dto.lims.DtoStandardMethodDetail;
import com.sinoyd.lims.lim.utils.ImportUtils;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 人员上岗证导入校验处理类
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/13
 * @since V100R001
 */
@Data
public class PersonCertVerifyHandle implements IExcelVerifyHandler<DtoImportPersonCert> {

    /**
     * 工具类
     */
    private final ImportUtils importUtils = new ImportUtils();

    private final List<DtoPerson> persons;
    private final Map<String, DtoStandardMethod> methodMap;
    private final Map<String, List<DtoStandardMethodDetail>> methodDetailMap;
    private final List<DtoPersonCert> personCertList;

    public PersonCertVerifyHandle(List<DtoPerson> persons,
                                  Map<String, DtoStandardMethod> methodMap,
                                  Map<String, List<DtoStandardMethodDetail>> methodDetailMap,
                                  List<DtoPersonCert> personCertList) {
        this.persons = persons;
        this.methodMap = methodMap;
        this.methodDetailMap = methodDetailMap;
        this.personCertList = personCertList;
    }

    @Override
    public ExcelVerifyHandlerResult verifyHandler(DtoImportPersonCert dto) {
        // 导入数据处理
        try {
            // 跳过空行
            if (importUtils.checkObjectIsNull(dto)) {
                return new ExcelVerifyHandlerResult(true);
            }
            // 前后去空格
            importUtils.strToTrim(dto);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }

        // 校验结果
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        // 校验错误信息
        StringBuilder failStr = new StringBuilder("第" + dto.getRowNum() + "行数据校验错误");

        // 必填项校验
        importUtils.checkIsNull(result, dto.getPersonName(), "姓名", failStr);
        importUtils.checkIsNull(result, dto.getCertCode(), "证书编号", failStr);
        importUtils.checkIsNull(result, dto.getSampleType(), "监测类别", failStr);
        importUtils.checkIsNull(result, dto.getRedAnalyzeItemName(), "项目名称", failStr);
//        importUtils.checkIsNull(result, dto.getRedCountryStandard(), "标准编号", failStr);
//        importUtils.checkIsNull(result, dto.getRedAnalyzeMethodName(), "标准名称", failStr);
        importUtils.checkIsNull(result, dto.getMethodId(), "方法id", failStr);
        importUtils.checkIsNull(result, dto.getAchieveDate(), "有效开始日期", failStr);
        importUtils.checkIsNull(result, dto.getCertEffectiveTime(), "有效结束日期", failStr);

        // 校验人员是否存在
        if (StringUtil.isNotEmpty(dto.getPersonName())) {
            boolean personExists = persons.stream()
                    .anyMatch(person -> dto.getPersonName().equals(person.getCName()));
            if (!personExists) {
                result.setSuccess(false);
                failStr.append("；人员不存在");
            }
        }

        // 校验方法ID是否存在且有详情数据
        if (StringUtil.isNotEmpty(dto.getMethodId())) {
            checkMethodIdValid(result, dto.getMethodId(), failStr);
        }

        // 日期格式校验
        importUtils.checkDateTwo(result, dto.getAchieveDate(), "有效开始日期", failStr);
        importUtils.checkDateTwo(result, dto.getCertEffectiveTime(), "有效结束日期", failStr);

        // 校验有效结束日期不能小于有效开始日期
        checkDateRange(result, dto.getAchieveDate(), dto.getCertEffectiveTime(), failStr);
        // 校验证书编号是否存在
        checkCertCode(result, dto, failStr);
        // 处理校验字符串
        String failString = failStr.toString().replaceFirst("；", ":");
        result.setMsg(failString);
        return result;
    }

    /**
     * 校验方法ID是否存在且有详情数据
     *
     * @param result   校验结果
     * @param methodId 方法ID
     * @param failStr  错误信息
     */
    private void checkMethodIdValid(ExcelVerifyHandlerResult result, String methodId, StringBuilder failStr) {
        DtoStandardMethod standardMethod = methodMap.get(methodId);
        if (standardMethod == null) {
            result.setSuccess(false);
            failStr.append("；方法ID在标准方法中不存在");
            return;
        }

        List<DtoStandardMethodDetail> details = methodDetailMap.get(standardMethod.getId());
        if (details == null || details.isEmpty()) {
            result.setSuccess(false);
            failStr.append("；方法ID对应的标准方法没有详情数据");
        }
    }

    /**
     * 校验日期范围
     *
     * @param result    校验结果
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param failStr   错误信息
     */
    private void checkDateRange(ExcelVerifyHandlerResult result, String startDate, String endDate, StringBuilder failStr) {
        if (StringUtil.isNotEmpty(startDate) && StringUtil.isNotEmpty(endDate)) {
            try {
                Date achieveDate = importUtils.stringToDateAllFormat(startDate);
                Date effectiveTime = importUtils.stringToDateAllFormat(endDate);

                if (effectiveTime.before(achieveDate)) {
                    result.setSuccess(false);
                    failStr.append("；有效结束日期不能小于有效开始日期");
                }
            } catch (Exception e) {
                result.setSuccess(false);
                failStr.append("；日期格式转换错误");
            }
        }
    }

    /**
     * 校验证书编号
     *
     * @param result  校验结果
     * @param dto     导入数据实体
     * @param failStr 错误信息
     */
    private void checkCertCode(ExcelVerifyHandlerResult result, DtoImportPersonCert dto, StringBuilder failStr) {
        List<DtoPersonCert> personCerts = personCertList.stream().filter(p -> p.getCertCode().equals(dto.getCertCode())).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(personCerts)) {
            result.setSuccess(false);
            failStr.append("；证书编号已存在");
        }

    }
}