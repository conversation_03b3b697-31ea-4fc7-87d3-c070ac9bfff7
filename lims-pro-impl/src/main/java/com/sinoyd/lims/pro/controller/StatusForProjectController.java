package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.StatusForProjectService;
import com.sinoyd.lims.pro.criteria.StatusForProjectCriteria;
import com.sinoyd.lims.pro.dto.DtoStatusForProject;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * StatusForProject服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/8
 * @since V100R001
 */
 @Api(tags = "示例: StatusForProject服务")
 @RestController
 @RequestMapping("api/pro/statusForProject")
 public class StatusForProjectController extends BaseJpaController<DtoStatusForProject, String,StatusForProjectService> {


    /**
     * 分页动态条件查询StatusForProject
     * @param statusForProjectCriteria 条件参数
     * @return RestResponse<List<StatusForProject>>
     */
     @ApiOperation(value = "分页动态条件查询StatusForProject", notes = "分页动态条件查询StatusForProject")
     @GetMapping
     public RestResponse<List<DtoStatusForProject>> findByPage(StatusForProjectCriteria statusForProjectCriteria) {
         PageBean<DtoStatusForProject> pageBean = super.getPageBean();
         RestResponse<List<DtoStatusForProject>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, statusForProjectCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询StatusForProject
     * @param id 主键id
     * @return RestResponse<DtoStatusForProject>
     */
     @ApiOperation(value = "按主键查询StatusForProject", notes = "按主键查询StatusForProject")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoStatusForProject> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoStatusForProject> restResponse = new RestResponse<>();
         DtoStatusForProject statusForProject = service.findOne(id);
         restResponse.setData(statusForProject);
         restResponse.setRestStatus(StringUtil.isNull(statusForProject) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增StatusForProject
     * @param statusForProject 实体列表
     * @return RestResponse<DtoStatusForProject>
     */
     @ApiOperation(value = "新增StatusForProject", notes = "新增StatusForProject")
     @PostMapping
     public RestResponse<DtoStatusForProject> create(@RequestBody DtoStatusForProject statusForProject) {
         RestResponse<DtoStatusForProject> restResponse = new RestResponse<>();
         restResponse.setData(service.save(statusForProject));
         return restResponse;
      }

     /**
     * 新增StatusForProject
     * @param statusForProject 实体列表
     * @return RestResponse<DtoStatusForProject>
     */
     @ApiOperation(value = "修改StatusForProject", notes = "修改StatusForProject")
     @PutMapping
     public RestResponse<DtoStatusForProject> update(@RequestBody DtoStatusForProject statusForProject) {
         RestResponse<DtoStatusForProject> restResponse = new RestResponse<>();
         restResponse.setData(service.update(statusForProject));
         return restResponse;
      }

    /**
     * "根据id批量删除StatusForProject
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除StatusForProject", notes = "根据id批量删除StatusForProject")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }