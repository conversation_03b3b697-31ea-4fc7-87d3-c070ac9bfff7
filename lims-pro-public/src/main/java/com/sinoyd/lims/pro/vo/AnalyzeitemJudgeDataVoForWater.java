package com.sinoyd.lims.pro.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AnalyzeitemJudgeDataVoForWater implements Serializable {

    @Excel(name = "分析项目",needMerge = true,orderNum = "30",width = 23)
    private String analyzeItemName;

    @ExcelCollection(name = "", orderNum = "35")
    private List<ComparetypeJudgeDataVoForWater> dataVos;

}
