ALTER TABLE TB_LIM_ReportConfig
    ADD COLUMN versionNum varchar(100) NULL COMMENT '版本号';



ALTER TABLE TB_LIM_ReportConfig
    ADD COLUMN controlNum varchar(100) NULL COMMENT '受控编号';


-- 系统信息管理配置表
DROP TABLE IF EXISTS TB_BASE_SystemConfig;
CREATE TABLE TB_BASE_SystemConfig
(
    id                 varchar(50)                                                NOT NULL COMMENT '主键',
    fullName           varchar(100)                                               NOT NULL COMMENT '系统名称全写',
    shortName          varchar(50) NULL DEFAULT NULL COMMENT '系统名称简写',
    welcomeWord        varchar(255) NULL DEFAULT NULL COMMENT '欢迎登陆语',
    companyName        varchar(255) NULL DEFAULT NULL COMMENT '企业名称',
    companyAddress     varchar(1000) NULL DEFAULT NULL COMMENT '企业地址',
    companyPostCode    varchar(20) NULL DEFAULT NULL COMMENT '企业邮编',
    companyPhone       varchar(50) NULL DEFAULT NULL COMMENT '企业联系方式',
    companyEnglishName varchar(255) NULL DEFAULT NULL COMMENT '企业英文名称',
    orgId              varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate         datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate         datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间',
    PRIMARY KEY (`id`)
) COMMENT = '系统信息管理配置'