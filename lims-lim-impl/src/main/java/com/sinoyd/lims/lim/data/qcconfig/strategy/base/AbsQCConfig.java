package com.sinoyd.lims.lim.data.qcconfig.strategy.base;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.dto.rcc.DtoQualityLimitDisposition;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsTestFormula;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.ParamsTestFormulaRepository;
import com.sinoyd.lims.lim.repository.rcc.QualityControlLimitRepository;
import com.sinoyd.lims.lim.repository.rcc.QualityLimitDispositionRepository;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Transient;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 质控限值基类
 *
 * <AUTHOR>
 * @version V5.2.0 2022/6/14
 */
public abstract class AbsQCConfig {

    private final QualityControlLimitRepository qualityControlLimitRepository;

    private final ParamsTestFormulaRepository paramsTestFormulaRepository;

    private final QualityLimitDispositionRepository qualityLimitDispositionRepository;

    public AbsQCConfig() {
        qualityControlLimitRepository = SpringContextAware.getBean(QualityControlLimitRepository.class);
        paramsTestFormulaRepository = SpringContextAware.getBean(ParamsTestFormulaRepository.class);
        qualityLimitDispositionRepository = SpringContextAware.getBean(QualityLimitDispositionRepository.class);
    }


    /**
     * 保存质控数据
     *
     * @param testQCRangeCopy 质控数据
     */
    @Transactional
    public DtoQualityControlLimit save(DtoQualityControlLimit testQCRangeCopy) {
        //修改数据
        DtoQualityControlLimit qcRange = qualityControlLimitRepository.findOne(testQCRangeCopy.getId());
        if (StringUtil.isNotNull(qcRange)) {
            testQCRangeCopy.setCreator(qcRange.getCreator());
            testQCRangeCopy.setCreateDate(qcRange.getCreateDate());
            testQCRangeCopy.setModifier(qcRange.getModifier());
            testQCRangeCopy.setModifyDate(qcRange.getModifyDate());
            testQCRangeCopy.setQcGrade(qcRange.getQcGrade());
            testQCRangeCopy.setSubstituteId(StringUtil.isNotEmpty(testQCRangeCopy.getSubstituteId()) ? testQCRangeCopy.getSubstituteId() : qcRange.getSubstituteId());
            testQCRangeCopy.setIsCheckItem(StringUtil.isNotNull(testQCRangeCopy.getIsCheckItem()) ? testQCRangeCopy.getIsCheckItem() : qcRange.getIsCheckItem());
        }
        if (StringUtil.isNotNull(testQCRangeCopy)) {
            if (testQCRangeCopy.getJudgingMethod().toString().equals(EnumBase.EnumJudgingMethod.小于检出限.getValue().toString())
                    || testQCRangeCopy.getJudgingMethod().toString().equals(EnumBase.EnumJudgingMethod.小于测定下限.getValue().toString())) {
                testQCRangeCopy.setAllowLimit("");
            }
            if (StringUtil.isNotNull(testQCRangeCopy.getIsCheckItem())) {
                if ("0".equals(testQCRangeCopy.getIsCheckItem().toString())) {
                    testQCRangeCopy.setRangeConfig("");
                }
            }
            if (!StringUtil.isNotEmpty(testQCRangeCopy.getFormula())
                    || !StringUtil.isNotEmpty(testQCRangeCopy.getDispositionId())
                    || UUIDHelper.GUID_EMPTY.equals(testQCRangeCopy.getDispositionId())) {
                testQCRangeCopy.setDispositionId(UUIDHelper.GUID_EMPTY);
                //设置默认公式
                Optional<DtoQualityLimitDisposition> dispositionOptional = qualityLimitDispositionRepository
                        .findByQcGradeAndQcTypeAndJudgingMethodAndIsAcquiesce(testQCRangeCopy.getQcGrade(),
                                testQCRangeCopy.getQcType(), testQCRangeCopy.getJudgingMethod(), Boolean.TRUE)
                        .stream().findFirst();
                dispositionOptional.ifPresent(p -> {
                    testQCRangeCopy.setFormula(p.getFormula());
                });
            }
            //获取传入的测试项目Id
            String testId = testQCRangeCopy.getTestId();
            if (!StringUtil.isNotEmpty(testQCRangeCopy.getSubstituteId())) {
                testQCRangeCopy.setSubstituteId(UUIDHelper.GUID_EMPTY);
            }
            //设置质控类型名称
            testQCRangeCopy.setQcTypeName(getQcTypeName());
            testQCRangeCopy.setCheckItem(getCheckItemType(testQCRangeCopy));
            if (EnumLIM.EnumCheckItemType.公式参数.getValue().equals(testQCRangeCopy.getCheckItem())) {
                //获取测试项目下所有的公式参数
                List<DtoParamsTestFormula> params = paramsTestFormulaRepository.findByObjId(testId);
                //判断填写的公式参数是否存在
                List<DtoParamsTestFormula> temps = params.stream().filter(p -> testQCRangeCopy.getCheckItemOther().equals(p.getAlias())).collect(Collectors.toList());
                if (StringUtil.isEmpty(temps) && StringUtil.isNotEmpty(params)) {
                    throw new BaseException("参数名称需要同测试项目公式保持一致，才能正确进行评价");
                }
            } else if (EnumLIM.EnumCheckItemType.出证结果.getValue().equals(testQCRangeCopy.getCheckItem()) && StringUtil.isEmpty(testQCRangeCopy.getCheckItemOther())){
                testQCRangeCopy.setCheckItemOther("");
            }
        }
        return qualityControlLimitRepository.save(testQCRangeCopy);
    }

    /**
     * 查询所有数据
     *
     * @param testId 测试项目编号
     * @return 所有质控限值数据
     */
    public List<DtoQualityControlLimit> queryData(String testId) {
        return qualityControlLimitRepository.findAll();
    }


    /**
     * 获取质控类型
     *
     * @return 质控类型
     */
    public abstract Integer getQcType();

    /**
     * 获取排序值
     *
     * @return 排序值
     */
    public abstract Integer getOrderNum();

    /**
     * 获取质控类型名称
     *
     * @return 质控类型名称
     */
    public abstract String getQcTypeName();

    public Integer getCheckItemType(DtoQualityControlLimit testQCRangeCopy) {
        Integer checkItem;
        //判断检查项范围是否为空
        if (StringUtil.isEmpty(testQCRangeCopy.getRangeConfig())) {
            //判断检查项是否为空
            if (StringUtil.isNotNull(testQCRangeCopy.getCheckItem())) {
                ///获取检查项
                checkItem = testQCRangeCopy.getCheckItem();
                //设置是否设置检查项
                if (EnumLIM.EnumQCType.加标.getValue().equals(testQCRangeCopy.getQcType())
                        || EnumLIM.EnumQCType.空白加标.getValue().equals(testQCRangeCopy.getQcType())
                        || EnumLIM.EnumQCType.平行.getValue().equals(testQCRangeCopy.getQcType())) {
                    //当是否设置检查项为否是，设置检查项为空
                    if (testQCRangeCopy.getIsCheckItem() == 0) {
                        checkItem = -1;
                    } else {
                        checkItem = testQCRangeCopy.getCheckItem();
                    }
                }
            } else {
                checkItem = -1;
            }
        } else {
            if (StringUtil.isNotNull(testQCRangeCopy.getCheckItem())) {
                checkItem = testQCRangeCopy.getCheckItem();
                if (EnumLIM.EnumQCType.加标.getValue().equals(testQCRangeCopy.getQcType())
                        || EnumLIM.EnumQCType.空白加标.getValue().equals(testQCRangeCopy.getQcType())
                        || EnumLIM.EnumQCType.平行.getValue().equals(testQCRangeCopy.getQcType())) {
                    if (testQCRangeCopy.getIsCheckItem() == 0) {
                        checkItem = -1;
                    } else if (testQCRangeCopy.getIsCheckItem() == 1) {
                        checkItem = 1;
                    } else {
                        checkItem = testQCRangeCopy.getCheckItem();
                    }
                }
            } else {
                checkItem = 1;
            }
        }
        return checkItem;
    }

    /**
     * 获取质控等级类型
     *
     * @return 质控等级类型
     */
    public Integer getQcGrade() {
        return EnumLIM.EnumQCGrade.内部质控.getValue();
    }

}
