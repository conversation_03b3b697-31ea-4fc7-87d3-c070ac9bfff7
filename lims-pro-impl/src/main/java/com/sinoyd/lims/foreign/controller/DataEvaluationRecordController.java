package com.sinoyd.lims.foreign.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.foreign.DataCurveVO;
import com.sinoyd.lims.foreign.DataEvaluationRecordVO;
import com.sinoyd.lims.foreign.service.DataCurveService;
import com.sinoyd.lims.foreign.service.DataEvaluationRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 评价明细服务
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Api(tags = "示例: dataEvaluationRecord服务")
@RestController
@RequestMapping("api/pro/foreign/dataEvaluationRecord")
public class DataEvaluationRecordController extends ExceptionHandlerController<DataEvaluationRecordService> {

    /**
     * 评价明细数据同步
     *
     * @param map 同步参数
     * @return RestResponse<List < DataAnalyseDataVO>>
     */
    @ApiOperation(value = "数据同步", notes = "数据同步")
    @PostMapping("/syncData")
    public RestResponse<List<DataEvaluationRecordVO>> getSyncData(@RequestBody Map<String, Object> map) {
        List<DataEvaluationRecordVO> curveVOList = service.getSyncData(map);
        RestResponse<List<DataEvaluationRecordVO>> response = new RestResponse();
        response.setData(curveVOList);
        return response;
    }

}
