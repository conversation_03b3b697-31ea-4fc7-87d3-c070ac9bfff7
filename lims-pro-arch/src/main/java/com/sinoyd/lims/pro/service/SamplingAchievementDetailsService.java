package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoSamplingAchievementDetails;

import javax.servlet.http.HttpServletResponse;

/**
 *  采样绩效管理明细service
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2023/03/13
 */
public interface SamplingAchievementDetailsService extends IBaseJpaService<DtoSamplingAchievementDetails, String> {

    /**
     * 导出
     * @param response 响应
     * @param criteria 查询条件
     */
    void export(HttpServletResponse response, BaseCriteria criteria);

}
