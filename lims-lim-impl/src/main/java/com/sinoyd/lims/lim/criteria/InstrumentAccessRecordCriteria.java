package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * ProjectInstrument查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2021年11月13日
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InstrumentAccessRecordCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始日期
     */
    private String dtBegin;

    /**
     * 结束日期
     */
    private String dtEnd;

    /**
     * 项目名称/编号
     */
    private String projectNameCode;

    /**
     * 仪器id
     */
    private String instrumentId;

    /**
     * 项目id(根据名称或编码查询)
     */
    private List<String> projectIds;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
//        condition.append(" and a.projectId = b.id ");
        if (StringUtil.isNotEmpty(this.dtBegin)) {
            Date startDate = DateUtil.stringToDate(this.dtBegin, DateUtil.YEAR);
            condition.append(" and a.useDate >= :startDate");
            values.put("startDate", startDate);
        }
        if (StringUtil.isNotEmpty(this.dtEnd)) {
            Date endDate = DateUtil.stringToDate(this.dtEnd, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(endDate);
            c.add(Calendar.DAY_OF_MONTH, 1);
            endDate = c.getTime();
            condition.append(" and a.useDate <= :endDate");
            values.put("endDate", endDate);
        }
        if (StringUtil.isNotEmpty(this.projectNameCode)) {
            if (StringUtil.isNotEmpty(this.projectIds)) {
                condition.append(" and (a.projectId in :projectIds or a.projectName like :projectNameCode )");
                values.put("projectIds", projectIds);
            } else {
                condition.append(" and a.projectName like :projectNameCode");
            }
            values.put("projectNameCode", "%" + projectNameCode + "%");
        }
        if (StringUtil.isNotEmpty(this.instrumentId)) {
            condition.append(" and exists (select 1 from DtoProjectInstrumentDetails cc, DtoInstrument dd " +
                    " where cc.instrumentId = dd.id and dd.isDeleted = 0 and dd.id = :instrumentId and cc.projectInstrumentId = a.id) ");
            values.put("instrumentId", instrumentId);
        }
        condition.append(" and a.isDeleted = 0 order by a.useDate desc ");
        return condition.toString();
    }
}
