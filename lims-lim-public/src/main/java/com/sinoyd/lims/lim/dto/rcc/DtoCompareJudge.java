package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.CompareJudge;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * DtoCompareJudge实体
 * <AUTHOR>
 * @version V1.0.0 2023/06/13
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_CompareJudge")
@Data
@DynamicInsert
public class DtoCompareJudge extends CompareJudge {

    /**
     * 分析项目名称
     */
    @Transient
    private String analyzeItemName;

}
