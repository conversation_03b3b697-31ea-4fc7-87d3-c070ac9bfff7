package com.sinoyd.lims.pro.strategy.strategy.submitRestrict;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.rcc.DtoItemRelation;
import com.sinoyd.lims.lim.dto.rcc.DtoItemRelationParams;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.service.AnalyseDataService;
import com.sinoyd.lims.strategy.context.CheckRelationItem;
import org.springframework.aop.interceptor.AsyncExecutionAspectSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class CheckRelationItemServiceImpl implements CheckRelationItem {

    private AnalyseDataService analyseDataService;

    @Override
    @Async(AsyncExecutionAspectSupport.DEFAULT_TASK_EXECUTOR_BEAN_NAME)
    public Future<List<String>> initCheckRelationItem(List<DtoAnalyseData> analyseDataList, List<DtoSample> sampleList,
                                                      List<DtoAnalyseData> analyseDatas, List<DtoItemRelationParams> paramsList,
                                                      List<DtoItemRelation> relationList, List<DtoItemRelationParams> itemRelationParamsList) {
        List<String> msgList = checkRelationItem(analyseDataList, sampleList, analyseDatas, paramsList, relationList, itemRelationParamsList);
        return new AsyncResult<>(msgList);
    }

    private List<String> checkRelationItem(List<DtoAnalyseData> analyseDataList, List<DtoSample> sampleList, List<DtoAnalyseData> analyseDatas,
                                           List<DtoItemRelationParams> paramsList, List<DtoItemRelation> relationList,
                                           List<DtoItemRelationParams> itemRelationParamsList) {
        List<String> msgList = new ArrayList<>();
        analyseDataList.forEach(p -> {
            String analyzeItemId = p.getAnalyseItemId();
            String sampleId = p.getSampleId();
            DtoSample sample = sampleList.stream().filter(s -> sampleId.equals(s.getId())).findFirst().orElse(new DtoSample());
            String analyzeItemName = p.getRedAnalyzeItemName();
            String testValue = p.getTestValue();
            String testValueDstr = p.getTestValueDstr();
            String limitValue = p.getExamLimitValue();
            List<DtoAnalyseData> dataList = analyseDatas.stream().filter(a -> sampleId.equals(a.getSampleId())).collect(Collectors.toList());
            List<DtoItemRelation> relations = new ArrayList<>();
            List<DtoItemRelationParams> relationParamsList = new ArrayList<>();
            if (StringUtil.isNotEmpty(testValue)) {
                Set<String> relationIdList = paramsList.stream().map(DtoItemRelationParams::getRelationId).collect(Collectors.toSet());
                relations = relationList.stream().filter(r -> relationIdList.contains(r.getId())).collect(Collectors.toList());
                relationParamsList = itemRelationParamsList.stream().filter(r -> relationIdList.contains(r.getRelationId())).collect(Collectors.toList());
            }
            String msg = analyseDataService.relationRemind(analyzeItemId, analyzeItemName, dataList, testValue,
                    testValueDstr, limitValue, Boolean.TRUE, sample.getCode(), relations, relationParamsList);
            if (StringUtil.isNotEmpty(msg)) {
                msgList.add(msg);
            }
        });
        return msgList;
    }

    @Autowired
    @Lazy
    public void setAnalyseDataService(AnalyseDataService analyseDataService) {
        this.analyseDataService = analyseDataService;
    }
}
