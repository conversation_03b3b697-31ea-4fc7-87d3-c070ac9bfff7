package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.repository.rcc.SubstituteRepository;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.customer.DtoImportQualityControlLimit;
import com.sinoyd.lims.lim.repository.rcc.QualityControlLimitRepository;
import com.sinoyd.lims.lim.service.ImportQualityControlLimitService;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.lim.verify.QualityControlLimitVerifyHandler;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 质控限制导入接口实现
 *
 * <AUTHOR>
 * @version V100R001
 * @date V1.0.0 2024/05/27
 */
@Service
public class ImportQualityControlLimitServiceImpl implements ImportQualityControlLimitService {


    private static final List<EnumBase.EnumJudgingMethod> judgingMethodList = Arrays.stream(EnumBase.EnumJudgingMethod.values()).collect(Collectors.toList());
    private QualityControlLimitVerifyHandler qualityControlLimitVerifyHandler;
    private TestService testService;
    private SampleTypeRepository sampleTypeRepository;
    private SubstituteRepository substituteRepository;
    private QualityControlLimitRepository qualityControlLimitRepository;

    /**
     * 处理导入表格
     *
     * @param file      传入的文件
     * @param objectMap
     * @param response
     * @return List<T>
     */
    @Override
    @Transactional
    public List<DtoQualityControlLimit> importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception {

        //文件格式校验
        PoiExcelUtils.verifyFileType(file);

        //更新校验容器并获取excel导入结果集
        qualityControlLimitVerifyHandler = new QualityControlLimitVerifyHandler();
        initHandleContainer(qualityControlLimitVerifyHandler);
        ExcelImportResult<DtoImportQualityControlLimit> importResult = getExcelData(file, response);
        //校验正确数据
        List<DtoImportQualityControlLimit> importList = importResult.getList();

        clearHandler();
        //跳过空行
        importList.removeIf(p -> StringUtil.isEmpty(p.getSampleTypeName()));
        if (StringUtil.isEmpty(importList)) {
            throw new BaseException("文件中无数据，请检查后导入");
        }
        // 导入数据转实体
        List<DtoQualityControlLimit> qualityControlLimits = addDataHandle(importList);
        // 清空原有配置
        clearOldLimit(qualityControlLimits);
        addData(qualityControlLimits);
        return qualityControlLimitRepository.findAll();
    }

    /**
     * 清空老的质控限制配置
     *
     * @param qualityControlLimits 质控限值集合
     */
    @Transactional
    public void clearOldLimit(List<DtoQualityControlLimit> qualityControlLimits) {
        if (StringUtil.isNotEmpty(qualityControlLimits)) {
            List<String> testIds = qualityControlLimits.stream().map(DtoQualityControlLimit::getTestId).distinct().collect(Collectors.toList());
            List<DtoQualityControlLimit> oldQcLimits = qualityControlLimitRepository.findByTestIdIn(testIds);
            qualityControlLimitRepository.delete(oldQcLimits);
        }
    }

    /**
     * 导入到数据库
     *
     * @param data 需要导入的数据
     */
    @Override
    public void addData(List<DtoQualityControlLimit> data) {
        if (StringUtil.isNotEmpty(data)) {
            qualityControlLimitRepository.save(data);
        }
    }

    /**
     * 获取文件需要导入的数据
     *
     * @param file     传入的文件
     * @param response
     * @return List
     */
    @Override
    public ExcelImportResult<DtoImportQualityControlLimit> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        //设置是否校验及校验器
        params.setNeedVerify(true);
        params.setVerifyHandler(qualityControlLimitVerifyHandler);
        ExcelImportResult<DtoImportQualityControlLimit> result = ExcelImportUtil.importExcelMore(
                file.getInputStream(),
                DtoImportQualityControlLimit.class, params);
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "质控限值导入错误信息");
            PoiExcelUtils.downLoadExcel("质控限值导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }


    private List<DtoQualityControlLimit> addDataHandle(List<DtoImportQualityControlLimit> importList) {
        List<DtoQualityControlLimit> result = new ArrayList<>();
        for (DtoImportQualityControlLimit importQcLimit : importList) {
            DtoQualityControlLimit qcLimit = new DtoQualityControlLimit();
            BeanUtils.copyProperties(importQcLimit, qcLimit);
            // 评判方式
            judgingMethodList.stream().filter(p -> p.name().equals(importQcLimit.getJudgeMethod())).findFirst().ifPresent(j -> {
                qcLimit.setJudgingMethod(j.getValue());
            });
            // 质控类型
            result.add(qcLimit);
        }
        return result;
    }

    private void initHandleContainer(QualityControlLimitVerifyHandler qualityControlLimitVerifyHandler) {
        qualityControlLimitVerifyHandler.setAllTestList(testService.findAll());
        qualityControlLimitVerifyHandler.setAllSubstituteList(substituteRepository.findAll());
        qualityControlLimitVerifyHandler.setAllSampleTypeList(sampleTypeRepository.findAll());
    }

    /**
     * 移除下次调用校验器需重新赋值的容器，避免数据混淆
     */
    private void clearHandler() {
        qualityControlLimitVerifyHandler.getAllSubstituteList().clear();
        qualityControlLimitVerifyHandler.getAllTestList().clear();
        qualityControlLimitVerifyHandler.getAllSampleTypeList().clear();
    }


    @Autowired
    public void setTestService(TestService testService) {
        this.testService = testService;
    }

    @Autowired
    public void setSubstituteRepository(SubstituteRepository substituteRepository) {
        this.substituteRepository = substituteRepository;
    }

    @Autowired
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    public void setQualityControlLimitRepository(QualityControlLimitRepository qualityControlLimitRepository) {
        this.qualityControlLimitRepository = qualityControlLimitRepository;
    }
}
