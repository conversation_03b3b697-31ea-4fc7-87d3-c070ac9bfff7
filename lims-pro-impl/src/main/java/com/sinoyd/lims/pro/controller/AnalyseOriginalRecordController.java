package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.AnalyseOriginalRecordService;
import com.sinoyd.lims.pro.criteria.AnalyseOriginalRecordCriteria;
import com.sinoyd.lims.pro.dto.DtoAnalyseOriginalRecord;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * AnalyseOriginalRecord服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: AnalyseOriginalRecord服务")
 @RestController
 @RequestMapping("api/pro/analyseOriginalRecord")
 public class AnalyseOriginalRecordController extends BaseJpaController<DtoAnalyseOriginalRecord, String,AnalyseOriginalRecordService> {


    /**
     * 分页动态条件查询AnalyseOriginalRecord
     * @param analyseOriginalRecordCriteria 条件参数
     * @return RestResponse<List<AnalyseOriginalRecord>>
     */
     @ApiOperation(value = "分页动态条件查询AnalyseOriginalRecord", notes = "分页动态条件查询AnalyseOriginalRecord")
     @GetMapping
     public RestResponse<List<DtoAnalyseOriginalRecord>> findByPage(AnalyseOriginalRecordCriteria analyseOriginalRecordCriteria) {
         PageBean<DtoAnalyseOriginalRecord> pageBean = super.getPageBean();
         RestResponse<List<DtoAnalyseOriginalRecord>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, analyseOriginalRecordCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询AnalyseOriginalRecord
     * @param id 主键id
     * @return RestResponse<DtoAnalyseOriginalRecord>
     */
     @ApiOperation(value = "按主键查询AnalyseOriginalRecord", notes = "按主键查询AnalyseOriginalRecord")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoAnalyseOriginalRecord> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoAnalyseOriginalRecord> restResponse = new RestResponse<>();
         DtoAnalyseOriginalRecord analyseOriginalRecord = service.findOne(id);
         restResponse.setData(analyseOriginalRecord);
         restResponse.setRestStatus(StringUtil.isNull(analyseOriginalRecord) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增AnalyseOriginalRecord
     * @param analyseOriginalRecord 实体列表
     * @return RestResponse<DtoAnalyseOriginalRecord>
     */
     @ApiOperation(value = "新增AnalyseOriginalRecord", notes = "新增AnalyseOriginalRecord")
     @PostMapping
     public RestResponse<DtoAnalyseOriginalRecord> create(@RequestBody @Validated DtoAnalyseOriginalRecord analyseOriginalRecord) {
         RestResponse<DtoAnalyseOriginalRecord> restResponse = new RestResponse<>();
         restResponse.setData(service.save(analyseOriginalRecord));
         return restResponse;
      }

     /**
     * 新增AnalyseOriginalRecord
     * @param analyseOriginalRecord 实体列表
     * @return RestResponse<DtoAnalyseOriginalRecord>
     */
     @ApiOperation(value = "修改AnalyseOriginalRecord", notes = "修改AnalyseOriginalRecord")
     @PutMapping
     public RestResponse<DtoAnalyseOriginalRecord> update(@RequestBody @Validated DtoAnalyseOriginalRecord analyseOriginalRecord) {
         RestResponse<DtoAnalyseOriginalRecord> restResponse = new RestResponse<>();
         restResponse.setData(service.update(analyseOriginalRecord));
         return restResponse;
      }

    /**
     * "根据id批量删除AnalyseOriginalRecord
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除AnalyseOriginalRecord", notes = "根据id批量删除AnalyseOriginalRecord")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }