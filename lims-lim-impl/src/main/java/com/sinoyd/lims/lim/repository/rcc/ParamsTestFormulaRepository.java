package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsTestFormula;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;


/**
 * ParamsTestFormula数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/4
 * @since V100R001
 */
public interface ParamsTestFormulaRepository extends IBaseJpaPhysicalDeleteRepository<DtoParamsTestFormula, String>, LimsRepository<DtoParamsTestFormula, String> {


    /**
     * 删除关联数据
     *
     * @param objId 测试公式id
     * @return 返回删除数量
     */
    @Transactional
    @Modifying
    @Query("delete from DtoParamsTestFormula p where p.objId = :objId")
    Integer deleteByObjId(@Param("objId") String objId);

    /**
     * @param objId 公式id
     * @return 返回公式相关参数
     */
    @Query("select p from DtoParamsTestFormula p where p.objId = :objId order by p.orderNum desc ")
    List<DtoParamsTestFormula> findByObjId(@Param("objId") String objId);

    /**
     * 根据公式id 获取相关的参数数据
     *
     * @param objectIds 对象的ids
     * @return 返回想要的参数数据
     */
    List<DtoParamsTestFormula> findByObjIdIn(List<String> objectIds);

    /**
     * 删除关联数据
     *
     * @param objIds 测试公式ids
     * @return 返回删除数量
     */
    @Transactional
    @Modifying
    @Query("delete from DtoParamsTestFormula p where p.objId in :objIds")
    Integer deleteByObjIdIn(@Param("objIds") Collection<String> objIds);
}