package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;

/**
 * 固定资产实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/13
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "FixedProperty")
@Data
@EntityListeners(AuditingEntityListener.class)
public class FixedProperty implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public FixedProperty() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 固定资产名称
     */
    @Column(length = 50)
    @ApiModelProperty("固定资产名称")
    @Length(message = "固定资产名称{validation.message.length}", max = 50)
    private String assetsName;

    /**
     * 品牌型号
     */
    @Column(length = 50)
    @ApiModelProperty("品牌型号")
    @Length(message = "品牌型号{validation.message.length}", max = 50)
    private String brandModel;

    /**
     * 资产编号
     */
    @Column(length = 11)
    @ApiModelProperty("资产编号")
    @Length(message = "资产编号{validation.message.length}", max = 11)
    private String assetsNo;

    /**
     * 采购时间
     */
    @ApiModelProperty("采购时间")
    private Date purchaseDate;

    /**
     * 采购价格
     */
    @ApiModelProperty("采购价格")
    private Double purchasePrice;

    /**
     * 供应商（Guid）
     */
    @Column(length = 50)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("供应商（Guid）")
    private String supplier;

    /**
     * 资产类型
     */
    @Column(length = 50)
    @ApiModelProperty("资产类型")
    @Length(message = "资产类型{validation.message.length}", max = 50)
    private String assetsType;

    /**
     * 所属科室（Guid）
     */
    @Column(length = 50)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属科室（Guid）")
    private String deptId;

    /**
     * 资产状态 (1使用中，2已报废)
     */
    @Column(length = 10)
    @ApiModelProperty("资产状态 (1使用中，2已报废)")
    private Integer status;

    /**
     * 管理人员（Guid）
     */
    @Column(length = 50)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("管理人员（Guid）")
    private String manager;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}
