package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoLocalTaskPeopleCompare;

import java.util.List;

/**
 * 访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2024/11/27
 */
public interface LocalTaskPeopleCompareRepository extends IBaseJpaPhysicalDeleteRepository<DtoLocalTaskPeopleCompare,String> {
    /**
     * 根据项目标识查询
     * @param projectId 项目标识
     * @return 结果
     */
    List<DtoLocalTaskPeopleCompare> findByProjectId(String projectId);

    /**
     * 根据送样单标识查询
     * @param receiveId 送样单标识
     * @return 结果
     */
    DtoLocalTaskPeopleCompare findByReceiveId(String receiveId);
}
