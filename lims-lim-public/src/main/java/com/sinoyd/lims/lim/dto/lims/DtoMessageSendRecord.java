package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.MessageSendRecord;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * 消息发送记录传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/9/15
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_MessageSendRecord")
@Data
@DynamicInsert
public class DtoMessageSendRecord extends MessageSendRecord {
    private static final long serialVersionUID = 1L;

}