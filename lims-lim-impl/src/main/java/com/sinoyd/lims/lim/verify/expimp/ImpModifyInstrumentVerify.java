package com.sinoyd.lims.lim.verify.expimp;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.lims.lim.dto.customer.DtoImportInstrumentExpend;
import com.sinoyd.lims.lim.dto.customer.expimp.DtoExpImpInstrument;
import com.sinoyd.lims.lim.utils.ImportUtils;
import lombok.Data;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 仪器更新导入数据校验
 * <AUTHOR>
 * @version V1.0.0 2024/11/13
 * @since V100R001
 */
@Data
public class ImpModifyInstrumentVerify implements IExcelVerifyHandler<DtoExpImpInstrument> {


    /**
     * 业务数据参数
     */
    private final Map<String, Boolean> relationMap;

    /**
     * 部门数据
     */
    private final List<DtoDepartment> dbDept;

    /**
     * 仪器类型
     */
    private final List<DtoCode> instrumentTypes;

    /**
     * 系统中所有仪器
     */
    private final List<DtoInstrument> dbInstrument;

    /**
     * 关联数据
     */
    private final List<DtoImportInstrumentExpend> importInsExpendList;

    /**
     * 存储判断重复数据的临时数据
     */
    private List<DtoExpImpInstrument> instrumentTempList = new ArrayList<>();

    /**
     * 工具类
     */
    private final ImportUtils importUtils = new ImportUtils();

    public ImpModifyInstrumentVerify(Map<String, Boolean> relationMap, List<DtoDepartment> dbDept, List<DtoCode> instrumentTypes,
                                     List<DtoInstrument> dbInstrument, List<DtoImportInstrumentExpend> importInsExpendList) {
        this.relationMap = relationMap;
        this.dbDept = dbDept;
        this.instrumentTypes = instrumentTypes;
        this.dbInstrument = dbInstrument;
        this.importInsExpendList = importInsExpendList;
    }

    /**
     * 仪器数据校验
     *
     * @param instrument 导入数据
     * @return 校验结果
     */
    @Override
    public ExcelVerifyHandlerResult verifyHandler(DtoExpImpInstrument instrument) {

        //region 导入数据处理
        try {
            if (StringUtil.isNotEmpty(instrument.getState())) {
                if ("null".contains(instrument.getState())) {
                    instrument.setState(null);
                }
            }
            if (StringUtil.isNotEmpty(instrument.getOriginType())) {
                if ("null".contains(instrument.getOriginType())) {
                    instrument.setOriginType(null);
                }
            }
            //跳过空行
            if (importUtils.checkObjectIsNull(instrument)) {
                return new ExcelVerifyHandlerResult(true);
            }
            //去除前后空格
            importUtils.strToTrim(instrument);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }
        defaultValue(instrument);
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        //校验错误总数据
        StringBuilder failStr = new StringBuilder("第" + instrument.getRowNum() + "行数据校验有误");
        if (StringUtil.isEmpty(instrumentTempList)) {
            instrumentTempList = new ArrayList<>();
        }
        //校验仪器名称是否为空
        importUtils.checkIsNull(result, instrument.getInstrumentName(), "仪器名称", failStr);
        //校验出厂编号是否为空
        importUtils.checkIsNull(result, instrument.getSerialNo(), "出厂编号", failStr);
        //校验状态是否为空
        importUtils.checkIsNull(result, instrument.getState(), "状态", failStr);
        //校验规格型号是否为空
        importUtils.checkIsNull(result, instrument.getModel(), "规格型号", failStr);
        //校验管理员是否为空
        importUtils.checkIsNull(result, instrument.getManagerName(), "管理员", failStr);
        //校验部门是否存在
        isExistDept(result, instrument, failStr);
        //校验仪器类型是否存在
        isExistInsType(result, instrument, failStr);
        //校验仪器是否存在
        isExistInstrument(result, instrument, failStr);
        //校验状态是否填写正确
        isExistStatus(result, instrument, failStr);
        //判断计量类型
        isExistOriginCyc(result, instrument, failStr);
        // 检查结果格式
        isResultFormat(result, instrument, failStr);
        //校验重复数据
        isRepeatData(result, instrument, instrumentTempList, failStr);
        //判断仪器价格格式
        importUtils.checkNumTwo(result, instrument.getPrice(), ",仪器价格", failStr);
        //判断溯源周期格式
        importUtils.checkNumTwo(result, instrument.getOriginCyc(), ",溯源周期", failStr);
        //判断维护周期格式
        importUtils.checkNumTwo(result, instrument.getMaintenanceCyc(), ",维护周期", failStr);
        //判断维护周期格式
        importUtils.checkDateTwo(result, instrument.getOriginDate(), ",溯源日期", failStr);
        //判断核查周期格式
        importUtils.checkNumTwo(result, instrument.getInspectPeriod(), ",核查周期", failStr);
        //判断最近维护日期格式
        importUtils.checkDateTwo(result, instrument.getMaintenanceDate(), ",最近维护日期", failStr);
        //判断最近核查日期
        importUtils.checkDateTwo(result, instrument.getInspectDate(), ",期间检查日期", failStr);
        //判断购置日期
        importUtils.checkDateTwo(result, instrument.getPurchaseDate(), ",购置日期", failStr);
        //最后开启日期
        importUtils.checkDateTwo(result, instrument.getRecentOpenDate(), ",开启日期", failStr);
        //返回校验数据
        String failString = failStr.toString().replaceFirst("；", ":");
        result.setMsg(failString);
        instrumentTempList.add(instrument);
        return result;
        //endregion
    }

    /**
     * 设置默认数据
     *
     * @param instrument 导入数据
     */
    private void defaultValue(DtoExpImpInstrument instrument) {
        //计量类型默认值
        instrument.setOriginType(StringUtil.isEmpty(instrument.getOriginType()) ? "1" : instrument.getOriginType());
        if ("检定".equals(instrument.getOriginType())) {
            instrument.setOriginType("1");
        }
        if ("校准".equals(instrument.getOriginType())) {
            instrument.setOriginType("2");
        }
        if ("自校".equals(instrument.getOriginType())) {
            instrument.setOriginType("3");
        }

        //状态判定
        instrument.setState(StringUtil.isEmpty(instrument.getState()) ? "1" : instrument.getState());
        if ("报废".equals(instrument.getState())) {
            instrument.setState("0");
        }
        if ("正常".equals(instrument.getState())) {
            instrument.setState("1");
        }
        if ("停用".equals(instrument.getState())) {
            instrument.setState("2");
        }
        if ("过期".equals(instrument.getState())) {
            instrument.setState("3");
        }
        // 溯源结果
        instrument.setOriginResult(StringUtil.isEmpty(instrument.getOriginResult()) ? "1" : instrument.getOriginResult());
        if ("合格".equals(instrument.getOriginResult())) {
            instrument.setOriginResult("1");
        }
        if ("不合格".equals(instrument.getOriginResult())) {
            instrument.setOriginResult("0");
        }
        // 核查结果
        instrument.setInspectResult(StringUtil.isEmpty(instrument.getInspectResult()) ? "1" : instrument.getInspectResult());
        if ("合格".equals(instrument.getInspectResult())) {
            instrument.setInspectResult("1");
        }
        if ("不合格".equals(instrument.getInspectResult())) {
            instrument.setInspectResult("0");
        }
    }

    /**
     * 判断部门导入
     *
     * @param result     校验结果
     * @param instrument 当前仪器
     * @param failStr    校验错误信息
     */
    private void isExistDept(ExcelVerifyHandlerResult result, DtoExpImpInstrument instrument, StringBuilder failStr) {

        // 判断填写部门是否为空
        if (StringUtil.isEmpty(instrument.getDeptName())) {
            result.setSuccess(false);
            failStr.append("；部门不能为空");
            return;
        }
        // 判断部门是否存在
        List<DtoDepartment> isExistDept = dbDept.stream().filter(p -> instrument.getDeptName().equals(p.getDeptName())).collect(Collectors.toList());
        if (StringUtil.isEmpty(isExistDept)) {
            result.setSuccess(false);
            failStr.append("；部门不存在");
        }
    }

    /**
     * 校验仪器类型
     *
     * @param result          校验结果
     * @param instrument      当前仪器
     * @param failStr         校验错误信息
     */
    private void isExistInsType(ExcelVerifyHandlerResult result, DtoExpImpInstrument instrument, StringBuilder failStr) {

        //region 判断类型是否为空
        if (StringUtil.isEmpty(instrument.getInstrumentTypeId())) {
            result.setSuccess(false);
            failStr.append("；仪器类型不能为空");
            return;
        }
        //endregion

        //region 判断仪器类型是否存在
        if (!relationMap.get("isImportInsType")) {
            //region 判断仪器类型在数据库中是否存在
            List<DtoCode> isExistType = instrumentTypes.stream().filter(p -> instrument.getInstrumentTypeId().equals(p.getDictName())).collect(Collectors.toList());
            if (StringUtil.isEmpty(isExistType)) {
                result.setSuccess(false);
                failStr.append("；仪器类型不存在");
            }
            //endregion
        } else {
            //region 判断仪器类型在关联表中是否存在
            List<String> importInsType = importInsExpendList.stream().map(DtoImportInstrumentExpend::getInstrumentTypeName).collect(Collectors.toList());
            importInsType.removeIf(Objects::isNull);
            List<String> isExistTypeName = Stream.of(instrument.getInstrumentTypeId()).filter(p -> !importInsType.contains(p)).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(isExistTypeName)) {
                result.setSuccess(false);
                failStr.append("；仪器类型与关联表不对应");
            }
            //endregion
        }
        //endregion

    }

    /**
     * 判断是否存在仪器数据
     *
     * @param result       校验结果
     * @param instrument   需要检验的数据
     * @param failStr      校验错误信息
     */
    private void isExistInstrument(ExcelVerifyHandlerResult result, DtoExpImpInstrument instrument, StringBuilder failStr) {

        //region 判断仪器编号是否为空
        if (StringUtil.isEmpty(instrument.getInstrumentsCode())) {
            result.setSuccess(false);
            failStr.append("；仪器编号不能为空");
            return;
        }
        //endregion
        String id = StringUtil.isEmpty(instrument.getId()) ? "" : instrument.getId();
        //region 判断编号是否存在
        if (StringUtil.isNotEmpty(dbInstrument.stream().filter(p -> !id.equals(p.getId()) && instrument.getInstrumentsCode().equals(p.getInstrumentsCode())).collect(Collectors.toList()))) {
            result.setSuccess(false);
            failStr.append("；仪器编号在系统中已存在");
        }
        //endregion
    }

    /**
     * 判断是否存在状态
     *
     * @param result     校验结果
     * @param instrument 需要检验的数据
     * @param failStr    校验错误信息
     */
    private void isExistStatus(ExcelVerifyHandlerResult result, DtoExpImpInstrument instrument, StringBuilder failStr) {
        //region 判断状态是否为空
        if (StringUtil.isEmpty(instrument.getState())) {
            result.setSuccess(false);
            failStr.append("；状态不能为空");
            return;
        }
        //endregion

        //region 状态处理
        String statusName;
        if ("0".equals(instrument.getState())) {
            statusName = "报废";
        } else if ("1".equals(instrument.getState())) {
            statusName = "正常";
        } else if ("2".equals(instrument.getState())) {
            statusName = "停用";
        } else if ("3".equals(instrument.getState())) {
            statusName = "过期";
        } else {
            statusName = instrument.getState();
        }
        //endregion

        //region 判断填写状态是否存在
        List<String> status = importInsExpendList.stream().map(DtoImportInstrumentExpend::getStatus).collect(Collectors.toList());
        status.removeIf(Objects::isNull);
        List<String> isExist = status.stream().filter(statusName::equals).collect(Collectors.toList());
        if (StringUtil.isEmpty(isExist)) {
            result.setSuccess(false);
            failStr.append("；状态不存在");
        }
        //endregion
    }

    /**
     * 判断溯源结果和核查结果
     *
     * @param result     校验结果
     * @param instrument 需要检验的数据
     * @param failStr    校验错误信息
     */
    private void isResultFormat(ExcelVerifyHandlerResult result, DtoExpImpInstrument instrument, StringBuilder failStr) {
        //region 状态处理
        List<String> status = Arrays.asList("合格", "不合格");
        if (StringUtil.isNotEmpty(instrument.getOriginResult())) {
            String resultName;
            if ("0".equals(instrument.getOriginResult())) {
                resultName = "不合格";
            } else if ("1".equals(instrument.getOriginResult())) {
                resultName = "合格";
            } else {
                resultName = instrument.getOriginResult();
            }
            List<String> isExist = status.stream().filter(resultName::equals).collect(Collectors.toList());
            if (StringUtil.isEmpty(isExist)) {
                result.setSuccess(false);
                failStr.append("；溯源结果格式不正确");
            }
        }
        if (StringUtil.isNotEmpty(instrument.getInspectResult())) {
            String resultName;
            if ("0".equals(instrument.getInspectResult())) {
                resultName = "不合格";
            } else if ("1".equals(instrument.getInspectResult())) {
                resultName = "合格";
            } else {
                resultName = instrument.getInspectResult();
            }
            List<String> isExist = status.stream().filter(resultName::equals).collect(Collectors.toList());
            if (StringUtil.isEmpty(isExist)) {
                result.setSuccess(false);
                failStr.append("；核查结果格式不正确");
            }
        }
    }

    /**
     * 判断是否存在计量类型
     *
     * @param result     校验结果
     * @param instrument 需要检验的数据
     * @param failStr    校验错误信息
     */
    private void isExistOriginCyc(ExcelVerifyHandlerResult result, DtoExpImpInstrument instrument, StringBuilder failStr) {
        if (StringUtil.isNotEmpty(instrument.getOriginType()) || !"null".equals(instrument.getOriginType())) {
            //region 处理检定方式
            String typeName;
            switch (instrument.getOriginType().trim()) {
                case "1":
                    typeName = "检定";
                    break;
                case "2":
                    typeName = "校准";
                    break;
                case "3":
                    typeName = "自校";
                    break;
                default:
                    typeName = "其他";
                    break;
            }
            //endregion

            //region 判断计量类型是否存在
            List<String> originCyc = importInsExpendList.stream().map(DtoImportInstrumentExpend::getMeteringType).collect(Collectors.toList());
            originCyc.removeIf(Objects::isNull);
            List<String> isExist = Stream.of(typeName).filter(p -> !originCyc.contains(p)).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(isExist)) {
                result.setSuccess(false);
                failStr.append("；计量类型不存在");
            }
            //endregion
        }
    }

    /**
     * 判断重复数据
     *
     * @param result            校验结果
     * @param instrument        本次实体
     * @param importInstruments 导入的仪器
     * @param failStr           检验错误信息
     */
    private void isRepeatData(ExcelVerifyHandlerResult result, DtoExpImpInstrument instrument, List<DtoExpImpInstrument> importInstruments, StringBuilder failStr) {
        //region 判断仪器编号是否重复
        if (StringUtil.isNotEmpty(instrument.getInstrumentsCode())) {
            List<Integer> repeatNum = importInstruments.stream().filter(p -> instrument.getInstrumentsCode().equals(p.getInstrumentsCode())).map(DtoExpImpInstrument::getRowNum).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(repeatNum)) {
                result.setSuccess(false);
                failStr.append("；仪器编号与第").append(repeatNum).append("行重复");
            }
        }
        //endregion
    }
}
