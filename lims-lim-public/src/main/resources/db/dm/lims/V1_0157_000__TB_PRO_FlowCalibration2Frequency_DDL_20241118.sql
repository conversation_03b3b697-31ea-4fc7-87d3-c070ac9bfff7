CREATE TABLE TB_PRO_FlowCalibration2Frequency
(
    id           varchar(50) NOT NULL COMMENT 'id',
    flowCalibrationId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '流量校准记录标识',
    sampleFolderId   varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '点位标识',
    periodCount   int NOT NULL DEFAULT 0 COMMENT '周期'
);
ALTER TABLE TB_PRO_FlowCalibration2Frequency ADD CONSTRAINT PRIMARY KEY (id);
COMMENT ON TABLE TB_PRO_FlowCalibration2Frequency IS '流量校准与点位频次关联表';
COMMENT ON COLUMN TB_PRO_FlowCalibration2Frequency.id IS 'id';
COMMENT ON COLUMN TB_PRO_FlowCalibration2Frequency.flowCalibrationId IS '流量校准记录标识';
COMMENT ON COLUMN TB_PRO_FlowCalibration2Frequency.sampleFolderId IS '点位标识';
COMMENT ON COLUMN TB_PRO_FlowCalibration2Frequency.periodCount IS '周期';

ALTER TABLE TB_PRO_FlowCalibration ADD COLUMN calibrationTypeName varchar(50);
COMMENT ON COLUMN TB_PRO_FlowCalibration.calibrationTypeName IS '校准类型名称';

