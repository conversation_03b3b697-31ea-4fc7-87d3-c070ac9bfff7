package com.sinoyd.base.dto.rcc;

import com.sinoyd.base.entity.EvaluationAnalyzeItem;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoEvaluationAnalyzeItem实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_BASE_EvaluationAnalyzeItem")
 @Data
 @DynamicInsert
 public  class DtoEvaluationAnalyzeItem extends EvaluationAnalyzeItem {

 }