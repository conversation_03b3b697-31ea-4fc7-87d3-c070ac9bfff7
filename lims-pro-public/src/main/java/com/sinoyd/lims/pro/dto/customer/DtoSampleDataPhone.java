package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class DtoSampleDataPhone {

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 数据id
     */
    private String anaId;

    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 分析项目名称
     */
    private String itemName;

    /**
     * 分析项目结果
     */
    private String anaValue;

    /**
     * 量纲
     */
    private String dimension;

    /**
     * 参数
     */
    private List<DtoAnalyseOriginalJson> analyseOriginalJsonList;

    /**
     * 公式
     */
    private String formula;

    /**
     * 公式id
     */
    private String formulaId;

    /**
     * 有效位数
     */
    Integer mostSignificance;

    /**
     * 小数位数
     */
    Integer mostDecimal;

    /**
     * 检出限
     */
    String examLimitValue;

    /**
     * 参数数据
     */
    Map<String, Object> paramMap;

    /**
     * 样品类型id
     */
    private String sampleTypeId;

    /**
     * 样品类型名称
     */
    private String sampleTypeName;

    /**
     * 质控编号
     */
    private String qcCode;

    /**
     * 质控值
     */
    private String qcValue;

    /**
     * 不确定度
     */
    private Integer uncertainType;

    /**
     * 评价方式
     */
    private String judgingMethod;

    /**
     * 公式
     */
    private String allowLimit;

    /**
     * 评价结果
     */
    private String qcInfo;

    /**
     * 是否合格
     */
    private Boolean isPass;

    /**
     * 量纲id
     */
    private String dimensionId;
}
