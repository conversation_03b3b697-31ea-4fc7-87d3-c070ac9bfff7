package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.PerformanceStatisticForReportData;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import java.util.Date;


/**
 * DtoPerformanceStatisticForReportData实体
 * <AUTHOR>
 * @version V1.0.0 2020/2/24
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_PerformanceStatisticForReportData")
 @Data
 @DynamicInsert
 public  class DtoPerformanceStatisticForReportData extends PerformanceStatisticForReportData {
    private static final long serialVersionUID = 1L;
    /**
     * 项目编号
     */
    @Transient
    private String projectCode;

    /**
     * 项目名称
     */
    @Transient
    private String projectName;

    /**
     * 项目类型id
     */
    @Transient
    private String projectTypeId;

    /**
     * 项目类型名称
     */
    @Transient
    private String projectTypeName;

    /**
     * 委托方id
     */
    @Transient
    private String customerId;

    /***
     * 委托方的名称
     */
    @Transient
    private String customerName;

    /**
     *  默认的构造函数
     */
    public DtoPerformanceStatisticForReportData() {

    }

    /**
     * 该构造函数主要用到 PerformanceStatisticForReportDataServiceImpl 下面 findList查询的方法，主要是查询采样项目的绩效
     * @param id 主键id
     * @param projectId 项目id
     * @param reportTime 报告时间
     * @param reportMakerId 编制报告人
     * @param report 报告数
     * @param orgId 组织机构id
     * @param projectCode 项目编号
     * @param projectName 项目名称
     * @param projectTypeId 项目类型id
     * @param projectTypeName 项目类型名称
     * @param customerId 委托方id
     * @param customerName 委托方
     */
    public DtoPerformanceStatisticForReportData(String id, String projectId,
                                                Date reportTime,Integer report,
                                                String reportMakerId,String orgId,String projectCode,
                                                String projectName,String projectTypeId,
                                                String projectTypeName,String customerId,
                                                String customerName
                                                ) {
        this.setId(id);
        this.setProjectId(projectId);
        this.setReportTime(reportTime);
        this.setReport(report);
        this.setReportMakerId(reportMakerId);
        this.setOrgId(orgId);
        this.setProjectCode(projectCode);
        this.setProjectName(projectName);
        this.setProjectTypeId(projectTypeId);
        this.setProjectTypeName(projectTypeName);
        this.setCustomerId(customerId);
        this.setCustomerName(customerName);
    }

    /**
     * 构造函数 工作量统计总计行
     */
    public DtoPerformanceStatisticForReportData(Integer report){
        this.setReport(report);
    }
}