package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoStatusForRecord;
import com.sinoyd.frame.service.IBaseJpaService;


/**
 * StatusForRecord操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/8
 * @since V100R001
 */
public interface StatusForRecordService extends IBaseJpaService<DtoStatusForRecord, String> {

    /**
     * 创建状态
     *
     * @param receiveId 送样单id
     */
    void createStatus(String receiveId, String module);

    /**
     * 状态置为已完成
     *
     * @param receiveId 送样单id
     * @param module 模块编码
     */
    void completeStatus(String receiveId, String module);

    /**
     * 修改状态数据
     *
     * @param record   处理后的送样单实体
     * @param nextPersonId     下一步操作人id
     * @param nextPerson     下一步操作人
     * @param opinion   意见
     */
    void modifyStatus(DtoReceiveSampleRecord record, String nextPersonId,String nextPerson, String opinion);
}