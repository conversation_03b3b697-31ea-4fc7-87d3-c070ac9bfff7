package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.pro.dto.DtoOATaskHandleLog;
import com.sinoyd.lims.pro.repository.OATaskHandleLogRepository;
import com.sinoyd.lims.pro.service.OATaskHandleLogService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 审批任务流程日志业务操作接口实现
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-03-26
 * @since V100R001
 */
@Service
public class OATaskHandleLogServiceImpl extends BaseJpaServiceImpl<DtoOATaskHandleLog, String, OATaskHandleLogRepository>
        implements OATaskHandleLogService {
    @Override
    public void findByPage(PageBean<DtoOATaskHandleLog> page, BaseCriteria criteria) {
        String sort = page.getSort();
        if (StringUtil.isEmpty(sort)) {
            page.setSort("completeTime+");
        }

        page.setRowsPerPage(1000);

        page.setEntityName("DtoOATaskHandleLog x");
        page.setSelect("select x");

        super.findByPage(page, criteria);
        List<DtoOATaskHandleLog> list = page.getData();
        if(StringUtil.isNotEmpty(list)){
            for (DtoOATaskHandleLog log:list ) {
                if("fileAudit".equals(log.getActTaskDefKey())){
                    if(log.getIsFirstStep()){
                        log.setAllComment(log.getAssigneeName()+"于"+ DateUtil.dateToString(log.getCompleteTime(),DateUtil.FULL)+"新增了文件审批申请。");
                    }else{
                        String allComment = log.getAssigneeName()+"于"+ DateUtil.dateToString(log.getCompleteTime(),DateUtil.FULL)
                                +(log.getIsAgree()?"通过":"退回")+"了"+log.getActTaskName()+"。";
                        if(StringUtil.isNotEmpty(log.getComment())&&allComment.endsWith("。")){
                            allComment = allComment.substring(0,allComment.lastIndexOf("。"))+",意见:"+log.getComment()+"。";
                        }
                        log.setAllComment(allComment);
                    }
                }
            }
        }
    }
}
