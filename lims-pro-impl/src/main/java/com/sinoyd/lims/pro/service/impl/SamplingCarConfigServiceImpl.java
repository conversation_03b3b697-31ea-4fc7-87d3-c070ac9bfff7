package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.lims.pro.dto.DtoSamplingCarConfig;
import com.sinoyd.lims.pro.dto.DtoSamplingPersonConfig;
import com.sinoyd.lims.pro.repository.SamplingCarConfigRepository;
import com.sinoyd.lims.pro.service.SamplingCarConfigService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;


/**
 * SamplingCarConfig操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Service
public class SamplingCarConfigServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoSamplingCarConfig,String,SamplingCarConfigRepository> implements SamplingCarConfigService {

    @Override
    public void findByPage(PageBean<DtoSamplingCarConfig> pb, BaseCriteria samplingCarConfigCriteria) {
        pb.setEntityName("DtoSamplingCarConfig s,DtoReceiveSampleRecord r,DtoProject p");
        pb.setSelect("select s,r.recordCode, r.senderName, r.sendTime as useDate,p.projectCode,p.projectName,p.customerName");
        comRepository.findByPage(pb, samplingCarConfigCriteria);

        List<DtoSamplingCarConfig> datas = pb.getData();
        List<DtoSamplingCarConfig> newDatas = new ArrayList<>();

        Iterator<DtoSamplingCarConfig> ite = datas.iterator();
        // 循环迭代获取JPQL中查询返回的属性

        while (ite.hasNext()) {
            Object obj = ite.next();
            Object[] objs = (Object[]) obj;
            // 按查询顺序下标一一获取
            DtoSamplingCarConfig cfg = (DtoSamplingCarConfig) objs[0];
            cfg.setRecordCode((String) objs[1]);
            cfg.setUsePerson((String) objs[2]);
            cfg.setUseDate((Date) objs[3]);
            cfg.setProjectCode((String) objs[4]);
            cfg.setProjectName((String) objs[5]);
            cfg.setCustomerName((String) objs[6]);

            newDatas.add(cfg);
        }

        pb.setData(newDatas);
    }
}