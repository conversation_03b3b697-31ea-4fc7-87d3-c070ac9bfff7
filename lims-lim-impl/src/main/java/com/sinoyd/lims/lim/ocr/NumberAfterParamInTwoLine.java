package com.sinoyd.lims.lim.ocr;

import com.sinoyd.lims.lim.service.ocr.RecognizeStrategy;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class NumberAfterParamInTwoLine implements RecognizeStrategy {
    @Override
    public String recognize(List<String> data, String paramName) {
        String[] originalArray = (String[]) data.toArray();
        String[] newArray = new String[originalArray.length / 2];
        // 遍历原始数组，两两拼接
        for (int i = 0; i < originalArray.length - 1; i += 2) {
            // 使用StringBuilder拼接字符串，提高效率
            StringBuilder sb = new StringBuilder();
            sb.append(originalArray[i]).append(originalArray[i + 1]);
            newArray[i / 2] = sb.toString();
        }
        data = Arrays.asList(newArray);
        String textWithParam = data.stream().filter(d->d.indexOf(paramName)!=-1).findFirst().orElse(null);
        if(textWithParam!=null){
            textWithParam = textWithParam.substring(textWithParam.indexOf(paramName)).trim();
            Pattern pattern = Pattern.compile("[0-9]+(\\.[0-9]+)?");
            Matcher matcher = pattern.matcher(textWithParam);
            if (matcher.find()) {
                return matcher.group();
            }
        }
        return "";
    }
}
