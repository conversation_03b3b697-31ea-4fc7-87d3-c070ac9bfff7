package com.sinoyd.lims.probase.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoProject;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

public interface ProjectBaseRepository extends IBaseJpaRepository<DtoProject, String> {
    /**
     * @param ids        样品ids
     * @param status     状态
     * @param modifier   修改人
     * @param modifyDate 修改时间
     * @return 返回更新信息
     */
    @Transactional
    @Modifying
    @Query("update DtoProject p set p.status = :status,p.modifyDate = :modifyDate,p.modifier = :modifier  where p.id in :ids")
    Integer updateProjectStatus(@Param("ids") List<String> ids, @Param("status") String status,
                                @Param("modifier") String modifier,
                                @Param("modifyDate") Date modifyDate);
}
