package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.pro.dto.DtoCostInfo;
import com.sinoyd.lims.pro.dto.DtoStatusForCostInfo;
import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.frame.service.IBaseJpaService;


/**
 * 费用状态操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/8
 * @since V100R001
 */
public interface StatusForCostInfoService extends IBaseJpaService<DtoStatusForCostInfo, String> {

    /**
     * 创建状态
     *
     * @param costInfoId 费用id
     */
    void createStatus(String costInfoId);

    /**
     * 修改状态数据
     *
     * @param from     状态起
     * @param to       状态止
     * @param sign     工作流信号对象
     * @param costInfo 费用实体
     */
    void modifyStatus(String from, String to, DtoWorkflowSign sign, DtoCostInfo costInfo);
}