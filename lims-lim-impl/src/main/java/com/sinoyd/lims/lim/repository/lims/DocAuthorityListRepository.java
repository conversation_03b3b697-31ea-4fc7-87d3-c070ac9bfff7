package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoDocAuthorityList;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 文件夹权限仓储
 *
 * <AUTHOR>
 * @version V1.0.0 2023/12/19
 * @since V100R001
 */
public interface DocAuthorityListRepository extends IBaseJpaPhysicalDeleteRepository<DtoDocAuthorityList, String> {

    /**
     * 根据文件夹id查询权限列表
     *
     * @param objectId 文件夹id
     * @return  List<DtoDocAuthorityList>
     */
    List<DtoDocAuthorityList> findByObjectId(String objectId);

    /**
     * 根据文件夹id查询权限列表
     *
     * @param objectIds 文件夹id
     * @return List<DtoDocAuthorityList>
     */
    List<DtoDocAuthorityList> findByObjectIdIn(List<String> objectIds);

    /**
     * 根据文件id和权限编码查询对应权限
     *
     * @param objectId 文件夹id
     * @param authCode 权限编码
     * @return DtoDocAuthorityList
     */
    DtoDocAuthorityList findByObjectIdAndAuthCode(String objectId, String authCode);

    /**
     * 批量设置
     *
     * @param ids             id集合
     * @param defaultOpenInd  是否开启
     * @return
     */
    @Transactional
    @Modifying
    @Query("update DtoDocAuthorityList a set a.defaultOpenInd = :defaultOpenInd where a.id in :ids")
    Integer batchSet(@Param("ids") List<String> ids, @Param("defaultOpenInd") Boolean defaultOpenInd);

    /**
     * 按照文件夹id和权限编码查询数据
     *
     * @param objectIds  文件夹id
     * @param authCodes  权限编码
     * @return  List<DtoDocAuthorityList>
     */
    List<DtoDocAuthorityList> findByObjectIdInAndAuthCodeIn(List<String> objectIds, List<String> authCodes);

}
