package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoProject2Report;

import java.util.List;


/**
 * 项目和报告关联关系访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2023/04/25
 * @since V100R001
 */
public interface Project2ReportRepository extends IBaseJpaPhysicalDeleteRepository<DtoProject2Report, String> {

    /**
     * 根据项目id查找
     *
     * @param projectId 项目id
     * @return 同项目id的费用个数
     */
    List<DtoProject2Report> findByProjectId(String projectId);

    /**
     * 根据项目id列表查找
     *
     * @param projectIdList 项目id
     * @return 同项目id的费用个数
     */
    List<DtoProject2Report> findByProjectIdIn(List<String> projectIdList);

    /**
     * 根据报告id查找
     *
     * @param reportId 报告id
     * @return 同项目id的费用个数
     */
    List<DtoProject2Report> findByReportId(String reportId);

}