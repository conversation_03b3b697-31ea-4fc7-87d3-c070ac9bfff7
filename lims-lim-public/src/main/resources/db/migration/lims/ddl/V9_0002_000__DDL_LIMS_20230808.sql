-- 报告信息表添加分析项目排序id字段
ALTER TABLE TB_PRO_Report
    ADD COLUMN analyseItemSortId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '分析项目排序id';

-- 报告信息表添加点位排序id字段
ALTER TABLE TB_PRO_Report
    ADD COLUMN folderSortId VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '点位排序id';

-- 新增报告点位排序信息表
CREATE TABLE TB_PRO_ReportFolderSortInfo
(
    id         varchar(50) NOT NULL COMMENT '主键id',
    reportId   varchar(50) NOT NULL COMMENT '报告id',
    folderId   varchar(50) NULL COMMENT '点位id',
    orderNum   int(11) NOT NULL DEFAULT 0 COMMENT '点位排序值',
    orgId      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator    varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId   varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier   varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id)
) COMMENT = '报告点位排序信息表';