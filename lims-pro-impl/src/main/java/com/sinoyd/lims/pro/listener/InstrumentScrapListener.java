package com.sinoyd.lims.pro.listener;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.lims.lim.dto.lims.DtoOAInstrumentScrap;
import com.sinoyd.lims.lim.service.OAInstrumentScrapService;
import com.sinoyd.lims.pro.dto.DtoOATaskRelation;
import com.sinoyd.lims.pro.service.OATaskRelationService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 仪器报废状态的监听
 *
 * <AUTHOR>
 * @version V1.0.0 2020-04-01
 * @since V100R001
 */
@Component
@Slf4j
public class InstrumentScrapListener implements ExecutionListener {

    private static final long serialVersionUID = 1L;

    @Override
    public void notify(DelegateExecution delegateExecution) {
        OATaskRelationService taskRelationService = SpringContextAware.getBean(OATaskRelationService.class);

        List<DtoOATaskRelation> relations = taskRelationService.findListByTaskId(delegateExecution.getProcessBusinessKey());

        List<String> objectIds = relations.stream().map(DtoOATaskRelation::getObjectId).distinct().collect(Collectors.toList());

        OAInstrumentScrapService oaInstrumentScrapService = SpringContextAware.getBean(OAInstrumentScrapService.class);

        CommonRepository commonRepository = SpringContextAware.getBean(CommonRepository.class);
        if (objectIds.size() > 0) {
            List<DtoOAInstrumentScrap> oaInstrumentScraps = oaInstrumentScrapService.findAll(objectIds);
            List<String> instrumentIds = oaInstrumentScraps.stream().map(DtoOAInstrumentScrap::getInstrumentId).distinct().collect(Collectors.toList());
            for (String id : instrumentIds) {
                DtoInstrument dtoInstrument = new DtoInstrument();
                dtoInstrument.setId(id);
                dtoInstrument.setState(EnumBase.EnumInstrumentStatus.报废.getValue());
                commonRepository.merge(dtoInstrument);
            }
        }
    }
}