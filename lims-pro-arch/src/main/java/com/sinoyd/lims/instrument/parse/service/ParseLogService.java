package com.sinoyd.lims.instrument.parse.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.instrument.parse.dto.DtoParseLog;

import javax.servlet.http.HttpServletResponse;

/**
 * 仪器解析日志业务访问接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2021/09/03
 */
public interface ParseLogService extends IBaseJpaService<DtoParseLog, String> {

    /**
     * 根据日志id下载解析文件
     * @param logId 日志id
     * @return Map<String, Object>
     */
    String download(String logId, HttpServletResponse response);
}
