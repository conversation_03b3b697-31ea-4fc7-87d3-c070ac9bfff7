package com.sinoyd.lims.lim.service;

import java.util.*;

import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoTest2Instrument;

/**
 * 使用仪器接口
 * <AUTHOR>
 * @version V1.0.0 2019/1/16
 * @since V100R001
 */
public interface Test2InstrumentService extends IBaseJpaService<DtoTest2Instrument, String> {

  /**
   * 获取使用仪器列表
   * 
   * @param testId  测试项目id
   * @param useType 使用仪器类型
   * @return List<DtoInstrument>
   */
  List<DtoInstrument> getList(String testId, Integer useType);

  /**
   * 新增使用仪器
   * 
   * @param testId        测试项目id
   * @param instrumentIds 仪器ids
   * @param useType       使用类型
   * @return Integer
   */
  Integer addTestInstrument(String testId, Collection<String> instrumentIds, Integer useType);

  /**
   * 删除使用仪器
   *
   * @param testId        测试项目id
   * @param instrumentIds 仪器ids
   * @param useType       使用类型
   * @return Integer
   */
  Integer deleteTestInstrument(String testId, Collection<String> instrumentIds, Integer useType);
}