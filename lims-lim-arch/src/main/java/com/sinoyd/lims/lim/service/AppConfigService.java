package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoAppConfig;

import java.util.List;

/**
 * app应用配置接口
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/1/12
 */
public interface AppConfigService extends IBaseJpaService<DtoAppConfig, String> {

    /**
     * 根据id查询文件路径
     *
     * @param id 主键id
     * @return app应用配置dto
     */
    DtoAppConfig findAttachment(String id);

    /**
     * 查询应用配置
     * @return 配置内容
     */
    List<DtoAppConfig> findAllConfig();
}