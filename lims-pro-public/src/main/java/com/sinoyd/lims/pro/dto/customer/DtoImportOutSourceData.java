package com.sinoyd.lims.pro.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.Data;

import java.util.Date;

/**
 * 分包数据导入传输类
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023/2/9
 **/
@Data
public class DtoImportOutSourceData implements IExcelDataModel, IExcelModel {

    /**
     * 检测类型
     */
    @Excel(name = "检测类型", orderNum = "10", width = 11)
    private String sampleType;

    /**
     * 采样日期
     */
    @Excel(name = "采样日期", orderNum = "20", width = 15, format = "yyyy-MM-dd")
    private Date samplingTime;

    /**
     * 点位名称
     */
    @Excel(name = "点位名称", orderNum = "30", width = 20)
    private String sampleFolderName;

    /**
     * 样品编号
     */
    @Excel(name = "样品编号", orderNum = "40", width = 11)
    private String sampleCode;

    /**
     * 分析项目
     */
    @Excel(name = "分析项目", orderNum = "50", width = 23)
    private String redAnalyzeItemName;

    /**
     * 分析方法
     */
    @Excel(name = "分析方法", orderNum = "60", width = 17)
    private String analyzeMethodName;

    /**
     * 出证结果
     */
    @Excel(name = "出证结果", orderNum = "70", width = 24)
    private String testValue;

    /**
     * 量纲
     */
    @Excel(name = "量纲", orderNum = "80", width = 17)
    private String dimensionName;

    /**
     * 状态
     */
    @Excel(name = "状态", orderNum = "90", width = 11)
    private String state;

    /**
     * 检出限
     */
    @Excel(name = "检出限", orderNum = "85", width = 17)
    private String detectionLimit;

    /**
     * 分析数据id
     */
    @Excel(name = "分析数据id", orderNum = "100", width = 11, isColumnHidden = true)
    private String analyseDataId;

    @Excel(
            name = "分析开始日期",
            orderNum = "110",
            width = 15.0D,
            format = "yyyy-MM-dd"
    )
    private Date analyzeTime;

    @Excel(
            name = "分析结束日期",
            orderNum = "110",
            width = 15.0D,
            format = "yyyy-MM-dd"
    )
    private Date analyzeEndTime;

    @Excel(
            name = "分包商",
            orderNum = "120",
            width = 20.0D
    )
    private String subcontractor;

    @Excel(
            name = "分包商CMA证书编号",
            orderNum = "130",
            width = 11.0D
    )
    private String cmaCode;

    @Excel(
            name = "分包报告编号",
            orderNum = "140",
            width = 11.0D
    )
    private String outSourceReportCode;

    private Integer rowNum;

    private String errorMsg;

    @Override
    public int getRowNum() {
        return this.rowNum;
    }

    @Override
    public void setRowNum(int i) {
        this.rowNum = i;
    }

    @Override
    public String getErrorMsg() {
        return this.errorMsg;
    }

    @Override
    public void setErrorMsg(String s) {
        this.errorMsg = s;
    }
}

