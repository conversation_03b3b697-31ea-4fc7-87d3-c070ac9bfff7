package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.pro.dto.DtoAnalyseStatisticsData;

import java.util.Map;

/**
 * 检测拥挤操作接口
 *
 * <AUTHOR>
 * @version V5.2.0 2022/7/7
 */
public interface TestStatisticsService {

    /**
     * 分页查询数据
     *
     * @param page 分页
     * @param criteria 查询条件
     */
    void findTestData(PageBean<DtoAnalyseStatisticsData> page, BaseCriteria criteria);

    /**
     * 查询统计图像数据
     * @param pageBean 分页数据
     * @param criteria 查询条件
     * @return 查询结果
     */
    Map<String,Object> findCircularChart(PageBean<DtoAnalyseStatisticsData> pageBean, BaseCriteria criteria);
}
