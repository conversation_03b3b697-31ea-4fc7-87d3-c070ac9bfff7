package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.pro.dto.DtoSampleFolderEvaluate;
import com.sinoyd.lims.pro.repository.SampleFolderEvaluateRepository;
import com.sinoyd.lims.pro.service.SampleFolderEvaluateService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * SampleFolderEvaluate操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/06/14
 * @since V100R001
 */
@Service
public class SampleFolderEvaluateServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoSampleFolderEvaluate, String, SampleFolderEvaluateRepository> implements SampleFolderEvaluateService {

    @Override
    @Transactional
    public DtoSampleFolderEvaluate save(DtoSampleFolderEvaluate entity) {
        DtoSampleFolderEvaluate result = repository.findBySampleFolderIdAndTestId(entity.getSampleFolderId(), entity.getTestId());
        if (result != null) {
            entity.setId(result.getId());
        }
        return super.save(entity);
    }

    /**
     * 批量保存
     *
     * @param folderEvaluateList evaluateList
     * @return 数据集合
     */
    @Transactional
    @Override
    public List<DtoSampleFolderEvaluate> saveList(List<DtoSampleFolderEvaluate> folderEvaluateList) {
        Set<String> folderIds = folderEvaluateList.stream().map(DtoSampleFolderEvaluate::getSampleFolderId).collect(Collectors.toSet());
        Set<String> testIds = folderEvaluateList.stream().map(DtoSampleFolderEvaluate::getTestId).collect(Collectors.toSet());
        List<DtoSampleFolderEvaluate> evaluateList = repository.findBySampleFolderIdInAndTestIdIn(folderIds, testIds);
        evaluateList.forEach(p -> {
            Optional<DtoSampleFolderEvaluate> evaluateOptional = folderEvaluateList.stream()
                    .filter(f -> p.getSampleFolderId().equals(f.getSampleFolderId())
                            && p.getTestId().equals(f.getTestId())).findFirst();
            evaluateOptional.ifPresent(dtoSampleFolderEvaluate -> dtoSampleFolderEvaluate.setId(p.getId()));
        });
        return super.save(folderEvaluateList);
    }
}
