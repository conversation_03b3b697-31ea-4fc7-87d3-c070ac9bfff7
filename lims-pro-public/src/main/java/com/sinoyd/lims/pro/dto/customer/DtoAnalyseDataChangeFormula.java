package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 更换公式的DTO
 * <AUTHOR>
 * @version V1.0.0 2019/12/16
 * @since V100R001
 */
@Data
public class DtoAnalyseDataChangeFormula {

    /**
     * 公式id
     */
    private String formulaId;

    /**
     * 现场领样单id
     */
    private String subId;

    /**
     * 分析数据ids
     */
    private List<String> analyzeDataIds = new ArrayList<>();
}
