package com.sinoyd.lims.pro.dto.customer;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 质控数
 * <AUTHOR>
 * @version V1.0.0 2020/02/11
 * @since V100R001
 */
@Data
public class DtoQCCompareCount {

    private Date analyzeTime;


    /**
     * 质控样数
     */
    private Long sampleCount;


    /**
     * 该构造函数用到QCCompareServiceImpl 下的 findQCCompareStatistics 方法，如果要修改需两边统一改正
     *
     * @param analyzeTime 分析时间
     * @param sampleCount 样品数
     */
    public DtoQCCompareCount(Date analyzeTime, Long sampleCount) {
        this.analyzeTime = analyzeTime;
        this.sampleCount = sampleCount;
    }
}
