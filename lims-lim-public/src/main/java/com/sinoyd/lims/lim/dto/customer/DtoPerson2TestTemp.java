package com.sinoyd.lims.lim.dto.customer;

import com.sinoyd.lims.lim.dto.lims.DtoPerson2Test;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 *  测试人员配置批量设置的模版
 * <AUTHOR>
 * @version V1.0.0 2019/12/21
 * @since V100R001
 */
@Data
public class DtoPerson2TestTemp {

    /**
     * 相关的测试人员
     */
    private List<DtoPerson2Test> person2Tests = new ArrayList<>();

    /**
     * 测试人员的ids
     */
    private List<String> testIds = new ArrayList<>();
}
