package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 样品参数保存传参
 * <AUTHOR>
 * @version V1.0.0 2019/12/20
 * @since V100R001
 */
@Data
public class DtoSampleParamsTemp {
    /**
     * 参数配置
     */
    private DtoParamsConfig paramsConfig;

    /**
     * 样品id集合
     */
    private List<String> sampleIds = new ArrayList<>();

    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 点位id
     */
    private String sampleFolderId;

    /**
     * 参数值
     */
    private String paramsValue = "";

    /**
     * 是否公共
     */
    private Boolean isPublic;

    /**
     * 分组id
     */
    private String groupId = UUIDHelper.GUID_EMPTY;
}
