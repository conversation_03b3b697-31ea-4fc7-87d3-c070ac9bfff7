package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.sinoyd.base.dto.customer.DtoImportRecordConfig2Test;
import com.sinoyd.base.utils.poi.ExcelStyle;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.lims.lim.service.DownLoadRecoedConfig2TestTemplateService;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Map;

/**
 * 原始记录单与测试项目关系模板下载接口实现
 * <AUTHOR>
 * @version V1.0.0 2023/10/9
 * @since V100R001
 */
@Service
public class DownLoadRecoedConfig2TestTemplateServiceImpl implements DownLoadRecoedConfig2TestTemplateService {
    @Override
    public void downLoadTestTemplate(HttpServletResponse response, Map<String, String> sheetNames, String fileName) {
        // 第一张Sheet参数
        ExportParams params1 = new ExportParams();
        // 设置第一张Sheet样式
        params1.setStyle(ExcelStyle.class);
        // 设置sheet名
        params1.setSheetName(sheetNames.get("firstName"));
        //导出模板
        PoiExcelUtils.exportExcel(new ArrayList<>(),DtoImportRecordConfig2Test.class,"原始记录单与测试项目关联模板",params1,response);
    }
}
