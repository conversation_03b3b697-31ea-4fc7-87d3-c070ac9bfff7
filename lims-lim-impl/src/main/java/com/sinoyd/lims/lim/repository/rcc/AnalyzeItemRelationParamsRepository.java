package com.sinoyd.lims.lim.repository.rcc;

import java.util.Collection;
import java.util.List;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeItemRelationParams;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

/**
 * 分析项目关系参数仓储
 *
 * <AUTHOR>
 * @version v1.0.0 2019/1/31
 * @since v100R001
 */
public interface AnalyzeItemRelationParamsRepository
        extends IBaseJpaPhysicalDeleteRepository<DtoAnalyzeItemRelationParams, String> {

    /**
     * 通过relationId获取参数
     *
     * @param relationIds 删除的关系ids
     * @return 返回分析项目关系参数集合
     */
    @Query("select p from DtoAnalyzeItemRelationParams p where p.relationId in :relationIds")
    List<DtoAnalyzeItemRelationParams> getListByRelationIds(@Param("relationIds") List<String> relationIds);

    /**
     * 通过relationId获取参数
     *
     * @param relationId 删除的关系ids
     * @return 返回分析项目关系参数集合
     */
    @Query("select p from DtoAnalyzeItemRelationParams p where p.relationId = :relationId order by p.orderNum desc")
    List<DtoAnalyzeItemRelationParams> getList(@Param("relationId") String relationId);

    /**
     * 删除该排序下所有的分析项目
     *
     * @param relationId 关系id
     * @param idList     分析项目id
     * @return 返回分析项目id
     */
    @Transactional
    @Modifying
    @Query("delete from DtoAnalyzeItemRelationParams p where p.relationId = :relationId and p.analyzeItemId in :idList")
    Integer deleteAnalyzeItemId(@Param("relationId") String relationId, @Param("idList") Collection<String> idList);

    /**
     * 删除该排序下所有的分析项目
     *
     * @param relationId 关系id
     * @return 返回分析项目id
     */
    @Transactional
    @Modifying
    @Query("delete from DtoAnalyzeItemRelationParams p where p.relationId = :relationId ")
    Integer deleteByRelationId(@Param("relationId") String relationId);


    /**
     * 根据关系id获取相关的参数
     *
     * @param relationId 关系id
     * @return 返回相应的参数数据
     */
    List<DtoAnalyzeItemRelationParams> findByRelationId(String relationId);

    /**
     * 根据分析项目查询
     *
     * @param analyzeItemIds 分析项目id集合
     * @return 查询到的数据
     */
    List<DtoAnalyzeItemRelationParams> findByAnalyzeItemIdIn(Collection<String> analyzeItemIds);
}