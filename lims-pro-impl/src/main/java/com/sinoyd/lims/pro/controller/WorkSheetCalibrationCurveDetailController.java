package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.WorkSheetCalibrationCurveDetailService;
import com.sinoyd.lims.pro.criteria.WorkSheetCalibrationCurveDetailCriteria;
import com.sinoyd.lims.pro.dto.DtoWorkSheetCalibrationCurveDetail;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * WorkSheetCalibrationCurveDetail服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: WorkSheetCalibrationCurveDetail服务")
 @RestController
 @RequestMapping("api/pro/workSheetCalibrationCurveDetail")
 public class WorkSheetCalibrationCurveDetailController extends BaseJpaController<DtoWorkSheetCalibrationCurveDetail, String,WorkSheetCalibrationCurveDetailService> {


    /**
     * 分页动态条件查询WorkSheetCalibrationCurveDetail
     * @param workSheetCalibrationCurveDetailCriteria 条件参数
     * @return RestResponse<List<WorkSheetCalibrationCurveDetail>>
     */
     @ApiOperation(value = "分页动态条件查询WorkSheetCalibrationCurveDetail", notes = "分页动态条件查询WorkSheetCalibrationCurveDetail")
     @GetMapping
     public RestResponse<List<DtoWorkSheetCalibrationCurveDetail>> findByPage(WorkSheetCalibrationCurveDetailCriteria workSheetCalibrationCurveDetailCriteria) {
         PageBean<DtoWorkSheetCalibrationCurveDetail> pageBean = super.getPageBean();
         RestResponse<List<DtoWorkSheetCalibrationCurveDetail>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, workSheetCalibrationCurveDetailCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询WorkSheetCalibrationCurveDetail
     * @param id 主键id
     * @return RestResponse<DtoWorkSheetCalibrationCurveDetail>
     */
     @ApiOperation(value = "按主键查询WorkSheetCalibrationCurveDetail", notes = "按主键查询WorkSheetCalibrationCurveDetail")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoWorkSheetCalibrationCurveDetail> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoWorkSheetCalibrationCurveDetail> restResponse = new RestResponse<>();
         DtoWorkSheetCalibrationCurveDetail workSheetCalibrationCurveDetail = service.findOne(id);
         restResponse.setData(workSheetCalibrationCurveDetail);
         restResponse.setRestStatus(StringUtil.isNull(workSheetCalibrationCurveDetail) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增WorkSheetCalibrationCurveDetail
     * @param workSheetCalibrationCurveDetail 实体列表
     * @return RestResponse<DtoWorkSheetCalibrationCurveDetail>
     */
     @ApiOperation(value = "新增WorkSheetCalibrationCurveDetail", notes = "新增WorkSheetCalibrationCurveDetail")
     @PostMapping
     public RestResponse<DtoWorkSheetCalibrationCurveDetail> create(@RequestBody @Validated DtoWorkSheetCalibrationCurveDetail workSheetCalibrationCurveDetail) {
         RestResponse<DtoWorkSheetCalibrationCurveDetail> restResponse = new RestResponse<>();
         restResponse.setData(service.save(workSheetCalibrationCurveDetail));
         return restResponse;
      }

     /**
     * 新增WorkSheetCalibrationCurveDetail
     * @param workSheetCalibrationCurveDetail 实体列表
     * @return RestResponse<DtoWorkSheetCalibrationCurveDetail>
     */
     @ApiOperation(value = "修改WorkSheetCalibrationCurveDetail", notes = "修改WorkSheetCalibrationCurveDetail")
     @PutMapping
     public RestResponse<DtoWorkSheetCalibrationCurveDetail> update(@RequestBody  @Validated  DtoWorkSheetCalibrationCurveDetail workSheetCalibrationCurveDetail) {
         RestResponse<DtoWorkSheetCalibrationCurveDetail> restResponse = new RestResponse<>();
         restResponse.setData(service.update(workSheetCalibrationCurveDetail));
         return restResponse;
      }

    /**
     * "根据id批量删除WorkSheetCalibrationCurveDetail
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除WorkSheetCalibrationCurveDetail", notes = "根据id批量删除WorkSheetCalibrationCurveDetail")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }