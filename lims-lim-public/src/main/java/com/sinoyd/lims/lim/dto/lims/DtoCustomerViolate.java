package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.CustomerViolate;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoCustomerViolate实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/6/2
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_CustomerViolate")
@Data
@DynamicInsert
public class DtoCustomerViolate extends CustomerViolate {
    private static final long serialVersionUID = 1L;

    /**
     * 创建人姓名
     */
    @Transient
    private String creatorName;
    /**
     * 登记人姓名
     */
    @Transient
    private String registerPersonName;
    /**
     * 企业名称
     */
    @Transient
    private String enterpriseName;
    /**
     * 处理人姓名
     */
    @Transient
    private String handlePersonName;
}