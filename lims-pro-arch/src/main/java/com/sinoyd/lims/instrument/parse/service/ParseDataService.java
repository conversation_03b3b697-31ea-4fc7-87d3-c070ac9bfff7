package com.sinoyd.lims.instrument.parse.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.instrument.parse.dto.DtoParseData;

import java.util.List;

/**
 * 仪器解析数据业务访问接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2021/7/26
 */
public interface ParseDataService extends IBaseJpaService<DtoParseData, String> {
    /**
     * 根据样品编号列表查询解析数据
     *
     * @param sampleCodeList 样品编号列表
     * @return 解析数据列表
     */
    List<DtoParseData> findParseData(List<String> sampleCodeList);

    /**
     * 根据日志id查询解析数据
     *
     * @param logId 日志id
     * @return 解析数据列表
     */
    List<DtoParseData> findParseData(String logId, String gatherCode, String analyzeItemName, String paramName);

    /**
     * 检查工作单是否保存过
     *
     * @param worksheetFolderId 工作单id
     * @return true: 保存过; false: 未保存过
     */
    Boolean checkDuplicateSync(String worksheetFolderId);
}
