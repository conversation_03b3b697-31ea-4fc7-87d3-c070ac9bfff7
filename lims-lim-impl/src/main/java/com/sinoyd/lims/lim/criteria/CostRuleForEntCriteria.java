package com.sinoyd.lims.lim.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * CostRuleForEnt查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CostRuleForEntCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private String id;
    /**
     * 关键字(企业名称)
     */
    private String key;

    @Override
    public String getCondition() {
        /**
         * 清除条件数据
         */
        values.clear();
        StringBuilder condition = new StringBuilder();
        condition.append(" and p.entId = e.id");
        if (StringUtil.isNotEmpty(this.id)) {
            condition.append(" and id = :id");
            values.put("id", this.id);
        }
        if (StringUtil.isNotEmpty(key)) {
            condition.append(" and e.name like :key");
            values.put("key", "%" + this.key + "%");
        }
        return condition.toString();
    }
}