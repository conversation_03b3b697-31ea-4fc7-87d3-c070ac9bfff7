package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.Contract;

import java.util.Date;

import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoContract实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_Contract") 
 @Where(clause = "isDeleted = 0")
 @Data
 @DynamicInsert
 public  class DtoContract extends Contract {
    /**
     * 计划收款日期
     */
    @Transient
    private Date collectDate;

    /**
     * 是否提醒
     */
    @Transient
    private Boolean isRemind;
}