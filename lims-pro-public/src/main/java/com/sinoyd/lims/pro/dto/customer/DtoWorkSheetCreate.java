package com.sinoyd.lims.pro.dto.customer;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 检测单创建的dto
 * <AUTHOR>
 * @version V1.0.0 2019/12/02
 * @since V100R001
 */
@Data
public class DtoWorkSheetCreate {


    /**
     * 测试项目ids（批量创建检测单所用）
     */
    private List<String> testIds;


    /**
     * 创建单个的检测单所用
     */
    private String testId;
    /**
     * 分析数据id 创建单个的检测单所用
     */
    private List<String> analyseDataIds = new ArrayList<>();

    /**
     * 分析人员id
     */
    private String analystId;

    /**
     * 人员姓名
     */
    private String analystName;

    /**
     * 是否需要制备
     */
    private Boolean isPreparation;

    /**
     * 分析时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date analyzeTime;
}
