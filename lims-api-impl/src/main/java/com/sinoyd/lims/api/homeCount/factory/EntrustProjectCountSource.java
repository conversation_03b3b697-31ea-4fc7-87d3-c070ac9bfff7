package com.sinoyd.lims.api.homeCount.factory;

import com.sinoyd.lims.api.constant.IHomeConstant;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

/**
 * 委托监测代办数字
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
@Component(IHomeConstant.ProjectCountSourceKey.ENTRUST_PROJECT)
public class EntrustProjectCountSource extends AbsCountSource {

    @Override
    public long homeCount() {
        //项目类型
        String code = "projectRegisterPage";
//        String[] values = new String[]{"WT", "SY", "LX", "QLC"};
        String[] values = new String[]{"WT", "LX", "QLC"};
        String projectTypeIds = fieldMonitoringService.getProjectTypeList(code, values)
                .stream().map(p -> p.get("id").toString()).collect(Collectors.joining(","));
        return getProjectList(0, projectTypeIds);
    }
}
