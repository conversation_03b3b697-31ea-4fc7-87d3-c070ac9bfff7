package com.sinoyd.lims.lim.dto.customer;

import lombok.Data;

import java.util.List;

/**
 * DtoAnalyzeCertHistory实体
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since  2024/11/25
 */
@Data
public class DtoAnalyzeCertHistory {
    /**
     * 检测类型
     */
    private String sampleType;

    /**
     * 分析项目
     */
    private String analyzeItem;

    /**
     * 分析方法
     */
    private String analyzeMethod;

    /**
     * 标准编号
     */
    private String countryStandard;

    /**
     * 分析人员
     */
    private String analystPeople;

    /**
     * 上岗证编号
     */
    private String certCode;

    /**
     * 上岗证有效期
     */
    private String certEffectiveTime;

    /**
     * 持证状态
     */
    private String status;

    /**
     * 关联历史附件标识集合
     */
    private List<String> historyFileIds;
}
