package com.sinoyd.lims.strategy.context.schemeSynchronization;

import com.sinoyd.lims.pro.dto.DtoSampleFolderTemplate;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SampleFolderContext {

    /**
     * 修改点位方案
     *
     * @param operateType        修改类型
     * @param folderTemplateList 修改点位内容
     */
    void synchronizationSampleFolder(Integer operateType, String projectId, List<DtoSampleFolderTemplate> folderTemplateList);
}
