package com.sinoyd.lims.pro.verify;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecordParamInfo;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.strategy.strategy.dataValidator.AbsDataValidator;
import com.sinoyd.lims.pro.util.VerifyUtils;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 现场任务导入参数及分析项目数据校验
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/23
 * @since V100R001
 */
@Data
public class FieldDataVerifyHandle implements IExcelVerifyHandler<Map<String, Object>> {

    private List<DtoSample> sampleList;
    private Map<String, DtoParamsConfig> paramsConfigMap;
    private Map<String, DtoReceiveSampleRecordParamInfo> paramInfoMap;
    private Map<String, List<DtoAnalyseData>> analyseDataMap;
    private List<AbsDataValidator> dataValidatorList;
    private Map<String, Integer> rowNumMap;

    public FieldDataVerifyHandle(List<DtoSample> sampleList, Map<String, DtoParamsConfig> paramsConfigMap, Map<String, DtoReceiveSampleRecordParamInfo> paramInfoMap,
                                 Map<String, List<DtoAnalyseData>> analyseDataMap, List<AbsDataValidator> dataValidatorList, Map<String, Integer> rowNumMap) {
        this.sampleList = sampleList;
        this.paramsConfigMap = paramsConfigMap;
        this.paramInfoMap = paramInfoMap;
        this.analyseDataMap = analyseDataMap;
        this.dataValidatorList = dataValidatorList;
        this.rowNumMap = rowNumMap;
    }

    @Override
    public ExcelVerifyHandlerResult verifyHandler(Map<String, Object> map) {
        String sampleCode = (map.containsKey("样品编号") && StringUtil.isNotNull(map.get("样品编号"))) ? map.get("样品编号").toString() : "";
        int rowNum = this.rowNumMap.getOrDefault(sampleCode, 0);
        //校验结果
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        //校验错误信息
        StringBuilder failureStr = new StringBuilder("第" + rowNum + "行数据校验错误");
        //验证样品编号，点位名称列数据是否正确
        checkSampleCodeFolderName(map, failureStr, result);
        //验证参数格式是否正确
        checkParamFormat(map, failureStr, result);
        result.setMsg(failureStr.toString());
//        if (result.isSuccess()) {
//            result.setSuccess(false);
//            result.setMsg("");
//        }
        return result;
    }

    /**
     * 验证样品编号，点位名称列数据是否正确
     *
     * @param map        行数据对象
     * @param failureStr 校验错误信息
     * @param result     校验结果
     */
    private void checkSampleCodeFolderName(Map<String, Object> map, StringBuilder failureStr, ExcelVerifyHandlerResult result) {
        if (!VerifyUtils.checkEmptyRow(map)) {
            String sampleCode = (map.containsKey("样品编号") && StringUtil.isNotNull(map.get("样品编号"))) ? map.get("样品编号").toString() : "";
            String folderName = (map.containsKey("点位名称") && StringUtil.isNotNull(map.get("点位名称"))) ? map.get("点位名称").toString() : "";
            if (StringUtil.isEmpty(sampleCode)) {
                result.setSuccess(false);
                failureStr.append("；样品编号不能为空 ");
            }
            DtoSample sample = this.sampleList.stream().filter(p -> sampleCode.equals(p.getCode())).findFirst().orElse(null);
            if (StringUtil.isNull(sample)) {
                result.setSuccess(false);
                failureStr.append("；送样单中不存在该样品 ");
            } else {
                if (!folderName.equals(sample.getRedFolderName())) {
                    result.setSuccess(false);
                    failureStr.append("：点位名称不匹配 ");
                }
            }
            if (StringUtil.isEmpty(folderName)) {
                result.setSuccess(false);
                failureStr.append("：点位名称不能为空 ");
            }
        }
    }

    /**
     * 验证样品编号，点位名称列数据是否正确
     *
     * @param map        行数据对象
     * @param failureStr 校验错误信息
     * @param result     校验结果
     */
    private void checkParamFormat(Map<String, Object> map, StringBuilder failureStr, ExcelVerifyHandlerResult result) {
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String paramName = entry.getKey(), paramVal = StringUtil.isNotNull(entry.getValue()) ? entry.getValue().toString() : "";
            if ("样品编号".equals(paramName) || "点位名称".equals(paramName)) {
                continue;
            }
            DtoReceiveSampleRecordParamInfo paramInfo = this.paramInfoMap.get(paramName);
            if (StringUtil.isNull(paramInfo)) {
                result.setSuccess(false);
                failureStr.append("现场数据模板中不存在参数：").append(paramName).append(" ");
                continue;
            }
            //根据模板参数类型判断是参数还是分析项目
            if (EnumLIM.EnumParamsType.样品参数.getValue().equals(paramInfo.getType())
                    || EnumLIM.EnumParamsType.点位参数.getValue().equals(paramInfo.getType())
                    || EnumLIM.EnumParamsType.公共参数.getValue().equals(paramInfo.getType())) {
                //校验样品及点位参数
                DtoParamsConfig paramsConfig = this.paramsConfigMap.get(paramInfo.getParamId());
                if (StringUtil.isNull(paramsConfig)) {
                    result.setSuccess(false);
                    failureStr.append("送样单中不存在参数：").append(paramName).append(" ");
                    continue;
                }
                if (paramsConfig.getIsRequired() && StringUtil.isEmpty(paramVal)) {
                    result.setSuccess(false);
                    failureStr.append("参数：").append(paramName).append("不能为空 ");
                    continue;
                }
                //根据控件类型校验参数值
                checkParamValByDefaultControl(paramVal, paramsConfig, failureStr, result);
            } else {
                //校验分析项目检测结果
                checkAnalyseData(paramName, map, paramInfo, failureStr, result);
            }
        }
    }

    /**
     * 根据控件类型校验参数值
     *
     * @param paramVal     参数值
     * @param paramsConfig 参数配置对象
     * @param failureStr   校验错误信息
     * @param result       校验结果
     */
    private void checkParamValByDefaultControl(String paramVal, DtoParamsConfig paramsConfig, StringBuilder failureStr, ExcelVerifyHandlerResult result) {
        if (StringUtil.isNotEmpty(paramVal)) {
            Integer defaultControl = paramsConfig.getDefaultControl();
            if (StringUtil.isNotNull(defaultControl) && (defaultControl.equals(EnumLIM.EnumDefaultControl.下拉框控件.getValue())
                    || defaultControl.equals(EnumLIM.EnumDefaultControl.下拉框控件.getValue()) || defaultControl.equals(EnumLIM.EnumDefaultControl.RadioGroup控件.getValue())
                    || defaultControl.equals(EnumLIM.EnumDefaultControl.CheckBoxGroup控件.getValue()) || defaultControl.equals(EnumLIM.EnumDefaultControl.开关控件.getValue())
                    || defaultControl.equals(EnumLIM.EnumDefaultControl.下拉树控件.getValue()))) {
                defaultControl = EnumLIM.EnumDefaultControl.下拉框控件.getValue();
            }
            final int finalControl = defaultControl;
            AbsDataValidator validator = dataValidatorList.stream().filter(p -> p.getControlType().equals(finalControl)).findFirst().orElse(null);
            if (StringUtil.isNotNull(validator)) {
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("dataSource", paramsConfig.getDataSource());
                if (!validator.validate(paramVal, paramMap)) {
                    result.setSuccess(false);
                    failureStr.append("参数值：").append(paramVal).append(" 格式不正确 ");
                }
            }
        }
    }

    /**
     * 根据控件类型校验参数值
     *
     * @param paramName  参数名称
     * @param map        行数据map
     * @param failureStr 校验错误信息
     * @param result     校验结果
     */
    private void checkAnalyseData(String paramName, Map<String, Object> map, DtoReceiveSampleRecordParamInfo paramInfo,
                                  StringBuilder failureStr, ExcelVerifyHandlerResult result) {
        String sampleCode = (map.containsKey("样品编号") && StringUtil.isNotNull(map.get("样品编号"))) ? map.get("样品编号").toString() : "";
        if (StringUtil.isNotEmpty(sampleCode)) {
            DtoSample sample = this.sampleList.stream().filter(p -> sampleCode.equals(p.getCode())).findFirst().orElse(null);
            if (StringUtil.isNotNull(sample)) {
                List<DtoAnalyseData> analyseDataListForSample = analyseDataMap.getOrDefault(sample.getId(), new ArrayList<>());
                DtoAnalyseData analyseDataForSample = analyseDataListForSample.stream().filter(p -> paramInfo.getParamId().equals(p.getTestId())).findFirst().orElse(null);
                if (StringUtil.isNull(analyseDataForSample)) {
                    result.setSuccess(false);
                    failureStr.append("样品：").append(sample.getCode()).append(" 不存在分析项目：").append(paramName).append(" ");
                }
            }
        }
    }

    @Autowired
    @Lazy
    public void setDataValidatorList(List<AbsDataValidator> dataValidatorList) {
        this.dataValidatorList = dataValidatorList;
    }
}
