package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoTestPost;

import java.util.List;

/**
 * 测试岗位接口
 *
 * <AUTHOR>
 * @version V1.0.0 2022/4/21
 * @since V100R001
 */
public interface TestPostService extends IBaseJpaService<DtoTestPost, String> {

    /**
     * 查询测试岗位及其配置信息
     *
     * @param id 测试岗位id
     * @return 测试岗位及其配置信息
     */
    DtoTestPost findTestPost(String id, BaseCriteria testPostCriteria);

    /**
     * 修改测试岗位配置信息(人员配置，测试项目配置)
     *
     * @param testPost 测试岗位对象
     * @return 测试岗位对象
     */
    DtoTestPost updateConfig(DtoTestPost testPost);

    /**
     * 根据id删除测试岗位信息(包括人员配置，测试项目配置)
     *
     * @param id 测试岗位id
     */
    void deleteTestPost(String id);

    /**
     * 根据id删除测试岗位信息(包括人员配置，测试项目配置)
     *
     * @param ids 测试岗位id列表
     */
    void deleteTestPost(List<String> ids);

    /**
     * 获取岗位树
     *
     * @return 岗位树
     */
    List<DtoTestPost> getTestPostTree();

    /**
     * 根据人员id查找
     *
     * @param personId 人员id
     * @return 岗位列表
     */
    List<DtoTestPost> findByPerson(String personId);

    /**
     * 根据采样小组id和测试项目id删除数据
     *
     * @param testPostId  采样小组id
     * @param testIds     测试项目id集合
     */
    void deleteTest(String testPostId, List<String> testIds);

    /**
     * 根据采样小组id和测试项目id新增数据
     *
     * @param testPostId  采样小组id
     * @param testIds     测试项目id集合
     */
    void addTest(String testPostId, List<String> testIds);
}