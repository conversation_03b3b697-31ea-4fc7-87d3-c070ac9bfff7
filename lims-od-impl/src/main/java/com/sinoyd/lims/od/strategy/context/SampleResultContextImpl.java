package com.sinoyd.lims.od.strategy.context;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.od.dto.DtoLabGroup;
import com.sinoyd.lims.od.dto.DtoSampleResult;
import com.sinoyd.lims.od.dto.customer.DtoGroupSummaryTemp;
import com.sinoyd.lims.od.dto.customer.DtoSampleResultTemp;
import com.sinoyd.lims.od.enums.EnumOds;
import com.sinoyd.lims.od.enums.EnumOds;
import com.sinoyd.lims.od.strategy.sampleResult.AbsSampleResultStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Component
public class SampleResultContextImpl implements SampleResultContext {

    /**
     * 所有具体生成策略字典
     */
    private final Map<String, AbsSampleResultStrategy> sampleResultStrategyMap = new ConcurrentHashMap<>();

    @Autowired
    public SampleResultContextImpl(Map<String, AbsSampleResultStrategy> sampleResultStrategyMap) {
        this.sampleResultStrategyMap.putAll(sampleResultStrategyMap);
    }


    @Override
    public List<DtoGroupSummaryTemp> findDefaultGroupBySampleId(Integer taskType, String sampleId) {
        String beanName = verify(taskType);
        return this.sampleResultStrategyMap.get(beanName).findDefaultGroupBySampleId(taskType,sampleId);
    }


    /**
     * 获取实验组汇总信息
     *
     * @param taskType          任务类型
     * @param groupSummaryTemps 实验数据汇总临时传输实体
     * @return 样品结果
     */
    @Override
    public List<DtoLabGroup> findGroupSummary(Integer taskType, List<DtoGroupSummaryTemp> groupSummaryTemps) {
        String beanName = verify(taskType);
        return this.sampleResultStrategyMap.get(beanName).findGroupSummary(groupSummaryTemps);
    }


    /**
     * 计算样品结果
     *
     * @param taskType         任务类型
     * @param sampleResultTemp 样品结果汇总传输实体
     * @return 计算样品结果
     */
    @Override
    @Transactional
    public DtoSampleResult calculate(Integer taskType, DtoSampleResultTemp sampleResultTemp) {
        String beanName = verify(taskType);
        return this.sampleResultStrategyMap.get(beanName).calculate(sampleResultTemp);
    }

    /**
     * 检验方法的合法性
     *
     * @param taskType 任务类型枚举值
     * @return beanName
     */
    private String verify(Integer taskType) {
        String beanName = EnumOds.EnumTaskType.getBeanName(taskType);
        if (!StringUtil.isNotNull(this.sampleResultStrategyMap.get(beanName))) {
            throw new BaseException("调用方法不合法");
        }
        return beanName;
    }
}
