package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * Params2ParamsFormula实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="Params2ParamsFormula")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class Params2ParamsFormula implements BaseEntity,Serializable {

    private static final long serialVersionUID = 1L;

    public Params2ParamsFormula() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 记录单Id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("记录单Id")
    private String recordId;

    /**
     * 参数配置Id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("参数配置Id")
    private String paramsConfigId;

    /**
     * 对象id（测试公式id-记录单参数，测试项目id-检测单参数）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("对象id（测试公式id-记录单参数，测试项目id-检测单参数）")
    private String objectId;

    /**
     * 公式
     */
    @Column(length = 1000)
    @ApiModelProperty("公式")
    @Length(message = "公式{validation.message.length}", max = 1000)
    private String formula;

    /**
     * isDeleted
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("isDeleted")
    private Boolean isDeleted = false;

    /**
     * 是否启用
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("isEnabled")
    private Boolean isEnabled = false;


    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}