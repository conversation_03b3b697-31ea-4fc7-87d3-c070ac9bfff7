package com.sinoyd.lims.lim.service;

import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 项目类型操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/23
 * @since V100R001
 */
public interface ProjectTypeService extends IBaseJpaService<DtoProjectType, String> {

    /**
     * 根据项目类型id获取项目类型
     *
     * @param ids 项目类型ids
     * @return 返回项目类型
     */
    List<DtoProjectType> findRedisByIds(List<String> ids);

    /**
     * 根据编码获取项目类型
     *
     * @param code   编码
     * @param values 值
     */
    List<Map<String, Object>> getProjectTypeByCode(String code, String[] values);


    /**
     * 项目编号配置根据编码获取项目类型
     *
     * @param code   编码
     * @param values 值
     */
    List<Map<String, Object>> getProjectTypeForSerialIdentifierConfig(String code, String[] values, String[] projectTypeIds, Integer configType, Integer qcType, Integer qcGrade);

    /**
     * 项目编号配置根据编码获取项目类型
     *
     * @param code   编码
     * @param values 值
     */
    List<Map<String, Object>> getProjectTypeForSerialIdentifierConfigTree(String code, String[] values, String[] projectTypeIds, Integer configType, Integer qcType, Integer qcGrade);


    /**
     * 获取项目类型树
     */
    List<TreeNode> getProjectTree();

    /**
     * 获取项目类型对应编码值
     *
     * @param projectType 项目类型
     * @param code        编码
     */
    String getConfigValue(String projectType, String code);

    /**
     * 获取项目类型对应编码值
     * @param typeIds 类型ids
     * @param code 编码值
     * @return 对应数据
     */
    Map<String, String> getConfigValueByIds(Collection<String> typeIds, String code);

    /**
     * 根据工作流id获取项目类型
     *
     * @param workflowId 工作流id
     * @return 对应工作流id的项目类型
     */
    List<DtoProjectType> findByWorkflowId(String workflowId);

    /**
     * 根据编码获取项目类型列表
     *
     * @param code 编码
     * @return 实体列表
     */
    List<DtoProjectType> findByTypeCode(String code);

    /**
     * 根据项目类型id获取项目类型列表
     *
     * @param typeId 项目类型id
     * @return 实体列表
     */
    List<DtoProjectType> findByTypeId(String typeId);

    /**
     * 根据编码获取项目类型结构树
     *
     * @param code   编码
     * @param values 值
     */
    List<TreeNode> getProjectTreeByCode(String code, String[] values);
}