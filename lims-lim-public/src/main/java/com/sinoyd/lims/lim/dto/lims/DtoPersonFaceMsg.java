package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.PersonFaceMsg;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoPersonFaceMsg实体
 * <AUTHOR>
 * @version V1.0.0 2022/9/26
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_PersonFaceMsg")
 @Data
 @DynamicInsert
 public  class DtoPersonFaceMsg extends PersonFaceMsg {
   private static final long serialVersionUID = 1L;
 }