package com.sinoyd.lims.lim.dto.customer;

import com.sinoyd.lims.lim.dto.lims.DtoInstrumentGatherParams;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 仪器接入数据传输VO
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/18
 * @since V100R001
 */
@Data
public class DtoInstrumentGatherDataVo {


    /**
     * 仪器接入id
     */
    private String instrumentGatherId;

    /**
     * mn号码
     */
    private String mnNumber;

    /**
     * 通道序号
     */
    private String channel;

    /**
     * 结果列名集合
     */
    private List<DtoInstrumentGatherParams> columns;

    /**
     * 数结果据集合
     */
    private List<Map<String, Object>> resultDataList;

}
