package com.sinoyd.lims.lim.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * OAContract实体
 * <AUTHOR>
 * @version V1.0.0 2020/9/28
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="OAContract")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class OAContract implements BaseEntity,Serializable {

    private static final long serialVersionUID = 1L;

    public OAContract() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();


    /**
     * 合同Id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("合同Id")
    private String contractId;

    /**
     * 合同名称
     */
    @Column(nullable = false)
    @ApiModelProperty("合同名称")
    @Length(message = "合同名称{validation.message.length}", max = 255)
    private String contractName;

    /**
     * 合同编号
     */
    @Column(length = 50)
    @ApiModelProperty("合同编号")
    @Length(message = "合同编号{validation.message.length}", max = 50)
    private String contractCode;

    /**
     * 合同类型（常量：收款合同、委外合同    编码:LIM_ContractType）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("合同类型（常量：收款合同、委外合同    编码:LIM_ContractType）")
    @Length(message = "合同类型{validation.message.length}", max = 50)
    private String type;

    /**
     * 合同周期（常量：长期检测合同、单次检测合同    编码：LIM_ContractPeriod）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("合同周期（常量：长期检测合同、单次检测合同    编码：LIM_ContractPeriod）")
    @Length(message = "合同周期{validation.message.length}", max = 50)
    private String period;

    /**
     * 总金额
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("总金额")
    private BigDecimal totalAmount = new BigDecimal(0);

    /**
     * 业务员id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("业务员id")
    private String salesManId;

    /**
     * 业务员
     */
    @Column(length = 50)
    @ApiModelProperty("业务员")
    private String salesManName;

    /**
     * 签订日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("签订日期")
    private Date signDate;

    /**
     * 开始日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("开始日期")
    private Date timeBegin;

    /**
     * 结束日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("结束日期")
    private Date timeEnd;

    /**
     * 单位名称
     */
    @Column(length = 50)
    @ApiModelProperty("单位名称")
    @Length(message = "单位名称{validation.message.length}", max = 50)
    private String entName;

    /**
     * 联系人
     */
    @Column(length = 50)
    @ApiModelProperty("联系人")
    @Length(message = "联系人{validation.message.length}", max = 50)
    private String linkMan;

    /**
     * 联系方式
     */
    @Column(length = 50)
    @ApiModelProperty("联系方式")
    @Length(message = "联系方式{validation.message.length}", max = 50)
    private String linkPhone;

    /**
     * 地址
     */
    @Column(length = 100)
    @ApiModelProperty("地址")
    @Length(message = "地址{validation.message.length}", max = 100)
    private String address;

    /**
     * 工期要求说明
     */
    @ApiModelProperty("工期要求说明")
    @Length(message = "工期要求说明{validation.message.length}", max = 255)
    private String explains;

    /**
     * 注意事项
     */
    @ApiModelProperty("注意事项")
    @Length(message = "注意事项{validation.message.length}", max = 255)
    private String attentions;

    /**
     * 备注
     */
    @Column(length = 1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
    private String remark;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}