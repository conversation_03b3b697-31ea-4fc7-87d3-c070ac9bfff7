package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoFlowCalibrationParamData;

import java.util.List;

/**
 * FlowCalibrationParamData数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2024/11/14
 */
public interface FlowCalibrationParamDataRepository extends IBaseJpaRepository<DtoFlowCalibrationParamData,String> {
    /**
     * 根据行标识集合查询
     * @param flowCalibrationRowIds 行标识集合
     * @return List<DtoFlowCalibrationParamData>
     */
    List<DtoFlowCalibrationParamData> findByFlowCalibrationRowIdIn(List<String> flowCalibrationRowIds);
}
