package com.sinoyd.lims.lim.verify;


import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.dto.customer.DtoImportRecordConfig2Test;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig;
import com.sinoyd.lims.lim.service.transform.ImportCommonCheckImpl;
import com.sinoyd.lims.lim.utils.ImportUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 *  原始记录单与测试项目关系导入数据校验
 *
 * <AUTHOR>
 * @version V1.0.0 2023/10/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RecordConfig2TestVerifyHandler extends ImportCommonCheckImpl implements IExcelVerifyHandler<DtoImportRecordConfig2Test> {
    /**
     * 工具类
     */
    private final ImportUtils importUtils = new ImportUtils();

    /**
     *   excel数据重复校验容器
     */
    private List<DtoImportRecordConfig2Test> sheetExistDataList;

    private List<DtoTest> allTestList;

    private List<DtoSampleType> allSampleTypeList;

    private List<DtoRecordConfig> allRecordConfigList;

    @SneakyThrows
    @Override
    public ExcelVerifyHandlerResult verifyHandler(DtoImportRecordConfig2Test importRecordConfig2Test) {
       //导入数据处理,跳过空行,数据去除前后空格
        try {
            if (importUtils.checkObjectIsNull(importRecordConfig2Test)) {
                return new ExcelVerifyHandlerResult(true);
            }
            importUtils.strToTrim(importRecordConfig2Test);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }
        //校验结果
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        //总校验错误信息
        StringBuilder failStr = new StringBuilder("第" + (importRecordConfig2Test.getRowNum()+1) + "行数据校验有误");
        //必填校验
        importUtils.checkIsNull(result, importRecordConfig2Test.getAnalyzeItem(), "分析项目", failStr);
        importUtils.checkIsNull(result, importRecordConfig2Test.getAnalyzeMethod(), "分析项目", failStr);
        importUtils.checkIsNull(result, importRecordConfig2Test.getSampleType(), "检测类型", failStr);
        importUtils.checkIsNull(result, importRecordConfig2Test.getRecordNames(), "原始记录单", failStr);
        //避免因未填导致的问题
        if(result.isSuccess()){
            //本sheet内数据重复
            checkSeetDataRepeat(result,failStr,sheetExistDataList,importRecordConfig2Test, Arrays.asList("sampleType","analyzeItem","analyzeMethod"));
            //判定测试项目是否存在
            checkIsExistTest(result,failStr,importRecordConfig2Test,allSampleTypeList,allTestList);
            //判定原始记录单是否存在
            checkIsExistRecordConfig(result,failStr,importRecordConfig2Test,allRecordConfigList);
            //更新重复校验容器
            sheetExistDataList.add(importRecordConfig2Test);
        }
        //构建异常信息
        String failString = failStr.toString().replaceFirst("；", ":");
        result.setMsg(failString);
        return result;
    }

    /**
     * 判定原始记录单是否存在
     * @param result                       校验结果
     * @param failStr                      校验错误数据
     * @param importRecordConfig2Test      导入实体
     * @param allRecordConfigList          所有原始记录单
     */
    private void checkIsExistRecordConfig(ExcelVerifyHandlerResult result, StringBuilder failStr, DtoImportRecordConfig2Test importRecordConfig2Test, List<DtoRecordConfig> allRecordConfigList) {
        List<String> singleReportNameList = new ArrayList<>(Arrays.asList(importRecordConfig2Test.getRecordNames().split("；")));
        for (String recordName:singleReportNameList) {
            DtoRecordConfig dtoRecordConfig = allRecordConfigList.stream().filter(r->recordName.equals(r.getRecordName())).findFirst().orElse(null);
            if(dtoRecordConfig == null){
                result.setSuccess(false);
                failStr.append("； 原始记录单").append(recordName).append("在系统中不存在");
            }
        }
    }

    /**
     * 判定测试项目是否存在
     * @param result                     校验结果
     * @param failStr                    校验错误数据
     * @param importRecordConfig2Test    导入实体
     * @param allSampleTypeList          所有检测类型
     * @param allTestList                所有测试项目
     */
    private void checkIsExistTest(ExcelVerifyHandlerResult result, StringBuilder failStr, DtoImportRecordConfig2Test importRecordConfig2Test, List<DtoSampleType> allSampleTypeList, List<DtoTest> allTestList) {
        DtoSampleType dtoSampleType = allSampleTypeList.stream().filter(s->s.getTypeName().equals(importRecordConfig2Test.getSampleType())).findFirst().orElse(null);
        if(dtoSampleType!=null){
            DtoTest dtoTest = allTestList.stream().filter(t->t.getRedAnalyzeItemName().equals(importRecordConfig2Test.getAnalyzeItem())
                    &&t.getRedAnalyzeMethodName().equals(importRecordConfig2Test.getAnalyzeMethod())
                    &&dtoSampleType.getId().equals(t.getSampleTypeId()))
                    .findFirst().orElse(null);
            if(dtoTest==null){
                result.setSuccess(false);
                failStr.append("； 测试项目在系统中不存在");
            }
        }
        else{
            result.setSuccess(false);
            failStr.append("； 检测类型").append(importRecordConfig2Test.getSampleType()).append("在系统中不存在");
        }
    }
}
