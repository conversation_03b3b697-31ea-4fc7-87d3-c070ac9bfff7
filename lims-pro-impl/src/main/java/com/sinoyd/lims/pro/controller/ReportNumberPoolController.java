package com.sinoyd.lims.pro.controller;


import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.ReportNumberPoolCriteria;
import com.sinoyd.lims.pro.dto.DtoReport;
import com.sinoyd.lims.pro.dto.DtoReportNumberPool;
import com.sinoyd.lims.pro.service.ReportNumberPoolService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 报告编号池服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2023/8/1
 * @since V100R001
 */
@Api(tags = "示例: ReportNumberPool服务")
@RestController
@RequestMapping("api/pro/reportNumberPool")
public class ReportNumberPoolController extends BaseJpaController<DtoReportNumberPool, String, ReportNumberPoolService> {

    /**
     * 分页动态条件查询ReportNumberPool
     *
     * @param reportNumberPoolCriteria 条件参数
     * @return RestResponse<List < DtoReportNumberPool>>
     */
    @ApiOperation(value = "分页动态条件查询ReportNumberPool", notes = "分页动态条件查询ReportNumberPool")
    @GetMapping
    public RestResponse<List<DtoReportNumberPool>> findByPage(ReportNumberPoolCriteria reportNumberPoolCriteria) {
        PageBean<DtoReportNumberPool> pageBean = super.getPageBean();
        RestResponse<List<DtoReportNumberPool>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, reportNumberPoolCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }


    /**
     * 查询报告详情
     *
     * @param code 报告编号
     * @return RestResponse<DtoReportNumberPool>
     */
    @ApiOperation(value = "查询报告详情", notes = "查询报告详情")
    @GetMapping("/report")
    public RestResponse<DtoReport> queryReport(String code) {
        RestResponse<DtoReport> restResponse = new RestResponse<>();
        restResponse.setData(service.queryReport(code));
        return restResponse;
    }

    /**
     * 新增报告编号
     *
     * @param reportNumberPool 实体列表
     * @return RestResponse<DtoReportNumberPool>
     */
    @ApiOperation(value = "新增报告编号", notes = "新增报告编号")
    @PostMapping
    public RestResponse<DtoReportNumberPool> create(@RequestBody @Validated DtoReportNumberPool reportNumberPool) {
        RestResponse<DtoReportNumberPool> restResponse = new RestResponse<>();
        restResponse.setData(service.save(reportNumberPool));
        return restResponse;
    }

    /**
     * 批量新增报告编号
     *
     * @param reportNumberPool 实体列表
     * @return RestResponse<DtoReportNumberPool>
     */
    @ApiOperation(value = "批量新增报告编号", notes = "批量新增报告编号")
    @PostMapping("/batch")
    public RestResponse<List<DtoReportNumberPool>> batchCreate(@RequestBody DtoReportNumberPool reportNumberPool) {
        RestResponse<List<DtoReportNumberPool>> restResponse = new RestResponse<>();
        restResponse.setData(service.batchCreate(reportNumberPool));
        return restResponse;
    }

    /**
     * 获取最大流水号
     *
     * @param reportNumberPool 实体列表
     * @return RestResponse<DtoReportNumberPool>
     */
    @ApiOperation(value = "获取最大流水号", notes = "获取最大流水号")
    @PostMapping("/queryMaxNumber")
    public RestResponse<Map<String,Object>> queryMaxNumber(@RequestBody DtoReportNumberPool reportNumberPool) {
        RestResponse<Map<String,Object>> restResponse = new RestResponse<>();
        restResponse.setData(service.queryMaxNumber(reportNumberPool));
        return restResponse;
    }

    /**
     * 编号作废
     *
     * @param ids
     * @return
     */
    @ApiOperation(value = "根据id批量编号作废", notes = "根据id批量编号作废")
    @DeleteMapping("")
    public RestResponse<String> cancel(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setCount(service.cancel(ids));
        return restResponse;
    }

    /**
     * 取消作废
     *
     * @param ids
     * @return
     */
    @ApiOperation(value = "根据id批量取消作废", notes = "根据id批量取消作废")
    @PostMapping("/cancelVoid")
    public RestResponse<String> cancelVoid(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setCount(service.cancelVoid(ids));
        return restResponse;
    }

}
