package com.sinoyd.lims.lim.data.sync.strategy;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.lims.lim.data.sync.dto.DtoDataCompareResult;
import com.sinoyd.lims.lim.data.sync.strategy.base.AbsDataSync;
import com.sinoyd.lims.lim.dto.rcc.DtoParams2ParamsFormula;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.Params2ParamsFormulaRepository;
import com.sinoyd.lims.lim.service.Params2ParamsFormulaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 原始记录单参数配置同步
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/5/25
 */
@Component
@DependsOn({"springContextAware"})
@Order(24)
@Slf4j
public class Params2ParamsFormulaSync extends AbsDataSync<DtoParams2ParamsFormula> {

    @Autowired
    private Params2ParamsFormulaService service;

    @Autowired
    private Params2ParamsFormulaRepository repository;


    /**
     * 比较数据
     *
     * @param recordIds 原始记录单配置Id
     * @return 比较结果
     */
    @Override
    public List<DtoDataCompareResult<DtoParams2ParamsFormula>> compareData(List<String> recordIds) {
        List<DtoParams2ParamsFormula> projectData = service.findAll();
        List<DtoParams2ParamsFormula> standardData = queryStandardData();
        if (StringUtil.isNotEmpty(recordIds)&&StringUtil.isNotEmpty(standardData)){
            standardData = standardData.stream().filter(p->recordIds.contains(p.getRecordId())).collect(Collectors.toList());
        }

        return compareData(standardData,projectData);
    }

    /**
     * 同步数据
     *
     * @param recordIds 原始记录单配置Id
     * @param webSocketServer websockets服务端
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncData(List<String> recordIds, WebSocketServer webSocketServer) {
        List<DtoDataCompareResult<DtoParams2ParamsFormula>> compareResult = compareData(recordIds);
        Optional<DtoDataCompareResult<DtoParams2ParamsFormula>> resultOptional = compareResult.parallelStream().filter(p -> COMPARE_CATEGORY[0].equals(p.getCategory())
                && p.getCount() > 0).findFirst();
        resultOptional.ifPresent(r -> {
            //已同步记录数
            int i = 0;
            DtoParams2ParamsFormula errorDto = null;
            try {
                for (DtoParams2ParamsFormula dtoParams2ParamsFormula : r.getAddDataList()) {
                    errorDto = dtoParams2ParamsFormula;
                    if (repository.findOne(dtoParams2ParamsFormula.getId()) != null) {
                        service.update(dtoParams2ParamsFormula);
                    } else {
                        service.save(dtoParams2ParamsFormula);
                    }
                    webSocketServer.sendMessage(String.format("同步" + getItemName() + "(%d/%d)", ++i, r.getCount()));
                }
            } catch (Exception e) {
                if (errorDto != null) {
                    webSocketServer.sendMessage("同步" + getItemName() + "发生错误, ID = " + errorDto.getId() + ", 错误信息: "
                            + e.getMessage());
                }
                log.error(e.getMessage(), e);
                throw new BaseException("同步" + getItemName() + "发生错误");
            }
        });
    }

    /**
     * 是否必须同步
     *
     * @return 返回结果
     */
    @Override
    public boolean mustSync() {
        return true;
    }

    /**
     * 获取显示名称
     *
     * @return 返回结果
     */
    @Override
    public String getItemName() {
        return EnumLIM.EnumDataSyncType.原始记录单参数公式配置.name();
    }

    /**
     * 获取排序值
     *
     * @return 返回结果
     */
    @Override
    public Integer getOrderNum() {
        return EnumLIM.EnumDataSyncType.原始记录单参数公式配置.getValue();
    }

    /**
     * 获取请求路径
     *
     * @return 返回结果
     */
    @Override
    public String getStandardDataQueryUrl() {
        return "/api/sinoyd-lims/lim/params2ParamsFormula";
    }

    /**
     * 获取同步类型
     *
     * @return 返回结果
     */
    @Override
    public Integer getSyncDataType() {
        return EnumLIM.EnumDataSyncType.原始记录单参数公式配置.getValue();
    }

    /**
     * 查询
     *
     * @return 查询结果
     */
    @Override
    public List<DtoParams2ParamsFormula> queryStandardData() {
        String url = standardDataConfig.getHost() + getStandardDataQueryUrl() +
                "/all";
        List<Map<String, Object>> resultMapList = (List<Map<String, Object>>) queryStandardData(url).getBody().get("data");
        return convertObject(resultMapList);
    }
}
