package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.FileAuditStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * FileAuditStatus实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/05/19
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "tb_pro_fileAuditStatus")
@Data
@DynamicInsert
public class DtoFileAuditStatus extends FileAuditStatus {

}