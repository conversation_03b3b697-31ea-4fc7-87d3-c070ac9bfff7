package com.sinoyd.base.repository.rcc;

import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.frame.repository.IBaseJpaRepository;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * 分析项目管理仓储
 * <AUTHOR>
 * @version V1.0.0 2019/1/30
 * @since V100R001
 */

public interface AnalyzeItemRepository extends IBaseJpaRepository<DtoAnalyzeItem, String> {

    /**
     * 重写获取所有的方法
     *
     * @return 返回未删除分析项目信息
     */
    @Override
    @Query("select p from DtoAnalyzeItem p where p.isDeleted = 0")
    List<DtoAnalyzeItem> findAll();


    /**
     * 根据分析项目名称获取分析项目
     *
     * @param analyzeItemName 分析项目名称
     * @return 分析项目实体
     */
    @Query("select d from DtoAnalyzeItem d where d.isDeleted = 0 and d.analyzeItemName = :analyzeItemName")
    DtoAnalyzeItem getByAnalyzeItemName(@Param("analyzeItemName") String analyzeItemName);

    /**
     * 根据名称与id获取重复的条数
     *
     * @param id              主键ID
     * @param analyzeItemName 分析项目名称
     * @return 返回个数
     */
    @Query("select count(p) from DtoAnalyzeItem p where p.id <> :id and p.analyzeItemName = :analyzeItemName and p.isDeleted = 0")
    Integer getCountByName(@Param("id") String id, @Param("analyzeItemName") String analyzeItemName);


    /**
     * 根据id数组获取分析项目
     *
     * @param ids 分析项目ids
     * @return 返回分析项目信息
     */
    @Query("select d from DtoAnalyzeItem d where d.isDeleted = 0 and d.id in :ids")
    @Override
    List<DtoAnalyzeItem> findAll(@Param("ids") Iterable<String> ids);


    /**
     * 返回所有的带假删的分析项目信息
     *
     * @return 返回带删除的分析项目信息
     */
    @Query("select p from DtoAnalyzeItem p")
    List<DtoAnalyzeItem> findAllDeleted();

    /**
     * 返回所有的带假删的分析项目信息
     *
     * @param ids 人员的ids
     * @return 返回带删除的分析项目信息
     */
    @Query("select p from DtoAnalyzeItem p where p.id in :ids")
    List<DtoAnalyzeItem> findAllDeleted(@Param("ids") List<String> ids);

    /**
     * 查询分析项目存在,但是拼音字段为空的数据
     * @return 分析项目存在,但是拼音字段为空的数据
     */
    @Query("select p from DtoAnalyzeItem p where (p.fullPinYin is null or p.fullPinYin = '' or p.pinYin is null or p.pinYin = '') and p.analyzeItemName is not null")
    List<DtoAnalyzeItem> findByAnalyzeItemNameNotNullAndFullPinYinIsNull();

    List<DtoAnalyzeItem> findByAnalyzeItemNameIn(List<String> names);

}