package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.lims.pro.core.ProCodeHelper;
import com.sinoyd.lims.pro.dto.DtoCostInfo;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.CostInfoRepository;
import com.sinoyd.lims.pro.repository.ProjectRepository;
import com.sinoyd.lims.pro.service.CostInfoService;
import com.sinoyd.lims.pro.service.SampleFolderService;
import com.sinoyd.lims.pro.service.SchemeCacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.concurrent.TimeUnit;

/**
 * 方案变动的缓存数据
 * <AUTHOR>
 * @version V1.0.0 2020/06/11
 * @since V100R001
 */
@Service
public class SchemeCacheServiceImpl implements SchemeCacheService {

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private CostInfoRepository costInfoRepository;

    @Autowired
    @Lazy
    private SampleFolderService sampleFolderService;

    @Async
    @Override
    public void checkSchemeChange(String projectId, CurrentPrincipalUser principalContextUser) {
        //重新赋值登录的账号信息
        ArrayList<GrantedAuthority> authorities = new ArrayList<>();
        SecurityContextHolder.getContext().setAuthentication(new UsernamePasswordAuthenticationToken(principalContextUser,
                null, authorities));
        checkSchemeChange(projectId);
    }

    @Async
    @Override
    public void checkScheme(String projectId, CurrentPrincipalUser principalContextUser) {
        //重新赋值登录的账号信息
        ArrayList<GrantedAuthority> authorities = new ArrayList<>();
        SecurityContextHolder.getContext().setAuthentication(new UsernamePasswordAuthenticationToken(principalContextUser,
                null, authorities));
        sampleFolderService.checkScheme(projectId);
    }

    /**
     * 核查某个项目的方案变动信息
     *
     * @param projectId 项目id
     */
    private void checkSchemeChange(String projectId) {
        //若存在对应项目变动方案的key则不用执行后续逻辑
        String key = EnumPRO.EnumPRORedis.getRedisKey(EnumPRO.EnumPRORedis.PRO_OrgId_SchemeChangeProject.getValue()) + projectId;
        if (!redisTemplate.hasKey(key)) {
            DtoProject project = projectRepository.findOne(projectId);
            if (!project.getStatus().equals(EnumPRO.EnumProjectStatus.项目登记中.toString())) {//项目不处于登记中才核查费用变动
                DtoCostInfo costInfo = costInfoRepository.findByProjectId(projectId);
                if (StringUtil.isNotNull(costInfo)) {//进行费用变更写入
                    if (costInfo.getSchemeChangeStatus().equals(EnumPRO.EnumSchemeChangeStatus.未变更.getValue())) {
                        costInfo.setSchemeChangeStatus(EnumPRO.EnumSchemeChangeStatus.已变更.getValue());
                        costInfoRepository.updateSchemeChangeStatus(costInfo.getId(), EnumPRO.EnumSchemeChangeStatus.已变更.getValue());
                    }
                    redisTemplate.opsForValue().set(key, projectId);
                    //设置过期时间，避免费用已经审核通过无法同步导致的redis信息残余
                    redisTemplate.expire(key, ProCodeHelper.EXPIRE_HOUR, TimeUnit.HOURS);
                }
            }
        } else {
            //重置过期时间
            redisTemplate.expire(key, ProCodeHelper.EXPIRE_HOUR, TimeUnit.HOURS);
        }
    }
}
