package com.sinoyd.lims.pro.criteria;

import com.sinoyd.base.factory.quality.QualityMark;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;

/**
 * 加标曲线的查询条件
 * <AUTHOR>
 * @version V1.0.0 2020/02/10
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddCurveStatisticsCriteria extends BaseCriteria implements Serializable {

    /**
     * 分析开始时间
     */
    private String startTime;

    /***
     * 分析结束时间
     */
    private String endTime;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 人员id
     */
    private String personId;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.sampleId = b.id");
        condition.append(" and a.isDeleted=0");
        condition.append(" and b.isDeleted=0");
        condition.append(" and a.testValue<>'' and a.testValue<>null");
        condition.append(" and a.qcType=:qcType");
        values.put("qcType", new QualityMark().qcTypeValue());
        condition.append(" and a.testId=:testId");
        values.put("testId", testId);
        if (StringUtils.isNotNullAndEmpty(personId) && !personId.equals(UUIDHelper.GUID_EMPTY)) {
            condition.append(" and a.analystId=:personId");
            values.put("personId", personId);
        }
        if (StringUtils.isNotNullAndEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and a.analyzeTime >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtils.isNotNullAndEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and a.analyzeTime < :endTime");
            values.put("endTime", c.getTime());
        }
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            condition.append(" and a.orgId=:orgId");
            condition.append(" and b.orgId=:orgId");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        return condition.toString();
    }
}
