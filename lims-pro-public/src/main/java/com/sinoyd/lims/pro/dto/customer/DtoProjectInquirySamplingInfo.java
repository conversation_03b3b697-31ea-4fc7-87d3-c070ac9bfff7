package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 项目进度采样情况
 * <AUTHOR>
 * @version V1.0.0 2020/2/7
 * @since V100R001
 */
@Data
public class DtoProjectInquirySamplingInfo {
    /**
     * 大类id
     */
    private String bigSampleTypeId;

    /**
     * 大类名称
     */
    private String bigSampleTypeName;

    /**
     * 未采个数
     */
    private Integer notSampled;

    /**
     * 采样情况
     */
    private List<DtoProjectInquirySamplingInfoDetail> detail = new ArrayList<>();
}
