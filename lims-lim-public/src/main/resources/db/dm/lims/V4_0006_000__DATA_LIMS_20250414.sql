INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName,
                                returnType, method, params, pageConfig, orderNum, bizType,
                                remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl,
                                isDefineFileName, defineFileName, beanName, versionNum,
                                controlNum, reportName, validate, usageNum)
VALUES ('b83f1289-8caa-4048-8175-2dd1e5310699', 1, 'WorkSheetSZJD', '水质 碱度测定原始记录.xlsx',
        'WorkSheet/水质 碱度测定原始记录.xlsx', 'output/WorkSheet/水质 碱度测定原始记录.xlsx',
        'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '',
        '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "SZJDWorkSheetDataSourceImpl" }',
        0, 2, NULL, 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-04-14 16:50:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-04-14 16:50:15', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetSZJD', 0, '', '', NULL, NULL, '',
        1, NULL);


INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('79b6624c-fe18-47ab-8b83-fc6673ca831d', 'b83f1289-8caa-4048-8175-2dd1e5310699', 'AnalyseDataManage', '实验室分析',
        '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-04-14 17:00:15', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-04-14 17:00:15', NULL);

INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName,
                                returnType, method, params, pageConfig, orderNum, bizType,
                                remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl,
                                isDefineFileName, defineFileName, beanName, versionNum,
                                controlNum, reportName, validate, usageNum)
VALUES ('d66e5334-d696-4def-a124-219561fc0ac6', 1, 'WorkSheetSZTSQY', '水质 碳酸根、重碳酸根和氢氧根测定原始记录.xlsx',
        'WorkSheet/水质 碳酸根、重碳酸根和氢氧根测定原始记录.xlsx', 'output/WorkSheet/水质 碳酸根、重碳酸根和氢氧根测定原始记录.xlsx',
        'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '',
        '{"originalRecordType" : "lineExtendBySampleTest",  "workSheetDataSourceType" : "SZTSQYWorkSheetDataSourceImpl" }',
        0, 2, NULL, 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-04-14 16:50:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-04-14 16:50:15', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetSZTSQY', 0, '', '', NULL, NULL, '',
        1, NULL);

INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('c529beae-0d95-4435-8e90-1d86731398af', 'd66e5334-d696-4def-a124-219561fc0ac6', 'AnalyseDataManage', '实验室分析',
        '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-04-14 17:00:15', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-04-14 17:00:15', NULL);