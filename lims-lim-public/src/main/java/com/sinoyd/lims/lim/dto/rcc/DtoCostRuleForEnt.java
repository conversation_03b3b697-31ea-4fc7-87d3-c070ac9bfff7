package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.CostRuleForEnt;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoCostRuleForEnt实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_CostRuleForEnt")
 @Data
 @DynamicInsert
 public  class DtoCostRuleForEnt extends CostRuleForEnt {
   private static final long serialVersionUID = 1L;

   /**
    * 企业名称
    */
   @Transient
   private String entName;
 }