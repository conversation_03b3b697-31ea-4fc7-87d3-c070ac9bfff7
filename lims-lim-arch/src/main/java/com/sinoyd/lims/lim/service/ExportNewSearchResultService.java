package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.lim.dto.customer.DtoExportNewSearchResult;
import com.sinoyd.lims.lim.dto.lims.DtoNewSearchResult;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 查新结果导出接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/5
 * @since V100R001
 */
public interface ExportNewSearchResultService extends ImportExcelService<DtoExportNewSearchResult, DtoNewSearchResult> {

    /**
     * 导出
     *
     * @param criteria   请求参数
     * @param response   响应体
     * @param sheetNames sheet页名称
     * @param fileName   附件名称
     */
    void export(BaseCriteria criteria, HttpServletResponse response, Map<String, String> sheetNames, String fileName);
}
