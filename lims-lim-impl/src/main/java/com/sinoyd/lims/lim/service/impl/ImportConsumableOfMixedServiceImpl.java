package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.criteria.ConsumableOfMixedCriteria;
import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.dto.lims.DtoConsumableOfMixed;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.repository.lims.ConsumableOfMixedRepository;
import com.sinoyd.base.service.AnalyzeItemService;
import com.sinoyd.base.service.ConsumableOfMixedService;
import com.sinoyd.base.service.DimensionService;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.lim.dto.customer.DtoImportConsumableOfMixed;
import com.sinoyd.lims.lim.service.ImportConsumableOfMixedService;
import com.sinoyd.lims.lim.verify.ConsumableOfMixedVerifyHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 标准样品混标导入
 *
 * <AUTHOR>
 * @version V1.0.0 2022/7/11
 * @since V100R001
 */
@Service
@Slf4j
public class ImportConsumableOfMixedServiceImpl implements ImportConsumableOfMixedService {

    private ConsumableOfMixedService consumableOfMixedService;

    private AnalyzeItemService analyzeItemService;

    private DimensionService dimensionService;

    private ConsumableOfMixedRepository consumableOfMixedRepository;

    @Override
    @Transactional
    public List<DtoConsumableOfMixed> importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception {
        PoiExcelUtils.verifyFileType(file);
        //region 参数
        List<DtoAnalyzeItem> dbAnaItem = analyzeItemService.findAll();

        List<DtoDimension> dbDimension = dimensionService.findAll();

        //标准样品id
        String consumableId = (String) objectMap.get(0);

        ConsumableOfMixedVerifyHandler verify = getVerify(consumableId, dbAnaItem);
        //endregion
        //获取消耗品导入结果
        ExcelImportResult<DtoImportConsumableOfMixed> result = getExcelData(verify, file, response);
        List<DtoImportConsumableOfMixed> importList = result.getList();
        importList.removeIf(p -> StringUtil.isEmpty(p.getAnalyzeItemName()));
        //获取导入的所有量纲数据
        List<String> dimensionNames = importList.stream().map(DtoImportConsumableOfMixed::getDimensionName)
                .filter(StringUtil::isNotEmpty).collect(Collectors.toList());
        //获取数据库中的量纲名称
        List<String> dbDimensionNames = dbDimension.stream().map(DtoDimension::getDimensionName).collect(Collectors.toList());
        //获取不存在量纲
        List<String> notExistDimension = dimensionNames.stream().filter(p -> !dbDimensionNames.contains(p)).collect(Collectors.toList());
        //保存的量纲数据
        List<DtoDimension> save = new ArrayList<>();
        //添加不存在的量纲
        if (StringUtil.isNotEmpty(notExistDimension)) {
            List<DtoDimension> insertBatch = new ArrayList<>();
            notExistDimension.forEach(p -> {
                DtoDimension dimension = new DtoDimension();
                dimension.setDimensionName(p);
                dimension.setBaseValue(BigDecimal.ZERO);
                insertBatch.add(dimension);
            });
            if (StringUtil.isNotEmpty(insertBatch)) {
                save = dimensionService.save(insertBatch);
            }
        }
        List<DtoConsumableOfMixed> insertData = new ArrayList<>();
        final List<DtoDimension> finalSave = save;
        importList.forEach(p -> {
            DtoConsumableOfMixed consumableOfMixed = new DtoConsumableOfMixed();
            p.setConsumableId(consumableId);
            //设置分析项目id
            Optional<DtoAnalyzeItem> anaItem = dbAnaItem.stream().filter(a -> p.getAnalyzeItemName().equals(a.getAnalyzeItemName())).findFirst();
            anaItem.ifPresent(item -> p.setAnalyzeItemId(item.getId()));
            //设置量纲id
            Optional<DtoDimension> dimension = dbDimension.stream().filter(a -> p.getDimensionName().equals(a.getDimensionName())).findFirst();
            if (dimension.isPresent()) {
                p.setDimensionId(dimension.get().getId());
            } else {
                dimension = finalSave.stream().filter(a -> p.getDimensionName().equals(a.getDimensionName())).findFirst();
                dimension.ifPresent(d -> p.setDimensionId(d.getId()));
            }
            BeanUtils.copyProperties(p, consumableOfMixed);
            insertData.add(consumableOfMixed);
        });

        if (StringUtil.isNotEmpty(insertData)) {
            addData(insertData);
        }

        return consumableOfMixedRepository.findByConsumableId(consumableId);
    }

    @Override
    @Transactional
    public void addData(List<DtoConsumableOfMixed> data) {
        consumableOfMixedService.save(data);
    }

    /**
     * 获取文件需要导入的数据
     *
     * @param verifyHandler 校验器
     * @param file          传入的文件
     * @param response      响应体
     * @return 导入结果
     */
    @Override
    public ExcelImportResult<DtoImportConsumableOfMixed> getExcelData(IExcelVerifyHandler<DtoImportConsumableOfMixed> verifyHandler, MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        //设置表头区域
        params.setTitleRows(0);
        //设置表头开始行
        params.setHeadRows(1);
        //设置开始工作簿
        params.setStartSheetIndex(0);
        //设置是否校验
        params.setNeedVerify(true);
        params.setVerifyHandler(verifyHandler);
        ExcelImportResult<DtoImportConsumableOfMixed> result = ExcelImportUtil.importExcelMore(
                file.getInputStream(),
                DtoImportConsumableOfMixed.class, params);
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "混标导入错误信息");
            PoiExcelUtils.downLoadExcel("混标导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }


    /**
     * 获取混标导入校验器
     *
     * @param consumableId 需要导入的标准样品id
     * @param dbAnaItem    系统中的所有分析因子
     * @return 校验器
     */
    private ConsumableOfMixedVerifyHandler getVerify(String consumableId, List<DtoAnalyzeItem> dbAnaItem) {
        PageBean<DtoConsumableOfMixed> pb = new PageBean<>();
        pb.setPageNo(1);
        pb.setRowsPerPage(10000);
        ConsumableOfMixedCriteria criteria = new ConsumableOfMixedCriteria();
        criteria.setConsumableId(consumableId);
        consumableOfMixedService.findByPage(pb, criteria);
        return new ConsumableOfMixedVerifyHandler(dbAnaItem, pb.getData());
    }

    @Override
    public ExcelImportResult<DtoImportConsumableOfMixed> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        return null;
    }

    @Autowired
    public void setConsumableOfMixedService(ConsumableOfMixedService consumableOfMixedService) {
        this.consumableOfMixedService = consumableOfMixedService;
    }

    @Autowired
    public void setAnalyzeItemService(AnalyzeItemService analyzeItemService) {
        this.analyzeItemService = analyzeItemService;
    }

    @Autowired
    public void setDimensionService(DimensionService dimensionService) {
        this.dimensionService = dimensionService;
    }

    @Autowired
    public void setConsumableOfMixedRepository(ConsumableOfMixedRepository consumableOfMixedRepository) {
        this.consumableOfMixedRepository = consumableOfMixedRepository;
    }
}
