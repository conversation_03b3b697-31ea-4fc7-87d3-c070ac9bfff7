package com.sinoyd.lims.pro.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.service.InstrumentService;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.lim.dto.lims.DtoOAInstrumentScrap;
import com.sinoyd.lims.lim.service.OAInstrumentScrapService;
import com.sinoyd.lims.pro.dto.DtoOATask;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;
import com.sinoyd.lims.pro.dto.DtoOATaskRelation;
import com.sinoyd.lims.pro.entity.OATask;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumOATaskType;
import com.sinoyd.lims.pro.service.OAProcInstrumentScrapService;
import com.sinoyd.lims.pro.service.OATaskRelationService;
import com.sinoyd.lims.pro.service.OATaskService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 仪器报废业务操作接口实现
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-03
 * @since V100R001
 */
@Service
public class OAProcInstrumentScrapServiceImpl implements OAProcInstrumentScrapService {
    /**
     * 审批任务服务
     */
    @Autowired
    @Lazy
    private OATaskService oaTaskService;

    /**
     * 审批任务关联服务
     */
    @Autowired
    @Lazy
    private OATaskRelationService oaTaskRelationService;

    /**
     * 仪器报废服务
     */
    @Autowired
    @Lazy
    private OAInstrumentScrapService instrumentScrapService;

    /**
     * 仪器服务
     */
    @Autowired
    @Lazy
    private InstrumentService instrumentService;

    @Override
    public String startProcess(DtoOATaskCreate<List<DtoOAInstrumentScrap>> taskDto) {
        Map<String, Object> vars = new HashMap<>();
        DtoOATask oaTask = oaTaskService.startProcess(EnumOATaskType.仪器报废, taskDto, vars);

        List<DtoOAInstrumentScrap> data = taskDto.getData();

        List<DtoOATaskRelation> taskRelations = new ArrayList<>();
        DtoOATaskRelation taskRelation = null;

        for (DtoOAInstrumentScrap scrap : data) {
            taskRelation = new DtoOATaskRelation();
            taskRelation.setTaskId(oaTask.getId());
            taskRelation.setObjectId(scrap.getId());

            taskRelations.add(taskRelation);
        }

        // 添加仪器报废记录
        instrumentScrapService.save(data);

        // 添加审批任务关联信息
        oaTaskRelationService.save(taskRelations);

        return oaTask.getId();
    }

    @Override
    public DtoOATaskDetail<List<DtoOAInstrumentScrap>, List<DtoInstrument>> findOATaskDetail(String taskId) {
        DtoOATask task = oaTaskService.findByTaskId(taskId);

        DtoOATaskDetail<List<DtoOAInstrumentScrap>, List<DtoInstrument>> taskDetail = new DtoOATaskDetail<>();
        taskDetail.setTask(task);
        taskDetail.setAttach(oaTaskService.getOATaskAttachDTO(task.getProcInstId()));

        List<DtoOATaskRelation> relations = oaTaskRelationService.findListByTaskId(taskId);

        // 仪器报废信息
        List<DtoOAInstrumentScrap> scraps = new ArrayList<>();

        // 仪器信息
        List<DtoInstrument> instruments = new ArrayList<>();

        for (DtoOATaskRelation relation : relations) {
            DtoOAInstrumentScrap scrap = instrumentScrapService.findOne(relation.getObjectId());
            scraps.add(scrap);

            DtoInstrument instrument = instrumentService.findOne(scrap.getInstrumentId());
            instruments.add(instrument);
        }

        // 设置详细信息
        taskDetail.setDetail(scraps);

        // 设置扩展信息
        taskDetail.setExtend(instruments);

        return taskDetail;
    }

    @Override
    public void findCanSponsor(PageBean<?> page, String key, String typeId) {

    }

    @Override
    @Transactional
    public OATask saveAsDraft(DtoOATaskCreate<List<DtoOAInstrumentScrap>> taskDto) {
        Map<String, Object> vars = new HashMap<>();
        DtoOATask oaTask = oaTaskService.saveAsDraft(EnumOATaskType.仪器报废, taskDto, vars);
        saveTaskRelation(taskDto,oaTask);
        return oaTask;
    }

    private void saveTaskRelation(DtoOATaskCreate<List<DtoOAInstrumentScrap>> taskDto,DtoOATask oaTask){
        List<DtoOAInstrumentScrap> data = taskDto.getData();
        List<DtoOATaskRelation> taskRelations = new ArrayList<>();
        DtoOATaskRelation taskRelation = null;
        for (DtoOAInstrumentScrap scrap : data) {
            taskRelation = new DtoOATaskRelation();
            taskRelation.setTaskId(oaTask.getId());
            taskRelation.setObjectId(scrap.getId());
            taskRelations.add(taskRelation);
        }
        // 添加仪器报废记录
        instrumentScrapService.save(data);
        // 添加审批任务关联信息
        oaTaskRelationService.save(taskRelations);
    }

    @Override
    @Transactional
    public OATask draftSave(DtoOATaskCreate<List<DtoOAInstrumentScrap>> taskDto) {
        //保存审批信息
        DtoOATask oaTask = oaTaskService.DraftSave(taskDto);
        //移除原关联关系
        List<DtoOATaskRelation> relationList = oaTaskRelationService.findListByTaskId(oaTask.getId());
        oaTaskRelationService.delete(relationList);
        //保存新关联关系
        saveTaskRelation(taskDto,oaTask);
        return oaTask;
    }

    @Override
    @Transactional
    public String draftSubmit(DtoOATaskCreate<List<DtoOAInstrumentScrap>> taskDto) {
        return oaTaskService.draftSubmit(taskDto, new HashMap<>());
    }
}