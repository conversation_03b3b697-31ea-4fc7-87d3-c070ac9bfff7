package com.sinoyd.lims.pro.listener;

import com.sinoyd.SpringContextAware;
import com.sinoyd.lims.pro.dto.DtoOADepartmentExpend;
import com.sinoyd.lims.pro.dto.DtoOATask;
import com.sinoyd.lims.pro.dto.DtoOATaskRelation;
import com.sinoyd.lims.pro.service.OADepartmentExpendService;
import com.sinoyd.lims.pro.service.OAProjectExpendService;
import com.sinoyd.lims.pro.service.OATaskRelationService;
import com.sinoyd.lims.pro.service.OATaskService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.springframework.stereotype.Component;

/**
 * 项目部门支出的通知
 *
 * <AUTHOR>
 * @version V1.0.0 2020-03-30
 * @since V100R001
 */
@Component
@Slf4j
public class OADepartmentExpendListener implements ExecutionListener {

    @Override
    public void notify(DelegateExecution delegateExecution) throws Exception {
        String businessKey = delegateExecution.getProcessBusinessKey();
        OATaskRelationService oaTaskRelationService = SpringContextAware.getBean(OATaskRelationService.class);
        DtoOATaskRelation relation = oaTaskRelationService.findByTaskId(businessKey);
        OATaskService oaTaskService = SpringContextAware.getBean(OATaskService.class);
        DtoOATask oaTask = oaTaskService.findOne(businessKey);
        OADepartmentExpendService oaDepartmentExpendService = SpringContextAware.getBean(OADepartmentExpendService.class);
        oaDepartmentExpendService.confirm(relation.getObjectId());
        oaDepartmentExpendService.syncOtherExpenditure(relation.getObjectId(), oaTask);
    }
}
