package com.sinoyd.lims.pro.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import java.util.Date;


/**
 * FolderSign实体
 * <AUTHOR>
 * @version V1.0.0 2021/7/7
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="FolderSign")
 @Data
 public  class FolderSign implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

   public  FolderSign() {
      this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
      this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 点位
    */
    @Column(length=50)
    @ApiModelProperty("点位")
	private String sampleFolderId;
    
    /**
    * 周期
    */
    @Column(nullable=false)
    @ApiModelProperty("周期")
	private Integer cycleOrder;
    
    /**
    * 签到时间
    */
    @Column(nullable=false)
    @ApiModelProperty("签到时间")
	private Date signTime;
    
    /**
    * 签到人id
    */
    @Column(length=50)
    @ApiModelProperty("签到人id")
	private String signPersonId;
    
    /**
    * 签到人名称
    */
    @Column(length=50)
    @ApiModelProperty("签到人名称")
    @Length(message = "签到人名称{validation.message.length}", max = 50)
	private String signPersonName;
    
    /**
    * 签到经度
    */
    @Column(length=50)
    @ApiModelProperty("签到经度")
    @Length(message = "签到经度{validation.message.length}", max = 50)
	private String signLon;
    
    /**
    * 签到纬度
    */
    @Column(length=50)
    @ApiModelProperty("签到纬度")
    @Length(message = "签到纬度{validation.message.length}", max = 50)
	private String signLat;

    /**
     * 签到说明
     */
    @Column(length=200)
    @ApiModelProperty("签到说明")
    @Length(message = "签到说明{validation.message.length}", max = 200)
    private String signTip;

    /**
     * 语音说明
     */
    @Column(length=200)
    @ApiModelProperty("语音说明")
    @Length(message = "语音说明{validation.message.length}", max = 500)
    private String voiceTip;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;
 }