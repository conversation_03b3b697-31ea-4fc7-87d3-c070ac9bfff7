package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * 仪器接入参数实体
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/17
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "InstrumentGatherParams")
@Data
@EntityListeners(AuditingEntityListener.class)
public class InstrumentGatherParams implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public InstrumentGatherParams() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();


    /**
     * 仪器接入表id
     */
    @ApiModelProperty("仪器接入表id")
    @Column(length = 50)
    private String instrumentGatherId;


    /**
     * 参数名称
     */
    @ApiModelProperty("参数名称")
    @Column(length = 100)
    @Length(message = "参数名称{validation.message.length}", max = 100)
    private String paramName;

    /**
     * 参数标识
     */
    @ApiModelProperty("参数标识")
    @Column(length = 100)
    @Length(message = "参数标识{validation.message.length}", max = 100)
    private String paramLabel;

    /**
     * 数据类型G:工况参数、CN2083:结果参数
     */
    @ApiModelProperty("数据类型G:工况参数、CN2083:结果参数")
    @Column(length = 10)
    @Length(message = "数据类型G{validation.message.length}", max = 10)
    private String dataType;

    /**
     * 是否枚举
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否枚举")
    private Boolean isEnum;

    /**
     * 枚举值数据源
     */
    @Column(length = 255)
    @ApiModelProperty("枚举数据源")
    @Length(message = "枚举数据源{validation.message.length}", max = 255)
    private String enumDataSource;

    /**
     * 通道数量
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("通道数量")
    private Integer channelNum;

    /**
     * 量纲
     */
    @ApiModelProperty("量纲")
    @Column(length = 20)
    @Length(message = "量纲{validation.message.length}", max = 20)
    private String dimension;

    /**
     * 排序
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("排序值")
    private Integer orderNum;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @Column(name = "remark")
    @Length(message = "备注{validation.message.length}", max = 255)
    private String remark;

    /**
     * 假删字段
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删字段")
    private Boolean isDeleted = false;


    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}