package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.util.MathUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import org.hibernate.mapping.Array;


/**
 * WorkSheetFolder查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkSheetFolderCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分析人员id
     */
    private String analystId;

    /**
     * 分析方法id
     */
    private String analyzeMethodId;

    /**
     * 分析项目名称
     */
    private String analyzeItemName;

    /**
     * 分析项目id
     */
    private String analyzeItemId;

    /**
     * 分析方法名称
     */
    private String analyzeMethodName;

    /**
     * 状态集合
     */
    private String status;

    /**
     * 测试项目id
     */
    private String testId;


    /**
     * 关键字
     */
    private String key;

    /**
     * 是否显示分析项目名称
     */
    private Boolean isShowAnalyzeItemName = false;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 开始时间
     */
    private String endTime;

    /**
     * 检测单id列表
     */
    private List<String> workSheetFolderIdList;

    /**
     * 校核人员id
     */
    private String checkerId;

    /**
     * 审核人员id
     */
    private String auditorId;

    /**
     * 项目名称，项目编号
     */
    private String projectKey;

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(this.analystId) && !this.analystId.equals(UUIDHelper.GUID_EMPTY)) {
            condition.append(" and analystId = :analystId");
            values.put("analystId", this.analystId);
        }
        if (StringUtils.isNotNullAndEmpty(this.analyzeMethodId) && !this.analyzeMethodId.equals(UUIDHelper.GUID_EMPTY)) {
            condition.append(" and analyzeMethodId = :analyzeMethodId");
            values.put("analyzeMethodId", this.analyzeMethodId);
        }
        if (StringUtil.isNotNull(status)) {
            if (status.contains(",")) {
                condition.append(" and workStatus in :status");
                List<String> stateList = Arrays.stream(this.status.split(",")).collect(Collectors.toList());
                List<Integer> stateInts = new ArrayList<>();
                for (String state : stateList) {
                    if (MathUtil.isInteger(state)) {
                        stateInts.add(Integer.valueOf(state));
                    } else {
                        throw new BaseException("传参错误！");
                    }
                }
                values.put("status", stateInts);
            } else {
                if (MathUtil.isInteger(this.status)) {
                    if (Integer.valueOf(this.status) > 0) {
                        condition.append(" and workStatus = :status");
                        values.put("status", Integer.valueOf(this.status));
                    }
                } else {
                    throw new BaseException("传参错误！");
                }
            }
        }
        if (StringUtils.isNotNullAndEmpty(startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and ").append("analyzeTime").append(" >= :from");
            values.put("from", from);
        }
        if (StringUtils.isNotNullAndEmpty(endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar endCalendar = Calendar.getInstance();
            endCalendar.setTime(to);
            endCalendar.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and ").append("analyzeTime").append(" < :to");
            this.values.put("to", endCalendar.getTime());
        }
        if (StringUtils.isNotNullAndEmpty(this.testId)) {
            condition.append(" and EXISTS (select 1 from DtoWorkSheet b where a.id=b.parentId and b.testId = :testId)");
            values.put("testId", testId);
        }
        if (StringUtil.isNotEmpty(this.analyzeItemName)) {
            condition.append(" and EXISTS (select 1 from DtoWorkSheet b where a.id=b.parentId and b.redAnalyzeItemName like :redAnalyzeItemName)");
            values.put("redAnalyzeItemName", "%" + this.analyzeItemName + "%");
        }
        if (StringUtil.isNotEmpty(this.analyzeMethodName)) {
            condition.append(" and EXISTS (select 1 from DtoWorkSheet b where a.id=b.parentId and b.testId in(select id from DtoTest t where (t.redAnalyzeMethodName like :redAnalyzeMethodName or t.redCountryStandard like :redAnalyzeMethodName)))");
            values.put("redAnalyzeMethodName", "%" + this.analyzeMethodName + "%");
        }
        if (StringUtil.isNotEmpty(this.analyzeItemId)) {
            condition.append(" and EXISTS (select 1 from DtoWorkSheet b where a.id=b.parentId and b.analyseItemId = :analyseItemId)");
            values.put("analyseItemId", this.analyzeItemId);
        }
        if (StringUtils.isNotNullAndEmpty(this.key)) {
            condition.append(" and workSheetCode like :key");
            values.put("key", "%" + key + "%");
        }
        if (StringUtil.isNotEmpty(this.workSheetFolderIdList)) {
            condition.append(" and id in :folderIds");
            values.put("folderIds", workSheetFolderIdList);
        }
        if (StringUtils.isNotNullAndEmpty(this.checkerId) && !this.checkerId.equals(UUIDHelper.GUID_EMPTY)) {
            condition.append(" and checkerId = :checkerId");
            values.put("checkerId", this.checkerId);
        }
        if (StringUtils.isNotNullAndEmpty(this.auditorId) && !this.auditorId.equals(UUIDHelper.GUID_EMPTY)) {
            condition.append(" and auditorId = :auditorId");
            values.put("auditorId", this.auditorId);
        }
        if (StringUtils.isNotNullAndEmpty(this.projectKey)) {
            condition.append(" and EXISTS (select 1 from DtoProject2WorkSheetFolder b, DtoProject p where a.id = b.workSheetFolderId and p.id = b.projectId and (p.projectName like :projectKey or p.projectCode like :projectKey))");
            values.put("projectKey", "%" + this.projectKey + "%");
        }
        if (StringUtil.isNotEmpty(this.sampleTypeId)) {
            condition.append(" and EXISTS (select 1 from DtoAnalyseData d,DtoSample b where a.id = d.workSheetFolderId and d.sampleId = b.id and d.isDeleted = 0 and b.isDeleted = 0 and b.sampleTypeId =:sampleTypeId)");
            values.put("sampleTypeId", this.sampleTypeId);
        }
        return condition.toString();
    }
}