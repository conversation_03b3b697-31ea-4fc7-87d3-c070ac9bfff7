package com.sinoyd.lims.api.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.api.dto.DtoHomeNotice;
import com.sinoyd.lims.api.dto.DtoHomePage;
import com.sinoyd.lims.api.service.MonitorHomeService;
import com.sinoyd.lims.lim.criteria.NoticeCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoNotice;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 仪器出入库移动端内容
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
@Api(tags = "示例: homeMonitor")
@RestController
@RequestMapping("api/monitor/home")
public class MonitorHomeController extends ExceptionHandlerController<MonitorHomeService> {

    /**
     * 按主键查询项目
     * @return RestResponse<DtoHomePhone>
     */
    @ApiOperation(value = "查询项目", notes = "查询项目")
    @GetMapping()
    public RestResponse<List<DtoHomePage>> findHomePage() {
        RestResponse<List<DtoHomePage>> restResponse = new RestResponse<>();
        List<DtoHomePage> homePhone = service.findHomePage();
        restResponse.setData(homePhone);
        restResponse.setRestStatus(StringUtil.isNull(homePhone) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 查询公告
     * @return RestResponse<DtoHomeNotice>
     */
    @ApiOperation(value = "查询公告", notes = "查询公告")
    @GetMapping("/notice")
    public RestResponse<DtoHomeNotice> findNotice() {
        RestResponse<DtoHomeNotice> restResponse = new RestResponse<>();
        DtoHomeNotice notice = service.findNotice();
        restResponse.setData(notice);
        restResponse.setRestStatus(StringUtil.isNull(notice) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 分页查询公告
     * @return RestResponse<DtoHomeNotice>
     */
    @ApiOperation(value = "分页查询公告", notes = "分页查询公告")
    @GetMapping("/noticePage")
    public RestResponse<List<DtoNotice>> findNoticePage(NoticeCriteria noticeCriteria) {
        RestResponse<List<DtoNotice>> restResponse = new RestResponse<>();
        PageBean<DtoNotice> pb = super.getPageBean();
        List<DtoNotice> notices = service.findNoticePage(pb, noticeCriteria);
        restResponse.setData(notices);
        restResponse.setCount(pb.getRowsCount());
        restResponse.setRestStatus(StringUtil.isNull(notices) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 根据id查询公告
     * @return RestResponse<DtoHomeNotice>
     */
    @ApiOperation(value = "分页查询公告", notes = "分页查询公告")
    @GetMapping("/notice/{id}")
    public RestResponse<DtoNotice> findNoticeById(@PathVariable(name = "id") String id) {
        RestResponse<DtoNotice> restResponse = new RestResponse<>();
        DtoNotice notice = service.findNoticeById(id);
        restResponse.setData(notice);
        restResponse.setRestStatus(StringUtil.isNull(notice) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

}
