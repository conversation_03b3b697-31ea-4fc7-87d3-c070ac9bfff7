package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.ContractCollectionPlan;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoContractCollectionPlan实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_ContractCollectionPlan")
@Data
@DynamicInsert
public class DtoContractCollectionPlan extends ContractCollectionPlan {
    /**
     * 合同名称
     */
    @Transient
    private String contractName;

    /**
     * 合同编号
     */
    @Transient
    private String contractCode;

    /**
     * 甲方名称
     */
    @Transient
    private String firstEntName;

    /**
     * 业务员名称
     */
    @Transient
    private String salesPersonName;

    /**
     * 所属月份
     */
    @Transient
    private String belongMonth;
}