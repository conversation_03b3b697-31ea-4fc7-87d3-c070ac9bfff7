package com.sinoyd.lims.monitor.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * Water实体
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="Water")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class Water implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  Water() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
       this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 关联id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("关联id")
	private String parentId = UUIDHelper.GUID_EMPTY;
    
    /**
    * 水体名称
    */
    @Column(length=50)
    @ApiModelProperty("水体名称")
    @Length(message = "水体名称{validation.message.length}", max = 50)
	private String waterName;
    
    /**
    * 水体编码
    */
    @Column(length=50)
    @ApiModelProperty("水体编码")
    @Length(message = "水体编码{validation.message.length}", max = 50)
	private String waterCode;
    
    /**
    * 水体类型：常量（河流、饮用水水厂、水系、水功能区、应用水源地、流域、湖库）
    */
    @Column(length=50)
    @ApiModelProperty("水体类型：常量（河流、饮用水水厂、水系、水功能区、应用水源地、流域、湖库）")
    @Length(message = "水体类型：常量（河流、饮用水水厂、水系、水功能区、应用水源地、流域、湖库）{validation.message.length}", max = 50)
	private String waterType;
    
    /**
    * 是否启用
    */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("是否启用")
	private Boolean isEnabled;
    
    /**
    * 备注
    */
    @Column(length=2000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 2000)
	private String remark;

   /**
    * 是否启用
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted;

    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }