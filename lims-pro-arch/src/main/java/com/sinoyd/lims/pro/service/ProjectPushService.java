package com.sinoyd.lims.pro.service;


import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.pro.dto.customer.DtoProjectPut;

import java.util.List;
import java.util.Map;

/**
 *  项目推送模模块相关功能接口（由上海环境院项目迁移而来）
 * <AUTHOR>
 * @version V1.0.0 2024/11/11
 * @since V100R001
 */
public interface ProjectPushService {

    /**
     * 根据监管平台方法名称查询数据
     * @param req 请求参数
     * @param response 响应
     * @return List<Map>
     */
    List<Map<String, Object>> queryByMethodName(Map<String,String> req,RestResponse response);

    /**
     * 推送合同
     * @param id 合同id
     * @return 响应字符串
     */
    String pushContract(String id);

    /**
     * 推送项目
     * @param project 参数
     * @return 响应字符串
     */
    String pushProject(DtoProjectPut project);

    /**
     * 推送方案
     * @param project 参数
     * @return 响应字符串
     */
    String pushFolder(DtoProjectPut project);

    /**
     * 获取采样计划列表
     * @param pId 监管平台项目id
     * @return List<Map<String,String>>
     */
    List<Map<String,String>> getSamplingPlan(String pId);

    /**
     * 更新监管平台计划采样时间
     * @param project
     * @return
     */
    String updateSamplingTime(DtoProjectPut project);

    /**
     * 推送计划
     * @param project 参数
     */
    void pushPlan(DtoProjectPut project);

    /**
     * 根据任务id推送采样人员
     * @param taskId 任务id
     * @return 响应
     */
    String updateSamplingPerson(String taskId);

    /**
     * 根据任务id推送响应仪器
     * @param taskId 任务id
     * @return 响应
     */
    String updateSamplingDevice(String taskId);

    /**
     * 获取项目推送列表
     * @param page page参数
     * @param criteria 查询条件
     */
    void findByCondition(PageBean<DtoProjectPut> page, BaseCriteria criteria);

    /**
     * 方法匹配
     * @param project 参数
     */
    void methodMatch(DtoProjectPut project);

    /**
     * 查询pdf报告
     * @param projectId 项目id
     * @return List<DtoDocument>
     */
    List<DtoDocument> findReport(String projectId);

    /**
     * 推送报告
     * @param project 参数
     * @return 响应
     */
    String pushReport(DtoProjectPut project);

    /**
     * 数据报告生成系统编号
     * @param projectId 参数
     * @return 编号
     */
    String getReportNum(String projectId);

    /**
     * 更新人员拓展表信息
     * @param expand 信息数据
     * @return DtoPersonExpand
     */
    DtoPerson updatePersonExpand(DtoPerson expand);

    /**
     * 更新仪器拓展表信息
     * @param expand 信息数据
     * @return DtoInstrumentExpand
     */
    DtoInstrument updateInstrumentExpand(DtoInstrument expand);

}
