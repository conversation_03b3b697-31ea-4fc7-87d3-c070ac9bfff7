package com.sinoyd.lims.pro.strategy.strategy.documentFileName;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.service.ReceiveSampleRecordService;
import com.sinoyd.lims.strategy.IFileNameConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 采样单附件名称生成
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2022/7/28
 */
@Component(IFileNameConstant.FileNameStrategyKey.SAMPLING_FILENAME)
public class SamplingFileNameStrategy extends AbsDocumentFileNameStrategy{

    private ReceiveSampleRecordService receiveSampleRecordService;

    @Override
    public Map<String, String> generateDocumentName(Map<String,Object> map) {
        Map<String, String> recordMap = new HashMap<>();
        if (map.containsKey(EnumPRO.EnumDocumnetName.送样单.getValue())) {
            String reportId = map.get(EnumPRO.EnumDocumnetName.送样单.getValue()).toString();
            if (StringUtil.isNotEmpty(reportId)) {
                DtoReceiveSampleRecord record = receiveSampleRecordService.findOne(reportId);
                if (StringUtil.isNotNull(record)) {
                    recordMap.put("samplingTime", DateUtil.dateToString(record.getSamplingTime(), DateUtil.YEAR_ZH_CN));
                    recordMap.put("recordCode", record.getRecordCode());
                }
            }
        }
        return recordMap;
    }

    @Autowired
    @Lazy
    public void setReceiveSampleRecordService(ReceiveSampleRecordService receiveSampleRecordService) {
        this.receiveSampleRecordService = receiveSampleRecordService;
    }
}
