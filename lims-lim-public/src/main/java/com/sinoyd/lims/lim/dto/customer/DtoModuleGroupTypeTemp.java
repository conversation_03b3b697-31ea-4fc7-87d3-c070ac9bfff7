package com.sinoyd.lims.lim.dto.customer;


import com.sinoyd.lims.lim.dto.rcc.DtoReportModule2GroupType;
import lombok.Data;

import java.util.List;

/**
 * 报告组件和分页方式关联关系传输实体
 * <AUTHOR>
 * @version V1.0.0 2023/04/19
 * @since V100R001
 */
@Data
public class DtoModuleGroupTypeTemp {


    /**
     * 报告组件配置关联关系id
     */
    private String reportConfigModuleId;

    /**
     * 分页方式列表
     */
    List<DtoReportModule2GroupType> groupTypeList;

}
