package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 参数部分公式管理查询条件
 * <AUTHOR>
 * @version v1.0.0 2019/5/14
 * @since v100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ParamsPartFormulaCriteria extends BaseCriteria {

    /**
     * 公式Id
     */
    private String formulaId;

    private Integer formulaType;


    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();

        if (StringUtils.isNotNullAndEmpty(formulaId) && !UUIDHelper.GUID_EMPTY.equals(this.formulaId)) {
            condition.append(" and formulaId = :formulaId");
            values.put("formulaId", this.formulaId);
        }

        if ( StringUtil.isNotNull(formulaType) &&  formulaType.intValue() != -1) {
            condition.append(" and (formulaType = :formulaType)");
            values.put("formulaType", this.formulaType);
        }

        return condition.toString();
    }
}