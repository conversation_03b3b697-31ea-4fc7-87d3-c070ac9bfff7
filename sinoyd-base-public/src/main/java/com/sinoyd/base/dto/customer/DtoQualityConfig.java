package com.sinoyd.base.dto.customer;

import lombok.Data;

@Data
public class DtoQualityConfig {

    /**
     * 排序值
     */
    private Integer orderNumber = 0;
    /**
     * 是否跟原样
     */
    private Boolean isFollowSample = false;

    /**
     * 样品上方还是下方(true 是down false 是 up)
     */
    private Boolean upordown = true;

    /**
     * 扩展排序关联(true 是 关联 false 是 不关联)
     */
    private Boolean extension = false;

    /**
     * 有效位数
     */
    private Integer mostSignificance = -1;

    /**
     * 小数位数
     */
    private Integer mostDecimal = -1;
}
