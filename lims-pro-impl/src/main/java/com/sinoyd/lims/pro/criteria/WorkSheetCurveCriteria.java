package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * WorkSheetCurveCriterian标线对应检测单查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年12月27日
 * @since   V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WorkSheetCurveCriteria extends BaseCriteria implements Serializable {

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 标准曲线id
     */
    private String standardCurveId;

    /**
     * 状态
     */
    private Integer workStatus = EnumPRO.EnumStatus.所有.getValue();

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and wsc.worksheetId = w.id");
        condition.append(" and w.parentId = wsf.id");

        if (StringUtils.isNotNullAndEmpty(this.standardCurveId) && !this.standardCurveId.equals(UUIDHelper.GUID_EMPTY)) {
            condition.append(" and wsc.standardCurveId = :standardCurveId");
            values.put("standardCurveId", this.standardCurveId);
        }
        if (StringUtils.isNotNullAndEmpty(this.testId) && !this.testId.equals(UUIDHelper.GUID_EMPTY)) {
            condition.append(" and w.testId = :testId");
            values.put("testId", this.testId);
        }
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and wsf.analyzeTime >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
            condition.append(" and wsf.analyzeTime < :endTime");
            values.put("endTime", to);
        }
        if (!EnumPRO.EnumStatus.所有.getValue().equals(this.workStatus)) {
            condition.append(" and wsf.workStatus = :workStatus");
            values.put("workStatus", this.workStatus);
        }
        return condition.toString();
    }
}
