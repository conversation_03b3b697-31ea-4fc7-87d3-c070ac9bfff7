package com.sinoyd.lims.lim.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;


/**
 * StandardMethodDetail实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/13
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "StandardMethodDetail")
@Data
@EntityListeners(AuditingEntityListener.class)
public class StandardMethodDetail extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();


    /**
     * 方法id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("方法id")
    private String methodId;


    /**
     * 关联id（测试项目、分析方法）
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("关联id（测试项目、分析方法）")
    private String objectId;


}