package com.sinoyd.lims.lim.dto.lims;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.dto.customer.DtoImportTest;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.rcc.DtoSampleTypeGroup;
import com.sinoyd.lims.lim.entity.Test;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.*;


/**
 * DtoTest实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_Test")
@Data
@DynamicInsert
public class DtoTest extends Test {

    /**
     * 监测间隔
     */
    @Transient
    private String projectInterval;

    @Transient
    private String industryTypeName;

    @Transient
    @Excel(name = "样品类型", orderNum = "6")
    private String sampleTypeName;

    @Transient
    private Set<DtoSampleType> sampleTypeList = new HashSet<>();

    @Transient
    private DtoSampleTypeGroup sampleTypeGroup = new DtoSampleTypeGroup();

    @Transient
    private List<String> testIds = new ArrayList<>();

    @Transient
    private Integer useType;

    @Transient
    private List<String> instrumentIds = new ArrayList<>();

    /**
     * 质控限制配置状态
     */
    @Transient
    private String qcRangeStatus;

    /**
     * 质控限值配置数量
     */
    @Transient
    private Integer qcRangeNum;

    /**
     * 默认人员
     */
    @Transient
    private String defaultPerson;

    /**
     * 测试岗位名称
     */
    @Transient
    private String testPostName;

    /**
     * 测试岗位id
     */
    @Transient
    private String testPostId;

    /**
     * 分析方法备注
     */
    @Transient
    private String methodRemark;

    @Transient
    private Boolean isCreate = Boolean.FALSE;

    @Transient
    private List<DtoTest> childTest = new ArrayList<>();

    @Transient
    private Boolean isAutoAdd = false;

    /**
     * 是否停用
     */
    @Transient
    private Boolean isDeactivate = Boolean.FALSE;

    @Transient
    private String type2TestId;

    /**
     * 测试扩展信息
     */
    @Transient
    private List<DtoTestExpand> testExpandList;

    @Transient
    private Integer timePerPeriod;

    @Transient
    private Integer sampleOrder;

    /**
     * 测试项目状态，用于例行点位
     */
    @Transient
    private String testStatus;

    /**
     * 样品数量
     */
    @Transient
    private Integer sampleCount;

    /**
     * 提示信息
     */
    @Transient
    private String message;

    /**
     * 因子合并显示名称
     */
    @Transient
    private String itemAliasText = "";

    /**
     * 方法合并显示名称
     */
    @Transient
    private String methodAliasText = "";

    /**
     * 默认的构造函数
     */
    public DtoTest() {

    }


    /**
     * 该构造函数用到RecordConfig2TestServiceImpl下的findTest方法
     *
     * @param id                   主键id
     * @param redAnalyzeItemName   项目名称
     * @param redAnalyzeMethodName 方法名称
     * @param redCountryStandard   编号
     * @param sampleTypeId         检测类型id
     * @param sampleTypeName       检测类型名称
     */
    public DtoTest(String id, String redAnalyzeItemName,
                   String redAnalyzeMethodName,
                   String redCountryStandard, String sampleTypeId, String sampleTypeName) {
        this.setId(id);
        this.setRedAnalyzeItemName(redAnalyzeItemName);
        this.setRedAnalyzeMethodName(redAnalyzeMethodName);
        this.setRedCountryStandard(redCountryStandard);
        this.setSampleTypeId(sampleTypeId);
        this.setSampleTypeName(sampleTypeName);
    }

    /**
     * @param id                   主键id
     * @param analyzeMethodId      分析方法id
     * @param redAnalyzeMethodName 方法名称
     * @param redAnalyzeItemName   项目名称
     * @param redCountryStandard   编号
     * @param analyzeItemId        分析项目id
     * @param cert                 检测资质
     * @param sampleTypeId         检测类型id
     */
    public DtoTest(String id, String analyzeMethodId, String redAnalyzeMethodName, String redAnalyzeItemName,
                   String redCountryStandard, String analyzeItemId, Integer cert, String sampleTypeId) {
        this.setId(id);
        this.setAnalyzeItemId(analyzeItemId);
        this.setRedAnalyzeItemName(redAnalyzeItemName);
        this.setAnalyzeMethodId(analyzeMethodId);
        this.setRedAnalyzeMethodName(redAnalyzeMethodName);
        this.setRedCountryStandard(redCountryStandard);
        this.setSampleTypeId(sampleTypeId);
        this.setCert(cert);
    }

    /**
     * 导入对象转换为实体
     *
     * @param importTest 导入数据
     */
    public void importToEntity(DtoImportTest importTest) {
        Date timeNow = new Date();
        setId(importTest.getId());
        setTestName(importTest.getTestName());
        setRedAnalyzeMethodName(importTest.getRedAnalyzeMethodName());
        setRedCountryStandard(importTest.getRedCountryStandard());
        setRedYearSn(importTest.getRedYearSn());
        setRedAnalyzeItemName(importTest.getRedAnalyzeItemName());
        setDimensionId(StringUtil.isEmpty(importTest.getDimensionId()) ? UUIDHelper.GUID_EMPTY : importTest.getDimensionId());
        setExamLimitValue(importTest.getExamLimitValue());
        setMostSignificance(importTest.getMostSignificance() == null ? -1 : importTest.getMostSignificance());
        setMostDecimal(importTest.getMostDecimal() == null ? -1 : importTest.getMostDecimal());
        setValidTime(importTest.getValidTime() == null ? BigDecimal.valueOf(-1) : importTest.getValidTime());
        setIsCompleteField(importTest.getIsCompleteField() != null && "1".equals(importTest.getIsCompleteField()));
        setIsOutsourcing(importTest.getIsOutsourcing() != null && "1".equals(importTest.getIsOutsourcing()));
        setIsSamplingOut(importTest.getIsSamplingOut() != null && "1".equals(importTest.getIsSamplingOut()));
        setIsQCP(importTest.getIsQCP() != null && "1".equals(importTest.getIsQCP()));
        setIsQCB(importTest.getIsQCB() != null && "1".equals(importTest.getIsQCB()));
        setIsSeries(importTest.getIsSeries() != null && "1".equals(importTest.getIsSeries()));
        setIsInsUseRecord(importTest.getIsInsUseRecord() != null && "1".equals(importTest.getIsInsUseRecord()));
        setDefaultPerson(importTest.getDefaultPerson());
        setParentId(UUIDHelper.GUID_EMPTY);
        setReportDimensionId(UUIDHelper.GUID_EMPTY);
        setCreateDate(timeNow);
        setCreator(PrincipalContextUser.getPrincipal().getUserId());
        setModifier(UUIDHelper.GUID_EMPTY);
        setModifyDate(timeNow);
        setTips(importTest.getTips());
        setReviseType(1);
        setSamplePeriod(1);
        setLowerLimit(importTest.getLowerLimit());
    }

}