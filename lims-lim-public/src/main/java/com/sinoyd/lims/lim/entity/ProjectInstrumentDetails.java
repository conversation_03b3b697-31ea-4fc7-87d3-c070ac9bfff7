package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * ProjectInstrumentDetails实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "ProjectInstrumentDetails")
@Data
public class ProjectInstrumentDetails implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public  ProjectInstrumentDetails() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 项目仪器表id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("项目仪器表id")
    private String projectInstrumentId;

    /**
     * 仪器id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("仪器id")
    private String instrumentId;

    /**
     * 是否已入库 1：是 0：否
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否已入库 1：是 0：否")
    private Boolean isStorage;

    /**
     * 入库日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("入库日期")
    private Date inDate;

    /**
     * 入库合格情况，0 - 不合格，1 - 合格
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("入库合格情况，0 - 不合格，1 - 合格")
    private Integer inQualified;

    /**
     * 出库日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("出库日期")
    private Date outDate;

    /**
     * 出库合格情况，0 - 不合格，1 - 合格
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("出库合格情况，0 - 不合格，1 - 合格")
    private Integer outQualified;

    /**
     * 入库人id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("入库人id")
    private String inPerson;

    /**
     * 出库人id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("出库人id")
    private String outPerson;

    /**
     * 是否已确认，0 - 否，1 - 是
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否已确认，0 - 否，1 - 是")
    private Integer isConfirm;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;
}