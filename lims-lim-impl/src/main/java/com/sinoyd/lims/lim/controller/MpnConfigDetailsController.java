package com.sinoyd.lims.lim.controller;

import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.MpnConfigDetailsCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoMpnConfigDetails;
import com.sinoyd.lims.lim.service.MpnConfigDetailsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import oracle.jdbc.proxy.annotation.Post;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Mnp配置接口服务
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
@Api(tags = "Mnp配置接口服务")
@RestController
@RequestMapping("api/lim/mpnConfigDetails")
@Validated
public class MpnConfigDetailsController extends BaseJpaController<DtoMpnConfigDetails, String, MpnConfigDetailsService> {
    /**
     * 分页动态条件查询mpnConfig
     *
     * @param mpnConfigCriteria 条件参数
     * @return RestResponse<List < mpnConfig>>
     */
    @ApiOperation(value = "分页动态条件查询", notes = "分页动态条件查询")
    @GetMapping()
    public RestResponse<List<DtoMpnConfigDetails>> findByPage(MpnConfigDetailsCriteria mpnConfigCriteria) {
        PageBean<DtoMpnConfigDetails> pageBean = super.getPageBean();
        RestResponse<List<DtoMpnConfigDetails>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, mpnConfigCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 分页动态条件查询mpnConfig详情
     *
     * @param mpnConfigCriteria 条件参数
     * @return RestResponse<List < mpnConfig>>
     */
    @ApiOperation(value = "分页动态条件查询mpnConfig详情", notes = "分页动态条件查询mpnConfig详情")
    @GetMapping("/findPage")
    public RestResponse<Map<String, Object>> findDetailsByPage(MpnConfigDetailsCriteria mpnConfigCriteria) {
        PageBean<DtoMpnConfigDetails> pageBean = super.getPageBean();
        RestResponse<Map<String, Object>> restResponse = new RestResponse<>();
        restResponse.setData(service.findDetailsByPage(pageBean, mpnConfigCriteria));
        restResponse.setRestStatus(StringUtil.isEmpty(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }


    /**
     * 根据id查询Mnp配置
     *
     * @param id
     * @return 计划实体
     */
    @ApiOperation(value = "根据id查询Mnp配置", notes = "根据id查询Mnp配置")
    @GetMapping("/{id}")
    public RestResponse<DtoMpnConfigDetails> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoMpnConfigDetails> restResp = new RestResponse<>();
        DtoMpnConfigDetails entity = service.findOne(id);
        restResp.setData(entity);
        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }


    /**
     * 新增Mnp配置
     *
     * @param mpnConfig Mnp配置实体
     * @return 新增的Mnp配置实体
     */
    @ApiOperation(value = "新增Mnp配置", notes = "新增Mnp配置")
    @PostMapping("")
    public RestResponse<DtoMpnConfigDetails> create(@Validated @RequestBody DtoMpnConfigDetails mpnConfig) {
        RestResponse<DtoMpnConfigDetails> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoMpnConfigDetails data = service.save(mpnConfig);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }


    /**
     * 更新Mnp配置
     *
     * @param mpnConfig Mnp配置实体
     * @return 更新后的Mnp配置实体
     */
    @ApiOperation(value = "更新Mnp配置", notes = "更新Mnp配置")
    @PutMapping("")
    public RestResponse<DtoMpnConfigDetails> update(@Validated @RequestBody DtoMpnConfigDetails mpnConfig) {
        RestResponse<DtoMpnConfigDetails> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoMpnConfigDetails data = service.update(mpnConfig);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 单个假删
     *
     * @param id 参数id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.logicDeleteById(id);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 批量假删
     *
     * @param ids 参数ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResponse.setCount(count);

        return restResponse;
    }

    /**
     * 下载模版
     *
     * @param mpnConfigId Mnp配置Id
     */
    @ApiOperation(value = "下载模版", notes = "下载模版")
    @GetMapping("/template/{mpnConfigId}")
    public RestResponse<DtoMpnConfigDetails> exportTemplate(@PathVariable String mpnConfigId, HttpServletResponse response) {
        RestResponse<DtoMpnConfigDetails> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.exportTemplate(mpnConfigId, response);
        return restResponse;
    }

    /**
     * 导入数据
     *
     * @param mpnConfigId Mnp配置Id
     */
    @ApiOperation(value = "导入数据", notes = "导入数据")
    @PostMapping("/import")
    public RestResponse<Void> importData(MultipartFile file, String mpnConfigId, HttpServletResponse response) throws Exception {
        RestResponse<Void> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.importData(file, mpnConfigId, response);
        return restResponse;
    }

    /**
     * 数据导出
     *
     * @param mpnConfigCriteria 查询条件
     * @param response          响应
     * @return 操作状态
     */
    @GetMapping("/export")
    public RestResponse<String> exportPersonDetails(MpnConfigDetailsCriteria mpnConfigCriteria, HttpServletResponse response) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.export(mpnConfigCriteria, response);
        return restResponse;
    }


}
