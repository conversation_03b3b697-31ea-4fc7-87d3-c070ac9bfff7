package com.sinoyd.lims.lim.service;

import java.util.List;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoCurve;
import com.sinoyd.lims.lim.dto.lims.DtoCurveDetail;

/**
 * 曲线详单管理
 * <AUTHOR>
 * @version V1.0.0 2019/2/25
 * @since V100R001
 */
public interface CurveDetailService extends IBaseJpaService<DtoCurveDetail, String> {

    /**
     * 计算曲线斜率截距
     *
     * @param curveDetail 曲线详单List
     * @param curve       标准曲线
     */
    DtoCurve calculation(List<DtoCurveDetail> curveDetail, DtoCurve curve);


    /**
     * 获取曲线的明细
     *
     * @param curveIds 曲线ids
     * @return 返回标准曲线明细
     */
    List<DtoCurveDetail> findCurveDetailByCurveIds(List<String> curveIds);
}