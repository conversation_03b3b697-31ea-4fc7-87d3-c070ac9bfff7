package com.sinoyd.base.factory;

import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.service.QualityDivationService;
import com.sinoyd.base.service.impl.divationMethod.*;
import com.sinoyd.boot.common.exception.BaseException;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class QualityDivation {

    public static Map<Integer, QualityDivationService> qcMap = new HashMap<>();

    static {
        //绝对偏差
        qcMap.put(EnumBase.EnumJudgingMethod.绝对偏差.getValue(), new AbsoluteDeviationServiceImpl());
        //绝对误差
        qcMap.put(EnumBase.EnumJudgingMethod.绝对误差.getValue(), new AbsoluteErrorServiceImpl());
        //小于检出限
        qcMap.put(EnumBase.EnumJudgingMethod.小于检出限.getValue(), new DetectionLimitServiceImpl());
        //限值判定
        qcMap.put(EnumBase.EnumJudgingMethod.限值判定.getValue(), new JudgeLimitServiceImpl());
        //穿透率
        qcMap.put(EnumBase.EnumJudgingMethod.穿透率.getValue(), new PenetrationRateServiceImpl());
        //回收率
        qcMap.put(EnumBase.EnumJudgingMethod.回收率.getValue(), new RecoveryServiceImpl());
        //相对偏差
        qcMap.put(EnumBase.EnumJudgingMethod.相对偏差.getValue(), new RelativeDeviationServiceImpl());
        //相对误差
        qcMap.put(EnumBase.EnumJudgingMethod.相对误差.getValue(), new RelativeErrorServiceImpl());
        //小于测定下限
        qcMap.put(EnumBase.EnumJudgingMethod.小于测定下限.getValue(), new TestLimitServiceImpl());
        //小于测定下限
        qcMap.put(EnumBase.EnumJudgingMethod.相对准确度.getValue(), new RelativeAccuracyServiceImpl());
        //小于测定下限
        qcMap.put(EnumBase.EnumJudgingMethod.比值.getValue(), new ratioServiceImpl());
    }

    /**
     * 质控限值计算
     *
     * @param controlLimit 质控限值配置
     * @param valueList    数据结果
     * @return 计算结果
     */
    public void deviationValue(DtoQualityControlLimit controlLimit, List<String> valueList, Map<String, Object> map) {
        if (!qcMap.containsKey(controlLimit.getJudgingMethod())) {
            throw new BaseException("所传判定方式不是有效的，请确认");
        }
        qcMap.get(controlLimit.getJudgingMethod()).deviationValue(controlLimit, valueList, map);
    }
}
