package com.sinoyd.lims.pro.service;

import java.util.List;

import com.sinoyd.base.dto.lims.DtoConsumable;
import com.sinoyd.base.dto.lims.DtoConsumableDetail;
import com.sinoyd.lims.lim.dto.lims.DtoOAConsumablePurchaseDetail;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;
import com.sinoyd.lims.pro.entity.OATask;

/**
 * 消耗品采购业务操作接口
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-03
 * @since V100R001
 */
public interface OAProcConsumablePurchaseDetailService {
    /**
     * 添加审批并启动流程
     * 
     * @param taskDto 参数实体
     * @return 流程实例ID
     */
    String startProcess(DtoOATaskCreate<List<DtoOAConsumablePurchaseDetail>> taskDto);

    /**
     * 查询任务详细信息
     * 
     * @param taskId 任务ID
     * @return
     */
    DtoOATaskDetail<List<DtoOAConsumablePurchaseDetail>, String> findOATaskDetail(String taskId);

    /**
     * 入库
     *
     * @param detail 入库数据
     * @param purchaseDetailId 采购详细id
     * @return 审批计划详细数据
     */
    DtoOAConsumablePurchaseDetail warehousing(DtoConsumableDetail detail,String purchaseDetailId);

    /**
     * 标准样品入库
     *
     * @param consumable 标准样品数据
     * @return 审批计划详细数据
     */
    DtoOAConsumablePurchaseDetail warehousingStandard(DtoConsumable consumable,String purchaseDetailId);

    /**
     * 保存为草稿
     * @param taskDto  表单数据
     * @return         oa任务
     */
    OATask saveAsDraft(DtoOATaskCreate<List<DtoOAConsumablePurchaseDetail>> taskDto);

    /**
     * 保存为草稿
     * @param taskDto  表单数据
     * @return         oa任务
     */
    OATask draftSave(DtoOATaskCreate<List<DtoOAConsumablePurchaseDetail>> taskDto);

    /**
     * 草稿提交
     * @param taskDto  表单数据
     * @return         oa任务
     */
    String draftSubmit(DtoOATaskCreate<List<DtoOAConsumablePurchaseDetail>> taskDto);
}
