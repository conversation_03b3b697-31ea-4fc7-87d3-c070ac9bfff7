package com.sinoyd.lims.lim.dto.customer;

import com.sinoyd.base.entity.SampleType;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Transient;
import java.util.List;


/**
 * DtoSampleTypeTemplate2Test实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DtoSampleTypeTemplate extends SampleType {

    @Transient
    private String industryTypeName;

    @Transient
    private List<DtoTest> testList;

    @Transient
    private List<String> testIds;
}