package com.sinoyd.lims.instrument.parse.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.instrument.parse.dto.DtoTcpData;
import com.sinoyd.lims.instrument.parse.repository.TcpDataRepository;
import com.sinoyd.lims.instrument.parse.service.TcpDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class TcpDataServiceImpl extends BaseJpaServiceImpl<DtoTcpData, String, TcpDataRepository> implements TcpDataService {

    /**
     * 根据任务编号，采样单号获取tcp数据
     *
     * @param taskId   任务编号
     * @param sampleId 采样单号
     * @return tcp数据
     */
    @Override
    public List<DtoTcpData> findByTaskIdAndSampleId(String taskId, String sampleId) {
        return repository.findByWwTaskIdAndWwSampleId(taskId, sampleId);
    }
}
