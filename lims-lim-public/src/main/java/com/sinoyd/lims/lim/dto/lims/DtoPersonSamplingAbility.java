package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.PersonSamplingAbility;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 人员采样能力实体
 *
 * <AUTHOR>
 * @version V1.0.0 2024/8/1
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_PersonSamplingAbility")
@Data
@DynamicInsert
public class DtoPersonSamplingAbility extends PersonSamplingAbility {

    /**
     * 检测类型名称
     */
    @Transient
    private String sampleTypeName;

    /**
     * 采样方法名称
     */
    @Transient
    private String samplingMethodName;

    /**
     * 标准编号
     */
    @Transient
    private String countryStandard;

    /**
     * 证书名称
     */
    @Transient
    private String certName;
}
