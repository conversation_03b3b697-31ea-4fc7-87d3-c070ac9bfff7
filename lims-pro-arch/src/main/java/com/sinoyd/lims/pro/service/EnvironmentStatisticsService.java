package com.sinoyd.lims.pro.service;


import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.monitor.dto.lims.DtoFixedPointProperty;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.customer.DtoDataDetail;

import java.util.List;
import java.util.Map;

/**
 * 环境质量统计操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/04/27
 * @since V100R001
 */
public interface EnvironmentStatisticsService extends IBaseJpaService<DtoProject, String> {


    /**
     * 查询项目
     *
     * @param pb  框架PageBean
     * @param environmentStatisticsCriteria 查询条件
     * @return 项目实体集合
     */
    List<DtoProject> findDtoProject(PageBean<DtoProject> pb, BaseCriteria environmentStatisticsCriteria);

    /**
     * 获取例行监测计划信息(树)
     *
     * @param year  年份
     * @param month 月份
     */
    List<TreeNode> getPointProperty(Integer year, Integer month);

    /**
     * 已选监测计划列表
     *
     * @param projectId 任务id
     * @return 监测计划集合
     */
    List<DtoFixedPointProperty> getProjectProperty(String projectId);

    /**
     * 通过任务ids获取详细数据
     *
     * @param dataDetail
     * @return 详细数据
     */
    Map<String, Object> findDetailDataByProjectList(DtoDataDetail dataDetail);
}