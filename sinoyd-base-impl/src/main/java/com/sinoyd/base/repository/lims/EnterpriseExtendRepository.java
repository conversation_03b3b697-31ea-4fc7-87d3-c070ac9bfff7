package com.sinoyd.base.repository.lims;

import com.sinoyd.base.dto.lims.DtoEnterpriseExtend;
import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.List;


/**
 * EnterpriseExtend数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/9/18
 * @since V100R001
 */
public interface EnterpriseExtendRepository extends IBaseJpaPhysicalDeleteRepository<DtoEnterpriseExtend, String>,
        LimsRepository<DtoEnterpriseExtend, String> {

    /**
     * 根据企业id获取扩展信息
     *
     * @param entId
     * @return
     */
    @Query("select d from DtoEnterpriseExtend d where d.entId = :entId")
    List<DtoEnterpriseExtend> findByEntId(@Param("entId") String entId);

    /**
     * 根据企业ids获取扩展信息
     *
     * @param entIds
     * @return
     */
    @Query("select d from DtoEnterpriseExtend d where d.entId in :entIds")
    List<DtoEnterpriseExtend> findByEntIds(@Param("entIds") Collection<String> entIds);
}