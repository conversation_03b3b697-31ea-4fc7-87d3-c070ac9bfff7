package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.ReportConfig2Module;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * DtoReportConfig2Module实体
 * <AUTHOR>
 * @version V1.0.0 2020/10/14
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_ReportConfig2Module")
@Data
@DynamicInsert
public class DtoReportConfig2Module extends ReportConfig2Module {

    private static final long serialVersionUID = 1L;

    /**
     * 组件编码
     */
    @Transient
    private String moduleCode;

    /**
     * 组件名称
     */
    @Transient
    private String moduleName;

    /**
     * 组件主表名称
     */
    @Transient
    private String tableName;


    /**
     * 组件数据行表名称
     */
    @Transient
    private String sourceTableName;

    /**
     * 报告配置id
     */
    @Transient
    private String recordConfigId;

    /**
     * 报告组件分页配置列表
     */
    @Transient
    private List<DtoReportModule2GroupType> reportModule2GroupTypeList;

}
