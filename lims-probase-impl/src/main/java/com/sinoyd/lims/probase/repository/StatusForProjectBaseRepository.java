package com.sinoyd.lims.probase.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoStatusForProject;

import java.util.List;

public  interface StatusForProjectBaseRepository  extends IBaseJpaPhysicalDeleteRepository<DtoStatusForProject, String> {
    /**
     * 查询指定项目的状态信息
     *
     * @param projectId 项目id
     * @return 对应项目下的状态信息
     */
    List<DtoStatusForProject> findByProjectId(String projectId);
}
