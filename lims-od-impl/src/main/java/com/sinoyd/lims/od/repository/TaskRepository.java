package com.sinoyd.lims.od.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoTraining;
import com.sinoyd.lims.od.dto.DtoTask;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * TaskRepository
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
public interface TaskRepository extends IBaseJpaRepository<DtoTask, String> {


    @Transactional
    @Modifying
    @Query("update DtoTask a set a.taskState = :taskState where a.id in :ids")
    void updateStatus(@Param("ids") List<String> ids, @Param("taskState") Integer taskState);


    @Query("select a from DtoTask a where taskName like %:workSheetCode%")
    List<DtoTask> findByTaskNameLike(@Param("workSheetCode") String workSheetCode);
}
