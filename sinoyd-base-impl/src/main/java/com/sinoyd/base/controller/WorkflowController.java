package com.sinoyd.base.controller;

import com.sinoyd.base.criteria.WorkflowCriteria;
import com.sinoyd.base.dto.customer.DtoWorkflow;
import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.base.service.WorkflowService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * WorkflowController 主要是提供公用的工作流服务对象
 * <AUTHOR>
 * @version V1.0.0 2019/11/01
 * @since V100R001
 */
@Api(tags = "工作流服务")
@RestController
@RequestMapping("/api/base/workflow")
public class WorkflowController extends ExceptionHandlerController<WorkflowService> {

    /**
     * 分页动态条件查询仪器
     *
     * @param criteria 查询条件
     * @return 返回工作流信息
     */
    @ApiOperation(value = "分页动态条件查询工作流", notes = "分页动态条件查询工作流")
    @GetMapping
    public RestResponse<List<DtoWorkflow>> findByPage(WorkflowCriteria criteria) {
        RestResponse<List<DtoWorkflow>> restResp = new RestResponse<>();
        PageBean<DtoWorkflow> page = super.getPageBean();
        service.findByPage(page, criteria);
        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());
        return restResp;
    }

    @ApiOperation(value = "分页动态条件查询工作流", notes = "分页动态条件查询工作流")
    @PostMapping
    public RestResponse<String> submitSign(@RequestBody DtoWorkflowSign dtoWorkflowSign) throws Exception {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setData(service.submitSign(dtoWorkflowSign));
        return restResp;
    }

    @ApiOperation(value = "获取当前流程实例的任务", notes = "获取当前流程实例的任务")
    @GetMapping("/task")
    public RestResponse<Map<String, Object>> getCurrentTask(@RequestParam("procInstId") String procInstId) {
        RestResponse<Map<String, Object>> restResp = new RestResponse<>();
        Map<String, Object> map = service.getCurrentTask(procInstId);
        restResp.setData(map);
        return restResp;
    }

    @ApiOperation(value = "获取当前流程信息", notes = "获取当前流程信息")
    @GetMapping("/process")
    public RestResponse<Map<String, Object>> getProcessDefinition(@RequestParam("proDefKey") String proDefKey) {
        RestResponse<Map<String, Object>> restResp = new RestResponse<>();
        Map<String, Object> map = service.getProcessDefinition(proDefKey);
        restResp.setData(map);
        return restResp;
    }
}
