package com.sinoyd.lims.lim.service;

import com.sinoyd.lims.lim.dto.customer.DtoGenerateSN;

import java.util.Map;

/**
 * 生成序列号的统一方法
 * <AUTHOR>
 * @version V1.0.0 2019/10/23
 * @since V100R001
 */
public interface GenerateSerialNumberService {


    /**
     * 生成下一个编号
     *
     * @param format 格式
     * @param map    数据源
     * @return 返回相应的编号
     */
    String generateNextSN(String format, Map<String, Object> map);


    /**
     * 生成样品编号
     *
     * @param format         格式
     * @param map            数据源
     * @param isAutoCommitSN 是否自动提交流水号
     * @return 返回流水号及流水号数据源
     */
    DtoGenerateSN generateNextSN(String format, Map<String, Object> map, Boolean isAutoCommitSN);

    /**
     * 获取当前的数据编号
     *
     * @param format 格式
     * @param map    数据源
     * @return 返回想要的编号数据
     */
    DtoGenerateSN generateCurrentSN(String format, Map<String, Object> map);
}
