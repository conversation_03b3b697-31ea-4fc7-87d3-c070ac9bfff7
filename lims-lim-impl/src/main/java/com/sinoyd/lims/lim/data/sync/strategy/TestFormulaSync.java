package com.sinoyd.lims.lim.data.sync.strategy;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.lims.lim.data.sync.dto.DtoDataCompareResult;
import com.sinoyd.lims.lim.data.sync.strategy.base.AbsDataSync;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsFormula;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.ParamsFormulaRepository;
import com.sinoyd.lims.lim.service.ParamsFormulaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 测试项目公式同步
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/4/22
 */
@Component
@DependsOn({"springContextAware"})
@Order(11)
@Slf4j
public class TestFormulaSync extends AbsDataSync<DtoParamsFormula> {

    private ParamsFormulaService paramsFormulaService;

    private ParamsFormulaRepository paramsFormulaRepository;

    /**
     * 数据比较
     *
     * @param testIds 需要同步的测试项目id
     * @return 比较结果
     */
    @Override
    public List<DtoDataCompareResult<DtoParamsFormula>> compareData(List<String> testIds) {
        //获取项目上全部测试项目公式
        List<DtoParamsFormula> projectDataList = paramsFormulaService.findAll();
        //公共库中的测试项目公式
        List<DtoParamsFormula> standardTestFormulaList = queryStandardData();
        if(StringUtil.isNotEmpty(standardTestFormulaList) && StringUtil.isNotEmpty(testIds)){
            standardTestFormulaList = standardTestFormulaList.parallelStream()
                    .filter(p -> EnumLIM.EnumParamsFormulaObjectType.测试公式.getValue().equals(p.getObjectType()) &&
                            testIds.contains(p.getObjectId())).collect(Collectors.toList());
        }
        //比较数据
        return compareData(standardTestFormulaList, projectDataList);
    }

    /**
     * 同步数据
     *
     * @param testIds         需要同步的测试项目id集合
     * @param webSocketServer websockets服务端
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncData(List<String> testIds, WebSocketServer webSocketServer) {
        List<DtoDataCompareResult<DtoParamsFormula>> compareResult = compareData(testIds);
        Optional<DtoDataCompareResult<DtoParamsFormula>> resultOptional = compareResult.parallelStream().filter(p -> COMPARE_CATEGORY[0].equals(p.getCategory())
                && p.getCount() > 0).findFirst();
        resultOptional.ifPresent(r -> {
            //已同步记录数
            int i = 0;
            DtoParamsFormula errorDto = null;
            try {
                for (DtoParamsFormula dtoParamsFormula : r.getAddDataList()) {
                    errorDto = dtoParamsFormula;
                    if (paramsFormulaRepository.findOne(dtoParamsFormula.getId()) != null) {
                        paramsFormulaService.update(dtoParamsFormula);
                    } else {
                        paramsFormulaService.save(dtoParamsFormula);
                    }
                    webSocketServer.sendMessage(String.format("同步" + getItemName() + "(%d/%d)", ++i, r.getCount()));
                }
            } catch (Exception e) {
                if (errorDto != null) {
                    webSocketServer.sendMessage("同步" + getItemName() + "发生错误, ID = " + errorDto.getId() + ", 错误信息: "
                            + e.getMessage());
                }
                log.error(e.getMessage(), e);
                throw new BaseException("同步" + getItemName() + "发生错误");
            }
        });
    }

    /**
     * 是否必须同步
     *
     * @return true: 必须同步， false：不用必须同步
     */
    @Override
    public boolean mustSync() {
        return true;
    }

    /**
     * 同步的数据项名称
     *
     * @return 数据项名称
     */
    @Override
    public String getItemName() {
        return EnumLIM.EnumDataSyncType.测试项目公式.name();
    }

    /**
     * 获取排序值
     *
     * @return 排序值
     */
    @Override
    public Integer getOrderNum() {
        return EnumLIM.EnumDataSyncType.测试项目公式.getValue();
    }

    /**
     * 获取标准库数据查询url
     *
     * @return url
     */
    @Override
    public String getStandardDataQueryUrl() {
        return "/api/sinoyd-lims/lim/paramsFormula";
    }

    @Override
    public Integer getSyncDataType() {
        return EnumLIM.EnumDataSyncType.测试项目公式.getValue();
    }

    @Autowired
    @Lazy
    public void setParamsFormulaService(ParamsFormulaService paramsFormulaService) {
        this.paramsFormulaService = paramsFormulaService;
    }

    @Autowired
    public void setParamsFormulaRepository(ParamsFormulaRepository paramsFormulaRepository) {
        this.paramsFormulaRepository = paramsFormulaRepository;
    }
}