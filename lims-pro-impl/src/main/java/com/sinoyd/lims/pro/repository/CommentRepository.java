package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.DtoComment;
import com.sinoyd.frame.repository.IBaseJpaRepository;

import java.util.List;


/**
 * Comment数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface CommentRepository extends IBaseJpaRepository<DtoComment, String> {

    /**
     * 根据关联id和类型返回评论
     *
     * @param objectId  关联id
     * @param objectType 关联类型
     * @return 评论
     */
    List<DtoComment> findByObjectIdAndObjectTypeOrderByCommentTime(String objectId,Integer objectType);
}