package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.FolderPeriodWWInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * DtoFolderPeriodWWInfo实体
 * <AUTHOR>
 * @version V1.0.0 2021/7/7
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_FolderPeriodWWInfo")
@Data
@DynamicInsert
public class DtoFolderPeriodWWInfo extends FolderPeriodWWInfo {

    private static final long serialVersionUID = 1L;
}
