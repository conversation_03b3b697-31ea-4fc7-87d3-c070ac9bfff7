package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ocr对象参数检索
 * 
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OcrConfigParamCriteria extends BaseCriteria{
    /**
     * 参数名称
     */
    private String paramName;

    /**
     *  对象标识
     */
    private String configId;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        condition.append(" and configId = :configId");
        values.put("configId", this.configId);
        if(StringUtils.isNotNullAndEmpty(paramName))
        {
            condition.append(" and paramName like :paramName");
            values.put("paramName", "%" + this.paramName + "%");
        }
        return condition.toString();
    }
}