package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.PublishSystemVersionCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoPublishSystemVersion;
import com.sinoyd.lims.lim.service.PublishSystemVersionService;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 版本发布管理
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2022/11/9
 */
@RestController
@RequestMapping("/api/lim/publishSystemVersion")
@Validated
public class PublishSystemVersionController
        extends BaseJpaController<DtoPublishSystemVersion, String, PublishSystemVersionService> {


    /**
     * 版本发布管理新增
     *
     * @param dtoPublishSystemVersion 传入dto
     * @return RestResponse<DtoPublishSystemVersion> 响应版本发布管理dto
     */
    @ApiOperation(value = "版本发布管理新增", notes = "版本发布管理新增")
    @PostMapping
    public RestResponse<DtoPublishSystemVersion> add(@Validated @RequestBody DtoPublishSystemVersion dtoPublishSystemVersion) {
        RestResponse<DtoPublishSystemVersion> restResp = new RestResponse<>();
        restResp.setData(service.save(dtoPublishSystemVersion));
        return restResp;
    }

    /**
     * 分页动态条件查询版本发布管理
     *
     * @param criteria 分页条件
     * @return RestResponse<List < DtoPublishSystemVersion>> 响应版本发布管理dto list
     */
    @ApiOperation(value = "分页动态条件查询版本发布管理", notes = "分页动态条分页动态条件查询版本发布管理件查询收付款记录")
    @GetMapping
    public RestResponse<List<DtoPublishSystemVersion>> findByPage(PublishSystemVersionCriteria criteria) {
        RestResponse<List<DtoPublishSystemVersion>> restResp = new RestResponse<>();
        PageBean<DtoPublishSystemVersion> page = super.getPageBean();
        service.findByPage(page, criteria);
        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());
        return restResp;
    }

    /**
     * 版本发布管理查询详情
     *
     * @param id 主键
     * @return RestResponse<DtoPublishSystemVersion> 响应版本发布管理dto
     */
    @ApiOperation(value = "版本发布管理查询详情", notes = "版本发布管理查询详情")
    @GetMapping("/findOne")
    public RestResponse<DtoPublishSystemVersion> findOne(String id) {
        RestResponse<DtoPublishSystemVersion> restResp = new RestResponse<>();
        restResp.setData(service.findOne(id));
        return restResp;
    }


    /**
     * 版本发布管理批量删除
     *
     * @param ids 主键list
     * @return RestResponse<Integer> 响应删除条数
     */
    @ApiOperation(value = "版本发布管理批量删除", notes = "版本发布管理批量删除")
    @DeleteMapping
    public RestResponse<Integer> deleteByIds(@RequestBody List<String> ids) {
        RestResponse<Integer> restResp = new RestResponse<>();
        restResp.setData(service.logicDeleteById(ids));
        return restResp;
    }


    /**
     * 版本发布管理修改
     *
     * @param dtoPublishSystemVersion dto
     * @return RestResponse<DtoPublishSystemVersion> 响应版本发布管理dto
     */
    @ApiOperation(value = "版本发布管理修改", notes = "版本发布管理修改")
    @PutMapping
    public RestResponse<DtoPublishSystemVersion> update(@Validated @RequestBody DtoPublishSystemVersion dtoPublishSystemVersion) {
        RestResponse<DtoPublishSystemVersion> restResp = new RestResponse<>();
        restResp.setData(service.update(dtoPublishSystemVersion));
        return restResp;
    }

    /**
     * 获取flyway脚本编号
     *
     * @return 脚本编号
     */
    @ApiOperation(value = "获取flyway脚本编号", notes = "获取flyway脚本编号")
    @GetMapping("/flyway")
    public RestResponse<String> findFlyWayVersion() {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setData(service.findFlyWayVersion());
        return restResp;
    }
}
