package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoExamineTypeRecord;


import java.util.List;


/**
 *  考核类型天禧记录service
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2023/09/14
 */
public interface ExamineTypeRecordService extends IBaseJpaService<DtoExamineTypeRecord, String> {

    /**
     * 附件路径
     * @param id 考核实体标识
     * @return 返回相应的路径信息
     */
    DtoExamineTypeRecord findAttachPath(String id);

    /**
     * 批量插入考核记录
     * @param list 考核记录列表
     * @return 考核记录列表
     */
    List<DtoExamineTypeRecord> batchAddRecord(List<DtoExamineTypeRecord> list);

    /**
     * 根据考核项目标识获取所有填写记录
     * @param typeId 核项目标识
     * @return 填写记录列表
     */
    List<DtoExamineTypeRecord> findAllByExamineTypeId(String typeId);
}
