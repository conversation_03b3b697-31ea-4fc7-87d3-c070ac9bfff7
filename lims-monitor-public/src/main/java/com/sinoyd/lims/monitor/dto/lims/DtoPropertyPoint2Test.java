package com.sinoyd.lims.monitor.dto.lims;

import com.sinoyd.lims.monitor.entity.PropertyPoint2Test;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import java.util.List;


/**
 * DtoPropertyPoint2Test实体
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_MONITOR_PropertyPoint2Test")
 @Data
 @DynamicInsert
 public  class DtoPropertyPoint2Test extends PropertyPoint2Test {
     private static final long serialVersionUID = 1L;

     /**
      * 测试项目id列表
      */
     @Transient
     private List<String> testIdList;
}