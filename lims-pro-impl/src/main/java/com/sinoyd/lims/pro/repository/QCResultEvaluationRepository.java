package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.DtoQCResultEvaluation;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

import java.util.Collection;
import java.util.List;


/**
 * QCResultEvaluation数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface QCResultEvaluationRepository extends IBaseJpaPhysicalDeleteRepository<DtoQCResultEvaluation, String> {

    /**
     * 根据项目id集合获取质控评价数据
     *
     * @param projectIds 项目id集合
     * @return 质控评价数据
     */
    List<DtoQCResultEvaluation> findByProjectIdIn(Collection<String> projectIds);
}