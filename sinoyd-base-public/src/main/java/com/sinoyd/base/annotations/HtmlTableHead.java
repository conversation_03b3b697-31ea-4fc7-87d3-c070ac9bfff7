package com.sinoyd.base.annotations;

import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.*;

/**
 * 表格列名称注解：用于标注表格标签中对应th的值
 *
 * <AUTHOR>
 * @version V5.2.0 2025/04/21
 * @since V100R001
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD})
@Inherited
@Documented
public @interface HtmlTableHead {

    /**
     * 值
     * 别名 {@link #cols()}
     *
     * @see #cols()
     * @return 列名
     */
    @AliasFor("cols")
    String[] value() default "";

    /**
     * 表格列名称值
     *
     * @return 列名
     */
    @AliasFor("value")
    String[] cols() default "";
}
