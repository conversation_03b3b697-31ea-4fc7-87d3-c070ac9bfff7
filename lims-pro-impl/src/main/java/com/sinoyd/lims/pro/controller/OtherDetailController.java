package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.OtherDetailService;
import com.sinoyd.lims.pro.criteria.OtherDetailCriteria;
import com.sinoyd.lims.pro.dto.DtoOtherDetail;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * OtherDetail服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2022/5/18
 * @since V100R001
 */
 @Api(tags = "示例: OtherDetail服务")
 @RestController
 @RequestMapping("api/pro/otherDetail")
 public class OtherDetailController extends BaseJpaController<DtoOtherDetail, String,OtherDetailService> {


    /**
     * 分页动态条件查询OtherDetail
     * @param otherDetailCriteria 条件参数
     * @return RestResponse<List<OtherDetail>>
     */
     @ApiOperation(value = "分页动态条件查询OtherDetail", notes = "分页动态条件查询OtherDetail")
     @GetMapping
     public RestResponse<List<DtoOtherDetail>> findByPage(OtherDetailCriteria otherDetailCriteria) {
         PageBean<DtoOtherDetail> pageBean = super.getPageBean();
         RestResponse<List<DtoOtherDetail>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, otherDetailCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询OtherDetail
     * @param id 主键id
     * @return RestResponse<DtoOtherDetail>
     */
     @ApiOperation(value = "按主键查询OtherDetail", notes = "按主键查询OtherDetail")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoOtherDetail> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoOtherDetail> restResponse = new RestResponse<>();
         DtoOtherDetail otherDetail = service.findOne(id);
         restResponse.setData(otherDetail);
         restResponse.setRestStatus(StringUtil.isNull(otherDetail) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增OtherDetail
     * @param otherDetail 实体列表
     * @return RestResponse<DtoOtherDetail>
     */
     @ApiOperation(value = "新增OtherDetail", notes = "新增OtherDetail")
     @PostMapping
     public RestResponse<DtoOtherDetail> create(@RequestBody @Validated DtoOtherDetail otherDetail) {
         RestResponse<DtoOtherDetail> restResponse = new RestResponse<>();
         restResponse.setData(service.save(otherDetail));
         return restResponse;
      }

     /**
     * 新增OtherDetail
     * @param otherDetail 实体列表
     * @return RestResponse<DtoOtherDetail>
     */
     @ApiOperation(value = "修改OtherDetail", notes = "修改OtherDetail")
     @PutMapping
     public RestResponse<DtoOtherDetail> update(@RequestBody @Validated DtoOtherDetail otherDetail) {
         RestResponse<DtoOtherDetail> restResponse = new RestResponse<>();
         restResponse.setData(service.update(otherDetail));
         return restResponse;
      }

    /**
     * "根据id批量删除OtherDetail
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除OtherDetail", notes = "根据id批量删除OtherDetail")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    @ApiOperation(value = "新增OtherDetail", notes = "新增OtherDetail")
    @PostMapping("/batchSave")
    public RestResponse<Void> batchSave(@RequestBody List<DtoOtherDetail> otherDetails) {
        RestResponse<Void> response = new RestResponse<>();
        service.batchSave(otherDetails);
        return response;
    }
 }