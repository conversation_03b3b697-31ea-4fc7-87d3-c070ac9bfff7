package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.ExamineCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoExamine;
import com.sinoyd.lims.lim.service.ExamineService;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 考核管理controller
 *
 * <AUTHOR>
 * @version V1.0.0 2023/09/14
 * @since V100R001
 */
@Api(tags = "DtoExamine服务")
@RestController
@RequestMapping("api/lim/examine")
@Validated
public class ExamineController extends BaseJpaController<DtoExamine, String, ExamineService> {

    /**
     * 新增考核
     * @param examine 考核实体
     * @return  考核实体
     */
    @PostMapping
    public RestResponse<DtoExamine>  addExamine(@Validated @RequestBody DtoExamine examine){
        RestResponse<DtoExamine> response = new RestResponse<>();
        response.setData(service.save(examine));
        return response;
    }

    /**
     * 考核查询(单条详情）
     * @param id 考核标识
     * @return  考核实体包含下属大小项
     */
    @GetMapping("/{id}")
    public RestResponse<DtoExamine>  getExamineById(@PathVariable("id")String id){
        RestResponse<DtoExamine> response = new RestResponse<>();
        response.setData(service.findOne(id));
        return response;
    }

    /**
     * 考核修改
     * @param examine 考核实体
     * @return  考核实体
     */
    @PutMapping
    public RestResponse<DtoExamine>  updateExamine(@Validated @RequestBody DtoExamine examine){
        RestResponse<DtoExamine> response = new RestResponse<>();
        response.setData(service.update(examine));
        return response;
    }

    /**
     * 删除考核
     * @param id 考核标识
     * @return  假删数量
     */
    @DeleteMapping("/{id}")
    public RestResponse<DtoExamine>  delExamineById(@PathVariable("id")String id){
        RestResponse<DtoExamine> response = new RestResponse<>();
        response.setCount(service.logicDeleteById(id));
        return response;
    }

    /**
     * 批量删除考核
     * @param examineIdList 考核标识列表
     * @return  假删数量
     */
    @DeleteMapping
    public RestResponse<Void>  batchDelExamineById(@RequestBody List<String> examineIdList){
        RestResponse<Void> response = new RestResponse<>();
        response.setCount(service.batchDelExamineById(examineIdList));
        return response;
    }

    /**
     * 考核复制
     * @param id 考核标识
     * @return  复制的考核实体
     */
    @PostMapping("/copy/{id}")
    public RestResponse<DtoExamine>  copyExamine(@PathVariable("id")String id){
        RestResponse<DtoExamine> response = new RestResponse<>();
        response.setData(service.copyExamine(id));
        return response;
    }


    /**
     * 校验是否可以送审 全100
     * @param id 考核标识
     * @return  校验信息
     */
    @GetMapping("/auditCheck/{id}")
    public RestResponse<Boolean>  auditCheck(@PathVariable("id")String id){
        RestResponse<Boolean> response = new RestResponse<>();
        response.setData(service.auditCheck(id));
        return response;
    }



    /**
     *  更新考核状态
     * @param id 考核标识
     * @param status 要更新的状态
     * @return  考核实体
     */
    @PutMapping("/submit/{id}/{status}")
    public RestResponse<DtoExamine> updateExamineStatus(@PathVariable("id")String id,@PathVariable("status")Integer status, String opinion){
        RestResponse<DtoExamine> response = new RestResponse<>();
        response.setData(service.updateExamineStatus(id,status,opinion));
        return response;
    }


    /**
     * 分页动态条件查询DtoExamine
     *
     * @param examineCriteria 条件参数
     * @return RestResponse<List<DtoExamine>>
     */
    @GetMapping
    public RestResponse<List<DtoExamine>> findByPage(ExamineCriteria examineCriteria) {
        PageBean<DtoExamine> pageBean = super.getPageBean();
        RestResponse<List<DtoExamine>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, examineCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

}
