package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.pro.dto.DtoProjectPushLog;
import com.sinoyd.lims.pro.repository.ProjectPushLogRepository;
import com.sinoyd.lims.pro.service.ProjectPushLogService;
import org.springframework.stereotype.Service;


/**
 * ProjectPushLog操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Service
public class ProjectPushLogServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoProjectPushLog,String,ProjectPushLogRepository> implements ProjectPushLogService {

    @Override
    public void findByPage(PageBean<DtoProjectPushLog> pb, BaseCriteria projectPushLogCriteria) {
        pb.setEntityName("DtoProjectPushLog a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, projectPushLogCriteria);
    }
}