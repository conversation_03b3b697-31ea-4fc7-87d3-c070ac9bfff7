package com.sinoyd.lims.od.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

/**
 * OdPersonAnswer查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OdPersonAnswerCriteria extends BaseCriteria {

    /**
     * 嗅辨员id
     */
    private String personId;

    /**
     * 样品编号
     */
    private String sampleCode;

    /**
     * 是否嗅辨完成
     */
    private Boolean isCompleted = false;


    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务类型
     */
    private Integer taskType;

    /**
     * 配气员Id
     */
    private String gasMixerId;

    /**
     * 嗅辨开始时间
     */
    private String startTime;

    /**
     * 嗅辨结束时间
     */
    private String endTime;

    /**
     * 实验组
     */
    private Integer groupSn;

    /**
     * 实验次序
     */
    private Integer seqSn;


    @Override
    public String getCondition() {
        values.clear(); // 清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and p.labSeqId = a.id");
        condition.append(" and a.groupId = g.id");
        condition.append(" and s.id = g.sampleId");
        condition.append(" and t.id = s.taskId");
        condition.append(" and a.labState <> -1");

        Calendar calendar = new GregorianCalendar();
        //开始时间查询
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date date = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and t.odDate >= :startTime");
            values.put("startTime", date);
        }
        //结束时间查询
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date date = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            date = calendar.getTime();
            condition.append(" and  t.odDate < :endTime");
            values.put("endTime", date);
        }

        if (StringUtil.isNotNull(taskType)) {
            condition.append(" and t.taskType = :taskType");
            values.put("taskType", taskType);
        }

        if (StringUtil.isNotEmpty(gasMixerId)) {
            condition.append(" and t.gasMixerId = :gasMixerId");
            values.put("gasMixerId", gasMixerId);
        }

        if (StringUtil.isNotEmpty(taskName)) {
            condition.append(" and t.taskName like :taskName");
            values.put("taskName", "%" + taskName + "%");
        }

        if (StringUtil.isNotEmpty(personId)) {
            condition.append(" and exists (select 1 from DtoOdPerson pe where p.personId = pe.id and pe.personId = :personId)");
            values.put("personId", personId);
        }

        if (StringUtil.isNotEmpty(sampleCode)) {
            condition.append(" and s.sampleCode like :sampleCode");
            values.put("sampleCode", "%" + sampleCode + "%");
        }

        if (StringUtil.isNotNull(isCompleted)) {
            condition.append(" and p.isCompleted = :isCompleted");
            values.put("isCompleted", isCompleted);
        }

        if (StringUtil.isNotNull(groupSn)) {
            condition.append(" and g.sn = :groupSn");
            values.put("groupSn", groupSn);
        }

        if (StringUtil.isNotNull(seqSn)) {
            condition.append(" and a.sn = :seqSn");
            values.put("seqSn", seqSn);
        }
        return condition.toString();
    }
}
