package com.sinoyd.lims.lim.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;


/**
 * PersonAbility实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "PersonAbility")
@Data
public class PersonAbility extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public PersonAbility() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 人员Id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("人员Id")
    private String personId;

    /**
     * 测试项目Id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("测试项目Id")
    private String testId;

    /**
     * 证书Id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("证书Id")
    private String personCertId;

    /**
     * 证书获得日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("证书获得日期")
    private Date achieveDate;

    /**
     * 有效期至
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("有效期至")
    private Date certEffectiveTime;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 检测类型id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("检测类型id")
    private String sampleTypeId;

    /**
     * 采样方法id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("采样方法id")
    private String samplingMethodId;

    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    @Length(message = "项目名称{validation.message.length}", max = 255)
    private String redAnalyzeItemName;

    /**
     * 方法名称
     */
    @ApiModelProperty("方法名称")
    @Length(message = "方法名称{validation.message.length}", max = 255)
    private String redAnalyzeMethodName;

    /**
     * 标准编号
     */
    @Column(length = 50)
    @ApiModelProperty("标准编号")
    @Length(message = "标准编号{validation.message.length}", max = 200)
    private String redCountryStandard;

    /**
     * 能力类型
     */
    @ApiModelProperty("能力类型")
    @Length(message = "能力类型{validation.message.length}", max = 100)
    private String abilityType;

}