package com.sinoyd.lims.lim.dto.customer;

import com.sinoyd.lims.lim.dto.rcc.DtoSerialNumberConfig;
import lombok.Data;

/**
 * 生成的SN的实体数据
 * <AUTHOR>
 * @version V1.0.0 2020/7/15
 * @since V100R001
 */
@Data
public class DtoGenerateSN {

    /**
     * 编号
     */
    private String code;


    /**
     * 当前人id
     */
    private String inputPersonId;


    /**
     * 序列配置（需要新增的）
     */
    private DtoSerialNumberConfig serialNumberConfigCreate;


    /**
     * 序列配置（需要更新的）
     */
    private DtoSerialNumberConfig serialNumberConfigUpdate;

    /**
     * 当前流水号类型
     */
    private String currentSerialNumberType;

    /**
     * 当前流水号参数0，对应 DtoSerialNumberConfig 中 para0
     */
    private String currentPara0;

    /**
     * 当前流水号参数0，对应 DtoSerialNumberConfig 中 para1
     */
    private String currentPara1;

    /**
     * 当前流水号参数0，对应 DtoSerialNumberConfig 中 para2
     */
    private String currentPara2;

    /**
     * 当前流水号参数0，对应 DtoSerialNumberConfig 中 para3
     */
    private String currentPara3;
}
