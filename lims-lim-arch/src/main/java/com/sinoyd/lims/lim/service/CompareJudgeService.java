package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoCompareJudge;
import com.sinoyd.lims.lim.vo.CompareJudgeVO;

/**
 * 比对评判管理service
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/06/13
 */
public interface CompareJudgeService extends IBaseJpaService<DtoCompareJudge, String> {

    /**
     * 保存比对评判数据
     *
     * @param vo 评判数据对象
     * @return 保存后的对评判数据
     */
    DtoCompareJudge saveCompareJudge(CompareJudgeVO vo);

    /**
     * 修改比对评判数据
     *
     * @param vo 评判数据对象
     * @return 保存后的对评判数据
     */
    DtoCompareJudge updateCompareJudge(CompareJudgeVO vo);
}
