package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * Notice实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="Notice")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class Notice implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  Notice() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 公告标题
    */
    @Column(nullable=false)
    @ApiModelProperty("公告标题")
    @Length(max = 255, message = "公告标题不能超过255")
	private String title;
    
    /**
    * 公告类型（常量）：LIM_NoticeCategory(1通知、2行政、4标准规范、8内部管理、16其他)
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("公告类型（常量）：LIM_NoticeCategory(1通知、2行政、4标准规范、8内部管理、16其他)")
    private String category;
    
    /**
    * 公告内容
    */
    @ApiModelProperty("公告内容")
	private String content;
    
    /**
    * 是否发布（0.不发布 1.发布）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否发布（0.不发布 1.发布）")
    private Boolean isRelease;
    
    /**
    * 发布人id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("发布人id")
    private String releaseId;
    
    /**
    * 发布人
    */
    @ApiModelProperty("发布人")
	private String releaseMan;
    
    /**
    * 发布时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @ApiModelProperty("发布时间")
    private Date releaseTime;
    
    /**
    * 是否置顶(0.不置顶 1.置顶)
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否置顶(0.不置顶 1.置顶)")
    private Boolean isTop;
    
    /**
    * 公告标签（常量）：LIM_NoticeLabel（一般、紧急、重要）
    */
    @Column(length=1000)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("公告标签（常量）：LIM_NoticeLabel（一般、紧急、重要）")
    @Length(message = "公告标签{validation.message.length}", max = 1000)
    private String label;
    
    /**
    * 状态
    */
    @Column(length=50)
    @ApiModelProperty("状态")
    @Length(message = "状态{validation.message.length}", max = 50)
    private String status;
    
    /**
    * 排序值
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("排序值")
    private Integer orderNum;
    
    /**
    * 浏览次数
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("浏览次数")
    private Integer clickNumber;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }