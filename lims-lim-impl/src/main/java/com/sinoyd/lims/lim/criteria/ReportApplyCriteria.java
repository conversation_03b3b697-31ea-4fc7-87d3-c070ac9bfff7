package com.sinoyd.lims.lim.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * ReportApply查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportApplyCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键id
    */
    private String id;

    /**
     * 报表配置id
     */
    private String configId;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.reportConfigId = c.id ");
        if (StringUtil.isNotEmpty(this.id)) {
            condition.append(" and a.id = :id");
            values.put("id", this.id);
        }

        if (StringUtil.isNotEmpty(this.configId)) {
            condition.append(" and a.reportConfigId = :configId");
            values.put("configId", this.configId);
        }

        return condition.toString();
    }
}