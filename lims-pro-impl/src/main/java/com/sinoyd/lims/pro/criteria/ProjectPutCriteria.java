package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 项目推送查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2022/11/15
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectPutCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    private String registerStartTime;

    private String registerEndTime;

    private String registerPerson;

    private Integer isHandle;

    private String key;

    @Override
    public String getCondition() {
        values.clear();
        Calendar calendar = new GregorianCalendar();
        StringBuilder condition = new StringBuilder();
        condition.append(" and p.id = c.projectId and c.isPush = 1");
        List<String> statusList = Stream.of(EnumPRO.EnumProjectStatus.项目下达中.name(), EnumPRO.EnumProjectStatus.方案审核中.name(),
                EnumPRO.EnumProjectStatus.方案未通过.name(), EnumPRO.EnumProjectStatus.方案确认中.name(), EnumPRO.EnumProjectStatus.方案编制中.name(),
                EnumPRO.EnumProjectStatus.开展中.name(), EnumPRO.EnumProjectStatus.数据汇总中.name(), EnumPRO.EnumProjectStatus.已办结.name()).collect(Collectors.toList());
        condition.append(" and p.status in :statusList");
        values.put("statusList", statusList);
        //登记时间开始时间查询
        if (StringUtil.isNotEmpty(this.registerStartTime)) {
            Date date = DateUtil.stringToDate(this.registerStartTime, DateUtil.YEAR);
            condition.append(" and p.inceptTime >= :registerStartTime");
            values.put("registerStartTime", date);
        }
        //登记时间结束时间查询
        if (StringUtil.isNotEmpty(this.registerEndTime)) {
            Date date = DateUtil.stringToDate(this.registerEndTime, DateUtil.YEAR);
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            date = calendar.getTime();
            condition.append(" and p.inceptTime < :registerEndTime");
            values.put("registerEndTime", date);
        }
        if (StringUtil.isNotEmpty(this.key)) {
            condition.append(" and (p.projectName like :key or p.projectCode like :key or p.customerName like :key)");
            values.put("key", "%"+this.key+"%");
        }
        if (StringUtil.isNotEmpty(this.registerPerson)){
            condition.append(" and exists(select 1 from DtoProjectPlan pl where pl.projectId = p.id and pl.leaderId = :registerPerson)");
            values.put("registerPerson",this.registerPerson);
        }
        if (this.isHandle!=null){
            condition.append(" and c.isHandle = :isHandle");
            values.put("isHandle",this.isHandle);
        }
        return condition.toString();
    }
}
