package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 样品分配传参
 * <AUTHOR>
 * @version V1.0.0 2019/12/06
 * @since V100R001
 */
@Data
public class DtoSampleAssignParam {

    /**
     * 领样单id
     */
    private String subId;

    /**
     * 分析人id
     */
    private String analystId;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 分析项目id
     */
    private List<String> analyseItemIds;

    /**
     * 新的测试项目id
     */
    private String newTestId;

    /**
     * 修改原因
     */
    private String opinion;

    /**
     * 是否同时更改同方法测试项目的分析方法
     */
    private Boolean isChangeAll;

    /**
     * 旧的分析方法id
     */
    private String oldMethodId;

    /**
     * 样品id集合
     */
    private List<String> sampleIds = new ArrayList<>();
}
