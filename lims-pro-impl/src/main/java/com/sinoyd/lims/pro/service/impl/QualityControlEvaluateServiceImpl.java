package com.sinoyd.lims.pro.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.SpringContextAware;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.factory.QualityTaskFactory;
import com.sinoyd.base.factory.quality.*;
import com.sinoyd.base.factory.task.QualityControlKind;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.service.CalculateService;
import com.sinoyd.base.service.DimensionService;
import com.sinoyd.base.utils.base.DivationUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.boot.frame.sys.model.ConfigModel;
import com.sinoyd.boot.frame.sys.service.IConfigService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.service.CalculationService;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.data.qcconfig.service.QCConfigService;
import com.sinoyd.lims.lim.dto.customer.DtoSampleItemGroupTag;
import com.sinoyd.lims.lim.dto.lims.DtoAnalyzeItemSortDetail;
import com.sinoyd.lims.lim.dto.lims.DtoCurve;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.lims.DtoTestExpand;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsPartFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsTestFormula;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.AnalyzeItemSortDetailRepository;
import com.sinoyd.lims.lim.repository.lims.CurveRepository;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsPartFormulaRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsTestFormulaRepository;
import com.sinoyd.lims.lim.repository.rcc.QualityControlLimitRepository;
import com.sinoyd.lims.lim.service.QualityControlLimitService;
import com.sinoyd.lims.lim.service.TestExpandService;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.pro.core.ProCodeHelper;
import com.sinoyd.lims.pro.criteria.QualityControlEvaluateCriteria;
import com.sinoyd.lims.pro.criteria.SampleGroupCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.*;
import com.sinoyd.lims.pro.entity.AnalyseData;
import com.sinoyd.lims.pro.entity.Project2WorkSheetFolder;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.*;
import com.sinoyd.lims.pro.util.MathUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 质控评价操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2022/11/10
 * @since V100R001
 */
@Service
@Slf4j
public class QualityControlEvaluateServiceImpl extends BaseJpaServiceImpl<DtoQualityControlEvaluate, String, QualityControlEvaluateRepository> implements QualityControlEvaluateService {

    private static final Pattern PATTERN = Pattern.compile("\\[\\w+\\]");

    private AnalyseDataRepository analyseDataRepository;
    private QualityControlLimitRepository qualityControlLimitRepository;
    private QualityControlRepository qualityControlRepository;
    private AnalyseOriginalRecordRepository analyseOriginalRecordRepository;
    private QualityControlEvaluateRepository qualityControlEvaluateRepository;
    private SampleRepository sampleRepository;
    private SampleTypeRepository sampleTypeRepository;
    private ParamsTestFormulaRepository paramsTestFormulaRepository;
    private TestRepository testRepository;
    private ProService proService;
    private DimensionService dimensionService;
    private WorkSheetFolderRepository workSheetFolderRepository;
    private AnalyzeItemSortDetailRepository analyzeItemSortDetailRepository;
    private TestExpandService testExpandService;
    private CodeService codeService;
    private ReportDetailRepository reportDetailRepository;
    private SampleFolderRepository sampleFolderRepository;
    private TestService testService;
    private AnalyseDataService analyseDataService;
    private ReceiveSubSampleRecord2SampleRepository receiveSubSampleRecord2SampleRepository;
    private ParamsPartFormulaRepository paramsPartFormulaRepository;

    private QualityControlEvaluateTaskService qualityControlEvaluateTaskService;
    private CurveRepository curveRepository;
    private Project2WorkSheetFolderRepository project2WorkSheetFolderRepository;
    private QCConfigService qcConfigService;
    private SampleGroupService sampleGroupService;
    private QualityControlLimitService qualityControlLimitService;
    private IConfigService configService;
    private ReportRepository reportRepository;
    private ReportBaseInfoRepository reportBaseInfoRepository;

    private static final List<String> basicInfoLabelList = Arrays.asList("检测类型", "分析人", "分析日期", "状态");
    private static final List<String> blankExtendLabelList = Arrays.asList("分析项目", "样品编号", "检查项", "评判方式", "测定值", "允许值", "是否合格");
    private static final List<String> parallelExtendLabelList = Arrays.asList("分析项目", "样品编号", "检查项", "评判方式", "测定值", "均值", "偏差", "允许偏差", "是否合格");
    private static final List<String> jbExtendLabelList = Arrays.asList("分析项目", "样品编号", "检查项", "评判方式", "加标体积", "加标量", "测定值", "增值", "回收率", "允许偏差", "是否合格");
    private static final List<String> standardExtendLabelList = Arrays.asList("分析项目", "质控样编号", "标准值", "测定值", "是否合格");
    // 标样配置相对、偏差相对误差 时显示
    private static final List<String> standardExtendLabelListOfDeviation = Arrays.asList("分析项目", "质控样编号", "标准值", "评判方式", "检查项", "检查项值", "允许限值", "是否合格");
    private static final List<String> curveExtendLabelList = Arrays.asList("分析项目", "样品编号", "检查项", "评判方式", "标准溶液加入体积", "理论值", "测定值",
            "相对偏差/误差", "允许相对偏差/误差", "是否合格");
    private static final List<String> replaceExtendLabelList = Arrays.asList("分析项目", "样品编号", "检查项", "评判方式", "加入量", "测定值", "回收率", "允许回收率", "是否合格");
    private static final List<String> clExtendLabelList = Arrays.asList("分析项目", "样品编号", "检查项", "评判方式", "穿透公式（%）", "测定值", "穿透率（%）", "允许穿透率（%）", "是否合格");
    private static final List<String> negPosExtendLabelList = Arrays.asList("分析项目", "样品编号", "允许限值", "测定值", "是否合格");
    private static final List<String> jzExtendLabelList = Arrays.asList("分析项目", "样品编号", "校正点浓度", "实测浓度", "相对偏差/误差", "允许相对偏差/误差", "是否合格");

    @Override
    public void findByPage(PageBean<DtoQualityControlEvaluate> pb, BaseCriteria criteria) {
        pb.setEntityName("DtoQualityControlEvaluate a");
        pb.setSelect("select a");
        pb.setRowsPerPage(Integer.MAX_VALUE);
        QualityControlEvaluateCriteria qualityControlEvaluateCriteria = (QualityControlEvaluateCriteria) criteria;
        Boolean isLimit = qualityControlEvaluateCriteria.getIsLimit();
        String workSheetFolderId = qualityControlEvaluateCriteria.getWorkSheetFolderId();
        DtoWorkSheetFolder workSheetFolder = workSheetFolderRepository.findOne(workSheetFolderId);
        List<DtoAnalyzeItemSortDetail> details = new ArrayList<>();
        List<DtoAnalyseData> analyseDataList;
        if (StringUtil.isNotNull(workSheetFolder)) {
            details = StringUtil.isNotNull(workSheetFolder.getSortId())
                    ? analyzeItemSortDetailRepository.findBySortId(workSheetFolder.getSortId()) : new ArrayList<>();
            analyseDataList = analyseDataRepository.findByWorkSheetFolderIdAndIsDeletedFalse(workSheetFolderId);
        } else {
            List<String> sampleIds = receiveSubSampleRecord2SampleRepository.findByReceiveSubSampleRecordId(workSheetFolderId).stream().map(DtoReceiveSubSampleRecord2Sample::getSampleId).collect(Collectors.toList());
            analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds).stream().filter(a -> a.getIsCompleteField() && !a.getIsOutsourcing()).collect(Collectors.toList());
        }
        final List<DtoAnalyzeItemSortDetail> detailsSort = details;
        Map<String, DtoAnalyseData> analyseDataMap = analyseDataList.stream().collect(Collectors.toMap(DtoAnalyseData::getId, dto -> dto));
        List<String> analyseDataIdList = analyseDataList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(analyseDataIdList)) {
            qualityControlEvaluateCriteria.setObjectIdList(analyseDataIdList);
            comRepository.findByPage(pb, qualityControlEvaluateCriteria);
            List<DtoQualityControlEvaluate> qualityControlEvaluateList = pb.getData();
            //获取样品信息及质控信息
            List<String> sampleIdList = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
            List<Object[]> objects = comRepository.find("select a.id, a.code, a.qcId, a.sampleCategory, a.associateSampleId from DtoSample a where a.id in :sampleIdList and a.isDeleted = 0",
                    Collections.singletonMap("sampleIdList", sampleIdList));
            Map<String, Object[]> id2ObjectMap = new HashMap<>();
            List<String> qcIdList = qualityControlEvaluateList.stream().map(DtoQualityControlEvaluate::getQcId).distinct().collect(Collectors.toList());
            for (Object[] objArr : objects) {
                id2ObjectMap.put(objArr[0].toString(), objArr);
            }
            List<DtoQualityControl> qcList = StringUtil.isNotEmpty(qcIdList) ? qualityControlRepository.findAll(qcIdList) : new ArrayList<>();
            Map<String, DtoQualityControl> qcMap = qcList.stream().collect(Collectors.toMap(DtoQualityControl::getId, dto -> dto));
            List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
            SampleGroupCriteria sampleGroupCriteria = new SampleGroupCriteria();
            sampleGroupCriteria.setSampleIds(sampleIdList);
            sampleGroupCriteria.setTestIds(testIds);
            List<DtoSampleItemGroupTag> sampleItemGroupTagList = sampleGroupService.findSampleItemGroupTag(sampleGroupCriteria);
            for (DtoQualityControlEvaluate evaluate : qualityControlEvaluateList) {
                String sampleId = analyseDataMap.get(evaluate.getObjectId()).getSampleId();
                Object[] loopArr = id2ObjectMap.get(sampleId);
                EnumPRO.EnumSampleCategory category = StringUtil.isNotNull(loopArr)
                        ? EnumPRO.EnumSampleCategory.getByValue(Integer.valueOf(loopArr[3].toString())) : null;
                String anaItemName = analyseDataMap.get(evaluate.getObjectId()).getRedAnalyzeItemName();
                String anaItemId = analyseDataMap.get(evaluate.getObjectId()).getAnalyseItemId();
                String testId = analyseDataMap.get(evaluate.getObjectId()).getTestId();
                DtoQualityControl control = qcMap.get(evaluate.getQcId());
                Integer qcType = StringUtil.isNotNull(control) ? control.getQcType() : -1;
                if (qcType.equals(-1) && StringUtil.isNotNull(category)) {
                    qcType = category.getQcType();
                    //表示为串联样
//                    if (evaluate.getQcId().equals(UUIDHelper.GUID_EMPTY)) {
//                        qcType = new QualitySampleSeries().qcTypeValue();
//                    }
                }
                evaluate.setQcType(qcType);
                if (new QualityReplace().qcTypeValue().equals(qcType)) {
                    //替代样的分析项目名称显示替代物名称
                    anaItemName = StringUtil.isNotNull(control) ? control.getQcVolume() : "";
                }
                evaluate.setRedAnalyzeItemName(anaItemName);
                evaluate.setAnalyseItemId(anaItemId);
                evaluate.setSampleCode(StringUtil.isNotNull(loopArr) ? loopArr[1].toString() : "");
                if (EnumLIM.EnumQCType.替代物.getValue().equals(qcType)) {
                    String yySampleId = loopArr[4].toString();
                    DtoAnalyseData yyAnalyseData = analyseDataList.stream().filter(a -> a.getSampleId().equals(yySampleId)).findFirst().orElse(null);
                    if (yyAnalyseData != null) {
                        String yyCode = yyAnalyseData.getGatherCode();
                        evaluate.setSampleCode(StringUtil.isNotEmpty(yyCode) ? evaluate.getSampleCode() + "(" + yyCode + ")" : evaluate.getSampleCode());
                    }
                }
                Integer qcGrade = StringUtil.isNotNull(control) ? control.getQcGrade() : -1;
                if (qcGrade.equals(-1) && StringUtil.isNotNull(category)) {
                    qcGrade = category.getQcGrade();
                }
                evaluate.setQcGrade(qcGrade);
                evaluate.setQcTypeName(!qcType.equals(-1) ? QualityTaskFactory.getInstance().getQcSample(qcType).getRedFolderName("", qcGrade) : "");
                if (new QualitySampleSeries().qcTypeValue().equals(qcType)) {
                    DtoAnalyseData anaData = analyseDataList.stream().filter(a -> a.getId().equals(evaluate.getObjectId())).findFirst().orElse(null);
                    if (StringUtil.isNotNull(anaData) && EnumLIM.EnumQCGrade.内部质控.getValue().equals(anaData.getQcGrade())) {
                        evaluate.setQcTypeName("室内串联样");
                    }
                }
                evaluate.setJudgingMethodName((StringUtil.isNotNull(evaluate.getJudgingMethod()) && !evaluate.getJudgingMethod().equals(-1))
                        ? EnumBase.EnumJudgingMethod.getName(evaluate.getJudgingMethod()) : "/");

                if (StringUtil.isNotNull(evaluate.getIsPass())) {
                    if (evaluate.getIsPass()) {
                        evaluate.setQcMessage("合格");
                    } else {
                        evaluate.setQcMessage("不合格");
                    }
                }

                if (StringUtil.isNotEmpty(evaluate.getCheckItemValue()) && evaluate.getCheckItemValue().contains("无法计算")) {
                    evaluate.setQcMessage("未评价");
                    if (evaluate.getCheckItemValue().contains("不予评价")) {
                        evaluate.setQcMessage("不予评价");
                    }
                    evaluate.setCheckItemValue("无法计算");

                }
                //不确定类型为%时修正允许限值显示
                evaluate.setAllowLimitText(evaluate.getAllowLimit());
                if (EnumBase.EnumUncertainType.百分比.getValue().equals(evaluate.getUncertainType()) && StringUtil.isNotEmpty(evaluate.getAllowLimit())
                        && EnumBase.EnumJudgingMethod.范围判定.getValue().equals(evaluate.getJudgingMethod())) {
                    evaluate.setAllowLimitText(evaluate.getAllowLimit() + "%");
                }
                //处理待分组标识样品编号
                DtoSampleItemGroupTag sampleItemGroupTag = sampleItemGroupTagList.stream().filter(d -> d.getSampleId().equals(sampleId)
                        && d.getTestId().equals(testId)).findFirst().orElse(null);
                evaluate.setSampleCodeWithTag(evaluate.getSampleCode());
                if (sampleItemGroupTag != null) {
                    List<DtoQualityControlEvaluate> finalQualityControlEvaluateList = qualityControlEvaluateList;
                    sampleGroupService.generateAndSetTaggedSampleCode(
                            evaluate::setSampleCodeWithTag,
                            evaluate.getSampleCode(),
                            sampleItemGroupTag.getSampleCodeTag(),
                            sampleItemGroupTag.getSampleCategory(),
                            sampleItemGroupTag.getAssociateSampleId(),
                            // 4. Function: 如何根据ID查找关联编码
                            yySampleId -> {
                                Optional<DtoSampleItemGroupTag> yySampleGroupTag = sampleItemGroupTagList.stream()
                                        .filter(group -> yySampleId.equals(group.getSampleId())).findFirst();
                                if (yySampleGroupTag.isPresent()) {
                                    return finalQualityControlEvaluateList.stream()
                                            .filter(quality -> quality.getObjectId().equals(yySampleId))
                                            .findFirst()
                                            .map(DtoQualityControlEvaluate::getSampleCode);
                                }
                                return Optional.empty();
                            }
                    );
                }
                evaluate.setJudgeTypeName(EnumPRO.EnumQualityControlEvaluateType.getNameByValue(evaluate.getJudgeType()));
            }
            //按照样品编号,质控类型及分析项目名称过滤
            if (StringUtil.isNotEmpty(qualityControlEvaluateCriteria.getSampleCode())) {
                qualityControlEvaluateList = qualityControlEvaluateList.stream()
                        .filter(p -> p.getSampleCode().contains(qualityControlEvaluateCriteria.getSampleCode())).collect(Collectors.toList());
            }
            if (StringUtil.isNotEmpty(qualityControlEvaluateCriteria.getAnalyseItem())) {
                qualityControlEvaluateList = qualityControlEvaluateList.stream()
                        .filter(p -> p.getRedAnalyzeItemName().contains(qualityControlEvaluateCriteria.getAnalyseItem())).collect(Collectors.toList());
            }
            if (StringUtil.isNotNull(qualityControlEvaluateCriteria.getQcType())) {
                qualityControlEvaluateList = qualityControlEvaluateList.stream()
                        .filter(p -> qualityControlEvaluateCriteria.getQcType().equals(p.getQcType())).collect(Collectors.toList());
            }
            if (StringUtil.isNotNull(qualityControlEvaluateCriteria.getQcGrade())) {
                qualityControlEvaluateList = qualityControlEvaluateList.stream()
                        .filter(p -> qualityControlEvaluateCriteria.getQcGrade().equals(p.getQcGrade())).collect(Collectors.toList());
            }
            //判断现场平行是否需要计算 如果不计算，那么需要排除掉现场平行样
            //修改：现场平行样不需要排除，需要判断是否排除密码平行样
            if (!isLimit) {
                qualityControlEvaluateList = qualityControlEvaluateList.stream()
                        .filter(p -> !(EnumLIM.EnumQCGrade.外部质控.getValue().equals(p.getQcGrade())
                                && new QualityCipherParallel().qcTypeValue().equals(p.getQcType()))).collect(Collectors.toList());
            }
            //按照质控类型，样品编号，分析项目排序
            List<DtoCode> codeList = codeService.findCodes("LIM_QCConfigSort");
            qualityControlEvaluateList.sort(Comparator.comparing((DtoQualityControlEvaluate p) -> getQcSortNumByEvaluate(p, codeList), Comparator.reverseOrder())
                    .thenComparing(DtoQualityControlEvaluate::getSampleCode)
                    .thenComparing((DtoQualityControlEvaluate p) -> getSortNumByAnaItemSort(detailsSort, p), Comparator.reverseOrder()));

            pb.setData(qualityControlEvaluateList);
        }
    }

    /**
     * 获取分析项目排序值
     *
     * @param details  分析项目排序明细列表
     * @param evaluate 质控评价对象
     * @return 排序值
     */
    private int getSortNumByAnaItemSort(List<DtoAnalyzeItemSortDetail> details, DtoQualityControlEvaluate evaluate) {
        int sortNum = -1;
        if (StringUtil.isNotNull(evaluate.getAnalyseItemId())) {
            DtoAnalyzeItemSortDetail detail = details.stream().filter(p -> evaluate.getAnalyseItemId().equals(p.getAnalyzeItemId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(detail)) {
                sortNum = detail.getOrderNum();
            }
        }
        return sortNum;
    }

    @Transactional
    @Override
    public void updateEvaluate(String workSheetFolderId) {
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findByWorkSheetFolderIdAndIsDeletedFalse(workSheetFolderId);
        if (StringUtil.isEmpty(analyseDataList)) {
            List<String> sampleIds = receiveSubSampleRecord2SampleRepository.findByReceiveSubSampleRecordId(workSheetFolderId).stream().map(DtoReceiveSubSampleRecord2Sample::getSampleId).collect(Collectors.toList());
            analyseDataList = analyseDataRepository.findBySampleIdIn(sampleIds).stream().filter(a -> a.getIsCompleteField() && !a.getIsOutsourcing()).collect(Collectors.toList());
        }
        if (StringUtil.isNotEmpty(analyseDataList)) {
            List<String> analyseIdList = analyseDataList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
            List<String> testIdList = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
            List<DtoTest> testList = testRepository.findAll(testIdList);
            Map<String, DtoTest> testMap = testList.stream().collect(Collectors.toMap(DtoTest::getId, dto -> dto));
            List<DtoAnalyseOriginalRecord> originalRecordList = analyseOriginalRecordRepository.findByAnalyseDataIdIn(analyseIdList);
            Map<String, List<DtoAnalyseOriginalRecord>> originalRecordMap = originalRecordList.stream().collect(Collectors.groupingBy(DtoAnalyseOriginalRecord::getAnalyseDataId));
            List<DtoQualityControlEvaluate> oldEvaluateList = repository.findByObjectIdIn(analyseIdList);
            if (StringUtil.isNotEmpty(oldEvaluateList)) {
                //获取样品信息及质控信息
                List<String> sampleIdList = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
                List<Object[]> objects = comRepository.find("select a.id, a.qcId, a.sampleCategory, a.associateSampleId from DtoSample a where a.id in :sampleIdList and a.isDeleted = 0",
                        Collections.singletonMap("sampleIdList", sampleIdList));
                Map<String, Object[]> id2ArrMap = new HashMap<>();
                List<String> qcIdList = new ArrayList<>();
                for (Object[] objArr : objects) {
                    id2ArrMap.put(objArr[0].toString(), objArr);
                    if (!qcIdList.contains(objArr[1].toString())) {
                        qcIdList.add(objArr[1].toString());
                    }
                }
                List<DtoQualityControl> qualityControlList = qualityControlRepository.findAll(qcIdList);
                Map<String, DtoQualityControl> qualityControlMap = qualityControlList.stream().collect(Collectors.toMap(DtoQualityControl::getId, dto -> dto));
                List<DtoAnalyseQualityControlData> analyseQualityControlDataList = new ArrayList<>();
                for (DtoAnalyseData data : analyseDataList) {
                    Object[] objArr = id2ArrMap.get(data.getSampleId());
                    data.setSampleCategory(StringUtil.isNotNull(objArr) ? (Integer) objArr[2] : -1);
                    data.setAssociateSampleId(StringUtil.isNotNull(objArr) ? objArr[3].toString() : UUIDHelper.GUID_EMPTY);
                    //组装一个分析数据质控信息对象
                    String qcId = StringUtil.isNotNull(objArr) ? objArr[1].toString() : UUIDHelper.GUID_EMPTY;
                    data.setQcId(qcId);
                    DtoQualityControl qualityControl = qualityControlMap.getOrDefault(qcId, new DtoQualityControl());
                    DtoAnalyseOriginalRecord originalRecord = StringUtil.isNotEmpty(originalRecordMap.get(data.getId())) ? originalRecordMap.get(data.getId()).get(0) : null;
                    analyseQualityControlDataList.add(initAnalyseQualityControlData(data, qualityControl, originalRecord, testMap.get(data.getTestId())));
                }
                //更新质控评价
                updateQualityControlEvaluate(analyseQualityControlDataList, workSheetFolderId);
            }
        }
    }

    /**
     * 更新质控评价
     *
     * @param analyseQualityControlDataList 分析质控数据对象
     * @param workSheetFolderId             检测单id
     */
    @Transactional
    @Override
    public void updateQualityControlEvaluate(List<DtoAnalyseQualityControlData> analyseQualityControlDataList, String workSheetFolderId) {
        Date d1 = new Date();
        List<String> analyseIdList = new ArrayList<>();
        List<String> testIdList = new ArrayList<>();
        for (DtoAnalyseQualityControlData data : analyseQualityControlDataList) {
            analyseIdList.add(data.getAnalyseId());
            if (!testIdList.contains(data.getTestId())) {
                testIdList.add(data.getTestId());
            }
        }
        if (StringUtil.isNotEmpty(analyseIdList)) {
            //获取质控限值配置
            List<DtoQualityControlLimit> qualityControlLimitList = StringUtil.isNotEmpty(testIdList) ? qualityControlLimitRepository.findByTestIdIn(testIdList) : new ArrayList<>();
            List<DtoTest> testList = testRepository.findAll(testIdList);
            Map<String, DtoTest> testMap = testList.stream().collect(Collectors.toMap(DtoTest::getId, dto -> dto));
            List<String> parentTestIdList = testList.stream().map(DtoTest::getParentId).distinct().collect(Collectors.toList());
            //获取父测试项目的质控限值配置
            List<DtoQualityControlLimit> fuQualityControlLimitList = qualityControlLimitRepository.findByTestIdIn(parentTestIdList);
            if (StringUtil.isNotEmpty(fuQualityControlLimitList)) {
                qualityControlLimitList.addAll(fuQualityControlLimitList);
            }
            List<DtoQualityControlEvaluate> evaluateList = qualityControlEvaluateRepository.findByObjectIdIn(analyseIdList);
            //存在手动修改的质控评价限值， 限值及类型及说明要还原
            List<DtoQualityControlEvaluate> oldEvaluateList = new ArrayList<>(evaluateList);
            oldEvaluateList.forEach(v -> {
                if (EnumPRO.EnumQualityControlEvaluateType.人工评价.getValue().equals(v.getJudgeType()) && StringUtil.isEmpty(v.getAllowLimit())) {
                    v.setAllowLimit("/");
                }
            });
            deleteSampleCopy(analyseQualityControlDataList, evaluateList);
            updateEvaluateForStandard(evaluateList, qualityControlLimitList, analyseQualityControlDataList);
            CalculateService calculationService = SpringContextAware.getBean(CalculateService.class);
//            calculationService.setToken(PrincipalContextUser.getPrincipal().getToken());
            List<String> qcIdList = evaluateList.stream().map(DtoQualityControlEvaluate::getQcId).distinct().collect(Collectors.toList());
            List<DtoQualityControl> qcList = StringUtil.isNotEmpty(qcIdList) ? qualityControlRepository.findAll(qcIdList) : new ArrayList<>();
            Map<String, DtoQualityControl> qcMap = qcList.stream().collect(Collectors.toMap(DtoQualityControl::getId, dto -> dto));
            List<String> anaIdList = evaluateList.stream().map(DtoQualityControlEvaluate::getObjectId).distinct().collect(Collectors.toList());
            List<DtoAnalyseData> anaDataForEva = StringUtil.isNotEmpty(anaIdList) ? analyseDataRepository.findAll(anaIdList) : new ArrayList<>();
            evaluateList.forEach(p -> {
                Optional<DtoAnalyseData> data = anaDataForEva.stream().filter(ana -> p.getObjectId().equals(ana.getId())).findFirst();
                data.ifPresent(d -> {
                    p.setSampleTypeId(d.getSampleTypeId());
                });
            });
            Map<String, DtoAnalyseData> anaDataMap = anaDataForEva.stream().collect(Collectors.toMap(DtoAnalyseData::getId, dto -> dto));
            Map<String, String> anaId2QcInfoMap = new HashMap<>();
            updateEvaluateForCurve(evaluateList, qualityControlLimitList, analyseQualityControlDataList, workSheetFolderId, calculationService, anaId2QcInfoMap, testMap);
            updateEvaluateForBlank(evaluateList, qualityControlLimitList, analyseQualityControlDataList, anaDataMap, testList);
            Date d2 = new Date();
            log.info("===========================================数据准备：" + (d2.getTime() - d1.getTime()) + "ms===============================");

            List<Future<String>> asyncDataList = new ArrayList<>();
            final int batchSize = 50;
            List<DtoQualityControlEvaluate> list = null;
            CurrentPrincipalUser user = PrincipalContextUser.getPrincipal();
            UsernamePasswordAuthenticationToken token = new UsernamePasswordAuthenticationToken(user, null);
            for (DtoQualityControlEvaluate evaluate : evaluateList) {
                if (list == null) {
                    list = new ArrayList<>();
                }
                if (list.size() < batchSize) {
                    list.add(evaluate);
                } else if (list.size() == batchSize) {
                    //多线程处理排序
                    asyncDataList.add(qualityControlEvaluateTaskService.dealEvaluateData(list, analyseQualityControlDataList, qualityControlLimitList, workSheetFolderId, anaId2QcInfoMap, testList, qcMap, anaDataMap, token));
                    list = new ArrayList<>();
                    list.add(evaluate);
                }
            }
            if (StringUtil.isNotEmpty(list)) {
                asyncDataList.add(qualityControlEvaluateTaskService.dealEvaluateData(list, analyseQualityControlDataList, qualityControlLimitList, workSheetFolderId, anaId2QcInfoMap, testList, qcMap, anaDataMap, token));
            }
            //处理多线程处理的结果
            try {
                for (Future<String> asyncResult : asyncDataList) {
                    while (true) {
                        if (asyncResult.isDone() && !asyncResult.isCancelled()) {
                            break;
                        } else {
                            //防止CPU高速轮询被耗空
                            Thread.sleep(1);
                        }
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new BaseException("......多线程处理质控评价出错......");
            }
            Date d3 = new Date();
            log.info("=============================================循环用时：" + (d3.getTime() - d2.getTime()) + "ms====================================");
            updateQcInfoForStandard(evaluateList, analyseQualityControlDataList);
            updateQcInfoForCurve(evaluateList, analyseQualityControlDataList);
            Date d4 = new Date();
            qualityControlEvaluateRepository.save(evaluateList);
            Date d5 = new Date();
            log.info("=============================================evaluateList保存：" + (d5.getTime() - d4.getTime()) + "ms====================================");
            if (!UUIDHelper.GUID_EMPTY.equals(workSheetFolderId) && StringUtil.isNotEmpty(anaId2QcInfoMap)) {
                //手动更新质控评价时需要更新重新计算后的qcInfo
                Set<String> anaDataIdList = anaId2QcInfoMap.keySet();
                List<DtoAnalyseData> anaDataForUpdate = anaDataForEva.stream().filter(p -> anaDataIdList.contains(p.getId())).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(anaDataForUpdate)) {
                    for (DtoAnalyseData analyseData : anaDataForUpdate) {
                        if (anaId2QcInfoMap.containsKey(analyseData.getId())) {
                            if (EnumLIM.EnumQCType.曲线校核.getValue().equals(analyseData.getQcType())) {
                                //曲线校核需要判断多个质控信息是否都合格，有一条质控信息不合格则判断为不合格
//                                String passStr = anaId2QcInfoMap.get(analyseData.getId());
//                                String pass = !passStr.contains("不合格") ? "合格" : "不合格";
//                                analyseData.setQcInfo(pass);
                            } else {
                                analyseData.setQcInfo(anaId2QcInfoMap.get(analyseData.getId()));
                            }
                        }
                    }
                    analyseDataRepository.save(anaDataForUpdate);
                }
            }
            Date d6 = new Date();
            log.info("===============================================anaDataForUpdate保存：" + (d6.getTime() - d5.getTime()) + "ms=================================");
        }
    }


    /**
     * 跟新标样的质控评价
     *
     * @param evaluates                     评价
     * @param qualityControlLimits          质控限值配置
     * @param analyseQualityControlDataList 数据载体
     */
    @Transactional
    protected void updateEvaluateForStandard(List<DtoQualityControlEvaluate> evaluates, List<DtoQualityControlLimit> qualityControlLimits, List<DtoAnalyseQualityControlData> analyseQualityControlDataList) {
        List<DtoAnalyseQualityControlData> anaDataListForStandard = analyseQualityControlDataList.stream().filter(a -> new QualityStandard().qcTypeValue().equals(a.getQcType())).collect(Collectors.toList());
        List<String> testIds = anaDataListForStandard.stream().map(DtoAnalyseQualityControlData::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> tests = StringUtil.isNotEmpty(testIds) ? testRepository.findAll(testIds) : new ArrayList<>();
        List<String> anaIds = anaDataListForStandard.stream().map(DtoAnalyseQualityControlData::getAnalyseId).collect(Collectors.toList());
        List<DtoAnalyseData> analyseDataList = StringUtil.isNotEmpty(anaIds) ? analyseDataRepository.findAll(anaIds) : new ArrayList<>();
        Map<String, String> anaMap = analyseDataList.stream().collect(Collectors.toMap(DtoAnalyseData::getId, AnalyseData::getQcId, (r1, r2) -> r1));
        List<DtoQualityControlEvaluate> delEvaluateList = evaluates.stream().filter(e -> anaIds.contains(e.getObjectId())).collect(Collectors.toList());
        List<String> delEvaluateIds = delEvaluateList.stream().map(DtoQualityControlEvaluate::getId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(delEvaluateIds)) {
            evaluates.removeIf(e -> delEvaluateIds.contains(e.getId()));
            repository.deleteByIdIn(delEvaluateIds);
        }
        anaIds.forEach(anaId -> {
            DtoAnalyseQualityControlData loopData = analyseQualityControlDataList.stream().filter(a -> a.getAnalyseId().equals(anaId)).findFirst().orElse(null);
            if (StringUtil.isNotNull(loopData)) {
                Optional<DtoTest> testOptional = tests.stream().filter(t -> t.getId().equals(loopData.getTestId())).findFirst();
                List<DtoQualityControlLimit> controlLimitList = qualityControlLimits.stream().filter(p -> p.getTestId().equals(loopData.getTestId())
                        && p.getQcType().equals(loopData.getQcType()) && p.getQcGrade().equals(loopData.getQcGrade())).collect(Collectors.toList());
                AtomicInteger rangeRight = new AtomicInteger();
                if (StringUtil.isNotEmpty(controlLimitList)) {
                    controlLimitList.forEach(limit -> {
                        if (checkRangeConfigForStandard(limit, loopData, testOptional)) {
                            rangeRight.getAndIncrement();
                            DtoQualityControlEvaluate oldEvaluate = delEvaluateList.stream().filter(v -> v.getObjectId().equals(loopData.getAnalyseId())
                                    && limit.getId().equals(v.getLimitId())).findFirst().orElse(null);
                            DtoQualityControlEvaluate evaluate = new DtoQualityControlEvaluate();
                            evaluate.setObjectId(loopData.getAnalyseId());
                            evaluate.setLimitId(limit.getId());
                            evaluate.setQcId(anaMap.getOrDefault(loopData.getAnalyseId(), UUIDHelper.GUID_EMPTY));
                            evaluate.setCheckItem(EnumLIM.EnumCheckItemType.出证结果.getValue().equals(limit.getCheckItem()) ? EnumLIM.EnumCheckItemType.出证结果.toString() : limit.getCheckItemOther());
                            evaluate.setCheckItemValue("/");
                            evaluate.setJudgingMethod(limit.getJudgingMethod());
                            evaluate.setUncertainType(loopData.getUncertainType());
                            if (oldEvaluate != null && EnumPRO.EnumQualityControlEvaluateType.人工评价.getValue().equals(oldEvaluate.getJudgeType())) {
                                evaluate.setAllowLimit(StringUtil.isNotEmpty(oldEvaluate.getAllowLimit()) ? oldEvaluate.getAllowLimit() : "/");
                                evaluate.setJudgeType(EnumPRO.EnumQualityControlEvaluateType.人工评价.getValue());
                                evaluate.setRemark(oldEvaluate.getRemark());
                            } else {
                                if (EnumBase.EnumUncertainType.区间.getValue().equals(loopData.getUncertainType())) {
                                    evaluate.setAllowLimit(loopData.getQcValue() + "(" + loopData.getRangeLow() + "~" + loopData.getRangeHigh() + ")");
                                } else {
                                    evaluate.setAllowLimit(EnumBase.EnumJudgingMethod.范围判定.getValue().equals(limit.getJudgingMethod()) ? loopData.getQcValue() : limit.getAllowLimit());
                                }
                            }
                            updateEvaluateForStandard(limit, loopData, evaluate, testOptional);
                            evaluates.add(evaluate);
                        }
                    });
                }
                //没有限值或没有符合范围的情况
                if (StringUtil.isEmpty(controlLimitList) || rangeRight.get() == 0) {
                    DtoQualityControlEvaluate evaluate = new DtoQualityControlEvaluate();
                    evaluate.setObjectId(loopData.getAnalyseId());
                    evaluate.setLimitId(UUIDHelper.GUID_EMPTY);
                    evaluate.setQcId(anaMap.getOrDefault(loopData.getAnalyseId(), UUIDHelper.GUID_EMPTY));
                    evaluate.setCheckItem(EnumLIM.EnumCheckItemType.出证结果.toString());
                    evaluate.setJudgingMethod(EnumBase.EnumJudgingMethod.范围判定.getValue());
                    evaluate.setCheckItemValue("/");
                    evaluate.setUncertainType(loopData.getUncertainType());
                    DtoQualityControlEvaluate oldEvaluate = delEvaluateList.stream().filter(v -> v.getObjectId().equals(loopData.getAnalyseId())
                            && evaluate.getJudgingMethod().equals(v.getJudgingMethod()) && evaluate.getLimitId().equals(v.getLimitId())).findFirst().orElse(null);
                    if (oldEvaluate != null && EnumPRO.EnumQualityControlEvaluateType.人工评价.getValue().equals(oldEvaluate.getJudgeType())) {
                        evaluate.setAllowLimit(StringUtil.isNotEmpty(oldEvaluate.getAllowLimit()) ? oldEvaluate.getAllowLimit() : "/");
                        evaluate.setJudgeType(EnumPRO.EnumQualityControlEvaluateType.人工评价.getValue());
                        evaluate.setRemark(oldEvaluate.getRemark());
                    } else {
                        if (EnumBase.EnumUncertainType.区间.getValue().equals(loopData.getUncertainType())) {
                            evaluate.setAllowLimit(loopData.getQcValue() + "(" + loopData.getRangeLow() + "~" + loopData.getRangeHigh() + ")");
                        } else {
                            evaluate.setAllowLimit(loopData.getQcValue());
                        }
                    }
                    updateEvaluateForStandard(null, loopData, evaluate, testOptional);
                    evaluates.add(evaluate);
                }
            }
        });
    }

    /**
     * 更新标样qcinfo
     *
     * @param evaluates                     评价
     * @param analyseQualityControlDataList 数据载体
     */
    private void updateQcInfoForStandard(List<DtoQualityControlEvaluate> evaluates, List<DtoAnalyseQualityControlData> analyseQualityControlDataList) {
        List<DtoAnalyseQualityControlData> anaDataListForStandard = analyseQualityControlDataList.stream().filter(a -> new QualityStandard().qcTypeValue().equals(a.getQcType())).collect(Collectors.toList());
        List<String> anaIds = anaDataListForStandard.stream().map(DtoAnalyseQualityControlData::getAnalyseId).collect(Collectors.toList());
        List<DtoAnalyseData> analyseDataList = StringUtil.isNotEmpty(anaIds) ? analyseDataRepository.findAll(anaIds) : new ArrayList<>();
        evaluates.stream().filter(e -> anaIds.contains(e.getObjectId())).collect(Collectors.groupingBy(DtoQualityControlEvaluate::getObjectId)).forEach((anaId, standardEvaluates) -> {
            DtoAnalyseData analyseData = analyseDataList.stream().filter(a -> a.getId().equals(anaId)).findFirst().orElse(null);
            if (StringUtil.isNotNull(analyseData)) {
                if (standardEvaluates.stream().allMatch(s -> StringUtil.isNotNull(s.getIsPass()) && s.getIsPass())) {
                    analyseData.setQcInfo("合格");
                } else if (standardEvaluates.stream().anyMatch(s -> StringUtil.isNull(s.getIsPass()))) {
                    analyseData.setQcInfo("");
                } else {
                    analyseData.setQcInfo("不合格");
                }
            }
        });
    }

    /**
     * 更新曲线校核qcinfo
     *
     * @param evaluates                     评价
     * @param analyseQualityControlDataList 数据载体
     */
    protected void updateQcInfoForCurve(List<DtoQualityControlEvaluate> evaluates, List<DtoAnalyseQualityControlData> analyseQualityControlDataList) {
        List<DtoAnalyseQualityControlData> anaDataListForCurve = analyseQualityControlDataList.stream().filter(a -> new CurveCheck().qcTypeValue().equals(a.getQcType())).collect(Collectors.toList());
        List<String> anaIds = anaDataListForCurve.stream().map(DtoAnalyseQualityControlData::getAnalyseId).collect(Collectors.toList());
        List<DtoAnalyseData> analyseDataList = StringUtil.isNotEmpty(anaIds) ? analyseDataRepository.findAll(anaIds) : new ArrayList<>();
        evaluates.stream().filter(e -> anaIds.contains(e.getObjectId())).collect(Collectors.groupingBy(DtoQualityControlEvaluate::getObjectId)).forEach((anaId, curveEvaluates) -> {
            DtoAnalyseData analyseData = analyseDataList.stream().filter(a -> a.getId().equals(anaId)).findFirst().orElse(null);
            if (StringUtil.isNotNull(analyseData)) {
                if (curveEvaluates.size() == 1) {
                    DtoQualityControlEvaluate qualityControlEvaluate = curveEvaluates.get(0);
                    analyseData.setQcInfo(qualityControlEvaluate.getCheckItemValue());
                    //BUG2024121201861 录入界面，曲线校核样使用测定下限判定时,不需要显示质控信息
                    if (EnumBase.EnumJudgingMethod.小于测定下限.getValue().equals(qualityControlEvaluate.getJudgingMethod())) {
                        analyseData.setQcInfo("");
                    }
                }
            }
        });
    }

    /**
     * 更新曲线校核质控评价
     *
     * @param evaluates                     评价
     * @param qualityControlLimits          质控限制
     * @param analyseQualityControlDataList 数据
     * @param workSheetFolderId             检测单id
     * @param calculationService            计算服务
     * @param anaId2QcInfoMap               数据映射表
     * @param testMap                       测试项目映射表
     */
    @Transactional
    protected void updateEvaluateForCurve(List<DtoQualityControlEvaluate> evaluates, List<DtoQualityControlLimit> qualityControlLimits,
                                          List<DtoAnalyseQualityControlData> analyseQualityControlDataList, String workSheetFolderId,
                                          CalculationService calculationService, Map<String, String> anaId2QcInfoMap, Map<String, DtoTest> testMap) {
        List<DtoAnalyseQualityControlData> anaDataListForCurve = analyseQualityControlDataList.stream().filter(a -> new CurveCheck().qcTypeValue().equals(a.getQcType())).collect(Collectors.toList());
        List<String> anaIds = anaDataListForCurve.stream().map(DtoAnalyseQualityControlData::getAnalyseId).collect(Collectors.toList());
        List<DtoAnalyseData> analyseDataList = StringUtil.isNotEmpty(anaIds) ? analyseDataRepository.findAll(anaIds) : new ArrayList<>();
        Map<String, String> anaMap = analyseDataList.stream().collect(Collectors.toMap(DtoAnalyseData::getId, AnalyseData::getQcId, (r1, r2) -> r1));
        List<DtoQualityControlEvaluate> delEvaluateList = evaluates.stream().filter(e -> anaIds.contains(e.getObjectId())).collect(Collectors.toList());
        List<String> delEvaluateIds = delEvaluateList.stream().map(DtoQualityControlEvaluate::getId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(delEvaluateIds)) {
            evaluates.removeIf(e -> delEvaluateIds.contains(e.getId()));
            repository.deleteByIdIn(delEvaluateIds);
        }
        anaIds.forEach(anaId -> {
            DtoAnalyseQualityControlData loopData = analyseQualityControlDataList.stream().filter(a -> a.getAnalyseId().equals(anaId)).findFirst().orElse(null);
            if (StringUtil.isNotNull(loopData)) {
                DtoTest loopTest = testMap.get(loopData.getTestId());
                String parentTestId = StringUtil.isNotNull(loopTest) ? loopTest.getParentId() : UUIDHelper.GUID_EMPTY;
                List<DtoQualityControlLimit> controlLimitList = qualityControlLimits.stream().filter(p -> (p.getTestId().equals(loopData.getTestId())
                        || (!UUIDHelper.GUID_EMPTY.equals(parentTestId) && p.getTestId().equals(parentTestId)))
                        && p.getQcType().equals(loopData.getQcType()) && p.getQcGrade().equals(loopData.getQcGrade())).collect(Collectors.toList());
                String limitValue = loopData.getExamLimitValue();
                String lowerLimit = loopData.getLowerLimit();
                qualityControlLimitService.fillControlMsg(controlLimitList, limitValue, lowerLimit);
                for (EnumLIM.EnumCheckItemType checkItemType : EnumLIM.EnumCheckItemType.values()) {
                    if (EnumLIM.EnumCheckItemType.出证结果.getValue().equals(checkItemType.getValue())) {
                        List<DtoQualityControlLimit> controlLimitsOther = controlLimitList.stream().filter(p -> !(StringUtil.isNull(p.getCheckItem())
                                || p.getCheckItem().equals(-1))).collect(Collectors.toList());
                        if (controlLimitsOther.size() == 0) {
                            DtoQualityControlLimit qualityControlLimit = new DtoQualityControlLimit();
                            qualityControlLimit.setTestId(loopData.getTestId());
                            qualityControlLimit.setJudgingMethod(EnumBase.EnumJudgingMethod.相对偏差.getValue());
                            qualityControlLimit.setQcType(new CurveCheck().qcTypeValue());
                            qualityControlLimit.setQcGrade(EnumLIM.EnumQCGrade.内部质控.getValue());
                            qualityControlLimit.setIsDefault(true);
                            controlLimitsOther.add(qualityControlLimit);
                        } else {
                            controlLimitsOther = controlLimitsOther.stream().filter(p -> p.getCheckItem()
                                    .equals(EnumLIM.EnumCheckItemType.出证结果.getValue())).collect(Collectors.toList());
                        }
                        //如果没有配置曲线校核 出证结果判定，则不需要判定，如果出证和查曲线值 都没有配置，则需要默认值
                        if (controlLimitsOther.size() > 0) {
                            DtoQualityControlEvaluate evaluate = new DtoQualityControlEvaluate();
                            evaluate.setObjectId(loopData.getAnalyseId());
                            evaluate.setQcId(anaMap.getOrDefault(loopData.getAnalyseId(), UUIDHelper.GUID_EMPTY));
                            evaluate.setCheckItem(EnumLIM.EnumCheckItemType.出证结果.toString());
                            DtoQualityControlLimit limit = getControlLimit(controlLimitsOther, loopData.getTestValue(), calculationService);
                            evaluate.setJudgingMethod(StringUtil.isNotNull(limit) ? limit.getJudgingMethod() : -1);
                            evaluate.setLimitId(StringUtil.isNotNull(limit) ? limit.getIsDefault() ? UUIDHelper.GUID_EMPTY : limit.getId() : UUIDHelper.GUID_EMPTY);
                            DtoQualityControlEvaluate oldEvaluate = delEvaluateList.stream().filter(v -> v.getObjectId().equals(loopData.getAnalyseId())
                                    && evaluate.getJudgingMethod().equals(v.getJudgingMethod()) && evaluate.getLimitId().equals(v.getLimitId())).findFirst().orElse(null);
                            if (oldEvaluate != null && EnumPRO.EnumQualityControlEvaluateType.人工评价.getValue().equals(oldEvaluate.getJudgeType())) {
                                evaluate.setAllowLimit(StringUtil.isNotEmpty(oldEvaluate.getAllowLimit()) ? oldEvaluate.getAllowLimit() : "/");
                                evaluate.setJudgeType(EnumPRO.EnumQualityControlEvaluateType.人工评价.getValue());
                                evaluate.setRemark(oldEvaluate.getRemark());
                            }
                            String qcInfo = "";
                            if (EnumBase.EnumJudgingMethod.小于测定下限.getValue().equals(evaluate.getJudgingMethod())) {
                                qcInfo = StringUtil.isNotEmpty(loopData.getTestValue()) ? loopData.getTestValue() : "/";
                                evaluate.setCheckItemValue(qcInfo);
                            } else {
                                qcInfo = reCalculateQcInfoForCurve(controlLimitsOther, loopData, workSheetFolderId, testMap);
                                evaluate.setCheckItemValue(StringUtil.isNotEmpty(qcInfo) ? qcInfo : "/");
                            }
                            loopData.setQcInfo(qcInfo);
                            setLimitInfoForCurveEvaluate(evaluate, limit, loopData, new CurveCheck(), anaId2QcInfoMap);
                            evaluates.add(evaluate);
                        }
                    } else if (EnumLIM.EnumCheckItemType.公式参数.getValue().equals(checkItemType.getValue())) {
                        List<DtoQualityControlLimit> controlLimitsOther = controlLimitList.stream().filter(p -> EnumLIM.EnumCheckItemType.公式参数.getValue().equals(p.getCheckItem())).collect(Collectors.toList());
                        controlLimitsOther.stream().collect(Collectors.groupingBy(DtoQualityControlLimit::getCheckItemOther)).forEach((checkItem, limitForItem) -> {
                            DtoQualityControlLimit limit = getControlLimitForItemCurve(limitForItem, loopData.getQcValue(), calculationService);
                            if (StringUtil.isNotNull(limit)) {
                                DtoQualityControlEvaluate evaluate = new DtoQualityControlEvaluate();
                                evaluate.setJudgingMethod(limit.getJudgingMethod());
                                evaluate.setLimitId(limit.getId());
                                evaluate.setObjectId(loopData.getAnalyseId());
                                evaluate.setQcId(anaMap.getOrDefault(loopData.getAnalyseId(), UUIDHelper.GUID_EMPTY));
                                evaluate.setCheckItem(checkItem);
                                DtoQualityControlEvaluate oldEvaluate = delEvaluateList.stream().filter(v -> v.getObjectId().equals(loopData.getAnalyseId())
                                        && evaluate.getJudgingMethod().equals(v.getJudgingMethod()) && evaluate.getLimitId().equals(v.getLimitId())).findFirst().orElse(null);
                                if (oldEvaluate != null && EnumPRO.EnumQualityControlEvaluateType.人工评价.getValue().equals(oldEvaluate.getJudgeType())) {
                                    evaluate.setAllowLimit(StringUtil.isNotEmpty(oldEvaluate.getAllowLimit()) ? oldEvaluate.getAllowLimit() : "/");
                                    evaluate.setJudgeType(EnumPRO.EnumQualityControlEvaluateType.人工评价.getValue());
                                    evaluate.setRemark(oldEvaluate.getRemark());
                                }
                                String qcInfo = "";
                                if (EnumBase.EnumJudgingMethod.小于测定下限.getValue().equals(evaluate.getJudgingMethod())) {
                                    qcInfo = StringUtil.isNotEmpty(checkItem) ? loopData.getParamMap().getOrDefault(checkItem, "/") : "/";
                                    evaluate.setCheckItemValue(qcInfo);
                                } else {
                                    qcInfo = reCalculateQcInfoForItemCurve(limitForItem, loopData, checkItem, workSheetFolderId);
                                    evaluate.setCheckItemValue(StringUtil.isNotEmpty(qcInfo) ? qcInfo : "/");
                                }
                                loopData.setQcInfo(qcInfo);
                                setLimitInfoForCurveEvaluate(evaluate, limit, loopData, new CurveCheck(), anaId2QcInfoMap);
                                evaluates.add(evaluate);
                            }
                        });
                    }
                }
            }
        });
    }

    @Transactional
    protected void updateEvaluateForBlank(List<DtoQualityControlEvaluate> evaluates, List<DtoQualityControlLimit> qualityControlLimits, List<DtoAnalyseQualityControlData> analyseQualityControlDataList,
                                          Map<String, DtoAnalyseData> anaDataMap, List<DtoTest> testList) {
        List<DtoAnalyseQualityControlData> anaDataListForBlank = analyseQualityControlDataList.stream().filter(loopData -> new QualityBlank().qcTypeValue().equals(loopData.getQcType())
                || new QualityReagentBlank().qcTypeValue().equals(loopData.getQcType()) || new QualityTransportBlank().qcTypeValue().equals(loopData.getQcType())
                || new QualityInstrumentBlank().qcTypeValue().equals(loopData.getQcType()) || new QualityLocalBlank().qcTypeValue().equals(loopData.getQcType())
                || new QualityDilutionWater().qcTypeValue().equals(loopData.getQcType()) || new QualitySamplingContainerBlank().qcTypeValue().equals(loopData.getQcType())).collect(Collectors.toList());
        List<String> anaIds = anaDataListForBlank.stream().map(DtoAnalyseQualityControlData::getAnalyseId).collect(Collectors.toList());
        List<DtoAnalyseData> analyseDataList = StringUtil.isNotEmpty(anaIds) ? analyseDataRepository.findAll(anaIds) : new ArrayList<>();
        Map<String, String> anaMap = analyseDataList.stream().collect(Collectors.toMap(DtoAnalyseData::getId, AnalyseData::getQcId, (r1, r2) -> r1));
        List<DtoQualityControlEvaluate> delEvaluateList = evaluates.stream().filter(e -> anaIds.contains(e.getObjectId())).collect(Collectors.toList());
        List<String> delEvaluateIds = delEvaluateList.stream().map(DtoQualityControlEvaluate::getId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(delEvaluateIds)) {
            evaluates.removeIf(e -> delEvaluateIds.contains(e.getId()));
            repository.deleteByIdIn(delEvaluateIds);
        }
        List<String> testIds = testList.stream().map(DtoTest::getId).distinct().collect(Collectors.toList());
        List<DtoCurve> curveList = StringUtil.isNotEmpty(testIds) ? curveRepository.findByTestIdIn(testIds) : new ArrayList<>();
        Map<String, DtoCurve> curveMap = curveList.stream().collect(Collectors.groupingBy(DtoCurve::getTestId, Collectors.collectingAndThen(Collectors.toList(), list -> list.stream().sorted(Comparator.comparing(DtoCurve::getConfigDate, Comparator.reverseOrder())).findFirst().get())));
        for (DtoAnalyseQualityControlData loopData : anaDataListForBlank) {
            Optional<DtoTest> testOptional = testList.stream().filter(p -> loopData.getTestId().equals(p.getId())).findFirst();
            DtoCurve curve = curveMap.getOrDefault(loopData.getTestId(), null);
            List<DtoQualityControlLimit> controlLimitList = qualityControlLimits.stream().filter(p -> p.getTestId().equals(loopData.getTestId())
                    && p.getQcType().equals(loopData.getQcType()) && p.getQcGrade().equals(loopData.getQcGrade())).collect(Collectors.toList());
            //稀释水还需按照是否接种过滤一遍限值
            Integer isVaccinate = loopData.getIsVaccinate() != null ? loopData.getIsVaccinate() : 1;
            if (new QualityDilutionWater().qcTypeValue().equals(loopData.getQcType())) {
                controlLimitList = controlLimitList.stream().filter(p -> isVaccinate.equals(p.getIsVaccinate())).collect(Collectors.toList());
            }
            String limitValue = loopData.getExamLimitValue();
            String lowerLimit = loopData.getLowerLimit();
            qualityControlLimitService.fillControlMsg(controlLimitList, limitValue, lowerLimit);
            List<DtoQualityControlLimit> controlLimitListForA0 = controlLimitList.stream().filter(p -> EnumBase.EnumJudgingMethod.标曲A0波动范围.getValue().equals(p.getJudgingMethod())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(controlLimitListForA0)) {
                DtoQualityControlEvaluate evaluate = new DtoQualityControlEvaluate();
                evaluate.setObjectId(loopData.getAnalyseId());
                evaluate.setQcId(anaMap.getOrDefault(loopData.getAnalyseId(), UUIDHelper.GUID_EMPTY));
                //空白样，需要判断检查项是出证结果还是公式参数(其他) 1：出证结果 2：公式参数
                String checkItemOther = "";
                DtoQualityControlLimit limit = controlLimitListForA0.get(0);
                if (EnumLIM.EnumCheckItemType.公式参数.getValue().equals(limit.getCheckItem()) && StringUtil.isNotEmpty(limit.getCheckItemOther())) {
                    checkItemOther = limit.getCheckItemOther();
                }
                String samVal = halfLimit(loopData.getTestValueDst(), loopData.getExamLimitValue(), "sample");
                if (testOptional.isPresent()) {
                    if (EnumBase.EnumReviseType.先比较再修约.getValue().equals(testOptional.get().getReviseType())) {
                        samVal = halfLimit(loopData.getTestOriginValue(), loopData.getExamLimitValue(), "sample");
                        samVal = proService.getDecimal(loopData.getMostSignificance(), loopData.getMostDecimal(), samVal);
                    }
                }
                samVal = StringUtil.isNotEmpty(checkItemOther) ? loopData.getParamMap().getOrDefault(checkItemOther, "/") : samVal;
                evaluate.setCheckItem(StringUtil.isNotEmpty(checkItemOther) ? checkItemOther : EnumLIM.EnumCheckItemType.出证结果.toString());
                evaluate.setCheckItemValue(samVal);
                evaluate.setJudgingMethod(EnumBase.EnumJudgingMethod.标曲A0波动范围.getValue());
                evaluate.setLimitId(StringUtil.isNotNull(limit) ? limit.getId() : UUIDHelper.GUID_EMPTY);
                DtoQualityControlEvaluate oldEvaluate = delEvaluateList.stream().filter(v -> v.getObjectId().equals(loopData.getAnalyseId())
                        && EnumBase.EnumJudgingMethod.标曲A0波动范围.getValue().equals(v.getJudgingMethod())
                        && evaluate.getLimitId().equals(v.getLimitId())).findFirst().orElse(null);
                if (oldEvaluate != null && EnumPRO.EnumQualityControlEvaluateType.人工评价.getValue().equals(oldEvaluate.getJudgeType())) {
                    evaluate.setAllowLimit(StringUtil.isNotEmpty(oldEvaluate.getAllowLimit()) ? oldEvaluate.getAllowLimit() : "/");
                    evaluate.setJudgeType(EnumPRO.EnumQualityControlEvaluateType.人工评价.getValue());
                    evaluate.setRemark(oldEvaluate.getRemark());
                }
                if (!EnumPRO.EnumQualityControlEvaluateType.人工评价.getValue().equals(evaluate.getJudgeType())) {
                    evaluate.setAllowLimit("无法计算");
                }
                if (StringUtil.isNotNull(curve) && MathUtil.isNumeral(curve.getZeroPoint()) && !"0".equals(curve.getZeroPoint()) && StringUtil.isNotNull(limit)) {
                    String allowLimit = limit.getAllowLimitData();
                    //判定公式：比如检查项值为【0.1】，标准曲线空白值为【0.15】，允许限制为【X】<90 and
                    //【X】>110，则判定就为：【0.15*0.9<[0.1]<0.15*1.1】,如果满足，则为合格，反之不合格。
                    //③、如果没选择曲线、或者选择的曲线中没填写，或者为0，则判定为无法计算。
                    limit.setAllowLimit(resetAllowLimit(limit.getAllowLimitData(), curve.getZeroPoint()));
                    if (EnumPRO.EnumQualityControlEvaluateType.人工评价.getValue().equals(evaluate.getJudgeType())) {
                        DtoQualityControlLimit tempLimit = new DtoQualityControlLimit();
                        tempLimit.setAllowLimit(evaluate.getAllowLimit());
                        tempLimit.setAllowLimitData(evaluate.getAllowLimit());
                        evaluate.setIsPass(new QualityBlank().deviationQualified(tempLimit, samVal));
                    } else {
                        evaluate.setIsPass(new QualityBlank().deviationQualified(limit, samVal));
                        evaluate.setAllowLimit(limit.getAllowLimitData());
                    }
                    limit.setAllowLimit(allowLimit);
                }
                evaluates.add(evaluate);
            }
            List<DtoQualityControlLimit> controlLimitListOther = controlLimitList.stream().filter(p -> !EnumBase.EnumJudgingMethod.标曲A0波动范围.getValue().equals(p.getJudgingMethod())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(controlLimitListOther) || evaluates.stream().noneMatch(e -> e.getObjectId().equals(loopData.getAnalyseId()))) {
                DtoQualityControlEvaluate evaluate = new DtoQualityControlEvaluate();
                evaluate.setObjectId(loopData.getAnalyseId());
                evaluate.setQcId(anaMap.getOrDefault(loopData.getAnalyseId(), UUIDHelper.GUID_EMPTY));
                updateEvaluateForBlank(controlLimitListOther, loopData, evaluate, anaDataMap, testOptional, delEvaluateList);
                evaluates.add(evaluate);
            }
        }
    }

    /**
     * 标曲A0波动范围  根据  标准曲线空白值  重设限值
     * <p>
     * 判定公式：比如检查项值为【0.1】，标准曲线空白值为【0.15】，允许限制为【X】<90 and
     * 【X】>110，则判定就为：【0.15*0.9<[0.1]<0.15*1.1】,如果满足，则为合格，反之不合格。
     * ③、如果没选择曲线、或者选择的曲线中没填写，或者为0，则判定为无法计算。
     *
     * @param allowLimit 限值
     * @param zeroPoint  空白
     * @return 限值
     */
    private String resetAllowLimit(String allowLimit, String zeroPoint) {
        StringBuilder resetLimit = new StringBuilder();
        if (allowLimit.contains("and")) {
            String[] strArr = allowLimit.split("and");
            Stream.of(strArr).forEach(r -> {
                if (StringUtil.isNotEmpty(resetLimit.toString())) {
                    resetLimit.append(" and ");
                }
                resetLimit.append(resetRangeForLimit(r, zeroPoint));
            });
        } else {
            resetLimit.append(resetRangeForLimit(allowLimit, zeroPoint));
        }
        return resetLimit.toString();
    }

    private String resetRangeForLimit(String range, String zeroPoint) {
        for (String symbol : Arrays.asList(">=", ">", "<=", "<")) {
            if (range.contains(symbol)) {
                String[] strArr = range.split(symbol);
                if (strArr.length > 1 && MathUtil.isNumeral(strArr[1].trim())) {
                    String rightNumber = new BigDecimal(strArr[1].trim()).multiply(new BigDecimal(zeroPoint)).divide(new BigDecimal("100")).stripTrailingZeros().toPlainString();
                    return strArr[0].trim() + symbol + rightNumber;
                }
            }
        }
        return range;
    }

    /**
     * 删除原样加原样的质控评价
     *
     * @param analyseQualityControlDataList 数据载体
     * @param evaluateList                  评价
     */
    @Transactional
    protected void deleteSampleCopy(List<DtoAnalyseQualityControlData> analyseQualityControlDataList, List<DtoQualityControlEvaluate> evaluateList) {
        List<String> yyjyyObjIds = analyseQualityControlDataList.stream().filter(a -> new QualitySampleCopy().qcTypeValue().equals(a.getQcType())).map(DtoAnalyseQualityControlData::getAnalyseId).collect(Collectors.toList());
        List<String> delEvaluateIds = evaluateList.stream().filter(e -> yyjyyObjIds.contains(e.getObjectId())).map(DtoQualityControlEvaluate::getId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(delEvaluateIds)) {
            evaluateList.removeIf(e -> delEvaluateIds.contains(e.getId()));
            repository.deleteByIdIn(delEvaluateIds);
        }
    }

    /**
     * 更新质控评价对象的质控限值信息
     *
     * @param evaluate 质控评价对象
     * @param limit    质控限值对象
     */
    private void setLimitInfoForCurveEvaluate(DtoQualityControlEvaluate evaluate, DtoQualityControlLimit limit, DtoAnalyseQualityControlData loopData,
                                              QualityControlKind kind, Map<String, String> anaId2QcInfoMap) {
        String qcInfo = loopData.getQcInfo().replace("%", "");
        if (qcInfo.contains("无法计算")) {
            evaluate.setIsPass(false);
            if (!EnumPRO.EnumQualityControlEvaluateType.人工评价.getValue().equals(evaluate.getJudgeType())) {
                evaluate.setAllowLimit("/");
            }
        } else {
            if (StringUtil.isNotNull(limit)) {
                DtoQualityControlLimit tempLimit = new DtoQualityControlLimit();
                BeanUtils.copyProperties(limit, tempLimit, "id");
                if (EnumPRO.EnumQualityControlEvaluateType.人工评价.getValue().equals(evaluate.getJudgeType())) {
                    if (EnumBase.EnumJudgingMethod.小于测定下限.getValue().equals(evaluate.getJudgingMethod())) {
                        if (StringUtil.isNotEmpty(evaluate.getAllowLimit()) && MathUtil.isNumeral(evaluate.getAllowLimit())) {
                            tempLimit.setAllowLimit("[x]<" + evaluate.getAllowLimit());
                            tempLimit.setAllowLimitData("[x]<" + evaluate.getAllowLimit());
                            evaluate.setIsPass(kind.deviationQualified(tempLimit, qcInfo));
                        } else {
                            evaluate.setAllowLimit("/");
                            evaluate.setIsPass(null);
                        }
                    } else {
                        tempLimit.setAllowLimit(evaluate.getAllowLimit());
                        tempLimit.setAllowLimitData(evaluate.getAllowLimit());
                        evaluate.setIsPass(kind.deviationQualified(tempLimit, qcInfo));
                    }
                } else {
                    if (EnumBase.EnumJudgingMethod.小于测定下限.getValue().equals(evaluate.getJudgingMethod())) {
                        if (StringUtil.isNotEmpty(loopData.getLowerLimit()) && MathUtil.isNumeral(loopData.getLowerLimit())) {
                            tempLimit.setAllowLimit("[x]<" + loopData.getLowerLimit());
                            evaluate.setAllowLimit(loopData.getLowerLimit());
                            evaluate.setIsPass(kind.deviationQualified(tempLimit, qcInfo));
                        } else {
                            evaluate.setAllowLimit("/");
                            evaluate.setIsPass(null);
                        }
                    } else {
                        tempLimit.setAllowLimit(limit.getAllowLimitData());
                        tempLimit.setAllowLimitData(limit.getAllowLimitData());
                        evaluate.setAllowLimit(StringUtil.isNotEmpty(limit.getAllowLimitData()) ? limit.getAllowLimitData() : "/");
                        evaluate.setIsPass(kind.deviationQualified(tempLimit, qcInfo));
                    }
                }
            } else {
                evaluate.setAllowLimit("/");
                evaluate.setIsPass(null);
            }
        }
        // 质控限值id
        String passStr = "";
        if (StringUtil.isNotNull(evaluate.getIsPass())) {
            passStr = evaluate.getIsPass() ? "合格" : "不合格";
        }
        String existPassStr = anaId2QcInfoMap.getOrDefault(loopData.getAnalyseId(), "");
        anaId2QcInfoMap.put(loopData.getAnalyseId(), existPassStr + "," + passStr);
    }

    /**
     * 获取匹配的质控限值配置（曲线校核检查项为公式参数时专用）
     *
     * @param controlLimitList   质控限值配置列表
     * @param value              结果值
     * @param calculationService 计算服务对象
     */
    protected DtoQualityControlLimit getControlLimitForItemCurve(List<DtoQualityControlLimit> controlLimitList, String value, CalculationService calculationService) {
        if (MathUtil.isNumeral(value)) {
            for (DtoQualityControlLimit controlLimit : controlLimitList) {
                String rangeConfig = controlLimit.getRangeConfigData();
                if (StringUtil.isNotNull(rangeConfig)) {
                    if (DivationUtils.calculationResult(rangeConfig, new BigDecimal(value), calculationService)) {
                        return controlLimit;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 重新计算曲线校核样的偏差 (曲线校核样检查项为公式参数时专用)
     *
     * @param controlLimitList 质控限值配置列表
     * @param loopData         分析质控信息对象
     * @param checkItemOther   检测项公式参数名称
     * @return 偏差
     */
    private String reCalculateQcInfoForItemCurve(List<DtoQualityControlLimit> controlLimitList, DtoAnalyseQualityControlData loopData,
                                                 String checkItemOther, String workSheetFolderId) {
        String samVal = StringUtil.isNotEmpty(checkItemOther) ? loopData.getParamMap().getOrDefault(checkItemOther, "/") : "/";
        String qcInfo = "";
        if (!UUIDHelper.GUID_EMPTY.equals(workSheetFolderId)) {
            List<String> valueList = new ArrayList<>();
            valueList.add(loopData.getQcValue());
            valueList.add(samVal);
            valueList.add(samVal);
            List<DtoQualityControlLimit> limitList = controlLimitList.stream().filter(p -> p.getQcType().equals(loopData.getQcType())
                    && !EnumBase.EnumJudgingMethod.小于测定下限.getValue().equals(p.getJudgingMethod())).collect(Collectors.toList());
            DtoQualityControlLimit qualityControlLimit = new DtoQualityControlLimit();
            qualityControlLimit.setTestId(loopData.getTestId());
            qualityControlLimit.setJudgingMethod(EnumBase.EnumJudgingMethod.相对偏差.getValue());
            qualityControlLimit.setQcType(new CurveCheck().qcTypeValue());
            qualityControlLimit.setQcGrade(EnumLIM.EnumQCGrade.内部质控.getValue());
            limitList.add(qualityControlLimit);
            if (limitList.size() > 0) {
                Map<String, Object> qcMap = new CurveCheck().calculateDeviationValue(limitList, valueList);
                //计算室内范围偏差
                DtoQualityControlLimit limit = (DtoQualityControlLimit) qcMap.get("limit");
                if (StringUtil.isNotNull(limit)) {
                    String deviation = qcMap.get("qcRate").toString();
                    Integer sign = new CurveCheck().getQualityConfig().getMostSignificance();
                    Integer md = new CurveCheck().getQualityConfig().getMostDecimal();
                    String recoverRateStr = "";
                    recoverRateStr = proService.getDecimal(sign, md, deviation);
                    Boolean isPass = new CurveCheck().deviationQualified(limit, recoverRateStr);
                    if (StringUtil.isNotEmpty(recoverRateStr)) {
                        recoverRateStr = recoverRateStr.contains("无法计算") ? recoverRateStr : recoverRateStr + "%";
                    }
                    qcInfo = recoverRateStr;
                }
            }
        }
        return StringUtil.isNotNull(qcInfo) ? qcInfo : "";
    }


    /**
     * 收集质控评价不合格的质控样信息
     *
     * @param workSheetFolderId 检测单id
     * @return 不合格的质控样信息
     */
    @Override
    public List<String> checkEvaluatePass(String workSheetFolderId) {
        List<String> notPassTypeNameList = new ArrayList<>();
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findByWorkSheetFolderIdAndIsDeletedFalse(workSheetFolderId);
        if (StringUtil.isNotEmpty(analyseDataList)) {
            List<String> analyseIdList = analyseDataList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
            Map<String, DtoAnalyseData> analyseDataMap = analyseDataList.stream().collect(Collectors.toMap(DtoAnalyseData::getId, dto -> dto));
            List<DtoQualityControlEvaluate> evaluateNotPassList = repository.findByObjectIdIn(analyseIdList)
                    .stream().filter(p -> StringUtil.isNotNull(p.getIsPass()) && !p.getIsPass()).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(evaluateNotPassList)) {
                List<String> anaIdList = evaluateNotPassList.stream().map(DtoQualityControlEvaluate::getObjectId).distinct().collect(Collectors.toList());
                List<String> notPassSampleIdList = analyseDataList.stream().filter(p -> anaIdList.contains(p.getId()))
                        .map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
                List<Object[]> objArrList = (List<Object[]>) comRepository.find("select a.id, a.code, a.sampleCategory from DtoSample a where a.isDeleted = 0 " +
                        "and a.id in :ids", Collections.singletonMap("ids", notPassSampleIdList));
                Map<String, Object[]> objArrListMap = new HashMap<>();
                objArrList.forEach(p -> objArrListMap.put(p[0].toString(), p));
                //遍历不通过的评价信息，收集样品编号及质控类型
                for (DtoQualityControlEvaluate evaluate : evaluateNotPassList) {
                    DtoAnalyseData anaData = analyseDataMap.get(evaluate.getObjectId());
                    Object[] arr = objArrListMap.get(anaData.getSampleId());
                    String code = StringUtil.isNotNull(arr) ? arr[1].toString() : "";
                    Integer qcType = anaData.getQcType();
                    if ((StringUtil.isNull(qcType) || qcType.equals(-1)) && StringUtil.isNotNull(arr)) {
                        EnumPRO.EnumSampleCategory enumSampleCategory = EnumPRO.EnumSampleCategory.getByValue((Integer) arr[2]);
                        if (StringUtil.isNotNull(enumSampleCategory)) {
                            qcType = enumSampleCategory.getQcType();
                        }
                    }
                    notPassTypeNameList.add(EnumLIM.EnumQCType.getName(qcType) + "样" + code);
                }
            }
        }
        return notPassTypeNameList;
    }

    /**
     * 更新空白样质控评价信息
     *
     * @param controlLimitList 质控限值配置列表
     * @param loopData         分析质控信息对象
     * @param evaluate         质控评价信息对象
     */
    private void updateEvaluateForBlank(List<DtoQualityControlLimit> controlLimitList, DtoAnalyseQualityControlData loopData, DtoQualityControlEvaluate evaluate,
                                        Map<String, DtoAnalyseData> anaDataMap, Optional<DtoTest> testOptional, List<DtoQualityControlEvaluate> delEvaluateList) {
        //空白样，需要判断检查项是出证结果还是公式参数(其他) 1：出证结果 2：公式参数
        String checkItemOther = getCheckItemOther(controlLimitList);
        String samVal = "/";
        if (StringUtil.isNotEmpty(controlLimitList)) {
            samVal = halfLimit(loopData.getTestValueDst(), loopData.getExamLimitValue(), "sample");
            if (testOptional.isPresent()) {
                if (EnumBase.EnumReviseType.先比较再修约.getValue().equals(testOptional.get().getReviseType())) {
                    samVal = halfLimit(loopData.getTestOriginValue(), loopData.getExamLimitValue(), "sample");
                    samVal = proService.getDecimal(loopData.getMostSignificance(), loopData.getMostDecimal(), samVal);
                }
            }
            samVal = StringUtil.isNotEmpty(checkItemOther) ? loopData.getParamMap().getOrDefault(checkItemOther, "/") : samVal;
        }
        String limitValue = loopData.getExamLimitValue();
        String lowerLimit = loopData.getLowerLimit();
        qualityControlLimitService.fillControlMsg(controlLimitList, limitValue, lowerLimit);
        Map<String, Object> qcMap = new QualityBlank().calculateDeviationValue(controlLimitList, Arrays.asList(samVal, loopData.getExamLimitValue()));
        DtoQualityControlLimit limit = (DtoQualityControlLimit) qcMap.get("limit");
        //针对空白样，判断是否有配置小于检出限或者小于测定下限的限值配置，如有则默认获取其中一个（配置了小于检出限或者小于测定下限时，不需要根据samVal来判断使用的是哪个限值配置）
        if (StringUtil.isNull(limit)) {
            limit = checkDetectionTestLimit(controlLimitList);
        }
        String limitId = StringUtil.isNotNull(limit) ? limit.getIsDefault() ? UUIDHelper.GUID_EMPTY : limit.getId() : UUIDHelper.GUID_EMPTY;
        String passStr = qcMap.getOrDefault("qcRate", "").toString();
        evaluate.setCheckItem(StringUtil.isNotEmpty(checkItemOther) ? checkItemOther : (StringUtil.isNotNull(limit) ? EnumLIM.EnumCheckItemType.出证结果.toString() : "/"));

        //检查项为其他时，量纲获取公式参数上的量纲
        String dim = anaDataMap.containsKey(loopData.getAnalyseId()) ? anaDataMap.get(loopData.getAnalyseId()).getDimension() : "";
        if (StringUtil.isNotEmpty(checkItemOther)) {
            List<DtoAnalyseOriginalRecord> originalRecordList = analyseOriginalRecordRepository.findByAnalyseDataIdIn(Collections.singletonList(loopData.getAnalyseId()));
            if (StringUtil.isNotEmpty(originalRecordList)) {
                DtoParamsTestFormula paramsTestFormula = paramsTestFormulaRepository.findByObjId(originalRecordList.get(0).getTestFormulaId())
                        .stream().filter(p -> evaluate.getCheckItem().equals(p.getAlias())).findFirst().orElse(null);
                dim = StringUtil.isNotNull(paramsTestFormula) ? paramsTestFormula.getDimension() : "";
            }
        }
        evaluate.setDimensionName(dim);
        evaluate.setJudgingMethod(StringUtil.isNotNull(limit) ? limit.getJudgingMethod() : -1);
        DtoQualityControlEvaluate oldEvaluate = delEvaluateList.stream().filter(v -> v.getObjectId().equals(loopData.getAnalyseId())
                && evaluate.getJudgingMethod().equals(v.getJudgingMethod()) && limitId.equals(v.getLimitId())).findFirst().orElse(null);
        if (oldEvaluate != null && EnumPRO.EnumQualityControlEvaluateType.人工评价.getValue().equals(oldEvaluate.getJudgeType())) {
            evaluate.setAllowLimit(StringUtil.isNotEmpty(oldEvaluate.getAllowLimit()) ? oldEvaluate.getAllowLimit() : "/");
            evaluate.setJudgeType(EnumPRO.EnumQualityControlEvaluateType.人工评价.getValue());
            evaluate.setRemark(oldEvaluate.getRemark());
        }
        if (!EnumPRO.EnumQualityControlEvaluateType.人工评价.getValue().equals(evaluate.getJudgeType())) {
            evaluate.setAllowLimit(getYxPcForBlank(limit, loopData.getExamLimitValue(), loopData.getLowerLimit()));
        }
        Boolean pass = StringUtil.isNotEmpty(passStr) ? ("是".equals(passStr) || "合格".equals(passStr)) : null;
        //配置的允许限值不符合规范时，直接不评价
        if (StringUtil.isNotNull(limit) && EnumBase.EnumJudgingMethod.限值判定.getValue().equals(limit.getJudgingMethod()) &&
                ((EnumPRO.EnumQualityControlEvaluateType.人工评价.getValue().equals(evaluate.getJudgeType()) && !PATTERN.matcher(evaluate.getAllowLimit()).find())
                        || (EnumPRO.EnumQualityControlEvaluateType.系统验证.getValue().equals(evaluate.getJudgeType()) && !PATTERN.matcher(limit.getAllowLimit()).find()))) {
            evaluate.setIsPass(null);
        } else {
            if (EnumPRO.EnumQualityControlEvaluateType.人工评价.getValue().equals(evaluate.getJudgeType())) {
                evaluate.setIsPass(checkPassForBlank(limit, evaluate.getAllowLimit(), evaluate.getAllowLimit(), samVal, pass));
            } else {
                evaluate.setIsPass(checkPassForBlank(limit, loopData.getExamLimitValue(), loopData.getLowerLimit(), samVal, pass));
            }
            //检查项值不是数字类型时（小于检出限）,默认合格
            if (StringUtil.isNotNull(limit) && StringUtil.isNotEmpty(samVal) && !MathUtil.isNumeral(samVal)) {
                evaluate.setIsPass(true);
            }
        }
        if (StringUtil.isNotNull(limit) && EnumLIM.EnumCheckItemType.出证结果.getValue().equals(limit.getCheckItem())) {
            samVal = loopData.getTestValue();
        }
        evaluate.setCheckItemValue(StringUtil.isNotEmpty(samVal) ? samVal : "/");
        // 质控限值id
        evaluate.setLimitId(limitId);
    }

    /**
     * 更新阴性/阳性对照试验质控评价信息
     *
     * @param controlLimitList 质控限值配置列表
     * @param loopData         分析质控信息对象
     * @param evaluate         质控评价信息对象
     */
    private void updateEvaluateForPosNeg(List<DtoQualityControlLimit> controlLimitList, DtoAnalyseQualityControlData loopData, DtoQualityControlEvaluate evaluate) {
        String samVal = StringUtil.isNotEmpty(controlLimitList) ? loopData.getTestValueDst() : "/";
        evaluate.setCheckItemValue(StringUtil.isNotEmpty(samVal) ? samVal : "/");
        Map<String, Object> qcMap = new QualityPositiveControl().calculateDeviationValue(controlLimitList, Arrays.asList(samVal, loopData.getExamLimitValue()));
        DtoQualityControlLimit limit = (DtoQualityControlLimit) qcMap.get("limit");
        setLimitInfoForEvaluate(evaluate, limit, loopData.getQcInfo().replace("%", ""), new QualityParallel());
        String passStr = qcMap.getOrDefault("qcRate", "").toString();
        Boolean pass = StringUtil.isNotEmpty(passStr) ? ("是".equals(passStr) || "合格".equals(passStr)) : null;
        //配置的允许限值不符合规范时，直接不评价
        if (StringUtil.isNotNull(limit) && EnumBase.EnumJudgingMethod.限值判定.getValue().equals(limit.getJudgingMethod())
                && !PATTERN.matcher(limit.getAllowLimit()).find()) {
            evaluate.setIsPass(null);
        } else {
            evaluate.setIsPass(pass);
            //检查项值不是数字类型时（小于检出限）,默认合格
            if (StringUtil.isNotNull(limit) && StringUtil.isNotEmpty(samVal) && !MathUtil.isNumeral(samVal)) {
                evaluate.setIsPass(true);
            }
        }
    }

    /**
     * 判断空白样允许限值
     *
     * @param limit          质控限值配置
     * @param examLimitValue 检出限
     * @param lowerLimit     测定下限
     * @return 允许限值
     */
    public static String getYxPcForBlank(DtoQualityControlLimit limit, String examLimitValue, String lowerLimit) {
        String yxPc = "/";
        if (StringUtil.isNotNull(limit)) {
            if (EnumBase.EnumJudgingMethod.小于检出限.getValue().equals(limit.getJudgingMethod())) {
                yxPc = examLimitValue;
            } else if (EnumBase.EnumJudgingMethod.小于测定下限.getValue().equals(limit.getJudgingMethod())) {
                yxPc = lowerLimit;
            } else {
                yxPc = StringUtil.isNotEmpty(limit.getAllowLimitData()) ? limit.getAllowLimitData() : "/";
            }
        }
        return yxPc;
    }

    /**
     * 判断空白样是否合格
     *
     * @param limit           质控限值配置
     * @param examLimitValue  检出限
     * @param lowerLimitValue 测定下限
     * @param samValue        检查项值
     * @param isPass          是否合格
     * @return 是否合格
     */
    public static Boolean checkPassForBlank(DtoQualityControlLimit limit, String examLimitValue, String lowerLimitValue, String samValue, Boolean isPass) {
        Boolean pass = null;
        if (StringUtil.isNotNull(limit)) {
            if (EnumBase.EnumJudgingMethod.小于检出限.getValue().equals(limit.getJudgingMethod())) {
                if (MathUtil.isNumeral(samValue) && MathUtil.isNumeral(examLimitValue)) {
                    pass = new BigDecimal(samValue).compareTo(new BigDecimal(examLimitValue)) < 0 ? true : false;
                }
            } else if (EnumBase.EnumJudgingMethod.小于测定下限.getValue().equals(limit.getJudgingMethod())) {
                if (MathUtil.isNumeral(samValue) && MathUtil.isNumeral(lowerLimitValue)) {
                    pass = new BigDecimal(samValue).compareTo(new BigDecimal(lowerLimitValue)) < 0 ? true : false;
                }
            } else {
                pass = isPass;
            }
        }
        return pass;
    }

    /**
     * 更新平行样质控评价信息
     *
     * @param controlLimitList              质控限值配置列表
     * @param loopData                      分析质控信息对象
     * @param evaluate                      质控评价信息对象
     * @param analyseQualityControlDataList 分析数据对象
     * @param workSheetFolderId             检测单id
     * @param anaId2QcInfoMap               分析数据id和qcInfo的映射关系
     * @param calculationService            计算服务对象
     */
    protected void updateEvaluateForParallel(List<DtoQualityControlLimit> controlLimitList, DtoAnalyseQualityControlData loopData,
                                             DtoQualityControlEvaluate evaluate, List<DtoAnalyseQualityControlData> analyseQualityControlDataList,
                                             String workSheetFolderId, Map<String, String> anaId2QcInfoMap, CalculationService calculationService) {
        DtoAnalyseQualityControlData yyAnaData = analyseQualityControlDataList.stream().filter(p -> p.getSampleId().equals(loopData.getAssociateSampleId())
                && p.getTestId().equals(loopData.getTestId())).findFirst().orElse(null);
        String samVal = StringUtil.isNotNull(yyAnaData) ? halfLimit(yyAnaData.getTestOriginValue(), loopData.getExamLimitValue(), "qualityControl") : "";
        DtoQualityControlLimit limit = getControlLimit(controlLimitList, samVal, calculationService);
        String qcInfo = reCalculateQcInfoForParallel(controlLimitList, loopData, analyseQualityControlDataList, workSheetFolderId, yyAnaData);
        if (!qcInfo.equals(loopData.getQcInfo())) {
            anaId2QcInfoMap.put(loopData.getAnalyseId(), qcInfo);
        }
        loopData.setQcInfo(qcInfo);
        evaluate.setCheckItemValue(StringUtil.isNotEmpty(loopData.getQcInfo()) ? loopData.getQcInfo() : "/");
        setLimitInfoForEvaluate(evaluate, limit, loopData.getQcInfo().replace("%", ""), new QualityParallel());
    }

    /**
     * 重新计算平行样的偏差
     *
     * @param controlLimitList              质控限值配置列表
     * @param loopData                      分析质控信息对象
     * @param analyseQualityControlDataList 分析数据对象
     * @param workSheetFolderId             检测单id
     * @return 偏差
     */
    protected String reCalculateQcInfoForParallel(List<DtoQualityControlLimit> controlLimitList, DtoAnalyseQualityControlData loopData,
                                                  List<DtoAnalyseQualityControlData> analyseQualityControlDataList,
                                                  String workSheetFolderId, DtoAnalyseQualityControlData yyAnaData) {
        String qcInfo = StringUtil.isNotEmpty(loopData.getQcInfo()) ? loopData.getQcInfo() : "";
        if (!UUIDHelper.GUID_EMPTY.equals(workSheetFolderId) && StringUtil.isNotNull(yyAnaData)) {
            String yyTestValue = yyAnaData.getTestOriginValue();
            //判断是否有加原样
            List<DtoAnalyseQualityControlData> dataList = analyseQualityControlDataList.stream()
                    .filter(p -> p.getAssociateSampleId().equals(yyAnaData.getSampleId())
                            && p.getQcType().equals(new QualitySampleCopy().qcTypeValue())).collect(Collectors.toList());
            //需要调整 判断是否存在加原样，有的话需要以中间值当做结果
            if (dataList.size() > 0) {
                yyTestValue = yyAnaData.getSeriesValue();
            }
            List<DtoTest> testList = testService.findRedisByIds(Collections.singletonList(loopData.getTestId()));
            DtoTest test = StringUtil.isNotEmpty(testList) ? testList.get(0) : new DtoTest();
            yyTestValue = analyseDataService.halfLimitTestOrignValue(yyTestValue, yyAnaData.getTestValueDst(), loopData.getExamLimitValue(), test);
            if (EnumLIM.EnumCalculateWay.先修约后计算.getValue().equals(test.getCalculateWay())) {
                yyTestValue = proService.getDecimal(yyAnaData.getMostSignificance(), yyAnaData.getMostDecimal(), yyTestValue);
            }
            List<DtoQualityControlLimit> limitList = controlLimitList.stream().filter(p -> p.getQcType()
                    .equals(new QualityParallel().qcTypeValue()) && loopData.getQcGrade().equals(p.getQcGrade())).collect(Collectors.toList());
            DtoQualityControlLimit qualityControlLimit = new DtoQualityControlLimit();
            qualityControlLimit.setTestId(loopData.getTestId());
            qualityControlLimit.setJudgingMethod(EnumBase.EnumJudgingMethod.相对偏差.getValue());
            qualityControlLimit.setQcType(new QualityParallel().qcTypeValue());
            qualityControlLimit.setQcGrade(EnumLIM.EnumQCGrade.内部质控.getValue());
            //添加一个默认的类型
            limitList.add(qualityControlLimit);
            //点击更新质控信息按钮时需要重新计算平行样的偏差

            if (StringUtil.isNotNull(yyAnaData)) {
                List<DtoAnalyseQualityControlData> pxDataForYy;
                List<String> inTestValue = new ArrayList<>();
                if (EnumLIM.EnumQCGrade.内部质控.getValue().equals(loopData.getQcGrade())) {
                    //找到原样的所有室内平行样
                    pxDataForYy = analyseQualityControlDataList.stream().filter(p -> p.getAssociateSampleId().equals(yyAnaData.getSampleId())
                            && p.getTestId().equals(loopData.getTestId()) && EnumLIM.EnumQCGrade.内部质控.getValue().equals(p.getQcGrade())
                            && new QualityParallel().qcTypeValue().equals(p.getQcType())).collect(Collectors.toList());
                    inTestValue = pxDataForYy.stream().map(p -> analyseDataService.halfLimitTestOrignValue(p.getTestOriginValue(),
                            proService.getDecimal(loopData.getMostSignificance(), loopData.getMostDecimal(), p.getTestValueDst()),
                            loopData.getExamLimitValue(), test)).collect(Collectors.toList());
                } else {
                    //加入当前的室外平行样
                    pxDataForYy = new ArrayList<>(Collections.singletonList(loopData));
                    //找到室外平行样对应的室外平行样
                    pxDataForYy.addAll(analyseQualityControlDataList.stream().filter(p -> p.getAssociateSampleId().equals(loopData.getSampleId())
                            && p.getTestId().equals(loopData.getTestId()) && EnumLIM.EnumQCGrade.外部质控.getValue().equals(p.getQcGrade())
                            && new QualityParallel().qcTypeValue().equals(p.getQcType())).collect(Collectors.toList()));
                    //找到对应的室内平行样
                    List<DtoAnalyseQualityControlData> pxDataList = analyseQualityControlDataList.stream()
                            .filter(p -> p.getAssociateSampleId().equals(yyAnaData.getSampleId())
                                    && EnumLIM.EnumQCGrade.内部质控.getValue().equals(p.getQcGrade())
                                    && yyAnaData.getTestId().equals(p.getTestId())
                                    && new QualityParallel().qcTypeValue().equals(p.getQcType())).collect(Collectors.toList());
                    if (pxDataList.size() > 0) {
                        List<String> pxValueList = new ArrayList<>();
                        pxDataList.forEach(p -> {
                            String testOriginValue = p.getTestOriginValue();
                            if (dataList.size() > 0) {
                                testOriginValue = p.getSeriesValue();
                            }
                            pxValueList.add(testOriginValue);
                        });
                        if (EnumLIM.EnumCalculateWay.先修约后计算.getValue().equals(test.getCalculateWay())) {
                            pxValueList.clear();
                            pxDataList.forEach(p -> {
                                String testOriginValue = p.getTestOriginValue();
                                if (dataList.size() > 0) {
                                    testOriginValue = p.getSeriesValue();
                                }
                                pxValueList.add(proService.getDecimal(p.getMostSignificance(),
                                        p.getMostDecimal(), testOriginValue));
                            });
                        }
                        pxValueList.add(yyTestValue);
                        BigDecimal sum = new BigDecimal(0);
                        for (String val : pxValueList) {
                            val = halfLimit(val, loopData.getExamLimitValue(), "qualityControl");
                            try {
                                BigDecimal value = new BigDecimal(val);
                                sum = sum.add(value);
                            } catch (Exception ex) {
                                System.out.println(ex.getMessage());
                            }
                        }
                        BigDecimal avg = sum.divide(new BigDecimal(pxValueList.size()), ProCodeHelper.COMMON_ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN).stripTrailingZeros();
                        if (EnumLIM.EnumCalculateWay.先修约后计算.getValue().equals(test.getCalculateWay())) {
                            avg = new BigDecimal(proService.getDecimal(yyAnaData.getMostSignificance(), yyAnaData.getMostDecimal(), avg.toString()));
                        }
                        yyTestValue = avg.toString();
                    }
                }

                BigDecimal sum = new BigDecimal(0);
                if (StringUtil.isNotEmpty(yyTestValue) && MathUtil.isNumeral(yyTestValue)) {
                    sum = sum.add(new BigDecimal(halfLimit(yyTestValue, yyAnaData.getExamLimitValue(), "qualityControl")));
                }
                int count = 1;
                for (DtoAnalyseQualityControlData data : pxDataForYy) {
                    String val = data.getTestValue();
                    if (dataList.size() > 0) {
                        val = data.getSeriesValue();
                    }
                    if (EnumLIM.EnumCalculateWay.先修约后计算.getValue().equals(test.getCalculateWay())) {
                        val = proService.getDecimal(data.getMostSignificance(), data.getMostDecimal(), val);
                    }
                    String oriVal = halfLimit(val, data.getExamLimitValue(), "qualityControl");
                    if (MathUtil.isNumeral(oriVal)) {
                        sum = sum.add(new BigDecimal(oriVal));
                        count++;
                    }
                }
                List<String> valueList = new ArrayList<>();
                boolean isCompute = Boolean.FALSE;
                String yyVal = halfLimit(yyTestValue, yyAnaData.getExamLimitValue(), "qualityControl");
                boolean isComputeOne = isExamLimit(yyVal, loopData.getExamLimitValue());
                valueList.add(yyVal);

                String inPxVal = "";
                if (EnumLIM.EnumQCGrade.内部质控.getValue().equals(loopData.getQcGrade())) {
                    String testOriginValue = loopData.getTestOriginValue();
                    if (dataList.size() > 0) {
                        testOriginValue = loopData.getSeriesValue();
                    }
                    inPxVal = analyseDataService.halfLimitTestOrignValue(testOriginValue, loopData.getTestValueDst(), loopData.getExamLimitValue(), test);
                    if (EnumLIM.EnumCalculateWay.先修约后计算.getValue().equals(test.getCalculateWay())) {
                        inPxVal = proService.getDecimal(loopData.getMostSignificance(), loopData.getMostDecimal(), inPxVal);
                    }
                    inPxVal = halfLimit(inPxVal, loopData.getExamLimitValue(), "qualityControl");
                } else {
                    if (StringUtil.isNotEmpty(pxDataForYy)) {
                        DtoAnalyseQualityControlData xcData = pxDataForYy.get(0);
                        String testOriginValue = xcData.getTestValue();
                        if (dataList.size() > 0) {
                            testOriginValue = xcData.getSeriesValue();
                        }
                        inPxVal = analyseDataService.halfLimitTestOrignValue(testOriginValue, xcData.getTestValueDst(), xcData.getExamLimitValue(), test);
                        if (EnumLIM.EnumCalculateWay.先修约后计算.getValue().equals(test.getCalculateWay())) {
                            inPxVal = proService.getDecimal(xcData.getMostSignificance(), xcData.getMostDecimal(), inPxVal);
                        }
                        inPxVal = halfLimit(inPxVal, xcData.getExamLimitValue(), "qualityControl");
                    }
                }
                boolean isComputeTwo = isExamLimit(inPxVal, loopData.getExamLimitValue());
                valueList.add(inPxVal);
                String avg = sum.divide(new BigDecimal(count), ProCodeHelper.COMMON_ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN).toString();
                if (EnumLIM.EnumCalculateWay.先修约后计算.getValue().equals(test.getCalculateWay())) {
                    avg = proService.getDecimal(yyAnaData.getMostSignificance(), yyAnaData.getMostDecimal(), avg);
                }
                if (!isComputeOne || !isComputeTwo) {
                    if (!(!isComputeOne && !isComputeTwo)) {
                        isCompute = Boolean.TRUE;
                    }
                }
                valueList.add(avg);
                if (inTestValue.size() > 1) {
                    isCompute = Boolean.FALSE;
                    //多个平行样时需要按照标准差的方式进行计算
                    List<String> yyPxValList = analyseDataService.collectYyPxVal(yyVal, inTestValue, yyAnaData.getMostSignificance(), yyAnaData.getMostDecimal());
                    valueList.addAll(yyPxValList);
                }
                //计算偏差
                String rateStr = "";
                if (!isCompute) {
                    Map<String, Object> qcMap = new QualityParallel().calculateDeviationValue(limitList, valueList);
                    DtoQualityControlLimit limit = (DtoQualityControlLimit) qcMap.get("limit");
                    rateStr = qcMap.getOrDefault("qcRate", "").toString();
                    if (StringUtil.isNotNull(limit) && StringUtil.isNotEmpty(rateStr)) {
                        Integer sign = new QualityParallel().getQualityConfig().getMostSignificance();
                        Integer md = new QualityParallel().getQualityConfig().getMostDecimal();
                        //平行样qcInfo按照两位有效数字，两位小数修约
                        rateStr = proService.getDecimal(sign, md, rateStr);
                        //相对偏差或者相对误差需要带%
                        if (EnumBase.EnumJudgingMethod.相对偏差.getValue().equals(limit.getJudgingMethod())
                                || EnumBase.EnumJudgingMethod.相对误差.getValue().equals(limit.getJudgingMethod())) {
                            rateStr = rateStr.contains("无法计算") ? rateStr : rateStr + "%";
                        }
                    }
                } else {
                    rateStr = "无法计算";
                }
                qcInfo = rateStr;
            }

        }
        return qcInfo;
    }

    /**
     * 平行计算需要检出限一半计算
     *
     * @return 检出限一半
     */
    protected String halfLimit(String value, String examLimitValue, String type) {
        //获取小于检出限计算方式参数配置
        String configValue = analyseDataService.getConfigValue(type);
        if (MathUtil.isNumeral(value) && MathUtil.isNumeral(examLimitValue)) {
            //根据参数值处理小于检出限的数值
            //检出限大于检测结果
            if (MathUtil.getBigDecimal(examLimitValue).compareTo(MathUtil.getBigDecimal(value)) > 0) {
                if (StringUtil.isNotEmpty(configValue)) {
                    if (EnumBase.EnumLessExamLimit.检出限一半.getValue().equals(configValue)) {
                        value = (MathUtil.getBigDecimal(examLimitValue).divide(new BigDecimal(2))).toString();
                    }
                    if (EnumBase.EnumLessExamLimit.检出限.getValue().equals(configValue)) {
                        value = examLimitValue;
                    }
                    if (EnumBase.EnumLessExamLimit.零.getValue().equals(configValue)) {
                        value = "0";
                    }
                    if (EnumBase.EnumLessExamLimit.检测结果.getValue().equals(configValue)) {
                        value = value;
                    }
                } else {
                    value = (MathUtil.getBigDecimal(examLimitValue).divide(new BigDecimal(2))).toString();
                }
            }
        }
        return value;
    }

    private boolean isExamLimit(String value, String examLimitValue) {
        boolean isExamLimit = Boolean.FALSE;
        if (MathUtil.isNumeral(value) && MathUtil.isNumeral(examLimitValue)) {
            //检出限大于检测结果
            if (MathUtil.getBigDecimal(examLimitValue).compareTo(MathUtil.getBigDecimal(value)) > 0) {
                isExamLimit = Boolean.TRUE;
            }
        }
        return isExamLimit;
    }

    /**
     * 判断开关是否开启
     *
     * @return 是否开启
     */
    public Boolean switchIsOpen() {
        DtoCode code = codeService.findByCode(ProCodeHelper.PARALLEL_SCENE);
        return StringUtil.isNotNull(code) && "1".equals(code.getDictValue());
    }

    /**
     * 更新加标样质控评价信息
     *
     * @param controlLimitList 质控限值配置列表
     * @param loopData         分析质控信息对象
     * @param evaluate         质控评价信息对象
     */
    private void updateEvaluateForJb(List<DtoQualityControlLimit> controlLimitList, DtoAnalyseQualityControlData loopData, DtoQualityControlEvaluate evaluate,
                                     CalculationService calculationService, Map<String, DtoQualityControl> qcMap) {
        evaluate.setCheckItemValue(StringUtil.isNotEmpty(loopData.getQcInfo()) ? loopData.getQcInfo().replace("%", "") : "/");
        DtoQualityControl qc = qcMap.get(evaluate.getQcId());
        DtoQualityControlLimit limit = StringUtil.isNotNull(qc) ? getControlLimit(controlLimitList, qc.getRealSampleTestValue(), calculationService) : null;
        QualityControlKind kind = QualityTaskFactory.getInstance().getQcSample(new QualityMark().qcTypeValue());
        if (new QualityBlankMark().qcTypeValue().equals(loopData.getQcType())) {
            kind = QualityTaskFactory.getInstance().getQcSample(new QualityBlankMark().qcTypeValue());
        }
        String qcInfo = StringUtil.isNotEmpty(loopData.getQcInfo()) ? loopData.getQcInfo() : "";
        setLimitInfoForEvaluate(evaluate, limit, qcInfo.replace("%", ""), kind);
    }

    /**
     * 重新计算曲线校核样的偏差
     *
     * @param controlLimitList  质控限值配置列表
     * @param loopData          分析质控信息对象
     * @param workSheetFolderId 检测单id
     * @return 偏差
     */
    protected String reCalculateQcInfoForCurve(List<DtoQualityControlLimit> controlLimitList, DtoAnalyseQualityControlData loopData, String workSheetFolderId,
                                               Map<String, DtoTest> testMap) {
        String qcInfo = loopData.getQcInfo();
        String dstVal = proService.getDecimal(loopData.getMostSignificance(), loopData.getMostDecimal(), loopData.getTestOriginValue(), false);
        String testValueDst = analyseDataService.halfLimit(dstVal, loopData.getExamLimitValue(), "qualityControl");
//        String testValueDst = loopData.getTestValueDst();
        if (!UUIDHelper.GUID_EMPTY.equals(workSheetFolderId)) {
            List<String> valueList = new ArrayList<>();
            valueList.add(loopData.getQcValue());
            int calculateWay = testMap.containsKey(loopData.getTestId()) ? testMap.get(loopData.getTestId()).getCalculateWay() : -1;
            if (EnumLIM.EnumCalculateWay.先计算后修约.getValue().equals(calculateWay)) {
                valueList.add(loopData.getTestOriginValue());
                valueList.add(loopData.getTestOriginValue());
            } else {
                valueList.add(testValueDst);
                valueList.add(testValueDst);
            }
            List<DtoQualityControlLimit> limitList = controlLimitList.stream().filter(p -> p.getQcType().equals(loopData.getQcType())
                    && !EnumBase.EnumJudgingMethod.小于测定下限.getValue().equals(p.getJudgingMethod())).collect(Collectors.toList());
            DtoQualityControlLimit qualityControlLimit = new DtoQualityControlLimit();
            qualityControlLimit.setTestId(loopData.getTestId());
            qualityControlLimit.setJudgingMethod(EnumBase.EnumJudgingMethod.相对偏差.getValue());
            qualityControlLimit.setQcType(new CurveCheck().qcTypeValue());
            qualityControlLimit.setQcGrade(EnumLIM.EnumQCGrade.内部质控.getValue());
            qualityControlLimit.setIsDefault(true);
            limitList.add(qualityControlLimit);
            if (limitList.size() > 0) {
                Map<String, Object> qcMap = new CurveCheck().calculateDeviationValue(limitList, valueList);
                //计算室内范围偏差
                DtoQualityControlLimit limit = (DtoQualityControlLimit) qcMap.get("limit");
                if (StringUtil.isNotNull(limit)) {
                    String deviation = qcMap.get("qcRate").toString();
                    Integer sign = new CurveCheck().getQualityConfig().getMostSignificance();
                    Integer md = new CurveCheck().getQualityConfig().getMostDecimal();
                    String recoverRateStr = "";
                    recoverRateStr = proService.getDecimal(sign, md, deviation);
                    if (StringUtil.isNotEmpty(recoverRateStr)) {
                        recoverRateStr = recoverRateStr.contains("无法计算") ? recoverRateStr : recoverRateStr + "%";
                    }
                    qcInfo = recoverRateStr;
                }
            }
        }
        return StringUtil.isNotNull(qcInfo) ? qcInfo : "";
    }

    /**
     * 更新曲线校核样/校正系数检验样质控评价信息
     *
     * @param controlLimitList 质控限值配置列表
     * @param loopData         分析质控信息对象
     * @param evaluate         质控评价信息对象
     */
    private void updateEvaluateForCurve(List<DtoQualityControlLimit> controlLimitList, DtoAnalyseQualityControlData loopData, DtoQualityControlEvaluate evaluate,
                                        String workSheetFolderId, Map<String, String> anaId2QcInfoMap, CalculationService calculationService, Map<String, DtoTest> testMap) {
        String qcInfo = reCalculateQcInfoForCurve(controlLimitList, loopData, workSheetFolderId, testMap);
        if (!qcInfo.equals(loopData.getQcInfo())) {
            anaId2QcInfoMap.put(loopData.getAnalyseId(), qcInfo);
            loopData.setQcInfo(qcInfo);
        }
        evaluate.setCheckItemValue(StringUtil.isNotEmpty(loopData.getQcInfo()) ? loopData.getQcInfo() : "/");
        DtoQualityControlLimit limit = getControlLimit(controlLimitList, loopData.getQcValue(), calculationService);
        setLimitInfoForEvaluate(evaluate, limit, loopData.getQcInfo().replace("%", ""), new CurveCheck());
    }

    /**
     * BUG2024121001834 【重要】【2024-12-13】【陈军】【安徽】【质控限值管理】针对标样质控配置调整
     * 检查项范围可设置[a]<=3*[c]，代表标样的标准值小于3倍检出限
     * a代表标样的“标准值”，b代表“实测值”，c代表“检出限”
     *
     * @return 是否符合检查项范围
     */
    private boolean checkRangeConfigForStandard(DtoQualityControlLimit controlLimit, DtoAnalyseQualityControlData loopData, Optional<DtoTest> testOptional) {
        boolean result = true;
        if (controlLimit.getIsCheckItem() != null && controlLimit.getIsCheckItem() == 1 && StringUtil.isNotEmpty(controlLimit.getRangeConfig())
                && PATTERN.matcher(controlLimit.getRangeConfig()).find()) {
            String checkItemOther = getCheckItemOther(controlLimit);
            String samVal = querySamValue(controlLimit, loopData, testOptional, checkItemOther);
            try {
                Map<String, Object> map = new HashMap<>();
                map.put("a", new BigDecimal(loopData.getQcValue().split("±")[0]));
                map.put("b", new BigDecimal(samVal));
                map.put("c", new BigDecimal(loopData.getExamLimitValue()));
                map.put("x", new BigDecimal(samVal));
                String rangeCorrect = DivationUtils.UpdateOperators(controlLimit.getRangeConfig());
                CalculateService calculationService = SpringContextAware.getBean(CalculateService.class);
                Object calResult = calculationService.calculationExpression(rangeCorrect, map);
                if (calResult instanceof Boolean) {
                    result = (Boolean) calResult;
                } else if (calResult instanceof String) {
                    String flagResult = ((String) calResult);
                    if (flagResult.equals("false")) {
                        result = false;
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return result;
    }

    /**
     * 获取实测值
     *
     * @param controlLimit   质控限值配置
     * @param loopData       分析质控信息对象
     * @param testOptional   测试项目
     * @param checkItemOther 参数
     * @return 实测值
     */
    private String querySamValue(DtoQualityControlLimit controlLimit, DtoAnalyseQualityControlData loopData, Optional<DtoTest> testOptional, String checkItemOther) {
        String samVal = halfLimit(loopData.getTestValueDst(), loopData.getExamLimitValue(), "sample");
        if (testOptional.isPresent()) {
            if (EnumBase.EnumReviseType.先比较再修约.getValue().equals(testOptional.get().getReviseType())) {
                samVal = halfLimit(loopData.getTestOriginValue(), loopData.getExamLimitValue(), "sample");
                samVal = proService.getDecimal(loopData.getMostSignificance(), loopData.getMostDecimal(), samVal);
            }
        }
        samVal = StringUtil.isNotEmpty(checkItemOther) ? loopData.getParamMap().getOrDefault(checkItemOther, "/") : samVal;
        return samVal;
    }

    /**
     * 更新标样质控评价信息
     *
     * @param loopData 分析质控信息对象
     * @param evaluate 质控评价信息对象
     */
    private void updateEvaluateForStandard(DtoQualityControlLimit controlLimit, DtoAnalyseQualityControlData loopData,
                                           DtoQualityControlEvaluate evaluate, Optional<DtoTest> testOptional) {
        String checkItemOther = getCheckItemOther(controlLimit);
        String samVal = querySamValue(controlLimit, loopData, testOptional, checkItemOther);
        if (EnumBase.EnumJudgingMethod.范围判定.getValue().equals(evaluate.getJudgingMethod())) {
            if (!EnumPRO.EnumQualityControlEvaluateType.人工评价.getValue().equals(evaluate.getJudgeType())) {
                if (StringUtil.isNotEmpty(loopData.getQcValue())) {
                    if (EnumBase.EnumUncertainType.区间.getValue().equals(loopData.getUncertainType())) {
                        evaluate.setAllowLimit(loopData.getQcValue() + "(" + loopData.getRangeLow() + "~" + loopData.getRangeHigh() + ")");
                    } else {
                        evaluate.setAllowLimit(loopData.getQcValue());
                    }
                } else {
                    evaluate.setAllowLimit("/");
                }
            }
            evaluate.setCheckItem(StringUtil.isNotEmpty(checkItemOther) ? checkItemOther : EnumLIM.EnumCheckItemType.出证结果.toString());
            String bcValue = samVal;
            Boolean isPass = null;
            //计算是否在范围内
            if (EnumBase.EnumUncertainType.区间.getValue().equals(loopData.getUncertainType())) {
                //evaluate.getAllowLimit() 格式 a(b~c)
                //需要根据检查项值是否在范围区间内（[标准值-下限]~[标准值+上限]）判定是否合格
                //存在手填输入评价标准的情况，上下限需重新解析
                String bzValue = evaluate.getAllowLimit().split("\\(")[0];
                String rangLow = evaluate.getAllowLimit().substring(evaluate.getAllowLimit().indexOf("(") + 1, evaluate.getAllowLimit().indexOf("~"));
                String rangeHigh = evaluate.getAllowLimit().substring(evaluate.getAllowLimit().indexOf("~") + 1, evaluate.getAllowLimit().indexOf(")"));
                if (MathUtil.isNumeral(bcValue) && MathUtil.isNumeral(bzValue) && MathUtil.isNumeral(rangLow) && MathUtil.isNumeral(rangeHigh)) {
                    isPass = new BigDecimal(bcValue).compareTo(new BigDecimal(bzValue).subtract(new BigDecimal(rangLow))) >= 0
                            && new BigDecimal(bcValue).compareTo(new BigDecimal(bzValue).add(new BigDecimal(rangeHigh))) <= 0;
                }
            } else {
                String bzValue = evaluate.getAllowLimit().split("±")[0];
                if (MathUtil.isNumeral(bcValue) && MathUtil.isNumeral(bzValue)) {
                    BigDecimal wcV = new BigDecimal(bcValue).subtract(new BigDecimal(bzValue)).abs();
                    if (evaluate.getAllowLimit().contains("±") && evaluate.getAllowLimit().split("±").length > 1) {
                        BigDecimal fwValue;
                        //如果±后面的是%，进行处理
                        if (evaluate.getAllowLimit().split("±")[1].contains("%") || EnumBase.EnumUncertainType.百分比.getValue().equals(loopData.getUncertainType())) {
                            String percent = evaluate.getAllowLimit().split("±")[1].replace("%", "");
                            float f = Float.parseFloat(percent) / 100;
                            BigDecimal decimal = new BigDecimal(Float.toString(f));
                            BigDecimal bzValueDec = new BigDecimal(evaluate.getAllowLimit().split("±")[0]);
                            fwValue = bzValueDec.multiply(decimal);
                        } else {
                            fwValue = new BigDecimal(evaluate.getAllowLimit().split("±")[1]);
                        }
                        isPass = wcV.compareTo(fwValue) <= 0;
                    }
                }
            }
            if ((StringUtil.isNotNull(controlLimit) && EnumLIM.EnumCheckItemType.出证结果.getValue().equals(controlLimit.getCheckItem())) || StringUtil.isNull(controlLimit)) {
                samVal = loopData.getTestValue();
            }
            evaluate.setCheckItemValue(StringUtil.isNotEmpty(samVal) ? samVal : "/");
            evaluate.setIsPass(isPass);
        } else {
            String bzValue = loopData.getQcValue().split("±")[0];
            List<String> valueList = new ArrayList<>();
            valueList.add(bzValue);
            valueList.add(samVal);
            valueList.add(samVal);
            Map<String, Object> qcMap;
            if (EnumPRO.EnumQualityControlEvaluateType.人工评价.getValue().equals(evaluate.getJudgeType())) {
                DtoQualityControlLimit tempLimit = new DtoQualityControlLimit();
                BeanUtils.copyProperties(controlLimit, tempLimit, "id");
                tempLimit.setAllowLimit(evaluate.getAllowLimit());
                tempLimit.setAllowLimitData(evaluate.getAllowLimit());
                qcMap = new QualityStandard().calculateDeviationValue(Collections.singletonList(tempLimit), valueList);
            } else {
                controlLimit.setAllowLimitData(controlLimit.getAllowLimit());
                qcMap = new QualityStandard().calculateDeviationValue(Collections.singletonList(controlLimit), valueList);
            }
            String rateStr = qcMap.getOrDefault("qcRate", "").toString();
            if (MathUtil.isNumeral(rateStr)) {
                rateStr = proService.getDecimal(new QualityStandard().getQualityConfig().getMostSignificance(),
                        new QualityStandard().getQualityConfig().getMostDecimal(), rateStr);
            }
            String qcInfo = rateStr.contains("无法计算") || StringUtil.isEmpty(rateStr) ? rateStr : rateStr + "%";

            loopData.setQcInfo(qcInfo);
            evaluate.setCheckItemValue(StringUtil.isNotEmpty(loopData.getQcInfo()) ? loopData.getQcInfo() : "/");
            setLimitInfoForEvaluate(evaluate, controlLimit, loopData.getQcInfo().replace("%", ""), new QualityStandard());
            evaluate.setCheckItem(StringUtil.isNotEmpty(checkItemOther) ? checkItemOther : EnumLIM.EnumCheckItemType.出证结果.toString());
            if ("/".equals(evaluate.getCheckItemValue())) {
                evaluate.setIsPass(null);
            }
        }
    }

    /**
     * 更新串联样质控评价信息
     *
     * @param controlLimitList              质控限值配置列表
     * @param loopData                      分析质控信息对象
     * @param evaluate                      质控评价信息对象
     * @param analyseQualityControlDataList 分析数据及质控数据对象列表
     * @param workSheetFolderId             检测单id
     * @param anaId2QcInfoMap               分析数据id和qcInfo的映射关系
     * @param calculationService            计算服务对象
     */
    private void updateEvaluateForSampleSeries(List<DtoQualityControlLimit> controlLimitList, DtoAnalyseQualityControlData loopData, DtoQualityControlEvaluate evaluate,
                                               List<DtoAnalyseQualityControlData> analyseQualityControlDataList, String workSheetFolderId, Map<String, String> anaId2QcInfoMap,
                                               CalculationService calculationService) {
        String qcInfo = reCalculateQcInfoForSeries(controlLimitList, loopData, analyseQualityControlDataList, workSheetFolderId);
        if (!qcInfo.equals(loopData.getQcInfo())) {
            anaId2QcInfoMap.put(loopData.getAnalyseId(), qcInfo);
            loopData.setQcInfo(qcInfo);
        }
        evaluate.setCheckItemValue(StringUtil.isNotEmpty(loopData.getQcInfo()) ? loopData.getQcInfo() : "/");
        //找到原样
        DtoAnalyseQualityControlData yyData = analyseQualityControlDataList.stream().filter(p -> p.getSampleId().equals(loopData.getAssociateSampleId())
                && p.getTestId().equals(loopData.getTestId())).findFirst().orElse(null);
        DtoQualityControlLimit limit = StringUtil.isNotNull(yyData) ? getControlLimit(controlLimitList, yyData.getTestValueDst(), calculationService) : null;
        setLimitInfoForEvaluate(evaluate, limit, loopData.getQcInfo().replace("%", ""), new QualitySampleSeries());
    }

    /**
     * 重新计算串联样偏差
     *
     * @param controlLimitList              质控限值配置列表
     * @param loopData                      分析质控信息对象
     * @param analyseQualityControlDataList 分析数据及质控数据对象列表
     * @param workSheetFolderId             检测单id
     * @return 偏差
     */
    private String reCalculateQcInfoForSeries(List<DtoQualityControlLimit> controlLimitList, DtoAnalyseQualityControlData loopData,
                                              List<DtoAnalyseQualityControlData> analyseQualityControlDataList, String workSheetFolderId) {
        String qcInfo = loopData.getQcInfo();
        if (!UUIDHelper.GUID_EMPTY.equals(workSheetFolderId)) {
            //找到串联样的原样
            DtoAnalyseQualityControlData yyAnaData = analyseQualityControlDataList.stream().filter(p -> p.getSampleId().equals(loopData.getAssociateSampleId())
                    && p.getTestId().equals(loopData.getTestId())).findFirst().orElse(null);
            List<DtoQualityControlLimit> limitList = controlLimitList.stream()
                    .filter(p -> p.getQcType().equals(new QualitySampleSeries().qcTypeValue())).collect(Collectors.toList());
            boolean itemOtherFlag = StringUtil.isNotEmpty(limitList) && EnumLIM.EnumCheckItemType.公式参数.getValue().equals(limitList.get(0).getCheckItem())
                    && StringUtil.isNotEmpty(limitList.get(0).getCheckItemOther());
            if (StringUtil.isNotNull(yyAnaData)) {
                //找到原样对应的所有串联样
                List<String> valueList = new ArrayList<>();
                String tsdYY = halfLimit(yyAnaData.getTestValueDst(), yyAnaData.getExamLimitValue(), "qualityControl");
                if (itemOtherFlag) {
                    tsdYY = yyAnaData.getParamMap().getOrDefault(limitList.get(0).getCheckItemOther(), "");
                }
                valueList.add(tsdYY);
                List<DtoAnalyseQualityControlData> clList = analyseQualityControlDataList.stream().filter(p -> p.getAssociateSampleId().equals(yyAnaData.getSampleId())
                        && new QualitySampleSeries().qcTypeValue().equals(p.getQcType())).collect(Collectors.toList());
                for (DtoAnalyseQualityControlData cl : clList) {
                    String tsd = halfLimit(cl.getTestValueDst(), cl.getExamLimitValue(), "qualityControl");
                    if (itemOtherFlag) {
                        tsd = cl.getParamMap().getOrDefault(limitList.get(0).getCheckItemOther(), "");
                    }
                    //排除当前样品
                    if (!yyAnaData.getSampleId().equals(cl.getSampleId())) {
                        valueList.add(tsd);
                    }
                }
                //判断是否为平行串联
                if (loopData.getQcType().equals(new QualityParallel().qcTypeValue())) {
                    //清除数据
                    valueList.clear();
                    //找到原样对应的平行样
                    List<DtoAnalyseQualityControlData> pxList = analyseQualityControlDataList.stream().filter(p -> p.getAssociateSampleId().equals(yyAnaData.getAssociateSampleId())
                            && new QualityParallel().qcTypeValue().equals(p.getQcType())).collect(Collectors.toList());
                    for (DtoAnalyseQualityControlData px : pxList) {
                        String tsd = halfLimit(px.getTestValueDst(), px.getExamLimitValue(), "qualityControl");
                        if (itemOtherFlag) {
                            tsd = px.getParamMap().getOrDefault(limitList.get(0).getCheckItemOther(), "");
                        }
                        //排除当前样品
                        if (!loopData.getSampleId().equals(px.getSampleId())) {
                            valueList.add(tsd);
                        }
                    }
                    String loopTsd = halfLimit(loopData.getTestValueDst(), loopData.getExamLimitValue(), "qualityControl");
                    if (itemOtherFlag) {
                        loopTsd = loopData.getParamMap().getOrDefault(limitList.get(0).getCheckItemOther(), "");
                    }
                    valueList.add(loopTsd);
                }

                String recoverRateStr = "";

                if (controlLimitList.size() > 0) {
                    Map<String, Object> qcMap = new QualitySampleSeries().calculateDeviationValue(limitList, valueList);
                    String recoverRate = qcMap.get("qcRate").toString();
                    DtoQualityControlLimit limit = (DtoQualityControlLimit) qcMap.get("limit");
                    if (StringUtil.isNotNull(limit)) {
                        Integer sign = new QualitySampleSeries().getQualityConfig().getMostSignificance();
                        Integer md = new QualitySampleSeries().getQualityConfig().getMostDecimal();
                        recoverRateStr = proService.getDecimal(sign, md, recoverRate);
                        if (StringUtil.isNotNull(recoverRateStr)) {
                            recoverRateStr = recoverRateStr.contains("无法计算") ? recoverRateStr : recoverRateStr + "%";
                        }
                    }
                } else {
                    //没有配置串联样质控限值时，穿透率'/'掉
                    recoverRateStr = "/";
                }
                qcInfo = recoverRateStr;
            }
        }
        return StringUtil.isNotNull(qcInfo) ? qcInfo : "";
    }

    /**
     * 更新替代样质控评价信息
     *
     * @param controlLimitList   质控限值配置列表
     * @param loopData           分析质控信息对象
     * @param evaluate           质控评价信息对象
     * @param anaItemName        分析项目名称
     * @param calculationService 计算服务对象
     */
    private void updateEvaluateForReplace(List<DtoQualityControlLimit> controlLimitList, DtoAnalyseQualityControlData loopData,
                                          DtoQualityControlEvaluate evaluate, String anaItemName, List<DtoTest> testList, CalculationService calculationService) {
        DtoTest test = testList.stream().filter(p -> loopData.getTestId().equals(p.getId())).findFirst().orElse(null);
        String parentTestId = StringUtil.isNotNull(test) ? test.getParentId() : UUIDHelper.GUID_EMPTY;
        controlLimitList = controlLimitList.stream()
                .filter(p -> parentTestId.equals(p.getTestId()) && p.getQcType().equals(loopData.getQcType()) && StringUtil.isNotEmpty(p.getSubstituteName())
                        && p.getSubstituteName().equals(anaItemName)).collect(Collectors.toList());
        String qcInfo = StringUtil.isNotEmpty(loopData.getQcInfo()) ? loopData.getQcInfo() : "/";
        evaluate.setCheckItemValue(qcInfo);
        DtoQualityControlLimit limit = getControlLimit(controlLimitList, loopData.getTestValueDst(), calculationService);
        setLimitInfoForEvaluate(evaluate, limit, qcInfo.replace("%", ""), new QualityReplace());
    }

    @Override
    public DtoEvaluateDetail findEvaluateDetail(String evaluateId) {
        DtoQualityControlEvaluate evaluate = repository.findOne(evaluateId);
        if (StringUtil.isNotNull(evaluate)) {
            DtoEvaluateDetail detail = new DtoEvaluateDetail();
            //基础信息
            List<DtoEvaluateRow> basicInfo = new ArrayList<>();
            for (String label : basicInfoLabelList) {
                DtoEvaluateRow row = new DtoEvaluateRow(label, Collections.singletonList(""));
                basicInfo.add(row);
            }
            detail.setBasicInfo(basicInfo);
            DtoAnalyseData analyseData = analyseDataRepository.findOne(evaluate.getObjectId());
            DtoSample sample;
            if (StringUtil.isNotNull(analyseData) && !analyseData.getIsDeleted()) {
                if (StringUtil.isNotEmpty(analyseData.getAnalystName())) {
                    fillRowLabelValue(basicInfo, "分析人", Collections.singletonList(analyseData.getAnalystName()));
                    String analyzeTime = DateUtil.dateToString(analyseData.getAnalyzeTime(), DateUtil.YEAR);
                    if (analyzeTime.contains("1753")) {
                        analyzeTime = "";
                    }
                    fillRowLabelValue(basicInfo, "分析日期", Collections.singletonList(analyzeTime));
                }
                sample = sampleRepository.findOne(analyseData.getSampleId());
                if (StringUtil.isNotNull(sample) && !sample.getIsDeleted()) {
                    fillRowLabelValue(basicInfo, "状态", Collections.singletonList(sample.getStatus()));
                    DtoSampleType sampleType = sampleTypeRepository.findOne(sample.getSampleTypeId());
                    if (StringUtil.isNotNull(sampleType)) {
                        fillRowLabelValue(basicInfo, "检测类型", Collections.singletonList(sampleType.getTypeName()));
                    }
                }
                //获取扩展信息
                detail.setExpendInfo(getExpendInfo(evaluate, analyseData, sample));
                return detail;
            }
        }
        return null;
    }

    /**
     * 查询质控样品评价明细
     *
     * @param detail 质控样评价明细查询条件
     * @return 样品评价明细列表
     */
    @Override
    public List<DtoQcSampleEvaluateDetail> findEvaluateDetailByPage(PageBean<DtoQcSampleEvaluateDetail> pb, DtoQcSampleEvaluateDetail detail) {
        List<DtoQcSampleEvaluateDetail> evaluateDetailList = new ArrayList<>();
        List<DtoSample> qcSampleList = new ArrayList<>();
        //项目id传参存在，则获取项目下所有质控样
        if (StringUtil.isNotEmpty(detail.getProjectId()) && !UUIDHelper.GUID_EMPTY.equals(detail.getProjectId())) {
            qcSampleList = getQcSampleForProject(detail.getProjectId());
        } else if (StringUtil.isNotEmpty(detail.getReportId()) && !UUIDHelper.GUID_EMPTY.equals(detail.getReportId())) {
            //报告id传参存在，则获取报告下所有质控样
            qcSampleList = getQcSampleForReport(Collections.singletonList(detail.getReportId()));
        } else if (StringUtil.isNotEmpty(detail.getReportIdList())) {
            //报告id列表传参存在，则获取报告下所有质控样
            qcSampleList = getQcSampleForReport(detail.getReportIdList());
        }
        if (StringUtil.isNotEmpty(qcSampleList)) {
            List<String> testIds = qcSampleList.stream().map(DtoSample::getTestIds).filter(StringUtil::isNotNull).findFirst().orElse(null);
            Map<String, DtoSample> qcSampleMap = qcSampleList.stream().collect(Collectors.toMap(DtoSample::getId, dto -> dto));
            List<String> qcSampleFolderIdList = qcSampleList.stream().map(DtoSample::getSampleFolderId).distinct().collect(Collectors.toList());
            Map<String, DtoSampleFolder> folderMap = sampleFolderRepository.findAll(qcSampleFolderIdList).stream().collect(Collectors.toMap(DtoSampleFolder::getId, dto -> dto));
            List<String> qcSampleIdList = qcSampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
            List<DtoAnalyseData> qcAnalyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(qcSampleIdList);
            if (StringUtil.isNotNull(testIds) && testIds.size() > 0) {
                //只获取当前报告下样品所含测试项目的质控
                qcAnalyseDataList = qcAnalyseDataList.stream().filter(p -> testIds.contains(p.getTestId())).collect(Collectors.toList());
            }
            if (StringUtil.isNotEmpty(qcAnalyseDataList)) {
                List<String> qcAnalyseDataIdList = qcAnalyseDataList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
                List<DtoQualityControlEvaluate> qcEvaluateList = qualityControlEvaluateRepository.findByObjectIdIn(qcAnalyseDataIdList);
//                Map<String, DtoQualityControlEvaluate> qcEvaluateMap = qcEvaluateList.stream().collect(Collectors.toMap(DtoQualityControlEvaluate::getObjectId, dto -> dto));
                //遍历质控样的分析数据，样品数据，评价数据，组装一个质控明细对象列表
                evaluateDetailList = assembleQcEvaluateDetail(qcAnalyseDataList, qcSampleMap, qcEvaluateList, folderMap,
                        detail.getProjectId(), detail.getReportId());
                //按照查询条件过滤质控明细
                evaluateDetailList = filterEvaluateDetail(evaluateDetailList, detail);
                //按照质控等级,质控类型，分析项目, 样品编号排序
                List<DtoCode> codeList = codeService.findCodes("LIM_QCConfigSort");
                evaluateDetailList.sort(Comparator.comparing(DtoQcSampleEvaluateDetail::getQcGrade)
                        .thenComparing(p -> getQcSortNum(p.getQcType(), p.getQcGrade(), codeList), Comparator.reverseOrder())
                        .thenComparing(DtoQcSampleEvaluateDetail::getAnalyzeItemName)
                        .thenComparing(DtoQcSampleEvaluateDetail::getSampleCode));
            }
        }
        pb.setRowsCount(evaluateDetailList.size());
        //进行分页处理
        int rowsPerPage = pb.getRowsPerPage();
        int pageNum = pb.getPageNo();
        evaluateDetailList = evaluateDetailList.stream().skip((long) (pageNum - 1) * rowsPerPage).limit(rowsPerPage).collect(Collectors.toList());
        pb.setData(evaluateDetailList);
        return evaluateDetailList;
    }

    /**
     * 获取质控样品
     *
     * @param sampleList 样品列表
     */
    public List<DtoSample> getQcSample(List<DtoSample> sampleList) {
        List<DtoSample> resList = new ArrayList<>();
        List<String> sampleIdList = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<DtoSample> qcSampleList = StringUtil.isNotEmpty(sampleIdList) ? sampleRepository.findByAssociateSampleIdIn(sampleIdList) : new ArrayList<>();
        if (StringUtil.isNotEmpty(qcSampleList)) {
            resList.addAll(qcSampleList);
            resList.addAll(getQcSample(qcSampleList));
        }
        return resList;
    }

    /**
     * 初始化质控评价记录
     *
     * @param analyseDataId           分析数据id
     * @param qcType                  质控类型
     * @param qualityControlLimitList 质控限值配置列表
     * @param qcId                    质控信息id
     */
    @Override
    public DtoQualityControlEvaluate initQualityControlEvaluate(String analyseDataId, String testId, Integer qcType, Integer qcGrade, List<DtoQualityControlLimit> qualityControlLimitList,
                                                                String qcValue, String qcId) {
        return initEvaluate(analyseDataId, testId, qcType, qcGrade, qualityControlLimitList, qcValue, qcId, 1, null, null, null);
    }

    @Override
    public DtoQualityControlEvaluate initQualityControlEvaluate(Integer isVaccinate, String analyseDataId, String testId, Integer qcType, Integer qcGrade, List<DtoQualityControlLimit> qualityControlLimitList, String qcValue, String qcId) {
        return initEvaluate(analyseDataId, testId, qcType, qcGrade, qualityControlLimitList, qcValue, qcId, isVaccinate, null, null, null);
    }

    private DtoQualityControlEvaluate initEvaluate(String analyseDataId, String testId, Integer qcType, Integer qcGrade, List<DtoQualityControlLimit> qualityControlLimitList,
                                                   String qcValue, String qcId, Integer isVaccinate, Integer uncertainType, String rangeLow, String rangeHigh) {
        DtoQualityControlEvaluate evaluate = new DtoQualityControlEvaluate();
        evaluate.setObjectId(analyseDataId);
        evaluate.setQcId(qcId);
        evaluate.setCheckItem("/");
        evaluate.setAllowLimit("/");
        evaluate.setCheckItemValue("/");
        evaluate.setJudgingMethod(-1);
        List<DtoQualityControlLimit> limitListForAnaData = qualityControlLimitList.stream().filter(p -> {
            if (new QualityDilutionWater().qcTypeValue().equals(qcType)) {
                //稀释水还要根据是否接种过滤
                return p.getTestId().equals(testId)
                        && p.getQcType().equals(qcType) && p.getQcGrade().equals(qcGrade) && p.getIsVaccinate().equals(isVaccinate);
            } else {
                return p.getTestId().equals(testId)
                        && p.getQcType().equals(qcType) && p.getQcGrade().equals(qcGrade);
            }
        }).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(limitListForAnaData)) {
            DtoQualityControlLimit limit = limitListForAnaData.get(0);
            evaluate.setCheckItem(EnumLIM.EnumCheckItemType.getName(limit.getCheckItem()));
            evaluate.setJudgingMethod(limit.getJudgingMethod());
        }
        if (new QualityStandard().qcTypeValue().equals(qcType)) {
            //针对质控样，检查项默认“出证结果”，评判方式默认“限值判定”
            evaluate.setCheckItem(EnumLIM.EnumCheckItemType.出证结果.toString());
            evaluate.setJudgingMethod(EnumBase.EnumJudgingMethod.范围判定.getValue());
            //标样允许限值获取标样中的“标准值”
            evaluate.setAllowLimit(qcValue);
        }
        if (new CurveCheck().qcTypeValue().equals(qcType)) {
            evaluate.setCheckItem(EnumLIM.EnumCheckItemType.出证结果.toString());
        }
        if (new QualityStandard().qcTypeValue().equals(qcType)) {
            evaluate.setUncertainType(uncertainType != null ? uncertainType : EnumBase.EnumUncertainType.浓度.getValue());
            if (StringUtil.isNotEmpty(rangeLow) && StringUtil.isNotEmpty(rangeHigh)) {
                evaluate.setAllowLimit(qcValue + "(" + rangeLow + "~" + rangeHigh + ")");
            }
        }
        return evaluate;
    }

    @Override
    public DtoQualityControlEvaluate initQualityControlEvaluate(String analyseDataId, String testId, Integer qcType, Integer qcGrade, List<DtoQualityControlLimit> qualityControlLimitList, String qcValue, String qcId, Integer uncertainType) {
        return initEvaluate(analyseDataId, testId, qcType, qcGrade, qualityControlLimitList, qcValue, qcId, null, uncertainType, null, null);
    }

    @Override
    public DtoQualityControlEvaluate initQualityControlEvaluate(String analyseDataId, String testId, Integer qcType, Integer qcGrade, List<DtoQualityControlLimit> qualityControlLimitList, String qcValue, String qcId, Integer uncertainType, Integer isVaccinate) {
        return initEvaluate(analyseDataId, testId, qcType, qcGrade, qualityControlLimitList, qcValue, qcId, isVaccinate, uncertainType, null, null);
    }

    @Override
    public DtoQualityControlEvaluate initQualityControlEvaluate(String analyseDataId, String testId, Integer qcType, Integer qcGrade, List<DtoQualityControlLimit> qualityControlLimitList, String qcValue, String qcId, Integer uncertainType, String rangeLow, String rangeHigh) {
        return initEvaluate(analyseDataId, testId, qcType, qcGrade, qualityControlLimitList, qcValue, qcId, null, uncertainType, rangeLow, rangeHigh);

    }

    @Override
    public DtoQualityControlEvaluate initQualityControlEvaluate(String analyseDataId, String testId, Integer qcType, Integer qcGrade, List<DtoQualityControlLimit> qualityControlLimitList, String qcValue, String qcId, Integer uncertainType, String rangeLow, String rangeHigh, Integer isVaccinate) {
        return initEvaluate(analyseDataId, testId, qcType, qcGrade, qualityControlLimitList, qcValue, qcId, isVaccinate, uncertainType, rangeLow, rangeHigh);
    }

    /**
     * 初始化质控评价记录（暂时用于曲线校核样）
     *
     * @param analyseDataId           分析数据id
     * @param qcType                  质控类型
     * @param qualityControlLimitList 质控限值配置列表
     * @param qcId                    质控信息id
     */
    @Override
    public List<DtoQualityControlEvaluate> initQualityControlEvaluateList(String analyseDataId, String testId, Integer qcType, Integer qcGrade, List<DtoQualityControlLimit> qualityControlLimitList,
                                                                          String qcValue, String qcId) {
        List<DtoQualityControlEvaluate> evaluateList = new ArrayList<>();
        List<DtoQualityControlLimit> limitListForAnaData = qualityControlLimitList.stream().filter(p -> p.getTestId().equals(testId)
                && p.getQcType().equals(qcType) && p.getQcGrade().equals(qcGrade)).collect(Collectors.toList());
        //初始化出证结果评价信息
        List<DtoQualityControlLimit> testValLimitList = limitListForAnaData.stream().filter(p -> StringUtil.isNull(p.getCheckItem())
                || EnumLIM.EnumCheckItemType.出证结果.getValue().equals(p.getCheckItem()) || p.getCheckItem().equals(-1)).collect(Collectors.toList());
        evaluateList.add(initQualityControlEvaluate(analyseDataId, testId, qcType, qcGrade, testValLimitList, qcValue, qcId));
        //初始化公式参数评价信息
        List<DtoQualityControlLimit> otherItemLimitList = limitListForAnaData.stream().filter(p -> EnumLIM.EnumCheckItemType.公式参数.getValue().equals(p.getCheckItem())).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(otherItemLimitList)) {
            //按公式参数名称分组，一个公式参数初始化一条评价信息
            Map<String, List<DtoQualityControlLimit>> otherItemLimitMap = otherItemLimitList.stream().collect(Collectors.groupingBy(DtoQualityControlLimit::getCheckItemOther));
            for (Map.Entry<String, List<DtoQualityControlLimit>> entry : otherItemLimitMap.entrySet()) {
                DtoQualityControlLimit loopLimit = entry.getValue().get(0);
                evaluateList.add(initEvaluate(analyseDataId, qcId, loopLimit.getCheckItemOther(), "/", "/", loopLimit.getJudgingMethod()));
            }
        } else {
            evaluateList.add(initEvaluate(analyseDataId, qcId, "/", "/", "/", -1));
        }
        return evaluateList;
    }

    /**
     * 初始化质控评价对象
     *
     * @param objId         分析数据id
     * @param qcId          质控id
     * @param checkItem     检查项
     * @param allowLimit    允许限值
     * @param checkItemVal  检查项
     * @param judgingMethod 判定方式
     */
    private DtoQualityControlEvaluate initEvaluate(String objId, String qcId, String checkItem, String allowLimit, String checkItemVal, int judgingMethod) {
        DtoQualityControlEvaluate evaluate = new DtoQualityControlEvaluate();
        evaluate.setObjectId(objId);
        evaluate.setQcId(qcId);
        evaluate.setCheckItem(checkItem);
        evaluate.setAllowLimit(allowLimit);
        evaluate.setCheckItemValue(checkItemVal);
        evaluate.setJudgingMethod(judgingMethod);
        return evaluate;
    }

    /**
     * 更新质控评价对象的质控限值信息
     *
     * @param evaluate 质控评价对象
     * @param limit    质控限值对象
     * @param qcInfo   质控值
     */
    private void setLimitInfoForEvaluate(DtoQualityControlEvaluate evaluate, DtoQualityControlLimit limit, String qcInfo, QualityControlKind kind) {
        String checkItem = StringUtil.isNotNull(limit) ? EnumLIM.EnumCheckItemType.getName(limit.getCheckItem()) : "/";
        evaluate.setCheckItem(StringUtil.isNotEmpty(checkItem) ? checkItem : "/");
        evaluate.setJudgingMethod(StringUtil.isNotNull(limit) ? limit.getJudgingMethod() : -1);
        if (EnumPRO.EnumQualityControlEvaluateType.人工评价.getValue().equals(evaluate.getJudgeType())) {
            if (StringUtil.isEmpty(evaluate.getAllowLimit())) {
                evaluate.setAllowLimit("/");
            }
            if (PATTERN.matcher(evaluate.getAllowLimit()).find()) {
                DtoQualityControlLimit tempLimit = new DtoQualityControlLimit();
                tempLimit.setAllowLimit(evaluate.getAllowLimit());
                tempLimit.setAllowLimitData(evaluate.getAllowLimit());
                evaluate.setIsPass(kind.deviationQualified(tempLimit, qcInfo));
            } else {
                evaluate.setIsPass(null);
            }
        } else {
            String allowLimit = StringUtil.isNotNull(limit) ? limit.getAllowLimitData() : "/";
            evaluate.setAllowLimit(StringUtil.isNotEmpty(allowLimit) ? allowLimit : "/");
            //配置的允许限值不符合规范时，直接不评价
            evaluate.setIsPass((StringUtil.isNotNull(limit) && PATTERN.matcher(limit.getAllowLimit()).find()) ? kind.deviationQualified(limit, qcInfo) : null);
        }
        //如果评价的值为无法计算时，则评价为不合格
        if (qcInfo.contains("无法计算")) {
            evaluate.setIsPass(false);
        }
    }

    /**
     * 获取匹配的质控限值配置
     *
     * @param controlLimitList   质控限值配置列表
     * @param value              结果值
     * @param calculationService 计算服务对象
     */
    protected DtoQualityControlLimit getControlLimit(List<DtoQualityControlLimit> controlLimitList, String value, CalculationService calculationService) {
        if (MathUtil.isNumeral(value)) {
            for (DtoQualityControlLimit controlLimit : controlLimitList) {
                boolean isReplace = EnumLIM.EnumQCGrade.内部质控.getValue().equals(controlLimit.getQcGrade())
                        && EnumLIM.EnumQCType.替代物.getValue().equals(controlLimit.getQcType());
                String rangeConfig = isReplace ? "" : controlLimit.getRangeConfigData();
                Integer isCheckItem = controlLimit.getIsCheckItem();
                if (StringUtil.isNotNull(isCheckItem) && isCheckItem.equals(1)) {
                    if (StringUtil.isNotNull(rangeConfig)) {
                        if (DivationUtils.calculationResult(rangeConfig, new BigDecimal(value), calculationService)) {
                            return controlLimit;
                        }
                    }
                } else {
                    return controlLimit;
                }
            }
        }
        return null;
    }

    /**
     * 填充行对象的标签值
     *
     * @param rowList        行对象列表
     * @param labelName      标签名称
     * @param labelValueList 标签值列表
     */
    private DtoEvaluateRow fillRowLabelValue(List<DtoEvaluateRow> rowList, String labelName, List<String> labelValueList) {
        DtoEvaluateRow row = rowList.stream().filter(p -> p.getLabelName().equals(labelName)).findFirst().orElse(null);
        if (StringUtil.isNotNull(row)) {
            row.setLabelValueList(labelValueList);
        }
        return row;
    }

    /**
     * 填充行对象的标签名称和标签值
     *
     * @param rowList        行对象列表
     * @param labelName      标签名称
     * @param labelValueList 标签值列表
     * @param newLabelName   新的标签名称
     */
    private void fillRowLabelNameValue(List<DtoEvaluateRow> rowList, String labelName, List<String> labelValueList, String newLabelName) {
        DtoEvaluateRow row = fillRowLabelValue(rowList, labelName, labelValueList);
        if (StringUtil.isNotNull(row)) {
            row.setLabelName(newLabelName);
        }
    }

    /**
     * 组装一个分析数据质控信息对象
     *
     * @param analyseData    分析数据对象
     * @param qualityControl 质控信息对象
     * @param originalRecord 分析数据公示参数对象
     * @return 析数据质控信息对象
     */
    @Override
    public DtoAnalyseQualityControlData initAnalyseQualityControlData(DtoAnalyseData analyseData, DtoQualityControl qualityControl,
                                                                      DtoAnalyseOriginalRecord originalRecord, DtoTest test) {
        Map<String, String> paramMap = new HashMap<>();
        if (StringUtil.isNotNull(originalRecord)) {
            String json = originalRecord.getJson();
            TypeLiteral<List<DtoAnalyseOriginalJson>> typeLiteral = new TypeLiteral<List<DtoAnalyseOriginalJson>>() {
            };
            List<DtoAnalyseOriginalJson> originalJsonList = JsonIterator.deserialize(json, typeLiteral);
            for (DtoAnalyseOriginalJson originalJson : originalJsonList) {
                paramMap.put(originalJson.getAlias(), originalJson.getDefaultValue());
            }
        }
        Integer qcType = qualityControl.getQcType();
        //获取不到质控类型则从根据样品类型获取
        if ((StringUtil.isNull(qcType) || qcType.equals(-1)) && StringUtil.isNotNull(analyseData.getSampleCategory())) {
            EnumPRO.EnumSampleCategory enumSampleCategory = EnumPRO.EnumSampleCategory.getByValue(analyseData.getSampleCategory());
            if (StringUtil.isNotNull(enumSampleCategory)) {
                qcType = enumSampleCategory.getQcType();
            }
        }
        Integer qcGrade = qualityControl.getQcGrade();
        //获取不到质控类型则从根据样品类型获取
        if ((StringUtil.isNull(qcGrade) || qcGrade.equals(-1)) && StringUtil.isNotNull(analyseData.getSampleCategory())) {
            EnumPRO.EnumSampleCategory enumSampleCategory = EnumPRO.EnumSampleCategory.getByValue(analyseData.getSampleCategory());
            if (StringUtil.isNotNull(enumSampleCategory)) {
                qcGrade = enumSampleCategory.getQcGrade();
            }
            if (StringUtil.isNotNull(analyseData.getQcGrade()) && !analyseData.getQcGrade().equals(-1)) {
                qcGrade = analyseData.getQcGrade();
            }
        }
        String assId = qualityControl.getAssociateSampleId();
        if (StringUtil.isEmpty(assId) || UUIDHelper.GUID_EMPTY.equals(assId)) {
            assId = analyseData.getAssociateSampleId();
        }
        List<String> limitInfoList = getExamLimitInfo(test, analyseData);
        String lowerLimit = limitInfoList.get(1);
        DtoAnalyseQualityControlData controlData = new DtoAnalyseQualityControlData(analyseData.getId(), analyseData.getSampleId(),
                assId, analyseData.getTestValue(), analyseData.getTestValueDstr(), analyseData.getTestOrignValue(),
                analyseData.getPxAverageValue(), qcType, qcGrade, qualityControl.getQcValue(), analyseData.getQcInfo(),
                analyseData.getTestId(), analyseData.getExamLimitValue(), lowerLimit, analyseData.getMostSignificance(),
                analyseData.getMostDecimal(), paramMap, analyseData.getSeriesValue(), analyseData.getQcId(), qualityControl.getUncertainType(),
                qualityControl.getRangeLow(), qualityControl.getRangeHigh(), qualityControl.getIsVaccinate());
        controlData.setLowerLimit(lowerLimit);
        return controlData;
    }

    @Override
    public List<DtoQualityControlEvaluate> evaluateAnaData(Map<String, Object> analyseData) {
        List<DtoQualityControlEvaluate> evaluates = new ArrayList<>();
        String testId = (String) analyseData.get("testId");
        DtoTest test = testRepository.findOne(testId);
        Optional<DtoTest> testOptional = Optional.of(test);
        List<String> testIds = Arrays.asList(testId);
        List<DtoQualityControlLimit> qualityControlLimits = StringUtil.isNotEmpty(testIds) ? qualityControlLimitRepository.findByTestIdIn(testIds) : new ArrayList<>();
        Integer qcType = (Integer) analyseData.get("qcType");
        Integer qcGrade = (Integer) analyseData.get("qcGrade");
        Map<String, String> paramMap = new HashMap<>();
        analyseData.forEach((k, v) -> {
            paramMap.put(k, String.valueOf(v));
        });
        DtoAnalyseQualityControlData loopData = new DtoAnalyseQualityControlData((String) analyseData.get("id"), (String) analyseData.get("sampleId"),
                UUIDHelper.GUID_EMPTY, (String) analyseData.get("testValue"), (String) analyseData.get("testValueDstr"),
                (String) analyseData.get("testOrignValue"), (String) analyseData.get("pxAverageValue"), qcType, qcGrade, (String) analyseData.get("qcValue"),
                (String) analyseData.get("qcInfo"), (String) analyseData.get("testId"), (String) analyseData.get("examLimitValue"),
                "", (Integer) analyseData.get("mostSignificance"), (Integer) analyseData.get("mostDecimal"), paramMap,
                (String) analyseData.get("seriesValue"), (String) analyseData.get("qcId"), (Integer) analyseData.get("uncertainType"),
                (String) analyseData.get("rangeLow"), (String) analyseData.get("rangeHigh"));
        qualityControlLimits = qualityControlLimits.stream().filter(c -> c.getQcType().equals(qcType) && c.getQcGrade().equals(qcGrade)).collect(Collectors.toList());
        AtomicInteger sum = new AtomicInteger();
        if (StringUtil.isNotEmpty(qualityControlLimits)) {
            qualityControlLimits.forEach(limit -> {
                if (checkRangeConfigForStandard(limit, loopData, testOptional)) {
                    sum.getAndIncrement();
                    DtoQualityControlEvaluate evaluate = new DtoQualityControlEvaluate();
                    evaluate.setObjectId(loopData.getAnalyseId());
                    evaluate.setQcId((String) analyseData.get("qcId"));
                    evaluate.setCheckItem(EnumLIM.EnumCheckItemType.出证结果.getValue().equals(limit.getCheckItem()) ? EnumLIM.EnumCheckItemType.出证结果.toString() : limit.getCheckItemOther());
                    evaluate.setCheckItemValue("/");
                    evaluate.setJudgingMethod(limit.getJudgingMethod());
                    evaluate.setUncertainType(limit.getUncertainType());
                    if (EnumBase.EnumUncertainType.区间.getValue().equals(loopData.getUncertainType())) {
                        evaluate.setAllowLimit(loopData.getQcValue() + "(" + loopData.getRangeLow() + "~" + loopData.getRangeHigh() + ")");
                    } else {
                        evaluate.setAllowLimit(EnumBase.EnumJudgingMethod.范围判定.getValue().equals(limit.getJudgingMethod()) ? loopData.getQcValue() : limit.getAllowLimitData());
                    }
                    updateEvaluateForStandard(limit, loopData, evaluate, testOptional);
                    evaluates.add(evaluate);
                }
            });
        }
        if (StringUtil.isEmpty(qualityControlLimits) || sum.get() == 0) {
            DtoQualityControlEvaluate evaluate = new DtoQualityControlEvaluate();
            evaluate.setObjectId((String) analyseData.get("id"));
            evaluate.setQcId((String) analyseData.get("qcId"));
            evaluate.setCheckItem(EnumLIM.EnumCheckItemType.出证结果.toString());
            evaluate.setJudgingMethod(EnumBase.EnumJudgingMethod.范围判定.getValue());
            evaluate.setCheckItemValue("/");
            if (EnumBase.EnumUncertainType.区间.getValue().equals(analyseData.get("uncertainType"))) {
                evaluate.setAllowLimit(analyseData.get("qcValue") + "(" + analyseData.get("rangeLow") + "~" + analyseData.get("rangeHigh") + ")");
            } else {
                evaluate.setAllowLimit((String) analyseData.get("qcValue"));
            }
            evaluate.setUncertainType((Integer) analyseData.get("uncertainType"));
            updateEvaluateForStandard(null, loopData, evaluate, testOptional);
            evaluates.add(evaluate);
        }

        return evaluates;
    }

    /**
     * 获取检查项内容
     *
     * @param controlLimitList 数据集合
     * @return 检查项内容
     */
    private static String getCheckItemOther(List<DtoQualityControlLimit> controlLimitList) {
        String checkItemOther = "";
        if (StringUtil.isNotEmpty(controlLimitList)) {
            DtoQualityControlLimit limit = controlLimitList.get(0);
            if (EnumLIM.EnumCheckItemType.公式参数.getValue().equals(limit.getCheckItem()) && StringUtil.isNotEmpty(limit.getCheckItemOther())) {
                checkItemOther = limit.getCheckItemOther();
            }
        }
        return checkItemOther;
    }

    /**
     * 获取检查项内容
     *
     * @param controlLimit 数据
     * @return 检查项内容
     */
    private static String getCheckItemOther(DtoQualityControlLimit controlLimit) {
        String checkItemOther = "";
        if (StringUtil.isNotNull(controlLimit)) {
            if (EnumLIM.EnumCheckItemType.公式参数.getValue().equals(controlLimit.getCheckItem()) && StringUtil.isNotEmpty(controlLimit.getCheckItemOther())) {
                checkItemOther = controlLimit.getCheckItemOther();
            }
        }
        return checkItemOther;
    }

    /**
     * 获取质控类型排序值 常量 LIM_QCConfigSort
     *
     * @param evaluate 质控评价对象
     * @return 检查项内容
     */
    private int getQcSortNumByEvaluate(DtoQualityControlEvaluate evaluate, List<DtoCode> codeList) {
        return getQcSortNum(evaluate.getQcGrade(), evaluate.getQcType(), codeList);
    }

    /**
     * 获取质控类型排序值 常量 LIM_QCConfigSort
     *
     * @param qcGrade  质控等级
     * @param qcType   质控类型
     * @param codeList 排序常量对象
     * @return 检查项内容
     */
    private int getQcSortNum(Integer qcGrade, Integer qcType, List<DtoCode> codeList) {
        int sortNum = -1;
        try {
            QualityControlKind kind = QualityTaskFactory.getInstance().getQcSample(qcType);
            if (StringUtil.isNotNull(kind)) {
                if (StringUtil.isNotNull(qcType) && StringUtil.isNotNull(qcGrade)) {
                    if (StringUtil.isNotEmpty(codeList)) {
                        DtoCode code = codeList.stream().filter(p -> String.valueOf(qcType).equals(p.getDictValue())
                                && qcGrade.equals(p.getExtendI1())).findFirst().orElse(null);
                        if (StringUtil.isNotNull(code) && StringUtil.isNotNull(code.getSortNum())) {
                            sortNum = code.getSortNum();
                        }
                    }
                }
            }
            return sortNum;
        } catch (BaseException e) {
            return sortNum;
        }
    }

    /**
     * 获取质控评价扩展信息
     *
     * @param evaluate    质控评价对象
     * @param analyseData 分析数据对象
     * @param sample      样品对象
     * @return 质控评价扩展信息
     */
    @Override
    public List<DtoEvaluateRow> getExpendInfo(DtoQualityControlEvaluate evaluate, DtoAnalyseData analyseData, DtoSample sample) {
        List<DtoEvaluateRow> expendInfo = new ArrayList<>();
        DtoTest test = testRepository.findOne(analyseData.getTestId());
        DtoQualityControl qualityControl = qualityControlRepository.findOne(evaluate.getQcId());
        if (StringUtil.isNotNull(qualityControl)) {

            List<DtoAnalyseOriginalRecord> originalRecordList = analyseOriginalRecordRepository.findByAnalyseDataIdIn(Collections.singleton(analyseData.getId()));
            DtoAnalyseOriginalRecord originalRecord = StringUtil.isNotEmpty(originalRecordList) ? originalRecordList.get(0) : null;
            // 生成分析数据及质控信息对象
            DtoAnalyseQualityControlData analyseQualityControlData = initAnalyseQualityControlData(analyseData, qualityControl, originalRecord, test);
            if (new QualityBlank().qcTypeValue().equals(qualityControl.getQcType()) || new QualityReagentBlank().qcTypeValue().equals(qualityControl.getQcType())
                    || new QualityTransportBlank().qcTypeValue().equals(qualityControl.getQcType()) || new QualityInstrumentBlank().qcTypeValue().equals(qualityControl.getQcType())
                    || new QualitySamplingContainerBlank().qcTypeValue().equals(qualityControl.getQcType())) {
                expendInfo = getBlankExpendInfo(evaluate, analyseData, sample, test);   //空白样
            } else if (new QualityParallel().qcTypeValue().equals(qualityControl.getQcType())
                    && EnumLIM.EnumQCGrade.内部质控.getValue().equals(qualityControl.getQcGrade())) {
                expendInfo = getParallelExpendInfo(evaluate, analyseData, sample, test);    //室内平行样
            } else if ((new QualityParallel().qcTypeValue().equals(qualityControl.getQcType())
                    || new QualityCipherParallel().qcTypeValue().equals(qualityControl.getQcType()))
                    && EnumLIM.EnumQCGrade.外部质控.getValue().equals(qualityControl.getQcGrade())) {
                expendInfo = getXCParallelExpendInfo(evaluate, analyseData, sample, test);    //现场平行样 或者 密码平行样
            } else if (new QualityMark().qcTypeValue().equals(qualityControl.getQcType()) || new QualityBlankMark().qcTypeValue().equals(qualityControl.getQcType())) {
                expendInfo = getJbExpendInfo(evaluate, analyseData, sample, test, qualityControl);  //加标样
            } else if (new QualityStandard().qcTypeValue().equals(qualityControl.getQcType())) {
                expendInfo = getStandardExpendInfo(evaluate, analyseData, sample, test, qualityControl);  //质控样
            } else if (new CurveCheck().qcTypeValue().equals(qualityControl.getQcType())) {
                expendInfo = getCurveExpendInfo(evaluate, analyseData, sample, test, qualityControl, analyseQualityControlData);  //曲线校核样
            } else if (new QualityReplace().qcTypeValue().equals(qualityControl.getQcType())) {
                expendInfo = getReplaceExpendInfo(evaluate, analyseData, sample, test, qualityControl);  //替代样
            } else if (new QualityNegativeControl().qcTypeValue().equals(qualityControl.getQcType())
                    || new QualityPositiveControl().qcTypeValue().equals(qualityControl.getQcType())) {
                expendInfo = getNegPosExpendInfo(evaluate, analyseData, sample);
            } else if (new QualityCorrectionFactor().qcTypeValue().equals(qualityControl.getQcType())) {
                expendInfo = getJzExpendInfo(evaluate, analyseData, qualityControl, sample);
            }
        }
        if (EnumPRO.EnumSampleCategory.串联样.getValue().equals(sample.getSampleCategory())
                || evaluate.getQcId().equals(UUIDHelper.GUID_EMPTY)) {
            expendInfo = getClExpendInfo(evaluate, analyseData, sample);  //串联样
        }
        return expendInfo;
    }

    @Override
    @Transactional
    public void handleEvaluate(DtoQualityControlEvaluate qualityControlEvaluate) {
        DtoQualityControlEvaluate entity = repository.findOne(qualityControlEvaluate.getId());
        String oldLimit = entity.getAllowLimit();
        if (qualityControlEvaluate.getAllowLimit() != null && !qualityControlEvaluate.getAllowLimit().equals(oldLimit)) {
            if (qualityControlEvaluate.getJudgingMethod() == -1) {
                throw new BaseException("请先配置质控限值或确认检测项范围正确！");
            } else {
                List<Integer> excludeTypes = Stream.of(EnumBase.EnumJudgingMethod.小于测定下限.getValue(),
                        EnumBase.EnumJudgingMethod.小于检出限.getValue(), EnumBase.EnumJudgingMethod.范围判定.getValue(),
                        EnumBase.EnumJudgingMethod.标曲A0波动范围.getValue()).collect(Collectors.toList());
                if (!excludeTypes.contains(qualityControlEvaluate.getJudgingMethod()) && !qcConfigService.validateFormat(qualityControlEvaluate.getAllowLimit())) {
                    throw new BaseException("允许限值格式有误，请按照规定的格式进行配置！");
                }
            }
            entity.setJudgeType(qualityControlEvaluate.getJudgeType());
            entity.setAllowLimit(qualityControlEvaluate.getAllowLimit());
            repository.save(entity);
            this.updateEvaluate(qualityControlEvaluate.getWorkSheetFolderId());
        } else {
            //只修改说明不需要评价
            entity.setRemark(qualityControlEvaluate.getRemark());
            entity.setJudgeType(qualityControlEvaluate.getJudgeType());
            repository.save(entity);
        }
    }

    @Override
    @Transactional
    public void systemEvaluate(String worksheetFolderId) {
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findByWorkSheetFolderIdAndIsDeletedFalse(worksheetFolderId);
        if (StringUtil.isNotEmpty(analyseDataList)) {
            List<String> analyseIdList = analyseDataList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
            List<DtoQualityControlEvaluate> oldEvaluateList = repository.findByObjectIdIn(analyseIdList);
            oldEvaluateList.forEach(v -> {
                v.setJudgeType(EnumPRO.EnumQualityControlEvaluateType.系统验证.getValue());
                v.setRemark("");
            });
            repository.save(oldEvaluateList);
            this.updateEvaluate(worksheetFolderId);
        }
    }

    /**
     * 获取通用评价扩展信息
     *
     * @param expendInfo  扩展信息对象
     * @param evaluate    质控评价对象
     * @param analyseData 分析数据对象
     * @param sample      样品对象
     */
    private void setCommonExpendInfo(List<DtoEvaluateRow> expendInfo, DtoQualityControlEvaluate evaluate, DtoAnalyseData analyseData, DtoSample sample) {
        if (StringUtil.isNotNull(analyseData)) {
            fillRowLabelValue(expendInfo, "分析项目", Collections.singletonList(analyseData.getRedAnalyzeItemName()));
        }
        if (StringUtil.isNotNull(sample)) {
            fillRowLabelValue(expendInfo, "样品编号", Collections.singletonList(sample.getCode()));
            if (new QualityReplace().qcTypeValue().equals(analyseData.getQcType()) && StringUtils.isNotNullAndEmpty(sample.getAssociateSampleId())) {
                DtoSample yySample = sampleRepository.findOne(sample.getAssociateSampleId());
                if (StringUtil.isNotNull(yySample)) {
                    DtoAnalyseData yyAnaData = analyseDataRepository.findBySampleIdAndIsDeletedFalse(yySample.getId()).stream().filter(a -> a.getTestId().equals(analyseData.getTestId())).findFirst().orElse(null);
                    if (StringUtil.isNotNull(yyAnaData)) {
                        fillRowLabelValue(expendInfo, "样品编号", Collections.singletonList(sample.getCode() + "(" + yyAnaData.getGatherCode() + ")"));
                    }
                }
            }
        }
        fillRowLabelValue(expendInfo, "检查项", Collections.singletonList(StringUtil.isNotEmpty(evaluate.getCheckItem()) ? evaluate.getCheckItem() : ""));
        String judgingMethodName = StringUtil.isNotNull(evaluate.getJudgingMethod()) ? EnumBase.EnumJudgingMethod.getName(evaluate.getJudgingMethod()) : "";
        fillRowLabelValue(expendInfo, "评判方式", Collections.singletonList(judgingMethodName));
        fillRowLabelValue(expendInfo, "允许值", Collections.singletonList(StringUtil.isNotEmpty(evaluate.getAllowLimit()) ? evaluate.getAllowLimit() : ""));
        fillRowLabelValue(expendInfo, "允许限值", Collections.singletonList(StringUtil.isNotEmpty(evaluate.getAllowLimit()) ? evaluate.getAllowLimit() : ""));
        fillRowLabelValue(expendInfo, "允许偏差", Collections.singletonList(StringUtil.isNotEmpty(evaluate.getAllowLimit()) ? evaluate.getAllowLimit() : ""));
        fillRowLabelValue(expendInfo, "是否合格", Collections.singletonList(StringUtil.isNotNull(evaluate.getIsPass()) ? (evaluate.getIsPass() ? "是" : "否") : ""));
    }

    /**
     * 初始化质控评价扩展信息
     *
     * @param extendLabelList 扩展信息标签名称列表
     * @return 质控评价扩展信息
     */
    private List<DtoEvaluateRow> initExpendInfo(List<String> extendLabelList, DtoQualityControlEvaluate evaluate, DtoAnalyseData analyseData, DtoSample sample) {
        List<DtoEvaluateRow> expendInfo = new ArrayList<>();
        for (String label : extendLabelList) {
            expendInfo.add(new DtoEvaluateRow(label, Collections.singletonList("")));
        }
        setCommonExpendInfo(expendInfo, evaluate, analyseData, sample);
        return expendInfo;
    }

    /**
     * 获取空白样质控评价扩展信息
     *
     * @param evaluate    质控评价对象
     * @param analyseData 分析数据对象
     * @param sample      样品对象
     * @return 质控评价扩展信息
     */
    private List<DtoEvaluateRow> getBlankExpendInfo(DtoQualityControlEvaluate evaluate, DtoAnalyseData analyseData, DtoSample sample, DtoTest test) {
        List<DtoEvaluateRow> expendInfo = initExpendInfo(blankExtendLabelList, evaluate, analyseData, sample);
        String dim = evaluate.getDimensionName();
//        if (StringUtil.isNotEmpty(evaluate.getCheckItem()) && !EnumLIM.EnumCheckItemType.出证结果.toString().equals(evaluate.getCheckItem())) {
//            //检查项为其他时，量纲获取公式参数上的量纲
//            List<DtoAnalyseOriginalRecord> originalRecordList = analyseOriginalRecordRepository.findByAnalyseDataIdIn(Collections.singletonList(analyseData.getId()));
//            if (StringUtil.isNotEmpty(originalRecordList)) {
//                DtoParamsTestFormula paramsTestFormula = paramsTestFormulaRepository.findByObjId(originalRecordList.get(0).getTestFormulaId())
//                        .stream().filter(p -> evaluate.getCheckItem().equals(p.getAlias())).findFirst().orElse(null);
//                dim = StringUtil.isNotNull(paramsTestFormula) ? paramsTestFormula.getDimension() : "";
//            }
//        }
        fillRowLabelNameValue(expendInfo, "测定值", Collections.singletonList(StringUtil.isNotEmpty(evaluate.getCheckItemValue())
                ? evaluate.getCheckItemValue() : ""), "测定值（" + dim + "）");
        fillRowLabelNameValue(expendInfo, "允许值", Collections.singletonList(StringUtil.isNotEmpty(evaluate.getAllowLimit())
                ? evaluate.getAllowLimit() : ""), "允许值（" + dim + "）");
        return expendInfo;
    }

    /**
     * 获取平行样质控评价扩展信息
     *
     * @param evaluate    质控评价对象
     * @param analyseData 分析数据对象
     * @param sample      样品对象
     * @return 质控评价扩展信息
     */
    private List<DtoEvaluateRow> getParallelExpendInfo(DtoQualityControlEvaluate evaluate, DtoAnalyseData analyseData, DtoSample sample, DtoTest test) {
        List<DtoEvaluateRow> expendInfo = initExpendInfo(parallelExtendLabelList, evaluate, analyseData, sample);
        //处理偏差量纲
        String pcLg = (EnumBase.EnumJudgingMethod.相对偏差.getValue().equals(evaluate.getJudgingMethod())
                || EnumBase.EnumJudgingMethod.相对误差.getValue().equals(evaluate.getJudgingMethod())) ? "%" : analyseData.getDimension();
        fillRowLabelNameValue(expendInfo, "偏差", Collections.singletonList(evaluate.getCheckItemValue()), "偏差（" + pcLg + "）");
        String testId = analyseData.getTestId();
        //找到平行样的原样
        List<DtoSample> yySampleList = sampleRepository.findByIds(Collections.singletonList(sample.getAssociateSampleId()));
        if (StringUtil.isNotEmpty(yySampleList)) {
            DtoSample yySample = yySampleList.get(0);
            DtoAnalyseData yyAnaData = analyseDataRepository.findBySampleIdAndTestIdAndIsDeletedFalse(yySample.getId(), testId).stream().findFirst().orElse(null);
            List<String> allTstValList = new ArrayList<>();
            String avgVal = getInPxInfo(analyseData, sample, test, expendInfo, yySample, yyAnaData, allTstValList);
            fillRowLabelNameValue(expendInfo, "测定值", allTstValList, "测定值（" + analyseData.getDimension() + "）");
            fillRowLabelNameValue(expendInfo, "均值", Collections.singletonList(avgVal), "均值（" + analyseData.getDimension() + "）");
        }
        return expendInfo;
    }

    /**
     * 获取室内平行样测定值及均值信息
     *
     * @param analyseData   室内平行样数据
     * @param sample        室内平行样品
     * @param test          测试项目
     * @param expendInfo    扩展信息对象
     * @param yySample      原样品
     * @param yyAnaData     原样分析数据
     * @param allTstValList 平行样测定值列表
     * @return 均值
     */
    @Override
    public String getInPxInfo(DtoAnalyseData analyseData, DtoSample sample, DtoTest test, List<DtoEvaluateRow> expendInfo,
                              DtoSample yySample, DtoAnalyseData yyAnaData, List<String> allTstValList) {
        String avgVal;
        List<String> limitInfoList = getExamLimitInfo(test, analyseData);
        String examLimitValueLess = limitInfoList.get(2);
        if (StringUtil.isNull(sample)) {
            sample = sampleRepository.findOne(analyseData.getSampleId());
        }
        if (StringUtil.isNull(yySample)) {
            String assId = StringUtil.isNotNull(sample) ? sample.getAssociateSampleId() : "";
            List<DtoSample> yySampleList = sampleRepository.findByIds(Collections.singletonList(assId));
            if (StringUtil.isNotEmpty(yySampleList)) {
                yySample = yySampleList.get(0);
                yyAnaData = analyseDataRepository.findBySampleIdAndTestIdAndIsDeletedFalse(yySample.getId(), analyseData.getTestId()).stream().findFirst().orElse(null);
            }
        }
        if (StringUtil.isNotNull(yySample)) {
            String examLimitValue = analyseData.getExamLimitValue(), testId = analyseData.getTestId();
            BigDecimal aDec = BigDecimal.ZERO;
            if (StringUtil.isNotNull(yyAnaData) && MathUtil.isNumeral(yyAnaData.getTestValueDstr())) {
                aDec = aDec.add(new BigDecimal(yyAnaData.getTestValueDstr()));
            }
            BigDecimal bDec = BigDecimal.ZERO;
            if (StringUtil.isNotNull(analyseData) && MathUtil.isNumeral(analyseData.getTestValueDstr())) {
                bDec = bDec.add(new BigDecimal(analyseData.getTestValueDstr()));
            }
            //判断原样是否为串联样，或者原样是否有串联样
            if (EnumPRO.EnumSampleCategory.串联样.getValue().equals(yySample.getSampleCategory())) {
                //原样为串联样，找到串联样的原样
                DtoSample clYySample = sampleRepository.findByIds(Collections.singletonList(yySample.getAssociateSampleId())).stream().findFirst().orElse(null);
                if (StringUtil.isNotNull(clYySample)) {
                    DtoAnalyseData clYyAnaData = analyseDataRepository.findBySampleIdAndTestIdAndIsDeletedFalse(yySample.getId(), testId).stream().findFirst().orElse(null);
                    if (StringUtil.isNotNull(clYyAnaData) && MathUtil.isNumeral(clYyAnaData.getTestValueDstr())) {
                        aDec = aDec.add(new BigDecimal(clYyAnaData.getTestValueDstr()));
                    }
                    //找到原样样品的平行样(只考虑一个平行样)
                    List<DtoSample> yyPxSampleList = findPxByAssSmpId(clYySample.getId());
                    if (StringUtil.isNotEmpty(yyPxSampleList)) {
                        DtoAnalyseData yyPxAnaData = analyseDataRepository.findBySampleIdAndTestIdAndIsDeletedFalse(yySample.getId(), testId).stream().findFirst().orElse(null);
                        if (StringUtil.isNotNull(yyPxAnaData) && MathUtil.isNumeral(yyPxAnaData.getTestValueDstr())) {
                            bDec = bDec.add(new BigDecimal(yyPxAnaData.getTestValueDstr()));
                        }
                    }
                }
                avgVal = setTstAvgForPx(Stream.of(aDec, bDec).collect(Collectors.toList()), examLimitValue, examLimitValueLess,
                        analyseData.getMostSignificance(), analyseData.getMostDecimal(), allTstValList);
            } else {
                //找到原样的所有关联样
                List<DtoSample> yyAssSampleList = sampleRepository.findByAssociateSampleIdIn(Collections.singletonList(yySample.getId()));
                //原样的串联样(只考虑一个串联样的情况)
                DtoSample clSample = yyAssSampleList.stream().filter(p -> EnumPRO.EnumSampleCategory.串联样.getValue().equals(p.getSampleCategory())).findFirst().orElse(null);
                if (StringUtil.isNotNull(clSample)) {
                    DtoAnalyseData clAnaData = analyseDataRepository.findBySampleIdAndTestIdAndIsDeletedFalse(clSample.getId(), testId).stream().findFirst().orElse(null);
                    if (StringUtil.isNotNull(clAnaData) && MathUtil.isNumeral(clAnaData.getTestValueDstr())) {
                        aDec = aDec.add(new BigDecimal(clAnaData.getTestValueDstr()));
                    }
                    //原样有串联样(此时只考虑原样和串联样各添加一个平行样的情况)
                    if (StringUtil.isNotNull(expendInfo)) {
                        String code = StringUtil.isNotNull(sample) ? sample.getCode() : "";
                        fillRowLabelValue(expendInfo, "样品编号", Arrays.asList(yySample.getCode(), code));
                    }
                    //找到串联样的平行样
                    List<DtoSample> clPxSampleList = findPxByAssSmpId(clSample.getId());
                    if (StringUtil.isNotEmpty(clPxSampleList)) {
                        DtoSample clPxSample = clPxSampleList.get(0);
                        DtoAnalyseData clPxAnaData = analyseDataRepository.findBySampleIdAndTestIdAndIsDeletedFalse(clPxSample.getId(), testId).stream().findFirst().orElse(null);
                        if (StringUtil.isNotNull(clPxAnaData) && MathUtil.isNumeral(clPxAnaData.getTestValueDstr())) {
                            bDec = bDec.add(new BigDecimal(clPxAnaData.getTestValueDstr()));
                        }
                    }
                    avgVal = setTstAvgForPx(Stream.of(aDec, bDec).collect(Collectors.toList()), examLimitValue, examLimitValueLess,
                            analyseData.getMostSignificance(), analyseData.getMostDecimal(), allTstValList);
                } else {
                    //原样没有串联样（需要考虑原样有多个平行样的情况）,先找到原样的所有平行样
                    List<DtoSample> pxSampleList = findPxByAssSmpId(yySample.getId());
                    pxSampleList.sort(Comparator.comparing(DtoSample::getCode));
                    List<String> sampleIdList = pxSampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
                    sampleIdList.add(yySample.getId());
                    List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndTestIdAndIsDeletedFalse(sampleIdList, testId);
                    List<String> pxSampleIdListForTest = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
                    List<String> allSampleCodeList = new ArrayList<>();
                    List<BigDecimal> decList = new ArrayList<>();
                    allSampleCodeList.add(yySample.getCode());
                    if (StringUtil.isNotNull(yyAnaData)) {
                        // 根据测试项目配置的修约方式
                        if (EnumBase.EnumReviseType.先比较再修约.getValue().equals(test.getReviseType())) {
                            String compareValue = proService.getExamValue(yyAnaData.getTestOrignValue(), examLimitValue, examLimitValueLess);
                            allTstValList.add(proService.getDecimal(yyAnaData.getMostSignificance(), yyAnaData.getMostDecimal(), compareValue));
                        } else {
                            String yyDst = proService.getDecimal(yyAnaData.getMostSignificance(), yyAnaData.getMostDecimal(), yyAnaData.getTestOrignValue());
                            allTstValList.add(proService.getExamValue(yyDst, examLimitValue, examLimitValueLess));
                        }
                        if (MathUtil.isNumeral(yyAnaData.getTestValueDstr())) {
                            decList.add(new BigDecimal(yyAnaData.getTestValueDstr()));
                        }
                    }
                    for (DtoSample smp : pxSampleList) {
                        //按照当前测试项目对应的样品进行过滤
                        if (pxSampleIdListForTest.contains(smp.getId())) {
                            allSampleCodeList.add(smp.getCode());
                        }
                        DtoAnalyseData pxAnaData = analyseDataList.stream().filter(p -> p.getSampleId().equals(smp.getId())).findFirst().orElse(null);
                        if (StringUtil.isNotNull(pxAnaData)) {
                            // 根据测试项目配置的修约方式
                            if (EnumBase.EnumReviseType.先比较再修约.getValue().equals(test.getReviseType())) {
                                String compareValue = proService.getExamValue(pxAnaData.getTestOrignValue(), examLimitValue, examLimitValueLess);
                                allTstValList.add(proService.getDecimal(pxAnaData.getMostSignificance(), pxAnaData.getMostDecimal(), compareValue));
                            } else {
                                String pxDst = proService.getDecimal(pxAnaData.getMostSignificance(), pxAnaData.getMostDecimal(), pxAnaData.getTestOrignValue());
                                allTstValList.add(proService.getExamValue(pxDst, examLimitValue, examLimitValueLess));
                            }
                            if (MathUtil.isNumeral(pxAnaData.getTestValueDstr())) {
                                decList.add(new BigDecimal(pxAnaData.getTestValueDstr()));
                            }
                        }
                    }
                    if (StringUtil.isNotNull(expendInfo)) {
                        fillRowLabelValue(expendInfo, "样品编号", allSampleCodeList);
                    }
                    List<DtoSample> yyAddSampleList = yyAssSampleList.stream().filter(p -> EnumPRO.EnumSampleCategory.原样加原样.getValue().equals(p.getSampleCategory()))
                            .collect(Collectors.toList());
                    // 当前原样加原样分析数据
                    List<String> yyAddSampleIds = yyAddSampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
                    List<DtoAnalyseData> yyAddAnalyseDateList = analyseDataRepository.findBySampleIdInAndTestIdAndIsDeletedFalse(yyAddSampleIds, testId);
                    if (StringUtil.isNotEmpty(yyAddAnalyseDateList)) {
                        allTstValList.clear();
                        //原样有对应加原样，则测定值获取中间结果
                        if (StringUtil.isNotNull(yyAnaData)) {
                            allTstValList.add(StringUtil.isNotEmpty(yyAnaData.getSeriesValue()) ? yyAnaData.getSeriesValue() : "");
                        }
                        for (DtoSample smp : pxSampleList) {
                            DtoAnalyseData pxAnaData = analyseDataList.stream().filter(p -> p.getSampleId().equals(smp.getId())).findFirst().orElse(null);
                            if (StringUtil.isNotNull(pxAnaData)) {
                                allTstValList.add(StringUtil.isNotEmpty(pxAnaData.getSeriesValue()) ? pxAnaData.getSeriesValue() : "");
                            }
                        }
                    }
                    avgVal = claAvgForPx(decList, examLimitValue, examLimitValueLess, analyseData.getMostSignificance(), analyseData.getMostDecimal());
                }
            }
            return avgVal;
        }
        return "";
    }

    /**
     * 获取平行样质控评价扩展信息
     *
     * @param evaluate    质控评价对象
     * @param analyseData 分析数据对象
     * @param sample      样品对象
     * @return 质控评价扩展信息
     */
    private List<DtoEvaluateRow> getXCParallelExpendInfo(DtoQualityControlEvaluate evaluate, DtoAnalyseData analyseData, DtoSample sample, DtoTest test) {
        List<String> limitInfoList = getExamLimitInfo(test, analyseData);
        String examLimitValueLess = limitInfoList.get(2);
        List<DtoEvaluateRow> expendInfo = initExpendInfo(parallelExtendLabelList, evaluate, analyseData, sample);
        //处理偏差量纲
        String pcLg = EnumBase.EnumJudgingMethod.相对偏差.getValue().equals(evaluate.getJudgingMethod())
                || EnumBase.EnumJudgingMethod.相对误差.getValue().equals(evaluate.getJudgingMethod()) ? "%" : analyseData.getDimension();
        fillRowLabelNameValue(expendInfo, "偏差", Collections.singletonList(evaluate.getCheckItemValue()), "偏差（" + pcLg + "）");
        //找到平行样的原样
        List<DtoSample> yySampleList = sampleRepository.findByIds(Collections.singletonList(sample.getAssociateSampleId()));
        if (StringUtil.isNotEmpty(yySampleList)) {
            DtoSample yySample = yySampleList.get(0);
            //原样没有串联样（需要考虑原样有多个平行样的情况）,先找到原样的所有平行样
            List<String> allSampleCodeList = new ArrayList<>();
            List<String> allTstValList = new ArrayList<>();
            String avgValue = getXcPxInfo(analyseData, yySample, examLimitValueLess, test, allSampleCodeList, allTstValList);
            fillRowLabelValue(expendInfo, "样品编号", allSampleCodeList);
            fillRowLabelNameValue(expendInfo, "测定值", allTstValList, "测定值（" + analyseData.getDimension() + "）");
            //String avgVal = claAvgForPx(decList, examLimitValue, examLimitValueLess, analyseData.getMostSignificance(), analyseData.getMostDecimal());
            fillRowLabelNameValue(expendInfo, "均值", Collections.singletonList(avgValue), "均值（" + analyseData.getDimension() + "）");
        }
        return expendInfo;
    }

    /**
     * 获取现场平行样数据信息
     *
     * @param pxData             平行样数据
     * @param yySample           原样数据
     * @param examLimitValueLess 测定下限
     * @param allSampleCodeList  样品编号列表
     * @param allTstValList      原样结果与平行样结果
     */
    @Override
    public String getXcPxInfo(DtoAnalyseData pxData, DtoSample yySample, String examLimitValueLess, DtoTest test, List<String> allSampleCodeList, List<String> allTstValList) {
        if (StringUtil.isNull(yySample)) {
            DtoSample sample = sampleRepository.findOne(pxData.getSampleId());
            List<DtoSample> yySampleList = sampleRepository.findByIds(Collections.singletonList(sample.getAssociateSampleId()));
            if (StringUtil.isNotEmpty(yySampleList)) {
                yySample = yySampleList.get(0);
            }
        }
        if (StringUtil.isNull(examLimitValueLess)) {
            List<String> limitInfoList = getExamLimitInfo(test, pxData);
            examLimitValueLess = limitInfoList.get(2);
        }
        if (StringUtil.isNotNull(yySample)) {
            String testId = pxData.getTestId();
            DtoAnalyseData yyAnaData = analyseDataRepository.findBySampleIdAndTestIdAndIsDeletedFalse(yySample.getId(), testId).stream().findFirst().orElse(null);
            String examLimitValue = pxData.getExamLimitValue();
            List<DtoSample> pxSampleList = findXCPxByAssSmpId(yySample.getId(), EnumLIM.EnumQCGrade.外部质控.getValue(), pxData.getQcType());
            //找到对应的室内平行样
            List<DtoSample> spxSampleList = findXCPxByAssSmpId(yySample.getId(), EnumLIM.EnumQCGrade.内部质控.getValue(), pxData.getQcType());
            List<String> spxSamIds = spxSampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
            pxSampleList.sort(Comparator.comparing(DtoSample::getCode));
            List<String> sampleIdList = pxSampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
            sampleIdList.add(yySample.getId());
            sampleIdList.addAll(spxSamIds);
            List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndTestIdAndIsDeletedFalse(sampleIdList, testId);
            List<String> pxSampleIdListForTest = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
            if (spxSampleList.size() > 0) {
                allSampleCodeList.add((yySample.getCode() + "（含内平）"));
            } else {
                allSampleCodeList.add(yySample.getCode());
            }
            String avgValue = "";
            if (StringUtil.isNotNull(yyAnaData)) {
                avgValue = yyAnaData.getTestValue();
                //如果原样存在平行样，那么需要获取对应平行样的出证结果
                if (spxSampleList.size() > 0) {
                    DtoAnalyseData spxAnaData = analyseDataList.stream().filter(p -> spxSamIds.contains(p.getSampleId()) && testId.equals(p.getTestId()))
                            .findFirst().orElse(null);
                    if (StringUtil.isNotNull(spxAnaData)) {
                        allTstValList.add(spxAnaData.getTestValue());
                    } else {
                        allTstValList.add(proService.getExamValue(yyAnaData.getTestValueDstr(), examLimitValue, examLimitValueLess));
                    }
                } else {
                    allTstValList.add(proService.getExamValue(yyAnaData.getTestValueDstr(), examLimitValue, examLimitValueLess));
                }
            }
            for (DtoSample smp : pxSampleList) {
                //按照当前测试项目对应的样品进行过滤
                if (pxSampleIdListForTest.contains(smp.getId())) {
                    allSampleCodeList.add(smp.getCode());
                }
                DtoAnalyseData pxAnaData = analyseDataList.stream().filter(p -> p.getSampleId().equals(smp.getId())
                        && testId.equals(p.getTestId())).findFirst().orElse(null);
                if (StringUtil.isNotNull(pxAnaData)) {
                    //BUG2024030600046
                    String pxValue = pxAnaData.getTestValue();
                    if (StringUtil.isNotEmpty(pxAnaData.getSeriesValue())) {
                        pxValue = pxAnaData.getSeriesValue();
                    }
                    allTstValList.add(proService.getExamValue(pxValue, examLimitValue, examLimitValueLess));
                    avgValue = pxAnaData.getPxAverageValue();
                    avgValue = proService.getExamValue(avgValue, examLimitValue, examLimitValueLess);
                }
            }
            return avgValue;
        }
        return "";
    }

    /**
     * 获取加标样质控评价扩展信息
     *
     * @param evaluate    质控评价对象
     * @param analyseData 分析数据对象
     * @param sample      样品对象
     * @return 质控评价扩展信息
     */
    private List<DtoEvaluateRow> getJbExpendInfo(DtoQualityControlEvaluate evaluate, DtoAnalyseData analyseData, DtoSample sample,
                                                 DtoTest test, DtoQualityControl qualityControl) {
        List<DtoEvaluateRow> expendInfo = initExpendInfo(jbExtendLabelList, evaluate, analyseData, sample);
        DtoSample yySample = sampleRepository.findOne(sample.getAssociateSampleId());
        fillRowLabelValue(expendInfo, "样品编号", Arrays.asList(StringUtil.isNotNull(yySample) ? yySample.getCode() : "", sample.getCode()));
        fillRowLabelNameValue(expendInfo, "加标体积", Collections.singletonList(qualityControl.getQcVolume()),
                "加标体积（" + getDimName(qualityControl.getQcVolumeDimensionId()) + "）");
        fillRowLabelNameValue(expendInfo, "加标量", Collections.singletonList(qualityControl.getQcValue()),
                "加标量（" + getDimName(qualityControl.getQcValueDimensionId()) + "）");
        fillRowLabelNameValue(expendInfo, "测定值", Arrays.asList(qualityControl.getRealSampleTestValue(), qualityControl.getQcTestValue()),
                "测定值（" + getDimName(qualityControl.getQcTestValueDimensionId()) + "）");
        fillRowLabelNameValue(expendInfo, "回收率", Collections.singletonList(evaluate.getCheckItemValue()), "回收率（%）");
        //获取加标样增加值
        String zz = getAddValue(analyseData, qualityControl);
        fillRowLabelNameValue(expendInfo, "增值", Collections.singletonList(zz),
                "增值（" + getDimName(qualityControl.getRealSampleTestValueDimensionId()) + "）");
        return expendInfo;
    }

    /**
     * 获取加标样增加值
     *
     * @param analyseData    分析数据对象
     * @param qualityControl 质控对象
     * @return 增加值
     */
    private String getAddValue(DtoAnalyseData analyseData, DtoQualityControl qualityControl) {
        BigDecimal zz;
        String qcTestValue = qualityControl.getQcTestValue();//测定值
        String realSampleTestValue = qualityControl.getRealSampleTestValue();
        BigDecimal realSampleTestValueDecimal = MathUtil.isNumeral(realSampleTestValue) ? new BigDecimal(realSampleTestValue) : new BigDecimal(0);
        BigDecimal qcTestValueDecimal = MathUtil.isNumeral(qcTestValue) ? new BigDecimal(qcTestValue) : new BigDecimal(0);
        List<DtoAnalyseOriginalRecord> recordList = analyseOriginalRecordRepository.findByAnalyseDataIdIn(Collections.singletonList(analyseData.getId()));
        String formulaId = StringUtil.isNotEmpty(recordList) ? recordList.get(0).getTestFormulaId() : "";
        List<DtoParamsPartFormula> paramsPartFormulasAll = paramsPartFormulaRepository.findByFormulaId(formulaId);
        //倒序排序
        DtoParamsPartFormula paramsPartFormula = paramsPartFormulasAll.stream().filter(p -> p.getFormulaType().equals(EnumLIM.EnumPartFormulaType.加标公式.getValue()))
                .sorted(Comparator.comparing(DtoParamsPartFormula::getOrderNum).reversed()).collect(Collectors.toList()).stream().findFirst().orElse(null);
        int sig = StringUtil.isNotNull(paramsPartFormula) ? paramsPartFormula.getMostSignificance() : -1,
                dec = StringUtil.isNotNull(paramsPartFormula) ? paramsPartFormula.getMostDecimal() : -1;
        if (sig != -1 && dec != -1) {
            zz = new BigDecimal(proService.getDecimal(sig, dec, qcTestValueDecimal.subtract(realSampleTestValueDecimal).toString()));
        } else {
            zz = (qcTestValueDecimal.subtract(realSampleTestValueDecimal)).multiply(new BigDecimal(1000)).divide(new BigDecimal(1000));
        }
        return zz.stripTrailingZeros().toPlainString();
    }

    /**
     * 获取标样质控评价扩展信息
     *
     * @param evaluate    质控评价对象
     * @param analyseData 分析数据对象
     * @param sample      样品对象
     * @return 质控评价扩展信息
     */
    private List<DtoEvaluateRow> getStandardExpendInfo(DtoQualityControlEvaluate evaluate, DtoAnalyseData analyseData, DtoSample sample,
                                                       DtoTest test, DtoQualityControl qualityControl) {
        List<DtoEvaluateRow> expendInfo = null;
        if (EnumBase.EnumJudgingMethod.相对偏差.getValue().equals(evaluate.getJudgingMethod()) ||
                EnumBase.EnumJudgingMethod.相对误差.getValue().equals(evaluate.getJudgingMethod())) {
            expendInfo = initExpendInfo(standardExtendLabelListOfDeviation, evaluate, analyseData, sample);
            fillRowLabelNameValue(expendInfo, "检查项值", Collections.singletonList(evaluate.getCheckItemValue()), "检查项值（" + analyseData.getDimension() + "）");
        } else {
            expendInfo = initExpendInfo(standardExtendLabelList, evaluate, analyseData, sample);
            fillRowLabelNameValue(expendInfo, "测定值", Collections.singletonList(evaluate.getCheckItemValue()), "测定值（" + analyseData.getDimension() + "）");
        }
        //不确定类型为%时修正允许限值显示
        String qcValueText = qualityControl.getQcValue();
        if (EnumBase.EnumUncertainType.区间.getValue().equals(qualityControl.getUncertainType())) {
            //区间  格式： 标准（下限~上限）
            qcValueText = qualityControl.getQcValue() + "(" + qualityControl.getRangeLow() + "~" + qualityControl.getRangeHigh() + ")";
        } else if (EnumBase.EnumUncertainType.百分比.getValue().equals(qualityControl.getUncertainType())) {
            qcValueText = qcValueText + "%";
        }
        fillRowLabelNameValue(expendInfo, "标准值", Collections.singletonList(qcValueText), "标准值（" + analyseData.getDimension() + "）");
        fillRowLabelValue(expendInfo, "质控样编号", Collections.singletonList(qualityControl.getQcCode()));
        return expendInfo;
    }

    /**
     * 获取曲线校核样质控评价扩展信息
     *
     * @param evaluate    质控评价对象
     * @param analyseData 分析数据对象
     * @param sample      样品对象
     * @return 质控评价扩展信息
     */
    private List<DtoEvaluateRow> getCurveExpendInfo(DtoQualityControlEvaluate evaluate, DtoAnalyseData analyseData, DtoSample sample,
                                                    DtoTest test, DtoQualityControl qualityControl, DtoAnalyseQualityControlData analyseQualityControlData) {
        List<DtoEvaluateRow> expendInfo = initExpendInfo(curveExtendLabelList, evaluate, analyseData, sample);
        fillRowLabelNameValue(expendInfo, "标准溶液加入体积", Collections.singletonList(qualityControl.getQcVolume()),
                "标准溶液加入体积（" + getDimName(qualityControl.getQcVolumeDimensionId()) + "）");
        fillRowLabelNameValue(expendInfo, "理论值", Collections.singletonList(qualityControl.getQcValue()),
                "理论值（" + getDimName(qualityControl.getQcValueDimensionId()) + "）");
        String testValue = "";
        if (EnumLIM.EnumCheckItemType.出证结果.name().equals(evaluate.getCheckItem())) {
            testValue = analyseData.getTestValue();
        } else {
            testValue = StringUtil.isNotEmpty(evaluate.getCheckItem()) ? analyseQualityControlData.getParamMap().getOrDefault(evaluate.getCheckItem(), "/") : "/";
        }
        fillRowLabelNameValue(expendInfo, "测定值", Collections.singletonList(testValue), "测定值（" + analyseData.getDimension() + "）");
        fillRowLabelNameValue(expendInfo, "相对偏差/误差", Collections.singletonList(evaluate.getCheckItemValue()), "相对偏差/误差（%）");
        fillRowLabelNameValue(expendInfo, "允许相对偏差/误差", Collections.singletonList(evaluate.getAllowLimit()), "允许相对偏差/误差（%）");
        return expendInfo;
    }

    /**
     * 获取替代样样质控评价扩展信息
     *
     * @param evaluate    质控评价对象
     * @param analyseData 分析数据对象
     * @param sample      样品对象
     * @return 质控评价扩展信息
     */
    private List<DtoEvaluateRow> getReplaceExpendInfo(DtoQualityControlEvaluate evaluate, DtoAnalyseData analyseData, DtoSample sample,
                                                      DtoTest test, DtoQualityControl qualityControl) {
        List<DtoEvaluateRow> expendInfo = initExpendInfo(replaceExtendLabelList, evaluate, analyseData, sample);
        fillRowLabelValue(expendInfo, "分析项目", Collections.singletonList(qualityControl.getQcVolume()));
        fillRowLabelNameValue(expendInfo, "测定值", Collections.singletonList(analyseData.getTestValue()), "测定值（" + analyseData.getDimension() + "）");
        fillRowLabelNameValue(expendInfo, "回收率", Collections.singletonList(evaluate.getCheckItemValue()), "回收率（%）");
        fillRowLabelNameValue(expendInfo, "加入量", Collections.singletonList(qualityControl.getQcValue()), "加入量（" + getDimName(qualityControl.getQcValueDimensionId()) + "）");
        fillRowLabelNameValue(expendInfo, "允许回收率", Collections.singletonList(evaluate.getAllowLimit()), "允许回收率（%）");
        return expendInfo;
    }

    /**
     * 获取串联样质控评价扩展信息
     *
     * @param evaluate    质控评价对象
     * @param analyseData 分析数据对象
     * @param sample      样品对象
     * @return 质控评价扩展信息
     */
    private List<DtoEvaluateRow> getClExpendInfo(DtoQualityControlEvaluate evaluate, DtoAnalyseData analyseData, DtoSample sample) {
        List<DtoEvaluateRow> expendInfo = initExpendInfo(clExtendLabelList, evaluate, analyseData, sample);
        //获取串联样穿透率公式
        String clFormula = getClFormula(analyseData, sample);
        fillRowLabelValue(expendInfo, "穿透公式（%）", Collections.singletonList(clFormula));
        fillRowLabelValue(expendInfo, "穿透率（%）", Collections.singletonList(evaluate.getCheckItemValue()));
        fillRowLabelValue(expendInfo, "允许穿透率（%）", Collections.singletonList(evaluate.getAllowLimit()));
        List<String> sampleCodeList = new ArrayList<>();
        sampleCodeList.add(sample.getCode());
        DtoSample yySample = sampleRepository.findByIds(Collections.singletonList(sample.getAssociateSampleId())).stream().findFirst().orElse(null);
        String yyTestValueDst = "";
        //判断是否是串联平行
        Optional<DtoQualityControl> qcData = qualityControlRepository.findByIds(Collections.singletonList(sample.getQcId()))
                .stream().findFirst();
        if (qcData.isPresent()) {
            if (qcData.get().getQcType().equals(new QualityParallel().qcTypeValue())) {
                //平行
                Optional<String> qcId = qualityControlRepository.findByAssociateSampleIdIn(Collections.singletonList(yySample.getAssociateSampleId()))
                        .stream().filter(p -> p.getQcType().equals(new QualityParallel().qcTypeValue())).map(DtoQualityControl::getId).findFirst();
                if (qcId.isPresent()) {
                    Optional<DtoSample> sam = sampleRepository.findByQcIdIn(Collections.singletonList(qcId.get())).stream().findFirst();
                    if (sam.isPresent()) {
                        yySample = sam.get();
                    }
                }
            }
        }
        if (StringUtil.isNotNull(yySample)) {
            sampleCodeList.add(yySample.getCode());
            DtoAnalyseData yyAnaData = analyseDataRepository.findBySampleIdAndTestIdAndIsDeletedFalse(yySample.getId(), analyseData.getTestId()).stream().findFirst().orElse(null);
            if (StringUtil.isNotNull(yyAnaData)) {
                yyTestValueDst = yyAnaData.getTestValueDstr();
            }
        }

        fillRowLabelValue(expendInfo, "样品编号", sampleCodeList);
        fillRowLabelNameValue(expendInfo, "测定值", Arrays.asList(analyseData.getTestValueDstr(), yyTestValueDst), "测定值（" + analyseData.getDimension() + "）");
        return expendInfo;
    }

    /**
     * 获取阴性/阳性对照试验样质控评价扩展信息
     *
     * @param evaluate    质控评价对象
     * @param analyseData 分析数据对象
     * @param sample      样品对象
     * @return 质控评价扩展信息
     */
    private List<DtoEvaluateRow> getNegPosExpendInfo(DtoQualityControlEvaluate evaluate, DtoAnalyseData analyseData, DtoSample sample) {
        List<DtoEvaluateRow> expendInfo = initExpendInfo(negPosExtendLabelList, evaluate, analyseData, sample);
        fillRowLabelNameValue(expendInfo, "允许限值", Collections.singletonList(StringUtil.isNotEmpty(evaluate.getAllowLimit()) ? evaluate.getAllowLimit() : ""),
                "允许限值（" + analyseData.getDimension() + "）");
        fillRowLabelNameValue(expendInfo, "测定值", Collections.singletonList(analyseData.getTestValue()), "测定值（" + analyseData.getDimension() + "）");
        fillRowLabelNameValue(expendInfo, "回收率", Collections.singletonList(evaluate.getCheckItemValue()), "回收率（%）");
        return expendInfo;
    }

    /**
     * 获取校正系数检验样质控评价扩展信息
     *
     * @param evaluate       质控评价对象
     * @param analyseData    分析数据对象
     * @param qualityControl 质控信息
     * @param sample         样品对象
     * @return 质控评价扩展信息
     */
    private List<DtoEvaluateRow> getJzExpendInfo(DtoQualityControlEvaluate evaluate, DtoAnalyseData analyseData, DtoQualityControl qualityControl, DtoSample sample) {
        List<DtoEvaluateRow> expendInfo = initExpendInfo(jzExtendLabelList, evaluate, analyseData, sample);
        fillRowLabelNameValue(expendInfo, "校正点浓度", Collections.singletonList(StringUtil.isNotEmpty(qualityControl.getQcValue()) ? qualityControl.getQcValue() : ""),
                "校正点浓度（" + getDimName(qualityControl.getQcValueDimensionId()) + "）");
        fillRowLabelNameValue(expendInfo, "实测浓度", Collections.singletonList(analyseData.getTestValue()),
                "实测浓度（" + analyseData.getDimension() + "）");
        fillRowLabelNameValue(expendInfo, "相对偏差/误差", Collections.singletonList(analyseData.getQcInfo().replace("%", "")),
                "相对偏差/误差（%）");
        fillRowLabelNameValue(expendInfo, "允许相对偏差/误差", Collections.singletonList(StringUtil.isNotEmpty(evaluate.getAllowLimit()) ? evaluate.getAllowLimit() : ""),
                "允许相对偏差/误差（%）");
        return expendInfo;
    }

    /**
     * 设置平行样扩展信息中的测定值及均值
     *
     * @param analyseData 分析数据对象
     * @param sample      样品对象
     */
    private String getClFormula(DtoAnalyseData analyseData, DtoSample sample) {
        String formula = "[B]/[A]*100";
        List<DtoQualityControlLimit> controlLimitList = qualityControlLimitRepository.findByTestId(analyseData.getTestId());
        controlLimitList = controlLimitList.stream().filter(p -> EnumLIM.EnumQCType.串联样.getValue().equals(p.getQcType())).collect(Collectors.toList());
        //找到原样
        DtoAnalyseData yyData = analyseDataRepository.findBySampleIdAndTestIdAndIsDeletedFalse(sample.getAssociateSampleId(), analyseData.getTestId())
                .stream().findFirst().orElse(null);
        CalculationService calculationService = SpringContextAware.getBean(CalculationService.class);
        DtoQualityControlLimit limit = StringUtil.isNotNull(yyData) ? getControlLimit(controlLimitList, yyData.getTestValueDstr(), calculationService) : null;
        if (StringUtil.isNotNull(limit)) {
            formula = limit.getFormula();
        }
        return formula;
    }

    /**
     * 设置平行样扩展信息中的测定值及均值
     *
     * @param decList            数据列表
     * @param examLimitValue     检出限
     * @param examLimitValueLess 小于检出限结果
     * @param mostSignificance   有效位
     * @param mostDecimal        小数位
     * @return 均值
     */
    private String setTstAvgForPx(List<BigDecimal> decList, String examLimitValue, String examLimitValueLess, Integer mostSignificance,
                                  Integer mostDecimal, List<String> tstStrList) {
        for (BigDecimal dec : decList) {
            tstStrList.add(proService.getExamValue(dec.toString(), examLimitValue, examLimitValueLess));
        }
        //计算均值(小于检出限按检出限一半算)
        return claAvgForPx(decList, examLimitValue, examLimitValueLess, mostSignificance, mostDecimal);
    }

    /**
     * 计算平行样扩展信息中的均值
     *
     * @param decList          数据列表
     * @param examLimitVal     检出限
     * @param mostSignificance 有效位
     * @param mostDecimal      小数位
     * @return 均值
     */
    private String claAvgForPx(List<BigDecimal> decList, String examLimitVal, String examLimitValueLess, Integer mostSignificance, Integer mostDecimal) {
        String res = "";
        if (StringUtil.isNotEmpty(decList)) {
            if (!MathUtil.isNumeral(examLimitVal)) {
                throw new BaseException("检出限不是数字格式");
            }
            BigDecimal examDec = new BigDecimal(examLimitVal);
            BigDecimal sumDec = BigDecimal.ZERO;
            for (BigDecimal dec : decList) {
                dec = dec.compareTo(examDec) < 0 ? examDec.divide(new BigDecimal("2"), ProCodeHelper.COMMON_ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN) : dec;
                sumDec = sumDec.add(dec);
            }
            if (decList.size() > 2) {
                //求标准差
                BigDecimal sizeDec = new BigDecimal(String.valueOf(decList.size()));
                BigDecimal avgDec = sumDec.divide(sizeDec, ProCodeHelper.COMMON_ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN);
                BigDecimal var = BigDecimal.ZERO;
                //求方差
                for (BigDecimal dec : decList) {
                    var = var.add((dec.subtract(avgDec)).multiply(dec.subtract(avgDec)));
                }
                double doubleResult = Math.sqrt(var.divide(sizeDec, ProCodeHelper.COMMON_ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN).doubleValue()) * 100;
                res = String.valueOf(doubleResult);
            } else {
                res = sumDec.divide(new BigDecimal(String.valueOf(decList.size())), ProCodeHelper.COMMON_ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN).toString();
            }
            res = proService.getDecimal(mostSignificance, mostDecimal, res);
            res = proService.getExamValue(res, examLimitVal, examLimitValueLess);
        }
        return res;
    }

    /**
     * 根据关联样id找到原样对应的平行样
     *
     * @param assId 关联样id
     * @return 平行样列表
     */
    private List<DtoSample> findPxByAssSmpId(String assId) {
        List<DtoSample> pxSampleList = new ArrayList<>();
        List<DtoSample> assSampleList = sampleRepository.findByAssociateSampleIdIn(Collections.singletonList(assId));
        if (StringUtil.isNotEmpty(assSampleList)) {
            List<String> qcIdList = assSampleList.stream().map(DtoSample::getQcId).collect(Collectors.toList());
            List<DtoQualityControl> qcList = qualityControlRepository.findAll(qcIdList).stream().filter(p -> EnumLIM.EnumQCType.平行.getValue().equals(p.getQcType())
                    && EnumLIM.EnumQCGrade.内部质控.getValue().equals(p.getQcGrade())).collect(Collectors.toList());
            List<String> pxQcIdList = qcList.stream().map(DtoQualityControl::getId).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(pxQcIdList)) {
                pxSampleList = assSampleList.stream().filter(p -> pxQcIdList.contains(p.getQcId())).collect(Collectors.toList());
            }
        }
        return pxSampleList;
    }

    /**
     * 根据关联样id找到原样对应的平行样
     *
     * @param assId 关联样id
     * @return 平行样列表
     */
    private List<DtoSample> findXCPxByAssSmpId(String assId, Integer qcGroup, Integer qcType) {
        List<DtoSample> pxSampleList = new ArrayList<>();
        List<DtoSample> assSampleList = sampleRepository.findByAssociateSampleIdIn(Collections.singletonList(assId));
        if (StringUtil.isNotEmpty(assSampleList)) {
            List<String> qcIdList = assSampleList.stream().map(DtoSample::getQcId).collect(Collectors.toList());
            List<DtoQualityControl> qcList = qualityControlRepository.findAll(qcIdList).stream().filter(p -> qcType.equals(p.getQcType())
                    && qcGroup.equals(p.getQcGrade())).collect(Collectors.toList());
            List<String> pxQcIdList = qcList.stream().map(DtoQualityControl::getId).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(pxQcIdList)) {
                pxSampleList = assSampleList.stream().filter(p -> pxQcIdList.contains(p.getQcId())).collect(Collectors.toList());
            }
        }
        return pxSampleList;
    }

    /**
     * 根据量纲id获取量纲名称
     *
     * @param dimId 量纲id
     * @return 量纲名称
     */
    private String getDimName(String dimId) {
        String dimName = "";
        if (StringUtil.isNotEmpty(dimId) && !UUIDHelper.GUID_EMPTY.equals(dimId)) {
            DtoDimension dimension = dimensionService.findOne(dimId);
            if (StringUtil.isNotNull(dimension)) {
                dimName = dimension.getDimensionName();
            }
        }
        return dimName;
    }

    /**
     * 获取检出限，小于检出限结果，测定下限
     *
     * @param test        测试项目
     * @param analyseData 样品类型id
     * @return 检出限，小于检出限结果，测定下限
     */
    private List<String> getExamLimitInfo(DtoTest test, DtoAnalyseData analyseData) {
        String sampleTypeId = analyseData.getSampleTypeId();
        String lowerLimit = "", examLimitVal = "", examLimitValueLess = "";
        if (StringUtil.isNotNull(test)) {
            lowerLimit = test.getLowerLimit();
            examLimitVal = test.getExamLimitValue();
            examLimitValueLess = test.getExamLimitValueLess();
            List<DtoTestExpand> expands = testExpandService.findRedisByTestId(test.getId());
            if (StringUtils.isNotNullAndEmpty(sampleTypeId) && !sampleTypeId.equals(UUIDHelper.GUID_EMPTY)) {
                Optional<DtoTestExpand> optionalTestExpand = expands.stream().filter(p -> p.getSampleTypeId().equals(sampleTypeId)).findFirst();
                if (!optionalTestExpand.isPresent()) {
                    optionalTestExpand = expands.stream().filter(p -> p.getSampleTypeId().equals(test.getSampleTypeId())).findFirst();
                }
                if (optionalTestExpand.isPresent()) {
                    DtoTestExpand dtoTestExpand = optionalTestExpand.get();
                    if (StringUtil.isNotEmpty(dtoTestExpand.getLowerLimit())) {
                        lowerLimit = dtoTestExpand.getLowerLimit();
                    }
                    if (StringUtil.isNotEmpty(dtoTestExpand.getExamLimitValue())) {
                        examLimitVal = dtoTestExpand.getExamLimitValue();
                    }
                    if (StringUtil.isNotEmpty(dtoTestExpand.getExamLimitValueLess())) {
                        examLimitValueLess = dtoTestExpand.getExamLimitValueLess();
                    }
                }
            }
        }
        if (StringUtil.isNotEmpty(analyseData.getLowerLimit())) {
            lowerLimit = analyseData.getLowerLimit();
        }
        return Stream.of(examLimitVal, lowerLimit, examLimitValueLess).collect(Collectors.toList());
    }

    /**
     * 获取项目下所有质控样
     *
     * @param qcAnalyseDataList 质控样分析数据
     * @param qcSampleMap       质控样品映射
     * @param qcEvaluateList    质控评价映射
     * @param folderMap         点位映射
     * @param projectId         项目id
     * @param reportId          报告id
     * @return 样品评价明细列表
     */
    private List<DtoQcSampleEvaluateDetail> assembleQcEvaluateDetail(List<DtoAnalyseData> qcAnalyseDataList, Map<String, DtoSample> qcSampleMap,
                                                                     List<DtoQualityControlEvaluate> qcEvaluateList, Map<String, DtoSampleFolder> folderMap,
                                                                     String projectId, String reportId) {
        List<DtoQcSampleEvaluateDetail> detailList = new ArrayList<>();
        for (DtoAnalyseData analyseData : qcAnalyseDataList) {
            DtoSample sample = qcSampleMap.get(analyseData.getSampleId());
            String sampleCode = StringUtil.isNotNull(sample) ? sample.getCode() : "";
            Integer qcGrade = analyseData.getQcGrade();
            Integer qcType = analyseData.getQcType();
            if ((qcGrade.equals(-1) || qcType.equals(-1)) && StringUtil.isNotNull(sample)) {
                //从样品类型获取质控等级及质控类型
                EnumPRO.EnumSampleCategory category = EnumPRO.EnumSampleCategory.getByValue(sample.getSampleCategory());
                if (StringUtil.isNotNull(category)) {
                    qcGrade = category.getQcGrade();
                    qcType = category.getQcType();
                }
            }
            String qcTypeName = !qcType.equals(-1) ? QualityTaskFactory.getInstance().getQcSample(qcType).getRedFolderName("", qcGrade) : "";
            //获取质控评价对象
            List<DtoQualityControlEvaluate> qcList = qcEvaluateList.stream().filter(p -> p.getObjectId().equals(analyseData.getId())).collect(Collectors.toList());
            for (DtoQualityControlEvaluate evaluate : qcList) {
                String evaluateId = StringUtil.isNotNull(evaluate) ? evaluate.getId() : UUIDHelper.GUID_EMPTY;
                String checkItem = StringUtil.isNotNull(evaluate) ? evaluate.getCheckItem() : "";
                Integer judgeMethod = StringUtil.isNotNull(evaluate) ? evaluate.getJudgingMethod() : -1;
                String checkItemValue = StringUtil.isNotNull(evaluate) ? evaluate.getCheckItemValue() : "";
                String allowLimit = StringUtil.isNotNull(evaluate) ? evaluate.getAllowLimit() : "";
                Boolean pass = StringUtil.isNotNull(evaluate) ? evaluate.getIsPass() : null;
                String passStr = "2";
                if (StringUtil.isNotNull(pass) && !(StringUtil.isNotEmpty(evaluate.getCheckItemValue()) && evaluate.getCheckItemValue().contains("无法计算"))) {
                    passStr = pass ? "1" : "0";
                }
                String sampleFolderId = StringUtil.isNotNull(sample) ? sample.getSampleFolderId() : UUIDHelper.GUID_EMPTY;
                String folderName = folderMap.containsKey(sampleFolderId) ? folderMap.get(sampleFolderId).getWatchSpot() : "";
                detailList.add(new DtoQcSampleEvaluateDetail(evaluateId, sampleCode, analyseData.getRedAnalyzeItemName(), qcGrade, qcType, checkItem, judgeMethod,
                        checkItemValue, allowLimit, passStr, folderName, analyseData.getRedAnalyzeMethodName(), projectId, reportId, null, qcTypeName));
            }
        }
        return detailList;
    }

    /**
     * 按照查询条件过滤质控明细
     *
     * @param evaluateDetailList 质控明细
     * @param queryDetail        查询条件对象
     * @return 过滤后的质控明细
     */
    private List<DtoQcSampleEvaluateDetail> filterEvaluateDetail(List<DtoQcSampleEvaluateDetail> evaluateDetailList, DtoQcSampleEvaluateDetail queryDetail) {
        if (StringUtil.isNotEmpty(queryDetail.getIsPass())) {
            evaluateDetailList = evaluateDetailList.stream().filter(p -> queryDetail.getIsPass().equals(p.getIsPass())).collect(Collectors.toList());
        }
        if (StringUtil.isNotNull(queryDetail.getQcGrade())) {
            evaluateDetailList = evaluateDetailList.stream().filter(p -> queryDetail.getQcGrade().equals(p.getQcGrade())).collect(Collectors.toList());
        }
        if (StringUtil.isNotNull(queryDetail.getQcType())) {
            evaluateDetailList = evaluateDetailList.stream().filter(p -> queryDetail.getQcType().equals(p.getQcType())).collect(Collectors.toList());
        }
        if (StringUtil.isNotEmpty(queryDetail.getSampleFolderName())) {
            evaluateDetailList = evaluateDetailList.stream().filter(p -> p.getSampleFolderName().contains(queryDetail.getSampleFolderName())).collect(Collectors.toList());
        }
        if (StringUtil.isNotNull(queryDetail.getSampleCode())) {
            evaluateDetailList = evaluateDetailList.stream().filter(p -> p.getSampleCode().contains(queryDetail.getSampleCode())).collect(Collectors.toList());
        }
        if (StringUtil.isNotNull(queryDetail.getAnalyzeItemName())) {
            evaluateDetailList = evaluateDetailList.stream().filter(p -> p.getAnalyzeItemName().contains(queryDetail.getAnalyzeItemName())).collect(Collectors.toList());
        }
        if (StringUtil.isNotNull(queryDetail.getAnalyzeMethodName())) {
            evaluateDetailList = evaluateDetailList.stream().filter(p -> p.getAnalyzeMethodName().contains(queryDetail.getAnalyzeMethodName())).collect(Collectors.toList());
        }
        return evaluateDetailList;
    }

    /**
     * 获取项目下所有质控样
     *
     * @param projectId 项目id
     * @return 样品评价明细列表
     */
    private List<DtoSample> getQcSampleForProject(String projectId) {
        List<DtoSample> yySampleList = sampleRepository.findByProjectId(projectId);
        List<DtoSample> qcSampleList = getQcSample(yySampleList);
        List<String> qcSampleIdList = qcSampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<String> workSheetFolderIdList = project2WorkSheetFolderRepository.findByProjectId(projectId).stream().map(Project2WorkSheetFolder::getWorkSheetFolderId)
                .filter(p -> StringUtil.isNotEmpty(p) && !UUIDHelper.GUID_EMPTY.equals(p)).distinct().collect(Collectors.toList());
        if (StringUtil.isNotEmpty(workSheetFolderIdList)) {
            List<DtoAnalyseData> dataForWorkSheetFolder = analyseDataRepository.findByWorkSheetFolderIdInAndIsDeletedFalse(workSheetFolderIdList);
            List<String> inQcSampleIdList = dataForWorkSheetFolder.stream().filter(p -> StringUtil.isNotEmpty(p.getQcId()) && !UUIDHelper.GUID_EMPTY.equals(p.getQcId())
                            && !(EnumLIM.EnumQCGrade.内部质控.getValue().equals(p.getQcGrade()) && EnumLIM.EnumQCType.平行.getValue().equals(p.getQcType()))
                            && !(EnumLIM.EnumQCGrade.内部质控.getValue().equals(p.getQcGrade()) && EnumLIM.EnumQCType.加标.getValue().equals(p.getQcType())))
                    .map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
            inQcSampleIdList.removeAll(qcSampleIdList);
            if (StringUtil.isNotEmpty(inQcSampleIdList)) {
                List<DtoSample> inQcSampleList = sampleRepository.findByIdInAndIsDeletedFalse(inQcSampleIdList);
                qcSampleList.addAll(inQcSampleList);
            }
        }
        return qcSampleList;
    }

    /**
     * 获取报告下所有质控样
     *
     * @param reportIdList 项目id列表
     * @return 样品评价明细列表
     */
    private List<DtoSample> getQcSampleForReport(List<String> reportIdList) {
        return getQcSampleForReportSample(reportIdList, new ArrayList<>());
    }


    /**
     * 根据报告下的是样品获取质控信息
     *
     * @param reportIdList 报告ids
     * @param sampleList   报告绑定的样品信息
     * @return 质控样
     */
    @Override
    public List<DtoSample> getQcSampleForReportSample(List<String> reportIdList, List<DtoSample> sampleList) {
        List<DtoReportDetail> reportDetailList = reportDetailRepository.findByReportIdIn(reportIdList);
        Map<String, List<DtoReportDetail>> reportDetailMap = reportDetailList.stream().collect(Collectors.groupingBy(DtoReportDetail::getReportId));
        if (StringUtil.isEmpty(sampleList)) {
            List<String> sampleIdList = reportDetailList.stream().map(DtoReportDetail::getObjectId).collect(Collectors.toList());
            sampleList = StringUtil.isNotEmpty(sampleIdList) ? sampleRepository.findAll(sampleIdList) : new ArrayList<>();
        }
        List<DtoSample> yySampleList = sampleList.stream().filter(p -> !EnumPRO.EnumSampleCategory.质控样.getValue().equals(p.getSampleCategory()))
                .collect(Collectors.toList());
        List<DtoSample> qcSampleList = getQcSample(yySampleList);
        List<String> qcSampleIdList = qcSampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        //获取报告所选样品下的所有送样单中的全程序空白样和运输空白样
        List<DtoSample> kbSampleForRecord = getKbSampleForRecord(sampleList);
        for (DtoSample kbSample : kbSampleForRecord) {
            if (!qcSampleIdList.contains(kbSample.getId())) {
                qcSampleList.add(kbSample);
                qcSampleIdList.add(kbSample.getId());
            }
        }
        // 获取报告所选样品关联检测单中的没有与样品建立绑定关系的质控样
        List<DtoSample> innerQcSampleList = getInnerQcSample(yySampleList, qcSampleList);
        List<String> sampleIdList = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        Map<String, DtoReportBaseInfo> reportBaseInfoMap = StringUtil.isNotEmpty(reportIdList) ? reportBaseInfoRepository.findByReportIdIn(reportIdList)
                .stream().collect(Collectors.toMap(DtoReportBaseInfo::getReportId, dto -> dto, (r1, r2) -> r1)) : new HashMap<>();
        Map<String, DtoReport> reportMap = StringUtil.isNotEmpty(reportIdList) ? reportRepository.findAll(reportIdList).stream()
                .collect(Collectors.toMap(DtoReport::getId, dto -> dto)) : new HashMap<>();
        for (String reportId : reportIdList) {
            boolean reportQc = !reportBaseInfoMap.containsKey(reportId) || StringUtil.isNull(reportBaseInfoMap.get(reportId).getAssociateSampleQc())
                    || reportBaseInfoMap.get(reportId).getAssociateSampleQc();
            if (reportQc) {
                DtoReport report = reportMap.getOrDefault(reportId, null);
                if (StringUtil.isNotNull(report)) {
                    List<String> projectIds = Collections.singletonList(report.getProjectId());
                    List<String> yySampleIdForReport = reportDetailMap.getOrDefault(report.getId(), new ArrayList<>()).stream().map(DtoReportDetail::getObjectId).collect(Collectors.toList());
                    List<DtoSample> yySampleForReport = yySampleList.stream().filter(p -> yySampleIdForReport.contains(p.getId())).collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(yySampleForReport)) {
                        List<String> receiveIdList = yySampleForReport.stream().filter(p -> StringUtil.isNotEmpty(p.getReceiveId()) && !UUIDHelper.GUID_EMPTY.equals(p.getReceiveId()))
                                .map(DtoSample::getReceiveId).distinct().collect(Collectors.toList());
                        //仅过滤掉相关检测单中的室内质控样（排除室内空白及标样 空白加标、曲线校核样、替代物、）
                        innerQcSampleList.removeIf(s -> EnumLIM.EnumQCGrade.内部质控.getValue().equals(s.getQcGrade()) &&
                                (!receiveIdList.contains(s.getReceiveId()) || !projectIds.contains(s.getProjectId()) || !sampleIdList.contains(s.getAssociateSampleId()))
                                && !EnumLIM.EnumQCType.标准.getValue().equals(s.getQcType()) && !EnumLIM.EnumQCType.空白.getValue().equals(s.getQcType())
                                && !EnumLIM.EnumQCType.空白加标.getValue().equals(s.getQcType()) && !EnumLIM.EnumQCType.曲线校核.getValue().equals(s.getQcType())
                                && !EnumLIM.EnumQCType.替代物.getValue().equals(s.getQcType()) && !EnumLIM.EnumQCType.仪器空白.getValue().equals(s.getQcType())
                                && !EnumLIM.EnumQCType.试剂空白.getValue().equals(s.getQcType()) && !EnumLIM.EnumQCType.采样介质空白.getValue().equals(s.getQcType())
                                && !EnumLIM.EnumQCType.阳性对照试验.getValue().equals(s.getQcType()) && !EnumLIM.EnumQCType.阴性对照试验.getValue().equals(s.getQcType())
                                && !EnumLIM.EnumQCType.校正系数检验.getValue().equals(s.getQcType()));
                        // 过滤相关检测单中的全程序空白和现场平行
                        innerQcSampleList.removeIf(s -> (!receiveIdList.contains(s.getReceiveId()) || !projectIds.contains(s.getProjectId())
                                || !sampleIdList.contains(s.getAssociateSampleId())) && (EnumLIM.EnumQCType.空白.getValue().equals(s.getQcType())
                                || EnumLIM.EnumQCType.平行.getValue().equals(s.getQcType())) && EnumLIM.EnumQCGrade.外部质控.getValue().equals(s.getQcGrade()));
                    }
                }
            }
        }
        if (StringUtil.isNotEmpty(innerQcSampleList)) {
            qcSampleList.addAll(innerQcSampleList);
        }
        return qcSampleList;
    }


    /**
     * 获取报告所选样品下的所有送样单中的全程序空白样和运输空白样
     *
     * @param sampleList 报告中选取的样品
     * @return 全程序空白样和运输空白样
     */
    private List<DtoSample> getKbSampleForRecord(List<DtoSample> sampleList) {
        List<DtoSample> kbSampleForRecord = new ArrayList<>();
        if (StringUtil.isNotEmpty(sampleList)) {
            List<String> receiveIdList = sampleList.stream().map(DtoSample::getReceiveId)
                    .filter(receiveId -> !UUIDHelper.GUID_EMPTY.equals(receiveId))
                    .distinct().collect(Collectors.toList());
            List<DtoSample> sampleForRecord = sampleRepository.findByReceiveIdIn(receiveIdList);
            Set<String> qcIdList = sampleForRecord.stream().map(DtoSample::getQcId).collect(Collectors.toSet());
            List<DtoQualityControl> qcList = StringUtil.isNotEmpty(qcIdList) ? qualityControlRepository.findAll(qcIdList) : new ArrayList<>();
            qcList = qcList.stream().filter(p -> (EnumLIM.EnumQCType.空白.getValue().equals(p.getQcType()) || EnumLIM.EnumQCType.运输空白.getValue().equals(p.getQcType()))
                    && EnumLIM.EnumQCGrade.外部质控.getValue().equals(p.getQcGrade())).collect(Collectors.toList());
            List<String> kbQcIdList = qcList.stream().map(DtoQualityControl::getId).collect(Collectors.toList());
            kbSampleForRecord = sampleForRecord.stream().filter(p -> kbQcIdList.contains(p.getQcId())).collect(Collectors.toList());
        }
        return kbSampleForRecord;
    }


    /**
     * 获取不与原样关联的实验室质控样
     *
     * @param yySampleList 原样样品
     * @param qcSampleList 质控样品
     * @return 质控样
     */
    private List<DtoSample> getInnerQcSample(List<DtoSample> yySampleList, List<DtoSample> qcSampleList) {
        yySampleList.addAll(qcSampleList);
        List<String> sampleIds = yySampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdIn(sampleIds);
        List<String> workSheetFolderIds = analyseDataList.stream().filter(p -> StringUtil.isNotEmpty(p.getWorkSheetFolderId())
                && !UUIDHelper.GUID_EMPTY.equals(p.getWorkSheetFolderId())).map(DtoAnalyseData::getWorkSheetFolderId).distinct().collect(Collectors.toList());
        // 根据样品查询检测单中的质控样
        analyseDataList = analyseDataRepository.findByWorkSheetFolderIdInAndIsDeletedFalse(workSheetFolderIds);
        analyseDataList = analyseDataList.stream().filter(p -> !sampleIds.contains(p.getSampleId()) && StringUtil.isNotEmpty(p.getQcId())
                && !UUIDHelper.GUID_EMPTY.equals(p.getQcId())).collect(Collectors.toList());
        List<String> workSheetQcSampleIdList = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
        Map<String, List<DtoAnalyseData>> analyseDataForFolderMap = analyseDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleId));
        List<DtoSample> resList = new ArrayList<>();
        if (StringUtil.isNotEmpty(workSheetQcSampleIdList)) {
            resList = sampleRepository.findByIdInAndIsDeletedFalse(workSheetQcSampleIdList);
            for (DtoSample sample : resList) {
                DtoAnalyseData dataForSample = analyseDataForFolderMap.containsKey(sample.getId()) ? analyseDataForFolderMap.get(sample.getId()).get(0) : null;
                sample.setQcType(StringUtil.isNotNull(dataForSample) ? dataForSample.getQcType() : -1);
                sample.setQcGrade(StringUtil.isNotNull(dataForSample) ? dataForSample.getQcGrade() : -1);
            }
        }
        return resList;
    }

    /**
     * 获取报告质控样过滤参数值
     */
    protected Boolean reportQualityControl() {
        boolean value = Boolean.FALSE;
        ConfigModel configModel = configService.findConfig("sys_pro_report_qualityControl");
        if (StringUtil.isNotNull(configModel) && StringUtil.isNotEmpty(configModel.getConfigValue())) {
            value = Boolean.parseBoolean(configModel.getConfigValue());
        }
        return value;
    }


    /**
     * 判断是否有配置小于检出限或者小于测定下限的限值配置，如有则默认获取其中一个
     *
     * @param controlLimitList 测试项目
     * @return 质控限值配置对象
     */
    private DtoQualityControlLimit checkDetectionTestLimit(List<DtoQualityControlLimit> controlLimitList) {
        for (DtoQualityControlLimit limit : controlLimitList) {
            if (EnumBase.EnumJudgingMethod.小于检出限.getValue().equals(limit.getJudgingMethod())
                    || EnumBase.EnumJudgingMethod.小于测定下限.getValue().equals(limit.getJudgingMethod())) {
                return limit;
            }
        }
        return null;
    }

    @Autowired
    public void setAnalyseDataRepository(AnalyseDataRepository analyseDataRepository) {
        this.analyseDataRepository = analyseDataRepository;
    }

    @Autowired
    public void setQualityControlLimitRepository(QualityControlLimitRepository qualityControlLimitRepository) {
        this.qualityControlLimitRepository = qualityControlLimitRepository;
    }

    @Autowired
    public void setQualityControlRepository(QualityControlRepository qualityControlRepository) {
        this.qualityControlRepository = qualityControlRepository;
    }

    @Autowired
    public void setAnalyseOriginalRecordRepository(AnalyseOriginalRecordRepository analyseOriginalRecordRepository) {
        this.analyseOriginalRecordRepository = analyseOriginalRecordRepository;
    }

    @Autowired
    public void setQualityControlEvaluateRepository(QualityControlEvaluateRepository qualityControlEvaluateRepository) {
        this.qualityControlEvaluateRepository = qualityControlEvaluateRepository;
    }

    @Autowired
    public void setSampleRepository(SampleRepository sampleRepository) {
        this.sampleRepository = sampleRepository;
    }

    @Autowired
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    public void setParamsTestFormulaRepository(ParamsTestFormulaRepository paramsTestFormulaRepository) {
        this.paramsTestFormulaRepository = paramsTestFormulaRepository;
    }

    @Autowired
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    @Lazy
    public void setProService(ProService proService) {
        this.proService = proService;
    }

    @Autowired
    @Lazy
    public void setDimensionService(DimensionService dimensionService) {
        this.dimensionService = dimensionService;
    }

    @Autowired
    public void setWorkSheetFolderRepository(WorkSheetFolderRepository workSheetFolderRepository) {
        this.workSheetFolderRepository = workSheetFolderRepository;
    }

    @Autowired
    public void setAnalyzeItemSortDetailRepository(AnalyzeItemSortDetailRepository analyzeItemSortDetailRepository) {
        this.analyzeItemSortDetailRepository = analyzeItemSortDetailRepository;
    }

    @Autowired
    @Lazy
    public void setTestExpandService(TestExpandService testExpandService) {
        this.testExpandService = testExpandService;
    }

    @Autowired
    @Lazy
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    public void setReportDetailRepository(ReportDetailRepository reportDetailRepository) {
        this.reportDetailRepository = reportDetailRepository;
    }

    @Autowired
    public void setSampleFolderRepository(SampleFolderRepository sampleFolderRepository) {
        this.sampleFolderRepository = sampleFolderRepository;
    }

    @Autowired
    @Lazy
    public void setTestService(TestService testService) {
        this.testService = testService;
    }

    @Autowired
    public void setReceiveSubSampleRecord2SampleRepository(ReceiveSubSampleRecord2SampleRepository receiveSubSampleRecord2SampleRepository) {
        this.receiveSubSampleRecord2SampleRepository = receiveSubSampleRecord2SampleRepository;
    }

    @Autowired
    @Lazy
    public void setAnalyseDataService(AnalyseDataService analyseDataService) {
        this.analyseDataService = analyseDataService;
    }

    @Autowired
    public void setParamsPartFormulaRepository(ParamsPartFormulaRepository paramsPartFormulaRepository) {
        this.paramsPartFormulaRepository = paramsPartFormulaRepository;
    }

    @Autowired
    @Lazy
    public void setQualityControlEvaluateTaskService(QualityControlEvaluateTaskService qualityControlEvaluateTaskService) {
        this.qualityControlEvaluateTaskService = qualityControlEvaluateTaskService;
    }

    @Autowired
    public void setCurveRepository(CurveRepository curveRepository) {
        this.curveRepository = curveRepository;
    }

    @Autowired
    public void setProject2WorkSheetFolderRepository(Project2WorkSheetFolderRepository project2WorkSheetFolderRepository) {
        this.project2WorkSheetFolderRepository = project2WorkSheetFolderRepository;
    }

    @Autowired
    @Lazy
    public void setQcConfigService(QCConfigService qcConfigService) {
        this.qcConfigService = qcConfigService;
    }

    @Autowired
    @Lazy
    public void setSampleGroupService(SampleGroupService sampleGroupService) {
        this.sampleGroupService = sampleGroupService;
    }

    @Autowired
    @Lazy
    public void setQualityControlLimitService(QualityControlLimitService qualityControlLimitService) {
        this.qualityControlLimitService = qualityControlLimitService;
    }

    @Autowired
    @Lazy
    public void setConfigService(IConfigService configService) {
        this.configService = configService;
    }

    @Autowired
    @Lazy
    public void setReportRepository(ReportRepository reportRepository) {
        this.reportRepository = reportRepository;
    }

    @Autowired
    @Lazy
    public void setReportBaseInfoRepository(ReportBaseInfoRepository reportBaseInfoRepository) {
        this.reportBaseInfoRepository = reportBaseInfoRepository;
    }
}

