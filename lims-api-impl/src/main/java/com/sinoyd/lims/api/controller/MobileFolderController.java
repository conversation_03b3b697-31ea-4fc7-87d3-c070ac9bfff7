package com.sinoyd.lims.api.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.api.dto.customer.DtoSampleParamsApiPhone;
import com.sinoyd.lims.api.service.MobileFolderService;
import com.sinoyd.lims.lim.dto.customer.DtoDocAuthorityValidate;
import com.sinoyd.lims.lim.dto.lims.DtoFolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 移动端文件管理服务
 *
 * <AUTHOR>
 * @version V1.0.0 2023/10/30
 * @since V100R001
 */
@Api(tags = "示例: MobileFolder服务")
@RestController
@RequestMapping("api/field/folder")
public class MobileFolderController extends BaseJpaController<DtoFolder, String, MobileFolderService> {

    /**
     * 获取文件夹下所有文件
     *
     * @param folderId 文件夹id
     * @return RestResponse<List<Object>>
     */
    @ApiOperation(value = "获取文件夹下所有文件", notes = "获取文件夹下所有文件")
    @GetMapping("/findFile")
    public RestResponse<List<Object>> findFileAndFolder(@RequestParam("folderId") String folderId, @RequestParam("key") String key) {
        RestResponse<List<Object>> response = new RestResponse<>();
        response.setData(service.findFileAndFolder(folderId, key));
        return response;
    }

    /**
     * 权限验证
     *
     * @return 返回是否具有权限
     */
    @ApiOperation(value = "权限验证", notes = "权限验证")
    @PostMapping("/validateAuth")
    public RestResponse<Boolean> validateAuth(@RequestBody DtoDocAuthorityValidate dtoDocAuthorityValidate) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setData(service.validateAuth(dtoDocAuthorityValidate));
        return restResponse;
    }

    /**
     * 删除文件
     * @param sampleParamsPhone 文件id集合
     * @return RestResponse<Void>
     */
    @ApiOperation(value = "删除文件", notes = "删除文件")
    @DeleteMapping
    public RestResponse<Void> delete(@RequestBody DtoSampleParamsApiPhone sampleParamsPhone) {
        RestResponse<Void> response = new RestResponse<>();
        service.delete(sampleParamsPhone.getFileIds());
        return response;
    }

    /**
     * 更新文件备注
     * @param map 参数
     * @return RestResponse<Void>
     */
    @ApiOperation(value = "更新文件备注", notes = "更新文件备注")
    @PostMapping(path = "/updateRemark")
    public RestResponse<Void> updateRemark(@RequestBody Map<String, Object> map) {
        RestResponse<Void> response = new RestResponse<>();
        service.updateRemark(map);
        return response;
    }

}
