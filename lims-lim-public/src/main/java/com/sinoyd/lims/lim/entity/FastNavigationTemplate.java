package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * FastNavigationTemplate实体
 * <AUTHOR>
 * @version V1.0.0 2020/3/27
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="FastNavigationTemplate")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class FastNavigationTemplate implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  FastNavigationTemplate() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 用户id（GUID_EMPTY表示系统默认）
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("用户id（GUID_EMPTY表示系统默认）")
	private String userId;
    
    /**
    * 快速导航模块id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("快速导航模块id")
	private String fastNavId;
    
    /**
    * 导航模块名称
    */
    @Column(length=50)
    @ApiModelProperty("导航模块名称")
    @Length(message = "导航模块名称{validation.message.length}", max = 100)
    private String navName;
    
    /**
    * 导航模块地址
    */
    @Column(length=50)
    @ApiModelProperty("导航模块地址")
    @Length(message = "导航模块地址{validation.message.length}", max = 100)
    private String navUrl;
    
    /**
    * 导航模块图标地址
    */
    @Column(length=50)
    @ApiModelProperty("导航模块图标地址")
    @Length(message = "导航模块图标地址{validation.message.length}", max = 100)
    private String navIcon;
    
    /**
    * 排序值
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("排序值")
    private Integer orderNum= 0;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }