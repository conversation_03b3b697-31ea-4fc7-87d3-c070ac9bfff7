package com.sinoyd.lims.pro.dto;

import com.sinoyd.base.dto.lims.DtoConsumable;
import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.lims.pro.entity.Project2Inspect;
import com.sinoyd.lims.pro.entity.Project2Report;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


/**
 * toProject2Inspect 实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/2/8
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_Project2Inspect")
@Data
@DynamicInsert
public class DtoProject2Inspect extends Project2Inspect {

}