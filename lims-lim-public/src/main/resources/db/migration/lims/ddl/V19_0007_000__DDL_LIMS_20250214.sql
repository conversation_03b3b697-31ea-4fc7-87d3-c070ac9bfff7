ALTER TABLE TB_QA_YearlyManagementReviewPlan  add column detailId  varchar(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '计划标识';
ALTER TABLE TB_QA_YearlyQualityControlPlan  add column deptId  varchar(50)  COMMENT '责任部门标识';
ALTER TABLE TB_QA_YearlyInnerAuditPlan  change chargePersonId personId  varchar(50)  COMMENT '责任人标识';
ALTER TABLE TB_QA_YearlyInnerAuditPlan  add column deptId  varchar(50)  COMMENT '责任部门标识';
ALTER TABLE TB_QA_YearlyManagementReviewPlan  change chargePersonId personId  varchar(50)  COMMENT '责任人标识';
ALTER TABLE TB_QA_YearlyManagementReviewPlan  add column deptId  varchar(50)  COMMENT '责任部门标识';
ALTER TABLE TB_QA_YearlyInnerAuditPlan  add column planType int(10) NOT NULL DEFAULT 0 COMMENT '计划状态,0计划内/1计划外';
ALTER TABLE TB_QA_YearlyManagementReviewPlan  add column planType int(10)  NOT NULL DEFAULT 0 COMMENT '计划状态,0计划内/1计划外';
