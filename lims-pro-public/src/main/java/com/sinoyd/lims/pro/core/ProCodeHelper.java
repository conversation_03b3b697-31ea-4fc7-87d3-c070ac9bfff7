package com.sinoyd.lims.pro.core;

/**
 * Pro常量
 * <p>
 * 创建：徐竹
 * <p>
 * 创建时间：2018年12月12日10:34:00
 * <p>
 * 在接口内定义常量，避免初始化
 * 
 * @version 1.0
 */
public interface ProCodeHelper {
    /**
     * 是否需要先拆分领样单
     */
    Boolean FIRST_SUB_RECORD = true;

    /**
     * redis过期小时数
     */
    Long EXPIRE_HOUR = 1L;

    /**
     * 费用审批权限
     */
    String COST_APPROVE_AUTH = "pro_costInfoApprove_show";

    /**
     * 现场数据审核权限
     */
    String LOCAL_AUDIT_AUTH = "pro_localDataAudit_show";

    /**
     * 报告签发权限
     */
    String REPORT_SIGN_AUTH = "pro_reportSign_show";

    /**
     * 分析数据权限编码
     */
    String PRO_AnalyseData_Show = "LIMS_PRO_AnalyseData_Show";

    /**
     * 分析数据审核权限编码
     */
    String PRO_AnalyseData_Audit = "LIMS_PRO_AnalyseData_Audit";

    /**
     * 通用舍入位数
     */
    Integer COMMON_ROUNDING_MODE = 7;

    /**
     * 报告管理权限
     */
    String REPORT_ALL_AUTH = "pro_report_all";

    /**
     * 监测方式
     */
    String MONITOR_METHODS = "PRO_MonitorMethods";

    /**
     * 保存条件
     */
    String SAVE_CONDITION = "PRO_SaveCondition";

    /**
     * 监测类型
     */
    String SAMPLE_TYPE = "PRO_SampleType";

    /**
     * 交付方式
     */
    String POST_METHOD = "PRO_PostMethod";

    /**
     * 报告出具方式
     */
    String REPORT_METHOD = "PRO_ReportMethod";

    /**
     * 样品交接开关
     */
    String SKIP_SAMPLE_RECEIVE = "IsSkipSampleReceive";

    /**
     * 是否人脸识别
     */
    String FACE_RECOGNITION = "IsFaceRecognition";

    /**
     * 是否显示现场质控
     */
    String PARALLEL_SCENE = "IsSceneParallel";

    /**
     * 添加加标样是否弹出页面
     */
    String LIM_JB_EDITABLE = "LIM_JB_EDITABLE";

    /**
     * 首页检测超期天数
     */
    String ANALYSE_OVERDUE_DAYS = "AnalyseOverdueDays";

    /**
     * 是否自动流转到样品交接开关
     * <p>0: 现场任务和样品交接严格按顺序流转
     * 1: 现场任务和样品交接不卡顺序，创建样品编号后就自动流转到现场任务和样品交接
     * </p>
     */
    String LIM_FLOW_RECEIVE_SAMPLE = "LIM_FLOW_RECEIVE_SAMPLE";

    /**
     * 按岗位分配开关开启时，跳过样品分配
     */
    String PRO_AnalyseAllocationRules_Post = "PRO_AnalyseAllocationRules_Post";

    /**
     * 创建检测单时
     * 是否检查领样日期设置
     */
    String IsCheck_SampleReceiveDate = "IsCheck_SampleReceiveDate";

    /**
     * 项目进度
     * 是否显示数据修改标识
     */
    String PRO_IsShowChangeLog = "PRO_IsShowChangeLog";
}