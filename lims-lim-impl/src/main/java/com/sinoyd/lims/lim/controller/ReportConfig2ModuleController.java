package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.dto.rcc.DtoReportConfig2Module;
import com.sinoyd.lims.lim.dto.rcc.DtoReportModule;
import com.sinoyd.lims.lim.service.ReportConfig2ModuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * ReportConfig2Module服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2020/10/14
 * @since V100R001
 */
@Api(tags = "示例: ReportConfig2Module服务")
@RestController
@RequestMapping("api/lim/reportConfig2Module")
@Validated
public class ReportConfig2ModuleController extends BaseJpaController<DtoReportConfig2Module, String, ReportConfig2ModuleService> {


    /**
     * 根据recordConfigId查询
     *
     * @param recordConfigId 报告配置id
     * @return RestResponse<DtoReportConfig2Module>
     */
    @ApiOperation(value = "根据recordConfigId查询", notes = "根据recordConfigId查询")
    @GetMapping(path = "/{recordConfigId}")
    public RestResponse<List<DtoReportConfig2Module>> find(@PathVariable(name = "recordConfigId") String recordConfigId) {
        RestResponse<List<DtoReportConfig2Module>> restResponse = new RestResponse<>();
        List<DtoReportConfig2Module> reportConfig2Module = service.queryByRecordConfigId(recordConfigId);
        restResponse.setData(reportConfig2Module);
        restResponse.setRestStatus(StringUtil.isNull(reportConfig2Module) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 按主键查询组件信息及关联的分页配置信息
     *
     * @param id 主键id
     * @return RestResponse<DtoReportModule>
     */
    @ApiOperation(value = "按主键查询reportModule", notes = "按主键查询按主键查询reportModule")
    @GetMapping(path = "/moduleInfo/{id}")
    public RestResponse<DtoReportModule> findModuleInfo(@PathVariable(name = "id") String id) {
        RestResponse<DtoReportModule> restResponse = new RestResponse<>();
        DtoReportModule reportModule = service.findModuleInfo(id);
        restResponse.setData(reportModule);
        restResponse.setRestStatus(StringUtil.isNull(reportModule) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }


    /**
     * 新增ReportConfig2Module
     *
     * @param reportConfig2Module 实体列表
     * @return RestResponse<DtoReportConfig2Module>
     */
    @ApiOperation(value = "新增ReportConfig2Module", notes = "新增ReportConfig2Module")
    @PostMapping
    public RestResponse<DtoReportConfig2Module> create(@Validated @RequestBody DtoReportConfig2Module reportConfig2Module) {
        RestResponse<DtoReportConfig2Module> restResponse = new RestResponse<>();
        restResponse.setData(service.save(reportConfig2Module));
        return restResponse;
    }

    /**
     * 修改ReportConfig2Module
     *
     * @param ReportConfig2Module 实体列表
     * @return RestResponse<DtoReportConfig2Module>
     */
    @ApiOperation(value = "修改ReportConfig2Module", notes = "修改ReportConfig2Module")
    @PutMapping
    public RestResponse<DtoReportConfig2Module> update(@Validated @RequestBody DtoReportConfig2Module ReportConfig2Module) {
        RestResponse<DtoReportConfig2Module> restResponse = new RestResponse<>();
        restResponse.setData(service.update(ReportConfig2Module));
        return restResponse;
    }

    /**
     * "根据id批量删除ReportConfig2Module
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除ReportConfig2Module", notes = "根据id批量删除ReportConfig2Module")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        int count = service.deleteConfig2Module(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * "根据id批量删除报告配置
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除报告配置", notes = "根据id批量删除报告配置")
    @DeleteMapping("/recordConfig")
    public RestResponse<String> deleteRecordConfigForReport(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        int count = service.deleteRecordConfigForReport(ids);
        restResp.setCount(count);
        return restResp;
    }
}