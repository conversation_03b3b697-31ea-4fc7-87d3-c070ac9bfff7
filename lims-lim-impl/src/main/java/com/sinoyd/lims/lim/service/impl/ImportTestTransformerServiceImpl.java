package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.dto.rcc.DtoSubstitute;
import com.sinoyd.base.repository.rcc.AnalyzeItemRepository;
import com.sinoyd.base.repository.rcc.DimensionRepository;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.repository.rcc.SubstituteRepository;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.customer.*;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.dto.rcc.DtoParams;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.AnalyzeMethodRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsRepository;
import com.sinoyd.lims.lim.service.ImportTestTransformerService;
import com.sinoyd.lims.lim.service.transform.TransformImportStrategy;
import com.sinoyd.lims.lim.utils.ImportUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 测试项目迁移导入
 *
 * <AUTHOR>
 * @version V1.0.0 2022/9/7
 * @since V100R001
 */
@Service
@Slf4j
public class ImportTestTransformerServiceImpl implements ImportTestTransformerService {

    private WebSocketServer webSocketServer;
    private ImportUtils importUtils;
    private RedisTemplate redisTemplate;
    private AnalyzeItemRepository analyzeItemRepository;
    private AnalyzeMethodRepository analyzeMethodRepository;
    private DimensionRepository dimensionRepository;
    private ParamsRepository paramsRepository;
    private SampleTypeRepository sampleTypeRepository;
    private SubstituteRepository substituteRepository;





    /**
     * 实现类TransformImportStrategy策略接口的所有实现类
     */
    private List<TransformImportStrategy> strategyList;

    @Override
    @Transactional
    public void importExcel(MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        params.setNeedVerify(true);
        for (TransformImportStrategy s : strategyList) {
            s.importSheetData(file.getInputStream(), response, params);
        }
    }


    @Override
    public DtoDataSyncParams getBaseData(MultipartFile file, HttpServletResponse response) {
        // 调用文件转实体方法，获取校验格式后的数据源
        DtoTestDependentData dependentData = this.multiSheetFileToEntity(file, DtoTestDependentData.class, response);
        // 筛选测试项目、测试项目拓展，测试项目公式中的检测类型
        List<String> sampleTypeList = dependentData.getTestList().stream().map(DtoExportTest::getSampleTypeId).distinct().collect(Collectors.toList());
        sampleTypeList.addAll(dependentData.getTestExpandList().stream().map(DtoExportTestExpand::getSampleTypeId).distinct().collect(Collectors.toList()));
        sampleTypeList.addAll(dependentData.getParamsFormulaList().stream().map(DtoExportParamsFormula::getSampleTypeId).distinct().collect(Collectors.toList()));
        // 质控限值中的替代物数据
        List<String> substituteNames = dependentData.getQualityControlLimitList().stream().map(DtoExportQualityControlLimit::getSubstituteName).distinct().collect(Collectors.toList());
        DtoDataSyncParams dtoDataSyncParams = new DtoDataSyncParams();
        List<DtoSampleType> sampleTypes = sampleTypeRepository.getList();
        Map<String, DtoSampleType> sampleTypeMap = sampleTypes.stream().collect(Collectors.toMap(DtoSampleType::getTypeName, p -> p, (p1, p2) -> p1));
        List<DtoSubstitute> substitutes = substituteRepository.findAll();
        Map<String, DtoSubstitute> substituteMap = substitutes.stream().collect(Collectors.toMap(DtoSubstitute::getCompoundName, p -> p, (p1, p2) -> p1));
        List<DtoBaseData> sampleTypeBinds = sampleTypeList.stream()
                .filter(Objects::nonNull).distinct()
                .map(p -> {
                    DtoBaseData sampleTypeData = new DtoBaseData();
                    sampleTypeData.setSourceName(p);
                    // 匹配系统中存在的检测类型，
                    DtoSampleType sampleType = sampleTypeMap.get(p);
                    if (StringUtil.isNotNull(sampleType)){
                        sampleTypeData.setTargetName(sampleType.getTypeName());
                        sampleTypeData.setTargetId(sampleType.getId());
                    }
                    return sampleTypeData;
                }).collect(Collectors.toList());
        List<DtoBaseData> substituteBinds = substituteNames.stream()
                .filter(Objects::nonNull).distinct()
                .map(p -> {
                    DtoBaseData substituteNameData = new DtoBaseData();
                    substituteNameData.setSourceName(p);
                    // 匹配系统中存在的替代物数据
                    DtoSubstitute substitute = substituteMap.get(p);
                    if (StringUtil.isNotNull(substitute)){
                        substituteNameData.setTargetName(substitute.getCompoundName());
                        substituteNameData.setTargetId(substitute.getId());
                    }
                    return substituteNameData;
                }).collect(Collectors.toList());
        dtoDataSyncParams.setSampleTypeBinds(sampleTypeBinds);
        dtoDataSyncParams.setSubstituteBinds(substituteBinds);
        String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_TestMigrateData.getValue());
        // 拼接毫秒时间设置唯一key
        key = key + new Date().getTime();
        // 缓存导入的数据
        cacheRedisByField(key, dependentData);
        // 讲缓存的key放入参数进行前后端传输，后续直接用key从redis获取数据
        dtoDataSyncParams.setDataSourceRedisKey(key);

        return dtoDataSyncParams;
    }


    @Override
    public DtoDataSyncParams getCheckData(DtoDataSyncParams dtoDataSyncParams, HttpServletResponse response) {
        // 获取导入数据源
        DtoTestDependentData testDependentData = getImportDataByRedis(dtoDataSyncParams.getDataSourceRedisKey(), new DtoTestDependentData());
        List<DtoImportCheck> importChecks = new ArrayList<>();
        if (StringUtil.isNotNull(testDependentData)) {
            // 导入数据检查
            for (TransformImportStrategy strategy : strategyList) {
                List<DtoImportCheck> importCheckList = strategy.check(dtoDataSyncParams, testDependentData);
                if (StringUtil.isNotEmpty(importCheckList)){
                    importChecks.addAll(importCheckList);
                }
            }
        }
        dtoDataSyncParams.setImportChecks(importChecks);
        // 初始化导入实体
        DtoImportTestTemp importTestTemp = new DtoImportTestTemp();
        // 检测类型绑定数据
        List<DtoBaseData> sampleTypeBinds = dtoDataSyncParams.getSampleTypeBinds();
        Map<String, DtoBaseData> sampleTypeBindMap = sampleTypeBinds.stream().collect(Collectors.toMap(DtoBaseData::getSourceName, p -> p));
        // 替代物绑定数据
        List<DtoBaseData> substituteBinds = dtoDataSyncParams.getSubstituteBinds();
        Map<String, DtoBaseData> substituteMap = substituteBinds.stream().collect(Collectors.toMap(DtoBaseData::getSourceName, p -> p));

        // 定义需要导出的容器，导出未导入的测试项目及依赖数据
        DtoTestDependentData exportData = new DtoTestDependentData();
        // 获取待导入数据
        List<TransformImportStrategy> strategyListByAddData = strategyList.stream().sorted(Comparator.comparing(TransformImportStrategy::getAddDataOrderNum)).collect(Collectors.toList());
        for (TransformImportStrategy strategy : strategyListByAddData) {
            strategy.getAddData(substituteMap, sampleTypeBindMap, dtoDataSyncParams, testDependentData, importTestTemp, exportData);
        }
        String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_TestMigrateData.getValue());
        // 拼接毫秒时间设置唯一key
        key = key + new Date().getTime();
        // 缓存导入的数据
        cacheRedisByField(key, importTestTemp);
        // 缓存的key放入参数进行前后端传输，后续直接用key从redis获取数据
        dtoDataSyncParams.setImportDataTempRedisKey(key);
        // 判断测试项目未导入数据是否为空，暂时去掉导出数据
//        if (isAllListNotEmpty(exportData)) {
//            try {
//                Workbook workbook = importUtils.multiSheetWorkbook(exportData);
//                PoiExcelUtils.downLoadExcel("测试项目未导入数据", response, workbook);
//            } catch (Exception ex) {
//                log.error(ex.getMessage(), ex);
//                throw new BaseException("导入信息中有数据不正确，确认后导入");
//            }
//        } else {
//
//        }
        return dtoDataSyncParams;
    }

    @Override
    public List<Map<String, String>> getDataTable() {
        List<Map<String, String>> list = new ArrayList<>();
        for (TransformImportStrategy strategy : strategyList) {
            Map<String, String> map = new HashMap<>();
            map.put(strategy.getTableName(), strategy.getTableRemark());
            list.add(map);
        }
        return list;
    }

    /**
     * 确认导入
     *
     * @param dtoDataSyncParams 同步参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importData(DtoDataSyncParams dtoDataSyncParams) {
        // 通过redis获取导入数据源
        DtoImportTestTemp importTestTemp = getImportDataByRedis(dtoDataSyncParams.getImportDataTempRedisKey(), new DtoImportTestTemp());
        // 数据导入
        webSocketServer.sendMessage("开始数据导入..");
        for (TransformImportStrategy strategy : strategyList) {
            strategy.importData(importTestTemp, webSocketServer);
        }
        webSocketServer.sendMessage("导入数据完成...");
    }

//    /**
//     * 检查数据赋值
//     *
//     * @param dtoDataSyncParams 导入前检查
//     * @param testDependentData 导入数据
//     */
//    private void check(DtoDataSyncParams dtoDataSyncParams, DtoTestDependentData testDependentData) {
//        List<DtoExportAnalyzeItem> analyzeItemList = testDependentData.getAnalyzeItemList();
//        List<DtoExportAnalyzeMethod> analyzeMethodList = testDependentData.getAnalyzeMethodList();
//        List<DtoExportDimension> dimensionList = testDependentData.getDimensionList();
//        List<DtoExportParams> paramsList = testDependentData.getParamsList();
//        // 分析项目比较结果赋值
//        List<DtoDataCheck> analyzeItemCheckList = checkData(analyzeItemList,
//                analyzeItemRepository,
//                DtoExportAnalyzeItem::getId,
//                DtoExportAnalyzeItem::getAnalyzeItemName,
//                DtoAnalyzeItem::getAnalyzeItemName,
//                DtoAnalyzeItem::getId);
//        dtoDataSyncParams.setAnalyzeItemCheck(analyzeItemCheckList);
//        // 分析方法比较结果赋值
//        List<DtoDataCheck> analyzeMethodCheckList = checkData(analyzeMethodList,
//                analyzeMethodRepository,
//                DtoExportAnalyzeMethod::getId,
//                mergeFunction(DtoExportAnalyzeMethod::getMethodName, DtoExportAnalyzeMethod::getCountryStandard),
//                mergeFunction(DtoAnalyzeMethod::getMethodName, DtoAnalyzeMethod::getCountryStandard),
//                DtoAnalyzeMethod::getId);
//        dtoDataSyncParams.setAnalyzeMethodCheck(analyzeMethodCheckList);
//        // 量纲比较结果赋值
//        List<DtoDataCheck> dimensionCheckList = checkData(dimensionList,
//                dimensionRepository,
//                DtoExportDimension::getId,
//                DtoExportDimension::getDimensionName,
//                DtoDimension::getDimensionName,
//                DtoDimension::getId);
//        dtoDataSyncParams.setDimensionCheck(dimensionCheckList);
//        // 参数比较结果赋值
//        List<DtoDataCheck> paramsCheck = checkData(paramsList,
//                paramsRepository,
//                DtoExportParams::getId,
//                DtoExportParams::getParamName,
//                DtoParams::getParamName,
//                DtoParams::getId);
//        dtoDataSyncParams.setParamsCheck(paramsCheck);
//    }

    /**
     * 数据检查
     *
     * @param dataSource 数据源待检查数据
     * @param repository 数据访问接口
     * @param sourceId   数据源中的id函数式接口
     * @param sourceName 数据源中的name函数式接口
     * @param nameGetter 比较数据中的name函数式接口
     * @param idGetter   比较数据中的id函数式接口
     * @param <T>        比较数据类型
     * @param <V>        数据源类型
     * @param <K>        id类型
     * @return 检查后数据集合
     */
    private <T, V, K> List<DtoDataCheck> checkData(Collection<V> dataSource,
                                                   JpaRepository<T, String> repository,
                                                   Function<V, K> sourceId,
                                                   Function<V, String> sourceName,
                                                   Function<T, String> nameGetter,
                                                   Function<T, K> idGetter) {
        List<DtoDataCheck> dtoDataChecks = new ArrayList<>();
//        if (StringUtil.isNotEmpty(dataSource)) {
//            List<T> dataList = repository.findAll();
//            dtoDataChecks = dataSource.parallelStream()
//                    .map(p -> {
//                        T data = dataList.stream().filter(r -> nameGetter.apply(r).equals(sourceName.apply(p))).findFirst().orElse(null);
//                        DtoDataCheck dtoDataCheck = new DtoDataCheck();
//                        dtoDataCheck.setName(sourceName.apply(p));
//                        if (StringUtil.isNotNull(data)) {
//                            dtoDataCheck.setType(BASE_DATA_TYPE[1]);
//                            dtoDataCheck.setId((String) idGetter.apply(data));
//                        } else {
//                            dtoDataCheck.setType(BASE_DATA_TYPE[0]);
//                            dtoDataCheck.setId((String) sourceId.apply(p));
//                        }
//                        return dtoDataCheck;
//                    }).collect(Collectors.toList());
//        }
        return dtoDataChecks;
    }


    private <T> Function<T, String> mergeFunction(Function<T, String> function1, Function<T, String> function2) {
        return dto -> function1.apply(dto) + function2.apply(dto);
    }

    /**
     * 根据实体类字段缓存到redis
     *
     * @param redisKey redisKey
     * @param obj      数据
     */
    private void cacheRedisByField(String redisKey, Object obj) {
        Gson gosn = new Gson();
        Field[] declaredFields = obj.getClass().getDeclaredFields();
        for (Field field : declaredFields) {
            field.setAccessible(true);
            try {
                String json = gosn.toJson(field.get(obj));
                // key 拼接字段名称
                redisTemplate.opsForValue().set(redisKey + ":" + field.getName(), json, 5, TimeUnit.HOURS);
            } catch (Exception e) {
                log.error(e.getMessage());
                throw new BaseException(e.getMessage());
            }
        }
    }

    /**
     * 根据T获取redis中的导入数据
     *
     * @param redisKey redisKey
     * @param t        泛型
     * @param <T>
     * @return T
     */
    private <T> T getImportDataByRedis(String redisKey, T t) {
        Gson gson = new Gson();
        Field[] declaredFields = t.getClass().getDeclaredFields();
        for (Field field : declaredFields) {
            field.setAccessible(true);
            Type fc = field.getGenericType();
            if (fc instanceof ParameterizedType) {
                ParameterizedType pt = (ParameterizedType) fc;
                // 得到泛型里的class类型对象。
                try {
                    String cacheValue = (String) redisTemplate.opsForValue().get(redisKey + ":" + field.getName());
                    if (StringUtil.isNotEmpty(cacheValue)) {
                        if (List.class.isAssignableFrom(field.getType())) {
                            Type listType = TypeToken.getParameterized(List.class, pt.getActualTypeArguments()).getType();
                            List<Object> deserializedList = gson.fromJson(cacheValue, listType);
                            field.set(t, deserializedList);
                        }
                    }
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
        }
        return t;
    }

    /**
     * 多sheet页file转实体
     *
     * @param file   文件
     * @param tClass class
     * @return
     */
    public <T> T multiSheetFileToEntity(MultipartFile file, Class<T> tClass, HttpServletResponse response) {
        T entity;
        try {
            // 获取工作簿
            Workbook workBook = PoiExcelUtils.getWorkBook(file);
            // 定义校验错误的workbook
            Workbook errorWorkBook = new HSSFWorkbook();

            //初始化实体
            entity = tClass.getDeclaredConstructor().newInstance();
            // 获取到全部属性
            Field[] declaredFields = tClass.getDeclaredFields();
            // 循环属性，每一个属性对应一个实体
            for (Field field : declaredFields) {
                field.setAccessible(true);
                // sheetName
                String sheetName = "";
                // 获取list<?>中?的泛型或者Set<?>
                if (field.getType().isAssignableFrom(List.class) || field.getType().isAssignableFrom(Set.class)) {
                    // 如果是List类型，得到其Generic的类型
                    Type fc = field.getGenericType();
                    if (fc instanceof ParameterizedType) {
                        ParameterizedType pt = (ParameterizedType) fc;
                        // 得到泛型里的class类型对象。
                        Class<?> genericClazz = (Class<?>) pt.getActualTypeArguments()[0];
                        // 获取内部class 类中的注解
                        Annotation[] annotations = genericClazz.getDeclaredAnnotations();
                        for (Annotation annotation : annotations) {
                            if (annotation.annotationType() == javax.persistence.Table.class) {
                                javax.persistence.Table tableAnnotation = (javax.persistence.Table) annotation;
                                // 获取注解的 name 属性
                                sheetName = tableAnnotation.name();
                            }
                        }
                        Sheet sheet = workBook.getSheet(sheetName);
                        int sheetIndex = workBook.getSheetIndex(sheet);
                        // 定义校验器
                        ImportParams params = new ImportParams();
                        params.setTitleRows(0);
                        params.setHeadRows(1);
                        params.setStartSheetIndex(sheetIndex);
                        params.setNeedVerify(true);
                        params.setVerifyHandler(obj -> {
                            try {
                                if (importUtils.checkObjectIsNull(obj)) {
                                    return new ExcelVerifyHandlerResult(true);
                                }
                                importUtils.strToTrim(obj);
                            } catch (Exception e) {
                                log.error(e.getMessage());
                                throw new BaseException(e.getMessage());
                            }
                            return new ExcelVerifyHandlerResult(true);
                        });
                        ExcelImportResult<?> excelData = ExcelImportUtil.importExcelMore(file.getInputStream(), genericClazz, params);
                        if (excelData.isVerfiyFail()) {
                            Workbook failWorkbook = excelData.getFailWorkbook();
                            Sheet sourceSheet = failWorkbook.getSheetAt(sheetIndex);
                            Sheet targetSheet = errorWorkBook.createSheet(sourceSheet.getSheetName());
                            copySheet(sourceSheet, targetSheet);
                        } else {
                            // 根据字段类型设置值
                            List<?> list = excelData.getList();
                            // 去除空行
                            removeNullRowById(list);
                            field.set(entity, list);
                        }
                    }
                }
            }
            // 导出错误信息
            if (errorWorkBook.getNumberOfSheets() > 0) {
                PoiExcelUtils.downLoadExcel("测试项目导入失败信息", response, errorWorkBook);
                throw new BaseException("导入信息中有数据不正确，确认后导入");
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BaseException(e);
        }
        return entity;
    }

    /**
     * 拷贝sheet页
     *
     * @param sourceSheet 数据源sheet
     * @param targetSheet 目标sheet
     */
    private void copySheet(Sheet sourceSheet, Sheet targetSheet) {
        int rowCount = sourceSheet.getLastRowNum();
        for (int i = 0; i <= rowCount; i++) {
            Row sourceRow = sourceSheet.getRow(i);
            Row targetRow = targetSheet.createRow(i);
            int cellCount = sourceRow.getLastCellNum();
            for (int j = 0; j < cellCount; j++) {
                Cell sourceCell = sourceRow.getCell(j);
                Cell targetCell = targetRow.createCell(j);
                targetCell.setCellValue(sourceCell.getStringCellValue());
                // 设置单元格样式
                CellStyle sourceCellStyle = sourceCell.getCellStyle();
                CellStyle targetCellStyle = targetSheet.getWorkbook().createCellStyle();
                targetCellStyle.cloneStyleFrom(sourceCellStyle);
                // 设置字体
                Font sourceFont = sourceCell.getSheet().getWorkbook().getFontAt(sourceCellStyle.getFontIndex());
                Font targetFont = targetCell.getSheet().getWorkbook().createFont();
                targetFont.setBold(sourceFont.getBold());
                targetFont.setColor(sourceFont.getColor());
                targetFont.setFontName(sourceFont.getFontName());
                targetFont.setFontHeightInPoints(sourceFont.getFontHeightInPoints());
                targetFont.setItalic(sourceFont.getItalic());
                targetFont.setStrikeout(sourceFont.getStrikeout());
                targetFont.setTypeOffset(sourceFont.getTypeOffset());
                targetFont.setUnderline(sourceFont.getUnderline());
                targetCellStyle.setFont(targetFont);
                targetCell.setCellStyle(targetCellStyle);
            }
        }
    }

    /**
     * 根据id 为空删除空行数据
     *
     * @param list 数据源
     */
    private void removeNullRowById(List<?> list) {
        list.removeIf(p -> {
            try {
                Field idField = p.getClass().getDeclaredField("id");
                idField.setAccessible(true);
                String id = (String) idField.get(p);
                return StringUtil.isEmpty(id);
            } catch (Exception e) {
                throw new BaseException(e.getMessage());
            }
        });
    }

    /**
     * DtoTestDependentData 实体内是否存在不为空的字段
     *
     * @param dependentData DtoTestDependentData 实体
     * @return 是否
     */
    private Boolean isAllListNotEmpty(DtoTestDependentData dependentData) {
        // 遍历所有字段判断判断是否为空
        for (Field field : dependentData.getClass().getDeclaredFields()) {
            if (List.class.isAssignableFrom(field.getType())) {
                try {
                    field.setAccessible(true);
                    List<?> list = (List<?>) field.get(dependentData);
                    if (StringUtil.isNotEmpty(list)) {
                        return true;
                    }
                } catch (IllegalAccessException e) {
                    log.error(e.getMessage());
                }
            }
        }
        return false;
    }


    @Autowired
    public void setStrategyList(List<TransformImportStrategy> strategyList) {
        this.strategyList = strategyList;
    }


    @Autowired
    @Lazy
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }

    @Autowired
    @Lazy
    public void setRedisTemplate(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Autowired
    @Lazy
    public void setAnalyzeItemRepository(AnalyzeItemRepository analyzeItemRepository) {
        this.analyzeItemRepository = analyzeItemRepository;
    }

    @Autowired
    @Lazy
    public void setDimensionRepository(DimensionRepository dimensionRepository) {
        this.dimensionRepository = dimensionRepository;
    }

    @Autowired
    @Lazy
    public void setAnalyzeMethodRepository(AnalyzeMethodRepository analyzeMethodRepository) {
        this.analyzeMethodRepository = analyzeMethodRepository;
    }

    @Autowired
    @Lazy
    public void setParamsRepository(ParamsRepository paramsRepository) {
        this.paramsRepository = paramsRepository;
    }

    @Autowired
    @Lazy
    public void setWebSocketServer(WebSocketServer webSocketServer) {
        this.webSocketServer = webSocketServer;
    }

    @Autowired
    @Lazy
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    @Lazy
    public void setSubstituteRepository(SubstituteRepository substituteRepository) {
        this.substituteRepository = substituteRepository;
    }
}
