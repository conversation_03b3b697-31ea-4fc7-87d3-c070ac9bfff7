package com.sinoyd.lims.lim.service.impl;

import com.google.gson.Gson;
import com.sinoyd.base.dto.rcc.*;
import com.sinoyd.base.repository.rcc.AnalyzeItemRepository;
import com.sinoyd.base.repository.rcc.DimensionRepository;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.data.mapper.*;
import com.sinoyd.lims.lim.dto.customer.*;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.lims.DtoTestExpand;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.TestExpandRepository;
import com.sinoyd.lims.lim.repository.rcc.AnalyzeMethodRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * MarkersData实现接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/7
 * @since V100R001
 */
@Component
public class MarkersDataListenerImpl {

    private DimensionRepository dimensionRepository;
    private AnalyzeItemRepository analyzeItemRepository;
    private AnalyzeMethodRepository analyzeMethodRepository;
    private SampleTypeRepository sampleTypeRepository;
    private TestExpandRepository testExpandRepository;

    private AnalyzeItemMapper analyzeItemMapper;
    private DimensionMapper dimensionMapper;
    private AnalyzeMethodMapper analyzeMethodMapper;
    private TestMapper testMapper;
    private TestExpandMapper testExpandMapper;
    private QualityControlLimitMapper qualityControlLimitMapper;

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 处理标记数据
     *
     * @param testList                测试项目
     * @param qualityControlLimitList 质控限值
     * @return
     */
    public List<DtoTestDependentData> processMarkersData(List<DtoTest> testList, List<DtoQualityControlLimit> qualityControlLimitList) {
        List<DtoTestDependentData> result = new ArrayList<>();
        if (StringUtil.isNotEmpty(testList)) {

            List<String> testIds = testList.stream().map(DtoTest::getId).collect(Collectors.toList());
            // 量纲
            List<String> dimensionIds = testList.stream().map(DtoTest::getDimensionId).distinct().collect(Collectors.toList());
            List<DtoDimension> dtoDimensions = dimensionRepository.findAll(dimensionIds);
            Map<String, DtoDimension> dimensionMap = dtoDimensions.stream().collect(Collectors.toMap(DtoDimension::getId, dto -> dto));
            // 分析方法
            List<String> analyzeMethodId = testList.stream().map(DtoTest::getAnalyzeMethodId).collect(Collectors.toList());
            List<DtoAnalyzeMethod> analyzeMethods = analyzeMethodRepository.findAll(analyzeMethodId);
            Map<String, DtoAnalyzeMethod> analyzeMethodMap = analyzeMethods.stream().collect(Collectors.toMap(DtoAnalyzeMethod::getId, dto -> dto));
            // 分析项目
            List<String> analyzeItemId = testList.stream().map(DtoTest::getAnalyzeItemId).collect(Collectors.toList());
            List<DtoAnalyzeItem> analyzeItems = analyzeItemRepository.findAll(analyzeItemId);
            Map<String, DtoAnalyzeItem> analyzeItemMap = analyzeItems.stream().collect(Collectors.toMap(DtoAnalyzeItem::getId, dto -> dto));
            //获取测试项目拓展数据
            List<DtoTestExpand> testExpands = StringUtil.isNotEmpty(testIds) ? testExpandRepository.findByTestIdIn(testIds) : new ArrayList<>();
            Map<String, List<DtoTestExpand>> groupTestExpand = testExpands.stream().collect(Collectors.groupingBy(DtoTestExpand::getTestId));
            // 检测类型
            List<String> sampleTypeIds = testList.stream().map(DtoTest::getSampleTypeId).distinct().collect(Collectors.toList());
            sampleTypeIds.addAll(testExpands.stream().map(DtoTestExpand::getSampleTypeId).distinct().collect(Collectors.toList()));
            List<DtoSampleType> sampleTypeList = StringUtil.isNotEmpty(sampleTypeIds) ? sampleTypeRepository.findAll(sampleTypeIds) : new ArrayList<>();
            Map<String, String> sampleTypeMap = sampleTypeList.stream().collect(Collectors.toMap(DtoSampleType::getId, DtoSampleType::getTypeName));
            // 质控限值
            Map<String, List<DtoQualityControlLimit>> qualityControlLimitMap = qualityControlLimitList.stream().collect(Collectors.groupingBy(DtoQualityControlLimit::getTestId));
            // 测试项目依赖数据 redis key
            String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_TestDependentData.getValue());
            // 依赖数据缓存到redis
            for (DtoTest test : testList) {
                DtoTestDependentData testDependentData = new DtoTestDependentData();
                // 初始化需要转化的数据
                DtoExportDimension dtoExportDimension = new DtoExportDimension();
                DtoExportAnalyzeItem dtoExportAnalyzeItem = new DtoExportAnalyzeItem();
                DtoExportAnalyzeMethod dtoExportAnalyzeMethod = new DtoExportAnalyzeMethod();

                if (StringUtil.isNotNull(dimensionMap.get(test.getDimensionId()))) {
                    dtoExportDimension = dimensionMapper.toExportDimension(dimensionMap.get(test.getDimensionId()));
                }
                if (StringUtil.isNotNull(analyzeItemMap.get(test.getAnalyzeItemId()))) {
                    dtoExportAnalyzeItem = analyzeItemMapper.toExportAnalyzeItem(analyzeItemMap.get(test.getAnalyzeItemId()));
                }
                if (StringUtil.isNotNull(analyzeMethodMap.get(test.getAnalyzeMethodId()))) {
                    dtoExportAnalyzeMethod = analyzeMethodMapper.toExportAnalyzeMethod(analyzeMethodMap.get(test.getAnalyzeMethodId()));
                }
                testDependentData.setDimensionList(Collections.singletonList(dtoExportDimension));
                testDependentData.setAnalyzeItemList(Collections.singletonList(dtoExportAnalyzeItem));
                testDependentData.setAnalyzeMethodList(Collections.singletonList(dtoExportAnalyzeMethod));
                List<DtoExportTestExpand> testExpandList = new ArrayList<>();
                List<DtoExportQualityControlLimit> qualityControlLimits = new ArrayList<>();

                if (StringUtil.isNotNull(groupTestExpand) && StringUtil.isNotEmpty(groupTestExpand.get(test.getId()))) {
                    groupTestExpand.get(test.getId()).forEach(p -> {
                        DtoExportTestExpand testExpand = testExpandMapper.toExportTestExpand(p);
                        // 测试项目拓展 量纲、检测类型转换成中文,为后续导入时使用
                        testExpand.setDimensionId(dimensionMap.getOrDefault(testExpand.getDimensionId(), new DtoDimension()).getDimensionName());
                        testExpand.setSampleTypeId(sampleTypeMap.getOrDefault(testExpand.getSampleTypeId(), ""));
                        testExpandList.add(testExpand);
                    });
                }

                if (StringUtil.isNotNull(qualityControlLimitMap) && StringUtil.isNotEmpty(qualityControlLimitMap.get(test.getId()))) {
                    qualityControlLimits = qualityControlLimitMapper.toExportQualityControlLimitList(qualityControlLimitMap.get(test.getId()));
                }
                testDependentData.setTestExpandList(testExpandList);
                testDependentData.setQualityControlLimitList(qualityControlLimits);
                DtoExportTest dtoExportTest = testMapper.toExportTest(test);
                dtoExportTest.processSpecial(test);
                // 测试项目中 量纲、检测类型转换成中文,为后续导入时使用
                dtoExportTest.setDimensionId(dimensionMap.getOrDefault(dtoExportTest.getDimensionId(), new DtoDimension()).getDimensionName());
                dtoExportTest.setSampleTypeId(sampleTypeMap.getOrDefault(dtoExportTest.getSampleTypeId(), ""));

                // TODO：处理id转换成名称，比如量纲
                testDependentData.setTestList(Collections.singletonList(dtoExportTest));
                //String serialize = JsonStream.serialize(testDependentData);
                Gson gosn = new Gson();
                String json = gosn.toJson(testDependentData);
                redisTemplate.opsForHash().put(key, test.getId(), json);
                result.add(testDependentData);
            }
        }
        return result;
    }

    @Autowired
    @Lazy
    public void setDimensionRepository(DimensionRepository dimensionRepository) {
        this.dimensionRepository = dimensionRepository;
    }

    @Autowired
    @Lazy
    public void setAnalyzeItemRepository(AnalyzeItemRepository analyzeItemRepository) {
        this.analyzeItemRepository = analyzeItemRepository;
    }

    @Autowired
    @Lazy
    public void setAnalyzeMethodRepository(AnalyzeMethodRepository analyzeMethodRepository) {
        this.analyzeMethodRepository = analyzeMethodRepository;
    }

    @Autowired
    @Lazy
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    @Lazy
    public void setTestExpandRepository(TestExpandRepository testExpandRepository) {
        this.testExpandRepository = testExpandRepository;
    }

    @Autowired
    @Lazy
    public void setAnalyzeItemMapper(AnalyzeItemMapper analyzeItemMapper) {
        this.analyzeItemMapper = analyzeItemMapper;
    }

    @Autowired
    @Lazy
    public void setAnalyzeMethodMapper(AnalyzeMethodMapper analyzeMethodMapper) {
        this.analyzeMethodMapper = analyzeMethodMapper;
    }

    @Autowired
    @Lazy
    public void setDimensionMapper(DimensionMapper dimensionMapper) {
        this.dimensionMapper = dimensionMapper;
    }

    @Autowired
    @Lazy
    public void setQualityControlLimitMapper(QualityControlLimitMapper qualityControlLimitMapper) {
        this.qualityControlLimitMapper = qualityControlLimitMapper;
    }

    @Autowired
    @Lazy
    public void setTestExpandMapper(TestExpandMapper testExpandMapper) {
        this.testExpandMapper = testExpandMapper;
    }

    @Autowired
    @Lazy
    public void setTestMapper(TestMapper testMapper) {
        this.testMapper = testMapper;
    }
}
