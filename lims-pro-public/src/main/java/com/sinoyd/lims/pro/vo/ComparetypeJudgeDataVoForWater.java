package com.sinoyd.lims.pro.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import lombok.Data;

import java.util.List;

@Data
public class ComparetypeJudgeDataVoForWater {

    @Excel(name = "比对方式",needMerge = true,orderNum = "40",width = 23)
    private String compareType;

    @ExcelCollection(name = "", orderNum = "45")
    private List<JudgeDataVoForWater> dataVos;

}
