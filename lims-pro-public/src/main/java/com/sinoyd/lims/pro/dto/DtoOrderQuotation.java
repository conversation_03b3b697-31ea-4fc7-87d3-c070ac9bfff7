package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.OrderQuotation;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoOrderQuotation实体
 * <AUTHOR>
 * @version V1.0.0 2022/5/18
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_OrderQuotation") 
 @Where(clause = "isDeleted = 0")
 @Data
 @DynamicInsert
 public  class DtoOrderQuotation extends OrderQuotation {
   private static final long serialVersionUID = 1L;

   @Transient
   private Boolean isOther = false;

   @Transient
   private Boolean isQuotation = false;

    /**
     * 最终折扣率，用作前端展示
     */
    @Transient
    private String finalDiscount;
 }