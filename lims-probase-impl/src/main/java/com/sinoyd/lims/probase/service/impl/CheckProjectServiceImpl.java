package com.sinoyd.lims.probase.service.impl;

import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.base.service.WorkflowService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.probase.repository.ProjectBaseRepository;
import com.sinoyd.lims.probase.repository.ReceiveSampleRecordBaseRepository;
import com.sinoyd.lims.probase.repository.SampleBaseRepository;
import com.sinoyd.lims.probase.service.CheckProjectService;
import com.sinoyd.lims.probase.service.NewLogBaseService;
import com.sinoyd.lims.probase.service.StatusForProjectBaseService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
public class CheckProjectServiceImpl implements CheckProjectService {

    @Autowired
    private ProjectBaseRepository projectBaseRepository;

    @Autowired
    private ReceiveSampleRecordBaseRepository receiveSampleRecordBaseRepository;

    @Autowired
    private SampleBaseRepository sampleBaseRepository;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private NewLogBaseService newLogBaseService;

    @Autowired
    private StatusForProjectBaseService statusForProjectBaseService;

    @Autowired
    private CommonRepository commonRepository;

    @Transactional
    @Override
    public void checkProject(String projectId) {
        try {
            DtoProject objPro = projectBaseRepository.findOne(projectId);
            List<DtoReceiveSampleRecord> rsrList = receiveSampleRecordBaseRepository.findByProjectId(projectId);
//            List<DtoSample> sampleList = sampleBaseRepository.findByProjectId(projectId);
//
//            //是否存在不在送样单中的样品
//            Boolean isHave = sampleList.stream().filter(s -> s.getReceiveId().equals(UUIDHelper.GUID_EMPTY)
//                    && !s.getAnanlyzeStatus().equals(EnumPRO.EnumAnalyzeStatus.不需要分析.getValue())).count() > 0;
            Integer count = sampleBaseRepository.countSampleNoReceiveSample(projectId, UUIDHelper.GUID_EMPTY, EnumPRO.EnumAnalyzeStatus.不需要分析.getValue());
            Boolean isHave = count > 0;
            String name = "系统管理员";
            String operatorId = "11111111-1111-1111-1111-111111111111";
            try {
                operatorId = PrincipalContextUser.getPrincipal().getUserId();
                name = PrincipalContextUser.getPrincipal().getUserName();
            } catch (Exception ex) {

            }
            String signal = "";
            if (rsrList != null && rsrList.size() > 0) {
                if (rsrList.stream().filter(r -> r.getReceiveStatus().equals(EnumLIM.EnumReceiveRecordStatus.已数据确认.getValue())).count() == rsrList.size()
                        && !isHave)//数据汇总中
                {
                    if (EnumPRO.EnumProjectStatus.getByName(objPro.getStatus()).getValue() < EnumPRO.EnumProjectStatus.数据汇总中.getValue()) {
                        //工作流信号处理
                        signal = "projectTestEnd";
                    }
                } else if (rsrList.stream().filter(r -> !r.getReceiveStatus().equals(EnumLIM.EnumReceiveRecordStatus.已数据确认.getValue())).count() > 0
                        || isHave)//测试中
                {
                    if (EnumPRO.EnumProjectStatus.getByName(objPro.getStatus()).getValue() > EnumPRO.EnumProjectStatus.开展中.getValue()) {
                        //工作流信号处理
                        signal = "projectLaunch";
                    }
                }
            } else if (isHave && EnumPRO.EnumProjectStatus.getByName(objPro.getStatus()).getValue() > EnumPRO.EnumProjectStatus.开展中.getValue()) {
                //工作流信号处理
                signal = "projectLaunch";
            }
            if (StringUtils.isNotNullAndEmpty(signal)) {
                String sig = signal;
                String operator = operatorId;
                String operatorName = name;

                //工作流处理
                DtoWorkflowSign dtoWorkflowSign = new DtoWorkflowSign();
                dtoWorkflowSign.setObjectId(projectId);
                dtoWorkflowSign.setSignal(sig);
                dtoWorkflowSign.setIsActivate(true);
                dtoWorkflowSign.setIsAutoStatus(false);

                try {
                    String status = workflowService.submitSign(dtoWorkflowSign);
                    objPro.setStatus(StringUtils.isNotNullAndEmpty(status) ? status : EnumPRO.EnumProjectStatus.已办结.toString());

                } catch (Exception ex) {
                    throw new BaseException(ex);
                }

                statusForProjectBaseService.checkStatus(objPro);
                //日志
                String comment = String.format("更新项目状态为%s。", objPro.getStatus());
                newLogBaseService.createLog(objPro.getId(),
                        comment,
                        "",
                        EnumPRO.EnumLogType.项目流程.getValue(),
                        EnumPRO.EnumLogObjectType.项目.getValue(),
                        EnumPRO.EnumLogOperateType.更新项目状态.toString(),
                        operator,
                        operatorName,
                        "",
                        "");

                projectBaseRepository.updateProjectStatus(Collections.singletonList(objPro.getId()), objPro.getStatus(), PrincipalContextUser.getPrincipal().getUserId(), new Date());
            }
        } catch (Exception ex) {
            throw new BaseException(ex);
        }
    }
}
