package com.sinoyd.base.repository.lims;

import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.List;

/**
 * 仪器管理-基本信息-操作接口
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-29
 * @since V100R001
 */
public interface InstrumentRepository extends IBaseJpaRepository<DtoInstrument, String> {

  /**
   * 根据instrumentsCode及rowGuid判断是否重名
   *
   * @Param instrumentsCode
   * @Param id
   */
  @Query("select count(p) from DtoInstrument p where p.isDeleted != 1 and p.instrumentsCode = :instrumentsCode and p.id != :id")
  public Integer countByInsCode(@Param("instrumentsCode") String instrumentsCode, @Param("id") String id);

  /**
   * 根据id数组获取仪器
   *
   * @param ids
   * @return
   */
  @Query("select d from DtoInstrument d where d.isDeleted = 0 and d.id in :ids")
  List<DtoInstrument> findByIds(@Param("ids") Collection<String> ids);

  /***
   * 获取制造厂商
   * @return
   */
  @Query("select distinct d.factoryName from DtoInstrument d where d.factoryName <> null and d.factoryName <> '' order by d.factoryName")
  List<String> findFactoryName();

  /***
   * 获取溯源单位
   * @return
   */
  @Query("select distinct d.originUnit from DtoInstrument d where d.originUnit <> null and d.originUnit <> '' order by d.originUnit")
  List<String> findOriginUnit();

  /***
   * 获取检定校准所有单位
   * @return
   */
  @Query("select distinct d.checkDeptName from DtoInstrumentCheckRecord d where d.checkDeptName <> null and d.checkDeptName <> '' order by d.checkDeptName")
  List<String> findCheckUnit();

  /**
   * 获取所有假删的仪器信息
   *
   * @return 返回排除假删的数据
   */
  @Override
  @Query("select p from DtoInstrument p where p.isDeleted = 0")
  List<DtoInstrument> findAll();

  /**
   * 根据id数组获取信息
   *
   * @param ids 主键ids
   * @return 返回信息
   */
  @Query("select d from DtoInstrument d where d.isDeleted = 0 and d.id in :ids")
  @Override
  List<DtoInstrument> findAll(@Param("ids") Iterable<String> ids);


  /**
   * 返回所有的带假删的信息
   *
   * @return 返回带删除的信息
   */
  @Query("select p from DtoInstrument p")
  List<DtoInstrument> findAllDeleted();


  /**
   * 返回所有的带假删的信息
   *
   * @param ids 主键的ids
   * @return 返回带删除的信息
   */
  @Query("select p from DtoInstrument p where p.id in :ids")
  List<DtoInstrument> findAllDeleted(@Param("ids") List<String> ids);
}