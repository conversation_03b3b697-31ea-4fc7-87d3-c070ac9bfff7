package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.lims.EnterpriseRepository;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.DepartmentService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.criteria.FixedAssetsCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoFixedProperty;
import com.sinoyd.lims.lim.dto.customer.DtoImportFixedAssets;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.FixedAssetsRepository;
import com.sinoyd.lims.lim.service.FixedAssetsService;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 固定资产接口实现类
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/13
 * @since V100R001
 */
@Service
public class FixedAssetsServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoFixedProperty, String, FixedAssetsRepository> implements FixedAssetsService {

    private CodeService codeService;
    private ImportUtils importUtils;
    private DepartmentService departmentService;
    protected EnterpriseRepository enterpriseRepository;
    private PersonService personService;
    private final static String[] status = new String[]{"使用中", "已报废"};

    @Override
    public void findByPage(PageBean<DtoFixedProperty> page, BaseCriteria criteria) {
        page.setEntityName("DtoFixedProperty a");
        page.setSelect("select a");
        super.findByPage(page, criteria);
        // 供应商
        List<DtoEnterprise> enterpriseList = enterpriseRepository.findByIsDeletedFalseAndType(EnumBase.EnumEnterpriseType.供应商.getValue());
        // 所属科室
        List<DtoDepartment> deptList = departmentService.findAll();
        List<DtoPerson> personList = personService.findAll();

        List<DtoFixedProperty> pageDatas = page.getData();
        for (DtoFixedProperty fixedAssets : pageDatas) {
            Optional<DtoEnterprise> dtoEnterprise = enterpriseList.stream().filter(p -> p.getId().equals(fixedAssets.getSupplier())).findFirst();
            dtoEnterprise.ifPresent(p -> fixedAssets.setSupplierName(p.getName()));
            Optional<DtoDepartment> dtoDepartment = deptList.stream().filter(p -> p.getId().equals(fixedAssets.getDeptId())).findFirst();
            dtoDepartment.ifPresent(p -> fixedAssets.setDeptName(p.getDeptName()));
            Optional<DtoPerson> dtoPerson = personList.stream().filter(p -> p.getId().equals(fixedAssets.getManager())).findFirst();
            dtoPerson.ifPresent(p -> fixedAssets.setManagerName(p.getCName()));
        }
        page.setData(pageDatas);
    }

    @Override
    public DtoFixedProperty findAttachPath(String id) {
        return repository.findOne(id);
    }

    @Transactional
    @Override
    public DtoFixedProperty save(DtoFixedProperty entity) {
        // 资产编号
        String assetsNo = entity.getAssetsNo();
        DtoFixedProperty repositoryOne = repository.findByAssetsNo(assetsNo);
        if (StringUtil.isNotNull(repositoryOne) ) {
            throw new BaseException("资产编号重复！");
        }
        return super.save(entity);
    }

    @Transactional
    @Override
    public DtoFixedProperty update(DtoFixedProperty entity) {
        String assetsNo = entity.getAssetsNo();
        DtoFixedProperty repositoryOne = repository.findByAssetsNo(assetsNo);
        if (StringUtil.isNotNull(repositoryOne) && !entity.getId().equals(repositoryOne.getId())) {
            throw new BaseException("资产编号重复！");
        }
        return super.update(entity);
    }

    @Override
    public void downLoadExcel(HttpServletResponse response, Map<String, String> sheetNames, String fileName) {
        List<DtoImportFixedAssets> importFixedAssets = getNullIns();
        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoImportFixedAssets.class, importFixedAssets);
        // 设置下拉框
        this.processDropList(workBook);
        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
    }

    @Override
    public void export(BaseCriteria baseCriteria, HttpServletResponse response, Map<String, String> sheetNames, String fileName) {
        PageBean<DtoFixedProperty> page = new PageBean<>();
        page.setPageNo(1);
        page.setRowsPerPage(Integer.MAX_VALUE);
        FixedAssetsCriteria criteria = (FixedAssetsCriteria) baseCriteria;
        this.findByPage(page, criteria);
        List<DtoFixedProperty> DtoFixedProperty = page.getData();
        List<DtoImportFixedAssets> importFixedAssets = new ArrayList<>();

        // 供应商
        List<DtoEnterprise> enterpriseList = enterpriseRepository.findByIsDeletedFalseAndType(EnumBase.EnumEnterpriseType.供应商.getValue());
        // 所属科室
        List<DtoDepartment> deptList = departmentService.findAll();
        List<DtoPerson> personList = personService.findAll();

        for (DtoFixedProperty dtoFixedAsset : DtoFixedProperty) {
            DtoImportFixedAssets dtoImportFixedAssets = new DtoImportFixedAssets();
            BeanUtils.copyProperties(dtoFixedAsset, dtoImportFixedAssets);
            dtoImportFixedAssets.setPurchasePrice(dtoFixedAsset.getPurchasePrice().toString());
            dtoImportFixedAssets.setPurchaseDate(StringUtil.isNotNull(dtoFixedAsset.getPurchaseDate())?DateUtil.dateToString(dtoFixedAsset.getPurchaseDate(), DateUtil.YEAR):"");
            Optional<DtoEnterprise> dtoEnterprise = enterpriseList.stream().filter(p -> p.getId().equals(dtoFixedAsset.getSupplier())).findFirst();
            dtoEnterprise.ifPresent(p -> dtoImportFixedAssets.setSupplier(p.getName()));
            Optional<DtoDepartment> dtoDepartment = deptList.stream().filter(p -> p.getId().equals(dtoFixedAsset.getDeptId())).findFirst();
            dtoDepartment.ifPresent(p -> dtoImportFixedAssets.setDeptId(p.getDeptName()));
            Optional<DtoPerson> dtoPerson = personList.stream().filter(p -> p.getId().equals(dtoFixedAsset.getManager())).findFirst();
            dtoPerson.ifPresent(p -> dtoImportFixedAssets.setManager(p.getCName()));
            dtoImportFixedAssets.setStatus(EnumLIM.EnumAssetsStatus.getByValue(dtoFixedAsset.getStatus()));
            importFixedAssets.add(dtoImportFixedAssets);
        }
        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoImportFixedAssets.class, importFixedAssets);
        // 设置下拉框
        this.processDropList(workBook);
        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
    }

    /**
     * 处理下拉框信息
     *
     * @param workBook 工作簿
     */
    public void processDropList(Workbook workBook) {
        // 供应商
        List<DtoEnterprise> enterpriseList = enterpriseRepository.findByIsDeletedFalseAndType(EnumBase.EnumEnterpriseType.供应商.getValue());
        List<String> enterpriseNames = enterpriseList.stream().map(DtoEnterprise::getName).collect(Collectors.toList());

        // 获取所有资产类型
        List<DtoCode> dbPost = codeService.findCodes("PRO_Assets_Type");
        List<String> dictName = dbPost.stream().map(DtoCode::getDictName).collect(Collectors.toList());
        // 所属科室
        List<DtoDepartment> deptList = departmentService.findAll();
        List<String> deptNames = deptList.stream().map(DtoDepartment::getDeptName).collect(Collectors.toList());
        // 设置下拉框信息
        String[] enterpriseNamesDroplist = new String[enterpriseNames.size()];
        String[] dictNameDroplist = new String[dictName.size()];
        String[] deptNamesDroplist = new String[deptNames.size()];
        for (int i = 0; i < enterpriseNames.size(); i++) {
            enterpriseNamesDroplist[i] = enterpriseNames.get(i);
        }
        for (int i = 0; i < dictName.size(); i++) {
            dictNameDroplist[i] = dictName.get(i);
        }
        for (int i = 0; i < deptNames.size(); i++) {
            deptNamesDroplist[i] = deptNames.get(i);
        }
        importUtils.selectList(workBook, 5, 5, enterpriseNamesDroplist);
        importUtils.selectList(workBook, 6, 6, dictNameDroplist);
        importUtils.selectList(workBook, 7, 7, deptNamesDroplist);
        importUtils.selectList(workBook, 8, 8, status);
    }


    /**
     * 获取空固定资产数据
     *
     * @return 空list
     */
    private List<DtoImportFixedAssets> getNullIns() {
        // 获取固定资产空数据
        List<DtoImportFixedAssets> formulas = new ArrayList<>();
        DtoImportFixedAssets formula = new DtoImportFixedAssets();
        formulas.add(formula);
        return formulas;
    }

    @Autowired
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }

    @Autowired
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    public void setEnterpriseRepository(EnterpriseRepository enterpriseRepository) {
        this.enterpriseRepository = enterpriseRepository;
    }

    @Autowired
    public void setDepartmentService(DepartmentService departmentService) {
        this.departmentService = departmentService;
    }

    @Autowired
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }
}
