package com.sinoyd.lims.lim.dto.customer.expimp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.base.dto.customer.PoiBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *  文件管理清单导出poi实体
 *
 * @version V1.0.0 2025/4/27
 * @author: xiexy
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DtoExpFileManage extends PoiBaseEntity {
    /**
     * 主键id
     */
    @Excel(name = "名称", orderNum = "10", width = 50)
    private String filename;

    /**
     * 主键id
     */
    @Excel(name = "文件类型", orderNum = "20", width = 20)
    private String docSuffix;

    /**
     * 主键id
     */
    @Excel(name = "上传日期", orderNum = "30", width = 20)
    private String createDate;

    /**
     * 主键id
     */
    @Excel(name = "上传人", orderNum = "40", width = 20)
    private String uploadPerson;

    /**
     * 主键id
     */
    @Excel(name = "下载次数", orderNum = "50", width = 20)
    private Integer downloadTimes;
}
