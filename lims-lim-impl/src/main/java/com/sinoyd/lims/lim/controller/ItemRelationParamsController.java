package com.sinoyd.lims.lim.controller;

import com.sinoyd.lims.lim.dto.rcc.DtoItemRelationParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.service.ItemRelationParamsService;
import com.sinoyd.lims.lim.criteria.ItemRelationParamsCriteria;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * ItemRelationParams服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2023/11/1
 * @since V100R001
 */
 @Api(tags = "示例: ItemRelationParams服务")
 @RestController
 @RequestMapping("api/lim/itemRelationParams")
 @Validated
 public class ItemRelationParamsController extends BaseJpaController<DtoItemRelationParams, String,ItemRelationParamsService> {


    /**
     * 分页动态条件查询ItemRelationParams
     *
     * @param itemRelationParamsCriteria 条件参数
     * @return RestResponse<List < ItemRelationParams>>
     */
    @ApiOperation(value = "分页动态条件查询ItemRelationParams", notes = "分页动态条件查询ItemRelationParams")
    @GetMapping
    public RestResponse<List<DtoItemRelationParams>> findByPage(ItemRelationParamsCriteria itemRelationParamsCriteria) {
        PageBean<DtoItemRelationParams> pageBean = super.getPageBean();
        RestResponse<List<DtoItemRelationParams>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, itemRelationParamsCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询ItemRelationParams
     *
     * @param id 主键id
     * @return RestResponse<DtoItemRelationParams>
     */
    @ApiOperation(value = "按主键查询ItemRelationParams", notes = "按主键查询ItemRelationParams")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoItemRelationParams> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoItemRelationParams> restResponse = new RestResponse<>();
        DtoItemRelationParams itemRelationParams = service.findOne(id);
        restResponse.setData(itemRelationParams);
        restResponse.setRestStatus(StringUtil.isNull(itemRelationParams) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增ItemRelationParams
     *
     * @param itemRelationParams 实体列表
     * @return RestResponse<DtoItemRelationParams>
     */
    @ApiOperation(value = "新增ItemRelationParams", notes = "新增ItemRelationParams")
    @PostMapping
    public RestResponse<Boolean> create(@Validated @RequestBody DtoItemRelationParams itemRelationParams) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.create(itemRelationParams.getRelationId(), itemRelationParams.getAnalyzeItemIds());
        restResponse.setData(Boolean.TRUE);
        return restResponse;
    }

    /**
     * 新增ItemRelationParams
     *
     * @param itemRelationParams 实体列表
     * @return RestResponse<DtoItemRelationParams>
     */
    @ApiOperation(value = "修改ItemRelationParams", notes = "修改ItemRelationParams")
    @PutMapping
    public RestResponse<DtoItemRelationParams> update(@Validated @RequestBody DtoItemRelationParams itemRelationParams) {
        RestResponse<DtoItemRelationParams> restResponse = new RestResponse<>();
        restResponse.setData(service.update(itemRelationParams));
        return restResponse;
    }

    /**
     * "根据id批量删除ItemRelationParams
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除ItemRelationParams", notes = "根据id批量删除ItemRelationParams")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
}