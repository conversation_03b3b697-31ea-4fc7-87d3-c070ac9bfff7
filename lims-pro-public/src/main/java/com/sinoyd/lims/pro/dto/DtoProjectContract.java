package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.ProjectContract;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/11/21
 **/

@EqualsAndHashCode(callSuper = true)
@Entity
@Data
@Table(name = "TB_PRO_ProjectContract")
public class DtoProjectContract extends ProjectContract {
    /**
     * 任务所在区名称
     */
    @Transient
    private String addressName;
}
