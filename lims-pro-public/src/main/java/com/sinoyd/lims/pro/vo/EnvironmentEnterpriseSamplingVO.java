package com.sinoyd.lims.pro.vo;

import lombok.Data;

import java.util.List;

/**
 * 环保企业通采样信息推送VO
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2024/11/01
 **/
@Data
public class EnvironmentEnterpriseSamplingVO {

    public EnvironmentEnterpriseSamplingVO() {
    }

    public EnvironmentEnterpriseSamplingVO(String id, String code, List<EnvironmentEnterpriseSampleVO> samples) {
        this.id = id;
        this.code = code;
        this.samples = samples;
    }

    /**
     * 采样主键id，要求项目id+采样id组合唯一
     */
    private String id;

    /**
     * 采样编号/检测类型
     */
    private String code;

    private List<EnvironmentEnterpriseSampleVO> samples;
}
