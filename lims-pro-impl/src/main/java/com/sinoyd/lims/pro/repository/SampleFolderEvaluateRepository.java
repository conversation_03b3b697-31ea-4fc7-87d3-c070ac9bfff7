package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoSampleFolderEvaluate;

import java.util.Collection;
import java.util.List;

/**
 *  点位评判数据repository
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2023/06/12
 */
public interface SampleFolderEvaluateRepository extends IBaseJpaPhysicalDeleteRepository<DtoSampleFolderEvaluate, String> {

    /**
     * 根据点位id和测试id查询记录
     *
     * @param sampleFolderId 点位id
     * @param testId         测试id
     * @return DtoSampleFolderEvaluate
     */
    DtoSampleFolderEvaluate findBySampleFolderIdAndTestId(String sampleFolderId, String testId);

    /**
     * 根据点位ids获取集合
     *
     * @param folderIds 点位ids
     * @return 数据集合
     */
    List<DtoSampleFolderEvaluate> findBySampleFolderIdIn(List<String> folderIds);

    /**
     * 根据点位ids和测试ids查询记录
     *
     * @param folderIds 点位ids
     * @param testIds   测试ids
     * @return 数据集合
     */
    List<DtoSampleFolderEvaluate> findBySampleFolderIdInAndTestIdIn(Collection<String> folderIds, Collection<String> testIds);
}
