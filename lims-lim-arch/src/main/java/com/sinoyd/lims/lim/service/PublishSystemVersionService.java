package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoPublishSystemVersion;

/**
 * 版本发布管理
 * <AUTHOR>
 * @version V1.0.0
 * @since 2022/11/9
 */
public interface PublishSystemVersionService extends IBaseJpaService<DtoPublishSystemVersion, String> {


    /**
     * 附件路径
     *
     * @param id 主键id
     * @return 返回相应的路径信息
     */
    DtoPublishSystemVersion findAttachPath(String id);

    /**
     * 获取flyway脚本编号
     * @return flyway脚本编号
     */
    String findFlyWayVersion();

}