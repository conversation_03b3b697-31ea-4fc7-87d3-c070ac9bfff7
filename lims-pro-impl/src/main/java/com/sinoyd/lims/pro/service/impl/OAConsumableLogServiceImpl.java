package com.sinoyd.lims.pro.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.sinoyd.base.dto.lims.DtoConsumableDetail;
import com.sinoyd.base.dto.lims.DtoConsumableLog;
import com.sinoyd.base.entity.ConsumableLog;
import com.sinoyd.base.service.ConsumableDetailService;
import com.sinoyd.base.service.ConsumableLogService;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.service.DepartmentService;
import com.sinoyd.lims.pro.dto.DtoOATask;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;
import com.sinoyd.lims.pro.dto.DtoOATaskRelation;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumOATaskType;
import com.sinoyd.lims.pro.service.OAConsumableLogService;
import com.sinoyd.lims.pro.service.OATaskRelationService;
import com.sinoyd.lims.pro.service.OATaskService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * 领料申请业务操作接口实现
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-03
 * @since V100R001
 */
@Service
public class OAConsumableLogServiceImpl implements OAConsumableLogService {
    /**
     * 审批任务服务
     */
    @Autowired
    @Lazy
    private OATaskService oaTaskService;

    /**
     * 审批任务关联服务
     */
    @Autowired
    @Lazy
    private OATaskRelationService oaTaskRelationService;

    /**
     * 领料服务
     */
    @Autowired
    @Lazy
    private ConsumableLogService consumableLogService;

    /**
     * 领料详情服务
     */
    @Autowired
    @Lazy
    private ConsumableDetailService consumableDetailService;

    @Autowired
    private DepartmentService deptService;

    @Override
    public String startProcess(DtoOATaskCreate<List<DtoConsumableLog>> taskDto) {
        CurrentPrincipalUser user = PrincipalContextUser.getPrincipal();
        Map<String, Object> vars = new HashMap<>();

//        // 获取用户对应的部门负责人
//        DtoUser deptPrincipal = deptService.findPrincipal(user.getUserId());
//        if (StringUtil.isNotNull(deptPrincipal)) {
//            taskDto.setNextAssignee(deptPrincipal.getLoginId());
//            taskDto.setNextAssigneeId(deptPrincipal.getId());
//            taskDto.setNextAssigneeName(deptPrincipal.getUserName());
//        }
//        else {
//            taskDto.setNextAssignee(user.getLoginId());
//            taskDto.setNextAssigneeId(user.getUserId());
//            taskDto.setNextAssigneeName(user.getUserName());
//        }
//
        DtoOATask oaTask = oaTaskService.startProcess(EnumOATaskType.领料, taskDto, vars);

        List<DtoConsumableLog> data = taskDto.getData();

        List<DtoOATaskRelation> taskRelations = new ArrayList<>();
        DtoOATaskRelation taskRelation = null;

        for (ConsumableLog consumable : data) {
            taskRelation = new DtoOATaskRelation();
            taskRelation.setTaskId(oaTask.getId());
            taskRelation.setObjectId(consumable.getId());

            taskRelations.add(taskRelation);
        }

        // 添加领料记录
        consumableLogService.save(data);

        // 添加审批任务关联信息
        oaTaskRelationService.save(taskRelations);

        return oaTask.getId();
    }

    @Override
    public DtoOATaskDetail<List<DtoConsumableLog>, List<DtoConsumableDetail>> findOATaskDetail(String taskId) {
        DtoOATask task = oaTaskService.findByTaskId(taskId);

        DtoOATaskDetail<List<DtoConsumableLog>, List<DtoConsumableDetail>> taskDetail = new DtoOATaskDetail<>();
        taskDetail.setTask(task);
        taskDetail.setAttach(oaTaskService.getOATaskAttachDTO(task.getProcInstId()));

        List<DtoOATaskRelation> relations = oaTaskRelationService.findListByTaskId(taskId);

        // 领料信息
        List<DtoConsumableLog> consumables = new ArrayList<>();

        // 领料详细信息
        List<DtoConsumableDetail> consumableDetails = new ArrayList<>();

        for (DtoOATaskRelation relation : relations) {
            DtoConsumableLog consumable = consumableLogService.findOne(relation.getObjectId());
            consumables.add(consumable);

            DtoConsumableDetail consumableDetail = consumableDetailService.findOne(consumable.getConsumableDetailId());
            consumableDetails.add(consumableDetail);
        }

        // 设置详细信息
        taskDetail.setDetail(consumables);

        // 设置扩展信息
        taskDetail.setExtend(consumableDetails);

        return taskDetail;
    }

}