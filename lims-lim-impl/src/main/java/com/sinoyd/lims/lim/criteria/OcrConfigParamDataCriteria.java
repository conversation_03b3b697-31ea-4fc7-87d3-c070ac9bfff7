package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Calendar;
import java.util.Date;


/**
 * ocr识别记录查询条件
 * <AUTHOR>
 * @version V1.0.0 2024/02/22
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OcrConfigParamDataCriteria extends BaseCriteria {

    /**
     * 开始日期
     */
    private String startDateStr;

    /**
     * 结束日期
     */
    private String endDateStr;

    /**
     * 识别对象名称
     */
    private String configName;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(startDateStr)) {
            Date from = DateUtil.stringToDate(startDateStr, DateUtil.FULL);
            condition.append(" and d.createDate >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtil.isNotEmpty(endDateStr)) {
            Date to = DateUtil.stringToDate(endDateStr, DateUtil.FULL);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DATE, 1);
            to = c.getTime();
            condition.append(" and d.createDate <= :endTime");
            values.put("endTime", to);
        }
        if (StringUtil.isNotEmpty(configName)) {
            condition.append(" and exists( select 1 from DtoOcrConfig c where d.configId = c.id and c.configName like :configName) ");
            values.put("configName", "%" + configName + "%");
        }
        return condition.toString();
    }
}