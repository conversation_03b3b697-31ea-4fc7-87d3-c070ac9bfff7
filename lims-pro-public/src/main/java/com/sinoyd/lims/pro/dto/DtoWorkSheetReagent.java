package com.sinoyd.lims.pro.dto;

import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.lims.pro.entity.WorkSheetReagent;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import java.util.List;


/**
 * DtoWorkSheetReagent实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_WorkSheetReagent")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public  class DtoWorkSheetReagent extends WorkSheetReagent {
    private static final long serialVersionUID = 1L;

    /**
     * 检测单号
     */
    @Transient
    private String workSheetCode;

    /**
     * 配置人员id
     */
    @Transient
    private String creator;

    @Transient
    private Integer reagentType;

    @Transient
    private String diluent;

    @Transient
    private String suitItem;

    /**
     * 试剂配置记录id集合
     */
    @Transient
    private List<String> reagentConfigIds;


    public String getContent() {
        StringBuilder content = new StringBuilder();
        content.append("</br>试剂名称:").append(this.getReagentName());
        content.append("</br>试剂规格:").append(this.getReagentSpecification());
        content.append("</br>配置溶液:").append(this.getConfigurationSolution());
        content.append("</br>配置过程:").append(this.getCourse());
        if (!DateUtil.dateToString(this.getConfigDate(), DateUtil.YEAR).equals("1753-01-01")) {
            content.append("</br>配置日期:").append(DateUtil.dateToString(this.getConfigDate(), DateUtil.YEAR));
        } else {
            content.append("</br>配置日期:").append("");
        }
        if (!DateUtil.dateToString(this.getExpiryDate(), DateUtil.YEAR).equals("1753-01-01")) {
            content.append("</br>有效期:").append(DateUtil.dateToString(this.getExpiryDate(), DateUtil.YEAR));
        } else {
            content.append("</br>有效期:").append("");
        }
        return content.toString();
    }
}