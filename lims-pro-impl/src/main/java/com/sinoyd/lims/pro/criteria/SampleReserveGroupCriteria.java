package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * SampleReserveCriteria查询Sample时的条件
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/20
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SampleReserveGroupCriteria extends BaseCriteria implements Serializable {

    /**
     * 需要过滤的样品类型id集合
     */
    private List<String> ignoreSampleTypeIds;

    /**
     * 采样开始日期
     */
    private String beginTime;

    /**
     * 采样结束日期
     */
    private String endDate;

    /**
     * 检测类型
     */
    private String sampleType;

    /**
     * 项目查询条件
     */
    private String projectCondition;

    /**
     * 样品查询条件
     */
    private String sampleCondition;

    /**
     * 分析因子
     */
    private String analyzeItemName;

    /**
     * 分析方法，编号
     */
    private String analyzeMethodCondition;

    /**
     * 处置状态
     */
    private Integer disposeStatus;

    /**
     * 领取状态
     */
    private String receiveStatus;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and t1.analyseNums > 0 and t1.sampleId = t2.id and exists (SELECT 1 FROM DtoReceiveSampleRecord r WHERE t2.receiveId = r.id and r.receiveStatus > 1)");
        if (StringUtil.isNotEmpty(this.receiveStatus)) {
            if ("已领取".equals(this.receiveStatus)) {
                condition.append(" and t1.reserveNums >= t1.analyseNums");
            } else if ("未领取".equals(this.receiveStatus)) {
                condition.append("and t1.reserveNums < t1.analyseNums");
            }
        }
        if (StringUtils.isNotNullAndEmpty(this.beginTime)) {
            Date from = DateUtil.stringToDate(this.beginTime, DateUtil.YEAR);
            condition.append(" and t2.samplingTimeBegin >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtils.isNotNullAndEmpty(this.endDate)) {
            Date to = DateUtil.stringToDate(this.endDate, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            to = c.getTime();
            condition.append(" and t2.samplingTimeBegin < :endTime");
            values.put("endTime", to);
        }
        if (StringUtil.isNotEmpty(this.sampleType) && !UUIDHelper.GUID_EMPTY.equals(this.sampleType)) {
            condition.append(" and t2.sampleTypeId = :sampleTypeId");
            values.put("sampleTypeId", this.sampleType);
        }
        if (StringUtil.isNotEmpty(this.ignoreSampleTypeIds)) {
            condition.append(" and t2.sampleTypeId not in (:ignoreSampleTypeIds)");
            values.put("ignoreSampleTypeIds", this.ignoreSampleTypeIds);
        }
        if (StringUtil.isNotEmpty(this.projectCondition)) {
            condition.append(" AND t2.receiveId IN (" +
                    " SELECT sa.receiveId FROM DtoSample sa, DtoProject po where sa.projectId = po.id " +
                    " and ( po.projectName LIKE :projectCondition OR po.projectCode LIKE :projectCondition ))");
            values.put("projectCondition", "%" + this.projectCondition + "%");
        }
        if (StringUtil.isNotEmpty(this.sampleCondition)) {
            condition.append(" and (t2.code like :sampleCondition or t2.redFolderName like :sampleCondition)");
            values.put("sampleCondition", "%" + this.sampleCondition + "%");
        }
        if (StringUtil.isNotEmpty(this.analyzeItemName)) {
            condition.append(" and t1.analyseItemNames like :analyzeItemName ");
            values.put("analyzeItemName", "%" + this.analyzeItemName + "%");
        }
        if (StringUtil.isNotEmpty(this.analyzeMethodCondition)) {
            condition.append(" and exists (select 1 from DtoSampleGroup2Test a where t1.id = a.sampleGroupId " +
                    "and (a.redAnalyzeMethodName like :analyzeMethodCondition or a.redCountryStandard like :analyzeMethodCondition))");
            values.put("analyzeMethodCondition", "%" + this.analyzeMethodCondition + "%");
        }
        if (StringUtil.isNotNull(this.disposeStatus)) {
            if (this.disposeStatus.equals(1)) {
                condition.append(" and exists (select 1 from DtoSampleReserve sr where t1.id = sr.sampleGroupId and " +
                        "sr.reserveType = 2 and isDeleted = 0) ");
            } else if (this.disposeStatus.equals(0)) {
                condition.append(" and not exists (select 1 from DtoSampleReserve sr where t1.id = sr.sampleGroupId and " +
                        "sr.reserveType = 2 and isDeleted = 0) ");
            }
        }
        return condition.toString();
    }
}
