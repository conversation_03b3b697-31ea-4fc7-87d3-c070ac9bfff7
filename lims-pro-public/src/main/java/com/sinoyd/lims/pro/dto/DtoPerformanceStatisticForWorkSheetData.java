package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.PerformanceStatisticForWorkSheetData;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoPerformanceStatisticForWorkSheetData实体
 * <AUTHOR>
 * @version V1.0.0 2020/2/19
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_PerformanceStatisticForWorkSheetData")
 @Data
 @DynamicInsert
 public  class DtoPerformanceStatisticForWorkSheetData extends PerformanceStatisticForWorkSheetData {
    private static final long serialVersionUID = 1L;

    public DtoPerformanceStatisticForWorkSheetData() {

    }

    /**
     * 构造函数 工作量求和用
     * @param sample
     * @param localeGap
     * @param interiorGap
     * @param parallel
     * @param addition
     * @param curveItem
     * @param curveEntries
     * @param point
     * @param qcSample
     * @param valid
     */
    public DtoPerformanceStatisticForWorkSheetData(Integer sample,Integer localeGap,Integer interiorGap,Integer parallel,Integer addition,
                                                   Integer curveItem,Integer curveEntries,Integer point,Integer qcSample,Integer valid) {
        this.setSample(sample);
        this.setLocaleGap(localeGap);
        this.setInteriorGap(interiorGap);
        this.setParallel(parallel);
        this.setAddition(addition);
        this.setCurveItem(curveItem);
        this.setCurveEntries(curveEntries);
        this.setPoint(point);
        this.setQcSample(qcSample);
        this.setValid(valid);
    }

    /**
     * 检测类型名称
     */
    @Transient
    private String sampleTypeName;
}