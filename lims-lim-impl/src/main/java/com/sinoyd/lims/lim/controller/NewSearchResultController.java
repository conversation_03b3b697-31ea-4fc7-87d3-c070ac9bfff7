package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.NewSearchResultCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoNewSearchResult;
import com.sinoyd.lims.lim.service.NewSearchResultService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 查新结果接口服务
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
@Api(tags = "查新结果接口服务")
@RestController
@RequestMapping("api/lim/newSearchResult")
@Validated
public class NewSearchResultController extends BaseJpaController<DtoNewSearchResult, String, NewSearchResultService> {
    /**
     * 分页动态条件查询NewSearchResult
     *
     * @param newSearchResultCriteria 条件参数
     * @return RestResponse<List < newSearchResult>>
     */
    @ApiOperation(value = "分页动态条件查询", notes = "分页动态条件查询")
    @GetMapping
    public RestResponse<List<DtoNewSearchResult>> findByPage(NewSearchResultCriteria newSearchResultCriteria) {
        PageBean<DtoNewSearchResult> pageBean = super.getPageBean();
        RestResponse<List<DtoNewSearchResult>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, newSearchResultCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 根据id查询查新结果
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id查询查新结果", notes = "根据id查询查新结果")
    @GetMapping("/{id}")
    public RestResponse<DtoNewSearchResult> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoNewSearchResult> restResp = new RestResponse<>();
        DtoNewSearchResult entity = service.findOne(id);
        restResp.setData(entity);
        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }


    /**
     * 新增查新结果
     *
     * @param newSearchResult 查新结果实体
     * @return 新增的查新结果实体
     */
    @ApiOperation(value = "新增查新结果", notes = "新增查新结果")
    @PostMapping("")
    public RestResponse<DtoNewSearchResult> create(@Validated @RequestBody DtoNewSearchResult newSearchResult) {
        RestResponse<DtoNewSearchResult> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoNewSearchResult data = service.save(newSearchResult);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 更新查新结果
     *
     * @param newSearchResult 查新结果实体
     * @return 更新后的查新结果实体
     */
    @ApiOperation(value = "更新查新结果", notes = "更新查新结果")
    @PutMapping("")
    public RestResponse<DtoNewSearchResult> update(@Validated @RequestBody DtoNewSearchResult newSearchResult) {
        RestResponse<DtoNewSearchResult> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoNewSearchResult data = service.update(newSearchResult);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 提交查新结果
     *
     * @param ids 主键ids
     * @return 查新计划实体
     */
    @ApiOperation(value = "提交查新结果", notes = "提交查新结果")
    @PostMapping("/submit")
    public RestResponse<List<DtoNewSearchResult>> submit(@RequestBody List<String> ids) {
        RestResponse<List<DtoNewSearchResult>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.submit(ids));
        return restResponse;
    }

    /**
     * 退回查新结果
     *
     * @param ids 主键ids
     * @return 查新计划实体
     */
    @ApiOperation(value = "退回查新结果", notes = "退回查新结果")
    @PostMapping("/back")
    public RestResponse<List<DtoNewSearchResult>> backResult(@RequestBody List<String> ids) {
        RestResponse<List<DtoNewSearchResult>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.backResult(ids));
        return restResponse;
    }

    /**
     * 单个假删
     *
     * @param id 参数id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除", notes = "根据id批量删除")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.logicDeleteById(id);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 批量假删
     *
     * @param ids 参数ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除", notes = "根据id批量删除")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResponse.setCount(count);

        return restResponse;
    }


}
