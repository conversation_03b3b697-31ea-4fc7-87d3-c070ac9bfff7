package com.sinoyd.lims.lim.vo;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0.0 2025/02/17
 * @since V100R001
 */
@Data
public class CompareJudgeVO {

    /**
     * 主键id
     */
    private String id = UUIDHelper.NewID();

    /**
     * 分析项目id
     */
    private String analyzeItemId;

    /**
     * 检测类型
     */
    private Integer checkType;

    /**
     * 默认标样数
     */
    private String defaultStandardNum;

    /**
     * 是否数据对差
     */
    private Boolean dataDiscrepancy;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyDate;

    /**
     * 分析项目名称
     */
    private String analyzeItemName;
}
