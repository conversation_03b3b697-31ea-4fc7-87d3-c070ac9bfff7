package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoFileControlApply;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.List;
/**
 * FileControlApplyRepository数据访问接口
 * <AUTHOR>
 * @version V1.0.0 2019/9/4
 * @since V100R001
 */
public interface FileControlApplyRepository extends IBaseJpaRepository<DtoFileControlApply, String> {

    /**
     * 根据id数组获取列表
     * @param ids
     * @return
     */
    @Query("select d from DtoFileControlApply d where d.isDeleted = 0 and d.id in :ids")
    List<DtoFileControlApply> findByIds(@Param("ids") Collection<String> ids);
}