package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.CommentComplimentDetail;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoCommentComplimentDetail实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_CommentComplimentDetail")
 @Data
 @DynamicInsert
 public  class DtoCommentComplimentDetail extends CommentComplimentDetail {
   private static final long serialVersionUID = 1L;
 }