package com.sinoyd.lims.pro.repository;

import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoReceiveSubSampleRecord2Sample;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;


/**
 * ReceiveSubSampleRecord2Sample数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface ReceiveSubSampleRecord2SampleRepository extends IBaseJpaRepository<DtoReceiveSubSampleRecord2Sample, String>, LimsRepository<DtoReceiveSubSampleRecord2Sample, String> {
    /**
     * 删除对应领样单下的样品关联
     *
     * @param subIds 领样单id集合
     * @return 删除的条数
     */
    @Transactional
    @Modifying
    @Query("update DtoReceiveSubSampleRecord2Sample a set a.isDeleted = true,a.modifier = :modifier,a.modifyDate= :modifyDate where a.receiveSubSampleRecordId in :subIds")
    Integer deleteByReceiveSubSampleRecordIdIn(@Param("subIds") Collection<String> subIds,
                                               @Param("modifier") String modifier,
                                               @Param("modifyDate") Date modifyDate);

    /**
     * 删除对应样品下的的样品关联
     *
     * @param sampleIds 样品id集合
     * @return 删除的条数
     */
    @Transactional
    @Modifying
    @Query("update DtoReceiveSubSampleRecord2Sample a set a.isDeleted = true,a.modifier = :modifier,a.modifyDate= :modifyDate where a.sampleId in :sampleIds")
    Integer deleteBySampleIdIn(@Param("sampleIds") List<String> sampleIds,
                               @Param("modifier") String modifier,
                               @Param("modifyDate") Date modifyDate);

    /**
     * 根据领样单获取相关数据
     *
     * @param subId 领样单id
     * @return 数据
     */
    List<DtoReceiveSubSampleRecord2Sample> findByReceiveSubSampleRecordId(String subId);

    /**
     * 根据领样单获取相关数据
     *
     * @param subIds 领样单id集合
     * @return 数据
     */
    List<DtoReceiveSubSampleRecord2Sample> findByReceiveSubSampleRecordIdIn(List<String> subIds);

    /**
     * 根据样品id获取相关数据
     *
     * @param sampleId 样品id
     * @return 数据
     */
    List<DtoReceiveSubSampleRecord2Sample> findBySampleId(String sampleId);

    /**
     * 根据样品ids获取相关数据
     *
     * @param sampleIds 样品id
     * @return 数据
     */
    List<DtoReceiveSubSampleRecord2Sample> findBySampleIdIn(List<String> sampleIds);
}