package com.sinoyd.lims.lim.verify.expimp;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.lims.lim.constants.LimConstants;
import com.sinoyd.lims.lim.dto.customer.expimp.DtoExpImpEnterprise;
import com.sinoyd.lims.lim.utils.ImportUtils;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 固定资产导出导入校验器
 *
 * <AUTHOR>
 * @version V1.0.0 2023/12/12
 * @since V100R001
 */
@Component
@Data
public class ImpModifyEnterpriseVerify implements IExcelVerifyHandler<DtoExpImpEnterprise> {

    /**
     * 临时数据
     */
    private ThreadLocal<List<DtoExpImpEnterprise>> enterpriseTl = new ThreadLocal<>();

    /**
     * 所有的区域数据
     */
    private ThreadLocal<List<DtoArea>> dbAreaTl = new ThreadLocal<>();

    /**
     * 所有的区域数据
     */
    private ThreadLocal<Map<String,List<DtoCode>>> codeTl = new ThreadLocal<>();

    /**
     * 数据库的所有的企业数据
     */
    private ThreadLocal<List<DtoEnterprise>> dbEnterpriseTl = new ThreadLocal<>();

    /**
     * 工具类
     */
    private final ImportUtils importUtils = new ImportUtils();


    @Override
    public ExcelVerifyHandlerResult verifyHandler(DtoExpImpEnterprise enterprise) {
        //导入参数处理
        try {
            //跳过空行
            if (importUtils.checkObjectIsNull(enterprise)) {
                return new ExcelVerifyHandlerResult(true);
            }
            //前后去空格
            importUtils.strToTrim(enterprise);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }
        //校验结果
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        StringBuilder failStr = new StringBuilder("第" + (enterprise.getRowNum() + 1) + "行数据校验错误");
        //获取所有的职务
        List<DtoArea> dbArea = dbAreaTl.get();
        //获取所有企业数据
        List<DtoEnterprise> dbEnterprise = dbEnterpriseTl.get();
        //必填校验
        importUtils.checkIsNull(result, enterprise.getName(), "企业名称", failStr);
        if (StringUtil.isNotEmpty(enterprise.getIsPollutionStr()) && "是".equals(enterprise.getIsPollutionStr())){
            importUtils.checkIsNull(result, enterprise.getPollutionCode(), "污染源编号", failStr);
            importUtils.checkIsNull(result, enterprise.getPollutionType(), "污染源类型", failStr);
            //判断污染源类型是否存在
            isRepeatPollutionType(result,enterprise,failStr);
        }
        //导入编号重复判断
        List<DtoExpImpEnterprise> enterpriseTempList = enterpriseTl.get();
        if (StringUtil.isEmpty(enterpriseTempList)) {
            enterpriseTempList = new ArrayList<>();
        }
        isRepeatData(result, enterpriseTempList, enterprise, failStr, dbEnterprise);
        //校验区域是否必填
        checkAreaIsNull(result, enterprise, failStr);

        //判断区域是否存在
        isExistArea(result, enterprise, dbArea, failStr);

        //处理区域数据
        handlerData(dbArea, enterprise);
        //验证邮箱格式
        importUtils.checkEmail(result, enterprise.getEmail(), "邮箱", failStr);
        //处理校验结果字符
        String failString = failStr.toString().replaceFirst("；", ":");
        result.setMsg(failString);
        //添加到临时数据中
        enterpriseTempList.add(enterprise);
        enterpriseTl.set(enterpriseTempList);
        return result;
    }

    /**
     * 判断污染源客户类型是否存在
     *
     * @param result 校验结果
     * @param enterprise 客户数据
     * @param failStr 校验失败信息
     */
    private void isRepeatPollutionType(ExcelVerifyHandlerResult result, DtoExpImpEnterprise enterprise, StringBuilder failStr) {
        if (StringUtil.isNotEmpty(enterprise.getPollutionType())){
            Map<String, List<DtoCode>> codeMap = codeTl.get();
            List<DtoCode> pollutionSourceTypes = codeMap.get(LimConstants.ImportConstants.LIM_POLLUTION_SOURCE_TYPE);
            List<String> typeNames = pollutionSourceTypes.stream().map(DtoCode::getDictName).collect(Collectors.toList());
            String pollutionType = enterprise.getPollutionType();
            List<String> pollutionNames = new ArrayList<>();
            //获取到页面上填写的数据并处理为类型集合
            if (StringUtil.isNotEmpty(pollutionType)){
                if (pollutionType.contains("，") || pollutionType.contains(",")){
                    pollutionType = pollutionType.replace("，",",");
                    pollutionNames = Arrays.asList(pollutionType.split(","));
                }else{
                    pollutionNames = Stream.of(pollutionType).collect(Collectors.toList());
                }

            }
            List<String> finalPollutionNames = pollutionNames;
            List<String> existTypes = typeNames.stream().filter(finalPollutionNames::contains).collect(Collectors.toList());
            if (StringUtil.isEmpty(existTypes)){
                result.setSuccess(false);
                failStr.append("；污染源类型不存在");
            }
        }
    }

    /**
     * 判断区域是否必填
     *
     * @param result     校验结果
     * @param enterprise 导入的数据
     * @param failStr    校验结果字符串
     */
    private void checkAreaIsNull(ExcelVerifyHandlerResult result, DtoExpImpEnterprise enterprise, StringBuilder failStr) {
        //当填市级区域时，省级为必填项
        if (StringUtil.isNotEmpty(enterprise.getCityAreaName())) {
            importUtils.checkIsNull(result, enterprise.getProvinceAreaName(), "省级区域", failStr);
        }
        //当填写县、区级区域时，省级与市级区域为必填项
        if (StringUtil.isNotEmpty(enterprise.getAreaName())) {
            importUtils.checkIsNull(result, enterprise.getProvinceAreaName(), "省级区域", failStr);
            importUtils.checkIsNull(result, enterprise.getCityAreaName(), "市级区域", failStr);
        }
    }

    /**
     * 处理区域id
     *
     * @param dbArea     区域数据
     * @param enterprise 导入数据
     */
    private void handlerData(List<DtoArea> dbArea, DtoExpImpEnterprise enterprise) {
        //获取到导入的省、市、县、区
        String provinceName = enterprise.getProvinceAreaName();
        String cityName = enterprise.getCityAreaName();
        String areaName = enterprise.getAreaName();
        //处理省级区域id
        if (StringUtil.isNotEmpty(provinceName)) {
            Optional<DtoArea> areaOp = dbArea.stream().filter(p -> provinceName.equals(p.getAreaName())).findFirst();
            areaOp.ifPresent(p -> enterprise.setProvinceId(p.getId()));
        }
        //处理市级区域id
        if (StringUtil.isNotEmpty(cityName) && StringUtil.isNotEmpty(enterprise.getProvinceId())) {
            Optional<DtoArea> areaOp = dbArea.stream().filter(p -> cityName.equals(p.getAreaName())
                    && enterprise.getProvinceId().equals(p.getParentId())).findFirst();
            areaOp.ifPresent(p -> enterprise.setCityId(p.getId()));
        }
        //处理县、区级区域id
        if (StringUtil.isNotEmpty(areaName) && StringUtil.isNotEmpty(enterprise.getCityId())) {
            Optional<DtoArea> areaOp = dbArea.stream().filter(p -> areaName.equals(p.getAreaName())
                    && enterprise.getCityId().equals(p.getParentId())).findFirst();
            areaOp.ifPresent(p -> enterprise.setAreaId(p.getId()));
        }
    }

    /**
     * 判断填写区域是否存在
     *
     * @param result           校验结果
     * @param importEnterprise 导入的企业数据
     * @param dbArea           所有区域信息
     * @param failStr          错误信息
     */
    private void isExistArea(ExcelVerifyHandlerResult result, DtoExpImpEnterprise importEnterprise, List<DtoArea> dbArea, StringBuilder failStr) {
        Map<String, List<DtoArea>> areaGroup = dbArea.stream().collect(Collectors.groupingBy(DtoArea::getAreaName));
        String provinceName = importEnterprise.getProvinceAreaName();
        String cityName = importEnterprise.getCityAreaName();
        String areaName = importEnterprise.getAreaName();
        judgeArea(provinceName, areaGroup, result, failStr);
        judgeArea(cityName, areaGroup, result, failStr);
        judgeArea(areaName, areaGroup, result, failStr);
    }

    /**
     * 判断区域是否存在
     *
     * @param name      区域名称
     * @param areaGroup 区域分组数据
     * @param result    校验结果
     * @param failStr   错误信息
     */
    private void judgeArea(String name, Map<String, List<DtoArea>> areaGroup, ExcelVerifyHandlerResult result, StringBuilder failStr) {
        List<DtoArea> isExistArea;
        if (StringUtil.isNotEmpty(name)) {
            isExistArea = areaGroup.get(name);
            if (StringUtil.isEmpty(isExistArea)) {
                result.setSuccess(false);
                failStr.append("；").append(name).append("在系统中不存在");
            }
        }
    }


    /**
     * 重复数据校验
     *
     * @param result             校验结果
     * @param enterpriseTempList 临时数据
     * @param enterprise         实体
     * @param failStr            校验错误信息
     */
    private void isRepeatData(ExcelVerifyHandlerResult result, List<DtoExpImpEnterprise> enterpriseTempList, DtoExpImpEnterprise enterprise, StringBuilder failStr,List<DtoEnterprise> dbEnterprise) {
        if (StringUtil.isNotEmpty(enterprise.getSocialCreditCode())) {
            List<Integer> repeatNum = enterpriseTempList.stream()
                    .filter(p -> enterprise.getSocialCreditCode().equals(p.getSocialCreditCode()) && enterprise.getName().equals(p.getName()))
                    .map(DtoExpImpEnterprise::getRowNum).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(repeatNum)) {
                result.setSuccess(false);
                failStr.append("；与第").append(repeatNum).append("行企业名称以及社会信用代码重复");
            }
        }
        // id 为空时校验重复数据
        if (StringUtil.isEmpty(enterprise.getId())){
            List<DtoEnterprise> isExistEnterprise;
            if (StringUtil.isNotEmpty(enterprise.getSocialCreditCode())) {
                isExistEnterprise = dbEnterprise.stream().filter(p -> enterprise.getSocialCreditCode().equals(p.getSocialCreditCode()) && enterprise.getName().equals(p.getName())).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(isExistEnterprise)) {
                    result.setSuccess(false);
                    failStr.append("；企业名称以及社会信用代码在系统中已存在");
                }
            }
        }
    }

}
