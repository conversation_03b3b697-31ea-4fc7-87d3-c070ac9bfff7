package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsFormula;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * 参数公式管理仓储
 * <AUTHOR>
 * @version V1.0.0 2019/5/14
 * @since V100R001
 */
public interface ParamsFormulaRepository extends IBaseJpaRepository<DtoParamsFormula, String>, LimsRepository<DtoParamsFormula, String> {

    /**
     * 重写获取所有的方法
     *
     * @return 返回未删除的公式
     */
    @Override
    @Query("select p from DtoParamsFormula p where p.isDeleted = 0")
    List<DtoParamsFormula> findAll();

    /**
     * 根据参数公式idList查询公式
     *
     * @param ids 主键id
     * @return 返回公式
     */
    @Override
    @Query("select p from DtoParamsFormula p where p.isDeleted = 0 and p.id in :ids")
    List<DtoParamsFormula> findAll(@Param("ids") Iterable<String> ids);


    /**
     * 返回所有的带假删的方法信息
     *
     * @return 返回带删除的方法信息
     */
    @Query("select p from DtoParamsFormula p")
    List<DtoParamsFormula> findAllDeleted();

    /**
     * 返回所以的带删除的公式
     *
     * @param ids 方法的ids
     * @return 返回带删除的公式
     */
    @Query("select p from DtoParamsFormula p where p.id in :ids")
    List<DtoParamsFormula> findAllDeleted(@Param("ids") List<String> ids);

    /**
     * 根据对象id 查询相关的公式
     *
     * @param objectIds 对象ids
     * @return 返回相应的公式
     */
    @Query("select p from DtoParamsFormula p where p.isDeleted = 0 and p.objectId in :objectIds order by  p.configDate desc ")
    List<DtoParamsFormula> findByObjectIds(@Param("objectIds") List<String> objectIds);

    /**
     * 获取带赋值的数据的公式
     *
     * @param objectIds    公式ids
     * @param sampleTypeId 检测类型id
     * @return 返回数据
     */
    @Query("select p from DtoParamsFormula p where p.isDeleted = 0 and p.objectId in :objectIds and p.sampleTypeId = :sampleTypeId")
    List<DtoParamsFormula> findByObjectIdsAndSampleTypeId(@Param("objectIds") List<String> objectIds, @Param("sampleTypeId") String sampleTypeId);

    /**
     * 条件获取测试项目公式
     * @param objectId     对象id
     * @param objectType   对象类型
     * @param formula      公式
     * @return             测试项目公式列表
     */
    List<DtoParamsFormula> findAllByObjectIdAndObjectTypeAndFormulaAndIsDeletedFalse(String objectId,Integer objectType,String formula);

    /**
     * 根据测试项目获取所有测试项目公式
     * @param testIds     测试项目标识
     * @param objectType  测试羡慕类型
     * @return            测试项目公式
     */
    List<DtoParamsFormula> findAllByObjectIdInAndObjectTypeAndIsDeletedFalse(List<String> testIds,Integer objectType);

    /**
     * 根据对象id 查询相关的公式
     *
     * @param objectIds 对象ids
     * @return 返回相应的公式
     */
    @Query("select p from DtoParamsFormula p where  p.objectId in :objectIds ")
    List<DtoParamsFormula> findByObjectIdsDeleted(@Param("objectIds") List<String> objectIds);

    /**
     * 根据主键id真删
     *
     * @param ids 对象ids
     * @return 返回相应的公式
     */
    @Transactional
    @Modifying
    @Query("delete from DtoParamsFormula p where p.id  in :ids ")
    Integer deleteByIdIn(@Param("ids") Collection<String> ids);
}