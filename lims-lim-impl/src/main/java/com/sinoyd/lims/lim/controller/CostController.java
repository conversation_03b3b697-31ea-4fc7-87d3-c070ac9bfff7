package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.dto.rcc.DtoCost;
import com.sinoyd.lims.lim.service.CostService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 检测费服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/15
 * @since V100R001
 */
 @Api(tags = "Cost服务")
 @RestController
 @RequestMapping("api/lim/cost")
 @Validated
 public class CostController extends BaseJpaController<DtoCost, String,CostService> {
    /**
     * 根据检测类型id查询检测费
     *
     * @param sampleTypeId 检测类型id
     * @return RestResponse<List < Cost>>
     */
    @ApiOperation(value = "根据检测类型id查询检测费", notes = "根据检测类型id查询检测费")
    @GetMapping(path = "/sampleType")
    public RestResponse<List<DtoCost>> findBySampleType(@RequestParam(name = "sampleTypeId") String sampleTypeId, @RequestParam(name = "analyzeItemKey") String analyzeItemKey,
                                                        @RequestParam(name = "analyzeMethodKey") String analyzeMethodKey,
                                                        @RequestParam(name = "cert") Integer cert) {
        RestResponse<List<DtoCost>> restResponse = new RestResponse<>();
        List<DtoCost> costs = service.findBySampleType(sampleTypeId, analyzeItemKey,analyzeMethodKey,cert);
        restResponse.setRestStatus(StringUtil.isEmpty(costs) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(costs);
        restResponse.setCount(StringUtil.isEmpty(costs) ? 0 : costs.size());
        return restResponse;
    }

    /**
     * 新增检测费
     *
     * @param cost 实体列表
     * @return RestResponse<DtoCost>
     */
    @ApiOperation(value = "新增检测费", notes = "新增检测费")
    @PostMapping
    public RestResponse<DtoCost> create(@Validated @RequestBody DtoCost cost) {
        RestResponse<DtoCost> restResponse = new RestResponse<>();
        restResponse.setData(service.save(cost));
        return restResponse;
    }

    /**
     * 修改检测费
     *
     * @param cost 实体列表
     * @return RestResponse<DtoCost>
     */
    @ApiOperation(value = "修改检测费", notes = "修改检测费")
    @PutMapping
    public RestResponse<DtoCost> update(@Validated @RequestBody DtoCost cost) {
        RestResponse<DtoCost> restResponse = new RestResponse<>();
        restResponse.setData(service.update(cost));
        return restResponse;
    }

    /**
     * 批量保存检测费
     *
     * @param cost 实体列表
     * @return RestResponse<List<DtoCost>>
     */
    @ApiOperation(value = "批量保存检测费", notes = "批量保存检测费")
    @PostMapping(path = "/batch")
    public RestResponse<List<DtoCost>> save(@Validated @RequestBody List<DtoCost> cost) {
        RestResponse<List<DtoCost>> restResponse = new RestResponse<>();
        List<DtoCost> costs = service.save(cost);
        restResponse.setData(costs);
        restResponse.setCount(StringUtil.isEmpty(costs) ? 0 : costs.size());
        return restResponse;
    }
}