package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.base.dto.customer.DtoImportPerson;
import com.sinoyd.frame.util.PinYinUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.entity.Person;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.List;


/**
 * DtoPerson实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_Person")
 @Data
 @DynamicInsert
 public  class DtoPerson extends Person {

    @Transient
    private List<DtoPersonCert> certs;

    /**
     * base64Content的内容
     */
    @Transient
    private String base64Content;


    /**
     * 工作年限
     */
    @Transient
    private Integer workYear;

    /**
     * 部门名称
     */
    @Transient
    private String deptName;

    /**
     * 职称名称
     */
    @Transient
    private String technicalTitleName;

    /**
     * 枚举EnumPersonStatus:1代表在职 2代表离职 3代表休假
     */
    @Transient
    private String statusName;

    /**
     * 性别
     */
    @Transient
    private String sexText;

    /**
     * 头像路径
     */
    @Transient
    private String photoPath;

    /**
     * 角色
     */
    @Transient
    private String roleNames;

    /**
     * 用户名
     */
    @Transient
    private String userName;

    /**
     * 设置导入数据
     * @param importPerson 导入的数据
     */
    public void importToPersonEntity(DtoImportPerson importPerson){
        setUserName(importPerson.getUserName());
        setCName(importPerson.getChineseName());
        setFullPinYin(PinYinUtil.getFullSpell(importPerson.getChineseName()));
        setPinYin(PinYinUtil.getFirstSpell(importPerson.getChineseName()));
        setUserNo(importPerson.getUserNo());
        setPostId(importPerson.getPostId());
        setTechnicalTitleId(importPerson.getTechnicalTitleId() == null ? UUIDHelper.GUID_EMPTY : importPerson.getTechnicalTitleId());
        setSex(importPerson.getSex().trim().equals("男") ? 1 : 2);
        setNativePlace(importPerson.getNativePlace());
        setIdCard(importPerson.getIdCard());
        setPoliticalFace(importPerson.getPoliticalFace());
        setMobile(importPerson.getMobile());
        setHomeTel(importPerson.getHomeTel());
        setEmail(importPerson.getEmail());
        setHomeAddress(importPerson.getHomeAddress());
        setDegree(importPerson.getDegree() == null ? UUIDHelper.GUID_EMPTY : importPerson.getDegree());
        setSpecialty(importPerson.getSpecialty());
        setSchool(importPerson.getSchool());
        setStatus(importPerson.getStatus() == null ? 1 : Integer.valueOf(importPerson.getStatus()));
        setEmergentLinkMan(importPerson.getEmergentLinkMan());
        setContactMethod(importPerson.getContactMethod());
        setRoleNames(importPerson.getRoleNames());
        setYearsInThePosition(BigDecimal.ZERO);
    }

}