package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 原始记录单导出参数
 *
 * <AUTHOR>
 * @version V1.0.0 2019-05-13
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DataParamsConfigExportCriteria extends BaseCriteria {
    /**
     *  原始记录单标识
     */
    private String recordConfigId;

    /**
     * 数据参数标识列表
     */
    private List<String> paramsConfigIdList;

    @Override
    public String getCondition() {
        return null;
    }
}
