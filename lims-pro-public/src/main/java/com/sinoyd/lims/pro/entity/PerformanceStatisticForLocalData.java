package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;


/**
 * PerformanceStatisticForLocalData实体
 * <AUTHOR>
 * @version V1.0.0 2020/2/21
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="PerformanceStatisticForLocalData")
 @Data
 public  class PerformanceStatisticForLocalData implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  PerformanceStatisticForLocalData() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 送样单id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("送样单id")
	private String receiveId;
    
    /**
    * 送样单号
    */
    @Column(length=20)
    @ApiModelProperty("送样单号")
	private String recordCode;
    
    /**
    * 测试项目id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("测试项目id")
	private String testId;
    
    /**
    * 分析人Id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("送样登记Id")
	private String recorderId;
    
    /**
    * 送样日期
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("送样日期")
	private Date sendTime;

   /**
    * 采样日期
  */
   @Column(nullable=false)
   @ColumnDefault("'1753-1-1'")
   @ApiModelProperty("采样日期")
   private Date samplingTime;

    /**
    * 检测类型id
    */
    @Column(length=50)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("检测类型id")
	private String sampleTypeId;
    
    /**
    * 分析项目名称
    */
    @Column(length=100)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("分析项目名称")
	private String redAnalyzeItemName;
    
    /**
    * 分析方法名称
    */
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("分析方法名称")
	private String redAnalyzeMethodName;
    
    /**
    * 分析项目id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("分析项目id")
	private String analyseItemId;
    
    /**
    * 分析方法id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("分析方法id")
	private String analyzeMethodId;
    
    /**
    * 样品数
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("样品数")
	private Integer sample= 0;
    
    /**
    * 全程序空白样品数
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("全程序空白样品数")
	private Integer localeGap= 0;
    
    /**
    * 室内平行样品数
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("室内平行样品数")
	private Integer parallel= 0;
    
    /**
    * 有效数据
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("有效数据")
	private Integer valid= 0;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
 }