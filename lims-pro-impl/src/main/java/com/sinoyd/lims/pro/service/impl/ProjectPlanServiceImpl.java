package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoProjectPlan2Person;
import com.sinoyd.lims.lim.repository.lims.ProjectPlan2PersonRepository;
import com.sinoyd.lims.pro.dto.DtoProjectPlan;
import com.sinoyd.lims.pro.repository.ProjectPlanRepository;
import com.sinoyd.lims.pro.service.ProjectPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * 项目计划操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/18
 * @since V100R001
 */
 @Service
public class ProjectPlanServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoProjectPlan,String,ProjectPlanRepository> implements ProjectPlanService {

     private ProjectPlan2PersonRepository projectPlan2PersonRepository;

    @Override
    public void findByPage(PageBean<DtoProjectPlan> pb, BaseCriteria projectPlanCriteria) {
        pb.setEntityName("DtoProjectPlan a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, projectPlanCriteria);
    }

    @Override
    public DtoProjectPlan save(DtoProjectPlan entity) {
        updatePersonList(entity);
        return super.save(entity);
    }

    @Override
    public DtoProjectPlan update(DtoProjectPlan entity) {
        updatePersonList(entity);
        return super.update(entity);
    }

    @Override
    public List<DtoProjectPlan> findByProjectIds(List<String> projectIds) {
        return  repository.findByProjectIdIn(projectIds);
    }

    private void updatePersonList(DtoProjectPlan entity){
        List<DtoProjectPlan2Person> waitDeleteList = projectPlan2PersonRepository.findAllByProjectPlanId(entity.getId());
        projectPlan2PersonRepository.delete(waitDeleteList);
        if(StringUtil.isNotEmpty(entity.getAssessPersonIds())){
            List<DtoProjectPlan2Person> assessPersonList = new ArrayList<>();
            for (String personId:entity.getAssessPersonIds()) {
                DtoProjectPlan2Person plan2Person = new DtoProjectPlan2Person();
                plan2Person.setProjectPlanId(entity.getId());
                plan2Person.setAssessPersonId(personId);
                assessPersonList.add(plan2Person);
            }
            projectPlan2PersonRepository.save(assessPersonList);
        }
    }

    @Autowired
    public void setProjectPlan2PersonRepository(ProjectPlan2PersonRepository projectPlan2PersonRepository) {
        this.projectPlan2PersonRepository = projectPlan2PersonRepository;
    }
}