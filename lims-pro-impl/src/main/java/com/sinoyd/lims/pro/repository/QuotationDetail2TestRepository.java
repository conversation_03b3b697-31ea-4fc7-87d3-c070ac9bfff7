package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.DtoQuotationDetail2Test;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

import java.util.List;


/**
 * QuotationDetail2Test数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2022/5/23
 * @since V100R001
 */
public interface QuotationDetail2TestRepository extends IBaseJpaPhysicalDeleteRepository<DtoQuotationDetail2Test, String> {

    /**
     * 根据明细获取子项测试项目
     *
     * @param detailId 明细id
     * @return 子项测试项目
     */
    List<DtoQuotationDetail2Test> findByDetailId(String detailId);

    /**
     * 根据明细获取子项测试项目
     *
     * @param detailIds 明细ids
     * @return 子项测试项目
     */
    List<DtoQuotationDetail2Test> findByDetailIdIn(List<String> detailIds);

    /**
     * 根据明细和测试项目获取子项测试项目
     *
     * @param detailId 明细id
     * @param testIds  测试项目id
     * @return 子项测试项目
     */
    List<DtoQuotationDetail2Test> findByDetailIdAndTestIdIn(String detailId, List<String> testIds);
}