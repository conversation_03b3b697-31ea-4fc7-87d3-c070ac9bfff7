package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoProjectApproval;

import java.util.List;

/**
 * ProjectApproval操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/02
 * @since V100R001
 */
public interface ProjectApprovalService extends IBaseJpaService<DtoProjectApproval, String> {

    /**
     * 选择项目
     * @param projectId 项目id
     */
    DtoProjectApproval selectProject(String projectId);

    /**
     * 提交項目
     * @param approveIds 審核ids
     */
    void commit(List<String> approveIds, String personId, String comment);

    /**
     * 審核項目
     * @param isPass 是否通過
     * @param approveIds 審核ids
     */
    void audit(Boolean isPass, List<String> approveIds, String comment);

    /**
     * 检测当前项目是否存在在申请中方案
     * @param projectIds 项目id
     */
    void checkCondition(List<String> projectIds);

    /**
     * 提交校验
     * @param approveIds 申请id
     */
    Boolean verifyCommit(List<String> approveIds);

}
