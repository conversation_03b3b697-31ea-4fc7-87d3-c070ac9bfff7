package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoAutoTaskPlan;

/**
 * 自动任务下达数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/15
 * @since V100R001
 */
public interface AutoTaskPlanRepository extends IBaseJpaPhysicalDeleteRepository<DtoAutoTaskPlan, String> {

    /**
     * 根据任务编码和主键统计
     *
     * @param taskCode 任务编码
     * @param id 主键
     * @return 数量
     */
    Integer countByTaskCodeAndIdNot(String taskCode, String id);

}
