package com.sinoyd.lims.pro.controller;

import com.sinoyd.lims.pro.criteria.WorkSheetCurveCriteria;
import com.sinoyd.lims.pro.dto.DtoWorkSheetFolder;
import com.sinoyd.lims.pro.dto.customer.DtoWorkSheetCalibrationCurveTemp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.WorkSheetCalibrationCurveService;
import com.sinoyd.lims.pro.criteria.WorkSheetCalibrationCurveCriteria;
import com.sinoyd.lims.pro.dto.DtoWorkSheetCalibrationCurve;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * WorkSheetCalibrationCurve服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: WorkSheetCalibrationCurve服务")
 @RestController
 @RequestMapping("api/pro/workSheetCalibrationCurve")
 public class WorkSheetCalibrationCurveController extends BaseJpaController<DtoWorkSheetCalibrationCurve, String,WorkSheetCalibrationCurveService> {


    /**
     * 分页动态条件查询WorkSheetCalibrationCurve
     * @param workSheetCalibrationCurveCriteria 条件参数
     * @return RestResponse<List<WorkSheetCalibrationCurve>>
     */
     @ApiOperation(value = "分页动态条件查询WorkSheetCalibrationCurve", notes = "分页动态条件查询WorkSheetCalibrationCurve")
     @GetMapping
     public RestResponse<List<DtoWorkSheetCalibrationCurve>> findByPage(WorkSheetCalibrationCurveCriteria workSheetCalibrationCurveCriteria) {
         PageBean<DtoWorkSheetCalibrationCurve> pageBean = super.getPageBean();
         RestResponse<List<DtoWorkSheetCalibrationCurve>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, workSheetCalibrationCurveCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

    /**
     * 分页动态条件查询检测单
     * @param workSheetCurveCriteria 条件参数
     * @return RestResponse<List<DtoWorkSheetFolder>>
     */
    @ApiOperation(value = "分页动态条件查询检测单", notes = "分页动态条件查询检测单")
    @GetMapping("/workSheetFolder")
    public RestResponse<List<DtoWorkSheetFolder>> findWorkSheetFolderByPage(WorkSheetCurveCriteria workSheetCurveCriteria) {
        PageBean<DtoWorkSheetFolder> pageBean = super.getPageBean();
        RestResponse<List<DtoWorkSheetFolder>> restResponse = new RestResponse<>();
        service.findWorkSheetFolderByPage(pageBean, workSheetCurveCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

     /**
     * 按主键查询WorkSheetCalibrationCurve
     * @param id 主键id
     * @return RestResponse<DtoWorkSheetCalibrationCurve>
     */
     @ApiOperation(value = "按主键查询WorkSheetCalibrationCurve", notes = "按主键查询WorkSheetCalibrationCurve")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoWorkSheetCalibrationCurve> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoWorkSheetCalibrationCurve> restResponse = new RestResponse<>();
         DtoWorkSheetCalibrationCurve workSheetCalibrationCurve = service.findOne(id);
         restResponse.setData(workSheetCalibrationCurve);
         restResponse.setRestStatus(StringUtil.isNull(workSheetCalibrationCurve) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增WorkSheetCalibrationCurve
     * @param workSheetCalibrationCurve 实体列表
     * @return RestResponse<DtoWorkSheetCalibrationCurveTemp>
     */
     @ApiOperation(value = "新增WorkSheetCalibrationCurve", notes = "新增WorkSheetCalibrationCurve")
     @PostMapping
     public RestResponse<DtoWorkSheetCalibrationCurveTemp> create(@RequestBody DtoWorkSheetCalibrationCurveTemp workSheetCalibrationCurve) {
         RestResponse<DtoWorkSheetCalibrationCurveTemp> restResponse = new RestResponse<>();
         restResponse.setData(service.saveWorkSheetCalibrationCurve(workSheetCalibrationCurve));
         return restResponse;
     }

    /**
     * "根据id批量删除WorkSheetCalibrationCurve
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除WorkSheetCalibrationCurve", notes = "根据id批量删除WorkSheetCalibrationCurve")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 按检测id和子检测单id获取校准曲线信息
     * @param testId 测试项目id
     * @param workSheetId 检测单id
     * @return RestResponse<DtoWorkSheetCalibrationCurve>
     */
    @ApiOperation(value = "按检测id和子检测单id获取校准曲线信息", notes = "按检测id和子检测单id获取校准曲线信息")
    @GetMapping("/detail")
    public RestResponse<DtoWorkSheetCalibrationCurveTemp> findByTestIdAndWorkSheetId( @RequestParam(name = "workSheetId") String workSheetId,
                                                                                      @RequestParam(name = "testId") String testId) {
        RestResponse<DtoWorkSheetCalibrationCurveTemp> restResponse = new RestResponse<>();
        DtoWorkSheetCalibrationCurveTemp workSheetCalibrationCurve = service.findByTestIdAndWorkSheetId(workSheetId, testId);
        restResponse.setData(workSheetCalibrationCurve);
        restResponse.setRestStatus(StringUtil.isNull(workSheetCalibrationCurve) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }
 }