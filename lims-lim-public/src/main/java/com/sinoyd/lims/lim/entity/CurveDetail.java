package com.sinoyd.lims.lim.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;


/**
 * CurveDetail实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@MappedSuperclass
@ApiModel(description = "CurveDetail")
@Data
public class CurveDetail extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public CurveDetail() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 标准曲线标识
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("标准曲线标识")
    private String curveId;

    /**
     * 标准溶液加入体积
     */
    @Column(length = 50)
    @ApiModelProperty("标准溶液加入体积")
    @Length(message = "标准溶液加入体积{validation.message.length}", max = 50)
    private String vValue;

    /**
     * 含量值
     */
    @Column(length = 50)
    @ApiModelProperty("含量值")
    @Length(message = "含量值{validation.message.length}", max = 50)
    private String hValue;

    /**
     * X值
     */
    @Column(length = 50)
    @ApiModelProperty("X值")
    @Length(message = "X值{validation.message.length}", max = 50)
    private String xValue;

    /**
     * Y值
     */
    @Column(length = 50)
    @ApiModelProperty("Y值")
    @Length(message = "Y值{validation.message.length}", max = 50)
    private String yValue;

    /**
     * 排序
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("排序")
    private Integer orderNum;

    /**
     * 备注
     */
    @Column(length = 1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
    private String remark;

    /**
     * 220吸光度
     */
    @Column(length = 50)
    @ApiModelProperty("220吸光度")
    @Length(message = "220吸光度{validation.message.length}", max = 50)
    private String blank220;

    /**
     * 275吸光度
     */
    @Column(length = 50)
    @ApiModelProperty("275吸光度")
    @Length(message = "275吸光度{validation.message.length}", max = 50)
    private String blank275;

    /**
     * 减空白吸光度
     */
    @Column(length = 50)
    @ApiModelProperty("减空白吸光度")
    @Length(message = "减空白吸光度{validation.message.length}", max = 50)
    private String lessBlank;

    /**
     * 背景吸光度
     */
    @Column(length = 50)
    @ApiModelProperty("背景吸光度")
    @Length(message = "背景吸光度{validation.message.length}", max = 50)
    private String aValueBG;

    /**
     * 显色吸光度A1
     */
    @Column(length = 50)
    @ApiModelProperty("显色吸光度A1")
    @Length(message = "显色吸光度A1{validation.message.length}", max = 50)
    private String colorAbsorbance;

    /**
     * 干扰吸光度A2
     */
    @Column(length = 50)
    @ApiModelProperty("干扰吸光度A2")
    @Length(message = "干扰吸光度A2{validation.message.length}", max = 50)
    private String interfereAbsorbance;

    /**
     * 空白吸光度A0
     */
    @Column(length = 50)
    @ApiModelProperty("空白吸光度A0")
    @Length(message = "空白吸光度A0{validation.message.length}", max = 50)
    private String blankAbsorbance;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 采集编号
     */
    @Column
    @ApiModelProperty("采集编号")
    @Length(message = "采集编号{validation.message.length}", max = 150)
    private String code;


    /**
     * 是否为量纲行
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否为量纲行")
    private Boolean isDimensionRow = false;
}