package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoFixedProperty;

/**
 * 固定资产repository
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/13
 * @since V100R001
 */
public interface FixedAssetsRepository extends IBaseJpaPhysicalDeleteRepository<DtoFixedProperty, String> {

    /**
     * 根据资产编码查询
     * @param assetsNo
     * @return
     */
    DtoFixedProperty findByAssetsNo(String assetsNo);
}
