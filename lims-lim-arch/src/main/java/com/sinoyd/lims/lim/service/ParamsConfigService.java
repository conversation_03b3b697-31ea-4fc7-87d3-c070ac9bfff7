package com.sinoyd.lims.lim.service;

import java.util.Collection;
import java.util.List;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;

/**
 * 检测参数配置接口
 * <AUTHOR>
 * @version V1.0.0 2018/12/14
 * @since V100R001
 */
public interface ParamsConfigService extends IBaseJpaService<DtoParamsConfig, String> {
    /**
     * 根据检测小类id获取参数配置信息
     *
     * @param sampleTypeId 检测小类id
     * @return 对应检测小类id下的参数配置信息
     */
    List<DtoParamsConfig> findBySampleTypeId(String sampleTypeId);

    /**
     * 根据检测小类id及分析项目id集合获取参数配置信息
     *
     * @param sampleTypeId   检测小类id
     * @param analyzeItemIds 分析项目id集合
     * @return 对应检测小类id和分析项目id集合下的参数配置信息
     */
    List<DtoParamsConfig> findBySampleTypeId(String sampleTypeId, Collection<String> analyzeItemIds);

    /**
     * 根据样品类型和测试项目获取参数
     * @param sampleTypeId 检测小类id
     * @param testIds 测试项目id
     * @return 参数信息
     */
    List<DtoParamsConfig> findByTypeIdAndTestIds(String sampleTypeId, List<String> testIds);

    /**
     * 根据父级id集合、分析项目id集合获取配置信息
     *
     * @param parentIds      父级id集合
     * @param analyzeItemIds 分析项目id集合
     * @return 对应父级id集合、分析项目id集合获取配置信息
     */
    List<DtoParamsConfig> findByParentIdInAndAnalyzeItemIdInAndIsShowTrue(Collection<String> parentIds, Collection<String> analyzeItemIds);

    /**
     * 更新参数配置（包含相关分析项目配置的更新）
     *
     * @param paramsConfig 参数配置实体
     * @return 更新后的参数配置实体
     */
    DtoParamsConfig updateConfig(DtoParamsConfig paramsConfig);

    /**
     * 根据关联id集合获取参数配置信息
     *
     * @param objIds 关联id集合
     * @param type   关联类型
     * @return 对应关联id集合下的参数配置信息
     */
    List<DtoParamsConfig> findByObjIdInAndType(List<String> objIds, Integer type);


    /**
     * 根据Id获取有参数配置信息
     *
     * @param ids 方法ids
     * @return 返回带有假删的参数配置信息
     */
    List<DtoParamsConfig> findAllDeleted(List<String> ids);


    /**
     * 含假删的参数配置信息
     *
     * @return 返回参数配置信息
     */
    List<DtoParamsConfig> findAllDeleted();


    /**
     * 相应的参数数据
     *
     * @param copyTestId 复制的测试项目id
     * @param testIds    选择的测试项目ids
     */
    void copyParamsConfig(String copyTestId, List<String> testIds);


    /**
     * 针对检测类型的参数特殊处理
     *
     * @param objId          当前数据id
     * @param parentObjectId 父级的id
     * @param type           类型
     * @return 返回相应的参数数据
     */
    List<DtoParamsConfig> findSampleTypeParams(String objId, String parentObjectId, Integer type);

    /**
     * 针对检测类型的参数特殊处理
     *
     * @param objIds          当前数据id
     * @param parentObjectIds 父级的id
     * @param type           类型
     * @return 返回相应的参数数据
     */
    List<DtoParamsConfig> findSampleTypeParams(List<String> objIds, List<String> parentObjectIds, Integer type);

    /**
     * 将同名的测试项目公式参数的公式内容同步到个性化公式中
     * @param recordConfigId 原始记录单标识
     * @param paramsConfigIdList 数据参数标识列表
     */
    void formulaSync(String recordConfigId,List<String> paramsConfigIdList);

    /**
     * 复制检测类型参数
     * @param objectId  检测类型标识
     * @param list      参数列表
     * @return          参数列表
     */
    List<DtoParamsConfig> copyParamsConfigList(String objectId, List<DtoParamsConfig> list);
}