package com.sinoyd.lims.instrument.parse.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.instrument.parse.dto.DtoParseFileApp;
import com.sinoyd.lims.instrument.parse.service.ParseFileAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;


/**
 * FileApp服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
@Validated
@Api(tags = "示例: FileApp服务")
@RestController
@RequestMapping("api/pro/fileApp")
public class ParseFileAppController extends BaseJpaController<DtoParseFileApp, String, ParseFileAppService> {

    /**
     * 批量上传文件
     *
     * @param appId   应用id
     * @param request 携带文件的请求
     * @return RestResponse<DtoFileApp>
     */
    @ApiOperation(value = "上传文件", notes = "上传文件")
    @PostMapping("/file/{appId}")
    public RestResponse<String> upload(@PathVariable(name = "appId") String appId,
                                       HttpServletRequest request) {
        RestResponse<String> restResponse = new RestResponse<>();
        Boolean result = service.upload(appId, request);
        restResponse.setMsg(result ? ERestStatus.SUCCESS.getMsg() : "上传失败");
        return restResponse;
    }
}