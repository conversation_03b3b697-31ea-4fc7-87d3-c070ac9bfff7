package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.Params;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoParams实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_Params") 
 @Data
 @DynamicInsert
 public  class DtoParams extends Params {

 }