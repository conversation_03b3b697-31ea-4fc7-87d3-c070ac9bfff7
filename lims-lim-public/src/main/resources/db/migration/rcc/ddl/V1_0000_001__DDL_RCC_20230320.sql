-- ----------------------------------------------
-- -------- 归属于RCC部分的表相关DDL脚本 ------------
-- ---------------- Base模块 --------------------
-- ----------------------------------------------

create table TB_BASE_AnalyzeItem
(
    id              varchar(50)                                                not null comment '主键'
        primary key,
    analyzeItemName varchar(50) comment '名称',
    analyzeItemCode varchar(50) comment '分析因子编号',
    variableName    varchar(50) comment '变量名称（预留，前台改为别名）',
    pinYin          varchar(50) comment '拼音缩写',
    fullPinYin      varchar(100) comment '全拼',
    isDeleted       bit         default b'0'                                   not null comment '是否删除',
    orderNum        int         default 0                                      not null comment '排序值',
    orgId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate      datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate      datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_BASE_Dimension
(
    id              varchar(50)                                                not null comment '主键'
        primary key,
    dimensionName   varchar(50)                                                not null comment '量纲名称',
    dimensionTypeId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '量纲类型（使用常量Guid，常量名称BASE_DimensionType）',
    code            varchar(50) comment '编号',
    remark          varchar(1000) comment '备注',
    baseValue       decimal(38, 10)                                            not null comment '基准值',
    isDeleted       bit         default b'0'                                   not null comment '假删',
    orgId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate      datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate      datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_BASE_EvaluationAnalyzeItem
(
    id            varchar(50)                                                not null comment '主键'
        primary key,
    evaluationId  varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '评价标准id',
    analyzeItemId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '分析项目id',
    symbol        varchar(50) comment '符号(3.2预留)',
    unit          varchar(50) comment '单位(3.2预留)',
    remark        varchar(1000) comment '备注(3.2预留)',
    orderNum      int         default 0                                      not null comment '排序值(3.2预留',
    orgId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate    datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate    datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_BASE_EvaluationCriteria
(
    id           varchar(50)                                                not null comment '主键'
        primary key,
    name         varchar(100) comment '评价标准名称',
    code         varchar(20) comment '标准代码',
    categoryId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '标准类型（常量BASE_EvaluateType：国标和地标）',
    sampleTypeId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '检测类型',
    startTime    datetime    default '1753-01-01 00:00:00'                  null comment '实施时间',
    endTime      datetime    default '1753-01-01 00:00:00'                  null comment '废止时间',
    status       int         default 1                                      not null comment '标准状态（枚举EnumEvaluateCriteriaStatus：1代表有效 2代表废止）',
    applyRange   varchar(1000) comment '适用范围',
    remark       varchar(1000) comment '备注',
    isDeleted    bit         default b'0'                                   not null comment '假删',
    orgId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate   datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate   datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_BASE_EvaluationLevel
(
    id           varchar(50)                                                not null comment '主键'
        primary key,
    evaluationId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '评价标准id',
    parentId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '父级id',
    name         varchar(255) comment '等级名称（条件项名称）',
    describion   varchar(1000) comment '条件描述',
    orderNum     int         default 0                                      not null comment '排序值（条件编码）',
    orgId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate   datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate   datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_BASE_EvaluationValue
(
    id               varchar(50)                                                not null comment '主键'
        primary key,
    evaluationId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '评价标准id',
    levelId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '评价等级id',
    analyzeItemId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '分析项目id',
    upperLimit       varchar(50) comment '上限',
    upperLimitSymble varchar(50) comment '上限运算符',
    lowerLimit       varchar(50) comment '下限',
    lowerLimitSymble varchar(50) comment '下限运算符',
    remark           varchar(1000) comment '备注',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate       datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate       datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_BASE_IndustryType
(
    id           varchar(50)                                                not null comment '主键'
        primary key,
    parentId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '父级Id（Guid）（预留：多级行业类型使用）',
    industryName varchar(50) comment '名称',
    industryCode varchar(20) comment '编号',
    isDeleted    bit         default b'0'                                   not null comment '假删',
    orderNum     int         default 0                                      not null comment '排序值',
    remark       varchar(1000) comment '备注',
    orgId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate   datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate   datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;



create table TB_BASE_QualityControlLimit
(
    id             varchar(50)                                                not null comment '主键'
        primary key,
    testId         varchar(50)                                                not null comment '测试项目标识',
    rangeConfig    varchar(50) comment '检查项范围',
    judgingMethod  int         default 1                                      not null comment '评判方式（枚举EnumJudgingMethod：1.限值判定，2.小于检出限，3.回收率，4.相对偏差，5.相对误差，7.穿透率，6.绝对偏差）',
    allowLimit     varchar(50) comment '允许限值',
    qcGrade        int         default -1                                     not null comment '质控等级（枚举EnumQCGrade：1.外部质控  2.内部质控）',
    qcType         int         default -1                                     not null comment '质控类型（枚举EnumQCType：1.平行,2.空白,4.加标,8.标准,16.原样加原样,32.串联样,64.曲线校核,128.洗涤剂,256.运输空白,512.仪器空白,1024.试剂空白,2048.罐空白,4096.校正系数检验,8192.替代物,16384.阴性对照试验,32768.阳性对照试验,65536.采样介质空白,131072.空白加标;）',
    qcTypeName     varchar(50)                                                null comment '质控类型名称',
    substituteId   varchar(50)                                                not null comment '替代物标识',
    substituteName varchar(50)                                                null comment '代替物名称',
    formula        varchar(50)                                                null comment '穿透率公式',
    checkItem      int(50)     default 1                                      not null comment '检查项（枚举EnumCheckItemType:1.出证结果，2.公式参数）',
    checkItemOther varchar(50)                                                null comment '检查项内容',
    isCheckItem    int         default 1                                      not null comment '是否需要检查项',
    orgId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate     datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate     datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_BASE_Substitute
(
    id            varchar(50)                                                not null comment '主键'
        primary key,
    casCode       varchar(50)                                                not null comment 'CAS号',
    compoundName  varchar(100)                                               not null comment '化合物名称',
    addition      varchar(50)                                                not null comment '加入量',
    isDeleted     bit         default b'0'                                   not null comment '是否删除',
    orgId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate    datetime    default '1753-01-01 00:00:00'                  not null comment '创建时间',
    domainId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室id',
    modifier      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '最近修改人',
    modifyDate    datetime    default '1753-01-01 00:00:00'                  not null comment '最新修改时间',
    dimensionId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '量纲id',
    dimensionName varchar(100) comment '量纲名称'
) comment '替代物信息' ENGINE = InnoDB
                       CHARACTER SET = utf8
                       COLLATE = utf8_general_ci
                       ROW_FORMAT = Dynamic;

