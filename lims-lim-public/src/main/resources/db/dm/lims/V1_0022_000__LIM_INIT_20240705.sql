insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('00f6bb50-dc84-442c-8976-073ca5f6c4e1', '生活饮用水标准检测方法 金属指标 6.5 电感耦合等离子体发射光谱法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('0281ad54-9b29-4d40-ac33-ee8e7ce563da', '水质 32种元素的测定 电感耦合等离子体发射光谱法（水平法）', 'HJ 776-2015', 0, '1753-01-01', null, 1, 0, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-23 16:59:42', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 11:16:31', 0, '水质 32种元素的测定 电感耦合等离子体发射光谱法', 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('02c4e46e-394b-4227-b444-4fa29e4fd372', '多氯联苯（PCBs）的测定 气相色谱法 美国环保局方法', 'USEPA 8082A-2007', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'USEPA 8082A-2007', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 17:25:10', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('04c36cc2-dde0-4001-baba-d027f29d82df', '生活饮用水标准检测方法 金属指标 15.2 电感耦合等离子体发射光谱法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('05aa7fed-6557-4c4b-b061-7414ce4cb452', '城镇污水水质标准检验方法 46 总砷的测定 46.3 电感耦合等离子体发射光谱法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('05f7d418-bc83-4e93-8019-a1ab4cc62cf1', '城镇污水水质标准检验方法 25 硝酸盐氮的测定 25.3  离子色谱法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 15:39:10', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('0895b4c1-997a-4395-a253-ecac4426337d', '城镇污水水质标准检验方法 53 总钠的测定 电感耦合等离子体发射光谱法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('09a48fdd-a67e-43b7-b75c-d3f7f35d58bc', '固体废物 热灼减率的测定 重量法', 'HJ 1024-2019', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 1024-2019', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('09b3af97-456e-46e9-b2d2-a956932e0107', '土壤和沉积物 半挥发性有机物的测定 气相色谱-质谱法', 'HJ 834-2017', 0, '1753-01-01', null, 0, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 834-2017', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-18 16:05:30', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('0a73533d-b462-4f86-8022-6fa6fc221331', '城镇污水水质标准检验方法 52 总钾的测定 电感耦合等离子体发射光谱法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('0bedf75e-cb2c-499f-91d5-280132e484a5', '水质 氨氮的测定 连续流动-水杨酸分光光度法', 'HJ 665-2013', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 665-2013', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('0c95df68-8b2e-4c6c-8742-f992c680ffd0', '生活饮用水标准检验方法 金属指标 2.1 原子吸收分光光度法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 16:36:23', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('0d3e3adc-46c9-410d-8830-4887a59b1281', '危险废物鉴别标准－浸出毒性鉴别 附录D 固体废物 金属元素的测定 火焰原子吸收光谱法', 'GB 5085.3-2007', 1, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 15:58:30', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 16:01:50', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('0db1b86f-5ab3-4ccf-b75a-ae62d705834c', '水质 总氮、挥发酚、硫化物、阴离子表面活性剂和六价铬的测定 连续流动分析-分光光度法 SL/T788-2019', 'SL/T788-2019', 1, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'SL/T788-2019', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '46905a56-f770-42be-8d41-5b6c94f0ed52', '2023-09-20 09:43:28', '5f7bcf90feb545968424b0a872863876', '46905a56-f770-42be-8d41-5b6c94f0ed52', '2024-02-19 09:28:48', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('0e821b25-f391-4569-a9a3-4219ec31613b', '城镇污水水质标准检验方法 48 总锑的测定 48.2 电感耦合等离子体发射光谱法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('0ec81870-96ac-45ba-a7e4-fe0a3128befc', '城镇污水水质标准检验方法 41 总汞的测定 41.2 原子荧光光度法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('0f642874-24bf-4cfe-9100-6f906ffdfd26', '土壤 有机碳的测定 燃烧氧化-非分散红外法', 'HJ 695-2014', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 695-2014', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('104da863-e195-4daa-8549-e9802ccfe922', '固定污染源废气 挥发性有机物的测定 固相吸附-热脱附/气相色谱-质谱法', 'HJ 734-2014', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 734-2014', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 09:39:43', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('10694917-126c-4866-96dd-7c67e7a34531', '水质 钡的测定 石墨炉原子吸收分光光度法', 'HJ 602-2011', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 602-2011', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('1091b191-a092-4c4a-92ce-776c2ac9235f', '化学发光法 3.1.2（二）3.1.3（二）', '《空气和废气监测分析方法》（第四版）国家环保总局（2003年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', '《空气和废气监测分析方法》(第四版)', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 09:00:48', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('10da3309-8422-4fe8-b486-17398f219de3', '水质 硫化物的测定 气相分子吸收光谱法', 'HJ 200-2023', 0, '2024-06-01', 'HJ 200-2023', 1, 0, '', '00000000-0000-0000-0000-000000000000', null, null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-03 10:49:59', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-03 10:49:59', 0, '', 0, 0, '', 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('116f085d-1b1d-42c4-a967-6e4a5fb657ad', '水质 pH值的测定 电极法', 'HJ 1147-2020', 0, '1753-01-01', null, 1, 0, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-08-08 14:16:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-08-10 15:58:04', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('135e0736-a38a-454f-b030-e0a2022959fd', '水污染物排放总量监测技术规范', 'HJ/T 92-2002', 0, '1753-01-01', null, 1, 0, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:15:05', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:15:05', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('1375b857-b93c-43bf-8847-3f4b91b47253', '固体废物 铅和镉的测定 石墨炉原子吸收分光光度法', 'HJ 787-2016', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 787-2016', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('160c2d7d-ae4c-4b4f-ac69-844d567091bb', '电感耦合等离子体发射光谱法', '《土壤元素的近代分析方法》中国环境监测总站（1992年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', '《土壤元素的近代分析方法》', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 13:23:53', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('177d22ba-a3fc-4c05-b375-a5bee39174ad', '城镇污水处理厂大气污染物排放标准 附录B 环境空气和废气 硫化氢的测定 亚甲基蓝分光光度法', 'DB 31/982-2016', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'DB 31/982-2016', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('178b08ec-08e3-46dd-9260-89547d6ca0e1', '环境空气 降尘的测定 重量法', 'HJ 1221-2021', 0, '1753-01-01', null, 1, 1, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-04-29 13:09:09', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-04-29 13:09:09', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('17e38f64-1189-4c84-9cb0-be3cab5b1fab', '固定污染源排气中沥青烟的测定 重量法', 'HJ/T 45-1999', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ/T 45-1999', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('17ede669-0d6c-4824-aaaa-1c1726b64757', '有机氯农药的测定 气相色谱法 美国环保局方法', 'USEPA 8081B-2007', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'USEPA 8081B-2007', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 17:25:36', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('19156ea9-07da-498d-a6ec-7f63edb21c29', '固定污染源废气 氟化氢的测定 离子色谱法', 'HJ 688-2019', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 688-2019', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('193bca0c-61ea-4614-944e-0fccb6a04430', '生活饮用水标准检验方法 金属指标 11.1 无火焰原子吸收分光光度法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('1a37b974-5feb-450b-a037-6f1388af7f6a', '居住区大气中甲醛卫生检验标准方法 分光光度法', 'GB/T 16129-1995', 0, '1753-01-01', '', 1, 1, '', '00000000-0000-0000-0000-000000000000', null, null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-04 08:34:38', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-04 08:34:38', 0, '', 0, 0, '', 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('1a7d479c-815b-4751-9399-06b8b5a6a097', '城镇污水水质标准检验方法 56 总铝的测定 电感耦合等离子体发射光谱法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('1a82b81c-6391-44e1-bc63-3ea970953799', '/', '', 0, '1753-01-01', null, 1, 1, '', '00000000-0000-0000-0000-000000000000', null, null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-29 16:05:23', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-04-29 16:30:48', 0, '', 0, 0, '', 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('1adcdb5c-1126-43f4-8318-e8db1d176d5f', '水质 硝基苯类化合物的测定 液液萃取/固相萃取-气相色谱法', 'HJ 648-2013', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 648-2013', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 10:22:12', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('1c640bd8-3c35-436e-9787-7f846b260bfc', '测试分析方法', '0001', 1, '2024-04-30', '0001', 0, 1, '', '00000000-0000-0000-0000-000000000000', null, null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-04-30 11:20:17', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-30 14:14:42', 1, '分析方法', 0, 1, '配置流程', 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('1ebd9571-bad5-4f8b-ba94-6508023f01c1', '生活饮用水标准检测方法 金属指标 4.5 电感耦合等离子体发射光谱法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('1f258e16-ac1a-4c76-9bc9-b5bcbf4f5e0f', '生活饮用水标准检测方法 金属指标 3.5 电感耦合等离子体发射光谱法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('204b16b4-5c6d-48d6-8c92-2788eeee3bf3', '固定污染源废气 挥发性卤代烃的测定 气袋采样-气相色谱法', 'HJ 1006-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 1006-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 10:07:24', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('214e7521-b8c7-4d92-bc94-e38b55f79264', '城镇污水水质标准检验方法 51 总铁的测定 51.2 电感耦合等离子体发射光谱法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('21abdac0-dcd1-4b8a-8d6e-ab314e94a0c0', '城镇污水水质标准检验方法 39 总铜的测定 39.4 电感耦合等离子体发射光谱法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('238b0db5-e30f-4aa0-a2ea-4598d23d7c62', '空气质量 硝基苯类的测定 锌还原-盐酸奈乙二胺分光光度法', 'GB/T 15501-1995', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 15501-1995', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 10:05:23', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('24bea723-3af9-4e4e-a259-e6a915c12b5f', '城镇污水水质标准检验方法 45 总镉的测定 45.5 电感耦合等离子体发射光谱法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('24d858fd-da8d-4f74-b08b-c21a802ea92c', '环境空气硝基苯类化合物的测定 气相色谱法', 'HJ 738-2015', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 738-2015', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 09:49:14', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('25b2a267-1a52-482c-b421-97fee3e1e042', '水质 丙烯醛、丙烯腈和乙醛的测定 吹扫捕集-气相色谱法', 'SL 748-2017', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'SL 748-2017', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-09-20 11:21:04', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 11:13:45', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('28324b0c-1eae-46a6-897c-fc51a6186e91', '水质 碘化物的测定 离子色谱法', 'HJ 778-2015', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 778-2015', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('28c8f500-bff8-40c0-bf83-ac458c303129', '恶臭（异味）污染物排放标准 附录B 环境空气和废气 硫化氢的测定 亚甲基蓝分光光度法', 'DB 31/1025-2016', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'DB 31/1025-2016', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('2aaa8498-40f3-4fd2-8c41-fa5775510045', '固定污染源废气 氯化氢的测定 硝酸银容量法', 'HJ 548-2016', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 548-2016', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('2adcd776-0a6a-44aa-81b6-5b9628d84d03', '水质 钾和钠的测定 火焰原子吸收分光光度法', 'GB/T 11904-1989', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 11904-1989', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('2ba7a415-7224-4765-bbfe-1641cc45489f', '生活饮用水标准检验方法 有机物指标 34.1 顶空气相色谱法', 'GB/T 5750.8-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.8-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('2c14c3ae-c24a-4b17-9789-e0be652e71f6', '民用建筑工程室内环境污染控制规范 简便取样仪检测方法', 'GB 50325-2010', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB 50325-2010', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('2cf976e4-923c-4608-bfcc-d1998f7770e2', '生活饮用水标准检验方法 消毒副产物指标 4 填充柱气相色谱法', 'GB/T 5750.10-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.10-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('2d06ee59-c14d-4b57-9543-e2890ee59bde', '生活饮用水标准检验方法 无机非金属指标 9.3 水杨酸盐分光光度法', 'GB/T 5750.5-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.5-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('2d18a6df-f1e7-4311-8a06-35801add0974', '亚甲基蓝分光光度法', '《空气和废气监测分析方法》（第四版增补版）国家环保总局（2007年）', 0, '1753-01-01', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 09:57:56', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 09:00:27', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('2e765730-2f45-4a6a-af34-6946a0642c3f', '土壤和沉积物 挥发性有机物的测定 吹扫捕集/气相色谱-质谱法', 'HJ 605-2011', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 605-2011', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 09:38:57', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('2e9a9d7c-44c3-4f8b-89df-9d7dc9e7899e', '水质 挥发酚的测定 4-氨基安替比林分光光度法（直接法）', 'HJ 503-2009', 0, '1753-01-01', null, 1, 0, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:24:19', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 10:50:49', 0, '水质 挥发酚的测定 4-氨基安替比林分光光度法', 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('313ec8f5-f920-45cb-9e97-8525805887b9', '水质 可溶性阳离子（Li+、Na+、NH4+、K+、Ca2+、Mg2+）的测定 离子色谱法', 'HJ 812-2016', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 812-2016', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-09-20 10:24:39', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-09-20 10:24:39', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('3225268b-7e53-4a23-b8ef-5c77fea8e91e', '生活饮用水标准检验方法 感官性状和物理指标 5.1 玻璃电极法', 'GB/T 5750.4-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.4-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 16:40:05', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('331deab3-e4f3-4c5f-ba4c-e6fdfe28cd4b', '生活饮用水标准检验方法 消毒副产物指标 2 填充柱气相色谱法', 'GB/T 5750.10-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.10-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('3354d06e-02d8-44f6-a959-4934fd93a2ca', '生活饮用水标准检验方法 有机物综合指标 1.1 酸性高锰酸钾滴定法', 'GB/T 5750.7-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.7-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 16:27:23', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('33c7127e-12ae-42fd-92f3-6221bfe4769f', '大气污染物综合排放标准 附录G 固定污染源废气 氯苯类化合物的测定 气袋采样-气相色谱法', 'DB 31/933-2015', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'DB 31/933-2015', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 09:43:27', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('33ce0b71-ba42-47b7-ae21-cdfce3c93c0b', '生活饮用水标准检测方法 金属指标 9.6 电感耦合等离子体发射光谱法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('341375eb-da2d-43e0-b7e6-c4409c88e091', '城镇污水水质标准检验方法 24 亚硝酸盐氮的测定 24.2 离子色谱法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('3427ef72-2c5a-4c41-8cda-57e869d24f4f', '城镇污水水质标准检验方法 7 悬浮固体的测定 重量法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('36c83029-3e8d-4651-8445-7175b36c3e65', '生活饮用水标准检验方法 感观性状和物理指标 9.1 4 氨基安替吡啉三氯甲烷萃取分光光度法', 'GB/T 5750.4-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.4-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('377464be-79a8-4dcb-862c-c76f2d17c0ad', '环境空气和废气 气相和颗粒物中多环芳烃的测定 气相色谱-质谱法', 'HJ 646-2013', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 646-2013', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 09:45:23', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('39b1a84c-f507-430e-95e5-0adf0b8a01d0', '固定污染源排气中氯化氢的测定 硫氰酸汞分光光度法', 'HJ/T 27-1999', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ/T 27-1999', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('39cc8a58-d338-4dc7-b1b5-a182f47a6efa', '大气污染物综合排放标准 附录H 环境空气 氯苯类化合物的测定 固相吸附-热脱附/气相色谱法', 'DB 31/933-2015', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'DB 31/933-2015', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 09:43:29', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('3a80f232-e440-4c0a-8e07-15d81b3e2113', '降雨量的测定', '', 0, '1753-01-01', null, 1, 0, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-28 17:47:20', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-28 17:47:20', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('3b17aa91-ce05-48ad-a975-541c3e6bad57', '生活饮用水标准检验方法 金属指标 5.1 原子吸收分光光度法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('3b955dcd-3ff8-41f9-ace2-338a497a1d69', '水质 硒的测定 石墨炉原子吸收分光光度法', 'GB/T 15505-1995', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 15505-1995', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('3bb382e7-1856-4b1c-9040-37ac54fd7e6c', '生活饮用水标准检验方法 有机物指标 16.1 气相色谱法', 'GB/T 5750.8-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.8-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('3d5021cb-58d4-4470-9738-f47d79f146f1', '汽车制造业（涂装）大气污染物排放标准 附录C 固定污染源废气 苯系物的测定 气袋采样-气相色谱法', 'DB 31/859-2014', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'DB 31/859-2014', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 09:47:03', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('3f2ca534-ee18-42a3-9e8c-3492d3f7fe4c', '生活饮用水标准检验方法 感观性状和物理指标 10.1 亚甲蓝分光光度法', 'GB/T 5750.4-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.4-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 16:40:22', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('3f9f9fbd-a1c4-4bb6-b2b2-9a2ae57f069d', '紫外荧光法3.1.1（3）', '《空气和废气监测分析方法》（第四版）国家环保总局（2003年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', '《空气和废气监测分析方法》(第四版)', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 09:08:35', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('3fa35b24-2508-4d4e-aa3e-58ab16d81908', '城镇污水水质标准检验方法 17 总氰化物的测定 吡啶-巴比妥酸分光光度法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('3fb5fcd4-9ef2-4c73-bd1f-f4daf42e039f', '土壤 水溶性和酸溶性硫酸盐的测定 重量法', 'HJ 635-2012', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 635-2012', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('411b1621-ef91-4bea-a022-b0529fd34bf6', '生活饮用水标准检测方法 金属指标 20.4 电感耦合等离子体发射光谱法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('41973685-45e6-4db1-9d5a-5f4da56f9085', '土壤和沉积物 石油烃（C10~C40）的测定 气相色谱法', 'HJ 1021-2019', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 1021-2019', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('419d321c-aad4-4d24-b16f-2afabc1bc7b0', '城镇污水水质标准检验方法 42 总铅的测定 42.6 电感耦合等离子体发射光谱法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('4269e112-df8a-418a-88eb-ff9ad664a9ec', '饮食业油烟排放标准（试行） 附录A 饮食业油烟采样方法及分析方法', 'GB 18483-2001', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB 18483-2001', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('42cbe054-9f83-41cd-b7d8-af11689010c1', '固体废物 铍、镍、铜和钼的测定 石墨炉原子吸收分光光度法', 'HJ 752-2015', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 752-2015', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 11:19:47', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('43cd5705-f08d-4482-a5d8-9b6ed4ca211e', '水质 肼和甲基肼的测定 对二甲氨基苯甲醛分光光度法', 'HJ 674-2013', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 674-2013', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('44973bfe-d10f-4279-aca9-b3e9b2d1f0d5', '固定污染源废气 砷的测定 二乙基二硫代氨基甲酸银分光光度法', 'HJ 540-2016', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 540-2016', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('454220c4-78ac-48f8-9e3f-e2fcd193931e', '气体滤波相关红外吸收法3.1.5（2）', '《空气和废气监测分析方法》（第四版）国家环保总局（2003年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', '《空气和废气监测分析方法》(第四版)', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 09:03:12', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('45d55311-2d5c-45b2-9071-373b9ace7b9e', '土壤和沉积物 汞、砷、硒、铋、锑的测定 微波消解/原子荧光法', 'HJ 680-2013', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 680-2013', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('46857017-614a-4b13-ba52-9458441672cc', '生活饮用水标准检测方法 金属指标 19.1 氢化物原子荧光法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('492346ec-ec30-43b2-8776-4bf06a5acd37', '测试分析方法', '0001', 1, '2024-04-30', '0001', 1, 1, '', '00000000-0000-0000-0000-000000000000', null, null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-04-30 11:11:05', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-04-30 11:13:45', 0, '测试方法', 1, 1, '先加入水，溶解部分试剂', 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1051', '城市区域环境振动测量方法', 'GB/T 10071-1988', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1988', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:08:40', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1154', '大气固定污染源 氟化物的测定 离子选择电极法', 'HJ/T 67-2001', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2001', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1155', '大气固定污染源 镉的测定 火焰原子吸收分光光度法', 'HJ/T 64.1-2001', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2001', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1156', '大气固定污染源 镉的测定 石墨炉原子吸收分光光度法', 'HJ/T 64.2-2001', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2001', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1158', '大气固定污染源 镍的测定 火焰原子吸收分光光度法', 'HJ/T 63.1-2001', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2001', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1159', '大气固定污染源 镍的测定 石墨炉原子吸收分光光度法', 'HJ/T 63.2-2001', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2002', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:09:36', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1168', '大气降水pH值的测定 电极法', 'GB/T 13580.4-1992', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1992', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:09:54', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1170', '大气降水电导率的测定方法', 'GB/T 13580.3-1992', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1992', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:10:53', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1171', '大气降水中铵盐的测定', 'GB/T 13580.11-1992', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1992', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:10:31', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1172', '大气降水中氟、氯、亚硝酸盐、硝酸盐、硫酸盐的测定 离子色谱法', 'GB/T 13580.5-1992', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1992', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B025', '2022-06-08 14:44:36', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1224', '辐射环境保护管理导则 电磁辐射监测仪器和方法', 'HJ/T 10.2-1996', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1996', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1228', '高氯废水 化学需氧量的测定 氯气校正法', 'HJ/T 70-2001', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2001', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:23:42', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1233', '工业企业厂界环境噪声排放标准', 'GB 12348-2008', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2008', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1347', '工作场所空气有毒物质测定 酰胺类化合物', 'GBZ/T 160.62-2004', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2004', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:11:20', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1408', '固定污染源废气 氮氧化物的测定 定电位电解法', 'HJ 693-2014', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2014', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1412', '固定污染源废气 低浓度颗粒物的测定 重量法', 'HJ 836-2017', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2017', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1414', '固定污染源废气 二氧化硫的测定 定电位电解法', 'HJ 57-2017', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2017', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1415', '固定污染源废气 二氧化硫的测定 非分散红外吸收法', 'HJ 629-2011', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2011', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:00:27', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1424', '固定污染源废气 硫酸雾的测定 离子色谱法', 'HJ 544-2016', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2016', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1432', '固定污染源废气 铅的测定 火焰原子吸收分光光度法', 'HJ 685-2014', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2014', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:01:25', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1439', '固定污染源废气 一氧化碳的测定 定电位电解法', 'HJ 973-2018', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2018', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 16:59:53', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1441', '固定污染源废气 总烃、甲烷和非甲烷总烃的测定 气相色谱法', 'HJ 38-2017', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2017', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1452', '固定污染源排气中丙烯腈的测定 气相色谱法', 'HJ/T 37-1999', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1999', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1456', '固定污染源排气中二氧化硫的测定 碘量法', 'HJ/T 56-2000', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2000', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1459', '固定污染源排气中酚类化合物的测定 4-氨基安替比林分光光度法（萃取法）', 'HJ/T 32-1999', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1999', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 11:12:42', 0, '固定污染源排气中酚类化合物的测定 4-氨基安替比林分光光度法', 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1465', '固定污染源排气中甲醇的测定 气相色谱法', 'HJ/T 33-1999', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1999', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1466', '固定污染源排气中颗粒物测定与气态污染物采样方法', 'GB/T 16157-1996', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1996', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:05:00', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1472', '固定污染源排气中氯气的测定 甲基橙分光光度法', 'HJ/T 30-1999', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1999', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1474', '固定污染源排气中氰化氢的测定 异烟酸-吡唑啉酮分光光度法', 'HJ/T 28-1999', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1999', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1489', '固体废物 腐蚀性测定 玻璃电极法', 'GB/T 15555.12-1995', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1995', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 19:25:07', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1492', '固体废物 汞、砷、硒、铋、锑的测定 微波消解/原子荧光法', 'HJ 702-2014', 0, '2021-06-08', null, 1, 0, '', '00000000-0000-0000-0000-000000000000', null, '2014', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '46905a56-f770-42be-8d41-5b6c94f0ed52', '2023-09-14 14:52:19', 0, null, 0, 1, 'HJ/T300-2007 固体废物 浸出毒性浸出方法 醋酸缓冲溶液法', 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1493', '固体废物 金属元素的测定 电感耦合等离子体质谱法', 'HJ 766-2015', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2015', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B011', '2022-03-29 15:19:38', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1494', '固体废物 六价铬的测定 二苯碳酰二肼分光光度法', 'GB/T 15555.4-1995', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1995', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 19:22:24', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1506', '固体废物 总铬的测定 二苯碳酰二肼分光光度法', 'GB/T 15555.5-1995', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1995', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 19:24:20', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1508', '固体废物 总铬的测定 火焰原子吸收分光光度法', 'HJ 749-2015', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2015', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 16:58:39', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1527', '环境空气 氨的测定 次氯酸钠-水杨酸分光光度法', 'HJ 534-2009', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2009', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:45:53', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1529', '环境空气 苯系物的测定 活性炭吸附/二硫化碳解吸-气相色谱法', 'HJ 584-2010', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2010', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 09:48:22', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1532', '环境空气 臭氧的测定 紫外光度法', 'HJ 590-2010', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2010', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1534', '环境空气 氮氧化物（一氧化氮和二氧化氮）的测定 盐酸萘乙二胺分光光度法', 'HJ 479-2009', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2009', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 10:42:41', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1538', '环境空气 二氧化硫的测定 甲醛吸收-副玫瑰苯胺分光光度法', 'HJ 482-2009', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2009', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1542', '环境空气 氟化物的测定 滤膜采样/氟离子选择电极法', 'HJ 955-2018', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2018', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 19:19:37', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1544', '环境空气 氟化物的测定 石灰滤纸采样氟离子选择电极法', 'HJ 481-2009', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2009', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1547', '环境空气 挥发性有机物的测定 罐采样/气相色谱-质谱法', 'HJ 759-2015', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2015', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2022-05-17 09:16:10', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1553', '环境空气 铅的测定 火焰原子吸收分光光度法', 'GB/T 15264-1994', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1994', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:23:21', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1555', '环境空气 铅的测定 石墨炉原子吸收分光光度法', 'HJ 539-2015', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2015', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1561', '环境空气 总悬浮颗粒物的测定 重量法', 'GB/T 15432-1995', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1995', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:06:27', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1569', '环境空气和废气 氨的测定 纳氏试剂分光光度法', 'HJ 533-2009', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2009', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1572', '环境空气和废气 氯化氢的测定 离子色谱法', 'HJ 549-2016', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2016', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1599', '建筑施工场界环境噪声排放标准', 'GB 12523-2011', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2011', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1601', '交流输变电工程电磁环境监测方法（试行）', 'HJ 681-2013', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2013', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1605', '空气和废气 颗粒物中金属元素的测定 电感耦合等离子体发射光谱法', 'HJ 777-2015', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2015', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1606', '空气和废气 颗粒物中铅等金属元素的测定 电感耦合等离子体质谱法', 'HJ 657-2013', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2013', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B015', '2022-05-18 16:43:00', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1610', '空气质量 苯胺类的测定 盐酸萘乙二胺分光光度法', 'GB/T 15502-1995', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1995', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:33:35', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1611', '空气质量 恶臭的测定 三点比较式臭袋法', 'GB/T 14675-1993', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1993', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:33:05', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1617', '空气质量 一氧化碳的测定 非分散红外法', 'GB/T 9801-1988', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1988', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1630', '气相色谱法测定多氯联苯 美国环保局', 'EPA 8082A-2007', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2007', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 10:18:26', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1674', '社会生活环境噪声排放标准', 'GB 22337-2008', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2008', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1678', '生活垃圾化学特性通用检测方法', 'CJ/T 96-2013', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2013', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1827', '水、土中有机磷农药测定的气相色谱法', 'GB/T 14552-2003', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2003', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2022-05-17 09:19:38', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1835', '水质 65种元素的测定 电感耦合等离子体质谱法', 'HJ 700-2014', 0, '2021-06-08', null, 1, 0, '空白', '00000000-0000-0000-0000-000000000000', null, '2014', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-17 15:13:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1837', '水质 pH值的测定 电极法1', 'HJ 1147-2020', 1, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2020', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 15:18:27', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1838', '水质 阿特拉津的测定 高效液相色谱法', 'HJ 587-2010', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2010', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1842', '水质 氨氮的测定 纳氏试剂分光光度法', 'HJ 535-2009', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2009', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1849', '水质 苯胺类化合物的测定 气相色谱-质谱法', 'HJ 822-2017', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2017', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 10:22:29', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1853', '水质 吡啶的测定 顶空/气相色谱法', 'HJ 1072-2019', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2019', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:14:31', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1855', '水质 丙烯腈的测定 气相色谱法', 'HJ/T 73-2001', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2001', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1864', '水质 多环芳烃的测定 液液萃取和固相萃取高效液相色谱法', 'HJ 478-2009', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2009', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-10-21 13:15:05', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1869', '水质 二氧化氯和亚氯酸盐的测定 连续滴定碘量法', 'HJ 551-2016', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2016', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1875', '水质 酚类化合物的测定 液液萃取/气相色谱法', 'HJ 676-2013', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2013', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2022-05-17 09:18:36', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1876', '水质 粪大肠菌群的测定 多管发酵法', 'HJ 347.2-2018', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2018', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1881', '水质 钙和镁总量的测定 EDTA滴定法', 'GB/T 7477-1987', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1987', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1882', '水质 高锰酸盐指数的测定', 'GB/T 11892-1989', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1989', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B017', '2022-06-22 09:45:19', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1885', '水质 铬的测定 火焰原子吸收分光光度法', 'HJ 757-2015', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2015', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1886', '水质 汞、砷、硒、铋和锑的测定 原子荧光法', 'HJ 694-2014', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2014', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1889', '水质 化学需氧量的测定 重铬酸盐法', 'HJ 828-2017', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2017', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:22:04', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1896', '水质 挥发性有机物的测定 吹扫捕集/气相色谱-质谱法', 'HJ 639-2012', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2012', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2022-10-08 11:36:38', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1899', '水质 挥发性有机物的测定 顶空/气相色谱-质谱法', 'HJ 810-2016', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2016', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2022-05-17 09:15:21', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1916', '水质 硫化物的测定 碘量法', 'HJ/T 60-2000', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2000', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1917', '水质 硫化物的测定 气相分子吸收光谱法', 'HJ/T 200-2005', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2005', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-03 10:50:49', 0, null, 0, 0, null, 2);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1921', '水质 六价铬的测定 二苯碳酰二肼分光光度法', 'GB/T 7467-1987', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1987', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '46905a56-f770-42be-8d41-5b6c94f0ed52', '2024-01-22 14:52:18', 0, null, 0, 1, '', 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1922', '水质 六六六、滴滴涕的测定 气相色谱法', 'GB/T 7492-1987', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1987', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 10:19:43', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1924', '水质 氯苯类化合物的测定 气相色谱法', 'HJ 621-2011', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2011', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2022-05-17 09:17:19', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1925', '水质 氯化物的测定 硝酸银滴定法', 'GB/T 11896-1989', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1989', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:41:42', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1928', '水质 镍的测定 火焰原子吸收分光光度法', 'GB/T 11912-1989', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1989', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:48:59', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1931', '水质 氰化物的测定 容量法和分光光度法（异烟酸-巴比妥酸分光光度法）', 'HJ 484-2009', 0, '2021-06-08', null, 1, 0, '', '00000000-0000-0000-0000-000000000000', null, '2009', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B012', '2022-06-23 11:53:59', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1935', '水质 全盐量的测定 重量法', 'HJ/T 51-1999', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1999', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1937', '水质 溶解氧的测定 电化学探头法', 'HJ 506-2009', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2009', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1948', '水质 石油类和动植物油类的测定 红外分光光度法', 'HJ 637-2018', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2012', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:29:03', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1949', '水质 水温的测定 温度计或颠倒温度计测定法', 'GB/T 13195-1991', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1991', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:24:42', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1952', '水质 铁、锰的测定 火焰原子吸收分光光度法', 'GB/T 11911-1989', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1989', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:47:15', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1955', '水质 铜、锌、铅、镉的测定 原子吸收分光光度法', 'GB/T 7475-87', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1987', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:47:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1958', '水质 无机阴离子（F?、Cl?、NO??、Br?、NO??、PO???、SO???、SO???）的测定 离子色谱法', 'HJ 84-2016', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2016', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B025', '2022-10-20 15:07:59', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1976', '水质 悬浮物的测定 重量法', 'GB/T 11901-1989', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1989', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:17:06', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1985', '水质 银的测定 火焰原子吸收分光光度法', 'GB/T 11907-89', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1989', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:44:35', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1989', '水质 有机磷农药的测定 气相色谱法', 'GB/T 13192-1991', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1991', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 10:21:29', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1991', '水质 有机氯农药和氯苯类化合物的测定 气相色谱-质谱法', 'HJ 699-2014', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2014', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2022-05-24 21:04:14', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1993', '水质 浊度的测定 浊度计法', 'HJ 1075-2019', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2019', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1995', '水质 总氮的测定 碱性过硫酸钾消解紫外分光光度法', 'HJ 636-2012', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2012', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:13:47', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c1997', '水质 总铬的测定', 'GB/T 7466-1987', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1987', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c2000', '水质 总磷的测定 钼酸铵分光光度法', 'GB/T 11893-1989', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1989', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B022', '2022-06-09 14:48:26', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c2002', '水质 总有机碳的测定 燃烧氧化-非分散红外吸收法', 'HJ 501-2009', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2009', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:14:57', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c2019', '铁路边界噪声限值及其测量方法', 'GB/T 12525-1990', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1990', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:47:46', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c2029', '土壤 干物质和水分的测定 重量法', 'HJ 613-2011', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2011', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-09-13 08:29:36', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c2032', '土壤 氰化物和总氰化物的测定 分光光度法', 'HJ 745-2015', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2015', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-09-13 08:29:41', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c2058', '土壤和沉积物 硫化物的测定 亚甲基蓝分光光度法', 'HJ 833-2017', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2017', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c2078', '土壤水分测定法', 'NY/T 52-1987', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1987', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c2086', '土壤质量 铅、镉的测定 石墨炉原子吸收分光光度法', 'GB/T 17141-1997', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '1997', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:08:23', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c2091', '土壤质量 总汞、总砷、总铅的测定 原子荧光法 第1部分：土壤中总汞的测定', 'GB/T 22105.1-2008', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2008', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:07:52', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c2093', '土壤质量 总汞、总砷、总铅的测定 原子荧光法 第2部分：土壤中总砷的测定', 'GB/T 22105.2-2008', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2008', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:08:00', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('49d37696-e5b5-412a-b3fd-06d4141c2153', '移动通信基站电磁辐射环境监测方法', 'HJ 972-2018', 0, '2021-06-08', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2018', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('4b6845c9-1321-4498-8177-0994bc21fbc3', '固体废物 总汞的测定 冷原子吸收分光光度法', 'GB/T 15555.1-1995', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 15555.1-1995', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('4cbb6e7f-8e56-4bd8-8154-df4bda9ce70b', '生活饮用水标准检测方法 金属指标 2.3 电感耦合等离子体发射光谱法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 16:35:33', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('4cc9ccf0-1c05-4709-91af-fd3e3937d7d0', '家具制造业大气污染物排放标准 附录F 固定污染源废气 苯系物的测定 气袋采样-气相色谱法', 'DB 31/1059-2017', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'DB 31/1059-2017', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 09:44:08', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('4cd6674e-4658-4131-a677-b22ec6c30e9d', '生活饮用水标准检验方法 消毒副产物指标 6.1 甲醛', 'GB/T 5750.10-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.10-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 16:52:11', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('4d7ec32e-6ef4-4ceb-b7e4-3722356c1469', '生活饮用水标准检验方法 无机非金属指标 5.3 离子色谱法', 'GB/T 5750.5-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.5-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 16:39:05', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('4d9526b6-9ee7-4ad2-8e94-4e23b57464b1', '水质 硫化物的测定 亚甲基蓝分光光度法', 'HJ 1226-2021', 0, '1753-01-01', null, 1, 0, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-04-29 13:10:08', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-04-29 13:10:08', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('4da1272d-ab9c-42eb-8bde-65b236f956e8', '生活饮用水标准检验方法 感观性状和物理指标 1.1 铂-钴标准比色法', 'GB/T 5750.4-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.4-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 16:40:15', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('4e5c373f-43cc-4dc2-9b92-9c218ac0ecba', '生活饮用水标准检验方法 消毒副产物指标 1 毛细管柱气相色谱法', 'GB/T 5750.10-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.10-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('4fe937f2-db41-4e00-8e51-e5c9dc459035', '城镇污水水质标准检验方法 43 总铬的测定 43.2 火焰原子吸收分光光度法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 15:42:15', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('503965e3-7253-4af8-a8dc-f921970768f6', '定电位电解法3.1.5（3）', '《空气和废气监测分析方法》（第四版）国家环保总局（2003年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', '《空气和废气监测分析方法》(第四版)', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 09:02:16', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('518bac56-6d25-4438-b406-90b37768ee55', '污水综合排放标准 附录A 总锡的测定 石墨炉原子吸收分光光度法', 'DB 31/199-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'DB 31/199-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('52415215-4eaa-4de4-8410-a1bcab11b60d', '固定污染源烟气（SO2、NOX、颗粒物）排放连续监测技术规范', 'HJ 75-2017', 0, '1753-01-01', null, 1, 0, '', '00000000-0000-0000-0000-000000000000', null, null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-29 16:03:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-29 16:03:01', 0, '', 0, 0, '', 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('52b94251-d37c-4bac-9af6-089219be7107', '原子吸收分光光度法', '《空气和废气监测分析方法》（第四版增补版）国家环保总局（2007年）', 0, '1753-01-01', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 09:59:04', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 09:01:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('535452f0-e41a-4910-bb00-1f3f2c7f9b94', '固定污染源废气 铍的测定 石墨炉原子吸收分光光度法', 'HJ 684-2014', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 684-2014', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('5429f7b0-abb6-46fd-a7c4-28e1af4f5e95', '水质 叶绿素a 的测定 分光光度法', 'HJ 897-2017', 0, '1753-01-01', null, 1, 0, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-16 09:11:05', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-16 09:11:05', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('567bd64e-7c7f-44ad-853f-bc1e16e45a14', '水质 铊的测定 石墨炉原子吸收分光光度法', 'HJ 748-2015', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 748-2015', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('56fbfe1e-877a-485e-94c6-a61c2904cac0', '土壤和沉积物 铍的测定 石墨炉原子吸收分光光度法', 'HJ 737-2015', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 737-2015', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('5716a50e-a110-4c74-83f8-551ba230a499', '水质 总大肠菌群和粪大肠菌群的测定 纸片快速法', 'HJ 755-2015', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 755-2015', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-09-20 10:18:59', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-09-20 10:18:59', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('57d72e99-e4bf-412e-94e6-5864f7f259d4', '生活饮用水标准检验方法 无机非金属指标 10.1 重氮偶合分光光度法', 'GB/T 5750.5-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.5-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('59a1ed45-a12a-4b40-bd47-eba018bff41e', '毛细柱气相色谱法', '《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', 0, '1753-01-01', null, 1, 0, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:26:40', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 11:24:34', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('5a597e64-8ebb-4000-af2d-f242a080b645', '污水综合排放标准 附录B 化学需氧量的测定 分光光度法', 'DB 31/199-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'DB 31/199-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('5aa1b531-0511-40f7-9f20-1bd6ba1fe122', '生活饮用水标准检验方法 无机非金属指标 6.1 N，N-二乙基对苯二胺分光光度法', 'GB/T 5750.5-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.5-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 16:39:20', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('5acbd438-5693-4252-a7b6-a4cd50ec946b', '城镇污水水质标准检验方法 43 总铬的测定 43.3 电感耦合等离子体发射光谱法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 15:42:07', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('5b165f26-11a2-47b3-bd95-bee9fcf24072', '大气固定污染源 苯胺类的测定 气相色谱法', 'HJ/T 68-2001', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ/T 68-2001', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 09:18:23', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('5b521cce-777b-4a65-b020-0e40df8e8d0b', '测试分析方法2', '0002', 1, '2024-04-28', '0002', 1, 1, '', '00000000-0000-0000-0000-000000000000', null, null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-04-30 15:39:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-20 08:20:33', 0, '分析方法2', 1, 1, '配置流程', 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('5b925dab-72e5-4a9e-8135-1ec6a8839170', '电感耦合等离子体原子发射光谱法 3.2.13', '《空气和废气监测分析方法》（第四版）国家环保总局（2003年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', '《空气和废气监测分析方法》(第四版)', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 09:05:20', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('5bd9bb3c-7842-450a-8132-ad4e80c73665', '生活饮用水标准检验方法 无机非金属指标 1.2 离子色谱法', 'GB/T 5750.5-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.5-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('5bebcb93-c757-4f84-8166-d72b0b543a1c', '城镇污水水质标准检验方法 40 总锌的测定 40.4 电感耦合等离子体发射光谱法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('5cb080c5-3e25-4e22-b8ec-72c1052e2db9', '土壤检测 第2部分：土壤pH的测定', 'NYT 1121.2-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'NYT 1121.2-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('5cf4b574-302f-440a-8766-d8e71d716ab5', '城镇污水水质标准检验方法 18 硫化物的测定 18.1 对氨基N,N二甲基苯胺分光光度法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('5da0635d-b18f-4580-b0e4-401d8842ee65', '水质 丙烯腈和丙烯醛的测定 吹扫捕集/气相色谱法', 'HJ 806-2016', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 806-2016', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('5f730372-e615-4858-995d-35bbf176db2c', '钢罐采集空气挥发性有机物的测定 气相色谱/质谱法 美国国家环保局方法', 'TO-15-1999', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'TO-15-1999', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 09:50:05', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('608f7e8e-0ab5-4fc4-83ac-47d30ac2d48b', '甲醛缓冲溶液吸收-盐酸副玫瑰苯胺分光光度法5.4.1（5）', '《空气和废气监测分析方法》（第四版）国家环保总局（2003年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', '《空气和废气监测分析方法》(第四版)', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 09:04:54', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('60935a46-a3d6-4c27-86ab-bcbca61783c6', '上海市集中空调通风系统卫生管理规范 附录F 新风量检测方法', 'DB 31/405-2012', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'DB 31/405-2012', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 17:06:00', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('61581e41-ed57-444c-a333-639e5764b306', 'ICP-AES法', '《土壤元素的近代分析方法》中国环境监测总站（1992年）', 0, '1992-01-01', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 09:55:19', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 13:23:32', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('61e9477a-513f-4cf5-94b1-fa550899fcdd', '室内空气中可吸入颗粒物卫生标准 附录A 撞击式称重法', 'GB/T 17095-1997', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 17095-1997', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('63a6c7c2-ee15-4021-8be5-333c43659ac7', '生活垃圾焚烧污染物控制标准 3.7 热灼减率', 'GB 18485-2014', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB 18485-2014', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('640ba223-289b-4137-8714-74630f5b6d5a', '船舶工业大气污染物排放标准 附录C 固定污染源废气 苯系物的测定 气袋采样-气相色谱法', 'DB 31/934-2015', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'DB 31/934-2015', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 09:49:53', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('64e87a4a-a0b5-4a9b-80e7-3af56e798c10', '危险废物鉴别标准-浸出毒性鉴别附录A 固体废物 元素的测定 电感耦合等离子体原子发射光谱法1', 'GB 5085.3-2007', 1, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 15:53:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 15:57:19', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('66103486-e069-4797-b5dc-4e0536e5c647', '燃煤电厂大气污染物排放标准 附录A 固定污染源 低浓度颗粒物测定方法-重量法', 'DB 31/963-2016', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'DB 31/963-2016', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('666487da-cb3c-4dc1-b5ec-f4ec18042e2f', '土壤中六六六和滴滴涕测定的气相色谱法', 'GB/T 14550-2003', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 14550-2003', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 08:48:47', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('666c287c-80c4-4bb7-8fbe-a5d12efbee68', '环境空气 气态汞的测定 金膜富集/冷原子吸收分光光度法', 'HJ 910-2017', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 910-2017', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('67559f96-4cfe-4d67-bca8-181fdfa3af3c', '生活饮用水标准检验方法 有机物指标 5.1 吹脱捕集气相色谱法', 'GB/T 5750.8-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.8-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('6855b692-2fb1-4dd0-9061-4e4078171187', '生活饮用水标准检验方法 金属指标 9.1 无火焰原子吸收分光光度法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('68b87838-8db9-4883-86d8-dd004e3ad283', '固体废物 六价铬的测定 碱消解/火焰原子吸收分光光度法', 'HJ 687-2014', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 687-2014', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('697a471e-6ec7-41ad-b5c9-5e054dddce12', '危险废物鉴别标准 浸出毒性鉴别 附录C 石墨炉原子吸收光谱法', 'GB 5085.3-2007', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB 5085.3-2007', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 15:50:56', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('6a27831e-a136-4baf-9282-331f9bf8152a', '水质 挥发性石油烃（C6-C9）的测定 吹扫捕集/气相色谱法', 'HJ 893-2017', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 893-2017', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 17:01:42', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('6a6b42e0-b37e-470f-a6a4-02becfa88ebf', '生活饮用水标准检测方法 金属指标 12.3 电感耦合等离子体发射光谱法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('6cdac165-3172-40dd-84ea-dfdd6595feac', '生活饮用水标准检验方法 有机物指标 1.1 填充柱气相色谱法', 'GB/T 5750.8-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.8-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('6e0b9d36-9eb3-4178-87fe-65bc71ddd274', '环境空气 臭氧的测定 靛蓝二磺酸钠分光光度法及修改单', 'HJ 504-2009', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 504-2009', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('6f0cf3b6-7beb-49aa-b9b1-cfa2342e432d', '水质 酚类化合物的测定 气相色谱-质谱法', 'HJ 744-2015', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 744-2015', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 10:22:39', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('6fd91f36-3bd7-417f-8edd-31772618500e', '水质 总氮的测定 连续流动-盐酸萘乙二胺分光光度法', 'HJ 667-2013', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 667-2013', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('7073344b-2a81-492f-b46e-e5a550f21b96', '生活饮用水标准检验方法 无机非金属指标 2.2 离子色谱法', 'GB/T 5750.5-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.5-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('7224d83d-a734-41f5-8cc7-3ed6d96f6e79', '水质 甲醇和丙酮的测定 顶空/气相色谱法', 'HJ 895-2017', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 895-2017', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('74d9ae6c-6075-4a87-a6f2-7731b523d37c', '水质 亚硝酸盐氮的测定 分光光度法', 'GB/T 7493-1987', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 7493-1987', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('754766c1-97be-4657-92bc-867a679d3392', '城镇污水水质标准检验方法 50 总锰的测定 50.2 电感耦合等离子体发射光谱法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('762ae61d-3206-4ba4-92a8-623d543c3f96', '二苯碳酰二肼分光光度法', '《空气和废气监测分析方法》（第四版增补版）国家环保总局（2007年）', 0, '1753-01-01', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 09:57:48', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 09:00:04', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('7689ce7b-c477-4fc5-8371-85a76e5428c1', '居住区大气中苯、甲苯和二甲苯卫生检验标准方法 气相色谱法', 'GB/T 11737-1989', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 11737-1989', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 09:45:34', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('76c4e88e-f338-47f8-9785-ff8d12d9dd95', '固定污染源废气?油烟和油雾的测定?红外分光光度法', 'HJ 1077-2019', 0, '1753-01-01', null, 1, 0, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-10 14:29:09', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-17 16:21:53', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('776ccfaf-f7bd-4e35-bf7b-177d1111325e', '环境空气和废气 气相和颗粒物中多环芳烃的测定 高效液相色谱法', 'HJ 647-2013', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 647-2013', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 09:45:56', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('77746da8-4f08-4471-9284-769aa9a03eaf', '固体废物 22种金属元素的测定 电感耦合等离子体发射光谱法', 'HJ 781-2016', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 781-2016', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('779ade22-0f99-466d-89e1-413261b08268', '空气质量 二硫化碳的测定 二乙胺分光光度法', 'GB/T 14680-1993', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 14680-1993', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('792189ef-977f-4820-85c8-86abfbefe230', '土壤 可交换酸度的测定 氯化钾提取-滴定法', 'HJ 649-2013', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 649-2013', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('7b0f2119-1efd-420e-9fa8-1587196fb430', '城镇污水水质标准检验方法 47 总硒的测定 47.2 电感耦合等离子体发射光谱法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('7b1e8225-bcec-4a9f-9200-c6a7b3aa9fd5', '生活饮用水标准检验方法 无机非金属指标 3.2 离子色谱法', 'GB/T 5750.5-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.5-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 16:38:56', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('7b37198d-1223-4bc5-bfa7-aab189a7d893', '水质 二硫化碳的测定 二乙胺乙酸铜分光光度法', 'GB/T 15504-1995', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 15504-1995', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('7bf9c351-d6db-4b70-b5fb-a99bb054ff13', '固定污染源废气 甲硫醇等8种含硫有机化合物的测定 气袋采样-预浓缩/气相色谱-质谱法', 'HJ 1078-2019', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 1078-2019', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 09:42:39', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('7ca10411-d3b2-4d46-be97-7258bc99d9eb', '水质 多氯联苯化合物的测定 气相色谱-质谱法', 'HJ 715-2014', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 715-2014', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 10:20:13', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('7ccd2877-6389-4b00-91ac-4874c0c735a8', '分光光度法 美国国家环保局方法', 'USEPA 410.4-1993', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'USEPA 410.4-1993', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 17:25:05', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('7df14ba1-fc97-4238-9306-1b82fb55757c', '水质 氨氮的测定 气相分子吸收光谱法', 'HJ 195-2023', 0, '1753-01-01', 'HJ 195-2023', 1, 0, '', '00000000-0000-0000-0000-000000000000', null, null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-03 13:07:39', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-03 13:07:39', 0, '', 0, 0, '', 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('7e0478f0-0b92-4fbc-bcff-c64a534f60a0', '生活饮用水标准检验方法 感官性状和物理指标 8.1 称量法', 'GB/T 5750.4-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.4-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('7eef7e64-8129-42e7-9a45-87a498eddee3', '氧化还原电位的测定（电位测定法）', 'SL 94-1994', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'SL 94-1994', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('7eff469a-e11a-40ff-b34b-cd8c2da752be', '水质 挥发酚的测定 4-氨基安替比林分光光度法（萃取法）', 'HJ 503-2009', 0, '1753-01-01', null, 1, 0, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:24:09', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 10:50:58', 0, '水质 挥发酚的测定 4-氨基安替比林分光光度法', 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('7f6a0a79-0105-45c6-939f-4c1d4172ee98', '生活饮用水标准检验方法 无机非金属指标 4.1 异烟酸-吡唑啉酮分光光度法', 'GB/T 5750.5-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.5-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('7fca5245-536d-47b1-83cd-c18bb4be7de5', '土壤和沉积物 多氯联苯的测定 气相色谱-质谱法', 'HJ 743-2015', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 743-2015', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 09:38:29', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('8082b501-5c30-45e9-b233-5ee6744e67a3', '水质 挥发性卤代烃的测定 顶空气相色谱法', 'HJ 620-2011', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 620-2011', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 10:20:36', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('8173f242-4dab-48d8-ba85-e1caa51cc27d', '城镇污水水质标准检验方法 45 总镉的测定 45.2 直接火焰原子吸收光谱法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('81f7ac96-3ef6-4e94-904d-b9ccf312a2a8', '城镇污水水质标准检验方法 20 氟化物的测定 20.3 离子色谱法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 15:39:40', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('8207c447-cab0-47bb-b662-fd7a5338c89d', '城镇污水水质标准检验方法 46 总砷的测定 46.2 原子荧光光度法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('83b52dba-479e-4d2a-8d0f-2e072eea7943', '水质 浊度的测定', 'GB/T 13200-1991', 0, '1753-01-01', null, 1, 0, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:22:31', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:22:31', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('83e446f1-e21a-4771-bae8-14c19dd6692f', '生活饮用水标准检测方法 金属指标 19.3 电感耦合等离子体发射光谱法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('840abdfb-5189-4715-99b9-dfb7e5240c12', '生活饮用水标准检测方法 金属指标 5.5 电感耦合等离子体发射光谱法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('8500b84b-7dff-42dd-8d90-2362aff7e77a', '固定污染源废气 溴化氢的测定 离子色谱法', 'HJ 1040-2019', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 1040-2019', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('85c3895a-ea85-493c-b35b-db9ba557bd78', '城镇污水水质标准检验方法 23 氨氮的测定 23.1 纳氏试剂分光光度法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('85cae0ae-b6f9-4b1c-bc47-38fe6fe39e96', '水质 磷酸盐和总磷的测定 连续流动-钼酸铵分光光度法', 'HJ 670-2013', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 670-2013', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('86ED7B1F-BA6E-11E9-9C35-7922EB66D383', '固定污染源排放烟气黑度的测定 林格曼烟气黑度图法', 'HJ/T 398-2007', 0, '2001-01-01', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2007', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:03:42', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('86EE657B-BA6E-11E9-9C35-7922EB66D383', '水质 32种元素的测定 电感耦合等离子体发射光谱法（垂直法）', 'HJ 776-2015', 0, '2001-01-01', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2015', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 11:16:46', 0, '水质 32种元素的测定 电感耦合等离子体发射光谱法', 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('86F14B39-BA6E-11E9-9C35-7922EB66D383', '水质 硝基苯类化合物的测定 气相色谱-质谱法', 'HJ 716-2014', 0, '2001-01-01', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2014', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2022-05-17 09:17:36', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('86F1727E-BA6E-11E9-9C35-7922EB66D383', '环境空气 颗粒物中水溶性阴离子（F-、Cl-、Br-、NO2-、NO3-、PO43-、SO32-、SO42-）的测定 离子色谱法', 'HJ 799-2016', 0, '2001-01-01', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, '2016', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:46:37', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('878b3580-4c74-46cf-bf69-f2715b075c53', '城镇污水水质标准检验方法 16.1 氰化物的测定 异烟酸-吡唑啉酮分光光度法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('8b02901e-0cb9-4c60-bf72-c2f9b61935ea', '土壤 水溶性氟化物和总氟化物的测定 离子选择电极法', 'HJ 873-2017', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 873-2017', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('8c7af107-1d13-4f61-92c5-0ecf21b81ac8', '城镇污水水质标准检验方法 19 硫酸盐的测定 19.3 离子色谱法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 15:39:25', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('8d27e909-761b-462f-b21d-72e527d8df9d', '固定污染源排气中光气的测定 苯胺紫外分光光度法', 'HJ/T 31-1999', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ/T 31-1999', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('8d9e11fe-a5d3-416a-b5e2-cd7ebc3969ba', '大气固定污染源锡的测定 石墨炉原子吸收分光光度法', 'HJ/T 65-2001', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ/T 65-2001', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('8e11644b-479b-4b39-b169-7b89e7f58fe6', '塞氏盘法', '《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', 0, '1753-01-01', null, 1, 0, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:19:19', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 11:24:13', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('8ee68536-8f23-4547-93e5-9e4006ac1219', '气相色谱法 美国国家环保局方法', 'USEPA 8015C-2007', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'USEPA 8015C-2007', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 17:25:29', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('8f59915f-9392-43f2-b53a-1b388fb65707', '泄漏和敞开液面排放的挥发性有机物检测技术导则', 'HJ 733-2014', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 733-2014', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('92c810e5-d2d1-42c2-b4e5-bc7995e45cdc', '生活饮用水标准检测方法 金属指标 14.2 电感耦合等离子体发射光谱法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('932c7ebe-f14c-4677-834e-9d63e662539b', '水质 硝酸盐氮的测定 紫外分光光度法（试行）', 'HJ/T 346-2007', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ/T 346-2007', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('946333d0-66c0-4d63-9b7d-da36bcd32fbd', '危险废物鉴别标准-浸出毒性鉴别 附录A 固体废物 元素的测定 电感耦合等离子体原子发射光谱法1', 'GB 5085.3-2007', 1, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB 5085.3-2007', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 15:55:25', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('95aaadd7-c998-47c1-8d01-a9b35a9b9942', '水质 硫酸盐的测定 铬酸钡分光光度法', 'HJ/T 342-2007', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ/T 342-2007', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('961d39a3-1859-46d3-ad3b-482d83878fdf', '土壤中pH值的测定', 'NY/T 1377-2007', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'NY/T 1377-2007', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('9693cb27-cad2-4b33-a9ef-67dbceac3687', '土壤和沉积物 铊的测定 石墨炉原子吸收分光光度法', 'HJ 1080-2019', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 1080-2019', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('9760e46f-6b66-46c6-8f27-f0db3a59d1bd', '生活饮用水标准检验方法 有机物指标 2.1 顶空气相色谱法', 'GB/T 5750.8-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.8-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('98b01380-1869-4303-9063-e23afaf8f34e', '水质 氰化物的测定 流动注射分析方法', 'ISO14403-2', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'ISO14403-2', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-09-20 10:07:23', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 10:49:45', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('9b1ae123-5b1d-4dbd-be87-ea15ed067d42', '测试分析方法1', '0002', 1, '2024-04-30', '0002', 1, 1, '', '00000000-0000-0000-0000-000000000000', null, null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-20 09:07:54', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-20 09:08:52', 0, '分析方法1', 1, 1, '配置流程', 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('9c6974f8-1623-4dfe-9937-13eb2eb5b55c', '生活饮用水标准检验方法 有机物指标 14.1 气相色谱法', 'GB/T 5750.8-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.8-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('9c705e29-0b00-4342-87f2-efec7fbfa7e9', '水质 氨氮的测定 水杨酸分光光度法', 'HJ 536-2009', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 536-2009', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('9f533d92-98f0-4995-8b58-53bf788fa62c', '城镇污水水质标准检验方法 34 苯胺类的测定 偶氮分光光度法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('a0040df2-e9ed-40ad-bb73-42b796781ed9', '固定污染源排气中酚类化合物的测定 4-氨基安替比林分光光度法（直接法）', 'HJ/T 32-1999', 0, '1753-01-01', null, 1, 0, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-23 17:07:13', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 11:12:37', 0, '固定污染源排气中酚类化合物的测定 4-氨基安替比林分光光度法', 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('a069c257-bf83-4a3c-96a8-f6d98ebc7100', '生活饮用水标准检测方法 金属指标 8.1 汞 原子荧光法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('a1c3fd54-f02f-44b5-9d3e-842ed54ffe2b', '城镇污水水质标准检验方法 54 总钙的测定 电感耦合等离子体发射光谱法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('a28cd33b-17c8-427c-94f9-2e547dae925c', '民用建筑工程室内环境污染控制规范 附录G', 'GB 50325-2010', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB 50325-2010', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('a31c0360-bcdb-4437-80c0-bac1b46be588', '定电位电解法 5.4.11（2）', '《空气和废气监测分析方法》（第四版）国家环保总局（2003年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', '《空气和废气监测分析方法》(第四版)', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 09:01:39', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('a33ed395-03a4-40a9-b9d8-f883bd483f06', '生活饮用水标准检验方法 有机物指标 GB/T 5750.8-2006 3.1 气相色谱法', 'GB/T 5750.8-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.8-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('a5087ae7-dadc-40ad-966f-138a8197e280', '生活饮用水标准检验方法 消毒副产物指标 3 填充柱气相色谱法', 'GB/T 5750.10-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.10-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('a5734042-7a9b-471d-9f0b-b2cc3856392c', '测试方法', '0001', 1, '2024-04-30', '0001', 1, 1, '', '00000000-0000-0000-0000-000000000000', null, null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-04-30 11:15:35', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-04-30 11:19:07', 0, '测试方法', 1, 1, '制备流程', 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('a590452e-3655-4584-b4c3-f64bf286334c', '危险废物鉴别标准 浸出毒性鉴别 气相色谱法 附录I 附录V 附录W', 'GB 5085.3-2007', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB 5085.3-2007', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 15:49:58', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('a5f37bf4-d181-4958-b980-eb8f14ee0aa5', '电感耦合等离子体原子发射光谱法 美国国家环保局方法', 'USEPA 6010D-2014', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'USEPA 6010D-2014', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 17:26:05', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('a61a09bd-a46a-4649-b71d-ecba923ede86', '生活饮用水标准检验方法 第6部分：金属和类金属指标  21.2 电感耦合等离子体发射光谱法', 'GB/T 5750.6-2023', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2023', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 16:30:10', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('a65dde30-2c2f-44be-b718-64c0cb9267d8', '便携式、实验室电导率仪法', '《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', 0, '1753-01-01', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 09:58:48', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 09:58:48', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('a6a6ca0d-b31a-474a-982a-040bb720ccb8', '水中丙烯酰胺的测定 高效液相色谱法1', '', 1, '1753-01-01', null, 1, 1, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B012', '2022-08-16 16:24:39', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-12-19 08:36:03', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('a6f0284d-1f92-42ad-9cd8-1823effbfc04', '城镇污水水质标准检验方法 5 色度的测定 5.1 稀释倍数法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 15:42:37', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('a7294bc5-3230-417b-ac25-924e6ba408e9', '生活饮用水标准检验方法 有机物指标 1.2 毛细管柱气相色谱法', 'GB/T 5750.8-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.8-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('a7e30f72-6e09-4165-a9d8-95dd3ae03e5c', '城镇污水水质标准检验方法 6 pH的测定 电位计法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('a808a429-e8b1-464f-b265-faa4106e1cc3', '总烃和非甲烷烃的测定方法 气相色谱法6.1.5（1）', '《空气和废气监测分析方法》（第四版）国家环保总局（2003年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', '《空气和废气监测分析方法》(第四版)', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 09:02:45', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('a86f842f-35ec-4e49-a6c9-7a80e6f7368d', '生活饮用水标准检测方法 金属指标 13.2 电感耦合等离子体发射光谱法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('a8bb11ee-7167-4d8f-a513-147e2e0542ef', '固定污染源排气中丙烯醛的测定 气相色谱法', 'HJ/T 36-1999', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ/T 36-1999', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('a8bb286f-677f-4e25-b31d-f9b9f6284536', '餐饮业油烟排放标准 餐饮油烟采样方法及分析方法 附录A', 'DB 31/844-2014', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'DB 31/844-2014', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('a9f0b074-538d-403c-bc6d-8d49963c7432', '固体废物 氟化物的测定 离子选择性电极法', 'GB/T 15555.11-1995', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 15555.11-1995', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('aa318f93-cf6e-4990-b0d8-7587ebde6c71', '生活饮用水标准检测方法 金属指标 11.6 电感耦合等离子体发射光谱法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ab2382cc-51f2-4954-96b3-87454178a08c', '环境空气 苯并[a]芘测定 高效液相色谱法', 'HJ 956-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 956-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 11:22:34', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ab7d1cd2-27c4-4757-bf84-4c139642200e', '城镇污水水质标准检验方法 55 总镁的测定 电感耦合等离子体发射光谱法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ac3fd6c9-cf9c-4f6c-a8ec-11a343a4eb3c', '土壤和沉积物 六价铬的测定 碱溶液提取-火焰原子吸收分光光度法', 'HJ 1082-2019', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 1082-2019', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ac461db4-76ad-42f1-8798-bb3cf1e9d87b', '生活饮用水标准检测方法 金属指标 16.2 电感耦合等离子体发射光谱法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ad2aafbd-176a-45d9-9ddd-0fb4b24ac1e5', '室内空气质量标准 附录C 室内空气中总挥发性有机物（TVOC）的检验方法（热解吸/毛细管气相色谱法）', 'GB/T 18883-2002', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 18883-2002', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 09:43:44', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('adabf62a-51ec-4472-b409-9958ad8afa6d', '生活饮用水标准检验方法 金属指标 4.2 火焰原子吸收分光光度法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 16:36:42', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('adecf4a1-bb95-4a00-97d2-e4f99ed1290a', '土壤 总磷的测定 碱熔-钼锑抗分光光度法', 'HJ 632-2011', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 632-2011', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ae0cc1da-0294-4391-a394-890a1f8ed507', '危险废物鉴别标准 浸出毒性鉴别 附录D 固体废物 金属元素的测定 火焰原子吸收光谱法', 'GB 5085.3-2007', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB 5085.3-2007', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 15:57:37', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ae72d29f-6b8c-44ca-b02b-16098384403a', '土壤中石油烃（C10~C40）含量的测定 气相色谱法', 'BS EN ISO 16703-2011', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'BS EN ISO 16703-2011', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('af4d43bf-3acd-4b6e-940e-34c675ed5829', '土壤和沉积物 12种金属元素的测定 王水提取-电感耦合等离子体质谱法', 'HJ 803-2016', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 803-2016', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 10:13:46', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('af53d75f-73ad-47bb-aeda-9704e7b68e19', '固定污染源废气 氮氧化物的测定 分析仪器法', 'USEPA Method 7E-2016', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'USEPA Method 7E-2016', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('af653208-5acb-405a-b28f-ff902b77ffda', '生活饮用水标准检验方法 第8部分：有机物指标 附录A 吹扫捕集气相色谱质谱法测定挥发性有机物', 'GB/T 5750.8-2023', 0, '1753-01-01', null, 0, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.8-2023', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-18 16:05:16', 0, '', 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('b146aec7-532f-460e-a607-cc21e42be198', '城镇污水水质标准检验方法 12 五日生化需氧量的测定 稀释与接种法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('b3860314-946e-4445-9ab3-196144f4a357', '城镇污水水质标准检验方法 21 氯化物的测定 21.2 离子色谱法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 15:39:48', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('b5799f77-1fa7-47d9-bccc-7a5b8eeb9cf6', '测试分析方法1', '0002', 1, '2024-04-30', '0002', 1, 1, '', '00000000-0000-0000-0000-000000000000', null, null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-20 09:03:21', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-20 09:06:48', 0, '分析方法1', 1, 1, '配置流程', 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('b6a26b49-8750-4f68-bbf7-a561014bf973', '城镇污水水质标准检验方法 39 总铜的测定 39.2 直接火焰原子吸收光谱法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('b73a7e0a-a656-48d5-b3ca-578e10a06973', '水质 挥发性有机物的测定 吹扫捕集/气相色谱法', 'HJ 686-2014', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 686-2014', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 10:21:04', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('b75b2470-3042-45a2-a4aa-7d96436fdb60', '生活饮用水标准检验方法 有机物指标 23~28 气相色谱法', 'GB/T 5750.8-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.8-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 16:21:27', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('b80ece84-9292-4512-94a5-4fc510cc3fe8', '水质 浮游植物的测定 0.1 ml 计数框-显微镜计数法', 'HJ 1216-2021', 0, '1753-01-01', null, 1, 0, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-04-29 13:12:47', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-04-29 13:12:47', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('b82dc2db-70fe-4a1a-93be-80dfd50c8157', '室内空气质量标准 附录B 室内空气中苯的检验方法（毛细管气相色谱法）', 'GB/T 18883-2002', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 18883-2002', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 09:51:43', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('b850bcb1-173d-44be-999a-93c5e760a7f6', '气相色谱-质谱法测定半挥发性有机物 美国国家环保局方法', 'USEPA 8270E-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'USEPA 8270E-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 17:25:18', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('b93008cc-69bb-4195-9d67-654731117faa', '水质 钡的测定 火焰原子吸收分光光度法', 'HJ 603-2011', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 603-2011', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-09-20 11:10:32', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-09-20 11:10:32', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('babc02da-c17f-4148-ad77-cfdeb450a4cf', '城镇污水水质标准检验方法 9 溶解性总固体的测定 重量法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('bb214ec4-4440-44f5-af8e-771feceeee7c', '生活饮用水标准检验方法 感官性状和物理指标 7.1 乙二胺四乙酸二钠滴定法', 'GB/T 5750.4-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.4-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('bbf6d775-30b5-4c4f-8b8c-9b32bb4083d0', '测试分析方法1', '0002', 1, '2024-05-20', '0002', 1, 1, '', '00000000-0000-0000-0000-000000000000', null, null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-20 09:10:19', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-30 14:14:42', 0, '分析方法1', 1, 1, '配置流程', 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('bc68281f-d021-45f2-820e-10137d0a2bbd', '固定污染源排气中乙醛的测定 气相色谱法', 'HJ/T 35-1999', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ/T 35-1999', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('bdf1338e-d6ef-4cbf-880e-a366f9dcb319', '固定污染源排气中苯并(a)芘的测定 高效液相色谱法', 'HJ/T 40-1999', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ/T 40-1999', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('be0ea1f0-b906-48e3-bb76-aef7d5b3b5b0', '城镇污水水质标准检验方法 26 总氮的测定 26.3 碱性过硫酸钾消解紫外分光光度法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('be6290f5-015c-411c-9cb1-46d4a0370ffc', '固定污染源废气 碱雾的测定 电感耦合等离子体发射光谱法', 'HJ 1007-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 1007-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 10:09:16', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('beb61622-10c2-4a1d-8d72-ec8d10284465', '水质 氨氮的测定 气相分子吸收光谱法', 'HJ/T 195-2005', 0, '1753-01-01', null, 1, 0, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:23:39', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-03 13:47:54', 0, null, 0, 0, null, 2);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('c036917a-3297-429e-a3a1-a1451739d8fe', '水质 丁基黄原酸的测定 紫外分光光度法', 'HJ 756-2015', 0, '1753-01-01', null, 1, 0, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-16 12:57:06', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-16 12:57:06', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('c0f64e68-d97b-44dd-a904-997c75acf5a8', '水质 化学需氧量的测定 快速消解分光光度法', 'HJ/T 399-2007', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ/T 399-2007', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('c18cef30-cee7-4747-89c1-8488538387ea', '危险废物鉴别标准 浸出毒性鉴别 附录A 固体废物 元素的测定 电感耦合等离子体原子发射光谱法', 'GB 5085.3-2007', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB 5085.3-2007', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 15:56:40', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('c1d4ddbb-941e-4cad-aa9a-9b202acb6e09', '大气污染物综合排放标准 附录E 固定污染源废气 苯系物的测定 气袋采样-气相色谱法', 'DB 31/933-2015', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'DB 31/933-2015', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 17:14:09', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('c1dc0321-e33e-4407-9871-ac665e50b6e0', '危险废物鉴别标准 浸出毒性鉴别 附录D 火焰原子吸收光谱法', 'GB 5085.3-2007', 1, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB 5085.3-2007', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 16:02:02', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('c38f0277-29ba-4fdf-9885-5cdfef7b0618', '气相色谱法测定 有机磷农药 美国国家环保局方法', 'USEPA 8141B-2007', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'USEPA 8141B-2007', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 17:25:49', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('c3f0607f-a2f4-4ba8-a884-80f7da37f722', '水质 离子色谱法测定溶解性阴离子 第三部分 铬酸盐、碘化物、亚硫酸盐、硫氰酸盐和硫代硫酸酯的测定', 'ISO10304-3-1997', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'ISO10304-3-1997', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('c4bde3bb-035f-4f28-956e-b6eb427af910', '测试分析方法1', '0002', 1, '2024-05-20', '0002', 1, 1, '', '00000000-0000-0000-0000-000000000000', null, null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-20 09:09:09', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-20 09:10:02', 0, '分析方法1', 1, 1, '配置流程', 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('c54a383d-70b4-4be2-9c3a-68d19ec158b3', '土壤和沉积物 钴的测定 火焰原子吸收分光光度法', 'HJ 1081-2019', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 1081-2019', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('c5871bea-20c6-40b6-b823-63e1ce0f170a', '恶臭（异味）污染物排放标准 附录C 固定污染源废气 苯系物的测定 气袋采样-气相色谱法', 'DB 31/1025-2016', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'DB 31/1025-2016', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 09:45:51', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('c6ef5098-47a5-4d07-8b97-af498b24fc55', '生活饮用水标准检测方法 金属指标 22.3 电感耦合等离子体发射光谱法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('c7926e82-478d-43a9-9a8f-dce2818d30f4', '生活饮用水标准检验方法 金属指标 10.1 二苯碳酰二肼分光光度法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('c7aa864a-e62b-4973-adaf-8729d84ea801', '生活饮用水标准检验方法 消毒副产物指标 5.1 顶空气相色谱法', 'GB/T 5750.10-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.10-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 16:52:05', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('c7eff81b-5c9d-4fad-98a1-a55b7b05cc03', '固定污染源废气 氯苯类化合物的测定 气相色谱法', 'HJ 1079-2019', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 1079-2019', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 09:42:30', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('c97f8ba7-b34c-43fa-9b43-a9e38e796936', '生活饮用水标准检验方法 消毒副产物指标 1 填充柱气相色谱法', 'GB/T 5750.10-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.10-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('cb727bd6-199d-4215-91b5-c277600c2e7c', '城镇污水水质标准检验方法 40 总锌的测定 40.2 直接火焰原子吸收光谱法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('cefb9b49-25fd-4858-9efa-e4bd14fce9b5', '城镇污水水质标准检验方法 31 挥发酚的测定 31.2 直接分光光度法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 15:40:17', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('cf45d028-b92e-40a3-a467-11f5a9e177f7', '生活饮用水标准检测方法 金属指标 18.2 电感耦合等离子体发射光谱法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('d171f12b-776a-4b18-9b33-e07c9bfcc23e', '生活饮用水标准检验方法 有机物指标 44.1 气相色谱法', 'GB/T 5750.8-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.8-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('d3253ce0-3ccb-49b1-9b92-867bd1c8682d', '生活饮用水标准检验方法 金属指标 4.1 无火焰原子吸收分光光度法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 16:36:34', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('d376edd3-8f0a-4109-8b8e-9985cc82da7f', '无机阴离子的测定 离子色谱法 美国环保局方法', 'USEPA 9056A-2007', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'USEPA 9056A-2007', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('d40b1f3b-8820-44c7-a07e-f88880560f32', '水质 可萃取性石油烃（C10-C40）的测定 气相色谱法', 'HJ 894-2017', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 894-2017', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('d5143d26-e130-42ea-92cc-97b2b02b78df', '原子荧光分光光度法', '《空气和废气监测分析方法》（第四版增补版）国家环保总局（2007年）', 0, '1753-01-01', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 09:59:40', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 09:07:42', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('d62da727-000e-456f-bb93-35e57c7402a5', '城镇污水水质标准检验方法 42 总铅的测定 42.2 直接火焰原子吸收光谱法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('d7102dd4-0c6c-4d23-97a2-e864b5e965a4', '生活饮用水标准检验方法 金属指标 3.1 原子吸收分光光度法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 16:36:18', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('d7244ae8-a2f4-435c-bfed-5c23a89f0196', '民用建筑工程室内环境污染控制规范 附录F 室内空气中苯的测定', 'GB 50325-2010', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB 50325-2010', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('d8dd4138-4b88-44d9-a915-60d354acdbf4', '空气中氡浓度的闪烁瓶测量方法', 'GB/T 16147-1995', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 16147-1995', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('dae2470c-0638-4e65-9d6d-d6b928eafedc', '水质 总氮、挥发酚、硫化物、阴离子表面活性剂和六价铬的测定 连续流动分析-分光光度法', 'SL/T788-2019', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'SL/T788-2019', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '46905a56-f770-42be-8d41-5b6c94f0ed52', '2023-09-20 09:43:28', '5f7bcf90feb545968424b0a872863876', '46905a56-f770-42be-8d41-5b6c94f0ed52', '2023-09-20 09:43:28', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('dafc49ef-85d9-477c-aefd-8675287e9995', '涂料、油墨及其类似产品制造工业大气污染物排放标准 附录C 固定污染源废气 苯系物的测定 气袋采样-气相色谱法', 'DB 31/881-2015', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'DB 31/881-2015', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 09:47:13', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('db6816d9-f7bd-4666-8308-771b56ee0416', '生活饮用水标准检验方法 感观性状和物理指标 9.2 4 氨基安替吡啉直接分光光度法', 'GB/T 5750.4-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.4-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('dce2fa8f-60f6-42fe-8816-78243eba1d9b', '水质 氟化物的测定 离子选择电极法 1', 'GB/T 7484-1987', 1, '1753-01-01', null, 1, 0, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:21:34', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-12-19 08:36:38', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('dce39aea-f3fc-4bd7-b4b6-23a5d4feeb98', '城镇污水水质标准检验方法 附录H 紫外分光光度法测定油', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('dd1eafcd-a599-4a2c-a0dc-e19a0790c259', '环境空气 二氧化氮的测定 Saltzman法', 'GB/T 15435-1995', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 15435-1995', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('dd29a47a-c10b-474b-a3f0-d3e3c3e5894d', '危险废物鉴别标准 浸出毒性鉴别 附录F 固体废物 氟离子、溴酸根、氯离子、亚硝酸根、氰酸根、溴离子、硝酸根、磷酸根、硫酸根的测定 离子色谱法', 'GB 5085.3-2007', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB 5085.3-2007', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 15:50:23', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('dd4537f0-4e46-45b0-8ad6-af8e2600d0c4', '一硝基和二硝基化合物 还原-偶氮光度法', '《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', 0, '1753-01-01', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 09:57:12', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 11:23:25', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('dd5f07db-3651-463d-bc0d-f4ab20c61878', '土壤质量 氟化物的测定 离子选择电极法', 'GB/T 22104-2008', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 22104-2008', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('de5510a7-b9c4-4e6f-b946-a52972f23ad8', '生活垃圾焚烧大气污染物排放标准 3.7 热灼减率', 'DB 31/768-2013', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'DB 31/768-2013', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 17:12:52', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('e068527f-7977-4b97-bd1b-f5385f03bc90', '土壤和沉积物 多环芳烃的测定 气相色谱-质谱法', 'HJ 805-2016', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 805-2016', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '46905a56-f770-42be-8d41-5b6c94f0ed52', '2024-01-18 16:18:17', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('e10eafc0-64b8-4ae7-94bb-1f75d5b84401', '环境空气 硝基苯类化合物的测定 气相色谱-质谱法', 'HJ 739-2015', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 739-2015', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 09:47:49', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('e1bf3b3d-a400-4ae5-b4bd-408ddf3f3cbc', '城镇污水水质标准检验方法 13 化学需氧量的测定 重铬酸钾法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('e1c2f4de-92ba-46a0-acc3-57695ce941ef', '水质 氟化物的测定 氟试剂分光光度法', 'HJ 488-2009', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 488-2009', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-09-20 10:02:55', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-09-20 10:02:55', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('e1e69298-ec88-44ca-a2ae-554e133a1490', '生活饮用水标准检测方法 金属指标 6.1 砷 氢化物原子荧光法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('e21ef2da-6a70-4028-97fa-2e44c055ae60', '危险废物焚烧污染控制标准 3.6 热灼减率', 'GB 18484-2001', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB 18484-2001', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('e467c7e8-33fd-4a7d-b8e0-5f97c45af0ad', '生活饮用水标准检验方法 有机物指标 18.4 顶空-毛细管柱气相色谱法', 'GB/T 5750.8-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.8-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 16:23:06', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('e4b74b93-9121-4425-8db6-f4e029dff934', '土壤质量 总汞的测定 冷原子吸收分光光度法', 'GB/T 17136-1997', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 17136-1997', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('e5572ede-9545-4bec-83b2-16d4bef3e2d8', '铅字法 3.1.5', '《水和废水监测分析方法》（第四版）国家环境保护总局（2002年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', '《水和废水监测分析方法》(第四版)', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 11:27:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('e5a734fa-16c9-49ba-b763-6ce2d816ec1f', '土壤 氨氮、亚硝酸盐氮、硝酸盐氮的测定 氯化钾溶液提取-分光光度法', 'HJ 634-2012', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 634-2012', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('e5edaa20-a883-4891-96db-a30595ee89cd', '煤中全硫的测定方法', 'GB/T 214-2007', 0, '1753-01-01', null, 1, 0, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-19 15:39:10', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-19 15:39:10', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('e5fb9c64-378d-49ae-a8b2-50b5c2d68b26', '气相色谱-质谱法 4.3.2', '《水和废水监测分析方法》（第四版）国家环境保护总局（2002年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', '《水和废水监测分析方法》(第四版)', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 11:25:16', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('e63870be-eded-4971-ae66-c80c9c80970e', '水质 氟化物的测定 离子选择电极法', 'GB/T 7484-1987', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 7484-1987', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('e64d223a-cc12-4548-9d88-7ce77001403f', '城镇污水水质标准检验方法 31 挥发酚的测定 31.1 三氯甲烷萃取法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 15:40:09', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('e6df1cb9-65e3-404f-a612-fa73cfdb6165', '城镇污水水质检验方法标准 10 总固体的测定 重量法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('e71123e0-1553-491e-b271-99701c8e6d47', '环境空气 挥发性卤代烃的测定 活性炭吸附-二硫化碳解吸/气相色谱法', 'HJ 645-2013', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 645-2013', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('e9835bf0-da59-48a7-ab27-46145c53ef9f', '土壤和沉积物 有机氯农药的测定 气相色谱-质谱法', 'HJ 835-2017', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 835-2017', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 09:39:17', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ea97d9f0-41a4-408a-8491-f1d417b57f44', '生活饮用水标准检测方法 金属指标 7.6 电感耦合等离子体发射光谱法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('eb77bf87-02a7-48ec-a09c-059b32b158b9', '危险废弃物焚烧大气污染物排放标准 3.4 热灼减率', 'DB 31/767-2013', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'DB 31/767-2013', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed16fc20-74e2-11ec-bdd3-43f738053e8f', '固体废物 浸出毒性浸出方法 硫酸硝酸法', 'HJ/T 299-2007', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2007', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 11:23:40', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed172331-74e2-11ec-bdd3-43f738053e8f', '固定污染源废气 二氧化硫的测定 便携式紫外吸收法', 'HJ 1131-2020', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2020', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed174a42-74e2-11ec-bdd3-43f738053e8f', '固定污染源废气 挥发性有机物的测定 固相吸附/气相色谱-质谱法', 'HJ 734-2014', 1, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2014', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2024-01-03 15:10:23', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed177153-74e2-11ec-bdd3-43f738053e8f', '固定污染源废气 氮氧化物的测定 便携式紫外吸收法', 'HJ 1132-2020', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2020', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed179864-74e2-11ec-bdd3-43f738053e8f', '固定污染源废气 氮氧化物的测定 非分散红外吸收法', 'HJ 692-2014', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2014', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 15:51:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed17bf75-74e2-11ec-bdd3-43f738053e8f', '固定污染源废气 汞的测定?冷原子吸收分光光度法（暂行）', 'HJ 543-2009', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2009', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 15:28:04', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed1834a8-74e2-11ec-bdd3-43f738053e8f', '固定污染源废气?气态汞的测定?活性炭吸附/热裂解原子吸收分光光度法', 'HJ 917-2017', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2017', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 19:09:15', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed1882ca-74e2-11ec-bdd3-43f738053e8f', '固定污染源排气中一氧化碳的测定 非色散红外吸收法', 'HJ/T 44-1999', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 1999', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 15:51:28', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed18a9db-74e2-11ec-bdd3-43f738053e8f', '固定污染源排气中氮氧化物的测定 盐酸萘乙二胺分光光度法', 'HJ/T 43-1999', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 1999', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 15:51:35', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed18d0ec-74e2-11ec-bdd3-43f738053e8f', '固定污染源排气中氯乙烯的测定 气相色谱法', 'HJ/T34-1999', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 1999', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 13:21:14', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed191f0e-74e2-11ec-bdd3-43f738053e8f', '固定污染源排气中铬酸雾的测定 二苯基碳酰二肼分光光度法', 'HJ/T 29- 1999', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 1999', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 15:52:50', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed19461f-74e2-11ec-bdd3-43f738053e8f', '土壤 pH值的测定 电位法', 'HJ 962-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2018', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 19:23:40', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed196d30-74e2-11ec-bdd3-43f738053e8f', '土壤?石油类的测定?红外分光光度法', 'HJ 1051-2019', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2019', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:42:14', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed199441-74e2-11ec-bdd3-43f738053e8f', '土壤和沉积物 有机氯农药的测定 气相色谱法', 'HJ 921-2017', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2017', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-27 09:06:56', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed19bb52-74e2-11ec-bdd3-43f738053e8f', '土壤和沉积物?总汞的测定?催化热解-冷原子吸收分光光度法', 'HJ 923-2017', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2017', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:42:23', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed19e263-74e2-11ec-bdd3-43f738053e8f', '土壤和沉积物 铜、锌、铅、镍、铬的测定 火焰原子吸收分光光度法', 'HJ 491-2019', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2019', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 09:22:33', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed1a3085-74e2-11ec-bdd3-43f738053e8f', '大气降水中钙、镁的测定 原子吸收分光光度法', 'GB/T 13580.13-1992', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 1992', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 15:58:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed1a5796-74e2-11ec-bdd3-43f738053e8f', '大气降水中钠、钾的测定 原子吸收分光光度法', 'GB/T 13580.12-1992', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 1992', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 15:58:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed1accc9-74e2-11ec-bdd3-43f738053e8f', '异烟酸-吡唑啉酮分光光度法', '《空气和废气监测分析方法》（第四版增补版）国家环保总局（2007年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2007', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 09:02:23', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed1b41fc-74e2-11ec-bdd3-43f738053e8f', '气相色谱-质谱法', '《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2006', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 11:25:33', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed1b690d-74e2-11ec-bdd3-43f738053e8f', '气相色谱/质谱法（GC/MS）测定半挥发性有机物 美国环保局', 'EPA 8270D-2014', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2014', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-09-09 13:59:23', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed1bb72f-74e2-11ec-bdd3-43f738053e8f', '气相色谱法', '《空气和废气监测分析方法》（第四版增补版）国家环保总局（2007年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2007', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 09:04:11', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed1c0551-74e2-11ec-bdd3-43f738053e8f', '水中丙烯酰胺的测定 高效液相色谱法', null, 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 16:00:22', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed1c2c62-74e2-11ec-bdd3-43f738053e8f', '水中微囊藻毒素-LR的测定 高效液相色谱串联质谱法', null, 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 16:00:22', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed1c5373-74e2-11ec-bdd3-43f738053e8f', '水中微囊藻毒素的测定', 'GB/T 20466-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2006', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 16:00:22', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed1cefb7-74e2-11ec-bdd3-43f738053e8f', '水质 五日生化需氧量（BOD5）的测定 稀释与接种', 'HJ 505-2009', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2009', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-22 15:14:39', 0, null, 1, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed1e4f50-74e2-11ec-bdd3-43f738053e8f', '水质 氯苯的测定 气相色谱法', 'HJ/T 74-2001', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2001', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2022-05-17 09:17:05', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed1e9d72-74e2-11ec-bdd3-43f738053e8f', '水质 水样对费氏弧菌发光强度的抑制作用的测定（发光细菌试验） 第三部分：冻干细菌法', 'ISO 11348-3-2007', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2007', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 17:00:34', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed1f12a5-74e2-11ec-bdd3-43f738053e8f', '水质 游离氯和总氯的测定 N,N-二乙基-1,4-苯二胺滴定法', 'HJ 585-2010', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2010', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed1f39b6-74e2-11ec-bdd3-43f738053e8f', '水质 溶解氧的测定 碘量法', 'GB/T 7489-1987', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 1987', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 09:53:18', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed1f60c7-74e2-11ec-bdd3-43f738053e8f', '水质 烷基汞的测定 吹扫捕集/气相色谱—冷原子荧光光谱法', 'HJ 977-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2018', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 15:29:14', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed1f87d8-74e2-11ec-bdd3-43f738053e8f', '水质 甲醛的测定 乙酰丙酮分光光度法', 'HJ 601-2011', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2011', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 09:19:41', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed1faee9-74e2-11ec-bdd3-43f738053e8f', '水质 百菌清和溴氰菊酯的测定 气相色谱法', 'HJ 698-2014', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2014', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2022-08-25 16:31:25', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed1ffd0b-74e2-11ec-bdd3-43f738053e8f', '水质 石油类的测定 紫外分光光度法（试行）', 'HJ 970-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2018', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 16:59:50', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed20723e-74e2-11ec-bdd3-43f738053e8f', '水质 联苯胺的测定 高效液相色谱法', 'HJ 1017-2019', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2019', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed20994f-74e2-11ec-bdd3-43f738053e8f', '水质 色度的测定 稀释倍数法', 'HJ 1182-2021', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2021', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed20bf60-74e2-11ec-bdd3-43f738053e8f', '水质 色度的测定（铂钴比色法）', 'GB/T 11903-1989', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 1989', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 19:01:12', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed20e671-74e2-11ec-bdd3-43f738053e8f', '水质 苯胺类化合物的测定 N-(1-萘基)乙二胺偶氮分光光度法', 'GB 11889-1989', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 1989', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 08:37:05', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed210d82-74e2-11ec-bdd3-43f738053e8f', '水质 钙和镁的测定 原子吸收分光光度法', 'GB/T 11905-1989', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 1989', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 09:20:39', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed213493-74e2-11ec-bdd3-43f738053e8f', '水质 钾和钠的测定 火焰原子吸收分光光度法 1', 'GB/T 11904-1989', 1, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 1989', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-12-19 08:37:37', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed215ba4-74e2-11ec-bdd3-43f738053e8f', '水质 阴离子表面活性剂的测定 亚甲蓝分光光度法', 'GB/T 7494-1987', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 1987', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:50:44', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed2182b5-74e2-11ec-bdd3-43f738053e8f', '水质 黄磷的测定 气相色谱法', 'HJ 701-2014', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2014', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed21a9c6-74e2-11ec-bdd3-43f738053e8f', '水质 游离氯和总氯的测定 N,N-二乙基-1,4-苯二胺分光光度法', 'HJ 586-2010', 0, '2021-06-08', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2010', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-01-03 14:20:05', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed21d0d7-74e2-11ec-bdd3-43f738053e8f', '水质?四乙基铅的测定?顶空/气相色谱-质谱法', 'HJ 959-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2018', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 15:35:45', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed21f7e8-74e2-11ec-bdd3-43f738053e8f', '水质?总大肠菌群、粪大肠菌群和大肠埃希氏菌的测定?酶底物法', 'HJ 1001-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2018', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed221ef9-74e2-11ec-bdd3-43f738053e8f', '水质?总汞的测定?冷原子吸收分光光度法', 'HJ 597-2011', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2011', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:36:31', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed22460a-74e2-11ec-bdd3-43f738053e8f', '水质?挥发酚的测定?流动注射-4-氨基安替比林分光光度法', 'HJ 825-2017', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2017', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed226d1b-74e2-11ec-bdd3-43f738053e8f', '水质 苯系物的测定 顶空/气相色谱法', 'HJ 1067-2019', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2019', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 09:11:56', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed22942c-74e2-11ec-bdd3-43f738053e8f', '河流流量测验规范', 'GB 50179-2015 附录B', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2015', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed22bb3d-74e2-11ec-bdd3-43f738053e8f', '测烟望远镜法', '《空气和废气监测分析方法》（第四版增补版）国家环保总局（2007年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2007', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 09:04:29', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed22e24e-74e2-11ec-bdd3-43f738053e8f', '海洋监测规范 第4部分：海水分析', 'GB 17378.4-2007', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2007', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B014', '2022-05-17 16:18:32', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed233070-74e2-11ec-bdd3-43f738053e8f', '海洋监测规范 第5部分：沉积物分析', 'GB 17378.5-2007', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2007', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed237e92-74e2-11ec-bdd3-43f738053e8f', '火焰原子吸收分光光度法', '《土壤元素的近代分析方法》中国环境监测总站（1992年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 1992', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 13:23:47', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed23a5a3-74e2-11ec-bdd3-43f738053e8f', '环境γ辐射剂量率测量技术规范', 'HJ 11517-2021', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2021', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed23ccb4-74e2-11ec-bdd3-43f738053e8f', '环境噪声监测技术规范 噪声测量修正值', 'HJ 706-2014', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2014', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed23f3c5-74e2-11ec-bdd3-43f738053e8f', '环境噪声监测技术规范 城市声环境常规监测（功能区噪声）', 'HJ 640-2012', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2012', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed241ad6-74e2-11ec-bdd3-43f738053e8f', '环境噪声监测技术规范 城市声环境常规监测（区域环境噪声）', 'HJ 640-2012', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2012', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed2441e7-74e2-11ec-bdd3-43f738053e8f', '环境噪声监测技术规范 城市声环境常规监测（道路交通噪声）', 'HJ 640-2012', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2012', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed2468f8-74e2-11ec-bdd3-43f738053e8f', '环境噪声监测技术规范 结构传播固定设备室内噪声', 'HJ 707-2014', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2014', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed249009-74e2-11ec-bdd3-43f738053e8f', '环境影响评价技术导则 输变电', 'HJ 24-2020', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2020', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed252c4b-74e2-11ec-bdd3-43f738053e8f', '环境空气 汞的测定 巯基棉富集-冷原子荧光分光光度法（暂行）', 'HJ 542-2009', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2009', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 15:28:12', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed2ddedd-74e2-11ec-bdd3-43f738053e8f', '环境空气 臭氧前体有机物的测定 罐采样/气相色谱-氢离子火焰检测器/质谱检测器联用法 附录B', '《环境空气臭氧前体有机物手工监测技术要求（试行）》（环办监测函（2018）240号）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2018', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 11:07:00', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed2ea22e-74e2-11ec-bdd3-43f738053e8f', '环境空气 苯系物的测定 固体吸附/热脱附-气相色谱法', 'HJ 583-2010', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2010', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 09:48:14', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed2f6581-74e2-11ec-bdd3-43f738053e8f', '环境空气 降水中阳离子（Na?、NH??、K?、Mg??、Ca??）的测定 离子色谱法', 'HJ?1005-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2018', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B025', '2022-10-20 15:05:53', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed3028d3-74e2-11ec-bdd3-43f738053e8f', '环境空气PM10和PM2.5的测定 重量法', 'HJ 618-2011', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2011', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed304fe4-74e2-11ec-bdd3-43f738053e8f', '环境空气?总烃、甲烷和非甲烷总烃的测定?直接进样-气相色谱法', 'HJ 604-2017', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2017', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed324bb5-74e2-11ec-bdd3-43f738053e8f', '环境空气?醛、酮类化合物的测定?高效液相色谱法', 'HJ 683- 2014', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2014', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 09:48:35', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed330f06-74e2-11ec-bdd3-43f738053e8f', '环境空气?颗粒物中水溶性阳离子（Li+、Na+、NH4+、K+、Ca2+、Mg2+）的测定?离子色谱法', 'HJ 800-2016', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2016', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 09:44:37', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed333617-74e2-11ec-bdd3-43f738053e8f', '环境空气中氡的标准测量方法 附录C 连续氡监测仪法', 'GB/T 14582-1993', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 1993', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 08:56:54', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed33d258-74e2-11ec-bdd3-43f738053e8f', '环境空气和废气 酰胺类化合物的测定 液相色谱法', 'HJ 801-2016', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2016', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 09:48:46', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed3495a9-74e2-11ec-bdd3-43f738053e8f', '环境空气和废气?颗粒物中砷、硒、铋、锑的测定?原子荧光法', 'HJ 1133-2020', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2020', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:46:52', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed3558fc-74e2-11ec-bdd3-43f738053e8f', '环境空气气态污染物（SO2、NO2、O3、CO）连续自动监测系统技术要求及检测方法', 'HJ 654-2013', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2013', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed35a71d-74e2-11ec-bdd3-43f738053e8f', '环境空气颗粒物（PM10和PM2.5）连续自动监测系统技术要求及检测方法', 'HJ 653-2013', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2013', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 09:46:38', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed35f53e-74e2-11ec-bdd3-43f738053e8f', '生活饮用水标准检验方法 农药指标', 'GB/T 5750.9-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2006', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 16:11:50', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed36917f-74e2-11ec-bdd3-43f738053e8f', '生活饮用水标准检验方法 有机物指标', 'GB/T 5750.8-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2006', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 16:28:42', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed36dfa0-74e2-11ec-bdd3-43f738053e8f', '生活饮用水标准检验方法 消毒副产物指标', 'GB/T 5750.10-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2006', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed3706b1-74e2-11ec-bdd3-43f738053e8f', '甲基橙指示剂滴定法', '《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2006', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 11:25:40', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed3754d2-74e2-11ec-bdd3-43f738053e8f', '石墨炉原子吸收分光光度法', '《空气和废气监测分析方法》（第四版增补版）国家环保总局（2007年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2007', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 09:05:44', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed383f33-74e2-11ec-bdd3-43f738053e8f', '石墨炉原子吸收法', '《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, '2006年', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 11:25:46', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed386644-74e2-11ec-bdd3-43f738053e8f', '碘量法', '《空气和废气监测分析方法》（第四版增补版）国家环保总局（2007年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2007', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 09:06:06', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed388d55-74e2-11ec-bdd3-43f738053e8f', '碱片-离子色谱法', '《空气和废气监测分析方法》（第四版增补版）国家环保总局（2007年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2007', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 09:06:23', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed38b466-74e2-11ec-bdd3-43f738053e8f', '碱片-重量法', '《空气和废气监测分析方法》（第四版增补版）国家环保总局（2007年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2007', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 09:06:33', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed38db77-74e2-11ec-bdd3-43f738053e8f', '碱片-铬酸钡分光光度法', '《空气和废气监测分析方法》（第四版增补版）国家环保总局（2007年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2007', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 09:06:58', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed392999-74e2-11ec-bdd3-43f738053e8f', '空气质量 甲醛的测定 乙酰丙酮分光光度法', 'GB/T 15516-1995', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 1995', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 09:18:54', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed3950aa-74e2-11ec-bdd3-43f738053e8f', '蚕桑区桑叶氟化物含量控制标准', 'DB 33/392-2003', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2003', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed3977bb-74e2-11ec-bdd3-43f738053e8f', '表面污染测定 第1部分：β发射体（Eβmax＞0.15MeV）和α发射体', 'GB/T 14056.1-2008', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2008', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 08:47:32', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed399ecc-74e2-11ec-bdd3-43f738053e8f', '酚酞指示剂滴定法', '《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2006', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 11:25:53', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed39eced-74e2-11ec-bdd3-43f738053e8f', '酸碱指示剂滴定法', '《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2006', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 11:26:04', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed3a621e-74e2-11ec-bdd3-43f738053e8f', '重量法', '《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2006', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 11:26:15', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed3a892f-74e2-11ec-bdd3-43f738053e8f', '重铬酸钾容量法', '《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2006', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 11:26:30', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed3ab040-74e2-11ec-bdd3-43f738053e8f', '钼锑抗分光光度法', '《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2006', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-08 11:26:36', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed3afe61-74e2-11ec-bdd3-43f738053e8f', '镜检法', '《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2006', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 09:09:34', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed467012-74e2-11ec-bdd3-43f738053e8f', '高压架空输电线、变电站无线电干扰测量方法', 'GB/T 7349-2002', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', null, ' 2002', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed557481-45c7-4123-a450-a9ae5f2576cf', '水质 铍的测定 石墨炉原子吸收分光光度法', 'HJ/T 59-2000', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ/T 59-2000', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-09-20 11:09:16', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-09-20 11:09:16', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ed77a89d-35e2-447d-b92f-9fb1163e7da6', '燃煤电厂大气污染物排放标准 附录B 附录C', 'DB 31/963-2016', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'DB 31/963-2016', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ede3598f-ed66-4203-9fae-79a30adff88f', '生活饮用水标准检测方法 金属指标 7.1 硒 氢化物原子荧光法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('eecbad44-70e0-4d29-80e5-8bb4671149ab', '4-氨基安替比林分光光度法', '《空气和废气监测分析方法》（第四版增补版）国家环保总局（2007年）', 0, '2007-01-01', null, 1, 0, null, '00000000-0000-0000-0000-000000000000', null, null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 09:56:09', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-04-30 15:16:13', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('f128f9a6-42b1-4363-b6f1-999014b7184f', '城镇污水水质标准检验方法 37 硝基苯类 还原-偶氮光度法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('f218f1f0-360b-430b-8a99-1d8f62736120', '水质 氨氮的测定 流动分析（CFA和FIA）法和光谱测定法', 'ISO11732-2005', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'ISO11732-2005', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-19 15:34:46', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('f2b02bbd-b576-4029-8e42-1d379f30afd4', '生活饮用水标准检测方法 金属指标 1.4 电感耦合等离子体发射光谱法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('f5d769d1-3d28-4c9c-b62c-8de803b68f01', '城镇污水水质标准检验方法 27 总磷的测定 27.1 抗坏血酸还原钼蓝分光光度法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-02-18 15:40:23', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('f614af1b-ac20-4300-b1f9-addc240d5f46', '水质 丁基黄原酸的测定 吹扫捕集/气相色谱-质谱法', 'HJ 896-2017', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 896-2017', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-09-20 11:22:03', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-09-20 11:22:03', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('fab2dd95-eee1-4976-8b6b-ac5f336477d2', '城镇污水水质标准检验方法 49 总镍的测定 49.2 电感耦合等离子体发射光谱法', 'CJ/T 51-2018', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'CJ/T 51-2018', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('fc17cf5c-4945-4855-a03f-e73cc143da70', '生活饮用水标准检验方法 金属指标 8.2 冷原子吸收法', 'GB/T 5750.6-2006', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'GB/T 5750.6-2006', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('fc1d8523-c9d1-449b-9563-82b1f8be8b1d', '水质 乙腈的测定 吹扫捕集/气相色谱法', 'HJ 788-2016', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'HJ 788-2016', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('fd75b1a3-1ba9-4fca-8ab2-ddb50d465557', '水质 可吸附有机卤素（AOX）的测定 微库仑法', 'HJ 1214-2021', 0, '1753-01-01', null, 1, 0, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-04-29 13:11:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-04-29 13:11:50', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('fe0b32f6-f63a-4190-b58f-0ac95cc3237d', '水质 松节油的测定 气相色谱法', 'HJ 696-2014', 0, '1753-01-01', null, 1, 1, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 18:03:48', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 18:03:48', 0, null, 0, 0, null, 1);
insert into "TB_LIM_ANALYZEMETHOD" ("id", "methodName", "countryStandard", "isDeleted", "effectiveDate", "methodCode", "isCompleteTogether", "isControlled", "remark", "parentId", "countryStandardName", "yearSn", "effectiveDays", "warningDays", "isInforce", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate", "isInputBySample", "alias", "isCrossDay", "isPreparation", "preparedMethod", "status") values ('ffa16274-3003-433d-a830-1a4da0f102e0', '森林土壤磷的测定', 'LY/T 1232-2015', 0, '1753-01-01', null, 1, 1, null, '00000000-0000-0000-0000-000000000000', 'LY/T 1232-2015', null, -1, -1, 1, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-31 16:06:01', 0, null, 0, 0, null, 1);
