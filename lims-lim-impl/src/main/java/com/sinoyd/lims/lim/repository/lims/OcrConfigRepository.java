package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfig;

import java.util.List;


/**
 * 分析项目排序详情仓储
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
public interface OcrConfigRepository extends IBaseJpaRepository<DtoOcrConfig, String> {

    /**
     *
     * @param instrumentCode
     * @return
     */
    List<DtoOcrConfig> findByWwInstrumentCode(String instrumentCode);
}