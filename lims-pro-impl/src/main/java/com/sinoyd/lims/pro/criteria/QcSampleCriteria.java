package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.lims.pro.dto.customer.DtoQCSampleCensus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 质控样品数据统计查询条件
 * <AUTHOR>
 * @version V1.0.0 2022/11/17
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QcSampleCriteria extends BaseCriteria implements Serializable {

    /**
     * 分析开始日期
     */
    private String startTime;

    /**
     * 分析结束日期
     */
    private String endTime;

    /**
     * 采样开始日期
     */
    private String samplingStartTime;

    /**
     * 采样结束日期
     */
    private String samplingEndTime;

    /**
     * 样品类型id
     */
    private String sampleTypeId;

    /**
     * 质控等级
     */
    private Integer qcGrade;

    /**
     * 质控类型
     */
    private Integer qcType;

    /**
     * 是否合格
     */
    private Integer isPass;

    /**
     * 样品编号
     */
    private String sampleKey;

    /**
     * 分析项目
     */
    private String analyzeItem;

    /**
     * 分析方法
     */
    private String analyzeMethod;

    /**
     * 获取查询条件拼接语句
     *
     * @return 查询条件
     */
    @Override
    public String getCondition() {
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.sampleTypeId)) {
            condition.append(" and a.sampleTypeId = ?");
        }
        if (StringUtil.isNotEmpty(this.sampleKey)) {
            condition.append(" and a.code like ? ");
        }
        if (StringUtils.isNotNullAndEmpty(this.qcGrade)) {
            condition.append(" and a.qcGrade = ?");
        }
        if (StringUtils.isNotNullAndEmpty(this.qcType)) {
            condition.append(" and a.qcType = ?");
        }
        if (StringUtils.isNotNullAndEmpty(this.samplingStartTime)) {
            condition.append(" and a.samplingTimeBegin >= ?");
        }
        if (StringUtils.isNotNullAndEmpty(this.samplingEndTime)) {
            condition.append(" and a.samplingTimeBegin <= ?");
        }
        return condition.toString();
    }

    /**
     * 获取查询条件的占位符值
     *
     * @return 占位符数组
     */
    public String[] getConditonValue() {
        List<String> value = new ArrayList<>();
        if (StringUtil.isNotEmpty(this.sampleTypeId)) {
            value.add(this.sampleTypeId);
        }
        if (StringUtil.isNotEmpty(this.sampleKey)) {
            value.add("%"+this.sampleKey+"%");
        }
        if (StringUtils.isNotNullAndEmpty(this.qcGrade)) {
            value.add(this.qcGrade.toString());
        }
        if (StringUtils.isNotNullAndEmpty(this.qcType)) {
            value.add(this.qcType.toString());
        }
        if (StringUtils.isNotNullAndEmpty(this.samplingStartTime)) {
            value.add(this.samplingStartTime);
        }
        if (StringUtils.isNotNullAndEmpty(this.samplingEndTime)) {
            Date to = DateUtil.stringToDate(this.samplingEndTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            to = c.getTime();
            String samplingTimeEnd = DateUtil.dateToString(to, DateUtil.YEAR);
            value.add(samplingTimeEnd);
        }
        if (StringUtil.isNotEmpty(value)) {
            return value.stream().toArray(String[]::new);
        }
        return new String[]{};
    }

    /**
     * 处理特殊的查询条件
     *
     * @param resultData 需要处理的集合
     * @return 处理后的集合
     */
    public List<DtoQCSampleCensus> getListBySpecialCondition(List<DtoQCSampleCensus> resultData) {
        //处理条件
        //分析开始日期
        if (StringUtil.isNotEmpty(this.startTime)) {
            if (StringUtil.isNotEmpty(resultData)) {
                resultData = resultData.stream().filter(p -> {
                    boolean result = false;

                    if (StringUtil.isNotEmpty(p.getAnalyseTimes())) {
                        if (DateUtil.stringToDate(p.getAnalyseTimes().get("startTime").toString(), DateUtil.YEAR)
                                .compareTo(DateUtil.stringToDate(this.startTime, DateUtil.YEAR)) >= 0) {
                            result = true;
                        }
                    } else {
                        if (StringUtil.isNotEmpty(p.getAnalyseTime())) {
                            if (DateUtil.stringToDate(p.getAnalyseTime(), DateUtil.YEAR)
                                    .compareTo(DateUtil.stringToDate(this.startTime, DateUtil.YEAR)) >= 0) {
                                result = true;
                            }
                        }
                    }
                    return result;
                }).collect(Collectors.toList());
            }
        }
        //分析结束日期
        if (StringUtil.isNotEmpty(this.endTime)) {
            if (StringUtil.isNotEmpty(resultData)) {
                resultData = resultData.stream().filter(p -> {
                    boolean result = false;
                    if (StringUtil.isNotEmpty(p.getAnalyseTimes())) {
                        if (DateUtil.stringToDate(p.getAnalyseTimes().get("endTime").toString(), DateUtil.YEAR)
                                .compareTo(DateUtil.stringToDate(this.endTime, DateUtil.YEAR)) <= 0) {
                            result = true;
                        }
                    } else {
                        if (StringUtil.isNotEmpty(p.getAnalyseTime())) {
                            if (DateUtil.stringToDate(p.getAnalyseTime(), DateUtil.YEAR)
                                    .compareTo(DateUtil.stringToDate(this.endTime, DateUtil.YEAR)) <= 0) {
                                result = true;
                            }
                        }
                    }
                    return result;
                }).collect(Collectors.toList());
            }
        }
        //分析项目名称
        if (StringUtil.isNotEmpty(this.analyzeItem)) {
            if (StringUtil.isNotEmpty(resultData)) {
                resultData = resultData.stream()
                        .filter(p -> p.getRedAnalyseItems().contains(this.analyzeItem))
                        .collect(Collectors.toList());
            }
        }
        //分析方法
        if (StringUtil.isNotEmpty(this.analyzeMethod)) {
            if (StringUtil.isNotEmpty(resultData)) {
                resultData = resultData.stream()
                        .filter(p -> p.getRedAnalyseMethods().contains(this.analyzeMethod))
                        .collect(Collectors.toList());
            }
        }
        //是否合格
        if (StringUtils.isNotNullAndEmpty(this.isPass)) {
            if (StringUtil.isNotEmpty(resultData)) {
                resultData = resultData.stream()
                        .filter(p -> this.isPass.equals(p.getIsPass()))
                        .collect(Collectors.toList());
            }
        }
        return resultData;
    }
}
