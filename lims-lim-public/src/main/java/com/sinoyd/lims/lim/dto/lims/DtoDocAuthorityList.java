package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.DocAuthorityList;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * DtoDocAuthorityList实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/12/19
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_DocAuthorityList")
@Data
@DynamicInsert
public class DtoDocAuthorityList extends DocAuthorityList {

    /**
     * 人数
     */
    @Transient
    private Integer userNums;

    /**
     * id集合
     */
    @Transient
    private List<String> ids;

    /**
     * 权限编码集合
     */
    @Transient
    private List<String> authCodes;

    /**
     * 用户id集合
     */
    @Transient
    private List<String> userIds;

}
