package com.sinoyd.lims.probase.service.impl;

import com.sinoyd.SpringContextAware;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.probase.service.CheckSampleFactoryService;
import com.sinoyd.lims.probase.service.CheckSampleService;
import org.springframework.stereotype.Service;

@Service
public class CheckSampleFactoryServiceImpl implements CheckSampleFactoryService {

    /**
     * 不同项目核对样品状态
     *
     * @param type 包名
     * @param id   样品id
     */
    @Override
    public void checkSampleByType(String type, String id) {
        checkSample(type, id);
    }

    @Override
    public void checkSampleByType(String type, DtoSample dtoSample) {
        checkSample(type, dtoSample);
    }

    private void checkSample(String type, Object obj) {
        try {
            Class checkClass = Class.forName(type);//用描述作为包的命名
            CheckSampleService checkSampleService = SpringContextAware.getBean(checkClass);
            if (obj instanceof DtoSample) {
                checkSampleService.checkSample((DtoSample) obj);
            } else if (obj instanceof String) {
                checkSampleService.checkSample((String) obj);
            }
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
            throw new BaseException(ex);
        }
    }
}
