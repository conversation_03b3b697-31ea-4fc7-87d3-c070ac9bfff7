package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.pro.dto.DtoReportRecover2Report;
import com.sinoyd.lims.pro.repository.ReportRecover2ReportRepository;
import com.sinoyd.lims.pro.service.ReportRecover2ReportService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.stereotype.Service;


/**
 * reportrecover2report操作接口
 * <AUTHOR>
 * @version V1.0.0 2022/4/14
 * @since V100R001
 */
 @Service
public class ReportRecover2ReportServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoReportRecover2Report,String, ReportRecover2ReportRepository> implements ReportRecover2ReportService {

    @Override
    public void findByPage(PageBean<DtoReportRecover2Report> pb, BaseCriteria reportrecover2reportCriteria) {
        pb.setEntityName("Dtoreportrecover2report a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, reportrecover2reportCriteria);
    }
}