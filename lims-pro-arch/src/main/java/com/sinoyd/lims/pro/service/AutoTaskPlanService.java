package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.monitor.dto.lims.DtoFixedPointProperty;
import com.sinoyd.lims.pro.dto.DtoAutoTaskPlan;

import java.util.List;
import java.util.Map;

/**
 * 自动任务下达数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/15
 * @since V100R001
 */
public interface AutoTaskPlanService extends IBaseJpaService<DtoAutoTaskPlan, String> {

    /**
     * 根据任务id获取绑定监测计划数据
     *
     * @param taskId 任务id
     * @return List<DtoFixedPointProperty>
     */
    List<DtoFixedPointProperty> getPropertyByTaskId(String taskId);

    /**
     * 任务自动生成
     *
     * @param taskId 任务id
     */
    void autoCreateProject(String taskId);

    /**
     * 保存任务
     *
     * @param entity 实体
     * @return Map<String, Object>
     */
    Map<String, Object> savePlan(DtoAutoTaskPlan entity);

}
