package com.sinoyd.lims.pro.service.impl;

import com.jsoniter.JsonIterator;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoGenerateSN;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoTestPost;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.lims.lim.dto.rcc.DtoSerialIdentifierConfig;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.GenerateSerialNumberService;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.lim.service.ProjectTypeService;
import com.sinoyd.lims.lim.service.SerialIdentifierConfigService;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.OrderContractRepository;
import com.sinoyd.lims.pro.repository.SampleRepository;
import com.sinoyd.lims.pro.service.QualityControlService;
import com.sinoyd.lims.pro.service.ReportService;
import com.sinoyd.lims.pro.service.SerialNumberService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class SerialNumberServiceImpl {


    /**
     * 业务逻辑处理样品编号的生成
     *
     * <AUTHOR>
     * @version V1.0.0 2019/10/25
     * @since V100R001
     */
    @Service("sample")
    public class SampleSerialNumberServiceImpl implements SerialNumberService {

        @Autowired
        private QualityControlService qualityControlService;

        @Autowired
        private GenerateSerialNumberService generateSerialNumberService;

        @Autowired
        private PersonService personService;

        @Autowired
        private SerialIdentifierConfigService serialIdentifierConfigService;

        @Autowired
        private RedisTemplate redisTemplate;

        @Autowired
        private SampleRepository sampleRepository;

        @Transactional
        @Override
        public String createNewNumber(Object... params) {
            DtoGenerateSN returnMap = createNewNumber(false, params);
            return returnMap.getCode();
        }

        @Override
        public DtoGenerateSN createNewNumber(Boolean isAutoCommitSN, Object... params) {
            log.info("=======================================开始编号生成方法SampleSerialNumberServiceImpl.createNewNumber======================================");
            //项目信息
            DtoProject project = (DtoProject) params[0];

            //项目类型id
            DtoProjectType projectType = (DtoProjectType) params[1];

            //样品类型信息（小类的样品信息）
            DtoSampleType dtoSampleType = (DtoSampleType) params[2];


            //样品类型信息（大类的样品信息）
            DtoSampleType dtoSampleTypeParent = (DtoSampleType) params[3];

            //点位信息
            DtoSampleFolder dtoSampleFolder = (DtoSampleFolder) params[4];

            //采样时间
            Date samplingTimeBegin = (Date) params[5];

            //是否是清除样品编号，重新新建编号
            Boolean isCreate = (Boolean) params[6];

            isCreate = StringUtil.isNull(isCreate) ? false : isCreate;

            //人员id
            String currentUserId = String.valueOf(params[7]);

            //岗位信息
            DtoTestPost testPost = (DtoTestPost) params[15];

            //送样单信息
            DtoReceiveSampleRecord record = (DtoReceiveSampleRecord) params[16];

            //是否是质控信息
            Boolean isQC = false;

            //原样id
            String associateSampleId = UUIDHelper.GUID_EMPTY;

            //质控id
            String qcId = UUIDHelper.GUID_EMPTY;

            //样品类别
            Integer sampleCategory = EnumPRO.EnumSampleCategory.原样.getValue();

            //原样编号
            String associateSampleCode = "";

            //说明传了质控相应的信息
            Integer qcType = -1;
            Integer qcGrade = -1;
            if (params.length == 17) {
                isQC = (Boolean) params[8];
                associateSampleId = String.valueOf(params[9]);
                qcId = String.valueOf(params[10]);
                sampleCategory = Integer.valueOf(String.valueOf(params[11]));
                qcType = (Integer) params[12];
                qcGrade = (Integer) params[13];
                associateSampleCode = String.valueOf(params[14]);
            }

            isQC = StringUtil.isNull(isQC) ? false : isQC;

            //人员信息
            String userNo = getPerson(currentUserId);

            Map<String, Object> dataMap = new HashMap<>();

            //样品相关信息
            Map<String, Object> sampleMap = new HashMap<>();

            String format = "";

            //说明是质控样
            if (isQC || !sampleCategory.equals(EnumPRO.EnumSampleCategory.原样.getValue())) {
                if (!StringUtils.isNotNullAndEmpty(associateSampleCode)) {
                    DtoSample ySample = sampleRepository.findOne(associateSampleId);
                    if (StringUtil.isNotNull(ySample)) {
                        associateSampleCode = ySample.getCode();
                        sampleMap.put("samplingTimeBegin", ySample.getSamplingTimeBegin());
                        log.info("==================================如果原样编号为空，则根据原样id查询原样，并处理质控样采样时间======================");
                        log.info("==================================采样时间：" + ySample.getSamplingTimeBegin());
                    }
                } else {
                    sampleMap.put("samplingTimeBegin", samplingTimeBegin);
                    log.info("==================================如果原样编号不为空，则获取原样上传值的采样时间======================");
                    log.info("==================================采样时间：" + samplingTimeBegin);
                }
                sampleMap.put("associateSampleCode", associateSampleCode);

                if (sampleCategory.equals(EnumPRO.EnumSampleCategory.质控样.getValue())) {
                    if (qcType == -1 || qcGrade == -1) { //当等级未传的时候从质控数据中获取
                        DtoQualityControl qualityControl = qualityControlService.findOne(qcId);
                        if (StringUtil.isNotNull(qualityControl)) {
                            qcType = qualityControl.getQcType();
                            qcGrade = qualityControl.getQcGrade();
                        }
                    }
                } else {
                    EnumPRO.EnumSampleCategory category = EnumPRO.EnumSampleCategory.getByValue(sampleCategory);
                    if (StringUtil.isNotNull(category)) {
                        qcType = category.getQcType();
                        qcGrade = category.getQcGrade();
                    }
                }
                String typeId = "";
                if (StringUtil.isNotNull(projectType)) {
                    typeId = projectType.getId();
                }
                DtoSerialIdentifierConfig dtoSerialIdentifierConfig = serialIdentifierConfigService.findByConfigType(EnumLIM.EnumIdentifierConfig.质控样编号.getValue(),
                        qcGrade,
                        qcType, typeId);
                if (StringUtil.isNotNull(dtoSerialIdentifierConfig)) {
                    format = dtoSerialIdentifierConfig.getConfigRule();
                }
            } else { //说明是原样的
                sampleMap.put("associateSampleCode", "");
                sampleMap.put("samplingTimeBegin", samplingTimeBegin);
                DtoSerialIdentifierConfig dtoSerialIdentifierConfig;
                if (StringUtil.isNotNull(projectType)) {
                    dtoSerialIdentifierConfig = serialIdentifierConfigService.findByConfigTypeAndProjectType(EnumLIM.EnumIdentifierConfig.样品编号.getValue(), projectType.getId());
                    if (StringUtil.isNull(dtoSerialIdentifierConfig)) {
                        dtoSerialIdentifierConfig = serialIdentifierConfigService.findByConfigType(EnumLIM.EnumIdentifierConfig.样品编号.getValue());
                    }
                } else {
                    dtoSerialIdentifierConfig = serialIdentifierConfigService.findByConfigType(EnumLIM.EnumIdentifierConfig.样品编号.getValue());
                }
                if (StringUtil.isNotNull(dtoSerialIdentifierConfig)) {
                    format = dtoSerialIdentifierConfig.getConfigRule();
                }
            }
            Map<String, Object> projectMap = new HashMap<>();
            if (StringUtil.isNotNull(project)) {
                projectMap.put("projectCode", project.getProjectCode());
                projectMap.put("inputTime", project.getInputTime());
            }
            Map<String, Object> projectTypeMap = new HashMap<>();
            if (StringUtil.isNotNull(projectType)) {
                projectTypeMap.put("mark", projectType.getMark());
                String config = projectType.getConfig();
                if (StringUtils.isNotNullAndEmpty(config)) {
                    try {
                        Map<String, Object> configMap = JsonIterator.deserialize(config, Map.class);
                        projectTypeMap.put("station", configMap.get("station"));
                    } catch (Exception ex) {
                        System.out.println(ex.getMessage());
                    }
                }
            }
            Map<String, Object> sampleTypeMap = new HashMap<>();
            if (StringUtil.isNotNull(dtoSampleType)) {
                sampleTypeMap.put("shortName", dtoSampleType.getShortName());
            }
            if (StringUtil.isNotNull(dtoSampleTypeParent)) {
                sampleTypeMap.put("bigShortName", dtoSampleTypeParent.getShortName());
            }
            Map<String, Object> sampleFolderMap = new HashMap<>();
            if (StringUtil.isNotNull(dtoSampleFolder)) {
                sampleFolderMap.put("folderCode", dtoSampleFolder.getFolderCode());
            }
            Map<String, Object> personMap = new HashMap<>();
            if (StringUtils.isNotNullAndEmpty(userNo)) {
                personMap.put("userNo", userNo);
            }
            Map<String, Object> testPostMap = new HashMap<>();
            if (StringUtil.isNotNull(testPost)) {
                testPostMap.put("postCode", testPost.getPostCode());
            }
            Map<String, Object> recordMap = new HashMap<>();
            if (StringUtil.isNotNull(record)) {
                recordMap.put("sendTime", record.getSendTime());
            }
            //当前时间
            dataMap.put("time", new Date());
            dataMap.put("sample", sampleMap);
            dataMap.put("project", projectMap);
            dataMap.put("projectType", projectTypeMap);
            dataMap.put("sampleType", sampleTypeMap);
            dataMap.put("sampleFolder", sampleFolderMap);
            dataMap.put("person", personMap);
            dataMap.put("testPost", testPostMap);
            dataMap.put("receiveSampleRecord", recordMap);
            //取出当前编号信息
            DtoGenerateSN currentSN = generateSerialNumberService.generateCurrentSN(format, dataMap);

            //当前编号
            String currentCode = String.valueOf(currentSN.getCode());

            //当前使用人员
            String inputPersonId = String.valueOf(currentSN.getInputPersonId());

            String code = currentCode;

            String redisKey = "sampleCode:" + PrincipalContextUser.getPrincipal().getOrgId();

//            if (!isCreate) {
//                Integer count = sampleRepository.countByCode(currentCode);
//                if ((StringUtil.isNull(count) || count == 0) && inputPersonId.equals(currentUserId)) {
//                    //如果不是生成完之后保存流水号，这边还需要根据编号再判断一次是否存在
//                    if (!isAutoCommitSN) {
//                        Object value = redisTemplate.opsForValue().get(redisKey);
//                        if (StringUtils.isNotNullAndEmpty(value) && currentCode.equals(value)) {
//                            isCreate = true;
//                        } else {
//                            code = currentCode;
//                        }
//                    } else {
//                        code = currentCode;
//                    }
//                } else {
//                    isCreate = true;
//                }
//            }

            if (!isCreate) {
                isCreate = checkCode(code, isAutoCommitSN, redisKey);
            }

            while (isCreate) {
                if (!StringUtil.isNotEmpty(format)) {
                    throw new BaseException("未找到编号配置");
                }
                code = nextSampleCode(isCreate, code, currentSN, format, dataMap, isAutoCommitSN);
                isCreate = checkCode(code, isAutoCommitSN, redisKey);
            }
            //重新赋值样品编号
            currentSN.setCode(code);
            if (!isAutoCommitSN) {
                //将样品编号临时存放缓存中
                redisTemplate.opsForValue().set(redisKey, code, 1800, TimeUnit.SECONDS);
            }
            return currentSN;
        }

        private String nextSampleCode(Boolean isCreate, String code, DtoGenerateSN currentSN,
                                      String format, Map<String, Object> dataMap, Boolean isAutoCommitSN) {
            if (isCreate) {
                DtoGenerateSN nextGenerateSN = generateSerialNumberService.generateNextSN(format, dataMap, isAutoCommitSN);
                code = nextGenerateSN.getCode();
                currentSN.setSerialNumberConfigUpdate(nextGenerateSN.getSerialNumberConfigUpdate());
                currentSN.setSerialNumberConfigCreate(nextGenerateSN.getSerialNumberConfigCreate());
                currentSN.setCurrentSerialNumberType(nextGenerateSN.getCurrentSerialNumberType());
                currentSN.setCurrentPara0(nextGenerateSN.getCurrentPara0());
                currentSN.setCurrentPara1(nextGenerateSN.getCurrentPara1());
                currentSN.setCurrentPara2(nextGenerateSN.getCurrentPara2());
                currentSN.setCurrentPara3(nextGenerateSN.getCurrentPara3());
            }
            return code;
        }

        private Boolean checkCode(String currentCode, Boolean isAutoCommitSN, String redisKey) {
            Boolean isCreate = Boolean.FALSE;
            Integer count = sampleRepository.countByCode(currentCode);
            if ((StringUtil.isNull(count) || count == 0)) {
                //如果不是生成完之后保存流水号，这边还需要根据编号再判断一次是否存在
                if (!isAutoCommitSN) {
                    Object value = redisTemplate.opsForValue().get(redisKey);
                    if (StringUtils.isNotNullAndEmpty(value) && currentCode.equals(value)) {
                        isCreate = Boolean.TRUE;
                    }
                }
            } else {
                isCreate = Boolean.TRUE;
            }
            return isCreate;
        }

        /**
         * 将人员信息临时的缓存起来
         *
         * @param dtoPerson 人员信息
         */
        private void savePerson(DtoPerson dtoPerson) {
            redisTemplate.opsForValue().set(dtoPerson.getId() + PrincipalContextUser.getPrincipal().getOrgId(), dtoPerson.getUserNo(), 120, TimeUnit.SECONDS);
        }

        /**
         * 从缓存中获取数据
         *
         * @param personId 人员id
         * @return 返回人员编号
         */
        private String getPerson(String personId) {
            Object object = redisTemplate.opsForValue().get(personId + PrincipalContextUser.getPrincipal().getOrgId());
            if (StringUtils.isNotNullAndEmpty(object)) {
                return (String) object;
            }
            //人员信息
            DtoPerson dtoPerson = personService.findOne(personId);
            if (StringUtil.isNotNull(dtoPerson)) {
                return dtoPerson.getUserNo();
            }
            return "";
        }
    }

    /**
     * 业务逻辑处理室内样品编号的生成
     *
     * <AUTHOR>
     * @version V1.0.0 2019/10/25
     * @since V100R001
     */
    @Service("innerSample")
    public class InnerSampleSerialNumberServiceImpl implements SerialNumberService {


        @Autowired
        private GenerateSerialNumberService generateSerialNumberService;

        @Autowired
        private SerialIdentifierConfigService serialIdentifierConfigService;

        @Transactional
        @Override
        public String createNewNumber(Object... params) {

            //样品类别
            Integer sampleCategory = Integer.valueOf(String.valueOf(params[0]));

            //质控等级
            Integer qcGrade = Integer.valueOf(String.valueOf(params[1]));

            //质控类型
            Integer qcType = Integer.valueOf(String.valueOf(params[2]));

            //原样的样品信息
            DtoSample parentSample = (DtoSample) params[3];

            //检测单信息
            DtoWorkSheetFolder dtoWorkSheetFolder = (DtoWorkSheetFolder) params[4];

            //标样编号
            String byCode = String.valueOf(params[5]);

            EnumPRO.EnumSampleCategory category = EnumPRO.EnumSampleCategory.getByValue(sampleCategory);
            if (StringUtil.isNotNull(category) && !category.equals(EnumPRO.EnumSampleCategory.质控样)) {
                qcType = category.getQcType();
                qcGrade = category.getQcGrade();
            }

            String format = "";

            Map<String, Object> dataMap = new HashMap<>();

            if (StringUtil.isNotNull(qcGrade) && StringUtil.isNotNull(qcType)) {
                DtoSerialIdentifierConfig dtoSerialIdentifierConfig = serialIdentifierConfigService.findByConfigType(EnumLIM.EnumIdentifierConfig.质控样编号.getValue(),
                        qcGrade,
                        qcType);
                if (StringUtil.isNotNull(dtoSerialIdentifierConfig)) {
                    format = dtoSerialIdentifierConfig.getConfigRule();
                }
            }
            Map<String, Object> workSheetMap = new HashMap<>();
            if (StringUtil.isNotNull(dtoWorkSheetFolder)) {
                workSheetMap.put("code", dtoWorkSheetFolder.getWorkSheetCode());
            }
            Map<String, Object> sampleMap = new HashMap<>();
            if (StringUtil.isNotNull(parentSample)) {
                sampleMap.put("associateSampleCode", parentSample.getCode());
                sampleMap.put("samplingTimeBegin", parentSample.getSamplingTimeBegin());
            }
            dataMap.put("workSheet", workSheetMap);
            dataMap.put("standardSampleCode", byCode);
            dataMap.put("sample", sampleMap);
            //当前时间
            dataMap.put("time", new Date());
            return generateSerialNumberService.generateNextSN(format, dataMap);
        }

        @Override
        public DtoGenerateSN createNewNumber(Boolean isAutoCommitSN, Object... params) {
            return null;
        }
    }

    /**
     * 检测单编号的生成方法
     *
     * <AUTHOR>
     * @version V1.0.0 2019/10/25
     * @since V100R001
     */
    @Service("workSheet")
    public class WorkSheetSerialNumberServiceImpl implements SerialNumberService {
        @Autowired
        private SerialIdentifierConfigService serialIdentifierConfigService;

        @Autowired
        private GenerateSerialNumberService generateSerialNumberService;

        @Override
        public String createNewNumber(Object... params) {
            Map<String, Object> dataMap = new HashMap<>();
            //当前时间
            dataMap.put("time", new Date());
            String format = "";
            DtoSerialIdentifierConfig dtoSerialIdentifierConfig =
                    serialIdentifierConfigService.findByConfigType(EnumLIM.EnumIdentifierConfig.检测单编号.getValue());
            if (StringUtil.isNotNull(dtoSerialIdentifierConfig)) {
                format = dtoSerialIdentifierConfig.getConfigRule();
            }
            return generateSerialNumberService.generateNextSN(format, dataMap);
        }

        @Override
        public DtoGenerateSN createNewNumber(Boolean isAutoCommitSN, Object... params) {
            return null;
        }
    }

    /**
     * 合同编号的生成方法
     *
     * <AUTHOR>
     * @version V1.0.0 2019/10/25
     * @since V100R001
     */
    @Service("orderContract")
    public class OrderContractSerialNumberServiceImpl implements SerialNumberService {
        @Autowired
        private SerialIdentifierConfigService serialIdentifierConfigService;

        @Autowired
        private GenerateSerialNumberService generateSerialNumberService;

        @Autowired
        private OrderContractRepository orderContractRepository;

        @Override
        public String createNewNumber(Object... params) {
            Map<String, Object> dataMap = new HashMap<>();
            //当前时间
            dataMap.put("time", new Date());
            String format = "";
            DtoSerialIdentifierConfig dtoSerialIdentifierConfig =
                    serialIdentifierConfigService.findByConfigType(EnumLIM.EnumIdentifierConfig.合同编号.getValue());
            if (StringUtil.isNotNull(dtoSerialIdentifierConfig)) {
                format = dtoSerialIdentifierConfig.getConfigRule();
            }
            String code = generateSerialNumberService.generateCurrentSN(format, dataMap).getCode();
            boolean isCreate = checkCode(code);
            while (isCreate) {
                if (!StringUtil.isNotEmpty(format)) {
                    throw new BaseException("未找到编号配置");
                }
                code = generateSerialNumberService.generateNextSN(format, dataMap);
                isCreate = checkCode(code);
            }
            return code;
        }

        @Override
        public DtoGenerateSN createNewNumber(Boolean isAutoCommitSN, Object... params) {
            Map<String, Object> dataMap = new HashMap<>();
            //当前时间
            dataMap.put("time", new Date());
            String format = "";
            DtoSerialIdentifierConfig dtoSerialIdentifierConfig =
                    serialIdentifierConfigService.findByConfigType(EnumLIM.EnumIdentifierConfig.合同编号.getValue());
            if (StringUtil.isNotNull(dtoSerialIdentifierConfig)) {
                format = dtoSerialIdentifierConfig.getConfigRule();
            }
            generateSerialNumberService.generateNextSN(format, dataMap);
            return null;
        }

        /**
         * 检查编号是否存在，是否需要创建下一个编号
         *
         * @param code 编号
         * @return 是否需要创建下一个编号
         */
        private boolean checkCode(String code) {
            Integer count = orderContractRepository.countByCode(code);
            Boolean isCreate = Boolean.FALSE;
            if ((StringUtil.isNotNull(count) && count > 0)) {
                isCreate = Boolean.TRUE;
            }
            return isCreate;
        }
    }

    /**
     * 业务逻辑处理项目编号的生成
     *
     * <AUTHOR>
     * @version V1.0.0 2019/10/25
     * @since V100R001
     */
    @Service("project")
    public class ProjectSerialNumberServiceImpl implements SerialNumberService {

        @Autowired
        private GenerateSerialNumberService generateSerialNumberService;

        @Autowired
        private ProjectTypeService projectTypeService;

        @Autowired
        private SerialIdentifierConfigService serialIdentifierConfigService;

        @Transactional
        @Override
        public String createNewNumber(Object... params) {
            //项目类型id
            String projectTypeId = String.valueOf(params[0]);
            //项目登记时间
            Date inputTime = (Date) params[1];
            DtoProjectType projectType = projectTypeService.findOne(projectTypeId);
            Map<String, Object> dataMap = new HashMap<>();
            //当前时间
            dataMap.put("time", new Date());
            Map<String, Object> projectTypeMap = new HashMap<>();
            if (StringUtil.isNotNull(projectType)) {
                String config = projectType.getConfig();
                projectTypeMap.put("mark", projectType.getMark());
                if (StringUtils.isNotNullAndEmpty(config)) {
                    String station = "";
                    try {
                        Map<String, Object> configMap = JsonIterator.deserialize(config, Map.class);
                        station = StringUtil.isNotNull(configMap.get("station")) ? String.valueOf(configMap.get("station")) : "";
                    } catch (Exception ex) {
                        System.out.println(ex.getMessage());
                    }
                    projectTypeMap.put("station", station);
                }
            }
            Map<String, Object> projectMap = new HashMap<>();
            if (StringUtil.isNotNull(inputTime)) {
                projectMap.put("inceptTime", inputTime);
            }
            //当前时间
            dataMap.put("time", new Date());
            dataMap.put("projectType", projectTypeMap);
            dataMap.put("project", projectMap);
            String format = "";
            DtoSerialIdentifierConfig dtoSerialIdentifierConfig = serialIdentifierConfigService.findByConfigTypeAndProjectType(EnumLIM.EnumIdentifierConfig.项目编号.getValue(), projectTypeId);
            if (StringUtil.isNotNull(dtoSerialIdentifierConfig)) {
                format = dtoSerialIdentifierConfig.getConfigRule();
            } else {
                dtoSerialIdentifierConfig = serialIdentifierConfigService.findByConfigType(EnumLIM.EnumIdentifierConfig.项目编号.getValue());
                if (StringUtil.isNotNull(dtoSerialIdentifierConfig)) {
                    format = dtoSerialIdentifierConfig.getConfigRule();
                }
            }
            return generateSerialNumberService.generateNextSN(format, dataMap);
        }

        @Override
        public DtoGenerateSN createNewNumber(Boolean isAutoCommitSN, Object... params) {
            return null;
        }
    }


    /**
     * 业务逻辑处理质控任务编号的生成
     *
     * <AUTHOR>
     * @version V1.0.0 2019/10/25
     * @since V100R001
     */
    @Service("zkProject")
    public class ZKProjectSerialNumberServiceImpl implements SerialNumberService {

        @Autowired
        private GenerateSerialNumberService generateSerialNumberService;

        @Autowired
        private ProjectTypeService projectTypeService;

        @Autowired
        private SerialIdentifierConfigService serialIdentifierConfigService;

        @Transactional
        @Override
        public String createNewNumber(Object... params) {
            //项目类型id
            String projectTypeId = String.valueOf(params[0]);
            //项目登记时间
            Date inputTime = (Date) params[1];
            DtoProjectType projectType = projectTypeService.findOne(projectTypeId);
            Map<String, Object> dataMap = new HashMap<>();
            //当前时间
            dataMap.put("time", new Date());
            Map<String, Object> projectTypeMap = new HashMap<>();
            String station = "";
            String mark = "";
            if (StringUtil.isNotNull(projectType)) {
                String config = projectType.getConfig();
                mark = projectType.getMark();
                if (StringUtils.isNotNullAndEmpty(config)) {
                    try {
                        Map<String, Object> configMap = JsonIterator.deserialize(config, Map.class);
                        station = StringUtil.isNotNull(configMap.get("station")) ? String.valueOf(configMap.get("station")) : "";
                    } catch (Exception ex) {
                        System.out.println(ex.getMessage());
                    }
                }
            }
            projectTypeMap.put("mark", projectType.getMark());
            projectTypeMap.put("station", station);
            Map<String, Object> projectMap = new HashMap<>();
            if (StringUtil.isNotNull(inputTime)) {
                projectMap.put("inceptTime", inputTime);
            }
            //当前时间
            dataMap.put("time", new Date());
            dataMap.put("projectType", projectTypeMap);
            dataMap.put("project", projectMap);
            String format = "";
            DtoSerialIdentifierConfig dtoSerialIdentifierConfig = serialIdentifierConfigService.findByConfigType(EnumLIM.EnumIdentifierConfig.质控任务编号.getValue());
            if (StringUtil.isNotNull(dtoSerialIdentifierConfig)) {
                format = dtoSerialIdentifierConfig.getConfigRule();
            }
            return generateSerialNumberService.generateNextSN(format, dataMap);
        }

        @Override
        public DtoGenerateSN createNewNumber(Boolean isAutoCommitSN, Object... params) {
            return null;
        }
    }

    /**
     * 业务逻辑处理送样单的生成
     *
     * <AUTHOR>
     * @version V1.0.0 2019/10/25
     * @since V100R001
     */
    @Service("receiveSampleRecord")
    public class ReceiveSampleRecordSerialNumberServiceImpl implements SerialNumberService {

        @Autowired
        private SerialIdentifierConfigService serialIdentifierConfigService;

        @Autowired
        private GenerateSerialNumberService generateSerialNumberService;

        @Transactional
        @Override
        public String createNewNumber(Object... params) {
            Map<String, Object> dataMap = new HashMap<>();
            //当前时间
            dataMap.put("time", new Date());
            String format = "";
            DtoSerialIdentifierConfig dtoSerialIdentifierConfig = serialIdentifierConfigService.findByConfigType(EnumLIM.EnumIdentifierConfig.送样单编号.getValue());
            if (StringUtil.isNotNull(dtoSerialIdentifierConfig)) {
                format = dtoSerialIdentifierConfig.getConfigRule();
            }
            return generateSerialNumberService.generateNextSN(format, dataMap);
        }

        @Override
        public DtoGenerateSN createNewNumber(Boolean isAutoCommitSN, Object... params) {
            return null;
        }
    }

    /**
     * 业务逻辑处理报告的生成
     *
     * <AUTHOR>
     * @version V1.0.0 2019/10/25
     * @since V100R001
     */
    @Service("report")
    public class ReportSerialNumberServiceImpl implements SerialNumberService {
        @Autowired
        private SerialIdentifierConfigService serialIdentifierConfigService;

        @Autowired
        private GenerateSerialNumberService generateSerialNumberService;

        @Autowired
        private ReportService reportService;

        @Override
        public String createNewNumber(Object... params) {
            //报告类型名称
            String reportName = String.valueOf(params[0]);

            //年份
            String year = String.valueOf(params[1]);

            //人员id
            String currentUserId = String.valueOf(params[2]);

            String projectCode = String.valueOf(params[3]);

            //报告编号
            String reportCode = String.valueOf(params[4]);

            Map<String, Object> report = new HashMap<>();
            report.put("name", reportName);
            report.put("year", year);

            Map<String, Object> project = new HashMap<>();
            project.put("projectCode", projectCode);

            Map<String, Object> dataMap = new HashMap<>();
            //当前时间
            dataMap.put("time", new Date());
            dataMap.put("reportType", report);
            dataMap.put("project", project);
            dataMap.put("reportCode", reportCode);
            String format = "";
            DtoSerialIdentifierConfig dtoSerialIdentifierConfig = serialIdentifierConfigService.findByConfigTypeAndConfigName(EnumLIM.EnumIdentifierConfig.报告编号.getValue(), reportName);
            if (StringUtil.isNotEmpty(reportCode)) {
                dtoSerialIdentifierConfig = serialIdentifierConfigService.findByConfigType(EnumLIM.EnumIdentifierConfig.回收报告编号.getValue());
            }
            if (StringUtil.isNotNull(dtoSerialIdentifierConfig)) {
                format = dtoSerialIdentifierConfig.getConfigRule();
            }
            //取出当前编号信息
            DtoGenerateSN generateSN = generateSerialNumberService.generateCurrentSN(format, dataMap);

            //当前编号
            String currentCode = String.valueOf(generateSN.getCode());

            //当前使用人员
            String inputPersonId = String.valueOf(generateSN.getInputPersonId());

            String code = "";
            Boolean isCreate = false;
            DtoReport dtoReport = reportService.findByCode(currentCode);
            if (StringUtil.isNull(dtoReport) && inputPersonId.equals(currentUserId)) {
                code = currentCode;
            } else {
                isCreate = true;
            }
            if (isCreate) {
                code = generateSerialNumberService.generateNextSN(format, dataMap);
            }
            return code;
        }

        @Override
        public DtoGenerateSN createNewNumber(Boolean isAutoCommitSN, Object... params) {
            return null;
        }
    }
}
