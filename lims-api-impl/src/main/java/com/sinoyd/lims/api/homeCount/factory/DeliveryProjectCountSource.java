package com.sinoyd.lims.api.homeCount.factory;

import com.sinoyd.lims.api.constant.IHomeConstant;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

/**
 * 现场监测代办数字
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
@Component(IHomeConstant.ProjectCountSourceKey.DELIVERY_PROJECT)
public class DeliveryProjectCountSource extends AbsCountSource {

    @Override
    public long homeCount() {
        //项目类型
        String code = "projectRegisterPage";
        String[] values = new String[]{"WT", "SY", "LX", "QLC"};
        String projectTypeIds = fieldMonitoringService.getProjectTypeList(code, values)
                .stream().map(p -> p.get("id").toString()).collect(Collectors.joining(","));
        return getRecordList(projectTypeIds);
    }
}
