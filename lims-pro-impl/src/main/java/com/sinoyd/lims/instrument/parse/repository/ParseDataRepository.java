package com.sinoyd.lims.instrument.parse.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.instrument.parse.dto.DtoParseData;

import java.util.List;

/**
 * 仪器解析数据DB访问接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2021/7/26
 */
public interface ParseDataRepository extends IBaseJpaRepository<DtoParseData, String> {

    /**
     * 根据样品编号列表查询解析数据
     *
     * @param sampleCodeList 样品编号列表
     * @return 解析数据列表
     */
    List<DtoParseData> findBySampleCodeIn(List<String> sampleCodeList);

    /**
     * 根据日志id询解析数据
     *
     * @param parseLogId 日志id
     * @return 解析数据列表
     */
    List<DtoParseData> findByParselogId(String parseLogId);
}
