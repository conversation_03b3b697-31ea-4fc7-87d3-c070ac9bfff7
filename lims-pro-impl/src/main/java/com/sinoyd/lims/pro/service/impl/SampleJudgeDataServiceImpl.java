package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.rcc.DimensionRepository;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.utils.base.DivationUtils;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.CalculationService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoGenerateSN;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoCompareJudge;
import com.sinoyd.lims.lim.dto.rcc.DtoSampleTypeGroup;
import com.sinoyd.lims.lim.dto.rcc.DtoSerialNumberConfig;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.CompareJudgeRepository;
import com.sinoyd.lims.lim.repository.rcc.QualityControlLimitRepository;
import com.sinoyd.lims.lim.repository.rcc.SampleTypeGroupRepository;
import com.sinoyd.lims.pro.criteria.SampleGroupRecordCriteria;
import com.sinoyd.lims.pro.criteria.SampleJudgeDataCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoAnalyseDataInquiry;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.*;
import com.sinoyd.lims.pro.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * SampleJudgeData操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/06/14
 * @since V100R001
 */
@Service
public class SampleJudgeDataServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoSampleJudgeData, String, SampleJudgeDataRepository> implements SampleJudgeDataService {

    private SampleRepository sampleRepository;

    private TestRepository testRepository;

    private SampleFolderRepository sampleFolderRepository;

    private DimensionRepository dimensionRepository;

    private SampleService sampleService;

    private CompareJudgeRepository compareJudgeRepository;

    private QualityControlLimitRepository qualityControlLimitRepository;

    private SampleTypeRepository sampleTypeRepository;

    private ProjectService projectService;

    private AnalyseDataRepository analyseDataRepository;

    private SampleFolderEvaluateRepository sampleFolderEvaluateRepository;

    private ReportDetailRepository reportDetailRepository;

    private QualityControlRepository qualityControlRepository;

    private AnalyseDataService analyseDataService;

    private ReceiveSampleRecordRepository receiveSampleRecordRepository;

    private SampleGroupService sampleGroupService;

    private SampleGroupRepository sampleGroupRepository;

    private SampleTypeGroupRepository sampleTypeGroupRepository;

    @Override
    public void findByPage(PageBean<DtoSampleJudgeData> page, BaseCriteria criteria) {
        page.setSelect("select a");
        page.setEntityName("DtoSampleJudgeData a");
        super.findByPage(page, criteria);
    }

    @Override
    public Map<String, List<DtoSampleJudgeData>> findByFolderId(SampleJudgeDataVo vo) {
        String folderId = vo.getFolderId();
        String receiveId = vo.getReceiveId();
        List<DtoSample> samples = sampleRepository.findByReceiveId(receiveId).stream().filter(s -> s.getSampleFolderId().equals(folderId)).collect(Collectors.toList());
        List<String> sampleIds = samples.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<DtoSampleJudgeData> sampleJudgeDataList = repository.findBySampleIdIn(sampleIds);
        fillingTransientFields(sampleJudgeDataList);
        sampleJudgeDataList.sort(Comparator.comparing(DtoSampleJudgeData::getOrderNum).thenComparing(DtoSampleJudgeData::getSampleCode));
        return sampleJudgeDataList.stream().collect(Collectors.groupingBy(DtoSampleJudgeData::getAnalyzeItemName, LinkedHashMap::new, Collectors.toList()));
    }

    @Override
    public Object evaluteList(BaseCriteria criteria) {
        SampleJudgeDataCriteria sampleJudgeDataCriteria = (SampleJudgeDataCriteria) criteria;
        List<DtoSampleJudgeData> sampleJudgeDataList = getData(criteria);
        sampleJudgeDataList.sort(Comparator.comparing(DtoSampleJudgeData::getFolderName)
                .thenComparing(DtoSampleJudgeData::getAnalyzeItemName).thenComparing(DtoSampleJudgeData::getOrderNum)
                .thenComparing(DtoSampleJudgeData::getSampleCode));
        setFolderPass(sampleJudgeDataList, sampleJudgeDataCriteria.getCheckType());
        if (EnumBase.EnumCheckType.废水比对.getValue().equals(sampleJudgeDataCriteria.getCheckType())) {
            return sampleJudgeDataList.stream().collect(Collectors.groupingBy(DtoSampleJudgeData::getCheckType, LinkedHashMap::new,
                    Collectors.groupingBy(DtoSampleJudgeData::getFolderName, LinkedHashMap::new,
                            Collectors.groupingBy(DtoSampleJudgeData::getAnalyzeItemName, LinkedHashMap::new,
                                    Collectors.groupingBy(DtoSampleJudgeData::getOrderNum, LinkedHashMap::new, Collectors.toList())))));
        } else if (EnumBase.EnumCheckType.废气比对.getValue().equals(sampleJudgeDataCriteria.getCheckType())) {
            return sampleJudgeDataList.stream().collect(Collectors.groupingBy(DtoSampleJudgeData::getCheckType, LinkedHashMap::new,
                    Collectors.groupingBy(DtoSampleJudgeData::getFolderName, LinkedHashMap::new,
                            Collectors.groupingBy(DtoSampleJudgeData::getAnalyzeItemName, LinkedHashMap::new, Collectors.toList()))));
        }
        return null;
    }

    private void setFolderPass(List<DtoSampleJudgeData> sampleJudgeDataList, Integer checkType) {
        Set<String> folderNameList = sampleJudgeDataList.stream().map(DtoSampleJudgeData::getFolderName).collect(Collectors.toSet());
        Set<String> anaItemNameList = sampleJudgeDataList.stream().map(DtoSampleJudgeData::getAnalyzeItemName).collect(Collectors.toSet());
        for (String folderName : folderNameList) {
            for (String itemName : anaItemNameList) {
                List<DtoSampleJudgeData> judgeDataList = sampleJudgeDataList.stream().filter(p -> p.getFolderName().equals(folderName)
                        && p.getAnalyzeItemName().equals(itemName) && StringUtil.isNotEmpty(p.getPass())).collect(Collectors.toList());
                if (EnumBase.EnumCheckType.废水比对.getValue().equals(checkType)) {
                    if (judgeDataList.size() >= 3) {
                        int count = (int) judgeDataList.stream().filter(p -> "否".equals(p.getPass())).count();
                        if (count > 1) {
                            judgeDataList.forEach(p -> {
                                p.setIsSave(Boolean.TRUE);
                                if (!StringUtil.isNotEmpty(p.getFolderPass())) {
                                    p.setFolderPass("不合格");
                                    p.setIsSave(Boolean.FALSE);
                                }
                            });
                        } else {
                            count = (int) judgeDataList.stream().filter(p -> "是".equals(p.getPass())).count();
                            if (count + 1 >= judgeDataList.size()) {
                                judgeDataList.forEach(p -> {
                                    p.setIsSave(Boolean.TRUE);
                                    if (!StringUtil.isNotEmpty(p.getFolderPass())) {
                                        p.setFolderPass("合格");
                                        p.setIsSave(Boolean.FALSE);
                                    }
                                });
                            }
                        }
                    } else {
                        judgeDataList.forEach(p -> {
                            p.setIsSave(Boolean.TRUE);
                            if (!StringUtil.isNotEmpty(p.getFolderPass())) {
                                p.setFolderPass("不予评价");
                                p.setIsSave(Boolean.FALSE);
                            }
                        });
                    }
                } else if (EnumBase.EnumCheckType.废气比对.getValue().equals(checkType)) {
                    judgeDataList.forEach(p -> {
                        p.setIsSave(Boolean.TRUE);
                        if (!StringUtil.isNotEmpty(p.getFolderPass())) {
                            if ("是".equals(p.getResultEvaluate())) {
                                p.setFolderPass("合格");
                                p.setIsSave(Boolean.FALSE);
                            } else if ("否".equals(p.getResultEvaluate())) {
                                p.setFolderPass("不合格");
                                p.setIsSave(Boolean.FALSE);
                            }
                        }
                    });
                }
            }
        }
    }

    /**
     * 根据样品和测试项目创建比对数据
     *
     * @param sampleList 样品集合
     * @param testIds    测试项目ids
     * @param allTestIds 已存在的测试项目
     * @param project    项目信息
     */
    @Transactional
    @Override
    public void createJudgeDataBySampleTest(List<DtoSample> sampleList, Collection<String> testIds, Collection<String> allTestIds, DtoProject project) {
        if (testIds.size() > 0) {
            List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
            List<DtoTest> testList = testRepository.findAll(testIds);
            testList.forEach(p -> {
                if (!allTestIds.contains(p.getId())) {
                    p.setIsCreate(Boolean.TRUE);
                }
            });
            Set<String> sampleTypeIds = sampleList.stream().map(DtoSample::getSampleTypeId).collect(Collectors.toSet());
            List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdIn(sampleIds);
            List<DtoAnalyseData> addDataList = analyseDataList.stream().filter(p -> testIds.contains(p.getTestId()))
                    .collect(Collectors.toList());
            List<DtoSampleJudgeData> saveList = new ArrayList<>();
            //要新的序列
            List<DtoSerialNumberConfig> serialNumberConfigCreateList = new ArrayList<>();
            //要修改的序列
            List<DtoSerialNumberConfig> serialNumberConfigUpdateList = new ArrayList<>();
            List<DtoSample> qcSamples = new ArrayList<>();
            //获取源领样单
            List<DtoQualityControl> qualityControls = new ArrayList<>();
            Set<String> analyzeItemIds = addDataList.stream().map(DtoAnalyseData::getAnalyseItemId).collect(Collectors.toSet());
            List<DtoCompareJudge> compareJudges = compareJudgeRepository.findByAnalyzeItemIdIn(analyzeItemIds);
            List<String> compareIds = compareJudges.stream().map(DtoCompareJudge::getId).collect(Collectors.toList());
            List<DtoQualityControlLimit> limits = qualityControlLimitRepository.findByTestIdIn(compareIds);
            List<DtoSampleType> sampleTypes = sampleTypeRepository.findAll(sampleTypeIds);
            createSampleJudgeData(sampleList, testList, sampleTypes, compareJudges, limits, saveList, qcSamples,
                    qualityControls, project, serialNumberConfigCreateList, serialNumberConfigUpdateList);
            //如果数据存在，那么不需要新增
            List<DtoSampleJudgeData> judgeDataList = repository.findBySampleIdIn(sampleIds);
            List<DtoSampleJudgeData> saveDataList = new ArrayList<>();
            saveList.forEach(p -> {
                List<DtoSampleJudgeData> dataList = judgeDataList.stream().filter(d -> p.getTestId().equals(d.getTestId())
                        && d.getSampleId().equals(p.getSampleId())).collect(Collectors.toList());
                if (dataList.size() == 0) {
                    saveDataList.add(p);
                }
            });
            if (qualityControls.size() > 0) {
                comRepository.insertBatch(qualityControls);
            }
            if (StringUtil.isNotEmpty(saveDataList)) {
                repository.save(saveDataList);
            }
            if (serialNumberConfigCreateList.size() > 0) {
                comRepository.insert(serialNumberConfigCreateList);
            }
            if (serialNumberConfigUpdateList.size() > 0) {
                comRepository.updateBatch(serialNumberConfigUpdateList);
            }
            if (qcSamples.size() > 0) {
                comRepository.insertBatch(qcSamples);
            }
        }
    }

    /**
     * 根据测试项目修改比对数据
     *
     * @param sampleIds  样品ids
     * @param testIds    测试项目ids
     * @param newTestIds 新测试项目ids
     */
    @Transactional
    @Override
    public void updateJudgeDataByTest(List<String> sampleIds, List<String> testIds, List<String> newTestIds) {
        //找到质控数据
        List<String> qcSampleIds = sampleRepository.findByAssociateSampleIdIn(sampleIds).stream().map(DtoSample::getId).collect(Collectors.toList());
        sampleIds.addAll(qcSampleIds);
        //老的比对数据
        List<DtoSampleJudgeData> judgeDataList = repository.findBySampleIdInAndTestIdIn(sampleIds, testIds);
        if (testIds.size() > 0 && newTestIds.size() > 0) {
            List<DtoTest> newTestList = testRepository.findAll(newTestIds);
            List<DtoTest> oldTestList = testRepository.findAll(testIds);
            judgeDataList.forEach(p -> {
                Optional<DtoTest> oldTest = oldTestList.stream().filter(o -> p.getTestId().equals(o.getId())).findFirst();
                if (oldTest.isPresent()) {
                    Optional<DtoTest> newTest = newTestList.stream().filter(t -> oldTest.get().getAnalyzeItemId()
                            .equals(t.getAnalyzeItemId())).findFirst();
                    newTest.ifPresent(test -> p.setTestId(test.getId()));
                }
            });
            repository.save(judgeDataList);
        }
    }

    /**
     * 删除比对数据
     *
     * @param sampleIds 样品ids
     * @param testIds   测试项目ids
     */
    @Transactional
    @Override
    public void deleteJudgeDataByTest(List<String> sampleIds, List<String> testIds, Boolean isDeleted) {
        if (sampleIds.size() > 0) {
            //找到质控数据
            List<String> qcSampleIds = sampleRepository.findByAssociateSampleIdIn(sampleIds).stream()
                    .map(DtoSample::getId).collect(Collectors.toList());
            sampleIds.addAll(qcSampleIds);
            //比对数据
            List<DtoSampleJudgeData> judgeDataList = repository.findBySampleIdIn(sampleIds);
            if (testIds.size() > 0) {
                List<String> finalTestIds = testIds;
                judgeDataList = judgeDataList.stream().filter(p -> finalTestIds.contains(p.getTestId())).collect(Collectors.toList());
            } else {
                testIds = judgeDataList.stream().map(DtoSampleJudgeData::getTestId).distinct().collect(Collectors.toList());
            }
            //需要判断是否需要删除质控样
            Set<String> receiveIds = sampleRepository.findByIds(sampleIds).stream().map(DtoSample::getReceiveId)
                    .filter(receiveId -> !UUIDHelper.GUID_EMPTY.equals(receiveId)).collect(Collectors.toSet());
            deleteQualityControlData(receiveIds, judgeDataList);
            if (isDeleted) {
                repository.delete(judgeDataList);
            }
        }
    }

    /**
     * 比对数据作废
     *
     * @param judgeDataIds 比对ids
     */
    @Transactional
    @Override
    public void cancellationData(List<String> judgeDataIds) {
        List<DtoSampleJudgeData> judgeDataList = repository.findAll(judgeDataIds);
        //数据作废
        Set<String> sampleIds = judgeDataList.stream().map(DtoSampleJudgeData::getSampleId).collect(Collectors.toSet());
        List<String> testIds = judgeDataList.stream().map(DtoSampleJudgeData::getTestId).distinct().collect(Collectors.toList());
//        List<DtoSample> samples = sampleRepository.findAll(sampleIds);
//        //纠正送样单状态
//        List<String> receiveIds = samples.stream().map(DtoSample::getReceiveId)
//                .filter(receiveId -> !receiveId.equals(UUIDHelper.GUID_EMPTY)).distinct().collect(Collectors.toList());
//        List<DtoReceiveSampleRecord> recList = receiveSampleRecordRepository.findAll(receiveIds);
//        if (recList.stream().anyMatch(p -> !EnumLIM.EnumReceiveRecordStatus.新建.getValue().equals(p.getReceiveStatus()))) {
//            throw new BaseException("样品已经送样，无法清空样品编号！");
//        }
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdIn(sampleIds);
        analyseDataList = analyseDataList.stream().filter(p -> testIds.contains(p.getTestId())).collect(Collectors.toList());
        if (analyseDataList.stream().anyMatch(p -> !UUIDHelper.GUID_EMPTY.equals(p.getWorkSheetFolderId()))) {
            throw new BaseException("测试项目检测中不允许作废!");
        }

        changeCancellation(judgeDataIds, EnumPRO.EnumAnalyseDataStatus.作废.name(),
                EnumPRO.EnumAnalyseDataStatus.作废.getValue(), Boolean.TRUE);
    }

    /**
     * 取消比对数据作废
     *
     * @param judgeDataIds 比对ids
     */
    @Transactional
    @Override
    public void cancellationCancelData(List<String> judgeDataIds) {
        changeCancellation(judgeDataIds, EnumPRO.EnumAnalyseDataStatus.未测.name(),
                EnumPRO.EnumAnalyseDataStatus.未测.getValue(), Boolean.FALSE);
    }

    @Transactional
    public void processSampleGroup(Collection<String> sampleIds) {
        List<DtoSample> samples = sampleRepository.findAll(sampleIds);
        //纠正送样单状态
        List<String> receiveIds = samples.stream().map(DtoSample::getReceiveId)
                .filter(receiveId -> !receiveId.equals(UUIDHelper.GUID_EMPTY)).distinct().collect(Collectors.toList());
        // 获取送样单下所有样品分株数据
        List<DtoSampleGroup> allSampleGroupList = sampleGroupRepository.findByReceiveIdIn(receiveIds);

        for (String receiveId : receiveIds) {
            List<DtoSampleGroup> sampleGroups = allSampleGroupList.stream().filter(p -> p.getReceiveId().equals(receiveId)).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(sampleGroups)) {
                // 是否分组标识
                Integer isGroup = sampleGroups.get(0).getIsGroup();
                // 历史数据分组类型
                List<String> sampleTypeGroupIds = sampleGroups.stream().map(DtoSampleGroup::getSampleTypeGroupId).filter(p -> !UUIDHelper.GUID_EMPTY.equals(p)).distinct().collect(Collectors.toList());
                List<String> bigSampleTypeGroupIds = null;
                if (isGroup == 1) {
                    if (StringUtil.isNotEmpty(sampleTypeGroupIds)) {
                        List<DtoSampleTypeGroup> sonSampleTypeGroups = sampleTypeGroupRepository.findAll(sampleTypeGroupIds);
                        bigSampleTypeGroupIds = StringUtil.isNotEmpty(sonSampleTypeGroups) ?
                                sampleTypeGroupRepository.findAll(sonSampleTypeGroups.stream().map(DtoSampleTypeGroup::getParentId).collect(Collectors.toList()))
                                        .stream().map(DtoSampleTypeGroup::getId).collect(Collectors.toList()) : new ArrayList<>();
                    }
                }
                if (StringUtil.isEmpty(bigSampleTypeGroupIds)) {
                    List<DtoSample> sampleList = sampleRepository.findByReceiveId(receiveId);
                    List<String> sampleTypeIds = sampleList.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
                    List<DtoSampleType> sampleTypeList = sampleTypeRepository.findAll(sampleTypeIds);
                    bigSampleTypeGroupIds = sampleTypeList.stream().map(DtoSampleType::getDefaultLabelGroupId).filter(p -> !UUIDHelper.GUID_EMPTY.equals(p)).collect(Collectors.toList());
                }

                //先删除分组数据
                List<DtoSampleGroup> sampleGroupList = sampleGroups.stream().filter(p -> sampleIds.contains(p.getSampleId())).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(sampleGroupList)) {
                    sampleGroupService.delete(sampleGroupList);
                }
                // 构建更新样品分组参数，执行更新
                SampleGroupRecordCriteria recordCriteria = new SampleGroupRecordCriteria();
                recordCriteria.setGroupIds(bigSampleTypeGroupIds);
                recordCriteria.setReceiveId(receiveId);
                recordCriteria.setIsGroup(isGroup);
                sampleGroupService.updateGroupInfo(recordCriteria);
            }
        }
    }


    @Transactional
    public void changeCancellation(List<String> judgeDataIds, String dataStatus, Integer status, Boolean isDeleted) {
        //数据作废
        List<DtoSampleJudgeData> judgeDataList = repository.findAll(judgeDataIds);
        judgeDataList.forEach(p -> p.setDataStatus(dataStatus));
        //数据作废
        Set<String> sampleIds = judgeDataList.stream().map(DtoSampleJudgeData::getSampleId).collect(Collectors.toSet());
        Set<String> testIds = judgeDataList.stream().map(DtoSampleJudgeData::getTestId).collect(Collectors.toSet());
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdIn(sampleIds);
        List<DtoAnalyseData> anaDataList = analyseDataList.stream().filter(p -> testIds.contains(p.getTestId()))
                .collect(Collectors.toList());
        //数据作废
        anaDataList.forEach(p -> {
            p.setStatus(dataStatus);
            p.setDataStatus(status);
            p.setIsDeleted(isDeleted);
            if (isDeleted) {
                p.setWorkSheetId(UUIDHelper.GUID_EMPTY);
                p.setIsDataEnabled(false);
            }
        });
        repository.save(judgeDataList);
        analyseDataRepository.save(anaDataList);
        sampleService.check(new ArrayList<>(sampleIds));
        this.processSampleGroup(sampleIds);
    }

    private void createQualityJudgeData(List<DtoCompareJudge> compareJudgeList, DtoSample sample, DtoProject project,
                                        DtoTest test, List<DtoQualityControlLimit> limitList,
                                        List<DtoSampleJudgeData> saveList, List<DtoSample> qcSamples,
                                        List<DtoQualityControl> qualityControls,
                                        List<DtoSerialNumberConfig> serialNumberConfigCreateList,
                                        List<DtoSerialNumberConfig> serialNumberConfigUpdateList) {
        for (DtoCompareJudge compareJudge : compareJudgeList) {
            for (int i = 1; i <= compareJudge.getDefaultStandardNum(); i++) {
                Map<String, Object> returnMap = sampleService.getAssociateSample(1, EnumLIM.EnumQCGrade.外部质控.getValue(),
                        EnumLIM.EnumQCType.质控样.getValue(), sample, "", "", "", "", null,
                        PrincipalContextUser.getPrincipal().getUserId(), null, project, null, "", null);
                DtoSample qcSample = (DtoSample) returnMap.get("sample");
                qcSample.setSampleCategory(EnumPRO.EnumSampleCategory.比对评价样.getValue());
                qcSample.setSampleFolderId(sample.getSampleFolderId());
                qcSample.setRedFolderName("质控样");
                qcSample.setAssociateSampleId(UUIDHelper.GUID_EMPTY);
                qcSample.setSampleTypeName(sample.getSampleTypeName());
                qcSample.setStatus(EnumPRO.EnumSampleStatus.样品在检.name());
                DtoQualityControl qc = (DtoQualityControl) returnMap.get("qualityControl");
                DtoGenerateSN generateSN = sampleService.createSampleCode(project,
                        null, sample.getSampleTypeId(), sample.getSampleFolderId(),
                        new Date(), PrincipalContextUser.getPrincipal().getUserId(),
                        sample.getId(), true, PrincipalContextUser.getPrincipal().getUserId(), true,
                        UUIDHelper.GUID_EMPTY, UUIDHelper.GUID_EMPTY,
                        EnumPRO.EnumSampleCategory.质控样.getValue(), EnumLIM.EnumQCType.质控样.getValue(), 1, true, "1");
                qcSample.setCode(generateSN.getCode());
                DtoQualityControlLimit compareJudgeConfig = limitList.stream().filter(c -> compareJudge.getId().equals(c.getTestId()) && EnumBase.EnumJudgingType.质控样比对.getValue().equals(c.getQcType())).findFirst().orElse(null);
                if (compareJudgeConfig != null) {
                    DtoSampleJudgeData sampleJudgeData = new DtoSampleJudgeData();
                    sampleJudgeData.setSampleId(qcSample.getId());
                    sampleJudgeData.setTestId(test.getId());
                    sampleJudgeData.setCheckType(compareJudge.getCheckType());
                    sampleJudgeData.setAllowLimit(compareJudgeConfig.getAllowLimit());
                    sampleJudgeData.setCheckItemValue(compareJudgeConfig.getRangeConfig());
                    sampleJudgeData.setCompareType(compareJudgeConfig.getQcType());
                    sampleJudgeData.setJudgingMethod(compareJudgeConfig.getJudgingMethod());
                    sampleJudgeData.setDimensionId(test.getDimensionId());
                    saveList.add(sampleJudgeData);
                    qcSamples.add(qcSample);
                    qualityControls.add(qc);
                    if (StringUtil.isNotNull(generateSN)) {
                        DtoSerialNumberConfig serialNumberConfigCreate = generateSN.getSerialNumberConfigCreate();
                        if (StringUtil.isNotNull(serialNumberConfigCreate)) {
                            serialNumberConfigCreateList.add(serialNumberConfigCreate);
                        }
                        DtoSerialNumberConfig serialNumberConfigUpdate = generateSN.getSerialNumberConfigUpdate();
                        if (StringUtil.isNotNull(serialNumberConfigUpdate)) {
                            serialNumberConfigUpdateList.add(serialNumberConfigUpdate);
                        }
                    }
                }
            }
        }
    }

    private void createSampleJudgeData(List<DtoSample> sampleList, List<DtoTest> testList, List<DtoSampleType> sampleTypes,
                                       List<DtoCompareJudge> compareJudgeList, List<DtoQualityControlLimit> controlLimitList,
                                       List<DtoSampleJudgeData> saveList, List<DtoSample> qcSamples,
                                       List<DtoQualityControl> qualityControls, DtoProject project,
                                       List<DtoSerialNumberConfig> serialNumberConfigCreateList,
                                       List<DtoSerialNumberConfig> serialNumberConfigUpdateList) {
        Map<String, List<DtoSample>> folderId2Sample = sampleList.stream().collect(Collectors.groupingBy(DtoSample::getSampleFolderId));
        folderId2Sample.forEach((folderId, samples) -> {
            if (!UUIDHelper.GUID_EMPTY.equals(folderId)) {
                testList.forEach(test -> {
                    Map<String, Boolean> testMap = new HashMap<>();
                    testMap.put(test.getId(), Boolean.FALSE);
                    samples.forEach(sam -> {
                        DtoSampleType sampleType = sampleTypes.stream().filter(s -> s.getId().equals(sam.getSampleTypeId())).findFirst().get();
                        Optional<DtoCompareJudge> compareJudge = compareJudgeList.stream().filter(c -> c.getCheckType().equals(sampleType.getCheckType())
                                && c.getAnalyzeItemId().equals(test.getAnalyzeItemId())).findFirst();
                        compareJudge.ifPresent(c -> {
                            //判断是否需要新增
                            if (test.getIsCreate() && !testMap.get(test.getId())) {
                                List<DtoCompareJudge> judgeList = compareJudgeList.stream().filter(judge -> judge.getCheckType().equals(sampleType.getCheckType())
                                        && judge.getAnalyzeItemId().equals(test.getAnalyzeItemId())).collect(Collectors.toList());
                                createQualityJudgeData(judgeList, sam, project, test, controlLimitList, saveList, qcSamples,
                                        qualityControls, serialNumberConfigCreateList, serialNumberConfigUpdateList);
                                testMap.put(test.getId(), Boolean.TRUE);
                            }
                            DtoQualityControlLimit compareJudgeConfig = null;
                            List<DtoQualityControlLimit> compareJudgeConfigs2AnaData;
                            if (EnumBase.EnumCheckType.废水比对.getValue().equals(compareJudge.get().getCheckType())) {
                                compareJudgeConfigs2AnaData = controlLimitList.stream().filter(config -> c.getId().equals(config.getTestId())
                                        && EnumBase.EnumJudgingType.实际水样比对.getValue().equals(config.getQcType())).collect(Collectors.toList());
                            } else {
                                compareJudgeConfigs2AnaData = controlLimitList.stream().filter(config -> c.getId().equals(config.getTestId())
                                        && EnumBase.EnumJudgingType.烟气比对.getValue().equals(config.getQcType())).collect(Collectors.toList());
                            }
                            if (StringUtil.isNotEmpty(compareJudgeConfigs2AnaData)) {
                                if (compareJudgeConfigs2AnaData.get(0).getIsCheckItem().equals(1)) {
                                    compareJudgeConfig = new DtoQualityControlLimit();
                                    compareJudgeConfig.setQcType(compareJudgeConfigs2AnaData.get(0).getQcType());
                                    compareJudgeConfig.setAllowLimit("");
                                    compareJudgeConfig.setJudgingMethod(null);
                                    compareJudgeConfig.setRangeConfig("");
                                } else {
                                    compareJudgeConfig = compareJudgeConfigs2AnaData.get(0);
                                }
                            }
                            if (compareJudgeConfig != null) {
                                DtoSampleJudgeData sampleJudgeData = new DtoSampleJudgeData();
                                sampleJudgeData.setTestId(test.getId());
                                sampleJudgeData.setSampleId(sam.getId());
                                sampleJudgeData.setExpectedValue("/");
                                sampleJudgeData.setCheckType(c.getCheckType());
                                sampleJudgeData.setAllowLimit(compareJudgeConfig.getAllowLimit());
                                sampleJudgeData.setCheckItemValue(compareJudgeConfig.getRangeConfig());
                                sampleJudgeData.setCompareType(compareJudgeConfig.getQcType());
                                sampleJudgeData.setJudgingMethod(compareJudgeConfig.getJudgingMethod());
                                sampleJudgeData.setDimensionId(test.getDimensionId());
                                sampleJudgeData.setStandardCode("/");
                                saveList.add(sampleJudgeData);
                            }
                        });
                    });
                });
            }
        });
    }

    private void deleteQualityControlData(Collection<String> receiveIds, List<DtoSampleJudgeData> judgeDataList) {
        if (receiveIds.size() > 0) {
            List<DtoSample> sampleList = sampleRepository.findByReceiveIdIn(receiveIds);
            List<String> samIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
            List<DtoSampleJudgeData> allJudgeDataList = repository.findBySampleIdIn(samIds);
            Set<String> allTestIds = allJudgeDataList.stream().map(DtoSampleJudgeData::getTestId).collect(Collectors.toSet());
            List<DtoSampleJudgeData> deleteDataList = new ArrayList<>();
            allTestIds.forEach(tId -> {
                Set<String> sIds = judgeDataList.stream().filter(p -> tId.equals(p.getTestId()))
                        .map(DtoSampleJudgeData::getSampleId).collect(Collectors.toSet());
                Set<String> otherSamIds = allJudgeDataList.stream().filter(p -> tId.equals(p.getTestId())
                        && !sIds.contains(p.getSampleId())).map(DtoSampleJudgeData::getSampleId).collect(Collectors.toSet());
                List<DtoSample> otherSampleList = sampleList.stream().filter(p -> otherSamIds.contains(p.getId())
                        && !EnumPRO.EnumSampleCategory.比对评价样.getValue().equals(p.getSampleCategory())).collect(Collectors.toList());
                if (otherSampleList.size() == 0) {
                    List<DtoSampleJudgeData> otherJudgeData = allJudgeDataList.stream()
                            .filter(p -> otherSamIds.contains(p.getSampleId())).collect(Collectors.toList());
                    deleteDataList.addAll(otherJudgeData);
                }
            });
            //删除质控样
            Set<String> delSamIds = deleteDataList.stream().map(DtoSampleJudgeData::getSampleId).collect(Collectors.toSet());
            List<DtoSample> delSampleList = sampleList.stream().filter(p -> delSamIds.contains(p.getId())).collect(Collectors.toList());
            delSampleList.forEach(p -> p.setIsDeleted(Boolean.TRUE));
            sampleRepository.save(delSampleList);
            repository.delete(deleteDataList);
        }
    }

    @Override
    @Transactional
    public void addQcSample(Integer qcGrade, Integer qcType, String standardCode, String expectedValue, Integer num, List<String> ids) {
        List<DtoSampleJudgeData> sampleJudgeDataList = repository.findAll(ids);
        List<String> sampleIds = sampleJudgeDataList.stream().map(DtoSampleJudgeData::getSampleId).distinct().collect(Collectors.toList());
        List<DtoSample> samples = sampleRepository.findAll(sampleIds);
        DtoProject project = projectService.findOne(samples.get(0).getProjectId());
        List<String> sampleTypeIds = samples.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypes = sampleTypeRepository.findAll(sampleTypeIds);
        List<String> testIds = sampleJudgeDataList.stream().map(DtoSampleJudgeData::getTestId).distinct().collect(Collectors.toList());
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds).stream().filter(a -> testIds.contains(a.getTestId())).collect(Collectors.toList());
        List<DtoTest> tests = testRepository.findAll(testIds);
        List<String> analyzeItemIds = tests.stream().map(DtoTest::getAnalyzeItemId).distinct().collect(Collectors.toList());
        List<DtoCompareJudge> compareJudges = compareJudgeRepository.findByAnalyzeItemIdIn(analyzeItemIds);
        List<String> compareIds = compareJudges.stream().map(DtoCompareJudge::getId).collect(Collectors.toList());
        List<DtoQualityControlLimit> limits = qualityControlLimitRepository.findByTestIdIn(compareIds);
        List<DtoSampleJudgeData> saveList = new ArrayList<>();
        //要新的序列
        List<DtoSerialNumberConfig> serialNumberConfigCreateList = new ArrayList<>();
        //要修改的序列
        List<DtoSerialNumberConfig> serialNumberConfigUpdateList = new ArrayList<>();
        List<DtoSample> qcSamples = new ArrayList<>();
        List<DtoQualityControl> qualityControls = new ArrayList<>();
        for (DtoSampleJudgeData judgeData : sampleJudgeDataList) {
            DtoSample sample = samples.stream().filter(s -> judgeData.getSampleId().equals(s.getId())).findFirst().orElse(null);
            DtoTest test = tests.stream().filter(t -> t.getId().equals(judgeData.getTestId())).findFirst().orElse(null);
            if (sample != null) {
                DtoSampleType sampleType = sampleTypes.stream().filter(s -> s.getId().equals(sample.getSampleTypeId())).findFirst().orElse(null);
                if (sampleType != null) {
                    DtoQualityControlLimit limit = null;
                    DtoCompareJudge compareJudge = compareJudges.stream().filter(c -> c.getAnalyzeItemId().equals(test.getAnalyzeItemId()) && c.getCheckType().equals(sampleType.getCheckType())).findFirst().orElse(null);
                    if (compareJudge != null) {
                        String compareId = compareJudge.getId();
                        List<DtoQualityControlLimit> limits2Compare = new ArrayList<>();
                        if (EnumLIM.EnumQCType.质控样.getValue().equals(qcType)) {
                            limits2Compare = limits.stream().filter(l -> l.getQcType().equals(EnumBase.EnumJudgingType.质控样比对.getValue()) && compareId.equals(l.getTestId())).collect(Collectors.toList());
                        } else if (EnumLIM.EnumQCType.替代样.getValue().equals(qcType)) {
                            limits2Compare = limits.stream().filter(l -> l.getQcType().equals(EnumBase.EnumJudgingType.替代样比对.getValue()) && compareId.equals(l.getTestId())).collect(Collectors.toList());
                        }
                        if (StringUtil.isNotEmpty(limits2Compare)) {
                            if (limits2Compare.get(0).getIsCheckItem().equals(1)) {
                                limit = new DtoQualityControlLimit();
                                limit.setQcType(limits2Compare.get(0).getQcType());
                                limit.setAllowLimit("");
                                limit.setJudgingMethod(null);
                                limit.setRangeConfig("");
                            } else {
                                limit = limits2Compare.get(0);
                            }
                        }
                    }
                    if (limit != null) {
                        DtoAnalyseData analyseData = analyseDataList.stream().filter(a -> a.getTestId().equals(test.getId()) && a.getSampleId().equals(sample.getId())).findFirst().get();
                        if (EnumLIM.EnumQCType.质控样.getValue().equals(qcType)) {
                            for (int i = 0; i < num; i++) {
                                createQcSample(EnumLIM.EnumQCType.质控样.getValue(), sample, project, serialNumberConfigCreateList, serialNumberConfigUpdateList, saveList, qcSamples, test, compareJudge, limit, standardCode, expectedValue, analyseData, qualityControls);
                            }
                        } else if (EnumLIM.EnumQCType.替代样.getValue().equals(qcType)) {
                            for (int i = 0; i < num; i++) {
                                createQcSample(EnumLIM.EnumQCType.替代样.getValue(), sample, project, serialNumberConfigCreateList, serialNumberConfigUpdateList, saveList, qcSamples, test, compareJudge, limit, standardCode, expectedValue, analyseData, qualityControls);
                            }
                        }
                    } else {
                        throw new BaseException("未配置相应质控样比对评判配置");
                    }
                }
            }
        }
        if (qualityControls.size() > 0) {
            comRepository.insertBatch(qualityControls);
        }
        if (StringUtil.isNotEmpty(saveList)) {
            repository.save(saveList);
        }
        if (serialNumberConfigCreateList.size() > 0) {
            comRepository.insert(serialNumberConfigCreateList);
        }
        if (serialNumberConfigUpdateList.size() > 0) {
            comRepository.updateBatch(serialNumberConfigUpdateList);
        }
        if (qcSamples.size() > 0) {
            comRepository.insertBatch(qcSamples);
        }
    }

    @Override
    @Transactional
    public void syncData(Map<String, Object> params) {
        List<DtoSample> sampleList;
        if (params.get("receiveId") == null) {
            String reportId = (String) params.get("reportId");
            List<String> sampleIds = reportDetailRepository.findByReportId(reportId).stream().filter(r -> EnumPRO.EnumReportDetailType.样品.getValue().equals(r.getObjectType())).map(DtoReportDetail::getObjectId).distinct().collect(Collectors.toList());
            sampleList = sampleRepository.findAll(sampleIds);
        } else {
            String receiveId = (String) params.get("receiveId");
            sampleList = sampleRepository.findByReceiveId(receiveId);
        }
        List<String> folderIds = sampleList.stream().filter(p -> !UUIDHelper.GUID_EMPTY.equals(p.getSampleFolderId())).map(DtoSample::getSampleFolderId).distinct().collect(Collectors.toList());
        List<DtoSample> samples = sampleRepository.findBySampleFolderIdIn(folderIds);
        List<String> sampleIds = samples.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<DtoSampleJudgeData> sampleJudgeDataList = repository.findBySampleIdIn(sampleIds);
        List<DtoSampleJudgeData> saveList = new ArrayList<>();
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds);
        for (DtoSampleJudgeData judgeData : sampleJudgeDataList) {
            DtoAnalyseData analyseData = analyseDataList.stream().filter(a -> a.getSampleId().equals(judgeData.getSampleId()) && a.getTestId().equals(judgeData.getTestId())).findFirst().orElse(null);
            if (analyseData != null) {
                DtoAnalyseDataInquiry dataInquiry = analyseDataService.findInquiry(analyseData.getSampleId()).stream().filter(d -> d.getId().equals(analyseData.getId())).findFirst().orElse(null);
                judgeData.setExpectedValue(analyseData.getTestValue());
                if (dataInquiry != null) {
                    if (dataInquiry.getAffirm().getStatus() != null) {
                        if (EnumPRO.EnumStatus.待处理.getValue().equals(dataInquiry.getAffirm().getStatus())) {
                            judgeData.setDataStatus("确认中");
                        } else if (EnumPRO.EnumStatus.已处理.getValue().equals(dataInquiry.getAffirm().getStatus())) {
                            judgeData.setDataStatus("已确认");
                        }
                    } else if (dataInquiry.getCheck().getStatus() != null && EnumPRO.EnumStatus.待处理.getValue().equals(dataInquiry.getCheck().getStatus())) {
                        judgeData.setDataStatus("复核中");
                    } else if (dataInquiry.getAudit().getStatus() != null && EnumPRO.EnumStatus.待处理.getValue().equals(dataInquiry.getAudit().getStatus())) {
                        judgeData.setDataStatus("审核中");
                    } else if (dataInquiry.getTest().getStatus() != null && EnumPRO.EnumStatus.待处理.getValue().equals(dataInquiry.getTest().getStatus())) {
                        judgeData.setDataStatus("检测中");
                    } else if (dataInquiry.getUndetect().getStatus() != null && EnumPRO.EnumStatus.待处理.getValue().equals(dataInquiry.getUndetect().getStatus())) {
                        judgeData.setDataStatus("未测");
                    } else if (dataInquiry.getWait().getStatus() != null && EnumPRO.EnumStatus.待处理.getValue().equals(dataInquiry.getWait().getStatus())) {
                        judgeData.setDataStatus("待检");
                    }
                }
                saveList.add(judgeData);
            }
        }
        if (StringUtil.isNotEmpty(saveList)) {
            repository.save(saveList);
        }
    }

    /**
     * 修改比对数据
     *
     * @param receiveId 送样单id
     */
    @Override
    public void updateJudgeData(String receiveId) {

    }

    @Override
    @Transactional
    public void syncConfig(Map<String, Object> params) {
        List<DtoSample> sampleList;
        if (params.get("receiveId") == null) {
            String reportId = (String) params.get("reportId");
            List<String> sampleIds = reportDetailRepository.findByReportId(reportId).stream().filter(r -> EnumPRO.EnumReportDetailType.样品.getValue().equals(r.getObjectType())).map(DtoReportDetail::getObjectId).distinct().collect(Collectors.toList());
            sampleList = sampleRepository.findAll(sampleIds);
        } else {
            String receiveId = (String) params.get("receiveId");
            sampleList = sampleRepository.findByReceiveId(receiveId);
        }
        CalculationService calculationService = SpringContextAware.getBean(CalculationService.class);
        List<String> sampleTypeIds = sampleList.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypes = sampleTypeRepository.findAll(sampleTypeIds);
        List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<DtoSampleJudgeData> sampleJudgeDataList = repository.findBySampleIdIn(sampleIds);
        List<DtoSampleJudgeData> saveList = new ArrayList<>();
        List<String> testIds = sampleJudgeDataList.stream().map(DtoSampleJudgeData::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> tests = testRepository.findAll(testIds);
        List<String> analyzeItemIds = tests.stream().map(DtoTest::getAnalyzeItemId).distinct().collect(Collectors.toList());
        List<DtoCompareJudge> compareJudges = compareJudgeRepository.findByAnalyzeItemIdIn(analyzeItemIds);
        List<String> compareIds = compareJudges.stream().map(DtoCompareJudge::getId).collect(Collectors.toList());
        List<DtoQualityControlLimit> limits = qualityControlLimitRepository.findByTestIdIn(compareIds);
        for (DtoSampleJudgeData judgeData : sampleJudgeDataList) {
            DtoSample sample = sampleList.stream().filter(s -> judgeData.getSampleId().equals(s.getId())).findFirst().orElse(null);
            DtoTest test = tests.stream().filter(t -> t.getId().equals(judgeData.getTestId())).findFirst().orElse(null);
            if (sample != null) {
                DtoSampleType sampleType = sampleTypes.stream().filter(s -> s.getId().equals(sample.getSampleTypeId())).findFirst().orElse(null);
                if (sampleType != null) {
                    DtoQualityControlLimit limit = null;
                    DtoCompareJudge compareJudge = compareJudges.stream().filter(c -> c.getAnalyzeItemId().equals(test.getAnalyzeItemId()) && sampleType.getCheckType().equals(c.getCheckType())).findFirst().orElse(null);
                    if (compareJudge != null) {
                        String compareId = compareJudge.getId();
                        List<DtoQualityControlLimit> limits2JudgeData = limits.stream().filter(l -> l.getQcType().equals(judgeData.getCompareType()) && compareId.equals(l.getTestId())).collect(Collectors.toList());
                        for (DtoQualityControlLimit limit2JudgeData : limits2JudgeData) {
                            if (limit2JudgeData.getIsCheckItem() != null && limit2JudgeData.getIsCheckItem().equals(1)) {
                                if (StringUtils.isNotNullAndEmpty(judgeData.getOnlineValue())) {
                                    if (DivationUtils.calculationResult(limit2JudgeData.getRangeConfig(), new BigDecimal(judgeData.getOnlineValue()), calculationService)) {
                                        limit = limit2JudgeData;
                                    }
                                } else {
                                    judgeData.setAllowLimit("");
                                    judgeData.setJudgingMethod(null);
                                    saveList.add(judgeData);
                                }
                            } else {
                                limit = limit2JudgeData;
                            }
                        }
                    }
                    if (limit != null) {
                        judgeData.setJudgingMethod(limit.getJudgingMethod());
                        judgeData.setAllowLimit(limit.getAllowLimit());
                        judgeData.setCheckItemValue(limit.getRangeConfig());
                        saveList.add(judgeData);
                    }
                }
            }
        }
        if (StringUtil.isNotEmpty(saveList)) {
            repository.save(saveList);
        }
    }

    @Override
    public void export(BaseCriteria criteria, HttpServletResponse response) {
        SampleJudgeDataCriteria sampleJudgeDataCriteria = (SampleJudgeDataCriteria) criteria;
        sampleJudgeDataCriteria.setIsFilterNotEvaluate(Boolean.TRUE);
        List<DtoSampleJudgeData> dataList = getData(criteria);
        dataList.sort(Comparator.comparing(DtoSampleJudgeData::getFolderName).thenComparing(DtoSampleJudgeData::getAnalyzeItemName).thenComparing(DtoSampleJudgeData::getCompareType).thenComparing(DtoSampleJudgeData::getSampleCode));
        Map<String, Map<String, Map<Integer, List<DtoSampleJudgeData>>>> map = dataList.stream().collect(Collectors.groupingBy(DtoSampleJudgeData::getFolderName, Collectors.groupingBy(DtoSampleJudgeData::getAnalyzeItemName,
                Collectors.groupingBy(DtoSampleJudgeData::getCompareType))));
        List<SampleJudgeDataVoForWater> dataVos = new ArrayList<>();
        for (Map.Entry<String, Map<String, Map<Integer, List<DtoSampleJudgeData>>>> entry : map.entrySet()) {
            String folderName = entry.getKey();
            SampleJudgeDataVoForWater vo = new SampleJudgeDataVoForWater();
            List<AnalyzeitemJudgeDataVoForWater> anaVos = new ArrayList<>();
            for (Map.Entry<String, Map<Integer, List<DtoSampleJudgeData>>> entry1 : entry.getValue().entrySet()) {
                String analyzeItemName = entry1.getKey();
                AnalyzeitemJudgeDataVoForWater anaVo = new AnalyzeitemJudgeDataVoForWater();
                anaVo.setAnalyzeItemName(analyzeItemName);
                List<ComparetypeJudgeDataVoForWater> comVos = new ArrayList<>();
                for (Map.Entry<Integer, List<DtoSampleJudgeData>> entry2 : entry1.getValue().entrySet()) {
                    ComparetypeJudgeDataVoForWater comVo = new ComparetypeJudgeDataVoForWater();
                    comVo.setCompareType(EnumBase.EnumJudgingType.getName(entry2.getKey()));
                    comVos.add(comVo);
                    List<JudgeDataVoForWater> juVos = new ArrayList<>();
                    for (DtoSampleJudgeData judgeData : entry2.getValue()) {
                        JudgeDataVoForWater juVo = new JudgeDataVoForWater();
                        juVo.setSampleCode(judgeData.getSampleCode());
                        juVo.setAllowLimit(judgeData.getAllowLimit());
                        juVo.setDimensionName(judgeData.getDimensionName());
                        juVo.setPass(judgeData.getPass());
                        juVo.setOnlineValue(judgeData.getOnlineValue());
                        juVo.setExpectedValue(judgeData.getExpectedValue());
                        juVo.setCheckItemValue(judgeData.getQcRateValue());
                        juVo.setJudgingMethod(EnumBase.EnumJudgingMethod.getName(judgeData.getJudgingMethod()));
                        juVo.setFolderPass(judgeData.getFolderPass());
                        juVo.setRemark(judgeData.getRemark());
                        juVos.add(juVo);
                    }
                    comVo.setDataVos(juVos);
                }
                anaVo.setDataVos(comVos);
                anaVos.add(anaVo);
            }
            vo.setDataVos(anaVos);
            vo.setFolderName(folderName);
            dataVos.add(vo);
        }
        PoiExcelUtils.exportExcel(dataVos, null, "比对评价数据表格", SampleJudgeDataVoForWater.class, "比对评价数据表格" + "_" + DateUtil.nowTime("yyyyMMddHHmmss"), response);
    }

    @Override
    public Map<String, Object> havingData(String receiveId) {
        List<String> sampleIds = sampleRepository.findByReceiveId(receiveId).stream().map(DtoSample::getId).collect(Collectors.toList());
        return havingData(sampleIds);
    }

    @Override
    public Map<String, Object> havingDataForReport(String reportId) {
        List<String> sampleIds = reportDetailRepository.findByReportId(reportId).stream().filter(r -> EnumPRO.EnumReportDetailType.样品.getValue().equals(r.getObjectType())).map(DtoReportDetail::getObjectId).distinct().collect(Collectors.toList());
        return havingData(sampleIds);
    }

    @Override
    public Map<String, Object> havingDataForProject(String projectId) {
        List<String> sampleIds = sampleRepository.findByProjectId(projectId).stream().map(DtoSample::getId).collect(Collectors.toList());
        return havingData(sampleIds);
    }

    @Override
    @Transactional
    public void updateEvaluateState(List<String> ids, Boolean isNotEvaluate) {
        List<DtoSampleJudgeData> sampleJudgeDataList = repository.findAll(ids);
        // 更新是否不参与评价状态
        sampleJudgeDataList.forEach(p -> p.setIsNotEvaluate(isNotEvaluate));
        this.update(sampleJudgeDataList);
    }


    /**
     * 判断当前样品中有没有比对数据
     *
     * @param sampleIds 样品id
     * @return Map<String, Boolean>
     */
    private Map<String, Object> havingData(List<String> sampleIds) {
        List<DtoSampleJudgeData> dataList = repository.findBySampleIdIn(sampleIds);
        List<Integer> checkTypes = dataList.stream().map(DtoSampleJudgeData::getCheckType).distinct().collect(Collectors.toList());
        Map<String, Object> map = new HashMap<>();
        if (dataList.size() > 0) {
            map.put("havingData", true);
        } else {
            map.put("havingData", false);
        }
        map.put("checkType", checkTypes);
        return map;
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> sampleIds = repository.findAll((List<String>) ids).stream().map(DtoSampleJudgeData::getSampleId).collect(Collectors.toList());
        List<String> qcIds = sampleRepository.findAll(sampleIds).stream().map(DtoSample::getQcId).collect(Collectors.toList());
        qualityControlRepository.logicDeleteById(qcIds, new Date());
//        List<String> analyseDataIds = analyseDataRepository.findBySampleIdIn(sampleIds).stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
//        analyseDataRepository.logicDeleteById(analyseDataIds, new Date());
        sampleService.logicDeleteById(sampleIds);
        return super.logicDeleteById(ids);
    }

    /**
     * 创建质控样
     *
     * @param qcType        质控类型
     * @param sample        样品
     * @param project       项目
     * @param saveList      比对数据保存列表
     * @param qcSamples     质控样保存列表
     * @param test          测试项目
     * @param compareJudge  比对评判方式
     * @param limit         比对评判配置
     * @param expectedValue 理论值
     */
    private void createQcSample(Integer qcType, DtoSample sample, DtoProject project, List<DtoSerialNumberConfig> serialNumberConfigCreateList, List<DtoSerialNumberConfig> serialNumberConfigUpdateList,
                                List<DtoSampleJudgeData> saveList, List<DtoSample> qcSamples, DtoTest test, DtoCompareJudge compareJudge, DtoQualityControlLimit limit, String standardCode, String expectedValue, DtoAnalyseData analyseData, List<DtoQualityControl> qualityControls
    ) {
        Map<String, Object> returnMap = sampleService.getAssociateSample(1, EnumLIM.EnumQCGrade.外部质控.getValue(),
                qcType, sample, "", "", "", "", null,
                PrincipalContextUser.getPrincipal().getUserId(), null, project, null, "", null);
        DtoSample qcSample = (DtoSample) returnMap.get("sample");
        qcSample.setSampleFolderId(sample.getSampleFolderId());
        qcSample.setSampleCategory(EnumPRO.EnumSampleCategory.比对评价样.getValue());
        qcSample.setRedAnalyzeItems(analyseData.getRedAnalyzeItemName());
        qcSample.setStatus(EnumPRO.EnumSampleStatus.样品在检.name());
        if (EnumLIM.EnumQCType.质控样.getValue().equals(qcType)) {
            qcSample.setRedFolderName("质控样");
            qcSample.setAssociateSampleId(UUIDHelper.GUID_EMPTY);
        } else if (EnumLIM.EnumQCType.替代样.getValue().equals(qcType)) {
            qcSample.setAssociateSampleId(sample.getId());
            qcSample.setRedFolderName("替代样");
        }
        DtoQualityControl qc = (DtoQualityControl) returnMap.get("qualityControl");
        qualityControls.add(qc);
        //DtoGenerateSN generateSN = (DtoGenerateSN) returnMap.get("generateSN");
        DtoGenerateSN generateSN = sampleService.createSampleCode(project,
                null,
                sample.getSampleTypeId(),
                sample.getSampleFolderId(),
                new Date(),
                PrincipalContextUser.getPrincipal().getUserId(),
                sample.getId(),
                true,
                PrincipalContextUser.getPrincipal().getUserId(), true,
                UUIDHelper.GUID_EMPTY,
                UUIDHelper.GUID_EMPTY,
                EnumPRO.EnumSampleCategory.质控样.getValue(), qcType, 1, true, "1");
        qcSample.setCode(generateSN.getCode());
        if (StringUtil.isNotNull(generateSN)) {
            DtoSerialNumberConfig serialNumberConfigCreate = generateSN.getSerialNumberConfigCreate();
            if (StringUtil.isNotNull(serialNumberConfigCreate)) {
                serialNumberConfigCreateList.add(serialNumberConfigCreate);
            }
            DtoSerialNumberConfig serialNumberConfigUpdate = generateSN.getSerialNumberConfigUpdate();
            if (StringUtil.isNotNull(serialNumberConfigUpdate)) {
                serialNumberConfigUpdateList.add(serialNumberConfigUpdate);
            }
        }
        DtoSampleJudgeData sampleJudgeData = new DtoSampleJudgeData();
        sampleJudgeData.setSampleId(qcSample.getId());
        sampleJudgeData.setTestId(test.getId());
        sampleJudgeData.setCheckType(compareJudge.getCheckType());
        sampleJudgeData.setAllowLimit(limit.getAllowLimit());
        sampleJudgeData.setCheckItemValue(limit.getRangeConfig());
        sampleJudgeData.setCompareType(limit.getQcType());
        sampleJudgeData.setJudgingMethod(limit.getJudgingMethod());
        sampleJudgeData.setExpectedValue(expectedValue);
        sampleJudgeData.setDimensionId(analyseData.getDimensionId());
        sampleJudgeData.setStandardCode(StringUtils.isNotNullAndEmpty(standardCode) ? standardCode : "");
        saveList.add(sampleJudgeData);
        qcSamples.add(qcSample);
    }

    /**
     * 填充冗余数据
     *
     * @param dataList 数据
     */
    @Override
    public void fillingTransientFields(List<DtoSampleJudgeData> dataList) {
        List<String> dimensionIds = dataList.stream().map(DtoSampleJudgeData::getDimensionId).distinct().collect(Collectors.toList());
        List<DtoDimension> dimensions = StringUtil.isNotEmpty(dimensionIds) ? dimensionRepository.findAll(dimensionIds) : new ArrayList<>();
        List<String> sampleIds = dataList.stream().map(DtoSampleJudgeData::getSampleId).distinct().collect(Collectors.toList());
        List<DtoReportDetail> reportDetails = StringUtil.isNotEmpty(sampleIds) ? reportDetailRepository.findByObjectIdInAndObjectType(sampleIds, EnumPRO.EnumReportDetailType.样品.getValue()) : new ArrayList<>();
        List<DtoSample> samples = StringUtil.isNotEmpty(sampleIds) ? sampleRepository.findAll(sampleIds) : new ArrayList<>();
        List<String> yySampleIds = samples.stream().map(DtoSample::getAssociateSampleId).filter(associateSampleId -> !UUIDHelper.GUID_EMPTY.equals(associateSampleId)).distinct().collect(Collectors.toList());
        List<DtoSample> yySamples = StringUtil.isNotEmpty(yySampleIds) ? sampleRepository.findAll(yySampleIds) : new ArrayList<>();
        List<String> testIds = dataList.stream().map(DtoSampleJudgeData::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> tests = StringUtil.isNotEmpty(testIds) ? testRepository.findAll(testIds) : new ArrayList<>();
        dataList.forEach(d -> {
            Optional<DtoTest> test = tests.stream().filter(t -> t.getId().equals(d.getTestId())).findFirst();
            test.ifPresent(t -> d.setAnalyzeItemName(t.getRedAnalyzeItemName()));
            Optional<DtoSample> sample = samples.stream().filter(s -> s.getId().equals(d.getSampleId())).findFirst();
            sample.ifPresent(s -> {
                Optional<DtoSample> yySample = yySamples.stream().filter(yy -> yy.getId().equals(s.getAssociateSampleId())).findFirst();
                yySample.ifPresent(yy -> d.setYySampleCode(yy.getCode()));
                d.setSampleName(s.getRedFolderName());
                d.setSampleCode(s.getCode());
                d.setSampleCategory(s.getSampleCategory());
            });
            Optional<DtoDimension> dimension = dimensions.stream().filter(di -> di.getId().equals(d.getDimensionId())).findFirst();
            dimension.ifPresent(di -> d.setDimensionName(di.getDimensionName()));
            d.setOrderNum(EnumBase.EnumJudgingType.getOrderNum(d.getCompareType()));
            d.setIsConnectReport(false);
            reportDetails.stream().filter(r -> r.getObjectId().equals(d.getSampleId())).findFirst().ifPresent(r -> d.setIsConnectReport(true));
        });
    }

    @Override
    public List<DtoSampleJudgeData> getData(BaseCriteria criteria) {
        SampleJudgeDataCriteria sampleJudgeDataCriteria = (SampleJudgeDataCriteria) criteria;
        List<DtoSample> sampleList;
        if (StringUtil.isNotEmpty(sampleJudgeDataCriteria.getReceiveId())) {
            String receiveId = sampleJudgeDataCriteria.getReceiveId();
            sampleList = sampleRepository.findByReceiveId(receiveId);
        } else if (StringUtil.isNotEmpty(sampleJudgeDataCriteria.getReportId())) {
            String reportId = sampleJudgeDataCriteria.getReportId();
            List<String> sampleIds = reportDetailRepository.findByReportId(reportId).stream().filter(r -> EnumPRO.EnumReportDetailType.样品.getValue().equals(r.getObjectType())).map(DtoReportDetail::getObjectId).distinct().collect(Collectors.toList());
            sampleList = sampleRepository.findAll(sampleIds);
        } else if (StringUtil.isNotEmpty(sampleJudgeDataCriteria.getProjectId())) {
            String projectId = sampleJudgeDataCriteria.getProjectId();
            List<String> receiveIds = receiveSampleRecordRepository.findByProjectId(projectId).stream().map(DtoReceiveSampleRecord::getId).collect(Collectors.toList());
            sampleList = StringUtil.isNotEmpty(receiveIds) ? sampleRepository.findByReceiveIdIn(receiveIds) : new ArrayList<>();
        } else {
            sampleList = new ArrayList<>();
        }
        sampleList = sampleList.stream().filter(p -> !p.getSampleCategory().equals(EnumPRO.EnumSampleCategory.质控样.getValue())).collect(Collectors.toList());
        List<String> folderIds = sampleList.stream().map(DtoSample::getSampleFolderId).filter(sampleFolderId -> !UUIDHelper.GUID_EMPTY.equals(sampleFolderId)).distinct().collect(Collectors.toList());
        List<DtoSampleFolderEvaluate> sampleFolderEvaluates = sampleFolderEvaluateRepository.findBySampleFolderIdIn(folderIds);
        List<DtoSampleFolder> sampleFolders = sampleFolderRepository.findAll(folderIds);
        Map<String, DtoSampleFolder> folderMap = sampleFolders.stream().collect(Collectors.toMap(DtoSampleFolder::getId, dto -> dto));
        Map<String, DtoSample> sampleMap = sampleList.stream().collect(Collectors.toMap(DtoSample::getId, dto -> dto));
        List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        sampleJudgeDataCriteria.setSampleIds(sampleIds);
        PageBean<DtoSampleJudgeData> pb = new PageBean<>();
        pb.setPageNo(1);
        pb.setRowsPerPage(Integer.MAX_VALUE);
        findByPage(pb, sampleJudgeDataCriteria);
        List<DtoSampleJudgeData> sampleJudgeDataList = pb.getData();
        sampleJudgeDataList.forEach(data -> {
            data.setResultEvaluate(data.getPass());
            data.setFolderName("");
            DtoSample sample = sampleMap.get(data.getSampleId());
            if (sample != null) {
                DtoSampleFolder sampleFolder = folderMap.get(sample.getSampleFolderId());
                data.setFolderName(sample.getRedFolderName());
                if (sampleFolder != null) {
                    data.setFolderId(sampleFolder.getId());
                    data.setFolderName(sampleFolder.getWatchSpot());
                    Optional<DtoSampleFolderEvaluate> sampleFolderEvaluate = sampleFolderEvaluates.stream().filter(s -> s.getSampleFolderId().equals(sampleFolder.getId()) && data.getTestId().equals(s.getTestId())).findFirst();
                    if (sampleFolderEvaluate.isPresent()) {
                        data.setFolderPass(sampleFolderEvaluate.get().getFolderPass());
                        data.setRemark(sampleFolderEvaluate.get().getRemark());
                        //data.setResultEvaluate(sampleFolderEvaluate.get().getResultEvaluate());
                        data.setResultEvaluate(data.getPass());
                        if (EnumBase.EnumCheckType.废气比对.getValue().equals(data.getCheckType())) {
                            data.setResultEvaluate(sampleFolderEvaluate.get().getResultEvaluate());
                            data.setQcRateValue(sampleFolderEvaluate.get().getQcRateValue());
                        }
                    }
                }
            }
        });
        fillingTransientFields(sampleJudgeDataList);
        if (StringUtil.isNotEmpty(sampleJudgeDataCriteria.getFolderPass())) {
            sampleJudgeDataList = sampleJudgeDataList.stream().filter(s -> sampleJudgeDataCriteria.getFolderPass().equals(s.getFolderPass())).collect(Collectors.toList());
        }
        return sampleJudgeDataList;
    }

    private void createJudgeData(DtoAnalyseData analyseData, DtoCompareJudge compareJudge, DtoQualityControlLimit limit,
                                 String expectedValue, List<DtoSampleJudgeData> saveList) {
        DtoSampleJudgeData sampleJudgeData = new DtoSampleJudgeData();
        sampleJudgeData.setSampleId(analyseData.getSampleId());
        sampleJudgeData.setTestId(analyseData.getTestId());
        sampleJudgeData.setCheckType(compareJudge.getCheckType());
        sampleJudgeData.setAllowLimit(limit.getAllowLimit());
        sampleJudgeData.setCheckItemValue(limit.getRangeConfig());
        sampleJudgeData.setCompareType(limit.getQcType());
        sampleJudgeData.setJudgingMethod(limit.getJudgingMethod());
        sampleJudgeData.setExpectedValue(expectedValue);
        sampleJudgeData.setDimensionId(analyseData.getDimensionId());
        saveList.add(sampleJudgeData);
    }

    @Autowired
    public void setSampleRepository(SampleRepository sampleRepository) {
        this.sampleRepository = sampleRepository;
    }

    @Autowired
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    public void setSampleFolderRepository(SampleFolderRepository sampleFolderRepository) {
        this.sampleFolderRepository = sampleFolderRepository;
    }

    @Autowired
    public void setDimensionRepository(DimensionRepository dimensionRepository) {
        this.dimensionRepository = dimensionRepository;
    }

    @Autowired
    @Lazy
    public void setSampleService(SampleService sampleService) {
        this.sampleService = sampleService;
    }

    @Autowired
    public void setCompareJudgeRepository(CompareJudgeRepository compareJudgeRepository) {
        this.compareJudgeRepository = compareJudgeRepository;
    }

    @Autowired
    public void setQualityControlLimitRepository(QualityControlLimitRepository qualityControlLimitRepository) {
        this.qualityControlLimitRepository = qualityControlLimitRepository;
    }

    @Autowired
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    @Lazy
    public void setProjectService(ProjectService projectService) {
        this.projectService = projectService;
    }

    @Autowired
    public void setAnalyseDataRepository(AnalyseDataRepository analyseDataRepository) {
        this.analyseDataRepository = analyseDataRepository;
    }

    @Autowired
    public void setSampleFolderEvaluateRepository(SampleFolderEvaluateRepository sampleFolderEvaluateRepository) {
        this.sampleFolderEvaluateRepository = sampleFolderEvaluateRepository;
    }

    @Autowired
    public void setReportDetailRepository(ReportDetailRepository reportDetailRepository) {
        this.reportDetailRepository = reportDetailRepository;
    }

    @Autowired
    public void setQualityControlRepository(QualityControlRepository qualityControlRepository) {
        this.qualityControlRepository = qualityControlRepository;
    }

    @Autowired
    @Lazy
    public void setAnalyseDataService(AnalyseDataService analyseDataService) {
        this.analyseDataService = analyseDataService;
    }

    @Autowired
    public void setReceiveSampleRecordRepository(ReceiveSampleRecordRepository receiveSampleRecordRepository) {
        this.receiveSampleRecordRepository = receiveSampleRecordRepository;
    }

    @Autowired
    public void setSampleGroupService(SampleGroupService sampleGroupService) {
        this.sampleGroupService = sampleGroupService;
    }

    @Autowired
    public void setSampleGroupRepository(SampleGroupRepository sampleGroupRepository) {
        this.sampleGroupRepository = sampleGroupRepository;
    }

    @Autowired
    public void setSampleTypeGroupRepository(SampleTypeGroupRepository sampleTypeGroupRepository) {
        this.sampleTypeGroupRepository = sampleTypeGroupRepository;
    }
}
