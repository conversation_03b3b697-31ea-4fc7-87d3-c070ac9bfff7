package com.sinoyd.lims.foreign;

import lombok.Data;

import java.util.Date;

/**
* 工作单明细
* <AUTHOR>
* @version V1.0.0 2023/11/7
* @since V100R001
*/
@Data
public class DataWorksheetFolderVO {

    /**
     * id
     */
    private String id;

    /**
     * 工作单id
     */
    private String workSheetFolderId;

    /**
     * 工作单号
     */
    private String workSheetCode;

    /**
     * 分析人名称
     */
    private String analystName;

    /**
     * 分析方法id
     */
    private String analyzeMethodId;

    /**
     * 退回意见
     */
    private String backOpinion;

    /**
     * 分析完成日期
     */
    private Date finishTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;
}
