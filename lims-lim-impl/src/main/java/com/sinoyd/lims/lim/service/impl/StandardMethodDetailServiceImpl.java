package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoStandardMethodDetail;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.repository.lims.StandardMethodDetailRepository;
import com.sinoyd.lims.lim.repository.rcc.AnalyzeMethodRepository;
import com.sinoyd.lims.lim.service.StandardMethodDetailService;
import com.sinoyd.lims.lim.service.TestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * StandardMethodDetail服务实现类
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/13
 * @since V100R001
 */
@Service
public class StandardMethodDetailServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoStandardMethodDetail, String, StandardMethodDetailRepository> implements StandardMethodDetailService {


    private TestService testService;

    private AnalyzeMethodRepository analyzeMethodRepository;


    @Override
    public void findByPage(PageBean<DtoStandardMethodDetail> page, BaseCriteria criteria) {
        page.setEntityName("DtoStandardMethodDetail a");
        page.setSelect("select a");
        super.findByPage(page, criteria);
        List<DtoStandardMethodDetail> standardMethodDetails = page.getData();
        if (StringUtil.isNotEmpty(standardMethodDetails)) {
            // 查询测试项目和采样方法
            List<String> objectIds = standardMethodDetails.stream().map(DtoStandardMethodDetail::getObjectId).distinct().collect(Collectors.toList());
            List<DtoTest> testList = StringUtil.isNotEmpty(objectIds) ? testService.findRedisByIds(objectIds) : new ArrayList<>();
            List<DtoAnalyzeMethod> analyzeMethods = StringUtil.isNotEmpty(objectIds) ? analyzeMethodRepository.findAll(objectIds) : new ArrayList<>();
            Map<String, DtoTest> testMap = testList.stream().collect(Collectors.toMap(DtoTest::getId, p -> p));
            Map<String, DtoAnalyzeMethod> analyzeMethodMap = analyzeMethods.stream().collect(Collectors.toMap(DtoAnalyzeMethod::getId, p -> p));
            for (DtoStandardMethodDetail methodDetail : standardMethodDetails) {
                if (testMap.containsKey(methodDetail.getObjectId())) {
                    DtoTest test = testMap.get(methodDetail.getObjectId());
                    methodDetail.setRedAnalyzeItemName(test.getRedAnalyzeItemName());
                    methodDetail.setRedAnalyzeMethodName(test.getRedAnalyzeMethodName());
                    methodDetail.setRedCountryStandard(test.getRedCountryStandard());
                } else if (analyzeMethodMap.containsKey(methodDetail.getObjectId())) {
                    DtoAnalyzeMethod analyzeMethod = analyzeMethodMap.get(methodDetail.getObjectId());
                    methodDetail.setRedAnalyzeMethodName(analyzeMethod.getMethodName());
                    methodDetail.setRedCountryStandard(analyzeMethod.getCountryStandard());
                }
            }
        }
    }

    @Override
    public DtoStandardMethodDetail save(DtoStandardMethodDetail entity) {
        List<String> objectIds = entity.getObjectIds();
        String methodId = entity.getMethodId();
        List<DtoStandardMethodDetail> standardMethodDetails = repository.findByMethodId(methodId);
        objectIds.removeIf(p -> standardMethodDetails.stream().map(DtoStandardMethodDetail::getObjectId).collect(Collectors.toList()).contains(p));
        List<DtoStandardMethodDetail> addMethodDetails = new ArrayList<>();
        for (String objectId : objectIds) {
            DtoStandardMethodDetail standardMethodDetail = new DtoStandardMethodDetail();
            standardMethodDetail.setMethodId(entity.getMethodId());
            standardMethodDetail.setObjectId(objectId);
            addMethodDetails.add(standardMethodDetail);
        }
        if (StringUtil.isNotEmpty(addMethodDetails)) {
            super.save(addMethodDetails);
        }
        return entity;
    }


    @Autowired
    @Lazy
    public void setAnalyzeMethodRepository(AnalyzeMethodRepository analyzeMethodRepository) {
        this.analyzeMethodRepository = analyzeMethodRepository;
    }

    @Autowired
    @Lazy
    public void setTestService(TestService testService) {
        this.testService = testService;
    }
}