package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoExplore;

import java.util.List;


/**
 * Explore操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/6/2
 * @since V100R001
 */
public interface ExploreService extends IBaseJpaService<DtoExplore, String> {

    /**
     * 加载项目的踏勘信息
     *
     * @param projectId 项目id
     * @return 踏勘信息列表
     */
    List<DtoExplore> loadProjectExplore(String projectId);

    /**
     * 附件路径
     *
     * @param id 项目id
     * @return 返回相应的路径信息
     */
    DtoExplore findAttachPath(String id);
}