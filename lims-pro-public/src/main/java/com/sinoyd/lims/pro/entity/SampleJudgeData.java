package com.sinoyd.lims.pro.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;

/**
 * SampleJudgeData实体
 * <AUTHOR>
 * @version V1.0.0 2023/6/13
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description="SampleJudgeData")
@Data
@EntityListeners(AuditingEntityListener.class)
public class SampleJudgeData implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public SampleJudgeData() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 样品id
     */
    @Column(length = 50)
    @ApiModelProperty("样品id")
    private String sampleId;

    /**
     * 测试项目id
     */
    @Column(length = 50)
    @ApiModelProperty("测试项目id")
    private String testId;

    /**
     * 评判方式
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("评判方式")
    private Integer judgingMethod;

    /**
     * 评判类型
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("评判类型")
    private Integer compareType;

    /**
     * 检测类型（0-废水比对，1-废气比对）
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("检测类型（0-废水比对，1-废气比对）")
    private Integer checkType;

    /**
     * 在线值
     */
    @Column(length = 50)
    @ApiModelProperty("在线值")
    @Length(message = "在线值{validation.message.length}", max = 50)
    private String onlineValue;

    /**
     * 理论值
     */
    @Column(length = 50)
    @ApiModelProperty("理论值")
    @Length(message = "理论值{validation.message.length}", max = 50)
    private String expectedValue;

    /**
     * 判定结果
     */
    @Column(length = 255)
    @ApiModelProperty("判定结果")
    @Length(message = "判定结果{validation.message.length}", max = 255)
    private String qcRateValue;

    /**
     * 是否合格
     */
    @Column
    @ApiModelProperty("是否合格")
    @Length(message = "是否合格{validation.message.length}", max = 50)
    private String pass;

    /**
     * 量纲id
     */
    @Column(length = 50)
    @ApiModelProperty("量纲id")
    private String dimensionId;

    /**
     * 检查项值
     */
    @Column(length = 50)
    @ApiModelProperty("检查项值")
    @Length(message = "检查项值{validation.message.length}", max = 50)
    private String checkItemValue;

    /**
     * 允许限值
     */
    @Column(length = 50)
    @ApiModelProperty("允许限值")
    @Length(message = "允许限值{validation.message.length}", max = 50)
    private String allowLimit;

    /**
     * 数据状态
     */
    @Column(length = 50)
    @ApiModelProperty("数据状态")
    @Length(message = "数据状态{validation.message.length}", max = 50)
    private String dataStatus;

    /**
     * 标样编号
     */
    @Column(length = 50)
    @ApiModelProperty("标样编号")
    @Length(message = "标样编号{validation.message.length}", max = 50)
    private String standardCode;

    /**
     * 监测时间/段
     */
    @Column(length = 50)
    @ApiModelProperty("监测时间/段")
    @Length(message = "监测时间/段{validation.message.length}", max = 50)
    private String testTimeStr;

    /**
     * 是否不参与评价
     */
    @Column(nullable = false)
    @ApiModelProperty("是否不参与评价")
    private Boolean isNotEvaluate;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}
