package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.entity.CurrentPrincipalUser;
import org.springframework.scheduling.annotation.Async;

/**
 * 方案缓存操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/11
 * @since V100R001
 */
public interface SchemeCacheService {

    /**
     * 核查方案信息
     *
     * @param projectId            项目id
     * @param principalContextUser 当前人员信息
     */
    void checkSchemeChange(String projectId, CurrentPrincipalUser principalContextUser);


    /**
     * 核查方案
     *
     * @param projectId            项目id
     * @param principalContextUser 当前人员信息
     */
    void checkScheme(String projectId, CurrentPrincipalUser principalContextUser);
}
