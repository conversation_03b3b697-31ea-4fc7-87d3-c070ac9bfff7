package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * CarGpsData实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="CarGpsData")
 @Data
 public  class CarGpsData implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  CarGpsData() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 车辆id（Guid）
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("车辆id（Guid）")
	private String carId;
    
    /**
    * 车辆编码(用于GPS数据传输)
    */
    @Column(length=50)
    @ApiModelProperty("车辆编码(用于GPS数据传输)")
    @Length(message = "车辆编码{validation.message.length}", max = 50)
    private String carSn;
    
    /**
    * 行驶轨迹
    */
    @ApiModelProperty("行驶轨迹")
	private String drivingLine;
    
    /**
    * 驾驶时长
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("驾驶时长")
    private BigDecimal drivingTime;
    
    /**
    * 英里数
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("英里数")
    private BigDecimal mileage;
    
    /**
    * 平均速度
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("平均速度")
    private BigDecimal avgSpeed;
    
    /**
    * 最高时速
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("最高时速")
    private BigDecimal maxSpeed;
    
    /**
    * 开始时间
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("开始时间")
    private Date beginTime;
    
    /**
    * 结束时间
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("结束时间")
    private Date endTime;
    
    /**
    * 耗油量
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("耗油量")
    private BigDecimal fuelCon;
    
    /**
    * 使用人员id（Guid）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("使用人员id（Guid）")
	private String userId;
    
    /**
    * 使用人员
    */
    @Column(length=50)
    @ApiModelProperty("使用人员")
	private String userName;
    
    /**
    * 是否最高级
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否最高级")
    private Integer isSkeptical;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
 }