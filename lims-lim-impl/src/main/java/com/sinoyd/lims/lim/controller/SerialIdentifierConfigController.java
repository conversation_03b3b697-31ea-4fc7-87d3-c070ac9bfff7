package com.sinoyd.lims.lim.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.service.SerialIdentifierConfigService;
import com.sinoyd.lims.lim.criteria.SerialIdentifierConfigCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoSerialIdentifierConfig;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * SerialIdentifierConfig服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
 @Api(tags = "可配置编号: 可配置编号服务")
 @RestController
 @RequestMapping("api/lim/serialIdentifierConfig")
 @Validated
 public class SerialIdentifierConfigController extends BaseJpaController<DtoSerialIdentifierConfig, String,SerialIdentifierConfigService> {


    /**
     * 分页动态条件查询可配置编号
     *
     * @param serialIdentifierConfigCriteria 条件参数
     * @return 可配置编号的相关信息
     */
    @ApiOperation(value = "分页动态条件查询可配置编号", notes = "分页动态条件查询可配置编号")
    @GetMapping
    public RestResponse<List<DtoSerialIdentifierConfig>> findByPage(SerialIdentifierConfigCriteria serialIdentifierConfigCriteria) {
        PageBean<DtoSerialIdentifierConfig> pageBean = super.getPageBean();
        RestResponse<List<DtoSerialIdentifierConfig>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, serialIdentifierConfigCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询可配置编号
     *
     * @param id 主键id
     * @return RestResponse<DtoSerialIdentifierConfig>
     */
    @ApiOperation(value = "按主键查询可配置编号", notes = "按主键查询可配置编号")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoSerialIdentifierConfig> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoSerialIdentifierConfig> restResponse = new RestResponse<>();
        DtoSerialIdentifierConfig serialIdentifierConfig = service.findOne(id);
        restResponse.setData(serialIdentifierConfig);
        restResponse.setRestStatus(StringUtil.isNull(serialIdentifierConfig) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增SerialIdentifierConfig
     *
     * @param serialIdentifierConfig 实体列表
     * @return RestResponse<DtoSerialIdentifierConfig>
     */
    @ApiOperation(value = "新增可配置编号", notes = "新增可配置编号")
    @PostMapping
    public RestResponse<DtoSerialIdentifierConfig> create(@Validated @RequestBody DtoSerialIdentifierConfig serialIdentifierConfig) {
        RestResponse<DtoSerialIdentifierConfig> restResponse = new RestResponse<>();
        restResponse.setData(service.save(serialIdentifierConfig));
        return restResponse;
    }

    /**
     * 新增可配置编号
     *
     * @param serialIdentifierConfig 实体列表
     * @return RestResponse<DtoSerialIdentifierConfig>
     */
    @ApiOperation(value = "修改可配置编号", notes = "修改可配置编号")
    @PutMapping
    public RestResponse<DtoSerialIdentifierConfig> update(@Validated @RequestBody DtoSerialIdentifierConfig serialIdentifierConfig) {
        RestResponse<DtoSerialIdentifierConfig> restResponse = new RestResponse<>();
        restResponse.setData(service.update(serialIdentifierConfig));
        return restResponse;
    }

    /**
     * "根据id批量删除可配置编号
     *
     * @param id id信息
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除SerialIdentifierConfig", notes = "根据id批量删除SerialIdentifierConfig")
    @DeleteMapping(path = "/{id}")
    public RestResponse<String> delete(@PathVariable(name = "id") String id) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);
        return restResp;
    }


    @ApiOperation(value = "验证编号规则", notes = "验证编号规则")
    @PostMapping("/valid")
    public RestResponse<Boolean> validRule(@RequestBody DtoSerialIdentifierConfig serialIdentifierConfig) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setData(service.validRule(serialIdentifierConfig));
        return restResponse;
    }
}