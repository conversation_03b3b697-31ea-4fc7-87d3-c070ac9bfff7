package com.sinoyd.lims.lim.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 测试项目导出实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/05/07
 * @since V100R001
 */
@Data
public class TestItemCombineVO {
    /**
     * 分析项目
     */
    @Excel(name = "分析项目",orderNum = "10",width = 25)
    private String itemAliasText;

    /**
     * 分析方法
     */
    @Excel(name = "分析方法",orderNum = "20",width = 35)
    private String methodAliasText;

    /**
     * 标准编号
     */
    @Excel(name = "标准编号",orderNum = "30",width = 22)
    private String redCountryStandard;

    /**
     * 检测类型
     */
    @Excel(name = "检测类型",orderNum = "40",width = 20)
    private String sampleTypeName;

    /**
     * 行业类型
     */
    @Excel(name = "行业类型",orderNum = "50",width = 20)
    private String industryTypeName;

    /**
     * 检测资质
     */
    @Excel(name = "检测资质",orderNum = "60",width = 20)
    private String cert;

    /**
     * 相关测试项目数
     */
    @Excel(name = "相关测试项目数",orderNum = "70",width = 20)
    private Integer relatedTestCount;
}
