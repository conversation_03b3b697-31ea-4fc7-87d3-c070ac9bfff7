package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoEnvironmentalRecord;
import java.util.List;

/**
 * 环境管理
 * <AUTHOR>
 * @version V1.0.0 2019/5/14
 * @since V100R001
 */
public interface EnvironmentalRecordRepository extends IBaseJpaPhysicalDeleteRepository<DtoEnvironmentalRecord, String> {

    /**
     * 根据关联id查询仪器使用记录
     * @param objectId 关联id
     * @return 仪器使用记录
     */
    List<DtoEnvironmentalRecord> findByObjectId(String objectId);

    /**
     * 根据关联id集合查询仪器使用记录
     *
     * @param objectIds 关联id集合
     * @return 仪器使用记录
     */
    List<DtoEnvironmentalRecord> findByObjectIdIn(List<String> objectIds);

    /**
     * 根据关联id集合删除仪器使用记录
     *
     * @param objectIds 关联id集合
     * @return 删除条数
     */
    Integer deleteByObjectIdIn(List<String> objectIds);
}