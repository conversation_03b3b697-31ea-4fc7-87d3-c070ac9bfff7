package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * TestQCRange实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="TestQCRange")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class TestQCRange implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  TestQCRange() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 测试标识
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("测试标识")
    private String testId;
    
    /**
    * 数值范围
    */
    @Column(length=50)
    @ApiModelProperty("数值范围")
    @Length(message = "数值范围{validation.message.length}", max = 50)
    private String rangeConfig;
    
    /**
    * 平行：相对偏差，其他质控类型：偏差
    */
    @Column(length=50)
    @ApiModelProperty("平行：相对偏差，其他质控类型：偏差")
    @Length(message = "偏差类型{validation.message.length}", max = 50)
    private String relLimit;
    
    /**
    * 绝对偏差
    */
    @Column(length=50)
    @ApiModelProperty("绝对偏差")
    @Length(message = "绝对偏差{validation.message.length}", max = 50)
    private String absLimit;
    
    /**
    * 质控等级（枚举EnumQCGrade：1.外部质控  2.内部质控）
    */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("质控等级（枚举EnumQCGrade：1.外部质控  2.内部质控）")
    private Integer qcGrade;
    
    /**
    * 质控类型（枚举EnumQCType：1.平行 2.空白 4.加标 8.标样）
    */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("质控类型（枚举EnumQCType：1.平行 2.空白 4.加标 8.标样）")
    private Integer qcType;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }