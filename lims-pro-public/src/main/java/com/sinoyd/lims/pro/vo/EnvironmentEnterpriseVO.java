package com.sinoyd.lims.pro.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 环保企业通信息推送VO
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2024/11/01
 **/
@Data
public class EnvironmentEnterpriseVO {

    public EnvironmentEnterpriseVO() {
    }

    public EnvironmentEnterpriseVO(String id, String uscc, String code, Integer status, Integer reportStatus, String createDate, Boolean isDeleted, Integer dueSampleNum,
                                   Integer yetSampleNum, Integer dueDetectNum, Integer yetDetectNum, List<EnvironmentEnterpriseReportVO> reportFiles,
                                   List<EnvironmentEnterpriseSamplingVO> items) {
        this.id = id;
        this.uscc = uscc;
        this.code = code;
        this.status = status;
        this.reportStatus = reportStatus;
        this.createDate = createDate;
        this.isDeleted = isDeleted;
        this.dueSampleNum = dueSampleNum;
        this.yetSampleNum = yetSampleNum;
        this.dueDetectNum = dueDetectNum;
        this.yetDetectNum = yetDetectNum;
        this.reportFiles = reportFiles;
        this.items = items;
    }

    /**
     * 项目id
     */
    private String id;

    /**
     * 受检方社会信用代码
     */
    private String uscc;

    /**
     * 项目编号
     */
    private String code;

    /**
     * 项目状态 1登记 2派发 3开展中 6报告 7完成
     */
    private Integer status;

    /**
     * 报告状态 1报告编制 2报告审核 3报告签发 4报告完成
     */
    private Integer reportStatus;

    /**
     * 创建时间
     */
    private String createDate;

    /**
     * 删除标记
     */
    private Boolean isDeleted;

    /**
     * 应采样数
     */
    private Integer dueSampleNum;

    /**
     * 已采样数
     */
    private Integer yetSampleNum;

    /**
     * 应检测数
     */
    private Integer dueDetectNum;

    /**
     * 已检测数
     */
    private Integer yetDetectNum;

    /**
     * 报告文件信息
     */
    private List<EnvironmentEnterpriseReportVO> reportFiles;

    /**
     * 采样信息
     */
    private List<EnvironmentEnterpriseSamplingVO> items;

}
