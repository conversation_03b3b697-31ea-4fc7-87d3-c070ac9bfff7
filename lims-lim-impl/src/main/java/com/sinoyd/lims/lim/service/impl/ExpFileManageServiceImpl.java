package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.criteria.DocumentCriteria;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.repository.lims.DocumentRepository;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.dto.customer.expimp.DtoExpFileManage;
import com.sinoyd.lims.lim.dto.customer.expimp.DtoExpImpEnterprise;
import com.sinoyd.lims.lim.service.transform.ExpDocumentManageService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 文件管理清单导出接口
 *
 * @version V1.0.0 2025/4/27
 * @author: xiexy
 * @since V100R001
 */
@Service
public class ExpFileManageServiceImpl extends BaseJpaServiceImpl<DtoDocument, String, DocumentRepository> implements ExpDocumentManageService {

    @Autowired
    @Lazy
    private DocumentService documentService;

    @Autowired
    @Lazy
    private ImportUtils importUtils;

    @Override
    public List<DtoDocument> importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception {
        return null;
    }

    @Override
    public void addData(List<DtoDocument> data) {

    }

    @Override
    public ExcelImportResult<DtoExpFileManage> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        return null;
    }

    @Override
    public ExcelImportResult<DtoExpFileManage> getExcelData(IExcelVerifyHandler<DtoExpFileManage> verifyHandler, MultipartFile file, HttpServletResponse response) throws Exception {
        return null;
    }

    @Override
    public void export(BaseCriteria criteria, HttpServletResponse response, Map<String, String> sheetNames, String fileName) {
        PageBean<DtoDocument> pageBean = new PageBean<>();
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        DocumentCriteria documentCriteria = (DocumentCriteria)criteria;
        documentService.findByPage(pageBean,documentCriteria);
        List<DtoDocument> list = pageBean.getData();
        List<DtoExpFileManage> expFileManageList = new ArrayList<>();
        for (DtoDocument document :list ) {
            DtoExpFileManage expFileManage = new DtoExpFileManage();
            expFileManage.setFilename(document.getFilename());
            expFileManage.setDocSuffix(document.getDocSuffix());
            expFileManage.setCreateDate(DateUtil.dateToString(document.getCreateDate(),DateUtil.YEAR));
            expFileManage.setUploadPerson(document.getUploadPerson());
            expFileManage.setDownloadTimes(document.getDownloadTimes());
            expFileManageList.add(expFileManage);
        }
        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoExpFileManage.class, expFileManageList);
        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
    }
}

