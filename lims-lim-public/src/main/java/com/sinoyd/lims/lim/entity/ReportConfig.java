package com.sinoyd.lims.lim.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * ReportConfig实体
 *
 * <AUTHOR>
 * @version V1.0.0 2020/10/13
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "ReportConfig")
@Data
@EntityListeners(AuditingEntityListener.class)
public class ReportConfig implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public ReportConfig() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 报表类型（枚举：EnumReportConfigType:1文件，2统计面板，3json数据源)
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("报表类型（枚举：EnumReportConfigType:1文件，2统计面板，3json数据源)")
    private Integer type;

    /**
     * 报表编码
     */
    @Column(length = 100)
    @ApiModelProperty("报表编码")
    @Length(message = "报表编码{validation.message.length}", max = 100)
    private String reportCode;

    /**
     * 类型编码
     */
    @Column(length = 50)
    @ApiModelProperty("类型编码")
    @Length(message = "类型编码{validation.message.length}", max = 50)
    private String typeCode;

    /**
     * 模板名称
     */
    @Column(length = 100)
    @ApiModelProperty("模板名称")
    @Length(message = "模板名称{validation.message.length}", max = 100)
    private String templateName;

    /**
     * 模版文件全名
     */
    @Column(length = 100)
    @ApiModelProperty("模版文件全名")
    @Length(message = "模版文件全名{validation.message.length}", max = 100)
    private String template;

    /**
     * 输出文件名
     */
    @Column(length = 100)
    @ApiModelProperty("输出文件名")
    @Length(message = "输出文件名{validation.message.length}", max = 100)
    private String outputName;

    /**
     * 返回文件类型（doc/docx/pdf……）
     */
    @Column(length = 20)
    @ApiModelProperty("返回文件类型（doc/docx/pdf……）")
    @Length(message = "返回文件类型{validation.message.length}", max = 20)
    private String returnType;

    /**
     * 调用方法全名
     */
    @ApiModelProperty("调用方法全名")
    @Length(message = "调用方法全名{validation.message.length}", max = 255)
    private String method;

    /**
     * 数据调用方法
     */
    @ApiModelProperty("数据调用方法")
    @Length(message = "数据调用方法{validation.message.length}", max = 255)
    private String dataMethod;

    /**
     * 参数键值对设置
     */
    @Column(length = 500)
    @ApiModelProperty("参数键值对设置")
    @Length(message = "参数键值对设置{validation.message.length}", max = 500)
    private String params;

    /**
     * json格式的页面配置
     */
    @ApiModelProperty("json格式的页面配置")
    private String pageConfig;

    /**
     * 接口路径
     */
    @Column(length = 50)
    @ApiModelProperty("接口路径")
    @Length(message = "接口路径{validation.message.length}", max = 50)
    private String strUrl;

    /**
     * 排序值
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("排序值")
    private Integer orderNum = 0;

    /**
     * 业务分类（枚举：EnumRecordType（1.报表 2.原始记录单）
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("业务分类（枚举：EnumRecordType（1.报表 2.原始记录单）")
    private Integer bizType;

    /**
     * 是否定义生成报表名称
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否定义生成报表名称")
    private Boolean isDefineFileName = false;

    /**
     * 配置报表名称
     */
    @Column(length = 500)
    @ApiModelProperty("配置报表名称")
    @Length(message = "配置报表名称{validation.message.length}", max = 500)
    private String defineFileName;

    /**
     * 配置方法名称
     */
    @Column(length = 500)
    @ApiModelProperty("配置方法名称")
    @Length(message = "配置方法名称{validation.message.length}", max = 500)
    private String beanName;

    /**
     * 备注
     */
    @Column(length = 500)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 500)
    private String remark;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;


    /**
     * 版本号
     */
    @Column(length = 100)
    @ApiModelProperty("版本号")
    @Length(message = "版本号{validation.message.length}", max = 100)
    private String versionNum;

    /**
     * 受控编号
     */
    @Column(length = 100)
    @ApiModelProperty("受控编号")
    @Length(message = "受控编号{validation.message.length}", max = 100)
    private String controlNum;

    /**
     * 版本号位置（EnumVersionNumLocation）
     */
    @Column(length = 50)
    @ApiModelProperty("版本号位置（EnumVersionNumLocation）")
    @Length(message = "版本号位置{validation.message.length}", max = 50)
    private String versionNumLocation;

    /**
     * 受控编号位置（EnumControlNumLocation）
     */
    @Column(length = 50)
    @ApiModelProperty("受控编号位置（EnumControlNumLocation）")
    @Length(message = "受控编号位置{validation.message.length}", max = 50)
    private String controlNumLocation;

    /**
     * 报表表头名称（多个sheet页的名称用;隔开）
     */
    @Column(length = 1000)
    @ApiModelProperty("报表表头名称")
    @Length(message = "报表表头名称{validation.message.length}", max = 1000)
    private String reportName;

    /**
     * 验证状态 0未验证 1已验证
     */
    @ColumnDefault("0")
    @ApiModelProperty("验证状态 0未验证 1已验证")
    private Integer validate;

    /**
     * 使用次数
     */
    @ApiModelProperty("使用次数")
    private Integer usageNum;

}