package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.lim.criteria.TestCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.pro.dto.DtoSampleFolder;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequency;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTest;
import com.sinoyd.lims.pro.dto.customer.DtoSampleFolderTestCount;
import com.sinoyd.lims.pro.repository.SampleFolderRepository;
import com.sinoyd.lims.pro.repository.SamplingFrequencyRepository;
import com.sinoyd.lims.pro.repository.SamplingFrequencyTestRepository;
import com.sinoyd.lims.pro.service.FolderExtendService;
import com.sinoyd.lims.pro.service.ProjectPushService;
import com.sinoyd.lims.pro.service.SampleFolderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * FolderExtend操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2022/11/28
 * @since V100R001
 */
@Slf4j
@Service
public class FolderExtendServiceImpl  implements FolderExtendService {

    private SampleFolderRepository sampleFolderRepository;

    private SampleFolderService sampleFolderService;

    private SamplingFrequencyRepository samplingFrequencyRepository;

    private SamplingFrequencyTestRepository samplingFrequencyTestRepository;

    private TestService testService;

    private TestRepository testRepository;

    private ProjectPushService projectPushService;

    @Override
    @Transactional
    public void batchUpdateAnalyzeMethod(DtoTest test) {
        List<DtoTest> testList = testRepository.findAll(test.getTestIds());
        testList.forEach(f->{
            f.setShMethodId(test.getShMethodId());
            f.setShMethodName(test.getShMethodName());
        });
        testRepository.save(testList);
    }

    @Override
    @Transactional
    public void batchUpdateSamplingMethod(DtoTest test) {
        List<DtoTest> testList = testRepository.findAll(test.getTestIds());
        testList.forEach(f->{
            f.setShSamplingMethodId(test.getShSamplingMethodId());
            f.setShSamplingMethodName(test.getShSamplingMethodName());
        });
        testRepository.save(testList);
    }

    @Override
    public List<DtoSampleFolder> findFoldersByProjectId(String projectId) {
        List<DtoSampleFolder> sampleFolders = sampleFolderRepository.findByProjectId(projectId);
        List<String> sampleFolderIds = sampleFolders.stream().map(DtoSampleFolder::getId).collect(Collectors.toList());
        List<DtoSamplingFrequency> frequencies = samplingFrequencyRepository.findBySampleFolderIdIn(sampleFolderIds);
        sampleFolders.forEach(s->{
            List<DtoSamplingFrequency> frequencies2Folder = frequencies.stream().filter(f->f.getSampleFolderId().equals(s.getId())).collect(Collectors.toList());
            Optional<DtoSamplingFrequency> frequency = frequencies2Folder.stream().max(Comparator.comparing(DtoSamplingFrequency::getPeriodCount));
            frequency.ifPresent(f->s.setPeriodCount(f.getPeriodCount()));
            frequency = frequencies2Folder.stream().max(Comparator.comparing(DtoSamplingFrequency::getTimePerPeriod));
            frequency.ifPresent(f->s.setTimePerPeriod(f.getTimePerPeriod()));
        });
        return sampleFolders.stream().sorted(Comparator.comparing(DtoSampleFolder::getWatchSpot)).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public List<DtoTest> findBySampleFolderId(BaseCriteria criteria) {
        TestCriteria extendCriteria = (TestCriteria) criteria;
        List<DtoSamplingFrequencyTest> frequencyTests = samplingFrequencyTestRepository.findBySampleFolderId(extendCriteria.getSampleFolderId());
        List<String> testIds = frequencyTests.stream().map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> testList = testService.findAll(testIds);
        fillingTransientFieldsBySampleFolderId(testList,extendCriteria.getSampleFolderId());
        if(StringUtil.isNotEmpty(extendCriteria.getKey())) {
            String key = extendCriteria.getKey();
            testList = testList.stream().filter(r->r.getRedAnalyzeItemName().contains(key)||r.getRedAnalyzeMethodName().contains(key)).collect(Collectors.toList());
        }
        testList.sort(Comparator.comparing(DtoTest::getRedAnalyzeItemName));
        return testList;
    }

    @Override
    public List<DtoTest> findByProjectId(String projectId) {
        List<DtoSampleFolder> sampleFolders = sampleFolderRepository.findByProjectId(projectId);
        if(StringUtil.isEmpty(sampleFolders)) {
            throw new BaseException("该任务下没有点位");
        }
        List<String> sampleFolderIds = sampleFolders.stream().map(DtoSampleFolder::getId).collect(Collectors.toList());
        List<DtoSamplingFrequencyTest> frequencyTests = samplingFrequencyTestRepository.findBySampleFolderIdIn(sampleFolderIds);
        return testService.findAll(frequencyTests.stream().map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList()));
    }

    /**
     * 填充冗余字段
     *
     * @param data  数据
     * @param sampleFolderId  点位id
     */
    private void fillingTransientFieldsBySampleFolderId(List<DtoTest> data,String sampleFolderId) {
        List<DtoSampleFolderTestCount> countList = sampleFolderService.querySampleFolderTestCount(Collections.singletonList(sampleFolderId));
        Map<String, String> req = new HashMap<>();
        req.put("methodName", "M_MonitoringAnalysisMethod");
        req.put("page", "1");
        req.put("rows", "100000");
        List<Map<String, Object>> mapList = new ArrayList<>();
        try {
            mapList = projectPushService.queryByMethodName(req,new RestResponse());
        } catch (BaseException e) {
            log.error(e.getMessage(), e);
        }
        final List<Map<String, Object>> finalMapList = mapList;
        data.forEach(d->{
            Optional<Map<String, Object>> map = finalMapList.stream().filter(m->StringUtil.isNotNull(m.get("METHODID"))&&(m.get("METHODID").toString()).equals(d.getShMethodId())).findFirst();
            map.ifPresent(m->{
                if(StringUtil.isNotNull(m.get("ITEMNAME")) && !m.get("ITEMNAME").toString().equals(d.getRedAnalyzeItemName())){
                    d.setMessage("选择监管平台分析方法对应的分析项目名称与平台不匹配");
                }
            });
            countList.stream().filter(v->d.getId().equals(v.getTestId())).findFirst().ifPresent(v->d.setSampleCount(v.getSampleCount()));
        });
    }

    @Autowired
    public void setSampleFolderRepository(SampleFolderRepository sampleFolderRepository) {
        this.sampleFolderRepository = sampleFolderRepository;
    }

    @Autowired
    public void setSamplingFrequencyRepository(SamplingFrequencyRepository samplingFrequencyRepository) {
        this.samplingFrequencyRepository = samplingFrequencyRepository;
    }

    @Autowired
    public void setSamplingFrequencyTestRepository(SamplingFrequencyTestRepository samplingFrequencyTestRepository) {
        this.samplingFrequencyTestRepository = samplingFrequencyTestRepository;
    }

    @Autowired
    @Lazy
    public void setTestService(TestService testService) {
        this.testService = testService;
    }

    @Autowired
    @Lazy
    public void setProjectPushService(ProjectPushService projectPushService) {
        this.projectPushService = projectPushService;
    }

    @Autowired
    @Lazy
    public void setSampleFolderService(SampleFolderService sampleFolderService) {
        this.sampleFolderService = sampleFolderService;
    }

    @Autowired
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }
}
