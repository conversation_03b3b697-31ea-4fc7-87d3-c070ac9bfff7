package com.sinoyd.lims.foreign.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.foreign.DataCurveDetailVO;
import com.sinoyd.lims.foreign.service.DataAnalyseDataService;
import com.sinoyd.lims.foreign.service.DataCurveDetailService;
import com.sinoyd.lims.foreign.service.impl.DataAnalyseDataServiceImpl;
import com.sinoyd.lims.foreign.service.impl.DataCurveDetailServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 曲线明细详情服务
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Api(tags = "示例: dataCurveDetail服务")
@RestController
@RequestMapping("api/pro/foreign/dataCurveDetail")
public class DataCurveDetailController extends ExceptionHandlerController<DataCurveDetailService> {

    /**
     * 曲线明细数据同步
     *
     * @param map 同步时间
     * @return RestResponse<List < DataAnalyseDataVO>>
     */
    @ApiOperation(value = "数据同步", notes = "数据同步")
    @PostMapping("/syncData")
    public RestResponse<List<DataCurveDetailVO>> getSyncData(@RequestBody Map<String,Object> map) {
        List<DataCurveDetailVO> curveVOList = service.getSyncData(map);
        RestResponse<List<DataCurveDetailVO>> response = new RestResponse();
        response.setData(curveVOList);
        return response;
    }
}
