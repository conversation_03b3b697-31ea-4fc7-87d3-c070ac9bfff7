package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.ReportFolderSortInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;


/**
 * DtoReportFolderSortInfo实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/07
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_ReportFolderSortInfo")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoReportFolderSortInfo extends ReportFolderSortInfo {

    /**
     * 点位名称
     */
    @Transient
    private String folderName;

}