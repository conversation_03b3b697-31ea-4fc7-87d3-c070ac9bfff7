package com.sinoyd.base.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * EvaluationCriteria实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="EvaluationCriteria")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class EvaluationCriteria implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  EvaluationCriteria() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 评价标准名称
    */
    @Column(length=100)
    @ApiModelProperty("评价标准名称")
    @Length(message = "评价标准名称{validation.message.length}", max = 100)
	private String name;
    
    /**
    * 标准代码
    */
    @Column(length=20)
    @ApiModelProperty("标准代码")
    @Length(message = "标准代码{validation.message.length}", max = 20)
    private String code;
    
    /**
    * 标准类型（常量BASE_EvaluateType：国标和地标）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("标准类型（常量BASE_EvaluateType：国标和地标）")
	private String categoryId;
    
    /**
    * 检测类型
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("检测类型")
	private String sampleTypeId;
    
    /**
    * 实施时间
    */
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("实施时间")
	private Date startTime;
    
    /**
    * 废止时间
    */
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("废止时间")
	private Date endTime;
    
    /**
    * 标准状态（枚举EnumEvaluateCriteriaStatus：1代表有效 2代表废止）
    */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("标准状态（枚举EnumEvaluateCriteriaStatus：1代表有效 2代表废止）")
    private Integer status;
    
    /**
    * 适用范围
    */
    @Column(length=1000)
    @ApiModelProperty("适用范围")
    @Length(message = "适用范围{validation.message.length}", max = 1000)
    private String applyRange;
    
    /**
    * 备注
    */
    @Column(length=1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
    private String remark;

    /**
     * 标准类别,关联枚举Enumbase.EnumEvaluationCriteriaType
     */
    @ApiModelProperty("标准类别")
    @Column(nullable=false)
    @ColumnDefault("1")
    private Integer criteriaType;
    
    /**
    * 假删
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
	private Boolean isDeleted=false;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }