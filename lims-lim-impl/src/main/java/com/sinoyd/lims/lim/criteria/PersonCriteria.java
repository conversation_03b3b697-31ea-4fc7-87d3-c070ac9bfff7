package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;

import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 人员管理查询条件
 * <AUTHOR>
 * @version v1.0.0 2019/5/6
 * @since v100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PersonCriteria extends BaseCriteria {
    
    /**
     * 关键字：姓名、编号、拼音
     */
    private String key;

    /**
     * 姓名
     */
    private String cName;

    /**
     * 所属科室(默认获取当前登录人员的单位id代表所有)
     */
    private String deptId;

    /**
     * 职务（空id代表所有）
     */
    private String postId;

    /**
     * 职称（空id代表所有）
     */
    private String technicalTitleId;
    /**
     * 职务（空id代表所有）
     */
    private Integer status;

    /**
     * 是否排除离职的人员
     */
    private Boolean isExcludeLeaved = false;

    @Override
    public String getCondition() {
        values.clear(); // 清除条件数据
        StringBuilder condition = new StringBuilder();

        // 关键字模糊查找
        if (StringUtils.isNotNullAndEmpty(key)) {
            condition.append(" and (cName like :key or pinYin like :key or fullPinYin like :key or userNo like :key)");
            values.put("key", "%" + this.key + "%");
        }
        if(StringUtil.isNotEmpty(cName)){
            condition.append(" and cName like :cName");
            values.put("cName", "%" + this.cName + "%");
        }
        //根据科室查找
        if (StringUtils.isNotNullAndEmpty(deptId) && !UUIDHelper.GUID_EMPTY.equals(this.deptId)) {
            condition.append(" and (deptId = :deptId)");
            values.put("deptId", this.deptId);
        }
        //根据职务查找
        if (StringUtils.isNotNullAndEmpty(postId) && !UUIDHelper.GUID_EMPTY.equals(this.postId)) {
            condition.append(" and (postId = :postId)");
            values.put("postId", this.postId);
        }
        //根据职称查找
        if (StringUtils.isNotNullAndEmpty(technicalTitleId) && !UUIDHelper.GUID_EMPTY.equals(this.technicalTitleId)) {
            condition.append(" and (technicalTitleId = :technicalTitleId)");
            values.put("technicalTitleId", this.technicalTitleId);
        }
        //根据人员状态查找
        if ( StringUtil.isNotNull(status) &&  status!= -1) {
            condition.append(" and (status = :status)");
            values.put("status", this.status);
        }

        //排除离职的人员
        if(isExcludeLeaved){
            condition.append(" and status != :status ");
            values.put("status", EnumLIM.EnumPersonStatus.离职.getValue());
        }
        condition.append(" and isDeleted = 0 ");

        return condition.toString();
    }
}