package com.sinoyd.lims.od.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoTraining;
import com.sinoyd.lims.od.dto.DtoStationarySourceResult;

import java.util.List;

/**
 * StationarySourceResultRepository
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
public interface StationarySourceResultRepository extends IBaseJpaRepository<DtoStationarySourceResult, String> {

    /**
     * 根据实验组ids查询
     *
     * @param groupIds 实验组ids
     * @return StationarySourceResult集合
     */
    List<DtoStationarySourceResult> findByGroupIdIn(List<String> groupIds);

    /**
     * 根据实验组id查询
     *
     * @param groupId 实验组id
     * @return StationarySourceResult集合
     */
    DtoStationarySourceResult findByGroupId(String groupId);
}
