package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.sinoyd.base.utils.base.DivationUtils;
import com.sinoyd.base.utils.poi.ExcelStyle;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.JsonUtil;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.common.http.HTTPCaller;
import com.sinoyd.common.http.NameValuePair;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.criteria.InstrumentGatherDataCriteria;
import com.sinoyd.lims.lim.dto.customer.DtoInstrumentGatherDataVo;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentGather;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentGatherData;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentGatherDataDetails;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentGatherParams;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.InstrumentGatherDataDetailsRepository;
import com.sinoyd.lims.lim.repository.lims.InstrumentGatherDataRepository;
import com.sinoyd.lims.lim.repository.lims.InstrumentGatherParamsRepository;
import com.sinoyd.lims.lim.service.InstrumentGatherDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 仪器接入数据操作接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/17
 * @since V100R001
 */
@Service
@Slf4j
@SuppressWarnings("unchecked")
public class InstrumentGatherDataServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoInstrumentGatherData, String, InstrumentGatherDataRepository> implements InstrumentGatherDataService {

    /**
     * 在线平台的唯一标示
     */
    @Value("${instrument-gather.appID:16}")
    private String INSTRUMENT_GATHER_APPID;
    /**
     * 在线平台秘钥
     */
    @Value("${instrument-gather.appSecret:9e5bf0c1e2646ead56e239039cd08f7b}")
    private String INSTRUMENT_GATHER_APPSECRET;
    /**
     * 统计社会信用代码
     */
    @Value("${instrument-gather.uscc:123456789999999999}")
    private String INSTRUMENT_GATHER_USCC;
    /**
     * 事实热数据url
     */
    @Value("${instrument-gather.hotdata_url:https://ddc.envchina.com/api/pl-hotdata}")
    private String INSTRUMENT_GATHER_HOTDATA_URL;
    /**
     * 数据查询url
     */
    @Value("${instrument-gather.rtdata_url:https://ddc.envchina.com/api/pl-rtdata}")
    private String INSTRUMENT_GATHER_RTDATA_URL;

    /**
     * 第一次刷新时间记录（时间戳）
     */
    private final String FIRST_REFRESHTIME_FLAG = "firstRefreshTimeFlag";

    /**
     * 同步锁
     */
    private final Object lockObject = new Object();

    private InstrumentGatherDataDetailsRepository instrumentGatherDataDetailsRepository;
    private InstrumentGatherParamsRepository instrumentGatherParamsRepository;
    private RedisTemplate redisTemplate;

    @Override
    public void findByPage(PageBean<DtoInstrumentGatherData> page, BaseCriteria criteria) {
        // 设置查询的实体类名及别名
        page.setEntityName("DtoInstrumentGatherData x");
        // 设置查询返回的字段、实体别名表示所有字段
        page.setSelect("select x");
        super.findByPage(page, criteria);
    }

    @Override
    public Map<String, List<Map<String, Object>>> findRealTimeData(DtoInstrumentGatherDataVo instrumentGatherDataVo) {
        Map<String, List<Map<String, Object>>> resultMap = new HashMap<>();
        // 参数配置数据
        List<DtoInstrumentGatherParams> instrumentGatherParams = instrumentGatherParamsRepository.findByInstrumentGatherId(instrumentGatherDataVo.getInstrumentGatherId());
        // 筛选结果参数和通道参数
        List<DtoInstrumentGatherParams> resultDataParams = instrumentGatherParams.stream()
                .filter(p -> p.getDataType().equals(EnumLIM.EnumInstrumentGatherDataType.结果数据.getValue()))
                .sorted(Comparator.comparing(DtoInstrumentGatherParams::getOrderNum).reversed()).collect(Collectors.toList());

        Map<String, Object> dataMap = new HashMap<>();
        List<NameValuePair> paramList = new ArrayList<>();
        paramList.add(new NameValuePair("type", EnumLIM.EnumInstrumentGatherDataType.实时数据.getValue()));
        paramList.add(new NameValuePair("mn", instrumentGatherDataVo.getMnNumber()));
        Map<String, Object> objectMap = remotingRequest(INSTRUMENT_GATHER_HOTDATA_URL, paramList);

        String mnKey = String.format("%s:%s", instrumentGatherDataVo.getMnNumber(), EnumLIM.EnumInstrumentGatherDataType.实时数据.getValue());

        if (objectMap.containsKey(mnKey) && objectMap.get(mnKey) instanceof List<?>) {
            List list = (List<?>) objectMap.get(mnKey);
            Map<String, Object> mnMap = (Map<String, Object>) list.get(0);
            dataMap = (Map<String, Object>) mnMap.get("data");
        }
        boolean isChannel = instrumentGatherParams.stream().anyMatch(p -> p.getDataType().equals(EnumLIM.EnumInstrumentGatherDataType.通道数据.getValue()));
        // 根据响应数据返回值解析，key值中包含Channel字段的所有key进行分组，获得所有通道
        List<String> channelList = dataMap.keySet().stream().filter(p -> p.split("-").length > 1 && p.split("-")[0].contains("Channel"))
                .map(p -> p.split("-")[0]).distinct().collect(Collectors.toList());
        // 通道数据不为空时，并且配置了通道参数。根据通道遍历，否则默认当前数据没有设置通道
        if (StringUtil.isNotEmpty(channelList) && isChannel) {
            for (String channelKey : channelList) {
                // 循环参数配置进行赋值
                List<Map<String, Object>> paramDataList = new ArrayList<>();
                for (DtoInstrumentGatherParams resultDataParam : resultDataParams) {
                    String value = "";
                    String paramLabel = resultDataParam.getParamLabel();
                    // 拼接通道序号判断是否存在，否则则是公共参数直接使用标识判断
                    if (dataMap.containsKey(channelKey + "-" + paramLabel)) {
                        value = dataMap.get(channelKey + "-" + paramLabel).toString();
                    } else {
                        value = dataMap.containsKey(paramLabel) ? dataMap.get(paramLabel).toString() : "";
                    }
                    value = handleValue(value, resultDataParam);
                    Map<String, Object> map = new HashMap<>();
                    String label = resultDataParam.getParamName() + (StringUtil.isNotEmpty(resultDataParam.getDimension()) ? "(" + resultDataParam.getDimension() + ")" : "");
                    map.put("label", label);
                    map.put("value", value);
                    paramDataList.add(map);
                }
                // 截取通道序号
                channelKey = channelKey.replace("Channel", "");
                resultMap.put(channelKey, paramDataList);
            }
        } else {
            List<Map<String, Object>> paramDataList = new ArrayList<>();
            for (DtoInstrumentGatherParams resultDataParam : resultDataParams) {
                Map<String, Object> map = new HashMap<>();
                String value = "";
                if (dataMap.containsKey(resultDataParam.getParamLabel())) {
                    value = dataMap.get(resultDataParam.getParamLabel()).toString();
                    value = handleValue(value, resultDataParam);
                }
                String paramName = resultDataParam.getParamName() + (StringUtil.isNotEmpty(resultDataParam.getDimension()) ? "(" + resultDataParam.getDimension() + ")" : "");
                map.put("label", paramName);
                map.put("value", value);
                paramDataList.add(map);
            }
            resultMap.put("0", paramDataList);
        }
        return resultMap;
    }

    /**
     * 处理枚举值和时间
     *
     * @param value        结果值
     * @param gatherParams 参数
     * @return 处理后的结果
     */
    private String handleValue(String value, DtoInstrumentGatherParams gatherParams) {
        value = replaceEnum(value, gatherParams);
        // 时间戳转日期
        if (gatherParams.getParamLabel().contains("_time") && StringUtil.isNotEmpty(value)) {
            value = DateUtil.dateToString(new Date(Long.parseLong(value) * 1000), DateUtil.FULL);
        }
        return value;
    }


    @Override
    public Map<String, List<Map<String, Object>>> findParamData(DtoInstrumentGatherDataVo instrumentGatherDataVo) {
        Map<String, List<Map<String, Object>>> dataVo = new HashMap<>();
        // 参数配置数据
        List<DtoInstrumentGatherParams> instrumentGatherParams = instrumentGatherParamsRepository.findByInstrumentGatherId(instrumentGatherDataVo.getInstrumentGatherId());
        // 工况参数
        List<DtoInstrumentGatherParams> parameterParams = instrumentGatherParams.stream()
                .filter(p -> p.getDataType().equals(EnumLIM.EnumInstrumentGatherDataType.工况数据.getValue())).collect(Collectors.toList());
        // 筛选通道参数
        DtoInstrumentGatherParams channelParams = instrumentGatherParams.stream()
                .filter(p -> p.getDataType().equals(EnumLIM.EnumInstrumentGatherDataType.通道数据.getValue())).findFirst().orElse(null);
        // 查询所有工况数据
        List<DtoInstrumentGatherData> gatherDataList = repository.findByInstrumentGatherIdAndDataType(instrumentGatherDataVo.getInstrumentGatherId(), EnumLIM.EnumInstrumentGatherDataType.工况数据.getValue());
        boolean channelFlag = Boolean.FALSE;
        if (null != channelParams) {
            channelFlag = Boolean.TRUE;
        }
        // 参数根据排序值排序
        parameterParams.sort(Comparator.comparing(DtoInstrumentGatherParams::getOrderNum).reversed());
        if (channelFlag) {
            List<String> gatherDataIds = gatherDataList.stream().map(DtoInstrumentGatherData::getId).collect(Collectors.toList());
            List<DtoInstrumentGatherDataDetails> gatherDataDetailers = instrumentGatherDataDetailsRepository.findByInstrumentGatherDataIdIn(gatherDataIds);
            // 筛选通道类型的参数，并根据通道值进行分组
            Map<String, List<DtoInstrumentGatherDataDetails>> detailsMap = gatherDataDetailers.stream().filter(p -> p.getInstrumentGatherParamsId().equals(channelParams.getId()))
                    .collect(Collectors.groupingBy(DtoInstrumentGatherDataDetails::getParamValue));
            // 根据通道数量遍历筛选
            for (int i = 0; i < channelParams.getChannelNum(); i++) {

                // 定义实时数据和参数数据容器，每一个通道一组容器
                // 动态字段按照 每个字段一个对象的格式，所有字段组装成一个集合
                List<Map<String, Object>> paramDataList = new ArrayList<>();
                // 根据通道序号筛选出当前通道的所有数据详情, 通道的值定义为数字
                List<String> gatherDataIdsOfChannel = detailsMap.getOrDefault(String.valueOf(i), new ArrayList<>()).stream()
                        .map(DtoInstrumentGatherDataDetails::getInstrumentGatherDataId).distinct().collect(Collectors.toList());
                // 当前通道下的所有数据
                List<DtoInstrumentGatherData> gatherDataListOfChannel = gatherDataList.stream().filter(p -> gatherDataIdsOfChannel.contains(p.getId())).collect(Collectors.toList());
                Optional<DtoInstrumentGatherData> gatherDataOptional = gatherDataListOfChannel.stream().max(Comparator.comparing(DtoInstrumentGatherData::getUploadTime));
                gatherDataOptional.ifPresent(dtoInstrumentGatherData ->
                        this.setGatherData(dtoInstrumentGatherData, gatherDataDetailers, parameterParams, paramDataList));
                dataVo.put(String.valueOf(i), paramDataList);
            }
        } else {
            Optional<DtoInstrumentGatherData> gatherDataOptional = gatherDataList.stream().max(Comparator.comparing(DtoInstrumentGatherData::getUploadTime));
            List<Map<String, Object>> paramDataList = new ArrayList<>();
            if (gatherDataOptional.isPresent()) {
                List<DtoInstrumentGatherDataDetails> gatherDataDetailers = instrumentGatherDataDetailsRepository.findByInstrumentGatherDataId(gatherDataOptional.get().getId());
                this.setGatherData(gatherDataOptional.get(), gatherDataDetailers, parameterParams, paramDataList);
            }
            dataVo.put("0", paramDataList);
        }
        return dataVo;
    }


    @Override
    @Transactional
    public void refreshData(DtoInstrumentGatherDataVo instrumentGatherDataVo) {
        List<DtoInstrumentGatherData> instrumentGatherDataList = repository.findByInstrumentGatherId(instrumentGatherDataVo.getInstrumentGatherId());
        String startTimeTamp = getStartTimeByRedis(instrumentGatherDataList);
        long endTimeTamp = System.currentTimeMillis() / 1000;
        String times = startTimeTamp + "-" + endTimeTamp;
        List<NameValuePair> paramList = new ArrayList<>();
        String type = String.format("%s|%s", EnumLIM.EnumInstrumentGatherDataType.工况数据.getValue(), EnumLIM.EnumInstrumentGatherDataType.结果数据.getValue());
        paramList.add(new NameValuePair("type", type));
        paramList.add(new NameValuePair("times", times));
        paramList.add(new NameValuePair("mn", instrumentGatherDataVo.getMnNumber()));
        Map<String, Object> objectMap = remotingRequest(INSTRUMENT_GATHER_RTDATA_URL, paramList);

        if (null != objectMap && objectMap.containsKey("records") && objectMap.get("records") instanceof List<?>) {
            // 远程接口响应结果集合
            List<Map<String, Object>> responseList = (List<Map<String, Object>>) objectMap.get("records");
            // 剔除掉与最后一次记录时间相同的数据
            if (StringUtil.isNotEmpty(instrumentGatherDataList)) {
                responseList.removeIf(p -> p.get("_time").toString().equals(startTimeTamp));
            }
            if (StringUtil.isNotEmpty(responseList)) {
                // 响应数据根据类型分组
                Map<String, List<Map<String, Object>>> responseMap = responseList.stream().collect(Collectors.groupingBy(p -> p.get("_type").toString()));
                // 参数配置数据
                List<DtoInstrumentGatherParams> instrumentGatherParams = instrumentGatherParamsRepository.findByInstrumentGatherId(instrumentGatherDataVo.getInstrumentGatherId());
                // 筛选结果参数和通道参数，根据参数类型分组
                Map<String, List<DtoInstrumentGatherParams>> paramGroupMap = instrumentGatherParams.stream()
                        .filter(p -> p.getDataType().equals(EnumLIM.EnumInstrumentGatherDataType.结果数据.getValue()) || p.getDataType().equals(EnumLIM.EnumInstrumentGatherDataType.工况数据.getValue()))
                        .collect(Collectors.groupingBy(DtoInstrumentGatherParams::getDataType));
                // 通道参数数据
                List<DtoInstrumentGatherParams> channelParams = instrumentGatherParams.stream().filter(p -> p.getDataType().equals(EnumLIM.EnumInstrumentGatherDataType.通道数据.getValue())).collect(Collectors.toList());

                List<DtoInstrumentGatherData> gatherDataList = new ArrayList<>();
                List<DtoInstrumentGatherDataDetails> gatherDataDetailsList = new ArrayList<>();
                for (Map.Entry<String, List<Map<String, Object>>> entry : responseMap.entrySet()) {
                    // 当前类型分组下的所有响应数据
                    List<Map<String, Object>> responseListOfType = entry.getValue();
                    // 当前类型分组下的所有参数配置
                    List<DtoInstrumentGatherParams> gatherParams = paramGroupMap.get(entry.getKey());

                    // 工况数据一条报文返回多个通道数据
                    for (Map<String, Object> responseData : responseListOfType) {
                        // 根据响应数据返回值解析，key值中包含Channel字段的所有key进行分组，获得所有通道
                        List<String> channelList = responseData.keySet().stream().filter(p -> p.split("-").length > 1 && p.split("-")[0].contains("Channel"))
                                .map(p -> p.split("-")[0]).distinct().collect(Collectors.toList());
                        if (StringUtil.isNotEmpty(channelList) && StringUtil.isNotEmpty(channelParams)) {
                            for (String channelKey : channelList) {
                                // 新增数据
                                DtoInstrumentGatherData gatherData = new DtoInstrumentGatherData();
                                gatherData.setDataType(entry.getKey());
                                gatherData.setInstrumentGatherId(instrumentGatherDataVo.getInstrumentGatherId());
                                gatherData.setUploadTime(new Date(Long.parseLong(responseData.get("_time").toString()) * 1000));
                                for (DtoInstrumentGatherParams resultDataParam : gatherParams) {
                                    String value = "";
                                    String paramLabel = resultDataParam.getParamLabel();
                                    // 拼接通道序号判断是否存在，否则则是公共参数直接使用标识判断
                                    if (responseData.containsKey(channelKey + "-" + paramLabel)) {
                                        value = responseData.get(channelKey + "-" + paramLabel).toString();
                                    } else {
                                        value = responseData.containsKey(paramLabel) ? responseData.get(paramLabel).toString() : "";
                                    }
                                    value = handleValue(value, resultDataParam);
                                    DtoInstrumentGatherDataDetails details = new DtoInstrumentGatherDataDetails();
                                    details.setInstrumentGatherDataId(gatherData.getId());
                                    details.setInstrumentGatherParamsId(resultDataParam.getId());
                                    details.setParmaName(resultDataParam.getParamName());
                                    details.setParamValue(value);
                                    details.setDimension(resultDataParam.getDimension());
                                    details.setOrderNum(resultDataParam.getOrderNum());
                                    gatherDataDetailsList.add(details);
                                }
                                // 截取通道序号
                                channelKey = channelKey.replace("Channel", "");
                                DtoInstrumentGatherParams channelParam = channelParams.get(0);
                                gatherDataDetailsList.add(new DtoInstrumentGatherDataDetails(gatherData.getId(), channelParam.getId(),
                                        channelParam.getParamName(),
                                        channelKey, channelParam.getDimension()));
                                gatherDataList.add(gatherData);
                            }
                        } else {
                            // 新增没有通道的数据
                            this.fillGatherData(responseListOfType, instrumentGatherDataVo.getInstrumentGatherId(), entry.getKey(),
                                    gatherParams, gatherDataList, gatherDataDetailsList);
                        }
                    }
                }
                // 新增结果数据和详情数据
                repository.save(gatherDataList);
                instrumentGatherDataDetailsRepository.save(gatherDataDetailsList);
            }
        }
    }

    /**
     * 仪器接入数据赋值
     *
     * @param responseListOfType    远程响应数据
     * @param instrumentGatherId    仪器接入id
     * @param dataType              数据类型
     * @param gatherParams          参数数据
     * @param gatherDataList        数据集合容器
     * @param gatherDataDetailsList 数据详情集合容器
     */
    private void fillGatherData(List<Map<String, Object>> responseListOfType, String instrumentGatherId, String dataType,
                                List<DtoInstrumentGatherParams> gatherParams, List<DtoInstrumentGatherData> gatherDataList,
                                List<DtoInstrumentGatherDataDetails> gatherDataDetailsList) {
        for (Map<String, Object> responseData : responseListOfType) {
            // 新增数据
            DtoInstrumentGatherData gatherData = new DtoInstrumentGatherData();
            gatherData.setDataType(dataType);
            gatherData.setInstrumentGatherId(instrumentGatherId);
            gatherData.setUploadTime(new Date(Long.parseLong(responseData.get("_time").toString()) * 1000));
            for (String key : responseData.keySet()) {
                DtoInstrumentGatherParams param = gatherParams.stream().filter(p -> p.getParamLabel().equals(key)).findFirst().orElse(null);
                if (null != param) {
                    DtoInstrumentGatherDataDetails details = new DtoInstrumentGatherDataDetails();
                    details.setInstrumentGatherDataId(gatherData.getId());
                    details.setInstrumentGatherParamsId(param.getId());
                    details.setParmaName(param.getParamName());
                    String value = responseData.get(key).toString();
                    // 数据值为枚举时，根据配置的数据源解析
                    value = handleValue(value, param);
                    details.setParamValue(value);
                    details.setDimension(param.getDimension());
                    details.setOrderNum(param.getOrderNum());
                    gatherDataDetailsList.add(details);
                }
            }
            gatherDataList.add(gatherData);
        }
    }


    @Override
    public DtoInstrumentGatherDataVo findResultDataPage(PageBean<DtoInstrumentGatherData> pageBean, BaseCriteria
            instrumentGatherDataCriteria) {
        this.findByPage(pageBean, instrumentGatherDataCriteria);
        List<DtoInstrumentGatherData> data = pageBean.getData();
        // 赋值冗余字段
        return fillingTransientFields((InstrumentGatherDataCriteria) instrumentGatherDataCriteria, data);
    }

    @Override
    public void findLogsPage(PageBean<DtoInstrumentGatherData> pageBean, BaseCriteria
            instrumentGatherDataCriteria) {
        this.findByPage(pageBean, instrumentGatherDataCriteria);
        List<DtoInstrumentGatherData> gatherDataList = pageBean.getData();
        InstrumentGatherDataCriteria criteria = (InstrumentGatherDataCriteria) instrumentGatherDataCriteria;

        List<DtoInstrumentGatherParams> paramsList = instrumentGatherParamsRepository.findByInstrumentGatherId(criteria.getInstrumentGatherId());
        List<String> gatherDataIds = gatherDataList.stream().map(DtoInstrumentGatherData::getId).collect(Collectors.toList());
        List<DtoInstrumentGatherDataDetails> instrumentGatherDataDetails = StringUtil.isNotEmpty(gatherDataIds) ?
                instrumentGatherDataDetailsRepository.findByInstrumentGatherDataIdIn(gatherDataIds) : new ArrayList<>();
        // 详情数据填充排序值
        instrumentGatherDataDetails = instrumentGatherDataDetails.stream().peek(p -> {
            Optional<DtoInstrumentGatherParams> paramsOptional = paramsList.stream().filter(param -> p.getInstrumentGatherParamsId().equals(param.getId())).findFirst();
            if (paramsOptional.isPresent()) {
                p.setOrderNum(paramsOptional.get().getOrderNum());
            } else {
                p.setOrderNum(0);
            }
        }).collect(Collectors.toList());

        Map<String, List<DtoInstrumentGatherDataDetails>> detailMap = instrumentGatherDataDetails.stream().collect(Collectors.groupingBy(DtoInstrumentGatherDataDetails::getInstrumentGatherDataId));
        for (DtoInstrumentGatherData gatherData : gatherDataList) {
            List<DtoInstrumentGatherDataDetails> detailsList = detailMap.getOrDefault(gatherData.getId(), new ArrayList<>());
            detailsList.stream().filter(p -> p.getParmaName().equals("采样编号")).findFirst().ifPresent(param -> {
                gatherData.setSampleCode(param.getParamValue());
            });
            // 根据
            String content = detailsList.stream().sorted(Comparator.comparing(DtoInstrumentGatherDataDetails::getOrderNum).reversed())
                    .map(p -> String.format("%s=%s%s", p.getParmaName(), p.getParamValue(), p.getDimension()))
                    .collect(Collectors.joining(";"));
            gatherData.setContent(content);
        }
    }

    @Override
    public void exportResultData(BaseCriteria criteria, HttpServletResponse response) {
        PageBean<DtoInstrumentGatherData> page = new PageBean<>();
        page.setPageNo(1);
        page.setRowsPerPage(Integer.MAX_VALUE);
        // 设置查询的实体类名及别名
        page.setEntityName("DtoInstrumentGatherData x");
        // 设置查询返回的字段、实体别名表示所有字段
        page.setSelect("select x");
        super.findByPage(page, criteria);
        List<DtoInstrumentGatherData> dataList = page.getData();
        if (StringUtil.isNotEmpty(dataList)) {
            DtoInstrumentGatherDataVo gatherDataVo = fillingTransientFields((InstrumentGatherDataCriteria) criteria, dataList);
            List<DtoInstrumentGatherParams> voColumns = gatherDataVo.getColumns();
            // 添加序号列
            voColumns.add(new DtoInstrumentGatherParams("序号", 0));
            List<Map<String, Object>> resultDataList = gatherDataVo.getResultDataList();
            AtomicInteger i = new AtomicInteger(1);
            resultDataList.forEach(p -> {
                p.put("序号", i.getAndIncrement());
            });
            List<ExcelExportEntity> excelExportEntityList = new ArrayList<>(voColumns.size());
            for (DtoInstrumentGatherParams column : voColumns) {
                ExcelExportEntity entity = new ExcelExportEntity();
                entity.setName(column.getParamName());
                entity.setKey(column.getParamName());
                entity.setOrderNum(column.getOrderNum());
                excelExportEntityList.add(entity);
            }
            ExportParams params = new ExportParams();
            params.setStyle(ExcelStyle.class);
            params.setSheetName("结果数据");
            Workbook workbook = ExcelExportUtil.exportExcel(params, excelExportEntityList, resultDataList);
            PoiExcelUtils.downLoadExcel("结果数据", response, workbook);
        }
    }

    @Override
    public void exportLogs(BaseCriteria criteria, HttpServletResponse response) {
        PageBean<DtoInstrumentGatherData> page = new PageBean<>();
        page.setPageNo(1);
        page.setRowsPerPage(Integer.MAX_VALUE);
        // 设置查询的实体类名及别名
        page.setEntityName("DtoInstrumentGatherData x");
        // 设置查询返回的字段、实体别名表示所有字段
        page.setSelect("select x");
        this.findLogsPage(page, criteria);
        List<DtoInstrumentGatherData> logDataList = page.getData();
        if (StringUtil.isNotEmpty(logDataList)) {
            List<DtoInstrumentGatherParams> voColumns = new ArrayList<>();
            // 添加序号列
            voColumns.add(new DtoInstrumentGatherParams("序号", 0));
            voColumns.add(new DtoInstrumentGatherParams("上传时间", 10));
            voColumns.add(new DtoInstrumentGatherParams("采样编号", 20));
            voColumns.add(new DtoInstrumentGatherParams("类型", 30));
            voColumns.add(new DtoInstrumentGatherParams("上传内容", 40));
            List<ExcelExportEntity> excelExportEntityList = new ArrayList<>(voColumns.size());
            for (DtoInstrumentGatherParams column : voColumns) {
                ExcelExportEntity entity = new ExcelExportEntity();
                entity.setName(column.getParamName());
                entity.setKey(column.getParamName());
                entity.setOrderNum(column.getOrderNum());
                excelExportEntityList.add(entity);
            }
            List<Map<String, Object>> exportMapList = new ArrayList<>();
            int i = 1;
            for (DtoInstrumentGatherData gatherData : logDataList) {
                Map<String, Object> map = new HashMap<>();
                map.put("序号", i);
                map.put("采样编号", gatherData.getSampleCode());
                map.put("类型", EnumLIM.EnumInstrumentGatherDataType.getByValue(gatherData.getDataType()));
                map.put("上传时间", gatherData.getUploadTime());
                map.put("上传内容", gatherData.getContent());
                exportMapList.add(map);
                i++;
            }
            ExportParams params = new ExportParams();
            params.setStyle(ExcelStyle.class);
            params.setSheetName("日志数据");
            Workbook workbook = ExcelExportUtil.exportExcel(params, excelExportEntityList, exportMapList);
            PoiExcelUtils.downLoadExcel("日志数据", response, workbook);
        }
    }

    private void getRefreshData(DtoInstrumentGather dtoInstrumentGather) {
        List<DtoInstrumentGatherData> instrumentGatherDataList = repository.findByInstrumentGatherId(dtoInstrumentGather.getId());
        String startTimeTamp = getStartTimeByRedis(instrumentGatherDataList);
        long endTimeTamp = System.currentTimeMillis() / 1000;
        String times = startTimeTamp + "-" + endTimeTamp;
        Map<String, Object> dataMap = new HashMap<>();
        List<NameValuePair> paramList = new ArrayList<>();
        String type = String.format("%s|%s", EnumLIM.EnumInstrumentGatherDataType.工况数据.getValue(), EnumLIM.EnumInstrumentGatherDataType.结果数据.getValue());
        paramList.add(new NameValuePair("type", type));
        paramList.add(new NameValuePair("times", times));
        Map<String, Object> objectMap = remotingRequest(INSTRUMENT_GATHER_RTDATA_URL, paramList);
        if (null != objectMap && objectMap.containsKey("records") && objectMap.get("records") instanceof List<?>) {
            // 远程接口响应结果
            List<Map<String, Object>> list = (List<Map<String, Object>>) objectMap.get("records");
        }
    }

    /**
     * reids 获取开始时间缓存
     *
     * @return 时间戳
     */
    private String getStartTimeByRedis(List<DtoInstrumentGatherData> instrumentGatherDataList) {
        String startTimeTamp = "";
        // 记录第一次刷新的时间作为标识，保存到redis，后续用作条件查询，如果查到数据，redis时间会失效，改为利用保存数据的最新时间
        if (StringUtil.isEmpty(instrumentGatherDataList)) {
            Object object = redisTemplate.opsForValue().get(FIRST_REFRESHTIME_FLAG);
            if (object == null) {
                // 取当前所在时间的 00:00:00
                LocalDate today = LocalDate.now();
                LocalDateTime midnight = today.atStartOfDay();
                Date date = Date.from(midnight.atZone(ZoneId.systemDefault()).toInstant());
                startTimeTamp = String.valueOf(date.getTime() / 1000);
                redisTemplate.opsForValue().set(FIRST_REFRESHTIME_FLAG, startTimeTamp);
            } else {
                startTimeTamp = object.toString();
            }
        } else {
            Optional<DtoInstrumentGatherData> maxDataOptional = instrumentGatherDataList.stream().max(Comparator.comparing(DtoInstrumentGatherData::getUploadTime));
            if (maxDataOptional.isPresent()) {
                startTimeTamp = String.valueOf(maxDataOptional.get().getUploadTime().getTime() / 1000);
                if (redisTemplate.hasKey(FIRST_REFRESHTIME_FLAG)) {
                    // 删除key
                    redisTemplate.delete(FIRST_REFRESHTIME_FLAG);
                }
            }
        }
        return startTimeTamp;
    }


    /**
     * 远程请求
     *
     * @param url       请求地址
     * @param paramList 请求参数
     * @return 返回值a
     */
    private Map<String, Object> remotingRequest(String url, List<NameValuePair> paramList) {
        // 公共参数部分赋值
        paramList.add(new NameValuePair("uscc", INSTRUMENT_GATHER_USCC));
        // 远程请求在线平台获取交互数据
        List<NameValuePair> headerList = new ArrayList();
        headerList.add(new NameValuePair("ddc-token", getToken()));
        headerList.add(new NameValuePair("Content-Type", "application/json"));
        try {
            String json = HTTPCaller.getInstance().getJsonString(url, "", headerList, paramList);
            Map map = JsonUtil.toObject(json, Map.class);
            return map;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("远程请求数据失败！");
        }
    }


    /**
     * 获取远程请求token
     *
     * @return token
     */
    private String getToken() {
        synchronized (lockObject) {
            String tokenKey = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_InstrumentGatherToken.getValue());
            Object tokenObject = redisTemplate.opsForValue().get(tokenKey);
            if (null == tokenObject) {
                long timestamp = System.currentTimeMillis() / 1000;
                // appId 拼接时间戳
                String a = String.format("%s-%s", INSTRUMENT_GATHER_APPID, timestamp);
                // 身份认证信息 base64化的JSON数据，b为空则传‘IiI=’
                String b = "IiI=";
                // md5(a.b.appSecret)
                String c = DivationUtils.toMD5(String.format("%s.%s.%s", a, b, INSTRUMENT_GATHER_APPSECRET));
                String token = String.format("%s.%s.%s", a, b, c);
                redisTemplate.opsForValue().set(tokenKey, token, 30, TimeUnit.MINUTES);
                log.info(" instrument token is {}", token);
                return token;
            } else {
                log.info(" instrument token is {}", tokenObject.toString());
                return tokenObject.toString();
            }
        }
    }


    /**
     * 枚举值替换
     *
     * @param value        原始值
     * @param gatherParams 参数配置
     * @return 替换后的值
     */
    private String replaceEnum(String value, DtoInstrumentGatherParams gatherParams) {
        // 数据值为枚举时，根据配置的数据源解析
        if (gatherParams.getIsEnum() && StringUtil.isNotEmpty(gatherParams.getEnumDataSource())) {
            try {
                Map enumDataSourceMap = JsonUtil.toObject(gatherParams.getEnumDataSource(), Map.class);
                if (enumDataSourceMap.containsKey(value) && StringUtil.isNotNull(enumDataSourceMap.get(value))) {
                    value = enumDataSourceMap.get(value).toString();
                }
            } catch (Exception e) {
                log.error(e.getMessage());
                throw new BaseException("刷新数据失败，无法解析枚举值" + gatherParams.getEnumDataSource());
            }
        }
        return value;
    }


    /**
     * 工况数据部分赋值
     *
     * @param gatherData      工况数据
     * @param detailsList     数据详情
     * @param parameterParams 参数配置
     * @param paramDataList   结果集容器
     */
    private void setGatherData(DtoInstrumentGatherData
                                       gatherData, List<DtoInstrumentGatherDataDetails> detailsList,
                               List<DtoInstrumentGatherParams> parameterParams, List<Map<String, Object>> paramDataList) {
        if (StringUtil.isNotNull(gatherData)) {
            List<DtoInstrumentGatherDataDetails> gatherDataDetailsList = detailsList.stream().filter(p -> gatherData.getId().equals(p.getInstrumentGatherDataId())).collect(Collectors.toList());
            // 遍历工况参数赋值
            for (DtoInstrumentGatherParams parameterParam : parameterParams) {
                Optional<DtoInstrumentGatherDataDetails> detailsOptional = gatherDataDetailsList.stream().filter(p -> parameterParam.getId().equals(p.getInstrumentGatherParamsId())).findFirst();
                if (detailsOptional.isPresent()) {
                    DtoInstrumentGatherDataDetails details = detailsOptional.get();
                    String key = details.getParmaName() + (StringUtil.isNotEmpty(details.getDimension()) ? "(" + parameterParam.getDimension() + ")" : "");
                    Map<String, Object> map = new HashMap<>();

                    map.put("label", key);
                    map.put("value", details.getParamValue());
                    paramDataList.add(map);
                } else {
                    String key = parameterParam.getParamName() + (StringUtil.isNotEmpty(parameterParam.getDimension()) ? "(" + parameterParam.getDimension() + ")" : "");
                    Map<String, Object> map = new HashMap<>();
                    map.put("label", key);
                    map.put("value", "");
                    paramDataList.add(map);
                }
            }
            Map<String, Object> paramDataMap = new HashMap<>();
            paramDataMap.put("label", "上传时间");
            paramDataMap.put("value", DateUtil.dateToString(gatherData.getUploadTime(), DateUtil.FULL));
            paramDataList.add(paramDataMap);
        }
    }

    /**
     * 填充冗余字段
     *
     * @param criteria                 查询参数
     * @param instrumentGatherDataList 数据集合
     */
    private DtoInstrumentGatherDataVo fillingTransientFields(InstrumentGatherDataCriteria criteria, List<DtoInstrumentGatherData> instrumentGatherDataList) {
        DtoInstrumentGatherDataVo result = new DtoInstrumentGatherDataVo();
        // 参数配置数据
        List<DtoInstrumentGatherParams> instrumentGatherParams = instrumentGatherParamsRepository.findByInstrumentGatherId(criteria.getInstrumentGatherId());
        // 筛选结果参数和通道参数
        List<DtoInstrumentGatherParams> resultDataParams = instrumentGatherParams.stream()
                .filter(p -> p.getDataType().equals(EnumLIM.EnumInstrumentGatherDataType.结果数据.getValue()) || p.getDataType().equals(EnumLIM.EnumInstrumentGatherDataType.通道数据.getValue()))
                .sorted(Comparator.comparing(DtoInstrumentGatherParams::getOrderNum).reversed()).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(instrumentGatherDataList)) {
            List<String> gatherDataIds = instrumentGatherDataList.stream().map(DtoInstrumentGatherData::getId).collect(Collectors.toList());
            List<DtoInstrumentGatherDataDetails> gatherDataDetailers = instrumentGatherDataDetailsRepository.findByInstrumentGatherDataIdIn(gatherDataIds);
            Map<String, List<DtoInstrumentGatherDataDetails>> gatherDataDetailsMap = gatherDataDetailers.stream().collect(Collectors.groupingBy(DtoInstrumentGatherDataDetails::getInstrumentGatherDataId));
            List<Map<String, Object>> resultDataList = new ArrayList<>();
            for (DtoInstrumentGatherData gatherData : instrumentGatherDataList) {
                Map<String, Object> map = new HashMap<>();
                map.put("上传时间", DateUtil.dateToString(gatherData.getUploadTime(), DateUtil.FULL));
                List<DtoInstrumentGatherDataDetails> gatherDataDetailsList = gatherDataDetailsMap.getOrDefault(gatherData.getId(), new ArrayList<>());
                // 遍历结果参数赋值
                for (DtoInstrumentGatherParams resultDataParam : resultDataParams) {
                    Optional<DtoInstrumentGatherDataDetails> detailsOptional = gatherDataDetailsList.stream().filter(p -> resultDataParam.getId().equals(p.getInstrumentGatherParamsId())).findFirst();
                    if (detailsOptional.isPresent()) {
                        map.put(detailsOptional.get().getParmaName(), detailsOptional.get().getParamValue());
                    } else {
                        map.put(resultDataParam.getParamName(), "");
                    }
                }
                resultDataList.add(map);
            }
            result.setResultDataList(resultDataList);
        }
        result.setColumns(resultDataParams);
        return result;
    }

    @Autowired
    public void setInstrumentGatherDataDetailsRepository(InstrumentGatherDataDetailsRepository
                                                                 instrumentGatherDataDetailsRepository) {
        this.instrumentGatherDataDetailsRepository = instrumentGatherDataDetailsRepository;
    }

    @Autowired
    public void setInstrumentGatherParamsRepository(InstrumentGatherParamsRepository
                                                            instrumentGatherParamsRepository) {
        this.instrumentGatherParamsRepository = instrumentGatherParamsRepository;
    }

    @Autowired
    public void setRedisTemplate(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
}