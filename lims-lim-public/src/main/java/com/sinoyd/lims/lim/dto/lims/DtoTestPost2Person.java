package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.TestPost2Person;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


/**
 * DtoTestPost2Person实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_TestPost2Person")
 @Data
 @DynamicInsert
 public  class DtoTestPost2Person extends TestPost2Person {
    /**
     * 姓名
     */
    @Transient
    private String cName;

}