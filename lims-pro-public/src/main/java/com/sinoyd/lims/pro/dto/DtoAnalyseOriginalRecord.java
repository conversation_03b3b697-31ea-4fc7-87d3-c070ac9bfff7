package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.AnalyseOriginalRecord;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoAnalyseOriginalRecord实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_AnalyseOriginalRecord")
 @Where(clause = "isDeleted = 0")
 @Data
 @DynamicInsert
 public  class DtoAnalyseOriginalRecord extends AnalyseOriginalRecord {
    private static final long serialVersionUID = 1L;

    /**
     * 样品id
     */
    @Transient
    private String sampleId;

    /**
     * 分析名称
     */
    @Transient
    private String itemName;
}