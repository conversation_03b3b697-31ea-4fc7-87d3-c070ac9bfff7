package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.pro.dto.DtoProject2Contract;
import com.sinoyd.lims.pro.repository.Project2ContractRepository;
import com.sinoyd.lims.pro.service.Project2ContractService;
import org.springframework.stereotype.Service;


/**
 * Project2Contract操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Service
public class Project2ContractServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoProject2Contract,String,Project2ContractRepository> implements Project2ContractService {

    @Override
    public void findByPage(PageBean<DtoProject2Contract> pb, BaseCriteria project2ContractCriteria) {
        pb.setEntityName("DtoProject2Contract a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, project2ContractCriteria);
    }
}