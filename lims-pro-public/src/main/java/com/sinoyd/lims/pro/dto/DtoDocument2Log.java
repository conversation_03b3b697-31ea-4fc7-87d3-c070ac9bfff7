package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.Document2Log;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * DtoDocument2Log实体
 * <AUTHOR>
 * @version V1.0.0 2019/12/12
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_Document2Log")
 @Data
 @DynamicInsert
 public  class DtoDocument2Log extends Document2Log {
   private static final long serialVersionUID = 1L;
 }