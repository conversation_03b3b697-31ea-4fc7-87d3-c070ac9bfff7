CREATE  TRIGGER "AFTER_UPDATE_PARAMSFORMULA"
    AFTER  UPDATE
    ON "TB_LIM_PARAMSFORMULA"
    referencing OLD ROW AS "OLD" NEW ROW AS "NEW"

 for each row

BEGIN

    IF
        new.isDeleted = 1 THEN
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramsformula', new.id, new.modifier, new.modifyDate, 2, null, null, null, new.orgId,
        new.domainId);
else
        if new.objectId != old.objectId or (new.objectId is null and old.objectId is not null) or
           (new.objectId is not null and old.objectId is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                   operateField, oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramsformula', new.id, new.modifier, new.modifyDate, 3, 'objectId', old.objectId,
        new.objectId, new.orgId, new.domainId);
END IF;
if
                new.formula != old.formula or (new.formula is null and old.formula is not null) or
                (new.formula is not null and old.formula is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                   operateField, oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramsformula', new.id, new.modifier, new.modifyDate, 3, 'formula', old.formula,
        new.formula, new.orgId, new.domainId);
END IF;
if
                new.configDate != old.configDate or (new.configDate is null and old.configDate is not null) or
                (new.configDate is not null and old.configDate is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                   operateField, oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramsformula', new.id, new.modifier, new.modifyDate, 3, 'configDate',
        old.configDate, new.configDate, new.orgId, new.domainId);
END IF;
if
                new.orignFormula != old.orignFormula or (new.orignFormula is null and old.orignFormula is not null) or
                (new.orignFormula is not null and old.orignFormula is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                   operateField, oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramsformula', new.id, new.modifier, new.modifyDate, 3, 'orignFormula',
        old.orignFormula, new.orignFormula, new.orgId, new.domainId);
END IF;
if
                new.orignFormulatType != old.orignFormulatType or
                (new.orignFormulatType is null and old.orignFormulatType is not null) or
                (new.orignFormulatType is not null and old.orignFormulatType is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                   operateField, oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramsformula', new.id, new.modifier, new.modifyDate, 3, 'orignFormulatType',
        old.orignFormulatType, new.orignFormulatType, new.orgId, new.domainId);
END IF;
if
                new.objectType != old.objectType or (new.objectType is null and old.objectType is not null) or
                (new.objectType is not null and old.objectType is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                   operateField, oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramsformula', new.id, new.modifier, new.modifyDate, 3, 'objectType',
        old.objectType, new.objectType, new.orgId, new.domainId);
END IF;
if
                new.sampleTypeId != old.sampleTypeId or (new.sampleTypeId is null and old.sampleTypeId is not null) or
                (new.sampleTypeId is not null and old.sampleTypeId is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                   operateField, oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramsformula', new.id, new.modifier, new.modifyDate, 3, 'sampleTypeId',
        old.sampleTypeId, new.sampleTypeId, new.orgId, new.domainId);
END IF;
if
                new.validate != old.validate or (new.validate is null and old.validate is not null) or
                (new.validate is not null and old.validate is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                   operateField, oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramsformula', new.id, new.modifier, new.modifyDate, 3, 'validate', old.validate,
        new.validate, new.orgId, new.domainId);
END IF;
if
                new.usageNum != old.usageNum or (new.usageNum is null and old.usageNum is not null) or
                (new.usageNum is not null and old.usageNum is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                   operateField, oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramsformula', new.id, new.modifier, new.modifyDate, 3, 'usageNum', old.usageNum,
        new.usageNum, new.orgId, new.domainId);
END IF;
END IF;
end;
