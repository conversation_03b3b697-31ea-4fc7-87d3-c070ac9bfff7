package com.sinoyd.lims.pro.configuration;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 *  首页业务流程的模块相关信息
 * <AUTHOR>
 * @version V1.0.0 2020/03/11
 * @since V100R001
 */
@Data
public class ProjectModule {

    /**
     * 模块编码
     */
    private String moduleName;

    /**
     * 模块编号
     */
    private String moduleCode;


    /**
     * 值 0：指按权限缓存，1：按人员缓存，3：即按权限也按任意进行缓存
     */
    private Integer value;


    /**
     * 缓存时间
     */
    private Integer cacheTimeout;


    /**
     * 组合的模块
     */
    private List<String> bindingModules = new ArrayList<>();
}
