package com.sinoyd.base.repository.rcc;

import com.sinoyd.base.dto.rcc.DtoSubstitute;
import com.sinoyd.frame.repository.IBaseJpaRepository;

import java.util.List;


/**
 * Substitute数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
public interface SubstituteRepository extends IBaseJpaRepository<DtoSubstitute, String> {

    /**
     * 按照替代物CasCode查询替代物信息
     *
     * @param casCode   CAS号
     * @param isDeleted 是否删除
     * @return List<DtoSubstitute>
     */
    List<DtoSubstitute> findByCasCodeAndIsDeleted(String casCode, boolean isDeleted);

    /**
     * 按照替代物化合物名称查询替代物信息
     *
     * @param compoundName 化合物名称
     * @param isDeleted    是否删除
     * @return List<DtoSubstitute>
     */
    List<DtoSubstitute> findByCompoundNameAndIsDeleted(String compoundName, boolean isDeleted);

}