package com.sinoyd.lims.lim.service.homeTaskNumber;

import com.sinoyd.lims.lim.configuration.HomeModule;
import com.sinoyd.lims.lim.dto.customer.DtoTaskNum;
import com.sinoyd.lims.lim.enums.EnumLIM;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 首页实验室待检代办数字缓存刷新
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2022/10/26
 */
@Component
public class AuditWorkSheetTask extends AbsTaskNumber{

    /**
     * 刷新卡片的代办数据
     *
     * @param homeModule 模块数据
     */
    @Override
    public void refreshCardNum(HomeModule homeModule, String orgId, String userId) {
        //获取数据库中的数量
        List<DtoTaskNum> taskNumList = this.getTaskNum(homeModule, orgId, userId, new ArrayList<>());
        String redisKey = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_AuditWorkSheetDetail.getValue());
        for (DtoTaskNum taskNum : taskNumList) {
            saveCardInfo(taskNum.getUserId(), redisKey,"auditWorkSheetCount", "auditWorkSheetDetail", taskNum.getCount());
        }
    }

    /**
     * 获取缓存数据集合
     *
     * @param homeModule 模块编码
     * @param orgId      组织Id
     * @param userId     用户Id
     * @param outTypeIds 不包含的项目类型
     * @return 结果集
     */
    @Override
    public List<DtoTaskNum> getTaskNum(HomeModule homeModule, String orgId, String userId, List<String> outTypeIds) {
        //创建查询sql语句
        StringBuilder stringBuilder = new StringBuilder("select b.personId as userId,count(b.id) as count from");
        stringBuilder.append(" (select a.id,a.checkerId as personId from tb_pro_worksheetfolder a where 1=1 and a.workStatus = 8 union");
        stringBuilder.append(" select a.id,a.auditorId as personId from tb_pro_worksheetfolder a where 1=1 and a.workStatus = 24 union");
        stringBuilder.append(" select a.id,a.certificatorId as personId from tb_pro_worksheetfolder a where 1=1 and a.workStatus = 16) b");
        stringBuilder.append(" GROUP BY b.personId");
        //执行sql语句
        return jdbcTemplate.query(stringBuilder.toString(),
                new String[]{},
                (resultSet, i) -> new DtoTaskNum(resultSet.getString("userId"),resultSet.getLong("count")));
    }

    /**
     * 获取模块编码
     *
     * @return 模块编码
     */
    @Override
    public String getModuleCode() {
        return EnumLIM.EnumHomeTaskModule.实验室审核.getValue();
    }
}
