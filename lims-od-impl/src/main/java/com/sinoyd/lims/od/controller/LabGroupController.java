package com.sinoyd.lims.od.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.od.criteria.LabGroupCriteria;
import com.sinoyd.lims.od.dto.DtoLabGroup;
import com.sinoyd.lims.od.service.LabGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * labGroup服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
@Api(tags = "示例:labGroup服务")
@RestController
@RequestMapping("api/ods/labGroup")
@Validated
public class LabGroupController extends BaseJpaController<DtoLabGroup, String, LabGroupService> {

    /**
     * 分页动态条件查询LabGroup
     *
     * @param labGroupCriteria 条件参数
     * @return RestResponse<List < LabGroup>>
     */
    @ApiOperation(value = "分页动态条件查询ReportApply", notes = "分页动态条件查询ReportApply")
    @GetMapping
    public RestResponse<List<DtoLabGroup>> findByPage(LabGroupCriteria labGroupCriteria) {
        PageBean<DtoLabGroup> pageBean = super.getPageBean();
        RestResponse<List<DtoLabGroup>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, labGroupCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id查询", notes = "根据id查询")
    @GetMapping("/{id}")
    public RestResponse<DtoLabGroup> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoLabGroup> restResp = new RestResponse<>();
        DtoLabGroup entity = service.findOne(id);
        restResp.setData(entity);
        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }


    /**
     * 新增
     *
     * @param labGroup 实体
     * @return 新增的实体
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("")
    public RestResponse<DtoLabGroup> create(@Validated @RequestBody DtoLabGroup labGroup) {
        RestResponse<DtoLabGroup> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoLabGroup data = service.save(labGroup);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 更新
     *
     * @param labGroup 实体
     * @return 更新后的实体
     */
    @ApiOperation(value = "更新", notes = "更新")
    @PutMapping("")
    public RestResponse<DtoLabGroup> update(@Validated @RequestBody DtoLabGroup labGroup) {
        RestResponse<DtoLabGroup> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoLabGroup data = service.update(labGroup);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 单个假删
     *
     * @param id 参数id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.logicDeleteById(id);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 批量假删
     *
     * @param ids 参数ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResponse.setCount(count);

        return restResponse;
    }

}
