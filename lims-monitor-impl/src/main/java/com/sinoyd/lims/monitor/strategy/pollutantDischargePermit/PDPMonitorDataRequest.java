package com.sinoyd.lims.monitor.strategy.pollutantDischargePermit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sinoyd.common.http.NameValuePair;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 排污许可证：自行监测数据爬取策略
 *
 * <AUTHOR>
 * @version V5.2.0 2025/04/23
 * @since V100R001
 */
@Service
public class PDPMonitorDataRequest extends AbstractPDPRequest {

    /**
     * 排污许可证数据爬取接口gate-url
     */
    @Value("${pollutant-discharge-permit.monitor-data.gate-url:none}")
    private String gateUrl;


    /**
     * 排污许可证数据爬取接口url
     */
    @Value("${pollutant-discharge-permit.monitor-data.url:none}")
    private String url;

    /**
     * 排污许可证数据爬取接口appSecret
     */
    @Value("${pollutant-discharge-permit.monitor-data.appSecret:none}")
    private String appSecret;

    /**
     * 排污许可证数据爬取接口appId
     */
    @Value("${pollutant-discharge-permit.monitor-data.appId:none}")
    private String appId;

    @Override
    protected void setRequestType(List<NameValuePair> paramList) {
        paramList.add(new NameValuePair("type", "monitorData"));
    }

    @Override
    public String handleResponse(JSONObject jsonObject) {
        //获取到返回结果
        JSONObject data = JSON.parseObject(JSON.toJSONString(jsonObject.get("data")));
        if (data != null) {
            //获取返回的自行监测数据
            JSONObject ariData = JSON.parseObject(JSON.toJSONString(data.get("airData")));
            return StringEscapeUtils.unescapeJava(ariData.getString("自行监测要求"));
        }
        return "";
    }

    @Override
    public String getGateUrl() {
        return gateUrl;
    }

    @Override
    protected String getUrl() {
        return this.url;
    }

    @Override
    protected String getAppId() {
        return this.appId;
    }

    @Override
    protected String getAppSecret() {
        return this.appSecret;
    }

    @Override
    protected String getRedisKey() {
        return "Html:Request:ddc-token";
    }

    /**
     * 初始化请求头参数
     *
     * @return 请求头参数
     */
    @Override
    protected List<NameValuePair> initHeader() {
        List<NameValuePair> headerList = new ArrayList<>();
        headerList.add(new NameValuePair("ddc-token", getDDCToken()));
        headerList.add(new NameValuePair("Accept", " application/json"));
        headerList.add(new NameValuePair("Content-Type", " application/json; charset=utf-8 "));
        headerList.add(new NameValuePair("Accept-Charset", " utf-8"));
        return headerList;
    }
}
