package com.sinoyd.lims.pro.strategy.strategy.schemeSynchronization.sample;

import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTemp;
import com.sinoyd.lims.pro.repository.SampleFolderTemplateRepository;
import com.sinoyd.lims.pro.repository.SamplingFrequencyRepository;
import com.sinoyd.lims.pro.repository.SamplingFrequencyTestTempRepository;
import com.sinoyd.lims.pro.service.SampleFolderService;
import com.sinoyd.lims.pro.service.SchemeService;
import com.sinoyd.lims.pro.service.WorkSheetFolderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class AbsSampleStrategy {

    protected SampleFolderService sampleFolderService;

    protected WorkSheetFolderService workSheetFolderService;

    protected SamplingFrequencyTestTempRepository samplingFrequencyTestTempRepository;

    protected SamplingFrequencyRepository samplingFrequencyRepository;

    protected SampleFolderTemplateRepository sampleFolderTemplateRepository;

    protected SchemeService schemeService;

    /**
     * 调整样品方案
     * @param samplingFrequencyTempList 修改点位内容
     */
    public abstract void synchronizationSample(List<DtoSamplingFrequencyTemp> samplingFrequencyTempList);

    @Autowired
    @Lazy
    public void setSampleFolderService(SampleFolderService sampleFolderService) {
        this.sampleFolderService = sampleFolderService;
    }

    @Autowired
    @Lazy
    public void setWorkSheetFolderService(WorkSheetFolderService workSheetFolderService) {
        this.workSheetFolderService = workSheetFolderService;
    }

    @Autowired
    public void setSamplingFrequencyTestTempRepository(SamplingFrequencyTestTempRepository samplingFrequencyTestTempRepository) {
        this.samplingFrequencyTestTempRepository = samplingFrequencyTestTempRepository;
    }

    @Autowired
    public void setSamplingFrequencyRepository(SamplingFrequencyRepository samplingFrequencyRepository) {
        this.samplingFrequencyRepository = samplingFrequencyRepository;
    }

    @Autowired
    @Lazy
    public void setSchemeService(SchemeService schemeService) {
        this.schemeService = schemeService;
    }

    @Autowired
    public void setSampleFolderTemplateRepository(SampleFolderTemplateRepository sampleFolderTemplateRepository) {
        this.sampleFolderTemplateRepository = sampleFolderTemplateRepository;
    }
}
