package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoReportConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig;

import java.util.Collection;
import java.util.List;


/**
 * 原始记录单数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2020/10/14
 * @since V100R001
 */
public interface RecordConfigRepository extends IBaseJpaRepository<DtoRecordConfig, String> {

    /**
     * 根据配置名称和配置类型查询
     *
     * @param recordName 记录配置id
     * @param recordType 配置类型
     * @return 返回数据
     */
    DtoRecordConfig findByRecordNameAndRecordType(String recordName, int recordType);

    /**
     * 根据模板配置id和配置类型查询
     *
     * @param reportConfigId 记录配置id
     * @param recordType     配置类型
     * @return 返回数据
     */
    DtoRecordConfig findByReportConfigIdAndRecordType(String reportConfigId, int recordType);

    /**
     * 根据模板配置id和配置类型查询
     *
     * @param reportConfigIds 记录配置id列表
     * @param recordType      配置类型
     * @return 返回数据
     */
    List<DtoRecordConfig> findByReportConfigIdInAndRecordType(List<String> reportConfigIds, int recordType);

    /**
     * 通过样品类型获取对应的配置
     *
     * @param sampleTypeIds 样品类型
     * @param ids           ids
     * @return 配置
     */
    List<DtoRecordConfig> findBySampleTypeIdInAndIdIn(List<String> sampleTypeIds, List<String> ids);


    /**
     * 根据报表类型获取数据
     *
     * @param recordType 配置类型
     * @return 返回数据
     */
    List<DtoRecordConfig> findAllByRecordType(int recordType);

    /**
     * 根据模板配置id和配置类型,和检测类型查询
     *
     * @param reportConfigId 记录配置id
     * @param recordType     配置类型
     * @param sampleTypeId   样品类型id
     * @return 返回数据
     */
    DtoRecordConfig findByReportConfigIdAndRecordTypeAndSampleTypeId(String reportConfigId, int recordType, String sampleTypeId);


    /**
     * 根据模板配置id和配置类型,和检测类型查询
     *
     * @param reportConfigId 记录配置id
     * @param recordType     配置类型
     * @param sampleTypeId   样品类型id
     * @return 返回数据
     */
    List<DtoRecordConfig> findByReportConfigIdAndRecordTypeAndSampleTypeIdIn(String reportConfigId, int recordType, Collection<String> sampleTypeId);
}