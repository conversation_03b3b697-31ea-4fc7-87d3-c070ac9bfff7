package com.sinoyd.lims.monitor.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.monitor.service.OicInformation2TestService;
import com.sinoyd.lims.monitor.criteria.OicInformation2TestCriteria;
import com.sinoyd.lims.monitor.dto.lims.DtoOicInformation2Test;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * OicInformation2Test服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
 @Api(tags = "示例: OicInformation2Test服务")
 @RestController
 @RequestMapping("api/monitor/oicInformation2Test")
 public class OicInformation2TestController extends BaseJpaController<DtoOicInformation2Test, String,OicInformation2TestService> {


    /**
     * 分页动态条件查询OicInformation2Test
     * @param oicInformation2TestCriteria 条件参数
     * @return RestResponse<List<OicInformation2Test>>
     */
     @ApiOperation(value = "分页动态条件查询OicInformation2Test", notes = "分页动态条件查询OicInformation2Test")
     @GetMapping
     public RestResponse<List<DtoOicInformation2Test>> findByPage(OicInformation2TestCriteria oicInformation2TestCriteria) {
         PageBean<DtoOicInformation2Test> pageBean = super.getPageBean();
         RestResponse<List<DtoOicInformation2Test>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, oicInformation2TestCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询OicInformation2Test
     * @param id 主键id
     * @return RestResponse<DtoOicInformation2Test>
     */
     @ApiOperation(value = "按主键查询OicInformation2Test", notes = "按主键查询OicInformation2Test")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoOicInformation2Test> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoOicInformation2Test> restResponse = new RestResponse<>();
         DtoOicInformation2Test oicInformation2Test = service.findOne(id);
         restResponse.setData(oicInformation2Test);
         restResponse.setRestStatus(StringUtil.isNull(oicInformation2Test) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增OicInformation2Test
     * @param oicInformation2Test 实体列表
     * @return RestResponse<DtoOicInformation2Test>
     */
     @ApiOperation(value = "新增OicInformation2Test", notes = "新增OicInformation2Test")
     @PostMapping
     public RestResponse<DtoOicInformation2Test> create(@RequestBody DtoOicInformation2Test oicInformation2Test) {
         RestResponse<DtoOicInformation2Test> restResponse = new RestResponse<>();
         restResponse.setData(service.save(oicInformation2Test));
         return restResponse;
      }

     /**
     * 新增OicInformation2Test
     * @param oicInformation2Test 实体列表
     * @return RestResponse<DtoOicInformation2Test>
     */
     @ApiOperation(value = "修改OicInformation2Test", notes = "修改OicInformation2Test")
     @PutMapping
     public RestResponse<DtoOicInformation2Test> update(@RequestBody DtoOicInformation2Test oicInformation2Test) {
         RestResponse<DtoOicInformation2Test> restResponse = new RestResponse<>();
         restResponse.setData(service.update(oicInformation2Test));
         return restResponse;
      }

    /**
     * "根据id批量删除OicInformation2Test
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除OicInformation2Test", notes = "根据id批量删除OicInformation2Test")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }