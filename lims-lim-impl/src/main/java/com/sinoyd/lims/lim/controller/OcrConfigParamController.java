package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.OcrConfigParamCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfigParam;
import com.sinoyd.lims.lim.service.OcrConfigParamService;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * ocr对象参数
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
@RestController
@RequestMapping("api/lim/ocrConfigParam")
@Validated
public class OcrConfigParamController extends BaseJpaController<DtoOcrConfigParam, String, OcrConfigParamService> {

    /**
     * 分页动态条件查询DtoOcrConfigParam
     *
     * @param ocrConfigParamCriteria 条件参数
     * @return RestResponse<List   <   DtoOcrConfigParam>>
     */
    @ApiOperation(value = "分页动态条件查询DtoOcrConfigParam", notes = "分页动态条件查询DtoOcrConfigParam")
    @GetMapping
    public RestResponse<List<DtoOcrConfigParam>> findByPage(OcrConfigParamCriteria ocrConfigParamCriteria) {
        PageBean<DtoOcrConfigParam> pageBean = super.getPageBean();
        RestResponse<List<DtoOcrConfigParam>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, ocrConfigParamCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 新增ocr对象参数
     *
     * @param ocrConfigParam 实体列表
     * @return RestResponse<DtoOcrConfigParam>
     */
    @ApiOperation(value = "ocr对象参数", notes = "ocr对象参数")
    @PostMapping
    public RestResponse<DtoOcrConfigParam> create(@Validated @RequestBody DtoOcrConfigParam ocrConfigParam) {
        RestResponse<DtoOcrConfigParam> restResponse = new RestResponse<>();
        restResponse.setData(service.save(ocrConfigParam));
        return restResponse;
    }

    /**
     * 查询ocr对象参数
     *
     * @param id 标识
     * @return 查询结果
     */
    @ApiOperation(value = "查询ocr对象参数", notes = "查询ocr对象参数")
    @GetMapping("/{id}")
    public RestResponse<DtoOcrConfigParam> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoOcrConfigParam> restResp = new RestResponse<>();
        restResp.setData(service.findOne(id));
        return restResp;
    }

    /**
     * 修改ocr对象参数
     *
     * @param entity ocr对象
     * @return ocr对象
     */
    @ApiOperation(value = "修改ocr对象参数", notes = "修改ocr对象参数")
    @PutMapping
    public RestResponse<DtoOcrConfigParam> update(@Validated @RequestBody DtoOcrConfigParam entity) {
        RestResponse<DtoOcrConfigParam> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.update(entity));
        restResp.setCount(1);
        return restResp;
    }

    /**
     * 批量删除ocr对象参数
     *
     * @param ids 标识列表
     * @return 删除数量
     */
    @ApiOperation(value = "批量删除ocr对象参数", notes = "批量删除ocr对象参数")
    @DeleteMapping
    public RestResponse<Integer> delete(@RequestBody List<String> ids) {
        RestResponse<Integer> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setCount(service.logicDeleteById(ids));
        return restResp;
    }

    /**
     * 参数类型下拉数据源
     *
     * @return 查询结果
     */
    @ApiOperation(value = "参数类型下拉数据源", notes = "参数类型下拉数据源")
    @GetMapping("/paramType")
    public RestResponse<List<Map<String,Object>>> getParamTypeSelectList() {
        RestResponse<List<Map<String,Object>>> restResp = new RestResponse<>();
        restResp.setData(service.getParamTypeSelectList());
        return restResp;
    }
}
