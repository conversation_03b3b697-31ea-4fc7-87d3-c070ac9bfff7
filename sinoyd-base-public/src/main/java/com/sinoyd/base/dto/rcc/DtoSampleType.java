package com.sinoyd.base.dto.rcc;

import com.sinoyd.base.entity.SampleType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


/**
 * DtoSampleType实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_BASE_SampleType")
 @Data
 @DynamicInsert
 public  class DtoSampleType extends SampleType {

	@Transient
	private boolean isLeaf;

    @Transient
    private String industryTypeName;

    @Transient
    private String sampleTypeName;

    /**
     * 是否测试项目提示（用作测试项目，存在”停用”、“作废”、“删除”的测试项目时，进行提示）
     */
    @Transient
    private Boolean isTestTip = false;

    /**
     * 分析项目名称，多个用、隔开
     */
    @Transient
    private String analyzeItemNames;

 }

