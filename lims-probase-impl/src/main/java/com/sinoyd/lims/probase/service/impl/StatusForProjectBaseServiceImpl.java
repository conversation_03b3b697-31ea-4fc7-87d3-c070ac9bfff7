package com.sinoyd.lims.probase.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.ProjectTypeService;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoProjectPlan;
import com.sinoyd.lims.pro.dto.DtoStatusForProject;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.probase.repository.ProjectPlanBaseRepository;
import com.sinoyd.lims.probase.repository.StatusForProjectBaseRepository;
import com.sinoyd.lims.probase.service.StatusForProjectBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Primary
@Service
public class StatusForProjectBaseServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoStatusForProject,String,StatusForProjectBaseRepository> implements StatusForProjectBaseService {

    @Autowired
    private ProjectTypeService projectTypeService;

    @Autowired
    private ProjectPlanBaseRepository projectPlanBaseRepository;

    /**
     * 创建状态
     *
     * @param projectId 项目id
     * @param module    模块编码
     */
    @Transactional
    @Override
    public void createStatus(String projectId, String module) {
        DtoStatusForProject status = new DtoStatusForProject();
        status.setModule(module);
        status.setProjectId(projectId);
        status.setStatus(EnumPRO.EnumStatus.待处理.getValue());
        //currentPersonId 和 nextPersonId 给默认值 2023-03-15
        status.setCurrentPersonId(UUIDHelper.GUID_EMPTY);
        status.setNextPersonId(UUIDHelper.GUID_EMPTY);
        repository.save(status);
    }

    /**
     * 新增状态
     * @param projectId 项目id
     * @param module 模块编码
     * @return 状态信息
     */
    @Override
    public DtoStatusForProject statusForProject(String projectId, String module){
        DtoStatusForProject status = new DtoStatusForProject();
        status.setModule(module);
        status.setProjectId(projectId);
        status.setStatus(EnumPRO.EnumStatus.待处理.getValue());
        status.setCurrentPersonId(UUIDHelper.GUID_EMPTY);
        status.setNextPersonId(UUIDHelper.GUID_EMPTY);
        return status;
    }

    /**
     * 办结项目状态
     *
     * @param project 项目
     */
    @Transactional
    @Override
    public void finishStatus(DtoProject project) {
        //读取该项目的所有状态数据
        List<DtoStatusForProject> statusList = repository.findByProjectId(project.getId());
        //任务置为办结，则所有流程中待处理的状态置为已处理
        for (DtoStatusForProject status : statusList) {
            if (status.getStatus().equals(EnumPRO.EnumStatus.待处理.getValue())) {
                status.setStatus(EnumPRO.EnumStatus.已处理.getValue());
                comRepository.merge(status);
            }
        }
        List<String> existModules = statusList.stream().map(DtoStatusForProject::getModule).collect(Collectors.toList());

        //委托类、例行、全流程需补齐采样准备、委托现场送样、报告管理、任务办结的状态
        //送样类需补齐报告管理、任务办结的状态
        //质控类需补齐数据汇总、结果评价的状态
        List<String> modules = new ArrayList<>();
        String projectTypeCode = projectTypeService.getConfigValue(project.getProjectTypeId(), "projectRegisterPage");
        if (projectTypeCode.equals(EnumPRO.EnumProjectType.委托类.getValue())||
                projectTypeCode.equals(EnumPRO.EnumProjectType.例行类.getValue()) ||
                projectTypeCode.equals(EnumPRO.EnumProjectType.全流程.getValue())) {
            DtoProjectPlan plan = projectPlanBaseRepository.findByProjectId(project.getId());
            if (plan.getIsMakePlan()) {
                modules = Arrays.asList(EnumLIM.EnumProjectModule.报告管理.getCode(), EnumLIM.EnumProjectModule.任务办结.getCode(), EnumLIM.EnumProjectModule.采样准备.getCode());
            } else {
                modules = Arrays.asList(EnumLIM.EnumProjectModule.报告管理.getCode(), EnumLIM.EnumProjectModule.任务办结.getCode(), EnumLIM.EnumProjectModule.委托现场送样.getCode());
            }
        } else if (projectTypeCode.equals(EnumPRO.EnumProjectType.送样类.getValue()) || projectTypeCode.equals(EnumPRO.EnumProjectType.现场类.getValue())) {
            modules = Arrays.asList(EnumLIM.EnumProjectModule.报告管理.getCode(), EnumLIM.EnumProjectModule.任务办结.getCode());
        } else if (projectTypeCode.equals(EnumPRO.EnumProjectType.质控类.getValue())) {
            modules = Arrays.asList(EnumLIM.EnumQCProjectModule.数据汇总.getCode(), EnumLIM.EnumQCProjectModule.评价结果.getCode());
        }

        for (String module : modules.stream().filter(p -> !existModules.contains(p)).collect(Collectors.toList())) {
            DtoStatusForProject status = new DtoStatusForProject();
            status.setModule(module);
            status.setProjectId(project.getId());
            status.setStatus(EnumPRO.EnumStatus.已处理.getValue());
            repository.save(status);
        }
    }

    /**
     * 核对项目状态
     *
     * @param project 项目
     */
    @Transactional
    @Override
    public void checkStatus(DtoProject project) {
        //读取该项目的所有状态数据并转为map
        List<DtoStatusForProject> statusList = repository.findByProjectId(project.getId());
        Map<String, DtoStatusForProject> statusMap = statusList.stream().collect(Collectors.toMap(DtoStatusForProject::getModule, status -> status));
        List<String> modules = new ArrayList<>();
        String projectTypeCode = projectTypeService.getConfigValue(project.getProjectTypeId(), "projectRegisterPage");
        DtoProjectPlan plan = projectPlanBaseRepository.findByProjectId(project.getId());
        if (projectTypeCode.equals(EnumPRO.EnumProjectType.委托类.getValue())
                || projectTypeCode.equals(EnumPRO.EnumProjectType.例行类.getValue())
                || projectTypeCode.equals(EnumPRO.EnumProjectType.全流程.getValue())) {
            if (plan.getIsMakePlan()) {
                modules = Arrays.stream(EnumLIM.EnumProjectModule.values()).map(EnumLIM.EnumProjectModule::getCode).collect(Collectors.toList());
                modules.remove(EnumLIM.EnumProjectModule.委托现场送样.getCode());
            } else {
                modules = Arrays.stream(EnumLIM.EnumProjectModule.values()).map(EnumLIM.EnumProjectModule::getCode).collect(Collectors.toList());
                modules.remove(EnumLIM.EnumProjectModule.采样准备.getCode());
            }
        } else if (projectTypeCode.equals(EnumPRO.EnumProjectType.送样类.getValue())) {
            modules = Arrays.stream(EnumLIM.EnumProjectModule.values()).map(EnumLIM.EnumProjectModule::getCode).collect(Collectors.toList());
            modules.remove(EnumLIM.EnumProjectModule.委托现场送样.getCode());
            modules.remove(EnumLIM.EnumProjectModule.采样准备.getCode());
        } else if (projectTypeCode.equals(EnumPRO.EnumProjectType.现场类.getValue())) {
            modules = Arrays.asList(EnumLIM.EnumProjectModule.现场任务.getCode(), EnumLIM.EnumProjectModule.报告管理.getCode(), EnumLIM.EnumProjectModule.任务办结.getCode());
        } else if (projectTypeCode.equals(EnumPRO.EnumProjectType.质控类.getValue())) {
            modules = Arrays.asList(EnumLIM.EnumQCProjectModule.项目登记.getCode(), EnumLIM.EnumQCProjectModule.数据汇总.getCode(), EnumLIM.EnumQCProjectModule.评价结果.getCode());
        }
        if (modules.contains(EnumLIM.EnumProjectModule.项目登记.getCode())) {
            Integer status = this.getRegisterStatus(project);
            DtoStatusForProject statusForProject = statusMap.getOrDefault(EnumLIM.EnumProjectModule.项目登记.getCode(), null);
            this.checkStatus(project.getId(), EnumLIM.EnumProjectModule.项目登记.getCode(), statusForProject, status);
        }
        if (modules.contains(EnumLIM.EnumProjectModule.技术审核.getCode()) && statusMap.containsKey(EnumLIM.EnumProjectModule.技术审核.getCode())) {
            Integer status = this.getModuleStatus(project, EnumPRO.EnumProjectStatus.技术审核中);
            DtoStatusForProject statusForProject = statusMap.get(EnumLIM.EnumProjectModule.技术审核.getCode());
            this.checkStatus(project.getId(), EnumLIM.EnumProjectModule.技术审核.getCode(), statusForProject, status);
        }
        if (modules.contains(EnumLIM.EnumProjectModule.项目下达.getCode()) && statusMap.containsKey(EnumLIM.EnumProjectModule.项目下达.getCode())) {
            Integer status = this.getModuleStatus(project, EnumPRO.EnumProjectStatus.项目下达中);
            DtoStatusForProject statusForProject = statusMap.get(EnumLIM.EnumProjectModule.项目下达.getCode());
            this.checkStatus(project.getId(), EnumLIM.EnumProjectModule.项目下达.getCode(), statusForProject, status);
        }
        if (modules.contains(EnumLIM.EnumProjectModule.采样准备.getCode())) {
            Integer status = this.getPrepareStatus(project, plan);
            DtoStatusForProject statusForProject = statusMap.getOrDefault(EnumLIM.EnumProjectModule.采样准备.getCode(), null);
            this.checkStatus(project.getId(), EnumLIM.EnumProjectModule.采样准备.getCode(), statusForProject, status);
        }
        if (modules.contains(EnumLIM.EnumProjectModule.委托现场送样.getCode())) {
            Integer status = this.getLocalSendStatus(project, plan);
            DtoStatusForProject statusForProject = statusMap.getOrDefault(EnumLIM.EnumProjectModule.委托现场送样.getCode(), null);
            this.checkStatus(project.getId(), EnumLIM.EnumProjectModule.委托现场送样.getCode(), statusForProject, status);
        }
        if (modules.contains(EnumLIM.EnumProjectModule.现场任务.getCode())) {
            Integer status = this.getModuleStatus(project, EnumPRO.EnumProjectStatus.项目登记中);
            DtoStatusForProject statusForProject = statusMap.getOrDefault(EnumLIM.EnumProjectModule.现场任务.getCode(), null);
            this.checkStatus(project.getId(), EnumLIM.EnumProjectModule.现场任务.getCode(), statusForProject, status);
        }
        if (modules.contains(EnumLIM.EnumProjectModule.报告管理.getCode())) {
            Integer status = this.getReportStatus(project);
            DtoStatusForProject statusForProject = statusMap.getOrDefault(EnumLIM.EnumProjectModule.报告管理.getCode(), null);
            this.checkStatus(project.getId(), EnumLIM.EnumProjectModule.报告管理.getCode(), statusForProject, status);
        }
        if (modules.contains(EnumLIM.EnumProjectModule.任务办结.getCode())) {
            Integer status = this.getProjectEndStatus(project);
            DtoStatusForProject statusForProject = statusMap.getOrDefault(EnumLIM.EnumProjectModule.任务办结.getCode(), null);
            this.checkStatus(project.getId(), EnumLIM.EnumProjectModule.任务办结.getCode(), statusForProject, status);
        }
        if (modules.contains(EnumLIM.EnumQCProjectModule.项目登记.getCode())) {
            Integer status = this.getModuleStatus(project, EnumPRO.EnumProjectStatus.项目登记中);
            DtoStatusForProject statusForProject = statusMap.getOrDefault(EnumLIM.EnumQCProjectModule.项目登记.getCode(), null);
            this.checkStatus(project.getId(), EnumLIM.EnumQCProjectModule.项目登记.getCode(), statusForProject, status);
        }
        if (modules.contains(EnumLIM.EnumQCProjectModule.数据汇总.getCode())) {
            Integer status = this.getModuleStatus(project, EnumPRO.EnumProjectStatus.数据汇总中);
            DtoStatusForProject statusForProject = statusMap.getOrDefault(EnumLIM.EnumQCProjectModule.数据汇总.getCode(), null);
            this.checkStatus(project.getId(), EnumLIM.EnumQCProjectModule.数据汇总.getCode(), statusForProject, status);
        }
        if (modules.contains(EnumLIM.EnumQCProjectModule.评价结果.getCode())) {
            Integer status = this.getModuleStatus(project, EnumPRO.EnumProjectStatus.结果评价中);
            DtoStatusForProject statusForProject = statusMap.getOrDefault(EnumLIM.EnumQCProjectModule.评价结果.getCode(), null);
            this.checkStatus(project.getId(), EnumLIM.EnumQCProjectModule.评价结果.getCode(), statusForProject, status);
        }
    }

    /**
     *
     * @param projectId 项目id
     * @param module 模块
     * @param statusForProject 项目状态
     * @param status 待处理状态
     */
    private void checkStatus(String projectId,String module,DtoStatusForProject statusForProject,Integer status) {
        if (StringUtil.isNotNull(statusForProject)) {
            if (StringUtil.isNotNull(status) && !statusForProject.getStatus().equals(status)) {
                statusForProject.setStatus(status);
                comRepository.merge(statusForProject);
            } else if (StringUtil.isNull(status)) {
                repository.delete(statusForProject);
            }
        } else if (StringUtil.isNotNull(status)) {
            statusForProject = new DtoStatusForProject();
            statusForProject.setProjectId(projectId);
            statusForProject.setStatus(status);
            statusForProject.setModule(module);
            repository.save(statusForProject);
        }
    }

    /**
     * 获取通用待处理状态
     *
     * @param project 项目实体
     * @param projectStatus 比较的状态
     */
    @Override
    public Integer getModuleStatus(DtoProject project,EnumPRO.EnumProjectStatus projectStatus) {
        EnumPRO.EnumProjectStatus status = EnumPRO.EnumProjectStatus.getByName(project.getStatus());
        if (StringUtil.isNotNull(status)) {
            return status.getValue() < projectStatus.getValue() ? null : status.getValue().equals(projectStatus.getValue()) ? EnumPRO.EnumStatus.待处理.getValue() : EnumPRO.EnumStatus.已处理.getValue();
        }
        return null;
    }

    /**
     * 获取项目登记待处理状态
     *
     * @param project 项目实体
     */
    @Override
    public Integer getRegisterStatus(DtoProject project) {
        EnumPRO.EnumProjectStatus status = EnumPRO.EnumProjectStatus.getByName(project.getStatus());
        if (StringUtil.isNotNull(status)) {
            return project.getStatus().equals(EnumPRO.EnumProjectStatus.项目登记中.toString()) || project.getStatus().equals(EnumPRO.EnumProjectStatus.审核未通过.toString()) ?
                    EnumPRO.EnumStatus.待处理.getValue() : EnumPRO.EnumStatus.已处理.getValue();
        }
        return null;
    }

    /**
     * 获取采样准备待处理状态
     *
     * @param project 项目实体
     * @param plan 项目计划实体
     */
    @Override
    public Integer getPrepareStatus(DtoProject project, DtoProjectPlan plan) {
        EnumPRO.EnumProjectStatus status = EnumPRO.EnumProjectStatus.getByName(project.getStatus());
        //默认不为编制方案，即默认不存在状态
        Boolean isMakePlan = StringUtil.isNull(plan) ? false : plan.getIsMakePlan();
        String projectTypeCode = projectTypeService.getConfigValue(project.getProjectTypeId(), "projectRegisterPage");
        if (StringUtil.isNotNull(status) && isMakePlan
                && (projectTypeCode.equals(EnumPRO.EnumProjectType.委托类.getValue()) ||
                projectTypeCode.equals(EnumPRO.EnumProjectType.例行类.getValue()) ||
                projectTypeCode.equals(EnumPRO.EnumProjectType.全流程.getValue()))) {
            if (project.getSamplingStatus().equals(EnumPRO.EnumSampledStatus.未采毕.getValue()) && project.getStatus().equals(EnumPRO.EnumProjectStatus.开展中.toString())) {
                return EnumPRO.EnumStatus.待处理.getValue();
            } else {
                assert status != null;
                if (status.getValue() >= EnumPRO.EnumProjectStatus.开展中.getValue()) {
                    return EnumPRO.EnumStatus.已处理.getValue();
                }
            }
        }
        return null;
    }

    /**
     * 获取委托现场送样待处理状态
     *
     * @param project 项目实体
     * @param plan 项目计划实体
     */
    @Override
    public Integer getLocalSendStatus(DtoProject project, DtoProjectPlan plan) {
        EnumPRO.EnumProjectStatus status = EnumPRO.EnumProjectStatus.getByName(project.getStatus());
        //默认为编制方案，即默认不存在状态
        Boolean isMakePlan = StringUtil.isNull(plan) ? true : plan.getIsMakePlan();
        String projectTypeCode = projectTypeService.getConfigValue(project.getProjectTypeId(), "projectRegisterPage");
        if (StringUtil.isNotNull(status) && !isMakePlan && (projectTypeCode.equals(EnumPRO.EnumProjectType.委托类.getValue())
                || projectTypeCode.equals(EnumPRO.EnumProjectType.例行类.getValue())
                || projectTypeCode.equals(EnumPRO.EnumProjectType.全流程.getValue()))) {
            assert status != null;
            if (project.getSamplingStatus().equals(EnumPRO.EnumSampledStatus.未采毕.getValue()) && status.getValue() >= EnumPRO.EnumProjectStatus.开展中.getValue() && status.getValue() < EnumPRO.EnumProjectStatus.已办结.getValue()) {
                return EnumPRO.EnumStatus.待处理.getValue();
            } else if (project.getSamplingStatus().equals(EnumPRO.EnumSampledStatus.已采毕.getValue()) || status.getValue().equals(EnumPRO.EnumProjectStatus.已办结.getValue())) {
                return EnumPRO.EnumStatus.已处理.getValue();
            }
        }
        return null;
    }

    /**
     * 获取编制报告待处理状态
     *
     * @param project 项目实体
     */
    @Override
    public Integer getReportStatus(DtoProject project) {
        EnumPRO.EnumProjectStatus status = EnumPRO.EnumProjectStatus.getByName(project.getStatus());
        String projectTypeCode = projectTypeService.getConfigValue(project.getProjectTypeId(), "projectRegisterPage");
        assert status != null;
        if (StringUtil.isNotNull(status) && status.getValue() >= EnumPRO.EnumProjectStatus.开展中.getValue() && !projectTypeCode.equals(EnumPRO.EnumProjectType.质控类.getValue())) {
            return project.getReportStatus().equals(EnumPRO.EnumReportStatus.未完成.getValue()) && status.getValue() < EnumPRO.EnumProjectStatus.已办结.getValue() ? EnumPRO.EnumStatus.待处理.getValue() :
                    EnumPRO.EnumStatus.已处理.getValue();
        }
        return null;
    }

    /**
     * 获取项目办结待处理状态
     *
     * @param project 项目实体
     */
    @Override
    public Integer getProjectEndStatus(DtoProject project) {
        EnumPRO.EnumProjectStatus status = EnumPRO.EnumProjectStatus.getByName(project.getStatus());
        String projectTypeCode = projectTypeService.getConfigValue(project.getProjectTypeId(), "projectRegisterPage");
        if (StringUtil.isNotNull(status)) {
            if (projectTypeCode.equals(EnumPRO.EnumProjectType.质控类.getValue())) {
                return status.getValue().equals(EnumPRO.EnumProjectStatus.已办结.getValue()) ? EnumPRO.EnumStatus.已处理.getValue() :
                        (status.getValue().equals(EnumPRO.EnumProjectStatus.结果评价中.getValue()) ? EnumPRO.EnumStatus.待处理.getValue() : null);
            } else {
                return status.getValue().equals(EnumPRO.EnumProjectStatus.已办结.getValue()) ? EnumPRO.EnumStatus.已处理.getValue() :
                        (status.getValue() >= EnumPRO.EnumProjectStatus.开展中.getValue() ? EnumPRO.EnumStatus.待处理.getValue() : null);
            }
        }
        return null;
    }
}
