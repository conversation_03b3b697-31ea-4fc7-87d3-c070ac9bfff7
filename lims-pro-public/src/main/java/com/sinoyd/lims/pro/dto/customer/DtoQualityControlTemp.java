package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.lims.lim.dto.customer.DtoTestFormulaParamsConfig;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 室内质控数据的创建
 * <AUTHOR>
 * @version V1.0.0 2019/12/02
 * @since V100R001
 */
@Data
public class DtoQualityControlTemp {

    /**
     * 质控等级
     */
    private Integer qcGrade;

    /**
     * 质控类型
     */
    private Integer qcType;


    /**
     * 相应的分析数据
     */
    private List<Map<String, Object>> analyseData = new ArrayList<>();


    /**
     * 公式相关参数
     */
    private List<DtoTestFormulaParamsConfig> paramsConfig = new ArrayList<>();


    /**
     * 质控的值（加入标准量）
     */
    @Length(message = "质控值（标准样的值/加标的值/平行样为空）{validation.message.length}", max = 50)
    private String qcValue;

    /**
     * 替代样加入量
     */
    private String addition;

    /**
     * 替代物加入量
     */
    private String casCode;

    /**
     * 替代物化合物名称
     */
    private String compoundName;

    /**
     * 质控的编号
     */
    @Length(message = "标样编号{validation.message.length}", max = 50)
    private String qcCode;


    /**
     * 加标体积
     */
    @Length(message = "加标体积{validation.message.length}", max = 50)
    private String qcVolume;


    /**
     * 有效位数
     */
    private Integer mostSignificance;

    /**
     * 小数位数
     */
    private Integer mostDecimal;


    /**
     * 量纲id
     */
    private String dimensionId;

    /**
     * 量纲名称
     */
    private String dimension;

    /**
     * 添加次数
     */
    private Integer copyTimes;


    /**
     * 样品分类
     */
    private Integer sampleCategory;


    /**
     * 检测单id
     */
    private String workSheetFolderId;


    /**
     * 有效期
     */
    private Date qcValidDate;


    /**
     * 质控id
     */
    private String qcId;


    /**
     * 数据id
     */
    private String anaId;


    /**
     * 公式id
     */
    private String formulaId;

    /**
     * 是否重新计算
     */
    private Boolean isCalculate = false;


    /**
     * 样值
     */
    @Length(message = "样值（可以是质量的也可以是浓度的）{validation.message.length}", max = 50)
    private String realSampleTestValue;


    /**
     * 测定值
     */
    @Length(message = "测定值{validation.message.length}", max = 50)
    private String qcTestValue;

    /**
     * 质控样别名
     */
    private String qcAliasName;

    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 检出限
     */
    private String examLimitValue;

    /**
     * 替代样出证结果
     */
    private String testValue;

    /**
     * 回收率
     */
    private String qcRecoverRate;

    /**
     * 标样的配置日期
     */
    private Date qcStandardDate;

    /**
     * 标样id
     */
    private String qcStandardId;

    /**
     * 加标液浓度
     */
    @Length(message = "加标液浓度{validation.message.length}", max = 50)
    private String qcConcentration;

    /**
     * 加标体积/标准溶液加入体积量纲id
     */
    private String qcVolumeDimensionId;

    /**
     * 加入标准量/标准物质加入量/替代物加入量量纲id
     */
    private String qcValueDimensionId;

    /**
     * 测定值量纲id
     */
    private String qcTestValueDimensionId;

    /**
     * 样值量纲id
     */
    private String realSampleTestValueDimensionId;

    /**
     * 标准溶液浓度
     */
    @Length(message = "标准溶液浓度{validation.message.length}", max = 50)
    private String ssConcentration;

    /**
     * 定容体积
     */
    @Length(message = "定容体积{validation.message.length}", max = 50)
    private String constantVolume;

    /**
     * 标准溶液浓度量纲标识
     */
    private String ssConcentrationDimensionId;

    /**
     * 定容体积量纲标识
     */
    private String constantVolumeDimensionId;

    /**
     * remark
     */
    @Length(message = "备注{validation.message.length}", max = 500)
    private String remark;

    /**
     * 不确定度类型
     */
    private Integer uncertainType;

    /**
     * 范围低点
     */
    private String rangeLow;

    /**
     * 范围高点
     */
    private String rangeHigh;

    /**
     * 稀释水是否接种
     */
    private Integer isVaccinate = 1;
}
