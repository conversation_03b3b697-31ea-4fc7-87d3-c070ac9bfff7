package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.*;

/**
 * 项目查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/02
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectApprovalCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请开始时间
     */
    private String startTime;

    /**
     * 申请结束时间
     */
    private String endTime;

    /**
     * 项目类型id
     */
    private String projectTypeId;

    /**
     * 委托方
     */
    private String customerName;

    /**
     * 受检方
     */
    private String inspectedEnt;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目负责人
     */
    private String leaderId;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 审核人id
     */
    private String approvePersonId;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and p.id = a.projectId");
        Calendar calendar = Calendar.getInstance();
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date date = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and a.approveDate >= :startTime");
            values.put("startTime", date);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date date = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            date = calendar.getTime();
            condition.append(" and a.approveDate < :endTime");
            values.put("endTime", date);
        }
        if (StringUtil.isNotEmpty(this.projectTypeId)) {
            condition.append(" and p.projectTypeId = :projectTypeId");
            values.put("projectTypeId", this.projectTypeId);
        }
        if (StringUtil.isNotEmpty(this.customerName)) {
            condition.append(" and (p.customerName like :customerName or p.customerAddress like :customerName)");
            values.put("customerName", "%" + this.customerName + "%");
        }
        if (StringUtil.isNotEmpty(this.inspectedEnt)) {
            condition.append(" and (p.inspectedEnt like :inspectedEnt or p.inspectedAddress like :inspectedEnt)");
            values.put("inspectedEnt", "%" + this.inspectedEnt + "%");
        }
        if (StringUtil.isNotEmpty(this.projectName)) {
            condition.append(" and p.projectName like :projectName");
            values.put("projectName", "%" + this.projectName + "%");
        }
        if (StringUtil.isNotEmpty(this.leaderId)) {
            condition.append(" and exists(select 1 from DtoProjectPlan pl where p.id = pl.projectId and pl.leaderId = :leaderId)");
            values.put("leaderId", this.leaderId);
        }
        if (StringUtil.isNotEmpty(this.approvePersonId)) {
            condition.append(" and a.approvePersonId = :approvePersonId");
            values.put("approvePersonId", this.approvePersonId);
        }
        if (StringUtil.isNotNull(this.status)) {
            List<String> approveStatus = new ArrayList<>();
            condition.append(" and a.modifyStatus in :approveStatus");
            if (this.status.equals(0)) {
                if (StringUtil.isNotEmpty(this.approvePersonId)) {
                    approveStatus = Arrays.asList(EnumLIM.EnumPojectApproveStatus.审核中.name());
                } else {
                    approveStatus = Arrays.asList(EnumLIM.EnumPojectApproveStatus.登记中.name(), EnumLIM.EnumPojectApproveStatus.审核不通过.name());
                }
            } else if (this.status.equals(1)) {
                if (StringUtil.isNotEmpty(this.approvePersonId)) {
                    approveStatus = Arrays.asList(EnumLIM.EnumPojectApproveStatus.审核不通过.name(), EnumLIM.EnumPojectApproveStatus.已完成.name());
                } else {
                    approveStatus = Arrays.asList(EnumLIM.EnumPojectApproveStatus.审核中.name(), EnumLIM.EnumPojectApproveStatus.已完成.name());
                }

            }
            values.put("approveStatus", approveStatus);
        } else {
            if (StringUtil.isNotEmpty(this.approvePersonId)) {
                condition.append(" and a.modifyStatus != :approveStatus");
                values.put("approveStatus", EnumLIM.EnumPojectApproveStatus.登记中.name());
            }
        }
        return condition.toString();
    }
}
