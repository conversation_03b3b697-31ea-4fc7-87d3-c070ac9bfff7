package com.sinoyd.base.constants;

import java.util.regex.Pattern;

/**
 * Base模块常量
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2021/09/03
 */
public interface IBaseConstants {
    /**
     * 仪器解析数据源UNIT
     */
    String INSTRUMENT_PARSE_PERSISTENCE_UNIT = "instrumentParseDataSourceUnit";

    /**
     * 正则表达式定义
     */
    interface RegExpression {
        /**
         * 是否数值
         */
        Pattern NUMBER_PATTERN = Pattern.compile("^-?\\d+(\\.\\d+)?$");
    }

    /**
     * 质控限值管理偏差公式配置标记符
     */
    String DEVIATION_FORMULA = "Deviation_Formula";

    /**
     * 在线值集合
     */
    String ONLINE_DATA_LIST = "OnlineDataList";

    /**
     * 实验室集合
     */
    String LABORATORY_DATA_LIST = "laboratoryDataList";
}
