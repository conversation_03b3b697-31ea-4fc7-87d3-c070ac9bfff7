package com.sinoyd.lims.lim.verify;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.dto.lims.DtoConsumableOfMixed;
import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.customer.DtoImportConsumableOfMixed;
import com.sinoyd.lims.lim.utils.ImportUtils;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 标准样品混标导入数据校验
 *
 * <AUTHOR>
 * @version V1.0.0 2022/7/11
 * @since V100R001
 */
@Data
public class ConsumableOfMixedVerifyHandler implements IExcelVerifyHandler<DtoImportConsumableOfMixed> {

    /**
     * 系统中所有的分析因子
     */
    private final List<DtoAnalyzeItem> dbAnaItem;

    /**
     * 系统重所选标样的混标数据
     */
    private final List<DtoConsumableOfMixed> dbData;

    /**
     * 构造函数
     *
     * @param dbAnaItem 系统中所有的分析因子
     * @param dbData    系统重所选标样的混标数据
     */
    public ConsumableOfMixedVerifyHandler(List<DtoAnalyzeItem> dbAnaItem, List<DtoConsumableOfMixed> dbData) {
        this.dbAnaItem = dbAnaItem;
        this.dbData = dbData;
    }

    /**
     * 工具类
     */
    private final ImportUtils importUtils = new ImportUtils();

    @Override
    public ExcelVerifyHandlerResult verifyHandler(DtoImportConsumableOfMixed dto) {
        //导入数据处理
        try {
            //跳过空行
            if (importUtils.checkObjectIsNull(dto)) {
                return new ExcelVerifyHandlerResult(true);
            }
            //前后去空格
            importUtils.strToTrim(dto);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }
        //校验结果
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        //校验错误信息
        StringBuilder failStr = new StringBuilder("第" + dto.getRowNum() + "行数据校验错误");
        //必填项判断
        importUtils.checkIsNull(result, dto.getAnalyzeItemName(), "分析项目", failStr);
        importUtils.checkIsNull(result, dto.getDimensionName(), "计量单位", failStr);
        importUtils.checkIsNull(result, dto.getConcentration(), "浓度", failStr);
        importUtils.checkIsNull(result, dto.getUncertainty(), "不确定度", failStr);
        //判断分析项目是否存在
        isExistAnaItem(dto, result, failStr);
        //判断数据在系统中是否存在
        isExistData(dto, result, failStr);
        String failString = failStr.toString().replaceFirst("；", ":");
        result.setMsg(failString);

        return result;
    }

    /**
     * 判断系统中是否有数据
     *
     * @param dto     需要导入的混标数据
     * @param result  校验结果
     * @param failStr 校验结果显示
     */
    private void isExistData(DtoImportConsumableOfMixed dto, ExcelVerifyHandlerResult result,
                             StringBuilder failStr) {
        if (StringUtil.isEmpty(dbData)) {
            return;
        }
        if (StringUtil.isNotEmpty(dto.getAnalyzeItemName())) {
            List<DtoConsumableOfMixed> isExistAnaItem = dbData.stream()
                    .filter(p -> dto.getAnalyzeItemName().equals(p.getAnalyzeItemName()))
                    .collect(Collectors.toList());
            if (StringUtil.isNotEmpty(isExistAnaItem)) {
                result.setSuccess(false);
                failStr.append("；此分析项目混标已存在");
            }
        }
    }

    /**
     * 判断分析项目是否存在
     *
     * @param dto     导入数据
     * @param result  校验结果
     * @param failStr 校验错误信息
     */
    private void isExistAnaItem(DtoImportConsumableOfMixed dto, ExcelVerifyHandlerResult result,
                                StringBuilder failStr) {
        if (StringUtil.isNotEmpty(dto.getAnalyzeItemName())) {
            List<DtoAnalyzeItem> isExistAnaItem = dbAnaItem.stream()
                    .filter(p -> dto.getAnalyzeItemName().equals(p.getAnalyzeItemName()))
                    .collect(Collectors.toList());
            if (StringUtil.isEmpty(isExistAnaItem)) {
                result.setSuccess(false);
                failStr.append("；分析项目在系统中不存在");
            }
        }
    }
}
