package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.DtoFolderSign;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

import java.util.Collection;
import java.util.List;


/**
 * FolderSign数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/7/7
 * @since V100R001
 */
public interface FolderSignRepository extends IBaseJpaPhysicalDeleteRepository<DtoFolderSign, String> {

    /**
     * 根据点位和周期获取签到信息
     *
     * @param folderId   点位id
     * @param cycleOrder 周期
     * @return 点位签到信息
     */
    List<DtoFolderSign> findBySampleFolderIdAndCycleOrder(String folderId, Integer cycleOrder);

    List<DtoFolderSign> findBySampleFolderIdIn(Collection<String> sampleFolderIds);

}