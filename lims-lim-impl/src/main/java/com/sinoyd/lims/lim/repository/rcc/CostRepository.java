package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.lims.lim.dto.rcc.DtoCost;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

import java.util.Collection;
import java.util.List;


/**
 * Cost数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/9
 * @since V100R001
 */
public interface CostRepository extends IBaseJpaPhysicalDeleteRepository<DtoCost, String> {

    /**
     * 根据检测类型返回检测费配置
     *
     * @param sampleTypeId 检测类型id
     * @return 根据检测类型返回检测费配置
     */
    List<DtoCost> findBySampleTypeId(String sampleTypeId);

    /**
     * 根据测试项目id集合返回检测费配置
     *
     * @param testIds 测试项目id集合
     * @return 返回测试项目id集合的检测费配置
     */
    List<DtoCost> findByTestIdIn(Collection<String> testIds);
}