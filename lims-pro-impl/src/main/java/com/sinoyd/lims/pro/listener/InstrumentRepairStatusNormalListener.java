package com.sinoyd.lims.pro.listener;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.lims.lim.dto.lims.DtoOAInstrumentRepairApply;
import com.sinoyd.lims.lim.service.OAInstrumentRepairApplyService;
import com.sinoyd.lims.pro.dto.DtoOATaskRelation;
import com.sinoyd.lims.pro.service.OATaskRelationService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 仪器维修状态恢复正常监听通知
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-02
 * @since V100R001
 */
@Component
@Slf4j
public class InstrumentRepairStatusNormalListener implements ExecutionListener {

    private static final long serialVersionUID = 1L;

    @Override
    public void notify(DelegateExecution delegateExecution) {
        OATaskRelationService taskRelationService = SpringContextAware.getBean(OATaskRelationService.class);
        List<DtoOATaskRelation> relations = taskRelationService.findListByTaskId(delegateExecution.getProcessBusinessKey());
        List<String> instrumentRepairRecordIds = relations.stream().map(DtoOATaskRelation::getObjectId).distinct().collect(Collectors.toList());
        OAInstrumentRepairApplyService oaInstrumentRepairApplyService = SpringContextAware.getBean(OAInstrumentRepairApplyService.class);
        CommonRepository commonRepository=SpringContextAware.getBean(CommonRepository.class);
        if (instrumentRepairRecordIds.size() > 0) {
            List<DtoOAInstrumentRepairApply> oaInstrumentRepairApplies = oaInstrumentRepairApplyService.findAll(instrumentRepairRecordIds);
            List<String> instrumentIds = oaInstrumentRepairApplies.stream().map(DtoOAInstrumentRepairApply::getInstrumentId).distinct().collect(Collectors.toList());
            for (String id : instrumentIds) {
                DtoInstrument dtoInstrument = new DtoInstrument();
                dtoInstrument.setId(id);
                dtoInstrument.setState(EnumBase.EnumInstrumentStatus.正常.getValue());
                commonRepository.merge(dtoInstrument);
            }
        }
    }
}