package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.lims.pro.dto.DtoParamsData;
import lombok.Data;

import java.util.List;

/**
 * 工作单表头参数传输对象，用来实验室分析更新表头参数时进行数据传输
 *
 * <AUTHOR>
 * @version ：v1.0.0
 * @date ：2022/1/27
 */
@Data
public class DtoWorkSheetParamData {

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 参数集合
     */
    private List<DtoParamsData> paramsData;

}
