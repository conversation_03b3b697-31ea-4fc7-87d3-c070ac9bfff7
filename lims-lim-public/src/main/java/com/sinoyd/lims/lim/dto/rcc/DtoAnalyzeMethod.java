package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.AnalyzeMethod;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

/**
 * DtoAnalyzeMethod实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_AnalyzeMethod")
@Data
@DynamicInsert
public class DtoAnalyzeMethod extends AnalyzeMethod {


    /**
     * 用于替换方法中被替换的方法id
     */
    @Transient
    private String oldMethodId;

    /**
     * 检测类型名称
     */
    @Transient
    private String sampleTypeName;
}