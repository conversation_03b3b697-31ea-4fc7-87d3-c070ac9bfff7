package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.entity.CurrentPrincipalUser;

/**
 * 处理项目的一些字段的缓存数据
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/11
 * @since V100R001
 */
public interface ProjectCacheService {

    /**
     * 更新项目上样品有关的信息字段
     *
     * @param projectId            项目id
     * @param principalContextUser 当前人员信息
     */
    void updateProjectSampleJson(String projectId, CurrentPrincipalUser principalContextUser);


    /**
     * 更新项目上报告有关的信息字段
     *
     * @param projectId            项目id
     * @param principalContextUser 当前人员信息
     */
    void updateProjectReportJson(String projectId, CurrentPrincipalUser principalContextUser);
}
