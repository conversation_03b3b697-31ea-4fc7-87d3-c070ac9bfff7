package com.sinoyd.lims.lim.dto.customer;

import lombok.Data;

/**
 * 报告复合组件子组件json信息实体</p>
 *
 * <AUTHOR>
 * @version V1.0.0 2022/12/20
 * @since V100R001
 */
@Data
public class DtoSonTableJson {

    /**
     * 表头组件编码
     */
    private String headModule;

    /**
     * 检测结果组件编码
     */
    private String bodyModule;

    /**
     * 检测结果2组件编码
     */
    private String secondBodyModule;

    /**
     * 检测结果3组件编码
     */
    private String thirdBodyModule;

    public DtoSonTableJson(String headModule, String bodyModule, String secondBodyModule, String thirdBodyModule) {
        this.headModule = headModule;
        this.bodyModule = bodyModule;
        this.secondBodyModule = secondBodyModule;
        this.thirdBodyModule = thirdBodyModule;
    }
}
