package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.lims.pro.dto.customer.DtoSubmitRestrictVo;
import com.sinoyd.lims.pro.service.OATaskCheckService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 审批校验 Controller
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2024/10/08
 */
@RestController
@RequestMapping("/api/pro/oaTaskCheck")
public class OATaskCheckController {

    private Map<String, OATaskCheckService> checkServiceMap;

    /**
     * 审批提交校验检查
     *
     * @param checkStrategy 检查策略
     * @param taskId    任务ID
     * @return 校验结果
     */
    @GetMapping("/submitCheck")
    public RestResponse<List<DtoSubmitRestrictVo>> submitCheck(@RequestParam("checkStrategy") String checkStrategy,
                                                               @RequestParam("taskId") String taskId) {
        RestResponse<List<DtoSubmitRestrictVo>> response = new RestResponse<>();
        response.setData(checkServiceMap.get(checkStrategy).submitCheck(taskId));
        return response;
    }

    @Autowired
    public void setCheckServiceMap(Map<String, OATaskCheckService> checkServiceMap) {
        this.checkServiceMap = checkServiceMap;
    }
}
