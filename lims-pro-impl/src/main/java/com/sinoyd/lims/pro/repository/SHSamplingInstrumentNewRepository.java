package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoSHSamplingInstrumentNew;

import java.util.List;

/**
 *  采样仪器repository
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2022/11/25
 */
public interface SHSamplingInstrumentNewRepository extends IBaseJpaPhysicalDeleteRepository<DtoSHSamplingInstrumentNew,String> {

    /**
     * 根据任务id查询采样仪器
     * @param taskId 任务id
     * @return List<DtoSHSamplingInstrumentNew>
     */
    List<DtoSHSamplingInstrumentNew> findByTaskId(String taskId);

    /**
     * 根据任务id集合查询采样仪器
     * @param taskIds 任务id集合
     * @return List<DtoSHSamplingInstrumentNew>
     */
    List<DtoSHSamplingInstrumentNew> findByTaskIdIn(List<String> taskIds);

}
