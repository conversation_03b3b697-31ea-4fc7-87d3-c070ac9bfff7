CREATE TABLE TB_PRO_AnalyseStatisticsData
(
    id                 varchar(50) NOT NULL COMMENT 'id',
    projectId          varchar(50) NOT NULL DEFAULT '' COMMENT '项目id',
    sampleId           varchar(50) NOT NULL DEFAULT '' COMMENT '样品id',
    sampleCode         varchar(50)          DEFAULT '' COMMENT '样品编号',
    workSheetFolderId  varchar(50) NOT NULL DEFAULT '' COMMENT '工作单id',
    testId             varchar(50) NOT NULL DEFAULT '' COMMENT '测试id',
    projectTypeId      varchar(50) NOT NULL DEFAULT '' COMMENT '项目类型id',
    projectTypeName    varchar(255)         DEFAULT NULL COMMENT '项目类型名称',
    sampleTypeId       varchar(50) NOT NULL DEFAULT '' COMMENT '样品类型id',
    sampleTypeName     varchar(255)         DEFAULT NULL COMMENT '样品类型名称',
    redAnalyzeItemName varchar(100)         DEFAULT '' COMMENT '分析项目名称',
    receiveTime        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登记时间',
    analyseDataStatus  varchar(50)          DEFAULT '' COMMENT '数据状态（处理后）',
    analystId          varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '分析人员Id',
    analystName        varchar(50)          DEFAULT '' COMMENT '分析人员',
    receiveId          varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '送样单id',
    receiveStatus      varchar(50)          DEFAULT '' COMMENT '领样单状态',
    dataStatus         int(11) NOT NULL DEFAULT '1' COMMENT '数据状态（int,枚举EnumAnalyseDataStatus：1.未测 2.在测 4.已测 8.拒绝 16.已确认 32.复核通过 64.作废）',
    status             varchar(50)          DEFAULT '' COMMENT '分析数据状态',
    analyzeTime        datetime    NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '数据分析时间',
    analyseDayLen      int(11) NOT NULL DEFAULT '2' COMMENT '分析时长（天数）',
    samplingTimeBegin  datetime    NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '采样时间',
    testIsDeleted      bit(1)               DEFAULT NULL COMMENT '测试项目假删',
    qcType             int(11) NOT NULL DEFAULT '-1' COMMENT '质控类型',
    qcGrade            int(11) NOT NULL DEFAULT '-1' COMMENT '质控等级',
    orgId              varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    createDate         datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifyDate         datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    receiveSampleDate  datetime             DEFAULT NULL COMMENT '交接时间',
    PRIMARY KEY (id),
    KEY                  idx_other_conditions (analystId,analyseDataStatus,projectTypeId),
    KEY                  idx_optimized (sampleTypeId,receiveTime,analystId,analyseDataStatus,projectTypeId) USING BTREE,
    KEY                  idx_AnalyseData1 (receiveTime,sampleTypeId,projectTypeId) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分析数据统计表';