package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoPerformanceStatisticForLocalData;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;

import java.util.List;


/**
 * PerformanceStatisticForLocalData操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/2/21
 * @since V100R001
 */
public interface PerformanceStatisticForLocalDataService extends IBaseJpaService<DtoPerformanceStatisticForLocalData, String> {

    /**
     * 创建现场数据绩效
     *
     * @param record          现场送样单
     * @param analyseDataList 现场数据
     */
    void createLocalAnalyseStatistic(DtoReceiveSampleRecord record, List<DtoAnalyseData> analyseDataList,CurrentPrincipalUser principalContextUser);

    /**
     * 创建现场数据绩效
     *
     * @param records         送样单的id
     * @param analyseDataList 分析数据
     */
    void createLocalAnalyseStatistic(List<DtoReceiveSampleRecord> records,
                                     List<DtoAnalyseData> analyseDataList,
                                     CurrentPrincipalUser principalContextUser);

    /**
     * 查询现场数据的绩效
     *
     * @param baseCriteria 查询条件
     */
    void findByPage(PageBean<DtoPerformanceStatisticForLocalData> pb, BaseCriteria baseCriteria);

    /**
     * 获取总计行
     *
     * @param baseCriteria
     * @return
     */
    DtoPerformanceStatisticForLocalData findSumPerformanceStatistic(BaseCriteria baseCriteria);
}