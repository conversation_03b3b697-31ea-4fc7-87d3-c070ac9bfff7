package com.sinoyd.lims.pro.controller;


import com.sinoyd.boot.common.annotation.BusinessLog;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ELogType;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.lim.dto.lims.DtoOAContract;
import com.sinoyd.lims.lim.dto.lims.DtoTraining;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;
import com.sinoyd.lims.pro.entity.OATask;
import com.sinoyd.lims.pro.service.OATrainingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 培训审批服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/6
 * @since V100R001
 */
@Api(tags = "工作流: 培训审批服务")
@RestController
@RequestMapping("/api/pro/oaTraining")
public class OATrainingController extends ExceptionHandlerController<OATrainingService> {

    /**
     * 添加培训申请
     *
     * @param taskDto 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "添加培训申请", notes = "添加培训申请")
    @PostMapping
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<String> startProcess(@RequestBody DtoOATaskCreate<List<DtoTraining>> taskDto) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        String procInstId = service.startProcess(taskDto);
        restResp.setData(procInstId);

        return restResp;
    }

    /**
     * 保存为草稿
     *
     * @param taskDto 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "保存为草稿", notes = "保存为草稿")
    @PostMapping("/saveAsDraft")
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<OATask> saveAsDraft(@RequestBody DtoOATaskCreate<List<DtoTraining>> taskDto) {
        RestResponse<OATask> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.saveAsDraft(taskDto));
        return restResp;
    }

    /**
     * 草稿保存
     *
     * @param taskDto 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "草稿保存", notes = "草稿保存")
    @PostMapping("/draftSave")
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<OATask> draftSave(@RequestBody DtoOATaskCreate<List<DtoTraining>> taskDto) {
        RestResponse<OATask> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.draftSave(taskDto));
        return restResp;
    }

    /**
     * 草稿提交
     *
     * @param taskDto 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "添加审批", notes = "添加审批启动流程")
    @PostMapping("/draftSubmit")
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<String> draftSubmit(@RequestBody DtoOATaskCreate<List<DtoTraining>> taskDto) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.draftSubmit(taskDto));
        return restResp;
    }

    /**
     * 查询培训审批信息
     *
     * @param taskId 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "查询培训审批信息", notes = "查询培训审批信息")
    @GetMapping(path = "/task/{taskId}")
    @BusinessLog(logType = ELogType.ACCESS)
    public RestResponse<DtoOATaskDetail<List<DtoTraining>, String>> findDetailByTaskId(@PathVariable(name = "taskId") String taskId) {
        RestResponse<DtoOATaskDetail<List<DtoTraining>, String>> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoOATaskDetail<List<DtoTraining>, String> detail = service.findOATaskDetail(taskId);
        restResp.setData(detail);

        return restResp;
    }

}
