package com.sinoyd.base.repository.rcc;

import com.sinoyd.base.dto.rcc.DtoEvaluationAnalyzeItem;
import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * EvaluationAnalyzeItem数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/18
 * @since V100R001
 */
public interface EvaluationAnalyzeItemRepository extends IBaseJpaPhysicalDeleteRepository<DtoEvaluationAnalyzeItem, String>, LimsRepository<DtoEvaluationAnalyzeItem, String> {

    /**
     * 根据评价等级获取相应的评价项目信息
     *
     * @param evaluationId 评价等级id
     * @return 返回相应的评价项目信息
     */
    List<DtoEvaluationAnalyzeItem> findByEvaluationId(String evaluationId);


    /**
     * 根据评价标准获取相应的评价项目信息
     */
    List<DtoEvaluationAnalyzeItem> findByEvaluationIdIn(List<String> evaluationIds);


    /**
     * 批量删除评价标准的相关分析项目id
     *
     * @param evaluationId         评价标准id
     * @param deleteAnalyzeItemIds 带删除的分析项目ids
     * @return 返回删除行
     */
    @Transactional
    @Modifying
    @Query("delete  from DtoEvaluationAnalyzeItem  AS  a where  a.evaluationId = :evaluationId and a.analyzeItemId in :deleteAnalyzeItemIds")
    Integer delete(@Param("evaluationId") String evaluationId, @Param("deleteAnalyzeItemIds") List<String> deleteAnalyzeItemIds);

    /**
     *  根据evaluationIds删除因子
     * @param evaluationIds 评价标准ids
     * @return 删除数量
     */
    @Transactional
    @Modifying
    @Query("delete  from DtoEvaluationAnalyzeItem  AS  a where  a.evaluationId in :evaluationIds")
    Integer deleteByEvaluationIdIn(@Param("evaluationIds") List<String>  evaluationIds);
}