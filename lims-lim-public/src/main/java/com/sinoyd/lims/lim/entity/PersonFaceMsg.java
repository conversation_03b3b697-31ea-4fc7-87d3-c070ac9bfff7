package com.sinoyd.lims.lim.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;


/**
 * PersonFaceMsg实体
 * <AUTHOR>
 * @version V1.0.0 2022/9/26
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="PersonFaceMsg")
 @Data
 public  class PersonFaceMsg implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  PersonFaceMsg() {
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
    * 人员id
    */
    @Column(length=50)
    @ApiModelProperty("人员id")
	private String personId;

    /**
    * 人脸信息
    */
    @Column(length=500)
    @ApiModelProperty("人脸信息")
	private String facePicture;

    /**
    * 假删
    */
    @Column(length=0,nullable=false)
    @ApiModelProperty("假删")
	private Boolean isDeleted;

    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("组织机构id")
	private String orgId;

    /**
     * 创建人
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;
 }