package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;

/**
 * SolutionCalibrationRecord实体
 * <AUTHOR>
 * @version V1.0.0 2024/04/19
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description="SamplingPersonConfig")
@Data
public class SolutionCalibrationRecord implements BaseEntity, Serializable {
    private static final long serialVersionUID = 1L;

    public  SolutionCalibrationRecord() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 溶液标定标识
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("溶液标定标识")
    private String solutionCalibrationId;

    /**
     * 溶液移取量
     */
    @ApiModelProperty("溶液移取量")
    @Column(length = 50)
    @Length(message = "溶液移取量{validation.message.length}", max = 50)
    private String transferVolume;

    /**
     * V始
     */
    @ApiModelProperty("V始")
    @Column(length = 50)
    @Length(message = "V始{validation.message.length}", max = 50)
    private String volumeStart;

    /**
     * V终
     */
    @ApiModelProperty("V终")
    @Column(length = 50)
    @Length(message = "V终{validation.message.length}", max = 50)
    private String volumeEnd;

    /**
     * 有效位数
     */
    @ApiModelProperty("有效位数")
    @Column
    private Integer mostSignificance;

    /**
     * 小数位数
     */
    @ApiModelProperty("小数位数")
    @Column
    private Integer mostDecimal;

    /**
     * 标定液浓度
     */
    @ApiModelProperty("标定液浓度")
    @Column(length = 50)
    @Length(message = "标定液浓度{validation.message.length}", max = 50)
    private String concentration;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;
}
