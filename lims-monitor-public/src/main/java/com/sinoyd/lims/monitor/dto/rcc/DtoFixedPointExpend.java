package com.sinoyd.lims.monitor.dto.rcc;

import com.sinoyd.lims.monitor.entity.FixedPointExpend;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoFixedPointExpend实体
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_MONITOR_FixedPointExpend")
 @Data
 @DynamicInsert
 public  class DtoFixedPointExpend extends FixedPointExpend {
   private static final long serialVersionUID = 1L;
 }