package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentUseRecord;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 仪器使用记录操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019-05-08
 * @since V100R001
 */
public interface InstrumentUseRecordRepository extends IBaseJpaRepository<DtoInstrumentUseRecord, String> {
    /**
     * 根据环境信息id删除仪器使用记录
     *
     * @param environmentalManageId 环境信息id
     */
    @Transactional
    Integer deleteByEnvironmentalManageId(String environmentalManageId);

    /**
     * 根据环境信息id删除仪器使用记录
     *
     * @param instrumentIds 仪器id集合
     * @param endTime       结束时间
     * @param startTime     开始时间
     * @param objectType    关联类型
     */
    List<DtoInstrumentUseRecord> findByInstrumentIdInAndObjectTypeAndStartTimeBeforeAndEndTimeAfter(List<String> instrumentIds, Integer objectType,
                                                                                                    Date endTime, Date startTime);

    /**
     * 根据关联id及类型获取仪器使用记录
     *
     * @param objectId   关联id
     * @param objectType 关联类型
     * @return 仪器使用记录
     */
    List<DtoInstrumentUseRecord> findByObjectIdAndObjectType(String objectId, Integer objectType);

    /**
     * 根据关联id及类型获取仪器使用记录
     *
     * @param objectIds  关联id集合
     * @param objectType 关联类型
     * @return 仪器使用记录
     */
    List<DtoInstrumentUseRecord> findByObjectIdInAndObjectType(List<String> objectIds, Integer objectType);

    /**
     * 根据关联id获取仪器使用记录
     *
     * @param objectId  关联id
     * @return 仪器使用记录
     */
    List<DtoInstrumentUseRecord> findByObjectId(String objectId);

    /**
     * 根据仪器id获取仪器使用记录
     *
     * @param instrumentIds 仪器id
     * @return 仪器使用记录
     */
    List<DtoInstrumentUseRecord> findByInstrumentIdIn(List<String> instrumentIds);

    /**
     * 根据环境记录id获取仪器使用记录
     *
     * @param environmentalManageId 环境记录id
     * @return 仪器使用记录
     */
    List<DtoInstrumentUseRecord> findByEnvironmentalManageId(String environmentalManageId);

    /**
     * 根据环境记录id获取仪器使用记录
     *
     * @param environmentalManageIds 环境记录id
     * @return 仪器使用记录
     */
    List<DtoInstrumentUseRecord> findByEnvironmentalManageIdIn(List<String> environmentalManageIds);

    /**
     * 根据关联id删除仪器使用记录
     *
     * @param objectIds  关联ID
     * @param modifier   修改人
     * @param modifyDate 修改时间
     * @return 删除条数
     */
    @Transactional
    @Modifying
    @Query("update DtoInstrumentUseRecord a set a.isDeleted = true,a.modifier = :modifier,a.modifyDate= :modifyDate where a.objectId in :objectIds")
    Integer deleteByObjectIdIn(@Param("objectIds") List<String> objectIds, @Param("modifier") String modifier,
                               @Param("modifyDate") Date modifyDate);
}