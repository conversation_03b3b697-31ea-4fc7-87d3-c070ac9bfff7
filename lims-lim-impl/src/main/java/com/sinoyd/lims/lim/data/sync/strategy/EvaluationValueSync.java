package com.sinoyd.lims.lim.data.sync.strategy;

import com.sinoyd.base.dto.rcc.DtoEvaluationCriteria;
import com.sinoyd.base.dto.rcc.DtoEvaluationValue;
import com.sinoyd.base.repository.rcc.EvaluationValueRepository;
import com.sinoyd.base.service.EvaluationValueService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.lims.lim.data.sync.dto.DtoDataCompareResult;
import com.sinoyd.lims.lim.data.sync.strategy.base.AbsDataSync;
import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 评价标准限值同步
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/5/23
 */
@Component
@DependsOn({"springContextAware"})
@Order(18)
@Slf4j
public class EvaluationValueSync extends AbsDataSync<DtoEvaluationValue> {

    @Autowired
    private EvaluationValueService service;

    @Autowired
    private EvaluationValueRepository repository;

    @Autowired
    private EvaluationSync evaluationSync;
    /**
     * 数据比较
     *
     * @param evaluationIds 需要同步的测试项目id
     * @return 比较结果
     */
    @Override
    public List<DtoDataCompareResult<DtoEvaluationValue>> compareData(List<String> evaluationIds) {
        List<DtoEvaluationValue> projectData = service.findAll();
        List<DtoEvaluationValue> standardData = queryStandardData();
        if (StringUtil.isNotEmpty(standardData) && StringUtil.isNotEmpty(evaluationIds)){
            standardData = standardData.stream().filter(p->evaluationIds.contains(p.getEvaluationId())).collect(Collectors.toList());
        }
        //判断评价标准是否被删除
        List<DtoEvaluationCriteria> evaluations = evaluationSync.queryStandardData();
        if (StringUtil.isNotEmpty(evaluations)){
            List<String> evaluationIdsTemp = evaluations.stream().map(DtoEvaluationCriteria::getId).collect(Collectors.toList());
            List<DtoEvaluationValue> temps = standardData.stream().filter(p->!evaluationIdsTemp.contains(p.getEvaluationId())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(temps)){
                List<String> tempsIds = temps.stream().map(DtoEvaluationValue::getId).collect(Collectors.toList());
                standardData.removeIf(p->tempsIds.contains(p.getId()));
            }
        }else{
            standardData = null;
        }
        return compareData(standardData,projectData);
    }

    /**
     * 同步数据
     *
     * @param evaluationIds         需要同步的数据id集合
     * @param webSocketServer websockets服务端
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncData(List<String> evaluationIds, WebSocketServer webSocketServer) {
        List<DtoDataCompareResult<DtoEvaluationValue>> compareResult = compareData(evaluationIds);
        Optional<DtoDataCompareResult<DtoEvaluationValue>> resultOptional = compareResult.parallelStream().filter(p -> COMPARE_CATEGORY[0].equals(p.getCategory())
                && p.getCount() > 0).findFirst();
        resultOptional.ifPresent(r -> {
            //已同步记录数
            int i = 0;
            DtoEvaluationValue errorDto = null;
            try {
                for (DtoEvaluationValue dtoEvaluationValue : r.getAddDataList()) {
                    errorDto = dtoEvaluationValue;
                    if (repository.findOne(dtoEvaluationValue.getId()) != null) {
                        service.update(dtoEvaluationValue);
                    } else {
                        service.save(dtoEvaluationValue);
                    }
                    webSocketServer.sendMessage(String.format("同步" + getItemName() + "(%d/%d)", ++i, r.getCount()));
                }
            } catch (Exception e) {
                if (errorDto != null) {
                    webSocketServer.sendMessage("同步" + getItemName() + "发生错误, ID = " + errorDto.getId() + ", 错误信息: "
                            + e.getMessage());
                }
                log.error(e.getMessage(), e);
                throw new BaseException("同步" + getItemName() + "发生错误");
            }
        });
    }

    @Override
    public boolean mustSync() {
        return true;
    }

    @Override
    public String getItemName() {
        return EnumLIM.EnumDataSyncType.评价标准限值.name();
    }

    @Override
    public Integer getOrderNum() {
        return EnumLIM.EnumDataSyncType.评价标准限值.getValue();
    }

    @Override
    public String getStandardDataQueryUrl() {
        return "/api/sinoyd-lims/base/evaluationValue";
    }

    @Override
    public Integer getSyncDataType() {
        return EnumLIM.EnumDataSyncType.评价标准限值.getValue();
    }
}
