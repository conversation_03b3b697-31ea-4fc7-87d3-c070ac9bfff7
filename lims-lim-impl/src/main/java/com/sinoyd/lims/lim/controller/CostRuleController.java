package com.sinoyd.lims.lim.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.service.CostRuleService;
import com.sinoyd.lims.lim.criteria.CostRuleCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoCostRule;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * 费用规则服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/15
 * @since V100R001
 */
 @Api(tags = "CostRule服务")
 @RestController
 @RequestMapping("api/lim/costRule")
 @Validated
 public class CostRuleController extends BaseJpaController<DtoCostRule, String,CostRuleService> {


    /**
     * 分页动态条件查询费用规则
     *
     * @param costRuleCriteria 条件参数
     * @return RestResponse<List < CostRule>>
     */
    @ApiOperation(value = "分页动态条件查询费用规则", notes = "分页动态条件查询费用规则")
    @GetMapping
    public RestResponse<List<DtoCostRule>> findByPage(CostRuleCriteria costRuleCriteria) {
        PageBean<DtoCostRule> pageBean = super.getPageBean();
        RestResponse<List<DtoCostRule>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, costRuleCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 新增CostRule
     * @param costRule 实体列表
     * @return RestResponse<DtoCostRule>
     */
    @ApiOperation(value = "新增CostRule", notes = "新增CostRule")
    @PostMapping
    public RestResponse<DtoCostRule> create(@Validated @RequestBody DtoCostRule costRule) {
        RestResponse<DtoCostRule> restResponse = new RestResponse<>();
        restResponse.setData(service.save(costRule));
        return restResponse;
    }

    /**
     * 修改CostRule
     * @param costRule 实体列表
     * @return RestResponse<DtoCostRule>
     */
    @ApiOperation(value = "修改CostRule", notes = "修改CostRule")
    @PutMapping
    public RestResponse<DtoCostRule> update(@Validated @RequestBody DtoCostRule costRule) {
        RestResponse<DtoCostRule> restResponse = new RestResponse<>();
        restResponse.setData(service.update(costRule));
        return restResponse;
    }
}