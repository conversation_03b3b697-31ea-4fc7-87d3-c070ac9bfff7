package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoStandardMethodDetail;

import java.util.List;

/**
 * StandardMethodDetail
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/13
 * @since V100R001
 */
public interface StandardMethodDetailRepository extends IBaseJpaPhysicalDeleteRepository<DtoStandardMethodDetail, String> {

    /**
     * 根据标准方法ids查询详情
     *
     * @param methodIds 方法ids
     * @return 详情
     */
    List<DtoStandardMethodDetail> findByMethodIdIn(List<String> methodIds);

    /**
     * 根据标准方法id查询详情
     *
     * @param methodId 方法id
     * @return 详情
     */
    List<DtoStandardMethodDetail> findByMethodId(String methodId);
}