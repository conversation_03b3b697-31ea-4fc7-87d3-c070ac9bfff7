package com.sinoyd.lims.monitor.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.monitor.dto.lims.DtoPropertyPoint2Test;
import com.sinoyd.frame.service.IBaseJpaService;


/**
 * PropertyPoint2Test操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
public interface PropertyPoint2TestService extends IBaseJpaService<DtoPropertyPoint2Test, String> {
    /**
     * 查询关联的测试计划
     *
     * @param pb 分页信息
     * @param propertyPoint2TestCriteria 查询条件
     */
    void findTest(PageBean<DtoTest> pb, BaseCriteria propertyPoint2TestCriteria);
}