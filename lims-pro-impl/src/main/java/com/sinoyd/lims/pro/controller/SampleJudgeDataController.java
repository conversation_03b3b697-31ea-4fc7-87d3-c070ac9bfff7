package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.SampleJudgeDataCriteria;
import com.sinoyd.lims.pro.dto.DtoSampleJudgeData;
import com.sinoyd.lims.pro.service.SampleJudgeDataService;
import com.sinoyd.lims.pro.vo.SampleJudgeDataVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * SampleJudgeData服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2023/06/13
 * @since V100R001
 */
@Api(tags = "示例: SampleJudgeData服务")
@RestController
@RequestMapping("api/pro/sampleJudgeData")
public class SampleJudgeDataController extends BaseJpaController<DtoSampleJudgeData, String, SampleJudgeDataService> {

    /**
     * 根据点位id查询比对数据列表
     *
     * @param vo 条件参数
     * @return RestResponse<Map < String, List < DtoSampleJudgeData>>>
     */
    @ApiOperation(value = "根据点位id查询比对数据列表", notes = "根据点位id查询比对数据列表")
    @GetMapping
    public RestResponse<Map<String, List<DtoSampleJudgeData>>> findByFolderId(SampleJudgeDataVo vo) {
        RestResponse<Map<String, List<DtoSampleJudgeData>>> response = new RestResponse<>();
        response.setData(service.findByFolderId(vo));
        return response;
    }

    @ApiOperation(value = "根据点位id查询比对数据列表", notes = "根据点位id查询比对数据列表")
    @GetMapping("/evaluateList")
    public RestResponse<Object> evaluteList(SampleJudgeDataCriteria criteria) {
        RestResponse<Object> response = new RestResponse<>();
        response.setData(service.evaluteList(criteria));
        return response;
    }

    @ApiOperation(value = "批量保存", notes = "批量保存")
    @PutMapping
    public RestResponse<List<DtoSampleJudgeData>> update(@RequestBody List<DtoSampleJudgeData> sampleJudgeDataList) {
        RestResponse<List<DtoSampleJudgeData>> response = new RestResponse<>();
        response.setData(service.update(sampleJudgeDataList));
        return response;
    }

    @ApiOperation(value = "添加质控样", notes = "添加质控样")
    @PostMapping("/addQcSample")
    public RestResponse<Void> addQcSample(@RequestBody SampleJudgeDataVo vo) {
        RestResponse<Void> response = new RestResponse<>();
        service.addQcSample(vo.getQcGrade(), vo.getQcType(), vo.getStandardCode(), vo.getExpectedValue(), vo.getNum(), vo.getIds());
        return response;
    }

    /**
     * 根据id批量删除
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    @PostMapping("/syncData")
    public RestResponse<Void> syncData(@RequestBody Map<String, Object> params) {
        RestResponse<Void> response = new RestResponse<>();
        service.syncData(params);
        return response;
    }

    @PostMapping("/syncConfig")
    public RestResponse<Void> syncConfig(@RequestBody Map<String, Object> params) {
        RestResponse<Void> response = new RestResponse<>();
        service.syncConfig(params);
        return response;
    }

    @GetMapping("/export")
    public RestResponse<String> exportPersonDetails(SampleJudgeDataCriteria criteria, HttpServletResponse response) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.export(criteria, response);
        return restResponse;
    }

    @GetMapping("/havingData")
    public RestResponse<Map> havingData(@RequestParam("receiveId") String receiveId) {
        RestResponse<Map> response = new RestResponse<>();
        response.setData(service.havingData(receiveId));
        return response;
    }

    @GetMapping("/havingDataForReport")
    public RestResponse<Map> havingDataForReport(@RequestParam("reportId") String reportId) {
        RestResponse<Map> response = new RestResponse<>();
        response.setData(service.havingDataForReport(reportId));
        return response;
    }

    @GetMapping("/havingDataForProject")
    public RestResponse<Map> havingDataForProject(@RequestParam("projectId") String projectId) {
        RestResponse<Map> response = new RestResponse<>();
        response.setData(service.havingDataForProject(projectId));
        return response;
    }

    @GetMapping("/updateJudgeData")
    public RestResponse<Boolean> updateJudgeData(@RequestParam("receiveId") String receiveId) {
        RestResponse<Boolean> response = new RestResponse<>();
        service.updateJudgeData(receiveId);
        response.setData(Boolean.TRUE);
        return response;
    }

    @PostMapping("/cancellation")
    public RestResponse<Boolean> cancellationData(@RequestBody List<String> ids) {
        RestResponse<Boolean> response = new RestResponse<>();
        service.cancellationData(ids);
        response.setData(Boolean.TRUE);
        return response;
    }

    @PostMapping("/cancellationCancel")
    public RestResponse<Boolean> cancellationCancelData(@RequestBody List<String> ids) {
        RestResponse<Boolean> response = new RestResponse<>();
        service.cancellationCancelData(ids);
        response.setData(Boolean.TRUE);
        return response;
    }

    /**
     * 更新是否不参与评价状态
     *
     * @param ids           比对数据ids
     * @param isNotEvaluate 是否不参与
     * @return 结果
     */
    @ApiOperation(value = "更新是否不参与评价状态", notes = "更新是否不参与评价状态")
    @PostMapping("/evaluateState/{isNotEvaluate}")
    public RestResponse<Boolean> updateEvaluateState(@RequestBody List<String> ids, @PathVariable("isNotEvaluate") Boolean isNotEvaluate) {
        RestResponse<Boolean> response = new RestResponse<>();
        service.updateEvaluateState(ids, isNotEvaluate);
        response.setData(Boolean.TRUE);
        return response;
    }
}
