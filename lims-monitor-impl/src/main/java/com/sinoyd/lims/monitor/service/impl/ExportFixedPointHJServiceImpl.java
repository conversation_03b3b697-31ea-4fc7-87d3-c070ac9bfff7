package com.sinoyd.lims.monitor.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.lims.EnterpriseRepository;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.AreaService;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.constants.LimConstants;
import com.sinoyd.lims.lim.utils.ImportUtils;
import com.sinoyd.lims.monitor.criteria.FixedpointCriteria;
import com.sinoyd.lims.monitor.dto.customer.*;
import com.sinoyd.lims.monitor.dto.customer.DtoExpImpFixedPointHJ;
import com.sinoyd.lims.monitor.dto.customer.DtoExpImpFixedPointHJ;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedPointExpend;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;
import com.sinoyd.lims.monitor.dto.rcc.DtoStation;
import com.sinoyd.lims.monitor.enums.EnumMonitor;
import com.sinoyd.lims.monitor.repository.rcc.FixedpointRepository;
import com.sinoyd.lims.monitor.repository.rcc.StationRepository;
import com.sinoyd.lims.monitor.service.ExportFixedPointHJService;
import com.sinoyd.lims.monitor.service.ExportFixedPointWRService;
import com.sinoyd.lims.monitor.service.FixedpointService;
import com.sinoyd.lims.monitor.verify.ImpModifyFixedPointHJVerify;
import com.sinoyd.lims.monitor.verify.ImpModifyFixedPointWRVerify;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 环境质量点位导入导出接口实现
 *
 * @version V1.0.0 2023/12/27
 * @author: hukq
 * @since V100R001
 */
@Service
public class ExportFixedPointHJServiceImpl extends BaseJpaServiceImpl<DtoFixedpoint, String, FixedpointRepository> implements ExportFixedPointHJService {

    private ImportUtils importUtils;

    private CodeService codeService;

    private FixedpointService fixedpointService;

    private StationRepository stationRepository;

    private EnterpriseRepository enterpriseRepository;

    private SampleTypeRepository sampleTypeRepository;

    private AreaService areaService;


    @Override
    public void export(BaseCriteria baseCriteria, HttpServletResponse response, Map<String, String> sheetNames, String fileName) {
        List<DtoFixedpoint> fixedpointList = getData(baseCriteria);

        //获取所有区域信息
        List<DtoArea> dbAreaList = areaService.findAll();

        List<DtoExpImpFixedPointHJ> expImpFixedPointHJS = new ArrayList<>();
        for (DtoFixedpoint fixedpoint : fixedpointList) {
            DtoExpImpFixedPointHJ expImpFixedPointHJ = new DtoExpImpFixedPointHJ();
            BeanUtils.copyProperties(fixedpoint, expImpFixedPointHJ);
            setAreaData(dbAreaList, fixedpoint.getAreaId(), expImpFixedPointHJ);

            expImpFixedPointHJ.setEnabledStatus(fixedpoint.getIsEnabled() ? "是" : "否");
            expImpFixedPointHJS.add(expImpFixedPointHJ);
        }
        //获取点位环境质量的点位类型
        List<DtoImportFixedExpand> pointTypes = findAllPointType(LimConstants.ImportConstants.FIXED_POINT_TYPE_HJ);
        //设置点位类型数据
        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoExpImpFixedPointHJ.class, DtoImportFixedExpand.class, expImpFixedPointHJS, pointTypes);

        //所属测站(必填)
        String[] stations = stationRepository.findAll().stream().map(DtoStation::getStname).toArray(String[]::new);
        // 等级
        String[] controls = codeService.findCodes(LimConstants.ImportConstants.CONTROL_LEVEL)
                .stream().map(DtoCode::getDictName).toArray(String[]::new);
        //启用状态(是,否)
        String[] status = new String[]{"是", "否"};
        importUtils.selectList(workBook, 4, 4, stations);
        importUtils.selectList(workBook, 5, 5, controls);
        importUtils.selectList(workBook, 12, 12, status);

        //下载Excel返回响应流
        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
    }

    @Override
    public List<DtoFixedpoint> importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception {
        //校验文件类型
        PoiExcelUtils.verifyFileType(file);
        //获取业务参数

        //获取所有的检测类型
        List<DtoSampleType> sampleTypes = sampleTypeRepository.findAll();
        //获取所有的企业
        List<DtoEnterprise> enterpriseList = enterpriseRepository.findAll();
        //获取所有的测站
        List<DtoStation> stationList = stationRepository.findAll();
        //获取所有区域信息
        List<DtoArea> dbAreaList = areaService.findAll();
        //获取所有的控制等级
        List<DtoCode> controlLevels = codeService.findCodes(LimConstants.ImportConstants.CONTROL_LEVEL);
        //获取所有的点位类型（环境质量）
        List<DtoCode> pointTypeHJ = codeService.findCodes(LimConstants.ImportConstants.FIXED_POINT_TYPE_HJ);
        Map<String, List<DtoCode>> codeMap = new HashMap<>();
        codeMap.put(LimConstants.ImportConstants.CONTROL_LEVEL, controlLevels);
        codeMap.put(LimConstants.ImportConstants.FIXED_POINT_TYPE_HJ, pointTypeHJ);
        // 初始化校验器
        ImpModifyFixedPointHJVerify impModifyFixedPointHJVerify = new ImpModifyFixedPointHJVerify(stationList, dbAreaList, codeMap, enterpriseList, sampleTypes);

        //获取导入校验后的数据
        ExcelImportResult<DtoExpImpFixedPointHJ> excelData = getExcelData(impModifyFixedPointHJVerify, file, response);
        List<DtoExpImpFixedPointHJ> list = excelData.getList();
        list.removeIf(p -> StringUtil.isEmpty(p.getPointName()));
        if (StringUtil.isEmpty(list)) {
            throw new BaseException("文件中无数据或者上传文件模板错误，请检查后重新上传");
        }
        List<String> ids = list.stream().map(DtoExpImpFixedPointHJ::getId).filter(StringUtil::isNotEmpty).collect(Collectors.toList());
        List<DtoFixedpoint> fixedpointList = repository.findAll(ids);
        //转换实体
        List<DtoFixedpoint> fixedPoints = importToEntity(list, controlLevels, pointTypeHJ, fixedpointList);
        //保存数据
        addData(fixedPoints);
        return fixedPoints;
    }

    @Override
    public void addData(List<DtoFixedpoint> data) {
        if (StringUtil.isNotEmpty(data)) {
            repository.save(data);
        }
    }

    @Override
    public ExcelImportResult<DtoExpImpFixedPointHJ> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        return null;
    }

    @Override
    public ExcelImportResult<DtoExpImpFixedPointHJ> getExcelData(IExcelVerifyHandler<DtoExpImpFixedPointHJ> verifyHandler, MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        //设置表头区域
        params.setTitleRows(0);
        //设置表头开始行
        params.setHeadRows(1);
        //设置开始工作簿
        params.setStartSheetIndex(0);
        //设置是否校验
        params.setNeedVerify(true);
        //设置校验handle
        params.setVerifyHandler(verifyHandler);
        ExcelImportResult<DtoExpImpFixedPointHJ> result = ExcelImportUtil.importExcelMore(file.getInputStream(), DtoExpImpFixedPointHJ.class, params);
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "企业信息");
            PoiExcelUtils.downLoadExcel("企业导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }


    private List<DtoFixedpoint> getData(BaseCriteria baseCriteria) {
        PageBean<DtoFixedpoint> page = new PageBean<>();
        FixedpointCriteria criteria = (FixedpointCriteria) baseCriteria;
        page.setPageNo(1);
        page.setRowsPerPage(Integer.MAX_VALUE);
        fixedpointService.findByPage(page, criteria);
        return page.getData();
    }


    /**
     * 获取拓展的点位类型
     *
     * @param codeValue 字典编码
     * @return 点位类型数据
     */
    private List<DtoImportFixedExpand> findAllPointType(String codeValue) {
        List<DtoImportFixedExpand> fixedExpands = new ArrayList<>();
        List<DtoCode> codes = codeService.findCodes(codeValue);
        if (StringUtil.isNotEmpty(codes)) {
            for (DtoCode code : codes) {
                DtoImportFixedExpand expand = new DtoImportFixedExpand();
                expand.setPointType(code.getDictName());
                fixedExpands.add(expand);
            }
        }
        return fixedExpands;
    }


    private void setAreaData(List<DtoArea> areaList, String id, DtoExpImpFixedPointHJ expImpFixedPointHJ) {
        DtoArea area = areaList.stream().filter(p -> p.getId().equals(id)).findFirst().orElse(null);
        if (StringUtil.isNotNull(area)) {
            expImpFixedPointHJ.setAreaName("");
            if ("0".equals(area.getParentId())) {
                expImpFixedPointHJ.setProvinceId(area.getId());
                expImpFixedPointHJ.setProvinceAreaName(area.getAreaName());
            } else {
                DtoArea parentArea = areaList.stream().filter(p -> p.getId().equals(area.getParentId())).findFirst().orElse(null);
                if (StringUtil.isNotNull(parentArea)) {
                    if ("0".equals(parentArea.getParentId())) {
                        expImpFixedPointHJ.setProvinceId(parentArea.getId());
                        expImpFixedPointHJ.setProvinceAreaName(parentArea.getAreaName());
                        expImpFixedPointHJ.setCityId(area.getId());
                        expImpFixedPointHJ.setCityAreaName(area.getAreaName());
                    } else {
                        DtoArea dtoArea = areaList.stream().filter(p -> p.getId().equals(parentArea.getParentId())).findFirst().orElse(null);
                        if (StringUtil.isNotNull(dtoArea)) {
                            if ("0".equals(dtoArea.getParentId())) {
                                expImpFixedPointHJ.setProvinceId(dtoArea.getId());
                                expImpFixedPointHJ.setProvinceAreaName(dtoArea.getAreaName());
                                expImpFixedPointHJ.setCityId(parentArea.getId());
                                expImpFixedPointHJ.setCityAreaName(parentArea.getAreaName());
                                expImpFixedPointHJ.setAreaId(area.getId());
                                expImpFixedPointHJ.setAreaName(area.getAreaName());
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取导出的所有点位类型
     *
     * @param folderTypeNames 点位类型导入字符串
     * @return 点位类型字符串集合
     */
    private Set<String> getFolderTypeNames(String folderTypeNames) {
        if (StringUtil.isNotEmpty(folderTypeNames)) {
            if (folderTypeNames.contains("、") || folderTypeNames.contains("，") || folderTypeNames.contains(",")) {
                folderTypeNames = folderTypeNames.replace("，", ",");
                folderTypeNames = folderTypeNames.replace("、", ",");
                return Arrays.stream(folderTypeNames.split(",")).collect(Collectors.toSet());
            } else {
                return Stream.of(folderTypeNames).collect(Collectors.toSet());
            }
        }
        return new HashSet<>();
    }

    /**
     * 转换为需要保存的实体
     *
     * @param importList 导入的数据
     * @return 实体集合
     */
    private List<DtoFixedpoint> importToEntity(List<DtoExpImpFixedPointHJ> importList,
                                               List<DtoCode> controlLevel,
                                               List<DtoCode> pointTypeHJ,
                                               List<DtoFixedpoint> fixedpointList) {
        Map<String, DtoFixedpoint> fixedpointMap = fixedpointList.stream().collect(Collectors.toMap(DtoFixedpoint::getId, p -> p));
        //点位数据
        List<DtoFixedpoint> fixedPoints = new ArrayList<>();
        for (DtoExpImpFixedPointHJ dto : importList) {
            DtoFixedpoint fixedPoint = new DtoFixedpoint();

            //处理点位类型
            if (StringUtil.isNotEmpty(dto.getFolderTypeName())) {
                //设置类型
                dto.setPointType(EnumMonitor.EnumPointType.环境质量.getValue());
                //设置环境质量的点位类型
                if (StringUtil.isNotEmpty(pointTypeHJ)) {
                    String folderTypeNames = dto.getFolderTypeName();
                    Set<String> folderTypeNameList = getFolderTypeNames(folderTypeNames);
                    List<String> folderTypes = new ArrayList<>();
                    for (String folderType : folderTypeNameList) {
                        Optional<DtoCode> pointTypeOp = pointTypeHJ
                                .stream().filter(p -> folderType.equals(p.getDictName())).findFirst();
                        pointTypeOp.ifPresent(p -> folderTypes.add(p.getDictCode()));
                    }
                    dto.setFolderType(String.join(",", folderTypes));
                }

            }
            //设置控制等级
            if (StringUtil.isNotEmpty(dto.getLevelName()) && StringUtil.isNotEmpty(controlLevel)) {
                Optional<DtoCode> controlLevelOp = controlLevel.stream().filter(p -> dto.getLevelName().equals(p.getDictName())).findFirst();
                controlLevelOp.ifPresent(p -> dto.setLevel(p.getDictCode()));
            }

            if (fixedpointMap.containsKey(dto.getId())) {
                fixedPoint = fixedpointMap.get(dto.getId());
            } else {
                dto.setId(UUIDHelper.NewID());
                //处理周期，次数默认为1
                fixedPoint.setCycleOrder(LimConstants.ImportConstants.DEFAULT_ORDER_NUM);
                fixedPoint.setTimesOrder(LimConstants.ImportConstants.DEFAULT_ORDER_NUM);
                fixedPoint.setEvaluationId("");
                fixedPoint.setEvaluationLevelId("");
            }

            //赋值属性
            BeanUtils.copyProperties(dto, fixedPoint);

            //根据所属区域名称获取区域数据
            String areaId = "";
            if (StringUtil.isNotEmpty(dto.getProvinceAreaName()) && StringUtil.isNotEmpty(dto.getProvinceId())) {
                areaId = dto.getProvinceId();
            }
            if (StringUtil.isNotEmpty(dto.getCityAreaName()) && StringUtil.isNotEmpty(dto.getCityId())) {
                areaId = dto.getCityId();
            }
            if (StringUtil.isNotEmpty(dto.getAreaName()) && StringUtil.isNotEmpty(dto.getAreaId())) {
                areaId = dto.getAreaId();
            }
            fixedPoint.setAreaId(areaId);
            //添加数据
            fixedPoints.add(fixedPoint);
        }
        return fixedPoints;
    }

    @Autowired
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }

    @Autowired
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    public void setFixedpointService(FixedpointService fixedpointService) {
        this.fixedpointService = fixedpointService;
    }

    @Autowired
    public void setStationRepository(StationRepository stationRepository) {
        this.stationRepository = stationRepository;
    }

    @Autowired
    public void setEnterpriseRepository(EnterpriseRepository enterpriseRepository) {
        this.enterpriseRepository = enterpriseRepository;
    }

    @Autowired
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    public void setAreaService(AreaService areaService) {
        this.areaService = areaService;
    }
}
