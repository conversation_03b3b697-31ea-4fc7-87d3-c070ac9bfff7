package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.entity.MpnConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * DtoMpnConfig实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/2/12
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_MpnConfig")
@Data
@DynamicInsert
public class DtoMpnConfig extends MpnConfig {

    /**
     * 分析项目
     */
    @Transient
    private String redAnalyzeItemName;

    /**
     * 分析方法
     */
    @Transient
    private String redAnalyzeMethodName;

    /**
     * 标准号
     */
    @Transient
    private String redCountryStandard;

    /**
     * 检测类型
     */
    @Transient
    private String sampleTypeName;

    /**
     * 参数1名称
     */
    @Transient
    private String param1;

    /**
     * 参数2名称
     */
    @Transient
    private String param2;

    /**
     * 参数3名称
     */
    @Transient
    private String param3;

    /**
     * 结果参数名称
     */
    @Transient
    private String resultParam;


    public Map<String, String> toParamMap(DtoMpnConfig dtoMpnConfig, Map<String, String> paramsMap) {
        Map<String, String> result = new LinkedHashMap<>();
        result.put("param1", paramsMap.get(dtoMpnConfig.getParam1Id()));
        if (StringUtil.isNotEmpty(dtoMpnConfig.getParam2Id())) {
            result.put("param2", paramsMap.get(dtoMpnConfig.getParam2Id()));
        }
        if (StringUtil.isNotEmpty(dtoMpnConfig.getParam3Id())) {
            result.put("param3", paramsMap.get(dtoMpnConfig.getParam3Id()));
        }
        result.put("resultParam", paramsMap.get(dtoMpnConfig.getResultParamId()));
        return result;
    }



}
