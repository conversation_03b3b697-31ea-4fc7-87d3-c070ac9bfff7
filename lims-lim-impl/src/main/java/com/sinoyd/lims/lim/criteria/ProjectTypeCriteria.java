package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;



/**
 * 项目类型查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年10月23日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectTypeCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否包含parentId为空id
     */
    private Boolean containParent;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据

        StringBuilder condition = new StringBuilder();
        // 是否包含parentId为空id
        if (!StringUtils.isNotNullAndEmpty(containParent) || !containParent) {
            condition.append(" and parentId <> :parentId");
            values.put("parentId", UUIDHelper.GUID_EMPTY);
        }

        return condition.toString();
    }
}