package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.factory.quality.QualityCipherParallel;
import com.sinoyd.base.factory.quality.QualityParallel;
import com.sinoyd.lims.lim.dto.customer.DtoTestQCRemindTemp;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.TestQCRemindConfig2TestService;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.customer.DtoQualityRemind;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.AnalyseDataRepository;
import com.sinoyd.lims.pro.repository.SampleRepository;
import com.sinoyd.lims.pro.service.QualityRemindService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 质控提醒实现
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@Service
public class QualityRemindServiceImpl implements QualityRemindService {

    @Autowired
    private AnalyseDataRepository analyseDataRepository;

    @Autowired
    private SampleRepository sampleRepository;

    @Autowired
    @Lazy
    private TestQCRemindConfig2TestService testQCRemindConfig2TestService;

    @Override
    public List<DtoQualityRemind> findByReceiveIds(List<String> receiveIds) {
        List<DtoSample> samples = sampleRepository.findByReceiveIdIn(receiveIds);
        List<String> ySampleIds = samples.stream().filter(p -> p.getSampleCategory().equals(EnumPRO.EnumSampleCategory.原样.getValue()))
                .map(DtoSample::getId).collect(Collectors.toList());
        samples = samples.stream().filter(p -> ySampleIds.contains(p.getId()) || ySampleIds.contains(p.getAssociateSampleId())).collect(Collectors.toList());
        List<String> sampleIds = samples.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds);
        List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
        List<DtoTestQCRemindTemp> remindList = testIds.size() > 0 ? testQCRemindConfig2TestService.findByTestIds(testIds) : new ArrayList<>();
        List<DtoQualityRemind> qrList = new ArrayList<>();
        //对分析数据按照测试项目id分组
        analyseDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getTestId, Collectors.toList())).forEach((testId, testDatas) -> {
            List<String> testSampleIds = testDatas.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
            Integer itemCount = (int) ySampleIds.stream().filter(testSampleIds::contains).distinct().count();
            List<DtoTestQCRemindTemp> remindTemps = remindList.stream().filter(p -> p.getTestId().equals(testId)
                    && p.getQcGrade().equals(EnumLIM.EnumQCGrade.外部质控.getValue())).collect(Collectors.toList());
            for (DtoTestQCRemindTemp remindTemp : remindTemps) {
                Double num = Math.ceil(1.0 * itemCount * remindTemp.getQcRemindPercent() / 100.0);
                Integer zkCount = (int) testDatas.stream().filter(p -> p.getQcGrade().equals(EnumLIM.EnumQCGrade.外部质控.getValue())
                        && p.getQcType().equals(remindTemp.getQcType())).map(DtoAnalyseData::getQcId).distinct().count();
                if (remindTemp.getQcType().equals(new QualityParallel().qcTypeValue())) {
                    zkCount = (int) testDatas.stream().filter(p -> p.getQcGrade().equals(EnumLIM.EnumQCGrade.外部质控.getValue())
                            && (p.getQcType().equals(new QualityCipherParallel().qcTypeValue()))
                            || p.getQcType().equals(remindTemp.getQcType())).map(DtoAnalyseData::getQcId).distinct().count();
                }
                if (zkCount < num) {
                    DtoQualityRemind qr = new DtoQualityRemind();
                    qr.setRedAnalyzeItemName(testDatas.get(0).getRedAnalyzeItemName());
                    qr.setQcType(remindTemp.getQcType());
                    qr.setItemCount(itemCount);
                    qr.setZkCount(zkCount);
                    qr.setQcRemindPercent(remindTemp.getQcRemindPercent());
                    qr.setDiffCount(num.intValue() - zkCount);
                    qrList.add(qr);
                }
            }
        });
        qrList.sort(Comparator.comparing(DtoQualityRemind::getRedAnalyzeItemName).thenComparing(Comparator.comparing(DtoQualityRemind::getQcType)));
        return qrList;
    }

    @Override
    public List<DtoQualityRemind> findByWorkSheetFolderId(String workSheetFolderId) {
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findByWorkSheetFolderIdAndIsDeletedFalse(workSheetFolderId);

        //筛选原样
        List<String> sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
        List<DtoSample> samples = sampleIds.size() > 0 ? sampleRepository.findAll(sampleIds) : new ArrayList<>();
        List<DtoSample> ySamples = samples.stream().filter(p -> p.getSampleCategory().equals(EnumPRO.EnumSampleCategory.原样.getValue())).collect(Collectors.toList());

        List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
        List<DtoTestQCRemindTemp> remindList = testIds.size() > 0 ? testQCRemindConfig2TestService.findByTestIds(testIds) : new ArrayList<>();
        List<DtoQualityRemind> qrList = new ArrayList<>();
        //对分析数据按照测试项目id分组
        analyseDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getTestId, Collectors.toList())).forEach((testId, testDatas) -> {
            List<String> testSampleIds = testDatas.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
            Integer itemCount = (int) ySamples.stream().filter(p -> testSampleIds.contains(p.getId())).distinct().count();
            List<DtoTestQCRemindTemp> remindTemps = remindList.stream().filter(p -> p.getTestId().equals(testId)
                    && p.getQcGrade().equals(EnumLIM.EnumQCGrade.内部质控.getValue())).collect(Collectors.toList());
            for (DtoTestQCRemindTemp remindTemp : remindTemps) {
                Double num = Math.ceil(1.0 * itemCount * remindTemp.getQcRemindPercent() / 100.0);
                Integer zkCount = (int) testDatas.stream().filter(p -> p.getQcGrade().equals(EnumLIM.EnumQCGrade.内部质控.getValue())
                        && p.getQcType().equals(remindTemp.getQcType())).map(DtoAnalyseData::getQcId).distinct().count();
                if (zkCount < num) {
                    DtoQualityRemind qr = new DtoQualityRemind();
                    qr.setRedAnalyzeItemName(testDatas.get(0).getRedAnalyzeItemName());
                    qr.setQcType(remindTemp.getQcType());
                    qr.setItemCount(itemCount);
                    qr.setZkCount(zkCount);
                    qr.setQcRemindPercent(remindTemp.getQcRemindPercent());
                    qr.setDiffCount(num.intValue() - zkCount);
                    qrList.add(qr);
                }
            }
        });
        qrList.sort(Comparator.comparing(DtoQualityRemind::getRedAnalyzeItemName).thenComparing(Comparator.comparing(DtoQualityRemind::getQcType)));
        return qrList;
    }
}
