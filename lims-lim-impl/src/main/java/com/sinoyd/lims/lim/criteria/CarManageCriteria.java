package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 车辆管理
 * <AUTHOR> 修改：徐肖波
 * @version V1.0.0 2019/3/11
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CarManageCriteria extends BaseCriteria {

    /**
     * 车辆类型（空Guid代表所有）
     */
    private String carType;

    /**
     * 负责人id（空Guid代表所有）
     */
    private String managerId;

    /**
     * 关键字（车辆型号、车牌号码）
     */
    private String key;

    /**
     * 状态
     */
    private String state;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(key)) {
            condition.append(" and (carCode like :key or carModel like :key)");
            values.put("key", "%" + this.key + "%");
        }
        if (StringUtils.isNotNullAndEmpty(carType)
                && !UUIDHelper.GUID_EMPTY.equals(this.carType)) {
            condition.append(" and (carType = :carType)");
            values.put("carType", this.carType);
        }
        if (StringUtils.isNotNullAndEmpty(managerId)
                && !UUIDHelper.GUID_EMPTY.equals(this.managerId)) {
            condition.append(" and (managerId = :managerId)");
            values.put("managerId", this.managerId);
        }
        if (StringUtil.isNotEmpty(state)) {
            if (state.contains(",")) {
                condition.append(" and state in :state");
                List<String> stateList = Arrays.stream(this.state.split(",")).collect(Collectors.toList());
                List<Integer> stateInts = new ArrayList<>();
                for (String state : stateList) {
                    stateInts.add(Integer.valueOf(state));
                }
                values.put("state", stateInts);
            } else {
                condition.append(" and state = :state");
                values.put("state", Integer.valueOf(this.state));
            }
        }
        return condition.toString();
    }
}