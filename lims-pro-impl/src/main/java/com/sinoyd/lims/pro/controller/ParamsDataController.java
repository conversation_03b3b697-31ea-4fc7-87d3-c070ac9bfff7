package com.sinoyd.lims.pro.controller;

import com.sinoyd.lims.pro.dto.customer.DtoGroupParamsSyncVo;
import com.sinoyd.lims.pro.dto.customer.DtoSampleInfo;
import com.sinoyd.lims.pro.dto.customer.DtoSampleItemParams;
import com.sinoyd.lims.pro.dto.customer.DtoSampleParamsTemp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.ParamsDataService;
import com.sinoyd.lims.pro.criteria.ParamsDataCriteria;
import com.sinoyd.lims.pro.dto.DtoParamsData;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;
import java.util.Map;


/**
 * ParamsData服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: ParamsData服务")
 @RestController
 @RequestMapping("api/pro/paramsData")
 public class ParamsDataController extends BaseJpaController<DtoParamsData, String,ParamsDataService> {


    /**
     * 分页动态条件查询ParamsData
     *
     * @param paramsDataCriteria 条件参数
     * @return RestResponse<List < ParamsData>>
     */
    @ApiOperation(value = "分页动态条件查询ParamsData", notes = "分页动态条件查询ParamsData")
    @GetMapping
    public RestResponse<List<DtoParamsData>> findByPage(ParamsDataCriteria paramsDataCriteria) {
        PageBean<DtoParamsData> pageBean = super.getPageBean();
        RestResponse<List<DtoParamsData>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, paramsDataCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询ParamsData
     *
     * @param id 主键id
     * @return RestResponse<DtoParamsData>
     */
    @ApiOperation(value = "按主键查询ParamsData", notes = "按主键查询ParamsData")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoParamsData> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoParamsData> restResponse = new RestResponse<>();
        DtoParamsData paramsData = service.findOne(id);
        restResponse.setData(paramsData);
        restResponse.setRestStatus(StringUtil.isNull(paramsData) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增ParamsData
     *
     * @param paramsData 实体列表
     * @return RestResponse<DtoParamsData>
     */
    @ApiOperation(value = "新增ParamsData", notes = "新增ParamsData")
    @PostMapping
    public RestResponse<DtoParamsData> create(@RequestBody @Validated DtoParamsData paramsData) {
        RestResponse<DtoParamsData> restResponse = new RestResponse<>();
        restResponse.setData(service.save(paramsData));
        return restResponse;
    }

    /**
     * 新增ParamsData
     *
     * @param paramsData 实体列表
     * @return RestResponse<DtoParamsData>
     */
    @ApiOperation(value = "修改ParamsData", notes = "修改ParamsData")
    @PutMapping
    public RestResponse<DtoParamsData> update(@RequestBody @Validated DtoParamsData paramsData) {
        RestResponse<DtoParamsData> restResponse = new RestResponse<>();
        restResponse.setData(service.update(paramsData));
        return restResponse;
    }

    /**
     * "根据id批量删除ParamsData
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除ParamsData", notes = "根据id批量删除ParamsData")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 保存样品参数数据
     *
     * @param temp 实体
     * @return RestResponse<DtoParamsData>
     */
    @ApiOperation(value = "保存样品参数数据", notes = "保存样品参数数据")
    @PostMapping("/sampleInfo")
    public RestResponse<Boolean> saveSampleParamsData(@RequestBody DtoSampleParamsTemp temp) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.saveSampleParamsData(temp);
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 计算样品参数数据
     *
     * @param info 实体
     * @return RestResponse<DtoParamsData>
     */
    @ApiOperation(value = "计算样品参数数据", notes = "计算样品参数数据")
    @PostMapping("/calculate")
    public RestResponse<List<Map<String, Object>>> calculate(@RequestBody DtoSampleInfo info) {
        RestResponse<List<Map<String, Object>>> restResponse = new RestResponse<>();
        restResponse.setData(service.calculateParamsData(info));
        return restResponse;
    }


    /**
     * 保存样品分析项目参数
     *
     * @param dto 实体
     * @return RestResponse<DtoParamsData>
     */
    @ApiOperation(value = "保存样品分析项目参数", notes = "保存样品分析项目参数")
    @PostMapping("/analyzeItem")
    public RestResponse<Boolean> saveAnalyzeItemParamsData(@RequestBody DtoSampleItemParams dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.saveAnalyzeItemParamsData(dto);
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 参数异常数据处理
     *
     * @return RestResponse<DtoParamsData>
     */
    @ApiOperation(value = "参数异常数据处理", notes = "参数异常数据处理")
    @PostMapping("/exceptionalData")
    public RestResponse<Boolean> exceptionalData() {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.exceptionalData();
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 同步分组参数数据
     *
     * @return RestResponse<Void>
     */
    @ApiOperation(value = "同步分组参数数据", notes = "同步分组参数数据")
    @PostMapping("/syncGroupParams")
    public RestResponse<Void> syncGroupParams(@RequestBody DtoGroupParamsSyncVo vo) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.syncGroupParams(vo.getGroupId(), vo.getParamsConfigIds(), vo.getSampleList());
        return restResponse;
    }
}