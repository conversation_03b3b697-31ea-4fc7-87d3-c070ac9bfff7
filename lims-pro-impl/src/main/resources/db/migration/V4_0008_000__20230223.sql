
drop index IX_TB_PRO_AnalyseData3 on tb_pro_analysedata;

create index IX_TB_PRO_AnalyseData3
    on tb_pro_analysedata (orgId, sampleId, isDeleted, isOutsourcing, isCompleteField, workSheetFolderId, testId);

DROP TABLE IF EXISTS TB_PRO_OutSourceData;
CREATE TABLE TB_PRO_OutSourceData  (
 id varchar(50) NOT NULL COMMENT '主键',
 analyseDataId varchar(50) NOT NULL COMMENT '分析数据id',
 analyzeMethodName varchar(255)  NULL DEFAULT NULL COMMENT '分析方法名称',
 analyzeMethodId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '分析方法id',
 testValue varchar(50) NULL DEFAULT NULL COMMENT '出证结果',
 dimensionName varchar(50) NULL DEFAULT NULL COMMENT '量纲名称',
 dimensionId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '量纲id',
 state int NOT NULL DEFAULT 0 COMMENT '状态（0未确认 1已确认）',
 orgId varchar(50) NOT NULL COMMENT '组织机构id',
 domainId varchar(50) NOT NULL COMMENT '组织机构id',
 creator varchar(50) NOT NULL COMMENT '创建人',
 createDate datetime NOT NULL COMMENT '创建时间',
 modifier varchar(50) NOT NULL COMMENT '修改人',
 modifyDate datetime NOT NULL COMMENT '修改时间',
 PRIMARY KEY (id)
) COMMENT = '分包数据表';