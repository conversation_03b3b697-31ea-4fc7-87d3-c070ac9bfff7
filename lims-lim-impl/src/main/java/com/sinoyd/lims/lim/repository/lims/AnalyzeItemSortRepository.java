package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoAnalyzeItemSort;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * 分析项目排序仓储
 *
 * <AUTHOR>
 * @version V1.0.0 2019-05-14
 * @since V100R001
 */
public interface AnalyzeItemSortRepository extends IBaseJpaPhysicalDeleteRepository<DtoAnalyzeItemSort, String> {

    /**
     * 根据id和名称查重
     *
     * @param id
     * @param sortName
     * @return
     */
    @Query("select count(p.id) from DtoAnalyzeItemSort p where p.id <> :id and p.sortName = :sortName")
    Integer getByIdAndName(@Param("id") String id, @Param("sortName") String sortName);

    /**
     * 根据排序名查找第一个
     *
     * @param sortName 排序名
     * @return DtoAnalyzeItemSort实体
     */
    DtoAnalyzeItemSort findBySortName(String sortName);
}