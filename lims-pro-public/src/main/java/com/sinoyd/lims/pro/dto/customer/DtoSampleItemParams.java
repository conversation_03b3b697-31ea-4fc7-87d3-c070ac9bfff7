package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 样品分析项目参数保存传参
 * <AUTHOR>
 * @version V1.0.0 2019/12/20
 * @since V100R001
 */
@Data
public class DtoSampleItemParams {
    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 参数配置
     */
    private List<DtoParamsConfig> paramsConfig = new ArrayList<>();

    /**
     * 样品id集合
     */
    private List<String> sampleIds = new ArrayList<>();

    /**
     * 测试项目id集合
     */
    private List<String> analyseItemIds = new ArrayList<>();
}
