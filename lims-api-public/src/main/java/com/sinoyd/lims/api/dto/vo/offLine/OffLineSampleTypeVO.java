package com.sinoyd.lims.api.dto.vo.offLine;

import lombok.Data;

/**
 * 离线数据录入（离线数据表）：检测类型表实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/09/23
 */
@Data
public class OffLineSampleTypeVO {

    /**
     * 主键ID，UUID格式
     */
    private String id;

    /**
     * 父id
     */
    private String parentId;

    /**
     * 类型编号
     */
    private String typeCode;

    /**
     * 类型名称
     */
    private String typeName;

    /**
     * 简称
     */
    private String shortName;

    /**
     * 排序值
     */
    private Integer orderNum;

    /**
     * 样品分类
     */
    private Integer category;

    /**
     * 所属机构
     */
    private String orgId;
}
