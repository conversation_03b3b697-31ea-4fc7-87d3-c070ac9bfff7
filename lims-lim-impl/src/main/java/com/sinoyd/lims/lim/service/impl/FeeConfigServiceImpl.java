package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.lims.lim.dto.lims.DtoFeeConfig;
import com.sinoyd.lims.lim.repository.lims.FeeConfigRepository;
import com.sinoyd.lims.lim.service.FeeConfigService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import org.springframework.stereotype.Service;


/**
 * FeeConfig操作接口
 * <AUTHOR>
 * @version V1.0.0 2022/5/18
 * @since V100R001
 */
 @Service
public class FeeConfigServiceImpl extends BaseJpaServiceImpl<DtoFeeConfig,String,FeeConfigRepository> implements FeeConfigService {

    @Override
    public void findByPage(PageBean<DtoFeeConfig> pb, BaseCriteria feeConfigCriteria) {
        pb.setEntityName("DtoFeeConfig a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, feeConfigCriteria);
    }
}