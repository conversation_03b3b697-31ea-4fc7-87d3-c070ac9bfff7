package com.sinoyd.lims.lim.criteria;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;

/**
 * 环境管理-环境信息
 * <AUTHOR>
 * @version v1.0.0 2019/3/12
 * @since V100R001
 */
public class EnvironmentalManageCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 检索开始时间
     */
    private String dtBegin;
    /**
     * 检索结束时间
     */
    private String dtEnd;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(dtBegin)) {
            Date from = DateUtil.stringToDate(this.dtBegin, DateUtil.YEAR);
            condition.append(" and startDate >= :dtBegin");
            values.put("dtBegin", from);
        }
        if (StringUtils.isNotNullAndEmpty(dtEnd)) {
            Date to = DateUtil.stringToDate(this.dtEnd, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and startDate < :dtEnd");
            values.put("dtEnd", c.getTime());
        }
        return condition.toString();
    }

    /**
     * @return the dtEnd
     */
    public String getDtEnd() {
        return dtEnd;
    }

    /**
     * @param dtEnd the dtEnd to set
     */
    public void setDtEnd(String dtEnd) {
        this.dtEnd = dtEnd;
    }

    /**
     * @return the dtBegin
     */
    public String getDtBegin() {
        return dtBegin;
    }

    /**
     * @param dtBegin the dtBegin to set
     */
    public void setDtBegin(String dtBegin) {
        this.dtBegin = dtBegin;
    }
}