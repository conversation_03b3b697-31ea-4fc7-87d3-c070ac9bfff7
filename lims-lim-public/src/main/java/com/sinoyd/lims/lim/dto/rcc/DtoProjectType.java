package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.ProjectType;

import java.util.Map;

import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoProjectType实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/1
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_ProjectType") 
 @Where(clause = "isDeleted = 0")
 @Data
 @DynamicInsert
 @ToString
 public  class DtoProjectType extends ProjectType {
   private static final long serialVersionUID = 1L;

   
    /**
     * 配置结构
     */
    @Transient
    private Map configObject;
 }