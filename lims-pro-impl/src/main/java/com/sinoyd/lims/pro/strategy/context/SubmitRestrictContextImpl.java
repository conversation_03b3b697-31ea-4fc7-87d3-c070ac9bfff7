package com.sinoyd.lims.pro.strategy.context;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.dto.customer.DtoSubmitRestrictVo;
import com.sinoyd.lims.pro.strategy.strategy.submitRestrict.AbsSubmitRestrictStrategy;
import com.sinoyd.lims.strategy.context.SubmitRestrictContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Component
public class SubmitRestrictContextImpl implements SubmitRestrictContext {

    /**
     * 所有具体生成策略字典
     */
    private final Map<String, AbsSubmitRestrictStrategy> submitRestrictStrategyMap = new ConcurrentHashMap<>();

    @Autowired
    public SubmitRestrictContextImpl(Map<String, AbsSubmitRestrictStrategy> submitRestrictStrategyMap) {
        this.submitRestrictStrategyMap.putAll(submitRestrictStrategyMap);
    }

    @Override
    public List<DtoSubmitRestrictVo> submitJudgment(String beanName, Object objMap, String status) {
        if (!StringUtil.isNotNull(this.submitRestrictStrategyMap.get(beanName))) {
            throw new BaseException("调用方法不合法");
        }
        return this.submitRestrictStrategyMap.get(beanName).generateSubmitRestrict(objMap, status);
    }
}
