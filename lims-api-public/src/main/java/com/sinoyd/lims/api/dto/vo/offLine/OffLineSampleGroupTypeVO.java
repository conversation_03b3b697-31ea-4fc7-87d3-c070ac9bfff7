package com.sinoyd.lims.api.dto.vo.offLine;

import lombok.Data;

/**
 * 离线数据录入（离线数据表）：分组配置表实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/09/23
 */
@Data
public class OffLineSampleGroupTypeVO {

    /**
     * 主键ID，UUID格式
     */
    private String id;

    /**
     * 采样id
     */
    private String parentId;

    /**
     * 分组类型（1.分组规则，2.分组）
     */
    private Integer groupType;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 样品类型id
     */
    private String sampleTypeId;

    /**
     * 固定剂
     */
    private String fixer;

    /**
     * 容器名称
     */
    private String containerName;

    /**
     * 保存条件
     */
    private String saveCondition;

    /**
     * 体积类型
     */
    private String volumeType;

    /**
     * 前处理方式
     */
    private String pretreatmentMethod;

    /**
     * 排序值
     */
    private Integer orderNum;

    /**
     * 所属机构
     */
    private String orgId;
}
