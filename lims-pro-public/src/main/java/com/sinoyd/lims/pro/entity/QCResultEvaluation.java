package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;


/**
 * QCResultEvaluation实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="QCResultEvaluation")
 @Data
 public  class QCResultEvaluation implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  QCResultEvaluation() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 项目Id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("项目Id")
	private String projectId;
    
    /**
    * 质量控制情况
    */
    @Column(length=1000)
    @ApiModelProperty("质量控制情况")
    @Length(message = "质量控制情况{validation.message.length}", max = 1000)
	private String qcSituation;
    
    /**
    * 结果比较
    */
    @Column(length=1000)
    @ApiModelProperty("结果比较")
    @Length(message = "结果比较{validation.message.length}", max = 1000)
	private String resultCompare;
    
    /**
    * 评价结果
    */
    @Column(length=1000)
    @ApiModelProperty("评价结果")
    @Length(message = "评价结果{validation.message.length}", max = 1000)
	private String evaluationResult;
    
    /**
    * 评价人员
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("评价人员")
    //@NotBlank(message = "评价人员{validation.message.blank}")
    @Length(message = "评价人员{validation.message.length}", max = 50)
	private String createPerson;
    
    /**
    * 评价时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @ApiModelProperty("评价时间")
	private Date createTime;
    
    /**
    * 备注
    */
    @Column(length=1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
	private String remark;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
 }