package com.sinoyd.lims.pro.strategy.context.schemeSynchronization;

import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.dto.DtoSampleFolderTemplate;
import com.sinoyd.lims.pro.strategy.strategy.schemeSynchronization.sampleFolder.AbsSampleFolderStrategy;
import com.sinoyd.lims.strategy.context.schemeSynchronization.SampleFolderContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Component
public class SampleFolderContextImpl implements SampleFolderContext {

    /**
     * 所有具体生成策略字典
     */
    private final Map<String, AbsSampleFolderStrategy> sampleFolderStrategyMap = new ConcurrentHashMap<>();

    @Autowired
    public SampleFolderContextImpl(Map<String, AbsSampleFolderStrategy> sampleFolderStrategyMap) {
        this.sampleFolderStrategyMap.putAll(sampleFolderStrategyMap);
    }

    @Override
    public void synchronizationSampleFolder(Integer operateType, String projectId, List<DtoSampleFolderTemplate> folderTemplateList) {
        String beanName = EnumBase.EnumSchemeSynchronization.getBeanName(operateType);
        if (!StringUtil.isNotNull(this.sampleFolderStrategyMap.get(beanName + "SampleFolder"))) {
            throw new BaseException("调用方法不合法");
        }
        this.sampleFolderStrategyMap.get(beanName + "SampleFolder").synchronizationSampleFolder(projectId, folderTemplateList);
    }
}
