package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoReportFolderSortInfo;

import java.util.List;


/**
 * 电子报告点位排序访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/07
 * @since V100R001
 */
public interface ReportFolderSortInfoRepository extends IBaseJpaRepository<DtoReportFolderSortInfo, String> {

    /**
     * 根据报告id查找点位排序信息
     *
     * @param reportId 报告id
     * @return 点位排序信息
     */
    List<DtoReportFolderSortInfo> findByReportId(String reportId);

}