package com.sinoyd.base.service;

import java.util.List;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.base.dto.rcc.DtoIndustryType;
import com.sinoyd.base.dto.customer.TreeNode;;

/**
 * 行业类型接口
 * <AUTHOR>
 * @version V1.0.0 2018/12/14
 * @since V100R001
 */
public interface IndustryTypeService extends IBaseJpaService<DtoIndustryType, String> {

    // 注意：附件的获取列表，下载和删除使用文件管理的接口，附件的上传使用框架统一处理

    /**
     * 获取检测类型树 第一层为行业类型 第二层开始为当前行业类型下所对应的检测类型
     *
     * @return 返回树结构
     */
    List<TreeNode> tree();

    /**
     * 获取检测类型树 第一层为行业类型 第二层开始为当前行业类型下所对应的大类检测类型
     *
     * @return 返回树结构
     */
    List<TreeNode> bigTree();

    /**
     * 根据用户Id获取有假删的数据
     *
     * @param ids 主键ids
     * @return 返回带有假删的数据
     */
    List<DtoIndustryType> findAllDeleted(List<String> ids);


    /**
     * 含假删的信息
     *
     * @return 返回假删信息
     */
    List<DtoIndustryType> findAllDeleted();

    /**
     * 附件上传
     * @param id 标识
     * @return   对象
     */
    DtoIndustryType findAttachPath(String id);
}