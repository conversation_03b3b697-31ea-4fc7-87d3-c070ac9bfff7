package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.pro.dto.DtoOrderQuotation;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.List;


/**
 * OrderQuotation操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2022/5/18
 * @since V100R001
 */
public interface OrderQuotationService extends IBaseJpaService<DtoOrderQuotation, String> {

    /**
     * 根据订单id获取费用记录
     *
     * @param orderIds 订单id集合
     * @return 费用记录
     */
    List<DtoOrderQuotation> findByOrderIdIn(List<String> orderIds);

    /**
     * 根据订单获取费用记录
     *
     * @param orderId 订单id
     * @return 费用记录
     */
    DtoOrderQuotation findByOrderId(String orderId);
}