package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoSampleGroup;
import com.sinoyd.lims.pro.dto.DtoSampleGroup2Test;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
* SampleGroup2Test数据访问操作接口
* <AUTHOR>
* @version V1.0.0 2023/7/28
* @since V100R001
*/
public interface SampleGroup2TestRepository extends IBaseJpaPhysicalDeleteRepository<DtoSampleGroup2Test, String> {

    /**
     * 根据样品分组id删除
     * @param
     */
    @Modifying
    void deleteBySampleGroupIdIn(@Param("sampleGroupIds")List<String> sampleGroupIds);

    /**
     * 根据样品分组id查询
     *
     * @param sampleGroupIds 样品分组id
     * @return List<DtoSampleGroup2Test>
     */
    List<DtoSampleGroup2Test> findBySampleGroupIdIn(List<String> sampleGroupIds);
}
