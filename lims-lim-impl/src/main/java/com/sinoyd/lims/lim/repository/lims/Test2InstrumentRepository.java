package com.sinoyd.lims.lim.repository.lims;
import java.util.Collection;
import java.util.List;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoTest2Instrument;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

/**
 * 使用仪器仓储
 * <AUTHOR>
 * @version V1.0.0 2019/1/17
 * @since V100R001
 */
public interface Test2InstrumentRepository extends IBaseJpaPhysicalDeleteRepository<DtoTest2Instrument, String> 
{
    /**
     * 通过测试id,仪器id找到有没有重复的仪器
     * @param testId 测试项目id
     * @param insId 仪器id
     * @param useType
     * @return 返回查找到的数据数量
     */
    @Query("select count(p.id) from DtoTest2Instrument p where p.testId = :testId and p.instrumentId = :insId and p.useType = :useType")
    Integer getCountByNameAndId(@Param("testId")String testId,@Param("insId") String insId,@Param("useType")Integer useType);

    /**
     * 删除关联数据
     *
     * @param testId 测试项目id
     * @param insIds 仪器id数组
     * @param useType
     * @return 返回删除数量
     */
    @Transactional
    @Modifying
    @Query("delete from DtoTest2Instrument p where p.testId = :testId and p.instrumentId in :insIds and p.useType = :useType ")
    Integer deleteByTestIdAndInsIds(@Param("testId")String testId,@Param("insIds")Collection<String> insIds,@Param("useType")Integer useType);

    /**
     * 获取使用仪器集合
     * @param testId 测试项目id
     * @param useType 
     * @return 返回集合
     */
    List<DtoTest2Instrument> findByTestIdAndUseType(String testId,Integer useType);
   
}