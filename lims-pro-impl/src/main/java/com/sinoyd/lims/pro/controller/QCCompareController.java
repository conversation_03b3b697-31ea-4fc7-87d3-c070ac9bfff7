package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.pro.criteria.QCCompareStatisticsCriteria;
import com.sinoyd.lims.pro.dto.customer.DtoQCCompareCurve;
import com.sinoyd.lims.pro.service.QCCompareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


/**
 * qcCompare 质控比例统计
 * <AUTHOR>
 * @version V1.0.0 2020/02/11
 * @since V100R001
 */
@Api(tags = "示例: 质控比例统计服务")
@RestController
@RequestMapping("api/pro/qcCompare")
public class QCCompareController extends ExceptionHandlerController<QCCompareService> {

    @ApiOperation(value = "质控比例统计", notes = "质控比例统计")
    @GetMapping
    public RestResponse<List<DtoQCCompareCurve>> findAddQCData(QCCompareStatisticsCriteria qcCompareStatisticsCriteria) {
        RestResponse<List<DtoQCCompareCurve>> restResp = new RestResponse<>();
        List<DtoQCCompareCurve> list = service.findQCCompareStatistics(qcCompareStatisticsCriteria);
        restResp.setRestStatus(StringUtil.isEmpty(list) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(list);
        return restResp;
    }
}
