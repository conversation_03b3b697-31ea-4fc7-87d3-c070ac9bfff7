package com.sinoyd.base.dto.lims;

import com.sinoyd.base.entity.LogForDocument;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * DtoLogForAnalyzeMethod实体
 * <AUTHOR>
 * @version V1.0.0 2024/01/22
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_BASE_LogForDocument")
@Data
@DynamicInsert
public class DtoLogForDocument extends LogForDocument {

}
