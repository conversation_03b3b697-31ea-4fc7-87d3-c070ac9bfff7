DROP TABLE IF EXISTS `TB_LIM_ItemRelation`;
CREATE TABLE `TB_LIM_ItemRelation`  (
     `id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'id',
     `leftFormula` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '分析项目Id',
     `rightFormula` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '分析项目名称',
     `formula` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '公式',
     `symbolType` int(11) NOT NULL DEFAULT -1 COMMENT '公式（1.等于，2.大于，3.小于等）',
     `configDate` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '配置日期',
     `type` int(11) NOT NULL DEFAULT -1 COMMENT '类型（枚举EnumAnalyzeItemRelationType：1.自检，2.上报）',
     `orgId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
     `creator` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
     `createDate` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
     `domainId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
     `modifier` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
     `modifyDate` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '修改时间',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

DROP TABLE IF EXISTS `TB_LIM_ItemRelationParams`;
CREATE TABLE `TB_LIM_ItemRelationParams`  (
      `id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'id',
      `relationId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '分析项目关系',
      `analyzeItemId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '分析项目Id',
      `analyzeItemName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '分析项目名称',
      `position` int(11) NOT NULL DEFAULT -1 COMMENT '位置（1.左，2.右，-1.没有）',
      `orderNum` int(11) NOT NULL DEFAULT 0 COMMENT '排序值（预留：列表显示排序用）',
      `orgId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
      `creator` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
      `createDate` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
      `domainId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
      `modifier` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
      `modifyDate` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '修改时间',
      PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;