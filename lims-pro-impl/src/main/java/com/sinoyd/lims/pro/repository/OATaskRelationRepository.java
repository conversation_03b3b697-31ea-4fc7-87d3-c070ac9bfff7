package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoOATaskRelation;
import com.sinoyd.lims.pro.dto.DtoOrderQuotation;

import java.util.Collection;
import java.util.List;

/**
 * 审批任务关联业务数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019-03-26
 * @since V100R001
 */
public interface OATaskRelationRepository extends IBaseJpaPhysicalDeleteRepository<DtoOATaskRelation, String>
{
    /**
     * 根据关联id列表查询
     * @param objIdList 关联id列
     * @return 关联业务数据
     */
    List<DtoOATaskRelation> findByObjectIdIn(List<String> objIdList);

    /**
     * 根据任务列表查询
     * @param taskIds 关联id列
     * @return 关联业务数据
     */
    List<DtoOATaskRelation> findByTaskIdIn(Collection<?> taskIds);
}
