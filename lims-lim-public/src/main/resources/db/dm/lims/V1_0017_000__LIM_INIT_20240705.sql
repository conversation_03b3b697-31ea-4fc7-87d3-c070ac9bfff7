CREATE TABLE "TB_LIM_ANALYZEITEMRELATION"
(
    "id"              VARCHAR(50)                                                 NOT NULL,
    "analyzeItemId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "analyzeItemName" VARCHAR(50),
    "formula"         VARCHAR(500),
    "configDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "type"            INT          DEFAULT (-1)                                   NOT NULL,
    "orgId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT               CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMRELATION"."analyzeItemId" IS '分析项目Id';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMRELATION"."analyzeItemName" IS '分析项目名称';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMRELATION"."configDate" IS '配置日期';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMRELATION"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMRELATION"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMRELATION"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMRELATION"."formula" IS '公式';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMRELATION"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMRELATION"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMRELATION"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMRELATION"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMRELATION"."type" IS '类型（枚举EnumAnalyzeItemRelationType：1.自检，2.上报）';


CREATE TABLE "TB_LIM_ANALYZEITEMRELATIONPARAMS"
(
    "id"              VARCHAR(50)                                                 NOT NULL,
    "relationId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "analyzeItemId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "analyzeItemName" VARCHAR(50),
    "orderNum"        INT          DEFAULT 0                                      NOT NULL,
    "orgId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT               CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMRELATIONPARAMS"."analyzeItemId" IS '分析项目Id';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMRELATIONPARAMS"."analyzeItemName" IS '分析项目名称';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMRELATIONPARAMS"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMRELATIONPARAMS"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMRELATIONPARAMS"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMRELATIONPARAMS"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMRELATIONPARAMS"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMRELATIONPARAMS"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMRELATIONPARAMS"."orderNum" IS '排序值（预留：列表显示排序用）';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMRELATIONPARAMS"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMRELATIONPARAMS"."relationId" IS '分析项目关系';


CREATE TABLE "TB_LIM_ANALYZEITEMSORT"
(
    "id"           VARCHAR(50)                                                 NOT NULL,
    "sortName"     VARCHAR(250),
    "sampleTypeId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "orderNum"     INT          DEFAULT 0                                      NOT NULL,
    "sortCode"     VARCHAR(250),
    "type"         INT          DEFAULT 3                                      NOT NULL,
    "orgId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "remark"       VARCHAR(255),
    NOT            CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMSORT"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMSORT"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMSORT"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMSORT"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMSORT"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMSORT"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMSORT"."orderNum" IS '排序值';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMSORT"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMSORT"."sampleTypeId" IS '样品类型id（预留：样品类型小类）';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMSORT"."sortCode" IS '编号（预留：可能会有其他标识时借用）';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMSORT"."sortName" IS '排序名称';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMSORT"."type" IS '类型（预留：枚举EnumAnalyzeItemSortType：1.只显示排序 2.只显示测试方案 3.全部显示）';


CREATE TABLE "TB_LIM_ANALYZEITEMSORTDETAIL"
(
    "id"              VARCHAR(50)                                                NOT NULL,
    "sortId"          VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "analyzeItemId"   VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "analyzeItemName" VARCHAR(50),
    "orderNum"        INT         DEFAULT 0                                      NOT NULL,
    "orgId"           VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    NOT               CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMSORTDETAIL"."analyzeItemId" IS '分析项目Id';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMSORTDETAIL"."analyzeItemName" IS '分析项目名称';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMSORTDETAIL"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMSORTDETAIL"."orderNum" IS '排序值';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMSORTDETAIL"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_ANALYZEITEMSORTDETAIL"."sortId" IS '分析项目排序Id';


CREATE TABLE "TB_LIM_ANALYZEMETHOD"
(
    "id"                  VARCHAR(50)                                                 NOT NULL,
    "methodName"          VARCHAR(255),
    "countryStandard"     VARCHAR(200),
    "isDeleted"           BIT          DEFAULT 0                                      NOT NULL,
    "effectiveDate"       TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "methodCode"          VARCHAR(50),
    "isCompleteTogether"  BIT          DEFAULT 0                                      NOT NULL,
    "isControlled"        BIT          DEFAULT 0                                      NOT NULL,
    "remark"              VARCHAR(1000),
    "parentId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "countryStandardName" VARCHAR(100),
    "yearSn"              VARCHAR(50),
    "effectiveDays"       INT          DEFAULT (-1)                                   NOT NULL,
    "warningDays"         INT          DEFAULT (-1)                                   NOT NULL,
    "isInforce"           BIT          DEFAULT 0                                      NOT NULL,
    "orgId"               VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"          TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"          TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "isInputBySample"     BIT          DEFAULT 0                                      NOT NULL,
    "alias"               VARCHAR(255),
    "isCrossDay"          BIT          DEFAULT 0                                      NOT NULL,
    "isPreparation"       BIT          DEFAULT 0                                      NOT NULL,
    "preparedMethod"      VARCHAR(200),
    "status"              INT          DEFAULT 1                                      NOT NULL,
    NOT                   CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHOD"."alias" IS '别名';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHOD"."countryStandardName" IS '国家标准名称（预留）';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHOD"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHOD"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHOD"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHOD"."effectiveDate" IS '标准实施日期';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHOD"."effectiveDays" IS '有效天数（预留）';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHOD"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHOD"."isCompleteTogether" IS '是否可以同时完成';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHOD"."isControlled" IS '是否受控';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHOD"."isCrossDay" IS '是否跨天完成';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHOD"."isDeleted" IS '是否删除';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHOD"."isInforce" IS '是否现行有效（预留）';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHOD"."isInputBySample" IS '是否按样品录入 1：是  0：否';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHOD"."isPreparation" IS '是否制备';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHOD"."methodCode" IS '受控编号';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHOD"."methodName" IS '方法名称';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHOD"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHOD"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHOD"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHOD"."parentId" IS '父级Id（Guid）（预留，例：XXX方法，拉伸测试）';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHOD"."preparedMethod" IS '制备方法';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHOD"."remark" IS '备注';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHOD"."status" IS '方法状态EnumAnalyzeMethodStatus：启用(1),停用(2),废止(3)';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHOD"."warningDays" IS '警告天数（预留）';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHOD"."yearSn" IS '年度（预留）';


CREATE TABLE "TB_LIM_ANALYZEMETHODREAGENTCONFIG"
(
    "id"                    VARCHAR(50)                                                 NOT NULL,
    "context"               VARCHAR(1000),
    "analyzeMethodId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "reagentName"           VARCHAR(100),
    "reagentSpecification"  VARCHAR(500),
    "configurationSolution" VARCHAR(500),
    "configDate"            TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "expiryDate"            TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "orderNum"              INT          DEFAULT 0                                      NOT NULL,
    "course"                VARCHAR(1000),
    "opinion"               VARCHAR(1000),
    "isDeleted"             BIT          DEFAULT 0                                      NOT NULL,
    "orgId"                 VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"               VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"            TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"            TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "reagentType"           INT          DEFAULT 0                                      NOT NULL,
    "concentration"         VARCHAR(200),
    "reagentVolumeQuality"  VARCHAR(200),
    "constantVolume"        VARCHAR(200),
    "diluent"               VARCHAR(200) DEFAULT '',
    "suitItem"              VARCHAR(200) DEFAULT '',
    NOT                     CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_ANALYZEMETHODREAGENTCONFIG" IS '试剂配置';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHODREAGENTCONFIG"."analyzeMethodId" IS '分析方法id（Guid）';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHODREAGENTCONFIG"."concentration" IS '浓度';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHODREAGENTCONFIG"."configDate" IS '配置日期';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHODREAGENTCONFIG"."constantVolume" IS '定容体积';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHODREAGENTCONFIG"."context" IS '需求的配置过程';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHODREAGENTCONFIG"."course" IS '稀释过程记录';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHODREAGENTCONFIG"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHODREAGENTCONFIG"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHODREAGENTCONFIG"."diluent" IS '稀释液';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHODREAGENTCONFIG"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHODREAGENTCONFIG"."expiryDate" IS '有效期';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHODREAGENTCONFIG"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHODREAGENTCONFIG"."isDeleted" IS '假删字段';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHODREAGENTCONFIG"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHODREAGENTCONFIG"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHODREAGENTCONFIG"."opinion" IS '其他情况';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHODREAGENTCONFIG"."orderNum" IS '排序值（预留）';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHODREAGENTCONFIG"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHODREAGENTCONFIG"."reagentName" IS '试剂名称';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHODREAGENTCONFIG"."reagentType" IS '试剂类型（1：一般试剂 2：标准溶液）';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHODREAGENTCONFIG"."reagentVolumeQuality" IS '试剂体积或质量';
COMMENT
ON COLUMN "TB_LIM_ANALYZEMETHODREAGENTCONFIG"."suitItem" IS '适用项目';


CREATE TABLE "TB_LIM_APPCONFIG"
(
    "id"           VARCHAR(50)  NOT NULL,
    "name"         VARCHAR(100) NOT NULL,
    "code"         VARCHAR(50)  NOT NULL,
    "linkAddress"  VARCHAR(1000),
    "roleId"       VARCHAR(1000),
    "status"       BIT DEFAULT 0,
    "orderNum"     INT DEFAULT 0,
    "remark"       VARCHAR(1000),
    "orgId"        VARCHAR(50)  NOT NULL,
    "creator"      VARCHAR(50)  NOT NULL,
    "createDate"   TIMESTAMP(0) NOT NULL,
    "domainId"     VARCHAR(50)  NOT NULL,
    "modifier"     VARCHAR(50)  NOT NULL,
    "modifyDate"   TIMESTAMP(0) NOT NULL,
    "type"         VARCHAR(50),
    "typeOrderNum" INT          NOT NULL,
    NOT            CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_APPCONFIG" IS 'app应用配置表';
COMMENT
ON COLUMN "TB_LIM_APPCONFIG"."code" IS '应用编码';
COMMENT
ON COLUMN "TB_LIM_APPCONFIG"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_APPCONFIG"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_APPCONFIG"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_APPCONFIG"."id" IS '主键';
COMMENT
ON COLUMN "TB_LIM_APPCONFIG"."linkAddress" IS '链接地址';
COMMENT
ON COLUMN "TB_LIM_APPCONFIG"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_APPCONFIG"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_APPCONFIG"."name" IS '应用名称';
COMMENT
ON COLUMN "TB_LIM_APPCONFIG"."orderNum" IS '排序值';
COMMENT
ON COLUMN "TB_LIM_APPCONFIG"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_APPCONFIG"."remark" IS '备注';
COMMENT
ON COLUMN "TB_LIM_APPCONFIG"."roleId" IS '角色';
COMMENT
ON COLUMN "TB_LIM_APPCONFIG"."status" IS '启用状态';
COMMENT
ON COLUMN "TB_LIM_APPCONFIG"."type" IS '应用分类';
COMMENT
ON COLUMN "TB_LIM_APPCONFIG"."typeOrderNum" IS '类型排序值';


CREATE TABLE "TB_LIM_CALENDARDATE"
(
    "id"           VARCHAR(50)  NOT NULL,
    "holidayName"  VARCHAR(50),
    "calendarDate" DATE         NOT NULL,
    "weekday"      INT          NOT NULL,
    "type"         INT          NOT NULL,
    "orgId"        VARCHAR(50)  NOT NULL,
    "creator"      VARCHAR(50)  NOT NULL,
    "createDate"   TIMESTAMP(0) NOT NULL,
    "domainId"     VARCHAR(50)  NOT NULL,
    "modifier"     VARCHAR(50)  NOT NULL,
    "modifyDate"   TIMESTAMP(0) NOT NULL,
    NOT            CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_CALENDARDATE" IS '日历日期';
COMMENT
ON COLUMN "TB_LIM_CALENDARDATE"."calendarDate" IS '日历日期';
COMMENT
ON COLUMN "TB_LIM_CALENDARDATE"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_CALENDARDATE"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_CALENDARDATE"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_CALENDARDATE"."holidayName" IS '节假日名称';
COMMENT
ON COLUMN "TB_LIM_CALENDARDATE"."id" IS '主键';
COMMENT
ON COLUMN "TB_LIM_CALENDARDATE"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_CALENDARDATE"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_CALENDARDATE"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_CALENDARDATE"."type" IS '类型（0：工作日，1：休息日）';
COMMENT
ON COLUMN "TB_LIM_CALENDARDATE"."weekday" IS '星期数（1：周日，2：周一，3：周二 ，4：周三，5：周四，6：周五，7：周六）';


CREATE TABLE "TB_LIM_CARCONSUMERRECORD"
(
    "id"         VARCHAR(50)                                                 NOT NULL,
    "carId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "type"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "amount"     DECIMAL(18, 2)                                              NOT NULL,
    "salesManId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "salseDate"  TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "content"    VARCHAR(1000),
    "orgId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT          CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_CARCONSUMERRECORD"."amount" IS '消费金额';
COMMENT
ON COLUMN "TB_LIM_CARCONSUMERRECORD"."carId" IS '车辆id（Guid）';
COMMENT
ON COLUMN "TB_LIM_CARCONSUMERRECORD"."content" IS '消费说明';
COMMENT
ON COLUMN "TB_LIM_CARCONSUMERRECORD"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_CARCONSUMERRECORD"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_CARCONSUMERRECORD"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_CARCONSUMERRECORD"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_CARCONSUMERRECORD"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_CARCONSUMERRECORD"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_CARCONSUMERRECORD"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_CARCONSUMERRECORD"."salesManId" IS '消费人员id（Guid）';
COMMENT
ON COLUMN "TB_LIM_CARCONSUMERRECORD"."salseDate" IS '消费日期';
COMMENT
ON COLUMN "TB_LIM_CARCONSUMERRECORD"."type" IS '记录类型（常量LIM_CarConsumerRecordType：维修、保养、ETC、年检、车险、GIS流量费、其他）';


CREATE TABLE "TB_LIM_CARGPSCONFIG"
(
    "id"           VARCHAR(50)                                                   NOT NULL,
    "carId"        VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "gpsModel"     VARCHAR(50)                                                   NOT NULL,
    "gpsCode"      VARCHAR(50)                                                   NOT NULL,
    "gpsPwd"       VARCHAR(50),
    "simNumber"    VARCHAR(50),
    "rateLimited"  VARCHAR(50),
    "rangeConfig"  DECIMAL(18, 2) DEFAULT 500.                                   NOT NULL,
    "gpsFrequency" VARCHAR(20)    DEFAULT '5',
    "orgId"        VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"      VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"   TIMESTAMP(0)   DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"     VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"     VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"   TIMESTAMP(0)   DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT            CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_CARGPSCONFIG"."carId" IS '车辆id（Guid）';
COMMENT
ON COLUMN "TB_LIM_CARGPSCONFIG"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_CARGPSCONFIG"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_CARGPSCONFIG"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_CARGPSCONFIG"."gpsCode" IS 'gps编号（MN号）';
COMMENT
ON COLUMN "TB_LIM_CARGPSCONFIG"."gpsFrequency" IS 'gps的接受频率(min)';
COMMENT
ON COLUMN "TB_LIM_CARGPSCONFIG"."gpsModel" IS 'gps型号';
COMMENT
ON COLUMN "TB_LIM_CARGPSCONFIG"."gpsPwd" IS 'gps密码';
COMMENT
ON COLUMN "TB_LIM_CARGPSCONFIG"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_CARGPSCONFIG"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_CARGPSCONFIG"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_CARGPSCONFIG"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_CARGPSCONFIG"."rangeConfig" IS '采样地点范围(m)';
COMMENT
ON COLUMN "TB_LIM_CARGPSCONFIG"."rateLimited" IS '限速';
COMMENT
ON COLUMN "TB_LIM_CARGPSCONFIG"."simNumber" IS 'SIM号码';


CREATE TABLE "TB_LIM_CARGPSDATA"
(
    "id"          VARCHAR(50)                                                 NOT NULL,
    "carId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "carSn"       VARCHAR(50),
    "drivingLine" CLOB,
    "drivingTime" DECIMAL(8, 2)                                               NOT NULL,
    "mileage"     DECIMAL(8, 2)                                               NOT NULL,
    "avgSpeed"    DECIMAL(8, 2)                                               NOT NULL,
    "maxSpeed"    DECIMAL(8, 2)                                               NOT NULL,
    "beginTime"   TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "endTime"     TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "fuelCon"     DECIMAL(8, 2)                                               NOT NULL,
    "userId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "userName"    VARCHAR(50),
    "isSkeptical" INT          DEFAULT 0                                      NOT NULL,
    "orgId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    NOT           CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_CARGPSDATA"."avgSpeed" IS '平均速度';
COMMENT
ON COLUMN "TB_LIM_CARGPSDATA"."beginTime" IS '开始时间';
COMMENT
ON COLUMN "TB_LIM_CARGPSDATA"."carId" IS '车辆id（Guid）';
COMMENT
ON COLUMN "TB_LIM_CARGPSDATA"."carSn" IS '车辆编码(用于GPS数据传输)';
COMMENT
ON COLUMN "TB_LIM_CARGPSDATA"."drivingLine" IS '行驶轨迹';
COMMENT
ON COLUMN "TB_LIM_CARGPSDATA"."drivingTime" IS '驾驶时长';
COMMENT
ON COLUMN "TB_LIM_CARGPSDATA"."endTime" IS '结束时间';
COMMENT
ON COLUMN "TB_LIM_CARGPSDATA"."fuelCon" IS '耗油量';
COMMENT
ON COLUMN "TB_LIM_CARGPSDATA"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_CARGPSDATA"."isSkeptical" IS '是否最高级';
COMMENT
ON COLUMN "TB_LIM_CARGPSDATA"."maxSpeed" IS '最高时速';
COMMENT
ON COLUMN "TB_LIM_CARGPSDATA"."mileage" IS '英里数';
COMMENT
ON COLUMN "TB_LIM_CARGPSDATA"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_CARGPSDATA"."userId" IS '使用人员id（Guid）';
COMMENT
ON COLUMN "TB_LIM_CARGPSDATA"."userName" IS '使用人员';


CREATE TABLE "TB_LIM_CARGPSREALDATA"
(
    "id"        VARCHAR(50)                                                 NOT NULL,
    "carId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "carDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "lat"       VARCHAR(50),
    "lon"       VARCHAR(50),
    "speed"     VARCHAR(50),
    "precision" VARCHAR(50),
    "direction" VARCHAR(50),
    "sigWatch"  VARCHAR(50),
    "operator"  VARCHAR(50),
    "orgId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    NOT         CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_CARGPSREALDATA"."carDate" IS '车辆行驶时间';
COMMENT
ON COLUMN "TB_LIM_CARGPSREALDATA"."carId" IS '车辆id（Guid）';
COMMENT
ON COLUMN "TB_LIM_CARGPSREALDATA"."direction" IS '运行方向';
COMMENT
ON COLUMN "TB_LIM_CARGPSREALDATA"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_CARGPSREALDATA"."lat" IS '纬度';
COMMENT
ON COLUMN "TB_LIM_CARGPSREALDATA"."lon" IS '经度';
COMMENT
ON COLUMN "TB_LIM_CARGPSREALDATA"."operator" IS '运营商';
COMMENT
ON COLUMN "TB_LIM_CARGPSREALDATA"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_CARGPSREALDATA"."precision" IS 'GPS精度';
COMMENT
ON COLUMN "TB_LIM_CARGPSREALDATA"."sigWatch" IS '网络强度';
COMMENT
ON COLUMN "TB_LIM_CARGPSREALDATA"."speed" IS '速度';


CREATE TABLE "TB_LIM_CARMANAGE"
(
    "id"                VARCHAR(50)                                                 NOT NULL,
    "carCode"           VARCHAR(50)                                                 NOT NULL,
    "carModel"          VARCHAR(50),
    "carType"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "state"             INT          DEFAULT 1                                      NOT NULL,
    "managerId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "buyDate"           TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "engineCode"        VARCHAR(50),
    "annualReviewDate"  TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "annualReviewCycle" INT          DEFAULT 12                                     NOT NULL,
    "oilConsumption"    VARCHAR(50),
    "remark"            VARCHAR(1000),
    "carBrand"          VARCHAR(50),
    "carColor"          VARCHAR(50),
    "useState"          INT          DEFAULT 0                                      NOT NULL,
    "orgId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT                 CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_CARMANAGE"."annualReviewCycle" IS '年检周期（月）';
COMMENT
ON COLUMN "TB_LIM_CARMANAGE"."annualReviewDate" IS '最近年检日期';
COMMENT
ON COLUMN "TB_LIM_CARMANAGE"."buyDate" IS '购置日期';
COMMENT
ON COLUMN "TB_LIM_CARMANAGE"."carBrand" IS '商标';
COMMENT
ON COLUMN "TB_LIM_CARMANAGE"."carCode" IS '车牌号码';
COMMENT
ON COLUMN "TB_LIM_CARMANAGE"."carColor" IS '车辆颜色';
COMMENT
ON COLUMN "TB_LIM_CARMANAGE"."carModel" IS '车辆型号';
COMMENT
ON COLUMN "TB_LIM_CARMANAGE"."carType" IS '车辆类型（常量LIM_CarType:轿车、货车、商务车、SUV）';
COMMENT
ON COLUMN "TB_LIM_CARMANAGE"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_CARMANAGE"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_CARMANAGE"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_CARMANAGE"."engineCode" IS '发动机号码';
COMMENT
ON COLUMN "TB_LIM_CARMANAGE"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_CARMANAGE"."managerId" IS '负责人Id（Guid）';
COMMENT
ON COLUMN "TB_LIM_CARMANAGE"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_CARMANAGE"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_CARMANAGE"."oilConsumption" IS '油耗';
COMMENT
ON COLUMN "TB_LIM_CARMANAGE"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_CARMANAGE"."remark" IS '备注';
COMMENT
ON COLUMN "TB_LIM_CARMANAGE"."state" IS '车辆状态(枚举EnumCarState:1:正常 2:维修,3:停用;4:过期)';
COMMENT
ON COLUMN "TB_LIM_CARMANAGE"."useState" IS '使用状态（枚举EnumCarUseState 0：空闲，1：使用中）';


CREATE TABLE "TB_LIM_COMPAREJUDGE"
(
    "id"                 VARCHAR(50)                                                 NOT NULL,
    "analyzeItemId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "checkType"          INT,
    "defaultStandardNum" INT          DEFAULT 0                                      NOT NULL,
    "orgId"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT                  CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_COMPAREJUDGE"."analyzeItemId" IS '分析项目id';
COMMENT
ON COLUMN "TB_LIM_COMPAREJUDGE"."checkType" IS '检测类型（0-废水比对，1-废气比对）';
COMMENT
ON COLUMN "TB_LIM_COMPAREJUDGE"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_COMPAREJUDGE"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_COMPAREJUDGE"."defaultStandardNum" IS '默认标样数';
COMMENT
ON COLUMN "TB_LIM_COMPAREJUDGE"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_COMPAREJUDGE"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_COMPAREJUDGE"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_COMPAREJUDGE"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_COMPAREJUDGE"."orgId" IS '组织机构id';


CREATE TABLE "TB_LIM_CONSUMABLESTORAGE"
(
    "id"                VARCHAR(50)                                                 NOT NULL,
    "purchaseDetailId"  VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "consumableId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "dimensionId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "dimensionName"     VARCHAR(100),
    "productionCode"    VARCHAR(20),
    "checkerId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "checkerName"       VARCHAR(50),
    "checkerDate"       TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "checkerResult"     INT          DEFAULT 1                                      NOT NULL,
    "manufacturerName"  VARCHAR(100),
    "expiryDate"        TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "storageNum"        DECIMAL(18, 4)                                              NOT NULL,
    "storageTime"       TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "balance"           DECIMAL(18, 2)                                              NOT NULL,
    "unitPrice"         DECIMAL(18, 2)                                              NOT NULL,
    "totalPrice"        DECIMAL(18, 2)                                              NOT NULL,
    "supplyCompanyId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "supplyCompanyName" VARCHAR(100),
    "operatorId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "operatorName"      VARCHAR(50),
    "appearance"        VARCHAR(100),
    "checkItem"         VARCHAR(100),
    "buyReason"         VARCHAR(1000),
    "keepPlace"         VARCHAR(100),
    "remark"            VARCHAR(1000),
    "isDeleted"         BIT          DEFAULT 0                                      NOT NULL,
    "orgId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT                 CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_CONSUMABLESTORAGE" IS '消耗品入库记录';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."appearance" IS '外观、状态';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."balance" IS '入库结存';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."buyReason" IS '购买原因';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."checkerDate" IS '验收日期';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."checkerId" IS '验收人Id（Guid）';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."checkerName" IS '验收人名字';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."checkerResult" IS '验收结论(枚举EnumCheckerResult：1:合格，0：不合格)';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."checkItem" IS '检验/验证项目';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."consumableId" IS '消耗品id（Guid）';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."dimensionId" IS '单位Id（冗余）（Guid）';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."dimensionName" IS '单位名称（冗余）';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."expiryDate" IS '有效日期';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."isDeleted" IS '假删';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."keepPlace" IS '存放位置';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."manufacturerName" IS '生产厂商名字';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."operatorId" IS '入库人Id（Guid）';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."operatorName" IS '入库人名称';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."productionCode" IS '生产批号';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."purchaseDetailId" IS '消耗品采购明细标识（Guid）';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."remark" IS '备注';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."storageNum" IS '入库数量';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."storageTime" IS '入库时间';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."supplyCompanyId" IS '供应商Id（Guid）';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."supplyCompanyName" IS '供应商名称';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."totalPrice" IS '总价';
COMMENT
ON COLUMN "TB_LIM_CONSUMABLESTORAGE"."unitPrice" IS '单价';


CREATE TABLE "TB_LIM_CONTRACT"
(
    "id"               VARCHAR(50)                                                 NOT NULL,
    "contractName"     VARCHAR(255)                                                NOT NULL,
    "contractCode"     VARCHAR(50),
    "type"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "period"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "totalAmount"      DECIMAL(18, 2)                                              NOT NULL,
    "arrivalAmount"    DECIMAL(18, 2)                                              NOT NULL,
    "lessAmount"       DECIMAL(18, 2)                                              NOT NULL,
    "badAmount"        DECIMAL(18, 2)                                              NOT NULL,
    "salesManId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "salesManName"     VARCHAR(50),
    "signDate"         TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "timeBegin"        TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "timeEnd"          TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "status"           INT          DEFAULT 0                                      NOT NULL,
    "collectionStatus" INT          DEFAULT 0                                      NOT NULL,
    "remindDays"       INT          DEFAULT 0                                      NOT NULL,
    "entId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "entName"          VARCHAR(50),
    "linkMan"          VARCHAR(50),
    "linkPhone"        VARCHAR(50),
    "address"          VARCHAR(100),
    "explains"         VARCHAR(255),
    "attentions"       VARCHAR(255),
    "remark"           VARCHAR(1000),
    "isDeleted"        BIT          DEFAULT 0                                      NOT NULL,
    "orgId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT                CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_CONTRACT"."address" IS '地址';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."arrivalAmount" IS '已收/付款';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."attentions" IS '注意事项';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."badAmount" IS '坏账';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."collectionStatus" IS '收/付款状态(枚举EnumContractCollectionStatus：0:未收/付款、1:部分收/付款、2:已收/付款)';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."contractCode" IS '合同编号';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."contractName" IS '合同名称';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."entId" IS '客户/外包单位';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."entName" IS '单位名称';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."explains" IS '工期要求说明';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."isDeleted" IS '假删';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."lessAmount" IS '剩余额';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."linkMan" IS '联系人';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."linkPhone" IS '联系方式';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."period" IS '合同周期（常量：长期检测合同、单次检测合同    编码：LIM_ContractPeriod）';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."remark" IS '备注';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."remindDays" IS '提醒天数';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."salesManId" IS '业务员id';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."salesManName" IS '业务员';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."signDate" IS '签订日期';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."status" IS '状态（枚举EnumContractStatus： 0未签  1已签）';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."timeBegin" IS '开始日期';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."timeEnd" IS '结束日期';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."totalAmount" IS '总金额';
COMMENT
ON COLUMN "TB_LIM_CONTRACT"."type" IS '合同类型（常量：收款合同、委外合同    编码:LIM_ContractType）';


CREATE TABLE "TB_LIM_CONTRACTCOLLECTIONPLAN"
(
    "id"          VARCHAR(50)                                                 NOT NULL,
    "contractId"  VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "collectDate" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "amount"      DECIMAL(18, 2)                                              NOT NULL,
    "status"      INT          DEFAULT 0                                      NOT NULL,
    "remark"      VARCHAR(1000),
    "orgId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "collectItem" VARCHAR(255) DEFAULT ''                                     NOT NULL,
    NOT           CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_CONTRACTCOLLECTIONPLAN"."amount" IS '计划收款金额';
COMMENT
ON COLUMN "TB_LIM_CONTRACTCOLLECTIONPLAN"."collectDate" IS '计划收款日期';
COMMENT
ON COLUMN "TB_LIM_CONTRACTCOLLECTIONPLAN"."collectItem" IS '收款项';
COMMENT
ON COLUMN "TB_LIM_CONTRACTCOLLECTIONPLAN"."contractId" IS '合同id';
COMMENT
ON COLUMN "TB_LIM_CONTRACTCOLLECTIONPLAN"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_CONTRACTCOLLECTIONPLAN"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_CONTRACTCOLLECTIONPLAN"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_CONTRACTCOLLECTIONPLAN"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_CONTRACTCOLLECTIONPLAN"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_CONTRACTCOLLECTIONPLAN"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_CONTRACTCOLLECTIONPLAN"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_CONTRACTCOLLECTIONPLAN"."remark" IS '备注';
COMMENT
ON COLUMN "TB_LIM_CONTRACTCOLLECTIONPLAN"."status" IS '收款状态(枚举EnumContractCollectionStatus：0:未收款、1:部分收款、2:已收款)';


CREATE TABLE "TB_LIM_COST"
(
    "id"                   VARCHAR(50)                                                   NOT NULL,
    "testId"               VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "redAnalyzeItemName"   VARCHAR(50),
    "redAnalyzeMethodName" VARCHAR(255),
    "redCountryStandard"   VARCHAR(50),
    "sampleTypeId"         VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "samplingCost"         DECIMAL(18, 2) DEFAULT 0.                                     NOT NULL,
    "analyzeCost"          DECIMAL(18, 2) DEFAULT 0.                                     NOT NULL,
    "orgId"                VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"              VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"           TIMESTAMP(0)   DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"             VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"             VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"           TIMESTAMP(0)   DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT                    CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_COST"."analyzeCost" IS '分析费';
COMMENT
ON COLUMN "TB_LIM_COST"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_COST"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_COST"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_COST"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_COST"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_COST"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_COST"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_COST"."redAnalyzeItemName" IS '分析项目名称';
COMMENT
ON COLUMN "TB_LIM_COST"."redAnalyzeMethodName" IS '分析方法名称';
COMMENT
ON COLUMN "TB_LIM_COST"."redCountryStandard" IS '标准编号';
COMMENT
ON COLUMN "TB_LIM_COST"."sampleTypeId" IS '检测类型id';
COMMENT
ON COLUMN "TB_LIM_COST"."samplingCost" IS '采样费';
COMMENT
ON COLUMN "TB_LIM_COST"."testId" IS '测试项目id';


CREATE TABLE "TB_LIM_COSTRULE"
(
    "id"         VARCHAR(50)                                                   NOT NULL,
    "laborCost"  DECIMAL(18, 2)                                                NOT NULL,
    "carCost"    DECIMAL(18, 2)                                                NOT NULL,
    "testRate"   DECIMAL(18, 2) DEFAULT 100.                                   NOT NULL,
    "offerRate"  DECIMAL(18, 2) DEFAULT 100.                                   NOT NULL,
    "reportRate" DECIMAL(18, 2) DEFAULT 100.                                   NOT NULL,
    "orgId"      VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"    VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate" TIMESTAMP(0)   DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"   VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"   VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate" TIMESTAMP(0)   DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT          CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_COSTRULE"."carCost" IS '车辆费单价';
COMMENT
ON COLUMN "TB_LIM_COSTRULE"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_COSTRULE"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_COSTRULE"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_COSTRULE"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_COSTRULE"."laborCost" IS '人工费单价';
COMMENT
ON COLUMN "TB_LIM_COSTRULE"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_COSTRULE"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_COSTRULE"."offerRate" IS '报价折扣率';
COMMENT
ON COLUMN "TB_LIM_COSTRULE"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_COSTRULE"."reportRate" IS '报告费费率';
COMMENT
ON COLUMN "TB_LIM_COSTRULE"."testRate" IS '检测费折扣率';


CREATE TABLE "TB_LIM_COSTRULEFORENT"
(
    "id"         VARCHAR(50)                                                   NOT NULL,
    "entId"      VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "laborCost"  DECIMAL(18, 2)                                                NOT NULL,
    "testRate"   DECIMAL(18, 2) DEFAULT 100.                                   NOT NULL,
    "offerRate"  DECIMAL(18, 2) DEFAULT 100.                                   NOT NULL,
    "orgId"      VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"    VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate" TIMESTAMP(0)   DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"   VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"   VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate" TIMESTAMP(0)   DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT          CLUSTER PRIMARY KEY("id"),
    CONSTRAINT "UIX_TB_LIM_CostRuleForEnt" UNIQUE ("entId")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_COSTRULEFORENT"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_COSTRULEFORENT"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_COSTRULEFORENT"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_COSTRULEFORENT"."entId" IS '企业id';
COMMENT
ON COLUMN "TB_LIM_COSTRULEFORENT"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_COSTRULEFORENT"."laborCost" IS '人工费单价';
COMMENT
ON COLUMN "TB_LIM_COSTRULEFORENT"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_COSTRULEFORENT"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_COSTRULEFORENT"."offerRate" IS '报价折扣率';
COMMENT
ON COLUMN "TB_LIM_COSTRULEFORENT"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_COSTRULEFORENT"."testRate" IS '检测费折扣率';


CREATE TABLE "TB_LIM_CURVE"
(
    "id"               VARCHAR(50)                                                 NOT NULL,
    "testId"           VARCHAR(50)                                                 NOT NULL,
    "configPersonId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "configName"       VARCHAR(50),
    "kRange"           VARCHAR(50),
    "bRange"           VARCHAR(50),
    "coefficientRange" VARCHAR(50),
    "coefficient"      VARCHAR(50)                                                 NOT NULL,
    "configDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "period"           INT          DEFAULT 30                                     NOT NULL,
    "zeroPoint"        VARCHAR(50),
    "kValue"           VARCHAR(50),
    "bValue"           VARCHAR(50),
    "cValue"           VARCHAR(50),
    "isDouble"         BIT          DEFAULT 0                                      NOT NULL,
    "curveType"        INT          DEFAULT 0                                      NOT NULL,
    "curveMode"        INT          DEFAULT 0                                      NOT NULL,
    "orgId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "forcedZero"       BIT          DEFAULT 0                                      NOT NULL,
    "doubleName"       VARCHAR(50),
    "curveInfo"        VARCHAR(100) DEFAULT '',
    "relevanceId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "isDeleted"        BIT          DEFAULT 0                                      NOT NULL,
    NOT                CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_CURVE"."bRange" IS '截距范围';
COMMENT
ON COLUMN "TB_LIM_CURVE"."bValue" IS '截距b';
COMMENT
ON COLUMN "TB_LIM_CURVE"."coefficient" IS '相关系数';
COMMENT
ON COLUMN "TB_LIM_CURVE"."coefficientRange" IS '相关系数范围';
COMMENT
ON COLUMN "TB_LIM_CURVE"."configDate" IS '配置日期';
COMMENT
ON COLUMN "TB_LIM_CURVE"."configName" IS '配置人员姓名';
COMMENT
ON COLUMN "TB_LIM_CURVE"."configPersonId" IS '配置人员id';
COMMENT
ON COLUMN "TB_LIM_CURVE"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_CURVE"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_CURVE"."curveInfo" IS '曲线信息';
COMMENT
ON COLUMN "TB_LIM_CURVE"."curveMode" IS '曲线模型（枚举（EnumCurveModel：0普通 1紫外 2荧光 3石墨  4离子电极））';
COMMENT
ON COLUMN "TB_LIM_CURVE"."curveType" IS '曲线类型（枚举(EnumCurveType:0直线型 1Log型  2二次)）';
COMMENT
ON COLUMN "TB_LIM_CURVE"."cValue" IS '实数c';
COMMENT
ON COLUMN "TB_LIM_CURVE"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_CURVE"."doubleName" IS '双曲线名称';
COMMENT
ON COLUMN "TB_LIM_CURVE"."forcedZero" IS '强制零点';
COMMENT
ON COLUMN "TB_LIM_CURVE"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_CURVE"."isDeleted" IS '假删字段';
COMMENT
ON COLUMN "TB_LIM_CURVE"."isDouble" IS '是否双曲线';
COMMENT
ON COLUMN "TB_LIM_CURVE"."kRange" IS '斜率范围';
COMMENT
ON COLUMN "TB_LIM_CURVE"."kValue" IS '斜率a';
COMMENT
ON COLUMN "TB_LIM_CURVE"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_CURVE"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_CURVE"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_CURVE"."period" IS '周期（天）';
COMMENT
ON COLUMN "TB_LIM_CURVE"."relevanceId" IS '关联曲线id';
COMMENT
ON COLUMN "TB_LIM_CURVE"."testId" IS '测试项目id';
COMMENT
ON COLUMN "TB_LIM_CURVE"."zeroPoint" IS '曲线零点';


CREATE TABLE "TB_LIM_CURVEDETAIL"
(
    "id"                  VARCHAR(50)                                                 NOT NULL,
    "curveId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "vValue"              VARCHAR(50),
    "hValue"              VARCHAR(50),
    "xValue"              VARCHAR(50),
    "yValue"              VARCHAR(50),
    "orderNum"            INT          DEFAULT 0                                      NOT NULL,
    "remark"              VARCHAR(1000),
    "blank220"            VARCHAR(50),
    "blank275"            VARCHAR(50),
    "lessBlank"           VARCHAR(50),
    "aValueBG"            VARCHAR(50),
    "orgId"               VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "colorAbsorbance"     VARCHAR(50),
    "interfereAbsorbance" VARCHAR(50),
    "blankAbsorbance"     VARCHAR(50),
    "code"                VARCHAR(150) DEFAULT '',
    NOT                   CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_CURVEDETAIL"."aValueBG" IS '背景吸光度';
COMMENT
ON COLUMN "TB_LIM_CURVEDETAIL"."blank220" IS '220吸光度';
COMMENT
ON COLUMN "TB_LIM_CURVEDETAIL"."blank275" IS '275吸光度';
COMMENT
ON COLUMN "TB_LIM_CURVEDETAIL"."blankAbsorbance" IS '空白吸光度A0';
COMMENT
ON COLUMN "TB_LIM_CURVEDETAIL"."code" IS '采集编号';
COMMENT
ON COLUMN "TB_LIM_CURVEDETAIL"."colorAbsorbance" IS '显色吸光度A1';
COMMENT
ON COLUMN "TB_LIM_CURVEDETAIL"."curveId" IS '标准曲线标识';
COMMENT
ON COLUMN "TB_LIM_CURVEDETAIL"."hValue" IS '含量值';
COMMENT
ON COLUMN "TB_LIM_CURVEDETAIL"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_CURVEDETAIL"."interfereAbsorbance" IS '干扰吸光度A2';
COMMENT
ON COLUMN "TB_LIM_CURVEDETAIL"."lessBlank" IS '减空白吸光度';
COMMENT
ON COLUMN "TB_LIM_CURVEDETAIL"."orderNum" IS '排序';
COMMENT
ON COLUMN "TB_LIM_CURVEDETAIL"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_CURVEDETAIL"."remark" IS '备注';
COMMENT
ON COLUMN "TB_LIM_CURVEDETAIL"."vValue" IS '标准溶液加入体积';
COMMENT
ON COLUMN "TB_LIM_CURVEDETAIL"."xValue" IS 'X值';
COMMENT
ON COLUMN "TB_LIM_CURVEDETAIL"."yValue" IS 'Y值';


CREATE TABLE "TB_LIM_CUSTOMERVIOLATE"
(
    "id"                 VARCHAR(50)                                                 NOT NULL,
    "enterpriseId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "registerPersonId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "registerTime"       TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "happenedTime"       TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "hasHandle"          BIT          DEFAULT 0                                      NOT NULL,
    "requiredHandleTime" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "handleTime"         TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "violateContent"     VARCHAR(2000),
    "handlePersonId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000',
    "handleWay"          VARCHAR(2000),
    "orgId"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT                  CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_CUSTOMERVIOLATE" IS '客户违约信息';
COMMENT
ON COLUMN "TB_LIM_CUSTOMERVIOLATE"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_CUSTOMERVIOLATE"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_CUSTOMERVIOLATE"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_CUSTOMERVIOLATE"."enterpriseId" IS '企业id';
COMMENT
ON COLUMN "TB_LIM_CUSTOMERVIOLATE"."handlePersonId" IS '处理人';
COMMENT
ON COLUMN "TB_LIM_CUSTOMERVIOLATE"."handleTime" IS '处理时间';
COMMENT
ON COLUMN "TB_LIM_CUSTOMERVIOLATE"."handleWay" IS '处理措施';
COMMENT
ON COLUMN "TB_LIM_CUSTOMERVIOLATE"."happenedTime" IS '发生时间';
COMMENT
ON COLUMN "TB_LIM_CUSTOMERVIOLATE"."hasHandle" IS '是否已处理';
COMMENT
ON COLUMN "TB_LIM_CUSTOMERVIOLATE"."id" IS '主键id';
COMMENT
ON COLUMN "TB_LIM_CUSTOMERVIOLATE"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_CUSTOMERVIOLATE"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_CUSTOMERVIOLATE"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_CUSTOMERVIOLATE"."registerPersonId" IS '登记人员id';
COMMENT
ON COLUMN "TB_LIM_CUSTOMERVIOLATE"."registerTime" IS '登记时间';
COMMENT
ON COLUMN "TB_LIM_CUSTOMERVIOLATE"."requiredHandleTime" IS '要求处理时间';
COMMENT
ON COLUMN "TB_LIM_CUSTOMERVIOLATE"."violateContent" IS '违约内容';


CREATE TABLE "TB_LIM_DOCAUTHORITY"
(
    "id"        VARCHAR(50)                                                NOT NULL,
    "objectId"  VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "authId"    VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "authName"  VARCHAR(250),
    "roleId"    VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "roleName"  VARCHAR(250),
    "authState" BIT         DEFAULT 0                                      NOT NULL,
    "orgId"     VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    NOT         CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_DOCAUTHORITY"."authId" IS '权限Id（常量Guid，常量名称 LIM_AuthType）';
COMMENT
ON COLUMN "TB_LIM_DOCAUTHORITY"."authName" IS '权限名称';
COMMENT
ON COLUMN "TB_LIM_DOCAUTHORITY"."authState" IS '权限状态';
COMMENT
ON COLUMN "TB_LIM_DOCAUTHORITY"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_DOCAUTHORITY"."objectId" IS '文件夹，文件Id（Guid）';
COMMENT
ON COLUMN "TB_LIM_DOCAUTHORITY"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_DOCAUTHORITY"."roleId" IS '角色Id（Guid）';
COMMENT
ON COLUMN "TB_LIM_DOCAUTHORITY"."roleName" IS '角色名称';


CREATE TABLE "TB_LIM_DOCAUTHORITYCONFIG"
(
    "id"              VARCHAR(50)                                                 NOT NULL,
    "objectId"        VARCHAR(50)  DEFAULT ''                                     NOT NULL,
    "authCode"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "authName"        VARCHAR(250) DEFAULT '',
    "defaultOpenInd"  BIT,
    "userId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "sortNum"         INT,
    "orgId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "authorityListId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    NOT               CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_DOCAUTHORITYCONFIG"."authCode" IS '权限编码（常量Guid，常量名称 LIM_AuthType）';
COMMENT
ON COLUMN "TB_LIM_DOCAUTHORITYCONFIG"."authName" IS '权限名称';
COMMENT
ON COLUMN "TB_LIM_DOCAUTHORITYCONFIG"."authorityListId" IS '文件夹对应权限id';
COMMENT
ON COLUMN "TB_LIM_DOCAUTHORITYCONFIG"."defaultOpenInd" IS '是否默认开启 1：是  0：否';
COMMENT
ON COLUMN "TB_LIM_DOCAUTHORITYCONFIG"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_DOCAUTHORITYCONFIG"."objectId" IS '文件夹，文件Id（Guid）';
COMMENT
ON COLUMN "TB_LIM_DOCAUTHORITYCONFIG"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_DOCAUTHORITYCONFIG"."sortNum" IS '排序号';
COMMENT
ON COLUMN "TB_LIM_DOCAUTHORITYCONFIG"."userId" IS '人员Id';


CREATE TABLE "TB_LIM_DOCAUTHORITYLIST"
(
    "id"             VARCHAR(50)                                                 NOT NULL,
    "objectId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "authCode"       VARCHAR(50)  DEFAULT ''                                     NOT NULL,
    "authName"       VARCHAR(250) DEFAULT '',
    "defaultOpenInd" BIT,
    "sortNum"        INT,
    "orgId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_DOCAUTHORITYLIST"."authCode" IS '权限编码（常量Guid，常量名称 LIM_AuthType）';
COMMENT
ON COLUMN "TB_LIM_DOCAUTHORITYLIST"."authName" IS '权限名称';
COMMENT
ON COLUMN "TB_LIM_DOCAUTHORITYLIST"."defaultOpenInd" IS '是否默认开启 1：是  0：否';
COMMENT
ON COLUMN "TB_LIM_DOCAUTHORITYLIST"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_DOCAUTHORITYLIST"."objectId" IS '文件夹id';
COMMENT
ON COLUMN "TB_LIM_DOCAUTHORITYLIST"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_DOCAUTHORITYLIST"."sortNum" IS '排序号';


CREATE TABLE "TB_LIM_ENTEVALUATION"
(
    "id"            VARCHAR(50)                                                 NOT NULL,
    "entId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "content"       VARCHAR(1000),
    "startPersonId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "auditPersonId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "auditOpinion"  VARCHAR(1000),
    "status"        INT          DEFAULT 0                                      NOT NULL,
    "orgId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"    TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"    TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT             CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_ENTEVALUATION"."auditOpinion" IS '审核意见';
COMMENT
ON COLUMN "TB_LIM_ENTEVALUATION"."auditPersonId" IS '审核人员id（Guid）';
COMMENT
ON COLUMN "TB_LIM_ENTEVALUATION"."content" IS '评价内容';
COMMENT
ON COLUMN "TB_LIM_ENTEVALUATION"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_ENTEVALUATION"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_ENTEVALUATION"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_ENTEVALUATION"."entId" IS '供应商id（Guid）';
COMMENT
ON COLUMN "TB_LIM_ENTEVALUATION"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_ENTEVALUATION"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_ENTEVALUATION"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_ENTEVALUATION"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_ENTEVALUATION"."startPersonId" IS '评价人员id（Guid）';
COMMENT
ON COLUMN "TB_LIM_ENTEVALUATION"."status" IS '评价状态（枚举EnumEvaluateStatus：0未评价 1已评价）';


CREATE TABLE "TB_LIM_ENTSUPPLIERGOODSEVALUATION"
(
    "id"                   VARCHAR(50)                                                   NOT NULL,
    "entId"                VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "goodsId"              VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "evaluationId"         VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "shortestDeliveryTime" VARCHAR(10),
    "lowestPrice"          DECIMAL(18, 2) DEFAULT (-1.)                                  NOT NULL,
    "comment"              VARCHAR(1000),
    "trialResult"          VARCHAR(1000),
    "trialComment"         VARCHAR(1000),
    "trialPerson"          VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "trialBeginDate"       TIMESTAMP(0)   DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "trialTimeLen"         VARCHAR(255),
    "auditChecker"         VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "auditData"            TIMESTAMP(0)   DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "serviceContent"       VARCHAR(1000),
    "info"                 VARCHAR(1000),
    "remark"               VARCHAR(1000),
    "orgId"                VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"              VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"           TIMESTAMP(0)   DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"             VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"             VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"           TIMESTAMP(0)   DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT                    CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERGOODSEVALUATION"."auditChecker" IS '审核人Id（Guid）';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERGOODSEVALUATION"."auditData" IS '审核日期';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERGOODSEVALUATION"."comment" IS '评价';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERGOODSEVALUATION"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERGOODSEVALUATION"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERGOODSEVALUATION"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERGOODSEVALUATION"."entId" IS '企业Id（Guid）';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERGOODSEVALUATION"."evaluationId" IS '评价Id（Guid）';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERGOODSEVALUATION"."goodsId" IS '供应品Id（Guid）';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERGOODSEVALUATION"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERGOODSEVALUATION"."info" IS '简介';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERGOODSEVALUATION"."lowestPrice" IS '最低报价';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERGOODSEVALUATION"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERGOODSEVALUATION"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERGOODSEVALUATION"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERGOODSEVALUATION"."remark" IS '备注';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERGOODSEVALUATION"."serviceContent" IS '主营产品';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERGOODSEVALUATION"."shortestDeliveryTime" IS '最短供货时间';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERGOODSEVALUATION"."trialBeginDate" IS '试用开始日期';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERGOODSEVALUATION"."trialComment" IS '试用评价';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERGOODSEVALUATION"."trialPerson" IS '试用人Id（Guid）';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERGOODSEVALUATION"."trialResult" IS '试用结果';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERGOODSEVALUATION"."trialTimeLen" IS '试用时长';


CREATE TABLE "TB_LIM_ENTSUPPLIERSERVICE"
(
    "id"         VARCHAR(50)                                                 NOT NULL,
    "entId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "goodsName"  VARCHAR(100)                                                NOT NULL,
    "goodsCode"  VARCHAR(20),
    "goodsModel" VARCHAR(50),
    "goodsType"  VARCHAR(100),
    "remark"     VARCHAR(1000),
    "orgId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT          CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERSERVICE"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERSERVICE"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERSERVICE"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERSERVICE"."entId" IS '供应商id（Guid）';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERSERVICE"."goodsCode" IS '商品编码';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERSERVICE"."goodsModel" IS '商品规格';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERSERVICE"."goodsName" IS '商品名称';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERSERVICE"."goodsType" IS '商品类型';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERSERVICE"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERSERVICE"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERSERVICE"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERSERVICE"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_ENTSUPPLIERSERVICE"."remark" IS '备注';


CREATE TABLE "TB_LIM_ENVIRONMENTAL"
(
    "id"                 VARCHAR(50)                                                 NOT NULL,
    "labName"            VARCHAR(50)                                                 NOT NULL,
    "labCode"            VARCHAR(50),
    "lowestTemperature"  VARCHAR(50),
    "highestTemperature" VARCHAR(50),
    "lowestHumidity"     VARCHAR(50),
    "highestHumidity"    VARCHAR(50),
    "lowestPressure"     VARCHAR(50),
    "highestPressure"    VARCHAR(50),
    "personInChargeId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "personInCharge"     VARCHAR(50),
    "isDeleted"          BIT          DEFAULT 0                                      NOT NULL,
    "orgId"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT                  CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTAL"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTAL"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTAL"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTAL"."highestHumidity" IS '最高湿度';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTAL"."highestPressure" IS '最高气压';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTAL"."highestTemperature" IS '最高温度';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTAL"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTAL"."isDeleted" IS '假删';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTAL"."labCode" IS '实验室编号';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTAL"."labName" IS '实验室名称';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTAL"."lowestHumidity" IS '最低湿度';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTAL"."lowestPressure" IS '最低气压';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTAL"."lowestTemperature" IS '最低温度';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTAL"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTAL"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTAL"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTAL"."personInCharge" IS '负责人';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTAL"."personInChargeId" IS '负责人id';


CREATE TABLE "TB_LIM_ENVIRONMENTALLOG"
(
    "id"              VARCHAR(50)                                                 NOT NULL,
    "environmentalId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "temperature"     VARCHAR(50),
    "humidity"        VARCHAR(50),
    "pressure"        VARCHAR(50),
    "updateTime"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "orgId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT               CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_ENVIRONMENTALLOG" IS '温湿度仪';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALLOG"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALLOG"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALLOG"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALLOG"."environmentalId" IS '实验室id';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALLOG"."humidity" IS '湿度';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALLOG"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALLOG"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALLOG"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALLOG"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALLOG"."pressure" IS '大气压';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALLOG"."temperature" IS '温度';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALLOG"."updateTime" IS '更新时间';


CREATE TABLE "TB_LIM_ENVIRONMENTALRECORD"
(
    "id"              VARCHAR(50)                                                 NOT NULL,
    "objectId"        VARCHAR(50)                                                 NOT NULL,
    "objectType"      INT          DEFAULT (-1)                                   NOT NULL,
    "environmentalId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "temperature"     VARCHAR(50),
    "humidity"        VARCHAR(50),
    "pressure"        VARCHAR(50),
    "usePersonId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "startTime"       TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "endTime"         TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "useTime"         DECIMAL(8, 1),
    "remark"          VARCHAR(1000),
    "orgId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT               CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALRECORD"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALRECORD"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALRECORD"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALRECORD"."endTime" IS '结束使用时间';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALRECORD"."environmentalId" IS '实验室id';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALRECORD"."humidity" IS '湿度';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALRECORD"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALRECORD"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALRECORD"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALRECORD"."objectId" IS '对象id（送样单-采样、领样单-现场分析、工作单-实验室）';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALRECORD"."objectType" IS '对象类型（枚举EnumEnvRecObjType1：采样，2：实验室分析，4：现场分析）';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALRECORD"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALRECORD"."pressure" IS '大气压';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALRECORD"."remark" IS '备注';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALRECORD"."startTime" IS '开始使用时间';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALRECORD"."temperature" IS '温度';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALRECORD"."usePersonId" IS '使用人id';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALRECORD"."useTime" IS '使用时长';


CREATE TABLE "TB_LIM_ENVIRONMENTALRECORD2SAMPLE"
(
    "id"                    VARCHAR(50) NOT NULL,
    "environmentalRecordId" VARCHAR(50) NOT NULL,
    "sampleId"              VARCHAR(50) NOT NULL,
    NOT                     CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_ENVIRONMENTALRECORD2SAMPLE" IS '仪器使用记录与样品关联表';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALRECORD2SAMPLE"."environmentalRecordId" IS '仪器使用记录id';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALRECORD2SAMPLE"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALRECORD2SAMPLE"."sampleId" IS '样品id';


CREATE TABLE "TB_LIM_ENVIRONMENTALRECORD2TEST"
(
    "id"                    VARCHAR(50) NOT NULL,
    "environmentalRecordId" VARCHAR(50) NOT NULL,
    "testId"                VARCHAR(50) NOT NULL,
    NOT                     CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_ENVIRONMENTALRECORD2TEST" IS '仪器使用记录与测试项目关联表';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALRECORD2TEST"."environmentalRecordId" IS '仪器使用记录id';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALRECORD2TEST"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_ENVIRONMENTALRECORD2TEST"."testId" IS '测试项目id';


CREATE TABLE "TB_LIM_EXAMINE"
(
    "id"                VARCHAR(50)                                                 NOT NULL,
    "title"             VARCHAR(255)                                                NOT NULL,
    "deptId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "deptName"          VARCHAR(50)                                                 NOT NULL,
    "inspectedPersonId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "inspectedPerson"   VARCHAR(50)                                                 NOT NULL,
    "registeDate"       TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "endDate"           TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "status"            INT          DEFAULT 10                                     NOT NULL,
    "auditOpinion"      VARCHAR(1000),
    "isDeleted"         BIT          DEFAULT 0                                      NOT NULL,
    "orgId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "addPersonId"       VARCHAR(50)                                                 NOT NULL,
    "addPersonName"     VARCHAR(50)                                                 NOT NULL,
    "addDate"           TIMESTAMP(0)                                                NOT NULL,
    NOT                 CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_EXAMINE" IS '考核管理表';
COMMENT
ON COLUMN "TB_LIM_EXAMINE"."addDate" IS '登记日期';
COMMENT
ON COLUMN "TB_LIM_EXAMINE"."addPersonId" IS '登记人标识';
COMMENT
ON COLUMN "TB_LIM_EXAMINE"."addPersonName" IS '登记人名称';
COMMENT
ON COLUMN "TB_LIM_EXAMINE"."auditOpinion" IS '审核意见';
COMMENT
ON COLUMN "TB_LIM_EXAMINE"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_EXAMINE"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_EXAMINE"."deptId" IS '考核部门标识';
COMMENT
ON COLUMN "TB_LIM_EXAMINE"."deptName" IS '考核部门名称';
COMMENT
ON COLUMN "TB_LIM_EXAMINE"."domainId" IS '所属实验室id';
COMMENT
ON COLUMN "TB_LIM_EXAMINE"."endDate" IS '考核截止时间';
COMMENT
ON COLUMN "TB_LIM_EXAMINE"."id" IS '主键';
COMMENT
ON COLUMN "TB_LIM_EXAMINE"."inspectedPerson" IS '受考核人员名称';
COMMENT
ON COLUMN "TB_LIM_EXAMINE"."inspectedPersonId" IS '受考核人员标识';
COMMENT
ON COLUMN "TB_LIM_EXAMINE"."isDeleted" IS '是否删除';
COMMENT
ON COLUMN "TB_LIM_EXAMINE"."modifier" IS '最近修改人';
COMMENT
ON COLUMN "TB_LIM_EXAMINE"."modifyDate" IS '最新修改时间';
COMMENT
ON COLUMN "TB_LIM_EXAMINE"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_EXAMINE"."registeDate" IS '考核登记时间';
COMMENT
ON COLUMN "TB_LIM_EXAMINE"."status" IS '考核状态';
COMMENT
ON COLUMN "TB_LIM_EXAMINE"."title" IS '考核目标';


CREATE TABLE "TB_LIM_EXAMINETYPE"
(
    "id"                VARCHAR(50)                                                 NOT NULL,
    "parentId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000',
    "examineId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "title"             VARCHAR(255)                                                NOT NULL,
    "inspectedPersonId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "inspectedPerson"   VARCHAR(50)                                                 NOT NULL,
    "content"           VARCHAR(255),
    "isDeleted"         BIT          DEFAULT 0                                      NOT NULL,
    "orgId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "progress"          INT          DEFAULT 0                                      NOT NULL,
    NOT                 CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_EXAMINETYPE" IS '考核类型表';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPE"."content" IS '考核内容';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPE"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPE"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPE"."domainId" IS '所属实验室id';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPE"."examineId" IS '考核标识';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPE"."id" IS '主键';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPE"."inspectedPerson" IS '受考核人员名称';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPE"."inspectedPersonId" IS '受考核人员标识';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPE"."isDeleted" IS '是否删除';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPE"."modifier" IS '最近修改人';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPE"."modifyDate" IS '最新修改时间';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPE"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPE"."parentId" IS '父项标识';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPE"."progress" IS '考核项目进度';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPE"."title" IS '考核目标';


CREATE TABLE "TB_LIM_EXAMINETYPERECORD"
(
    "id"            VARCHAR(50)                                                 NOT NULL,
    "examineTypeId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "examineDate"   TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "progress"      INT          DEFAULT 0                                      NOT NULL,
    "content"       VARCHAR(1000),
    "isDeleted"     BIT          DEFAULT 0                                      NOT NULL,
    "orgId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"    TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"    TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT             CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_EXAMINETYPERECORD" IS '考核记录表';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPERECORD"."content" IS '详情内容';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPERECORD"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPERECORD"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPERECORD"."domainId" IS '所属实验室id';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPERECORD"."examineDate" IS '登记日期';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPERECORD"."examineTypeId" IS '考核类型标识';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPERECORD"."id" IS '主键';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPERECORD"."isDeleted" IS '是否删除';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPERECORD"."modifier" IS '最近修改人';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPERECORD"."modifyDate" IS '最新修改时间';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPERECORD"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_EXAMINETYPERECORD"."progress" IS '进度';


CREATE TABLE "TB_LIM_FEECONFIG"
(
    "id"         VARCHAR(50)                                                  NOT NULL,
    "typeName"   VARCHAR(50),
    "standard"   DECIMAL(18, 2)                                               NOT NULL,
    "unit"       VARCHAR(50),
    "days"       DECIMAL(18, 2)                                               NOT NULL,
    "count"      DECIMAL(18, 2)                                               NOT NULL,
    "remark"     VARCHAR(255),
    "isDeleted"  BIT           DEFAULT 0                                      NOT NULL,
    "orgId"      VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"    VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate" TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"   VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"   VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate" TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "externalId" VARCHAR(50),
    "formula"    VARCHAR(1000) DEFAULT '',
    NOT          CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_FEECONFIG" IS '费用类型';
COMMENT
ON COLUMN "TB_LIM_FEECONFIG"."count" IS '数量';
COMMENT
ON COLUMN "TB_LIM_FEECONFIG"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_FEECONFIG"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_FEECONFIG"."days" IS '天数';
COMMENT
ON COLUMN "TB_LIM_FEECONFIG"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_FEECONFIG"."externalId" IS '关联系统产品编号';
COMMENT
ON COLUMN "TB_LIM_FEECONFIG"."formula" IS '计算公式';
COMMENT
ON COLUMN "TB_LIM_FEECONFIG"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_FEECONFIG"."isDeleted" IS '是否删除';
COMMENT
ON COLUMN "TB_LIM_FEECONFIG"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_FEECONFIG"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_FEECONFIG"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_FEECONFIG"."remark" IS '备注';
COMMENT
ON COLUMN "TB_LIM_FEECONFIG"."standard" IS '收费标准';
COMMENT
ON COLUMN "TB_LIM_FEECONFIG"."typeName" IS '费用类型名称';
COMMENT
ON COLUMN "TB_LIM_FEECONFIG"."unit" IS '单位';


CREATE TABLE "TB_LIM_FILECONTROLAPPLY"
(
    "id"            VARCHAR(50)                                                 NOT NULL,
    "controlCode"   VARCHAR(100),
    "applyPersonId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "applyDesc"     VARCHAR(1000),
    "applyDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "status"        VARCHAR(50),
    "controlDate"   TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "reviseDate"    TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "reviseContent" VARCHAR(1000),
    "abolishReason" VARCHAR(1000),
    "abolishDate"   TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "applyType"     INT          DEFAULT 1                                      NOT NULL,
    "isDeleted"     BIT          DEFAULT 0                                      NOT NULL,
    "orgId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"    TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"    TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT             CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_FILECONTROLAPPLY" IS '文件控制申请信息';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLY"."abolishDate" IS '废止的日期';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLY"."abolishReason" IS '废止的原因';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLY"."applyDate" IS '申请日期';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLY"."applyDesc" IS '申请描述';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLY"."applyPersonId" IS '申请人id（Guid）';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLY"."applyType" IS '申请类型（枚举EnumFileControlApplyType：1：受控申请，2：修订申请，3：废止申请）';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLY"."controlCode" IS '受控申请编号，修订申请编号，废止申请编号';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLY"."controlDate" IS '受控日期';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLY"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLY"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLY"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLY"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLY"."isDeleted" IS '假删';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLY"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLY"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLY"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLY"."reviseContent" IS '修订内容';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLY"."reviseDate" IS '修订日期';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLY"."status" IS '状态';


CREATE TABLE "TB_LIM_FILECONTROLAPPLYDETAIL"
(
    "id"          VARCHAR(50)                                                 NOT NULL,
    "parentId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "fileApplyId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "grantId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "controlCode" VARCHAR(100),
    "fileName"    VARCHAR(100),
    "fileCode"    VARCHAR(100),
    "fileType"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "maker"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "compileTime" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "status"      INT          DEFAULT 1                                      NOT NULL,
    "controlDate" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "abolishDate" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "version"     VARCHAR(50),
    "isDeleted"   BIT          DEFAULT 0                                      NOT NULL,
    "orgId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT           CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_FILECONTROLAPPLYDETAIL" IS '文件控制管理明细';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLYDETAIL"."compileTime" IS '编制时间';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLYDETAIL"."controlCode" IS '受控编号';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLYDETAIL"."controlDate" IS '受控日期';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLYDETAIL"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLYDETAIL"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLYDETAIL"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLYDETAIL"."fileApplyId" IS '申请单id（最新的文件记录，申请单id为Id）';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLYDETAIL"."fileCode" IS '文件编号';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLYDETAIL"."fileName" IS '文件名称';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLYDETAIL"."fileType" IS '文件类型（常量LIM_FileControlType：程序文件、标准文件、质量手册、作业指导书、记录单）';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLYDETAIL"."grantId" IS '发放回收id（只有发放回收的时候id才有数据，如果发放回收记录删除，id要清空）';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLYDETAIL"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLYDETAIL"."isDeleted" IS '假删';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLYDETAIL"."maker" IS '编制人id';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLYDETAIL"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLYDETAIL"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLYDETAIL"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLYDETAIL"."parentId" IS '父级id(最新的处理文件信息的父级id为Int，之前的文件信息的父级id是最新的文件信息的id)';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLYDETAIL"."status" IS '状态（枚举EnumFileControlApplyDetailStatus：1：未受控、2：受控申请中、3：修订中、4：废止中、5：已受控、6：已废止）';
COMMENT
ON COLUMN "TB_LIM_FILECONTROLAPPLYDETAIL"."version" IS '版本号';


CREATE TABLE "TB_LIM_FILEGRANTRECOVERY"
(
    "id"               VARCHAR(50)                                                 NOT NULL,
    "code"             VARCHAR(255),
    "materialPersonId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "recoveryPersonId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "grantTime"        TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "recoveryTime"     TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "remark"           VARCHAR(1000),
    "orgId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT                CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_FILEGRANTRECOVERY" IS '文件发放与回收';
COMMENT
ON COLUMN "TB_LIM_FILEGRANTRECOVERY"."code" IS '发放编号';
COMMENT
ON COLUMN "TB_LIM_FILEGRANTRECOVERY"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_FILEGRANTRECOVERY"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_FILEGRANTRECOVERY"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_FILEGRANTRECOVERY"."grantTime" IS '发放时间';
COMMENT
ON COLUMN "TB_LIM_FILEGRANTRECOVERY"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_FILEGRANTRECOVERY"."materialPersonId" IS '领用人id';
COMMENT
ON COLUMN "TB_LIM_FILEGRANTRECOVERY"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_FILEGRANTRECOVERY"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_FILEGRANTRECOVERY"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_FILEGRANTRECOVERY"."recoveryPersonId" IS '回收人id';
COMMENT
ON COLUMN "TB_LIM_FILEGRANTRECOVERY"."recoveryTime" IS '回收日期';
COMMENT
ON COLUMN "TB_LIM_FILEGRANTRECOVERY"."remark" IS '备注';


CREATE TABLE "TB_LIM_FIXEDPROPERTY"
(
    "id"            VARCHAR(50)                                                 NOT NULL,
    "assetsName"    VARCHAR(50)                                                 NOT NULL,
    "brandModel"    VARCHAR(50),
    "assetsNo"      VARCHAR(11)                                                 NOT NULL,
    "purchaseDate"  TIMESTAMP(0),
    "purchasePrice" DECIMAL(10, 2),
    "supplier"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000',
    "assetsType"    VARCHAR(50)                                                 NOT NULL,
    "deptId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "status"        INT                                                         NOT NULL,
    "manager"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "orgId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"    TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"    TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT             CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_FIXEDPROPERTY"."assetsName" IS '固定资产名称';
COMMENT
ON COLUMN "TB_LIM_FIXEDPROPERTY"."assetsNo" IS '资产编号';
COMMENT
ON COLUMN "TB_LIM_FIXEDPROPERTY"."assetsType" IS '资产类型';
COMMENT
ON COLUMN "TB_LIM_FIXEDPROPERTY"."brandModel" IS '品牌型号';
COMMENT
ON COLUMN "TB_LIM_FIXEDPROPERTY"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_FIXEDPROPERTY"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_FIXEDPROPERTY"."deptId" IS '所属科室（Guid）';
COMMENT
ON COLUMN "TB_LIM_FIXEDPROPERTY"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_FIXEDPROPERTY"."id" IS '主键id';
COMMENT
ON COLUMN "TB_LIM_FIXEDPROPERTY"."manager" IS '管理人员（Guid）';
COMMENT
ON COLUMN "TB_LIM_FIXEDPROPERTY"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_FIXEDPROPERTY"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_FIXEDPROPERTY"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_FIXEDPROPERTY"."purchaseDate" IS '采购时间';
COMMENT
ON COLUMN "TB_LIM_FIXEDPROPERTY"."purchasePrice" IS '采购价格';
COMMENT
ON COLUMN "TB_LIM_FIXEDPROPERTY"."status" IS '资产状态 (1使用中，2已报废)';
COMMENT
ON COLUMN "TB_LIM_FIXEDPROPERTY"."supplier" IS '供应商（Guid）';


CREATE TABLE "TB_LIM_HOLIDAYCONFIG"
(
    "id"          VARCHAR(50)  NOT NULL,
    "year"        INT          NOT NULL,
    "holidayName" VARCHAR(50)  NOT NULL,
    "beginDate"   DATE         NOT NULL,
    "endDate"     DATE         NOT NULL,
    "orgId"       VARCHAR(50)  NOT NULL,
    "creator"     VARCHAR(50)  NOT NULL,
    "createDate"  TIMESTAMP(0) NOT NULL,
    "domainId"    VARCHAR(50)  NOT NULL,
    "modifier"    VARCHAR(50)  NOT NULL,
    "modifyDate"  TIMESTAMP(0) NOT NULL,
    NOT           CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_HOLIDAYCONFIG" IS '节假日管理配置';
COMMENT
ON COLUMN "TB_LIM_HOLIDAYCONFIG"."beginDate" IS '开始时间';
COMMENT
ON COLUMN "TB_LIM_HOLIDAYCONFIG"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_HOLIDAYCONFIG"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_HOLIDAYCONFIG"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_HOLIDAYCONFIG"."endDate" IS '结束时间';
COMMENT
ON COLUMN "TB_LIM_HOLIDAYCONFIG"."holidayName" IS '节假日名称';
COMMENT
ON COLUMN "TB_LIM_HOLIDAYCONFIG"."id" IS '主键';
COMMENT
ON COLUMN "TB_LIM_HOLIDAYCONFIG"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_HOLIDAYCONFIG"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_HOLIDAYCONFIG"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_HOLIDAYCONFIG"."year" IS '年份';


CREATE TABLE "TB_LIM_INSTRUMENTCHECKRECORD"
(
    "id"            VARCHAR(50)                                                   NOT NULL,
    "instrumentId"  VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "originType"    INT            DEFAULT (-1)                                   NOT NULL,
    "checkDeptName" VARCHAR(100),
    "checkPersonId" VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "checkPerson"   VARCHAR(50),
    "checkTime"     TIMESTAMP(0)   DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "checkResult"   INT            DEFAULT 1                                      NOT NULL,
    "certiCode"     VARCHAR(100),
    "usability"     BIT            DEFAULT 0                                      NOT NULL,
    "originCyc"     DECIMAL(18, 1) DEFAULT 12.                                    NOT NULL,
    "checkContent"  VARCHAR(1000),
    "calibration"   VARCHAR(1000),
    "indicate"      VARCHAR(1000),
    "deviation"     VARCHAR(1000),
    "remark"        CLOB,
    "checkEndDate"  TIMESTAMP(0)   DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "cost"          DECIMAL(18, 2)                                                NOT NULL,
    "orgId"         VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"       VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"    TIMESTAMP(0)   DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"      VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"      VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"    TIMESTAMP(0)   DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "checkWay"      VARCHAR(50),
    NOT             CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_INSTRUMENTCHECKRECORD"."calibration" IS '校准值（修正值）';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTCHECKRECORD"."checkContent" IS '校准项';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTCHECKRECORD"."checkDeptName" IS '检定/校准单位名称';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTCHECKRECORD"."checkEndDate" IS '检定/校准有效期';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTCHECKRECORD"."checkPerson" IS '检定/校准人员';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTCHECKRECORD"."checkPersonId" IS '检定/校准人员id（Guid）';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTCHECKRECORD"."checkResult" IS '检定/校准结果(枚举：EnumOriginResult：1：合格:0：不合格)';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTCHECKRECORD"."checkTime" IS '检定/校准日期';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTCHECKRECORD"."cost" IS '费用';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTCHECKRECORD"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTCHECKRECORD"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTCHECKRECORD"."deviation" IS '误差（ABS(指示值-校准值)/校准值）';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTCHECKRECORD"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTCHECKRECORD"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTCHECKRECORD"."indicate" IS '指示值';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTCHECKRECORD"."instrumentId" IS '仪器Id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTCHECKRECORD"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTCHECKRECORD"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTCHECKRECORD"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTCHECKRECORD"."originCyc" IS '溯源周期';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTCHECKRECORD"."originType" IS '溯源方式(枚举：EnumOriginType:1检定、2校准、3自校)';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTCHECKRECORD"."remark" IS '备注';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTCHECKRECORD"."usability" IS '适用性(是、否)';


CREATE TABLE "TB_LIM_INSTRUMENTINSPECT"
(
    "id"              VARCHAR(50)                                                 NOT NULL,
    "instrumentId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "inspectPersonId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "inspectPerson"   VARCHAR(50),
    "inspectTime"     TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "inspectContent"  VARCHAR(1000),
    "inspectResult"   INT          DEFAULT 1                                      NOT NULL,
    "remark"          VARCHAR(1000),
    "cost"            DECIMAL(18, 2)                                              NOT NULL,
    "orgId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT               CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_INSTRUMENTINSPECT"."cost" IS '费用';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTINSPECT"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTINSPECT"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTINSPECT"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTINSPECT"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTINSPECT"."inspectContent" IS '期间核查内容';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTINSPECT"."inspectPerson" IS '期间核查人员';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTINSPECT"."inspectPersonId" IS '期间核查人员id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTINSPECT"."inspectResult" IS '期间核查结果(枚举：EnumInspectResult：1合格、0不合格)';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTINSPECT"."inspectTime" IS '期间核查时间';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTINSPECT"."instrumentId" IS '仪器Id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTINSPECT"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTINSPECT"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTINSPECT"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTINSPECT"."remark" IS '备注';


CREATE TABLE "TB_LIM_INSTRUMENTMAINTAINRECORD"
(
    "id"               VARCHAR(50)                                                 NOT NULL,
    "instrumentId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "maintainDeptName" VARCHAR(100),
    "startTime"        TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "endTime"          TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "mainTainPersonId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "maintainPerson"   VARCHAR(50),
    "maintainContent"  VARCHAR(1000),
    "maintainRule"     VARCHAR(1000),
    "maintainremark"   VARCHAR(1000),
    "cost"             DECIMAL(18, 2)                                              NOT NULL,
    "maintainTypeId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "orgId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "temperature"      VARCHAR(50)  DEFAULT '',
    "humidity"         VARCHAR(50)  DEFAULT '',
    NOT                CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_INSTRUMENTMAINTAINRECORD"."cost" IS '费用';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTMAINTAINRECORD"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTMAINTAINRECORD"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTMAINTAINRECORD"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTMAINTAINRECORD"."endTime" IS '结束维护时间';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTMAINTAINRECORD"."humidity" IS '相对湿度';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTMAINTAINRECORD"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTMAINTAINRECORD"."instrumentId" IS '设备Id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTMAINTAINRECORD"."maintainContent" IS '维护内容';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTMAINTAINRECORD"."maintainDeptName" IS '维护单位';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTMAINTAINRECORD"."maintainPerson" IS '维护人员';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTMAINTAINRECORD"."mainTainPersonId" IS '维护人员id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTMAINTAINRECORD"."maintainremark" IS '备注';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTMAINTAINRECORD"."maintainRule" IS '维护规则';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTMAINTAINRECORD"."maintainTypeId" IS '维护类型（3.2保留,及时未启用）';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTMAINTAINRECORD"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTMAINTAINRECORD"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTMAINTAINRECORD"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTMAINTAINRECORD"."startTime" IS '开始维护时间';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTMAINTAINRECORD"."temperature" IS '温度';


CREATE TABLE "TB_LIM_INSTRUMENTREPAIRRECORD"
(
    "id"                      VARCHAR(50)                                                 NOT NULL,
    "instrumentId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "purchaseApplyID"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "requestNoteCode"         VARCHAR(20),
    "repairResult"            INT          DEFAULT 1                                      NOT NULL,
    "failureDesc"             VARCHAR(1000),
    "failureDescPersonId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "failureDescPerson"       VARCHAR(50),
    "failureDescDate"         TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "repairResultCheckerId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "repairResultChecker"     VARCHAR(50),
    "repairResultCheckDate"   TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "repairContentRecorderId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "repairContentRecorder"   VARCHAR(50),
    "repairContentRecordDate" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "cost"                    DECIMAL(18, 2)                                              NOT NULL,
    "repairContent"           VARCHAR(1000),
    "checkContent"            VARCHAR(1000),
    "remark"                  VARCHAR(1000),
    "orgId"                   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"                 VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"              TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"                VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"                VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"              TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT                       CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_INSTRUMENTREPAIRRECORD"."checkContent" IS '验收情况';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTREPAIRRECORD"."cost" IS '费用';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTREPAIRRECORD"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTREPAIRRECORD"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTREPAIRRECORD"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTREPAIRRECORD"."failureDesc" IS '设备故障描述';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTREPAIRRECORD"."failureDescDate" IS '描述日期';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTREPAIRRECORD"."failureDescPerson" IS '描述人';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTREPAIRRECORD"."failureDescPersonId" IS '描述人id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTREPAIRRECORD"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTREPAIRRECORD"."instrumentId" IS '仪器Id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTREPAIRRECORD"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTREPAIRRECORD"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTREPAIRRECORD"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTREPAIRRECORD"."purchaseApplyID" IS '维修申请ID';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTREPAIRRECORD"."remark" IS '备注';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTREPAIRRECORD"."repairContent" IS '维修内容';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTREPAIRRECORD"."repairContentRecordDate" IS '维修记录日期';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTREPAIRRECORD"."repairContentRecorder" IS '维修记录人';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTREPAIRRECORD"."repairContentRecorderId" IS '维修记录人id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTREPAIRRECORD"."repairResult" IS '维修结果(枚举：EnumRepairRusult：1合格、0不合格)';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTREPAIRRECORD"."repairResultCheckDate" IS '验证日期';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTREPAIRRECORD"."repairResultChecker" IS '验证人';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTREPAIRRECORD"."repairResultCheckerId" IS '验证人id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTREPAIRRECORD"."requestNoteCode" IS '维修申请单号';


CREATE TABLE "TB_LIM_INSTRUMENTSTORAGE"
(
    "id"                VARCHAR(50)                                                 NOT NULL,
    "purchaseDetailId"  VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "instrumentId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "instrumentsCode"   VARCHAR(20),
    "instrumentTypeId"  VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "instrumentName"    VARCHAR(255),
    "model"             VARCHAR(255),
    "operator"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "operatorName"      VARCHAR(50),
    "storagNum"         INT          DEFAULT 0                                      NOT NULL,
    "storagTtime"       TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "balance"           DECIMAL(18, 2)                                              NOT NULL,
    "unitPrice"         DECIMAL(18, 2)                                              NOT NULL,
    "totalPrice"        DECIMAL(18, 2)                                              NOT NULL,
    "supplyCompanyId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "supplyCompanyName" VARCHAR(100),
    "serialNo"          VARCHAR(20),
    "factoryName"       VARCHAR(100),
    "isDeleted"         BIT          DEFAULT 0                                      NOT NULL,
    "orgId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT                 CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_INSTRUMENTSTORAGE" IS '仪器入库记录';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTSTORAGE"."balance" IS '入库结存';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTSTORAGE"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTSTORAGE"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTSTORAGE"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTSTORAGE"."factoryName" IS '制造厂商名称';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTSTORAGE"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTSTORAGE"."instrumentId" IS '仪器Id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTSTORAGE"."instrumentName" IS '设备名称';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTSTORAGE"."instrumentsCode" IS '本站编号';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTSTORAGE"."instrumentTypeId" IS '仪器类型（常量：LIM_InstrumentType）';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTSTORAGE"."isDeleted" IS '假删';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTSTORAGE"."model" IS '规格型号';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTSTORAGE"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTSTORAGE"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTSTORAGE"."operator" IS '入库人id（Guid）';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTSTORAGE"."operatorName" IS '入库人姓名';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTSTORAGE"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTSTORAGE"."purchaseDetailId" IS '仪器采购明细标识（Guid）';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTSTORAGE"."serialNo" IS '出厂编号';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTSTORAGE"."storagNum" IS '入库数量';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTSTORAGE"."storagTtime" IS '入库时间';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTSTORAGE"."supplyCompanyId" IS '供应商Id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTSTORAGE"."supplyCompanyName" IS '供应商名称';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTSTORAGE"."totalPrice" IS '总价';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTSTORAGE"."unitPrice" IS '单价';


CREATE TABLE "TB_LIM_INSTRUMENTUSERECORD"
(
    "id"                    VARCHAR(50)                                                 NOT NULL,
    "instrumentId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "objectId"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "environmentalManageId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "objectType"            INT          DEFAULT (-1)                                   NOT NULL,
    "usePersonId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "startTime"             TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "endTime"               TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "testIds"               VARCHAR(1000),
    "temperature"           VARCHAR(50),
    "humidity"              VARCHAR(50),
    "pressure"              VARCHAR(50),
    "beforeUseSituation"    VARCHAR(255),
    "beforeAfterSituation"  VARCHAR(255),
    "isAssistInstrument"    BIT          DEFAULT 0                                      NOT NULL,
    "remark"                VARCHAR(1000),
    "insOriginDate"         TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "orgId"                 VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"               VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"            TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"            TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "isDeleted"             BIT          DEFAULT 0                                      NOT NULL,
    NOT                     CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD"."beforeAfterSituation" IS '使用后情况';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD"."beforeUseSituation" IS '使用前情况';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD"."endTime" IS '结束使用时间';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD"."environmentalManageId" IS '环境记录id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD"."humidity" IS '湿度';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD"."insOriginDate" IS '仪器的有效期';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD"."instrumentId" IS '仪器id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD"."isAssistInstrument" IS '是否辅助仪器';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD"."isDeleted" IS '假删字段';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD"."objectId" IS '表单id（送样单-采样、领样单-现场分析、工作单-实验室）';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD"."objectType" IS '使用对象类型（枚举EnumInsUseObjType：1采样，2：实验室分析，4：现场分析）';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD"."pressure" IS '大气压';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD"."remark" IS '备注';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD"."startTime" IS '开始使用时间';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD"."temperature" IS '温度';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD"."testIds" IS '测试项目id支持多条';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD"."usePersonId" IS '使用人id';


CREATE INDEX "IX_INSUES_RECORD_OBJECTID" ON "TB_LIM_INSTRUMENTUSERECORD" ("objectId" ASC, "objectType" ASC, "orgId" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "TB_LIM_INSTRUMENTUSERECORD2SAMPLE"
(
    "id"                    VARCHAR(50)                                                NOT NULL,
    "instrumentUseRecordId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "sampleId"              VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    NOT                     CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD2SAMPLE"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD2SAMPLE"."instrumentUseRecordId" IS '仪器使用信息id';
COMMENT
ON COLUMN "TB_LIM_INSTRUMENTUSERECORD2SAMPLE"."sampleId" IS '样品id';


CREATE TABLE "TB_LIM_ITEMRELATION"
(
    "id"           VARCHAR(50)                                                 NOT NULL,
    "leftFormula"  VARCHAR(500) DEFAULT ''                                     NOT NULL,
    "rightFormula" VARCHAR(500) DEFAULT '',
    "formula"      VARCHAR(500) DEFAULT '',
    "symbolType"   INT          DEFAULT (-1)                                   NOT NULL,
    "configDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "type"         INT          DEFAULT (-1)                                   NOT NULL,
    "orgId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT            CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_ITEMRELATION"."configDate" IS '配置日期';
COMMENT
ON COLUMN "TB_LIM_ITEMRELATION"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_ITEMRELATION"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_ITEMRELATION"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_ITEMRELATION"."formula" IS '公式';
COMMENT
ON COLUMN "TB_LIM_ITEMRELATION"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_ITEMRELATION"."leftFormula" IS '分析项目Id';
COMMENT
ON COLUMN "TB_LIM_ITEMRELATION"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_ITEMRELATION"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_ITEMRELATION"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_ITEMRELATION"."rightFormula" IS '分析项目名称';
COMMENT
ON COLUMN "TB_LIM_ITEMRELATION"."symbolType" IS '公式（1.等于，2.大于，3.小于等）';
COMMENT
ON COLUMN "TB_LIM_ITEMRELATION"."type" IS '类型（枚举EnumAnalyzeItemRelationType：1.自检，2.上报）';


CREATE TABLE "TB_LIM_ITEMRELATIONPARAMS"
(
    "id"              VARCHAR(50)                                                 NOT NULL,
    "relationId"      VARCHAR(50)  DEFAULT ''                                     NOT NULL,
    "analyzeItemId"   VARCHAR(50)  DEFAULT ''                                     NOT NULL,
    "analyzeItemName" VARCHAR(50)  DEFAULT '',
    "position"        INT          DEFAULT (-1)                                   NOT NULL,
    "orderNum"        INT          DEFAULT 0                                      NOT NULL,
    "orgId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT               CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_ITEMRELATIONPARAMS"."analyzeItemId" IS '分析项目Id';
COMMENT
ON COLUMN "TB_LIM_ITEMRELATIONPARAMS"."analyzeItemName" IS '分析项目名称';
COMMENT
ON COLUMN "TB_LIM_ITEMRELATIONPARAMS"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_ITEMRELATIONPARAMS"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_ITEMRELATIONPARAMS"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_ITEMRELATIONPARAMS"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_ITEMRELATIONPARAMS"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_ITEMRELATIONPARAMS"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_ITEMRELATIONPARAMS"."orderNum" IS '排序值（预留：列表显示排序用）';
COMMENT
ON COLUMN "TB_LIM_ITEMRELATIONPARAMS"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_ITEMRELATIONPARAMS"."position" IS '位置（1.左，2.右，-1.没有）';
COMMENT
ON COLUMN "TB_LIM_ITEMRELATIONPARAMS"."relationId" IS '分析项目关系';


CREATE TABLE "TB_LIM_MESSAGESENDRECORD"
(
    "id"             VARCHAR(50)                                                NOT NULL,
    "jobId"          VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "messageType"    VARCHAR(100)                                               NOT NULL,
    "messageContent" CLOB,
    "receiver"       VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "sendTime"       TIMESTAMP(0),
    "status"         INT         DEFAULT 0                                      NOT NULL,
    "isConcern"      BIT         DEFAULT 0                                      NOT NULL,
    "sendType"       INT         DEFAULT 1                                      NOT NULL,
    "orgId"          VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "domainId"       VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_MESSAGESENDRECORD" IS '消息发送记录';
COMMENT
ON COLUMN "TB_LIM_MESSAGESENDRECORD"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_MESSAGESENDRECORD"."id" IS '主键id';
COMMENT
ON COLUMN "TB_LIM_MESSAGESENDRECORD"."isConcern" IS '是否重点关注';
COMMENT
ON COLUMN "TB_LIM_MESSAGESENDRECORD"."jobId" IS '任务id';
COMMENT
ON COLUMN "TB_LIM_MESSAGESENDRECORD"."messageContent" IS '消息内容';
COMMENT
ON COLUMN "TB_LIM_MESSAGESENDRECORD"."messageType" IS '消息类型';
COMMENT
ON COLUMN "TB_LIM_MESSAGESENDRECORD"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_MESSAGESENDRECORD"."receiver" IS '接收人id';
COMMENT
ON COLUMN "TB_LIM_MESSAGESENDRECORD"."sendTime" IS '发送时间';
COMMENT
ON COLUMN "TB_LIM_MESSAGESENDRECORD"."sendType" IS '1：平台  2：短信  4：APP  8：微信  16：钉钉';
COMMENT
ON COLUMN "TB_LIM_MESSAGESENDRECORD"."status" IS '状态 (0：未读，1：已读)';


CREATE TABLE "TB_LIM_NOTICE"
(
    "id"          VARCHAR(50)                                                  NOT NULL,
    "title"       VARCHAR(255)                                                 NOT NULL,
    "category"    VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "content"     CLOB,
    "isRelease"   BIT           DEFAULT 0                                      NOT NULL,
    "releaseId"   VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "releaseMan"  VARCHAR(255),
    "releaseTime" TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "isTop"       BIT           DEFAULT 0                                      NOT NULL,
    "label"       VARCHAR(1000) DEFAULT '00000000-0000-0000-0000-000000000000',
    "status"      VARCHAR(50),
    "orderNum"    INT           DEFAULT 0                                      NOT NULL,
    "clickNumber" INT           DEFAULT 0                                      NOT NULL,
    "orgId"       VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"     VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"  TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"    VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"    VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"  TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT           CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_NOTICE"."category" IS '公告类型（常量）：LIM_NoticeCategory(1通知、2行政、4标准规范、8内部管理、16其他)';
COMMENT
ON COLUMN "TB_LIM_NOTICE"."clickNumber" IS '浏览次数';
COMMENT
ON COLUMN "TB_LIM_NOTICE"."content" IS '公告内容';
COMMENT
ON COLUMN "TB_LIM_NOTICE"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_NOTICE"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_NOTICE"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_NOTICE"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_NOTICE"."isRelease" IS '是否发布（0.不发布 1.发布）';
COMMENT
ON COLUMN "TB_LIM_NOTICE"."isTop" IS '是否置顶(0.不置顶 1.置顶)';
COMMENT
ON COLUMN "TB_LIM_NOTICE"."label" IS '公告标签（常量）：LIM_NoticeLabel（一般、紧急、重要）';
COMMENT
ON COLUMN "TB_LIM_NOTICE"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_NOTICE"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_NOTICE"."orderNum" IS '排序值';
COMMENT
ON COLUMN "TB_LIM_NOTICE"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_NOTICE"."releaseId" IS '发布人id';
COMMENT
ON COLUMN "TB_LIM_NOTICE"."releaseMan" IS '发布人';
COMMENT
ON COLUMN "TB_LIM_NOTICE"."releaseTime" IS '发布时间';
COMMENT
ON COLUMN "TB_LIM_NOTICE"."status" IS '状态';
COMMENT
ON COLUMN "TB_LIM_NOTICE"."title" IS '公告标题';


CREATE TABLE "TB_LIM_NOTICEMSG"
(
    "id"              VARCHAR(50)                                                 NOT NULL,
    "noticeId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "content"         CLOB,
    "messagePersonId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "messagePerson"   VARCHAR(50),
    "msgTime"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "status"          INT          DEFAULT 1                                      NOT NULL,
    "orgId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT               CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_NOTICEMSG"."content" IS '内容';
COMMENT
ON COLUMN "TB_LIM_NOTICEMSG"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_NOTICEMSG"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_NOTICEMSG"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_NOTICEMSG"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_NOTICEMSG"."messagePerson" IS '留言人';
COMMENT
ON COLUMN "TB_LIM_NOTICEMSG"."messagePersonId" IS '留言人id';
COMMENT
ON COLUMN "TB_LIM_NOTICEMSG"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_NOTICEMSG"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_NOTICEMSG"."msgTime" IS '留言时间';
COMMENT
ON COLUMN "TB_LIM_NOTICEMSG"."noticeId" IS '公告id';
COMMENT
ON COLUMN "TB_LIM_NOTICEMSG"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_NOTICEMSG"."status" IS '状态（枚举EnumNoticeMsgStatus：1.正常 2.置顶 3.精华 4.假删）：1正常 2置顶 3精华 4假删  等情况预留用）';


CREATE TABLE "TB_LIM_OACONSUMABLEPICKLISTSDETAIL"
(
    "id"             VARCHAR(50)                                                 NOT NULL,
    "consumableId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "consumableName" VARCHAR(255),
    "specification"  VARCHAR(255),
    "materialNum"    DECIMAL(18, 2)                                              NOT NULL,
    "materialUnit"   VARCHAR(100),
    "materialUse"    VARCHAR(100),
    "remark"         VARCHAR(500),
    "gradeName"      VARCHAR(100),
    "codeInStation"  VARCHAR(100),
    "orgId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPICKLISTSDETAIL"."codeInStation" IS '标准编号';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPICKLISTSDETAIL"."consumableId" IS '消耗品Id';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPICKLISTSDETAIL"."consumableName" IS '名称';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPICKLISTSDETAIL"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPICKLISTSDETAIL"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPICKLISTSDETAIL"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPICKLISTSDETAIL"."gradeName" IS '等级';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPICKLISTSDETAIL"."id" IS 'Id';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPICKLISTSDETAIL"."materialNum" IS '领用数量';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPICKLISTSDETAIL"."materialUnit" IS '单位';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPICKLISTSDETAIL"."materialUse" IS '用途';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPICKLISTSDETAIL"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPICKLISTSDETAIL"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPICKLISTSDETAIL"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPICKLISTSDETAIL"."remark" IS '备注';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPICKLISTSDETAIL"."specification" IS '规格';


CREATE TABLE "TB_LIM_OACONSUMABLEPURCHASEDETAIL"
(
    "id"                VARCHAR(50)                                                 NOT NULL,
    "consumableId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "consumableName"    VARCHAR(50)                                                 NOT NULL,
    "codeInStation"     VARCHAR(50),
    "consumableCode"    VARCHAR(20),
    "materialType"      INT          DEFAULT 1                                      NOT NULL,
    "materialModel"     VARCHAR(50),
    "gradeId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "dimensionId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "dimensionName"     VARCHAR(50),
    "categoryId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "planNum"           INT          DEFAULT 0                                      NOT NULL,
    "surplusNum"        INT          DEFAULT 0                                      NOT NULL,
    "supplyTime"        TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "purpose"           VARCHAR(100),
    "skillRequire"      VARCHAR(255),
    "isPoison"          BIT          DEFAULT 0                                      NOT NULL,
    "keepCondition"     VARCHAR(1000),
    "safetyInstruction" VARCHAR(1000),
    "dilutedSolution"   VARCHAR(255),
    "dilutionMethod"    VARCHAR(255),
    "concentration"     VARCHAR(255),
    "uncertainty"       VARCHAR(255),
    "isMixedStandard"   BIT          DEFAULT 0                                      NOT NULL,
    "remark"            VARCHAR(1000),
    "isDeleted"         BIT          DEFAULT 0                                      NOT NULL,
    "orgId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "brand"             VARCHAR(255),
    "unitPrice"         DECIMAL(10, 2),
    "budgetAmount"      DECIMAL(10, 2),
    "articleNo"         VARCHAR(255),
    "userId"            VARCHAR(50),
    NOT                 CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_OACONSUMABLEPURCHASEDETAIL" IS '消耗品采购明细';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."articleNo" IS '货号';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."brand" IS '品牌';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."budgetAmount" IS '预算总额';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."categoryId" IS '类别常量（Guid）（LIM_ConsumableCategory:高压气体、易制毒品、化学试剂等）';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."codeInStation" IS '编号（本站编号）';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."concentration" IS '浓度';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."consumableCode" IS '标样编号';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."consumableId" IS '消耗品标识（Guid）';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."consumableName" IS '消耗品名称';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."dilutedSolution" IS '稀释液';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."dilutionMethod" IS '稀释方法';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."dimensionId" IS '计量单位Id（Guid）';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."dimensionName" IS '计量单位名称';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."gradeId" IS '等级常量（Guid）（LIM_ConsumableGrade:进口、分析纯、FMP、高纯等）';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."isDeleted" IS '假删';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."isMixedStandard" IS '是否混标（0代表否  1代表是）';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."isPoison" IS '是否易制毒';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."keepCondition" IS '保存条件';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."materialModel" IS '规格型号';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."materialType" IS '物资种类（枚举EnumMaterialType：1.消耗品 2.标样）';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."planNum" IS '申请计划数量';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."purpose" IS '用途';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."remark" IS '备注';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."safetyInstruction" IS '安全须知';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."skillRequire" IS '技术要求';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."supplyTime" IS '供应时间';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."surplusNum" IS '剩余未入库数量';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."uncertainty" IS '不确定度';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."unitPrice" IS '单价';
COMMENT
ON COLUMN "TB_LIM_OACONSUMABLEPURCHASEDETAIL"."userId" IS '使用人id';


CREATE TABLE "TB_LIM_OACONTRACT"
(
    "id"           VARCHAR(50)                                                 NOT NULL,
    "contractId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "contractName" VARCHAR(255)                                                NOT NULL,
    "contractCode" VARCHAR(50),
    "type"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "period"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "totalAmount"  DECIMAL(18, 2)                                              NOT NULL,
    "salesManId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "salesManName" VARCHAR(50),
    "signDate"     TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "timeBegin"    TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "timeEnd"      TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "entName"      VARCHAR(50),
    "linkMan"      VARCHAR(50),
    "linkPhone"    VARCHAR(50),
    "address"      VARCHAR(100),
    "explains"     VARCHAR(255),
    "attentions"   VARCHAR(255),
    "remark"       VARCHAR(1000),
    "isDeleted"    BIT          DEFAULT 0                                      NOT NULL,
    "orgId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT            CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_OACONTRACT" IS 'oa的合同管理信息';
COMMENT
ON COLUMN "TB_LIM_OACONTRACT"."address" IS '地址';
COMMENT
ON COLUMN "TB_LIM_OACONTRACT"."attentions" IS '注意事项';
COMMENT
ON COLUMN "TB_LIM_OACONTRACT"."contractCode" IS '合同编号';
COMMENT
ON COLUMN "TB_LIM_OACONTRACT"."contractName" IS '合同名称';
COMMENT
ON COLUMN "TB_LIM_OACONTRACT"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_OACONTRACT"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_OACONTRACT"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_OACONTRACT"."entName" IS '单位名称';
COMMENT
ON COLUMN "TB_LIM_OACONTRACT"."explains" IS '工期要求说明';
COMMENT
ON COLUMN "TB_LIM_OACONTRACT"."id" IS '主键id';
COMMENT
ON COLUMN "TB_LIM_OACONTRACT"."isDeleted" IS '假删';
COMMENT
ON COLUMN "TB_LIM_OACONTRACT"."linkMan" IS '联系人';
COMMENT
ON COLUMN "TB_LIM_OACONTRACT"."linkPhone" IS '联系方式';
COMMENT
ON COLUMN "TB_LIM_OACONTRACT"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_OACONTRACT"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_OACONTRACT"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_OACONTRACT"."period" IS '合同周期（常量：长期检测合同、单次检测合同    编码：LIM_ContractPeriod）';
COMMENT
ON COLUMN "TB_LIM_OACONTRACT"."remark" IS '备注';
COMMENT
ON COLUMN "TB_LIM_OACONTRACT"."salesManId" IS '业务员id';
COMMENT
ON COLUMN "TB_LIM_OACONTRACT"."salesManName" IS '业务员';
COMMENT
ON COLUMN "TB_LIM_OACONTRACT"."signDate" IS '签订日期';
COMMENT
ON COLUMN "TB_LIM_OACONTRACT"."timeBegin" IS '开始日期';
COMMENT
ON COLUMN "TB_LIM_OACONTRACT"."timeEnd" IS '结束日期';
COMMENT
ON COLUMN "TB_LIM_OACONTRACT"."totalAmount" IS '总金额';
COMMENT
ON COLUMN "TB_LIM_OACONTRACT"."type" IS '合同类型（常量：收款合同、委外合同    编码:LIM_ContractType）';


CREATE TABLE "TB_LIM_OAFILEABOLISH"
(
    "id"            VARCHAR(50)                                                 NOT NULL,
    "fileName"      VARCHAR(100)                                                NOT NULL,
    "fileId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "abolishReason" VARCHAR(1000),
    "abolishDate"   TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "isDeleted"     BIT          DEFAULT 0                                      NOT NULL,
    "orgId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"    TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"    TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT             CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_OAFILEABOLISH" IS '文件废止';
COMMENT
ON COLUMN "TB_LIM_OAFILEABOLISH"."abolishDate" IS '废止日期';
COMMENT
ON COLUMN "TB_LIM_OAFILEABOLISH"."abolishReason" IS '废止原因';
COMMENT
ON COLUMN "TB_LIM_OAFILEABOLISH"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_OAFILEABOLISH"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_OAFILEABOLISH"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_OAFILEABOLISH"."fileId" IS '文件标识关联';
COMMENT
ON COLUMN "TB_LIM_OAFILEABOLISH"."fileName" IS '文件名称';
COMMENT
ON COLUMN "TB_LIM_OAFILEABOLISH"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_OAFILEABOLISH"."isDeleted" IS '假删';
COMMENT
ON COLUMN "TB_LIM_OAFILEABOLISH"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_OAFILEABOLISH"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_OAFILEABOLISH"."orgId" IS '组织机构id';


CREATE TABLE "TB_LIM_OAFILECONTROL"
(
    "id"          VARCHAR(50)                                                 NOT NULL,
    "fileName"    VARCHAR(100)                                                NOT NULL,
    "fileId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "controlCode" VARCHAR(100),
    "fileCode"    VARCHAR(100),
    "version"     VARCHAR(50),
    "fileTypeId"  VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "maker"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "makerName"   VARCHAR(50),
    "compileTime" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "isDeleted"   BIT          DEFAULT 0                                      NOT NULL,
    "orgId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT           CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_OAFILECONTROL" IS '文件受控';
COMMENT
ON COLUMN "TB_LIM_OAFILECONTROL"."compileTime" IS '编制时间';
COMMENT
ON COLUMN "TB_LIM_OAFILECONTROL"."controlCode" IS '受控编号';
COMMENT
ON COLUMN "TB_LIM_OAFILECONTROL"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_OAFILECONTROL"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_OAFILECONTROL"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_OAFILECONTROL"."fileCode" IS '文件编号';
COMMENT
ON COLUMN "TB_LIM_OAFILECONTROL"."fileId" IS '文件标识关联';
COMMENT
ON COLUMN "TB_LIM_OAFILECONTROL"."fileName" IS '文件名称';
COMMENT
ON COLUMN "TB_LIM_OAFILECONTROL"."fileTypeId" IS '文件类型（常量LIM_FileControlType：程序文件、标准文件、质量手册、作业指导书、记录单）';
COMMENT
ON COLUMN "TB_LIM_OAFILECONTROL"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_OAFILECONTROL"."isDeleted" IS '假删';
COMMENT
ON COLUMN "TB_LIM_OAFILECONTROL"."maker" IS '编制人id';
COMMENT
ON COLUMN "TB_LIM_OAFILECONTROL"."makerName" IS '编制人名称';
COMMENT
ON COLUMN "TB_LIM_OAFILECONTROL"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_OAFILECONTROL"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_OAFILECONTROL"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_OAFILECONTROL"."version" IS '版本号';


CREATE TABLE "TB_LIM_OAFILEREVISION"
(
    "id"                VARCHAR(50)                                                 NOT NULL,
    "fileName"          VARCHAR(100)                                                NOT NULL,
    "fileId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "fileCode"          VARCHAR(100),
    "fileTypeId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "makerId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "makerName"         VARCHAR(50),
    "compileTime"       TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "sourceControlCode" VARCHAR(100),
    "sourceVersion"     VARCHAR(50)                                                 NOT NULL,
    "controlCode"       VARCHAR(100),
    "version"           VARCHAR(50)                                                 NOT NULL,
    "reviseDate"        TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "reviseContent"     VARCHAR(1000),
    "isDeleted"         BIT          DEFAULT 0                                      NOT NULL,
    "orgId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT                 CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_OAFILEREVISION" IS '文件修订';
COMMENT
ON COLUMN "TB_LIM_OAFILEREVISION"."compileTime" IS '编制时间';
COMMENT
ON COLUMN "TB_LIM_OAFILEREVISION"."controlCode" IS '受控编号';
COMMENT
ON COLUMN "TB_LIM_OAFILEREVISION"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_OAFILEREVISION"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_OAFILEREVISION"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_OAFILEREVISION"."fileCode" IS '文件编号';
COMMENT
ON COLUMN "TB_LIM_OAFILEREVISION"."fileId" IS '文件标识关联';
COMMENT
ON COLUMN "TB_LIM_OAFILEREVISION"."fileName" IS '文件名称';
COMMENT
ON COLUMN "TB_LIM_OAFILEREVISION"."fileTypeId" IS '文件类型（常量LIM_FileControlType：程序文件、标准文件、质量手册、作业指导书、记录单）';
COMMENT
ON COLUMN "TB_LIM_OAFILEREVISION"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_OAFILEREVISION"."isDeleted" IS '假删';
COMMENT
ON COLUMN "TB_LIM_OAFILEREVISION"."makerId" IS '编制人id';
COMMENT
ON COLUMN "TB_LIM_OAFILEREVISION"."makerName" IS '编制人名称';
COMMENT
ON COLUMN "TB_LIM_OAFILEREVISION"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_OAFILEREVISION"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_OAFILEREVISION"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_OAFILEREVISION"."reviseContent" IS '修订内容';
COMMENT
ON COLUMN "TB_LIM_OAFILEREVISION"."reviseDate" IS '修订日期';
COMMENT
ON COLUMN "TB_LIM_OAFILEREVISION"."sourceControlCode" IS '原受控编号';
COMMENT
ON COLUMN "TB_LIM_OAFILEREVISION"."sourceVersion" IS '原版本号';
COMMENT
ON COLUMN "TB_LIM_OAFILEREVISION"."version" IS '版本号';


CREATE TABLE "TB_LIM_OAINSTRUMENTPURCHASEDETAIL"
(
    "id"             VARCHAR(50)                                                 NOT NULL,
    "instrumentId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "instrumentName" VARCHAR(50)                                                 NOT NULL,
    "materialModel"  VARCHAR(50),
    "planNum"        INT          DEFAULT 0                                      NOT NULL,
    "surplusNum"     INT          DEFAULT 0                                      NOT NULL,
    "supplyTime"     TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "purpose"        VARCHAR(100),
    "skillRequire"   VARCHAR(255),
    "remark"         VARCHAR(1000),
    "isDeleted"      BIT          DEFAULT 0                                      NOT NULL,
    "orgId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_OAINSTRUMENTPURCHASEDETAIL" IS '仪器采购明细';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTPURCHASEDETAIL"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTPURCHASEDETAIL"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTPURCHASEDETAIL"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTPURCHASEDETAIL"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTPURCHASEDETAIL"."instrumentId" IS '仪器标识（Guid）';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTPURCHASEDETAIL"."instrumentName" IS '仪器名称';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTPURCHASEDETAIL"."isDeleted" IS '假删';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTPURCHASEDETAIL"."materialModel" IS '规格型号';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTPURCHASEDETAIL"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTPURCHASEDETAIL"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTPURCHASEDETAIL"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTPURCHASEDETAIL"."planNum" IS '申请计划数量';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTPURCHASEDETAIL"."purpose" IS '用途';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTPURCHASEDETAIL"."remark" IS '备注';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTPURCHASEDETAIL"."skillRequire" IS '技术要求';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTPURCHASEDETAIL"."supplyTime" IS '供应时间';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTPURCHASEDETAIL"."surplusNum" IS '剩余未入库数量';


CREATE TABLE "TB_LIM_OAINSTRUMENTREPAIRAPPLY"
(
    "id"           VARCHAR(50)                                                 NOT NULL,
    "instrumentId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "failureDesc"  VARCHAR(1000),
    "remark"       VARCHAR(1000),
    "orgId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT            CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_OAINSTRUMENTREPAIRAPPLY" IS '仪器的维修申请';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTREPAIRAPPLY"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTREPAIRAPPLY"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTREPAIRAPPLY"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTREPAIRAPPLY"."failureDesc" IS '设备故障描述';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTREPAIRAPPLY"."id" IS '主键id';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTREPAIRAPPLY"."instrumentId" IS '仪器Id';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTREPAIRAPPLY"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTREPAIRAPPLY"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTREPAIRAPPLY"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTREPAIRAPPLY"."remark" IS '备注';


CREATE TABLE "TB_LIM_OAINSTRUMENTSCRAP"
(
    "id"           VARCHAR(50)                                                 NOT NULL,
    "instrumentId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "scrapDesc"    VARCHAR(1000),
    "remark"       VARCHAR(1000),
    "isDeleted"    BIT          DEFAULT 0                                      NOT NULL,
    "orgId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT            CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_OAINSTRUMENTSCRAP" IS '仪器报废';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTSCRAP"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTSCRAP"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTSCRAP"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTSCRAP"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTSCRAP"."instrumentId" IS '仪器标识';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTSCRAP"."isDeleted" IS '假删';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTSCRAP"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTSCRAP"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTSCRAP"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTSCRAP"."remark" IS '备注';
COMMENT
ON COLUMN "TB_LIM_OAINSTRUMENTSCRAP"."scrapDesc" IS '报废描述';


CREATE TABLE "TB_LIM_OCRCONFIG"
(
    "id"         VARCHAR(50)                                                 NOT NULL,
    "configName" VARCHAR(255)                                                NOT NULL,
    "isDeleted"  BIT          DEFAULT 0                                      NOT NULL,
    "orgId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "domainId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "modifier"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT          CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_OCRCONFIG"."configName" IS '对象名称';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIG"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIG"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIG"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIG"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIG"."isDeleted" IS '假删字段';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIG"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIG"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIG"."orgId" IS '组织机构id';


CREATE TABLE "TB_LIM_OCRCONFIGPARAM"
(
    "id"             VARCHAR(50)                                                NOT NULL,
    "configId"       VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "paramName"      VARCHAR(255)                                               NOT NULL,
    "paramNameAlias" VARCHAR(255)                                               NOT NULL,
    "paramType"      INT         DEFAULT (-1)                                   NOT NULL,
    "dimension"      VARCHAR(50) DEFAULT '',
    "regexBegin"     VARCHAR(255),
    "regexEnd"       VARCHAR(255),
    "analyzeItemId"  VARCHAR(4000),
    "maxLength"      INT         DEFAULT 20                                     NOT NULL,
    "orderNum"       INT         DEFAULT 0                                      NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_OCRCONFIGPARAM"."analyzeItemId" IS '分析项目标识';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGPARAM"."configId" IS 'OCR对象标识';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGPARAM"."dimension" IS '量纲';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGPARAM"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGPARAM"."maxLength" IS '最大截取长度';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGPARAM"."orderNum" IS '排序值';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGPARAM"."paramName" IS '参数名称';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGPARAM"."paramNameAlias" IS '参数别名';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGPARAM"."paramType" IS '类型（枚举EnumOcrConfigParamType：样品参数1、现场数据2、现场公式3）';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGPARAM"."regexBegin" IS '起始字符串';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGPARAM"."regexEnd" IS '结束字符串';


CREATE TABLE "TB_LIM_OCRCONFIGPARAMDATA"
(
    "id"            VARCHAR(50)                                                 NOT NULL,
    "configParamId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "saveValue"     VARCHAR(255)                                                NOT NULL,
    "orgId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "domainId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"    TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "modifier"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"    TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "isDeleted"     BIT          DEFAULT 0                                      NOT NULL,
    "recordId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    NOT             CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_OCRCONFIGPARAMDATA"."configParamId" IS 'OCR对象参数标识';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGPARAMDATA"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGPARAMDATA"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGPARAMDATA"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGPARAMDATA"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGPARAMDATA"."isDeleted" IS '假删字段';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGPARAMDATA"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGPARAMDATA"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGPARAMDATA"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGPARAMDATA"."recordId" IS '识别记录标识';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGPARAMDATA"."saveValue" IS '参数值';


CREATE INDEX "UIX_tb_lim_ocrconfigparamdata" ON "TB_LIM_OCRCONFIGPARAMDATA" ("recordId" ASC, "isDeleted" ASC, "orgId" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "TB_LIM_OCRCONFIGRECORD"
(
    "id"         VARCHAR(50)                                                 NOT NULL,
    "configId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "sampleCode" VARCHAR(50)                                                 NOT NULL,
    "groupId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "groupName"  VARCHAR(255),
    "filePath"   VARCHAR(500)                                                NOT NULL,
    "originData" TEXT,
    "isDeleted"  BIT          DEFAULT 0                                      NOT NULL,
    "orgId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "domainId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "modifier"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT          CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_OCRCONFIGRECORD"."configId" IS 'OCR对象标识';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGRECORD"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGRECORD"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGRECORD"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGRECORD"."filePath" IS '文件保存路径';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGRECORD"."groupId" IS '分组标识';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGRECORD"."groupName" IS '分组名称';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGRECORD"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGRECORD"."isDeleted" IS '假删字段';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGRECORD"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGRECORD"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGRECORD"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGRECORD"."originData" IS 'ocr原始数据';
COMMENT
ON COLUMN "TB_LIM_OCRCONFIGRECORD"."sampleCode" IS '样品编号';


CREATE INDEX "UIX_tb_lim_ocrConfigRecord" ON "TB_LIM_OCRCONFIGRECORD" ("configId" ASC, "sampleCode" ASC, "groupId" ASC,
                                                                       "isDeleted" ASC, "orgId"
                                                                       ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "TB_LIM_OTHEREXPENDITURE"
(
    "id"          VARCHAR(50)                                                 NOT NULL,
    "operatorId"  VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "operator"    VARCHAR(50)                                                 NOT NULL,
    "operateDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "deptId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "paytype"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "category"    INT          DEFAULT 1                                      NOT NULL,
    "amount"      DECIMAL(18, 2)                                              NOT NULL,
    "explain"     VARCHAR(1000),
    "projectId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "projectCode" VARCHAR(200),
    "projectName" VARCHAR(1000),
    "entId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "entName"     VARCHAR(1000),
    "status"      VARCHAR(50),
    "orgId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT           CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_OTHEREXPENDITURE"."amount" IS '金额';
COMMENT
ON COLUMN "TB_LIM_OTHEREXPENDITURE"."category" IS '支出种类（枚举EnumOtherPayCategory：1支出，2收入）';
COMMENT
ON COLUMN "TB_LIM_OTHEREXPENDITURE"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_OTHEREXPENDITURE"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_OTHEREXPENDITURE"."deptId" IS '所属部门（Guid）';
COMMENT
ON COLUMN "TB_LIM_OTHEREXPENDITURE"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_OTHEREXPENDITURE"."entId" IS '企业id';
COMMENT
ON COLUMN "TB_LIM_OTHEREXPENDITURE"."entName" IS '企业名称';
COMMENT
ON COLUMN "TB_LIM_OTHEREXPENDITURE"."explain" IS '说明';
COMMENT
ON COLUMN "TB_LIM_OTHEREXPENDITURE"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_OTHEREXPENDITURE"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_OTHEREXPENDITURE"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_OTHEREXPENDITURE"."operateDate" IS '操作 日期';
COMMENT
ON COLUMN "TB_LIM_OTHEREXPENDITURE"."operator" IS '操作人';
COMMENT
ON COLUMN "TB_LIM_OTHEREXPENDITURE"."operatorId" IS '操作人id';
COMMENT
ON COLUMN "TB_LIM_OTHEREXPENDITURE"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_OTHEREXPENDITURE"."paytype" IS '支出类型（常量编码：LIM_ExpenditureType）';
COMMENT
ON COLUMN "TB_LIM_OTHEREXPENDITURE"."projectCode" IS '项目编号';
COMMENT
ON COLUMN "TB_LIM_OTHEREXPENDITURE"."projectId" IS '项目id';
COMMENT
ON COLUMN "TB_LIM_OTHEREXPENDITURE"."projectName" IS '项目名称';
COMMENT
ON COLUMN "TB_LIM_OTHEREXPENDITURE"."status" IS '状态';


CREATE TABLE "TB_LIM_PARAMS"
(
    "id"           VARCHAR(50)                                                 NOT NULL,
    "paramCode"    VARCHAR(50),
    "paramName"    VARCHAR(50),
    "remark"       VARCHAR(255),
    "variableName" VARCHAR(50),
    "regex"        VARCHAR(100),
    "dimension"    VARCHAR(50),
    "dimensionId"  VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "isDeleted"    BIT          DEFAULT 0                                      NOT NULL,
    "orgId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT            CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_PARAMS" IS '参数（样品属性参数、公式的参数、仪器的数据参数）';
COMMENT
ON COLUMN "TB_LIM_PARAMS"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_PARAMS"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_PARAMS"."dimension" IS '计量单位';
COMMENT
ON COLUMN "TB_LIM_PARAMS"."dimensionId" IS '计量单位Id（Guid）';
COMMENT
ON COLUMN "TB_LIM_PARAMS"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_PARAMS"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_PARAMS"."isDeleted" IS '是否删除';
COMMENT
ON COLUMN "TB_LIM_PARAMS"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_PARAMS"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_PARAMS"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_PARAMS"."paramCode" IS '唯一编号';
COMMENT
ON COLUMN "TB_LIM_PARAMS"."paramName" IS '参数名称';
COMMENT
ON COLUMN "TB_LIM_PARAMS"."regex" IS '正则表达式验证';
COMMENT
ON COLUMN "TB_LIM_PARAMS"."remark" IS '备注';
COMMENT
ON COLUMN "TB_LIM_PARAMS"."variableName" IS '变量名称';


CREATE TABLE "TB_LIM_PARAMS2PARAMSFORMULA"
(
    "id"             VARCHAR(50)                                                 NOT NULL,
    "recordId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "paramsConfigId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "objectId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "formula"        VARCHAR(1000),
    "isEnabled"      BIT          DEFAULT 0                                      NOT NULL,
    "isDeleted"      BIT          DEFAULT 0                                      NOT NULL,
    "orgId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_PARAMS2PARAMSFORMULA"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_PARAMS2PARAMSFORMULA"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_PARAMS2PARAMSFORMULA"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_PARAMS2PARAMSFORMULA"."formula" IS '公式';
COMMENT
ON COLUMN "TB_LIM_PARAMS2PARAMSFORMULA"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_PARAMS2PARAMSFORMULA"."isDeleted" IS 'isDeleted';
COMMENT
ON COLUMN "TB_LIM_PARAMS2PARAMSFORMULA"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_PARAMS2PARAMSFORMULA"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_PARAMS2PARAMSFORMULA"."objectId" IS '对象id（测试公式id-记录单参数，测试项目id-工作单参数）';
COMMENT
ON COLUMN "TB_LIM_PARAMS2PARAMSFORMULA"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_PARAMS2PARAMSFORMULA"."paramsConfigId" IS '参数配置Id';
COMMENT
ON COLUMN "TB_LIM_PARAMS2PARAMSFORMULA"."recordId" IS '记录单Id';


CREATE INDEX "paramsConfig" ON "TB_LIM_PARAMS2PARAMSFORMULA" ("recordId" ASC, "objectId" ASC, "isDeleted" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "TB_LIM_PARAMSCONFIG"
(
    "id"               VARCHAR(50)                                                  NOT NULL,
    "objId"            VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "paramsId"         VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "alias"            VARCHAR(50)                                                  NOT NULL,
    "defaultValue"     VARCHAR(100),
    "dimension"        VARCHAR(50),
    "dimensionId"      VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "orderNum"         INT           DEFAULT 0                                      NOT NULL,
    "type"             INT           DEFAULT (-1)                                   NOT NULL,
    "defaultControl"   INT           DEFAULT 1                                      NOT NULL,
    "dataSource"       VARCHAR(2000),
    "isDeleted"        BIT           DEFAULT 0                                      NOT NULL,
    "isRequired"       BIT           DEFAULT 0                                      NOT NULL,
    "mostSignificance" INT           DEFAULT (-1)                                   NOT NULL,
    "mostDecimal"      INT           DEFAULT (-1)                                   NOT NULL,
    "analyzeItemId"    VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "parentId"         VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "isShow"           BIT           DEFAULT 0                                      NOT NULL,
    "isFormula"        BIT           DEFAULT 0                                      NOT NULL,
    "formulaId"        VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "isAllConfig"      BIT           DEFAULT 0                                      NOT NULL,
    "orgId"            VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"          VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"       TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"         VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"         VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"       TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "paramsType"       INT           DEFAULT (-1)                                   NOT NULL,
    "referenceText"    VARCHAR(1000) DEFAULT '',
    NOT                CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_PARAMSCONFIG" IS '参数配置表（样品类型，企业，测试）';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."alias" IS '参数使用名称';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."analyzeItemId" IS '分析项目Id（用于检测类型相关分析项目）';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."dataSource" IS '数据源';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."defaultControl" IS '默认控件（枚举EnumDefaultControl:1.文本控件 2.日期控件 3.数字控件 4.下拉框控件 5.RadioGroup控件 6.CheckBoxGroup控件 7.日期时间控件 8.文本区域控件 9.时间控件）';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."defaultValue" IS '默认值';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."dimension" IS '计量单位';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."dimensionId" IS '计量单位id';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."formulaId" IS '公式Id';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."isAllConfig" IS '是否所有参数配置完成（用于记录单配置参数颜色区分）';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."isDeleted" IS 'isDeleted';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."isFormula" IS '是否有公式，有配置公式更新这个字段';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."isRequired" IS '是否必填（用于测试公式，检测类型参数）';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."isShow" IS '页面上是否显示，默认显示';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."mostDecimal" IS '小数位数';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."mostSignificance" IS '有效位数';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."objId" IS '对象id';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."orderNum" IS '排序值';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."paramsId" IS '参数id';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."paramsType" IS '参数类型（枚举EnumParamsType：1.公共参数、2.样品参数、3.分析项目参数、4.点位参数）';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."parentId" IS '父节点Id（用于检测类型相关分析项目）';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."referenceText" IS '参考文本';
COMMENT
ON COLUMN "TB_LIM_PARAMSCONFIG"."type" IS '对象类型（枚举EnumParamsConfigType：1.检测（样品）类型，2.测试项目，3.企业（预留）,4.方法（预留）,5.采样单（预留）6.原始记录单7.报告（预留）8.样品-采样单参数也是检测（样品）类型上的参数，只是进行设置用到采样单分组（检测（样品）类型公共参数）9.原始记录单-工作单参数（原始记录单上面部分的计算，如化学需氧量、BOD5）';


CREATE INDEX "INDEX2597106912695344" ON "TB_LIM_PARAMSCONFIG" ("objId" ASC, "type" ASC, "isDeleted" ASC, "orgId" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "TB_LIM_PARAMSFORMULA"
(
    "id"                VARCHAR(50)                                                 NOT NULL,
    "objectId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "formula"           VARCHAR(1000),
    "configDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "orignFormulatType" INT          DEFAULT 0                                      NOT NULL,
    "isDeleted"         BIT          DEFAULT 0                                      NOT NULL,
    "objectType"        INT          DEFAULT (-1)                                   NOT NULL,
    "sampleTypeId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "orgId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "validate"          INT          DEFAULT 0,
    "usageNum"          INT,
    "orignFormula"      VARCHAR(5000),
    NOT                 CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_PARAMSFORMULA"."configDate" IS '配置日期';
COMMENT
ON COLUMN "TB_LIM_PARAMSFORMULA"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_PARAMSFORMULA"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_PARAMSFORMULA"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_PARAMSFORMULA"."formula" IS '公式';
COMMENT
ON COLUMN "TB_LIM_PARAMSFORMULA"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_PARAMSFORMULA"."isDeleted" IS '假删';
COMMENT
ON COLUMN "TB_LIM_PARAMSFORMULA"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_PARAMSFORMULA"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_PARAMSFORMULA"."objectId" IS '参数Id（测试项目、检测类型参数）';
COMMENT
ON COLUMN "TB_LIM_PARAMSFORMULA"."objectType" IS '类型（枚举EnumParamsFormulaObjectType：0.测试公式 1.检测类型参数公式）';
COMMENT
ON COLUMN "TB_LIM_PARAMSFORMULA"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_PARAMSFORMULA"."orignFormula" IS '原始公式';
COMMENT
ON COLUMN "TB_LIM_PARAMSFORMULA"."orignFormulatType" IS '原始公式类型（枚举EnumOrignFormulatType：0:手写html,1:图片）';
COMMENT
ON COLUMN "TB_LIM_PARAMSFORMULA"."sampleTypeId" IS '检测类型id（仅用于测试项目）';
COMMENT
ON COLUMN "TB_LIM_PARAMSFORMULA"."usageNum" IS '使用次数';
COMMENT
ON COLUMN "TB_LIM_PARAMSFORMULA"."validate" IS '验证状态 0未验证 1已验证';


CREATE TABLE "TB_LIM_PARAMSPARTFORMULA"
(
    "id"               VARCHAR(50)                                                NOT NULL,
    "formulaId"        VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "formula"          VARCHAR(1000)                                              NOT NULL,
    "paramsName"       VARCHAR(100),
    "mostSignificance" INT         DEFAULT (-1)                                   NOT NULL,
    "mostDecimal"      INT         DEFAULT (-1)                                   NOT NULL,
    "orderNum"         INT         DEFAULT 0                                      NOT NULL,
    "formulaType"      INT         DEFAULT (-1)                                   NOT NULL,
    "orgId"            VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "detectionLimit"   VARCHAR(50),
    "calculationMode"  INT,
    "useTestLimit"     BIT         DEFAULT 0                                      NOT NULL,
    NOT                CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_PARAMSPARTFORMULA"."calculationMode" IS '计算方式（枚举EnumCalculationMode：0.原始值, 1.检出限一半，2.取零）';
COMMENT
ON COLUMN "TB_LIM_PARAMSPARTFORMULA"."detectionLimit" IS '测试项目部分公式检出限';
COMMENT
ON COLUMN "TB_LIM_PARAMSPARTFORMULA"."formula" IS '部分公式';
COMMENT
ON COLUMN "TB_LIM_PARAMSPARTFORMULA"."formulaId" IS '公式Id';
COMMENT
ON COLUMN "TB_LIM_PARAMSPARTFORMULA"."formulaType" IS '类型（EnumPartFormulaType：0.修约公式（用于测试公式及原始记录单参数公式中的修约） 1.检测类型参数公式 2.加标公式 3.BOD5判断公式 4.参数公式（如减空白后吸光度=吸光度-空白） 5.串联出证公式）';
COMMENT
ON COLUMN "TB_LIM_PARAMSPARTFORMULA"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_PARAMSPARTFORMULA"."mostDecimal" IS '小数位数';
COMMENT
ON COLUMN "TB_LIM_PARAMSPARTFORMULA"."mostSignificance" IS '有效位数';
COMMENT
ON COLUMN "TB_LIM_PARAMSPARTFORMULA"."orderNum" IS '排序值';
COMMENT
ON COLUMN "TB_LIM_PARAMSPARTFORMULA"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_PARAMSPARTFORMULA"."useTestLimit" IS '是否使用测试项目检出限';


CREATE TABLE "TB_LIM_PARAMSTESTFORMULA"
(
    "id"              VARCHAR(50)                                                NOT NULL,
    "objId"           VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "paramsId"        VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "paramsName"      VARCHAR(50),
    "alias"           VARCHAR(50),
    "defaultValue"    VARCHAR(50),
    "orderNum"        INT         DEFAULT 0                                      NOT NULL,
    "aliasInReport"   VARCHAR(50),
    "dimensionId"     VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "dimension"       VARCHAR(50),
    "sourceType"      INT         DEFAULT 0                                      NOT NULL,
    "isMust"          BIT         DEFAULT 0                                      NOT NULL,
    "isEditable"      BIT         DEFAULT 0                                      NOT NULL,
    "orgId"           VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "detectionLimit"  VARCHAR(50) DEFAULT '',
    "calculationMode" INT,
    NOT               CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_PARAMSTESTFORMULA" IS '测试公式与参数的关联表';
COMMENT
ON COLUMN "TB_LIM_PARAMSTESTFORMULA"."alias" IS '参数别名';
COMMENT
ON COLUMN "TB_LIM_PARAMSTESTFORMULA"."aliasInReport" IS '工作单模板中的变量名';
COMMENT
ON COLUMN "TB_LIM_PARAMSTESTFORMULA"."calculationMode" IS '计算方式（枚举EnumCalculationMode：0.原始值, 1.检出限一半，2.取零）';
COMMENT
ON COLUMN "TB_LIM_PARAMSTESTFORMULA"."defaultValue" IS '默认值';
COMMENT
ON COLUMN "TB_LIM_PARAMSTESTFORMULA"."detectionLimit" IS '参数检出限';
COMMENT
ON COLUMN "TB_LIM_PARAMSTESTFORMULA"."dimension" IS '计量单位';
COMMENT
ON COLUMN "TB_LIM_PARAMSTESTFORMULA"."dimensionId" IS '计量单位id';
COMMENT
ON COLUMN "TB_LIM_PARAMSTESTFORMULA"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_PARAMSTESTFORMULA"."isEditable" IS '是否允许修改';
COMMENT
ON COLUMN "TB_LIM_PARAMSTESTFORMULA"."isMust" IS '是否必填';
COMMENT
ON COLUMN "TB_LIM_PARAMSTESTFORMULA"."objId" IS '对象id（如测试公式id）';
COMMENT
ON COLUMN "TB_LIM_PARAMSTESTFORMULA"."orderNum" IS '排序值';
COMMENT
ON COLUMN "TB_LIM_PARAMSTESTFORMULA"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_PARAMSTESTFORMULA"."paramsId" IS '参数id';
COMMENT
ON COLUMN "TB_LIM_PARAMSTESTFORMULA"."paramsName" IS '参数名称';
COMMENT
ON COLUMN "TB_LIM_PARAMSTESTFORMULA"."sourceType" IS '类型（枚举EnumSourceType：0.无,1.样品，2.测试，3.企业，4.原始记录单）';


CREATE TABLE "TB_LIM_PERSON"
(
    "id"                 VARCHAR(50)                                                 NOT NULL,
    "cName"              VARCHAR(50)                                                 NOT NULL,
    "pinYin"             VARCHAR(100),
    "fullPinYin"         VARCHAR(100),
    "userNo"             VARCHAR(20),
    "deptId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "postId"             VARCHAR(50),
    "technicalTitleId"   VARCHAR(50),
    "technicalTitleDate" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "eName"              VARCHAR(50),
    "birthDay"           TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "email"              VARCHAR(100),
    "sex"                INT          DEFAULT 1                                      NOT NULL,
    "status"             INT          DEFAULT 1                                      NOT NULL,
    "idCard"             VARCHAR(20),
    "politicalFace"      VARCHAR(20),
    "volk"               VARCHAR(20),
    "nativePlace"        VARCHAR(100),
    "archivesPlace"      VARCHAR(100),
    "homeTown"           VARCHAR(100),
    "nation"             VARCHAR(20),
    "signature"          VARCHAR(200),
    "codeSigning"        VARCHAR(20),
    "photoUrl"           VARCHAR(500),
    "orderNum"           INT          DEFAULT 0                                      NOT NULL,
    "degree"             VARCHAR(50),
    "school"             VARCHAR(50),
    "specialty"          VARCHAR(50),
    "mobile"             VARCHAR(50),
    "homeTel"            VARCHAR(50),
    "birthPlace"         VARCHAR(100),
    "homeAddress"        VARCHAR(100),
    "emergentLinkMan"    VARCHAR(50),
    "contactMethod"      VARCHAR(50),
    "certificateNO"      VARCHAR(100),
    "certificateDate"    TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "yearsInThePosition" DECIMAL(18, 0)                                              NOT NULL,
    "workStartTime"      TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "joinCompanyTime"    TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "beginWorkTime"      TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "leaveCompanyTime"   TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "joinPartyDate"      TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "isDeleted"          BIT          DEFAULT 0                                      NOT NULL,
    "keyPost"            INT          DEFAULT 0                                      NOT NULL,
    "tecCompetence"      VARCHAR(255),
    "testResWork"        VARCHAR(255),
    "testResEvaluation"  VARCHAR(255),
    "submissionDuty"     VARCHAR(255),
    "developMethodRes"   VARCHAR(255),
    "experienceRequired" VARCHAR(255),
    "trainingPrograms"   VARCHAR(255),
    "manageRes"          VARCHAR(255),
    "remark"             VARCHAR(255),
    "externalId"         VARCHAR(100) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "orgId"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT                  CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_PERSON"."archivesPlace" IS '档案所在地';
COMMENT
ON COLUMN "TB_LIM_PERSON"."beginWorkTime" IS '开始工作时间';
COMMENT
ON COLUMN "TB_LIM_PERSON"."birthDay" IS '出生日期';
COMMENT
ON COLUMN "TB_LIM_PERSON"."birthPlace" IS '出生地';
COMMENT
ON COLUMN "TB_LIM_PERSON"."certificateDate" IS '准入证书获取时间';
COMMENT
ON COLUMN "TB_LIM_PERSON"."certificateNO" IS '准入证书';
COMMENT
ON COLUMN "TB_LIM_PERSON"."cName" IS '姓名';
COMMENT
ON COLUMN "TB_LIM_PERSON"."codeSigning" IS '签名密码';
COMMENT
ON COLUMN "TB_LIM_PERSON"."contactMethod" IS '联络方法';
COMMENT
ON COLUMN "TB_LIM_PERSON"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_PERSON"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_PERSON"."degree" IS '学历（常量（Guid）：LIM_Degree：大专、本科、硕士研究生、博士研究生）';
COMMENT
ON COLUMN "TB_LIM_PERSON"."deptId" IS '所属科室（Guid）';
COMMENT
ON COLUMN "TB_LIM_PERSON"."developMethodRes" IS '方法改进新方法制定和确认方面的职责';
COMMENT
ON COLUMN "TB_LIM_PERSON"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_PERSON"."emergentLinkMan" IS '紧急联络人';
COMMENT
ON COLUMN "TB_LIM_PERSON"."eName" IS '英文名';
COMMENT
ON COLUMN "TB_LIM_PERSON"."experienceRequired" IS '所需的专业知识和经验';
COMMENT
ON COLUMN "TB_LIM_PERSON"."externalId" IS '关联系统人员编号';
COMMENT
ON COLUMN "TB_LIM_PERSON"."fullPinYin" IS '全拼';
COMMENT
ON COLUMN "TB_LIM_PERSON"."homeAddress" IS '住址';
COMMENT
ON COLUMN "TB_LIM_PERSON"."homeTel" IS '固定电话';
COMMENT
ON COLUMN "TB_LIM_PERSON"."homeTown" IS '户籍所在地';
COMMENT
ON COLUMN "TB_LIM_PERSON"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_PERSON"."idCard" IS '身份证';
COMMENT
ON COLUMN "TB_LIM_PERSON"."isDeleted" IS '假删';
COMMENT
ON COLUMN "TB_LIM_PERSON"."joinCompanyTime" IS '入职时间';
COMMENT
ON COLUMN "TB_LIM_PERSON"."joinPartyDate" IS '入党日期';
COMMENT
ON COLUMN "TB_LIM_PERSON"."keyPost" IS '关键岗位';
COMMENT
ON COLUMN "TB_LIM_PERSON"."leaveCompanyTime" IS '离职时间';
COMMENT
ON COLUMN "TB_LIM_PERSON"."manageRes" IS '管理职责';
COMMENT
ON COLUMN "TB_LIM_PERSON"."mobile" IS '手机';
COMMENT
ON COLUMN "TB_LIM_PERSON"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_PERSON"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_PERSON"."nation" IS '国籍';
COMMENT
ON COLUMN "TB_LIM_PERSON"."nativePlace" IS '籍贯';
COMMENT
ON COLUMN "TB_LIM_PERSON"."orderNum" IS '排序值';
COMMENT
ON COLUMN "TB_LIM_PERSON"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_PERSON"."photoUrl" IS '人员头像图片路径';
COMMENT
ON COLUMN "TB_LIM_PERSON"."pinYin" IS '拼音';
COMMENT
ON COLUMN "TB_LIM_PERSON"."politicalFace" IS '政治面貌';
COMMENT
ON COLUMN "TB_LIM_PERSON"."postId" IS '职务（常量（Guid）：LIM_Post）';
COMMENT
ON COLUMN "TB_LIM_PERSON"."remark" IS '备注说明';
COMMENT
ON COLUMN "TB_LIM_PERSON"."school" IS '毕业院校';
COMMENT
ON COLUMN "TB_LIM_PERSON"."sex" IS '性别（枚举EnumSex：  1代表男   2代表女）';
COMMENT
ON COLUMN "TB_LIM_PERSON"."signature" IS '人员签名图片路径';
COMMENT
ON COLUMN "TB_LIM_PERSON"."specialty" IS '专业';
COMMENT
ON COLUMN "TB_LIM_PERSON"."status" IS '状态（枚举EnumPersonStatus:1代表在职 2代表离职 3代表休假）';
COMMENT
ON COLUMN "TB_LIM_PERSON"."submissionDuty" IS '提交意见和解释的职责';
COMMENT
ON COLUMN "TB_LIM_PERSON"."tecCompetence" IS '技术能力';
COMMENT
ON COLUMN "TB_LIM_PERSON"."technicalTitleDate" IS '职称获得日期';
COMMENT
ON COLUMN "TB_LIM_PERSON"."technicalTitleId" IS '职称（常量（Guid）：LIM_TechnicalTitle）';
COMMENT
ON COLUMN "TB_LIM_PERSON"."testResEvaluation" IS '检测和/或校准策划和结果评价方面的职责';
COMMENT
ON COLUMN "TB_LIM_PERSON"."testResWork" IS '从事检测和/或校准工作方面的职责';
COMMENT
ON COLUMN "TB_LIM_PERSON"."trainingPrograms" IS '资格和培训计划';
COMMENT
ON COLUMN "TB_LIM_PERSON"."userNo" IS '编号';
COMMENT
ON COLUMN "TB_LIM_PERSON"."volk" IS '民族';
COMMENT
ON COLUMN "TB_LIM_PERSON"."workStartTime" IS '入职年限（参加工作时间）';
COMMENT
ON COLUMN "TB_LIM_PERSON"."yearsInThePosition" IS '在岗时间';


CREATE TABLE "TB_LIM_PERSON2TEST"
(
    "id"                   VARCHAR(50)                                                NOT NULL,
    "personId"             VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "testId"               VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "orderNum"             INT         DEFAULT 0                                      NOT NULL,
    "sampleTypeId"         VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "isDefaultPerson"      BIT         DEFAULT 0                                      NOT NULL,
    "isDefaultAuditPerson" BIT         DEFAULT 0                                      NOT NULL,
    "orgId"                VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    NOT                    CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_PERSON2TEST"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_PERSON2TEST"."isDefaultAuditPerson" IS '是否默认复核人员';
COMMENT
ON COLUMN "TB_LIM_PERSON2TEST"."isDefaultPerson" IS '是否默认人员';
COMMENT
ON COLUMN "TB_LIM_PERSON2TEST"."orderNum" IS '排序值';
COMMENT
ON COLUMN "TB_LIM_PERSON2TEST"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_PERSON2TEST"."personId" IS '人员Id';
COMMENT
ON COLUMN "TB_LIM_PERSON2TEST"."sampleTypeId" IS '样品类型id';
COMMENT
ON COLUMN "TB_LIM_PERSON2TEST"."testId" IS '测试项目id';


CREATE TABLE "TB_LIM_PERSONABILITY"
(
    "id"                VARCHAR(50)                                                 NOT NULL,
    "personId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "testId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "personCertId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "achieveDate"       TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "certEffectiveTime" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "orgId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    NOT                 CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_PERSONABILITY"."achieveDate" IS '证书获得日期';
COMMENT
ON COLUMN "TB_LIM_PERSONABILITY"."certEffectiveTime" IS '有效期至';
COMMENT
ON COLUMN "TB_LIM_PERSONABILITY"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_PERSONABILITY"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_PERSONABILITY"."personCertId" IS '证书Id';
COMMENT
ON COLUMN "TB_LIM_PERSONABILITY"."personId" IS '人员Id';
COMMENT
ON COLUMN "TB_LIM_PERSONABILITY"."testId" IS '测试项目Id';


CREATE TABLE "TB_LIM_PERSONCERT"
(
    "id"                VARCHAR(50)                                                 NOT NULL,
    "personId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "certName"          VARCHAR(100)                                                NOT NULL,
    "certCode"          VARCHAR(50)                                                 NOT NULL,
    "issueCertTime"     TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "certEffectiveTime" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "certType"          VARCHAR(100) DEFAULT '-1'                                   NOT NULL,
    "phoneUrl"          VARCHAR(500),
    "issuingAuthority"  VARCHAR(100),
    "remark"            VARCHAR(1000),
    "orgId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "personName"        VARCHAR(255),
    NOT                 CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_PERSONCERT"."certCode" IS '证书编号';
COMMENT
ON COLUMN "TB_LIM_PERSONCERT"."certEffectiveTime" IS '有效期至';
COMMENT
ON COLUMN "TB_LIM_PERSONCERT"."certName" IS '证书名称';
COMMENT
ON COLUMN "TB_LIM_PERSONCERT"."certType" IS '证书类型（枚举EnumCertType：1.水 2.气 3.声 4.土 5.其他    多选用","隔开）';
COMMENT
ON COLUMN "TB_LIM_PERSONCERT"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_PERSONCERT"."issueCertTime" IS '发证日期';
COMMENT
ON COLUMN "TB_LIM_PERSONCERT"."issuingAuthority" IS '发证机关（预留，3.2）';
COMMENT
ON COLUMN "TB_LIM_PERSONCERT"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_PERSONCERT"."personId" IS '人员Id（Guid）';
COMMENT
ON COLUMN "TB_LIM_PERSONCERT"."phoneUrl" IS '图片存放位置';
COMMENT
ON COLUMN "TB_LIM_PERSONCERT"."remark" IS '备注（预留，3.2）';


CREATE TABLE "TB_LIM_PERSONFACEMSG"
(
    "id"          VARCHAR(50)                                                 NOT NULL,
    "personId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000',
    "facePicture" CLOB,
    "isDeleted"   BIT          DEFAULT 0                                      NOT NULL,
    "orgId"       VARCHAR(50)                                                 NOT NULL,
    "creator"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT           CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_PERSONFACEMSG"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_PERSONFACEMSG"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_PERSONFACEMSG"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_PERSONFACEMSG"."isDeleted" IS '假删';
COMMENT
ON COLUMN "TB_LIM_PERSONFACEMSG"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_PERSONFACEMSG"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_PERSONFACEMSG"."orgId" IS '组织机构id';


CREATE TABLE "TB_LIM_PROJECTINSTRUMENT"
(
    "id"                VARCHAR(50)                                                  NOT NULL,
    "projectId"         VARCHAR(500),
    "useDate"           TIMESTAMP(0)  DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "administratorId"   VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "administratorName" VARCHAR(50),
    "userIds"           VARCHAR(2000) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "userNames"         VARCHAR(2000),
    "outQualified"      INT           DEFAULT (-1)                                   NOT NULL,
    "intQualified"      INT           DEFAULT (-1)                                   NOT NULL,
    "outRemarks"        VARCHAR(1000),
    "inRemarks"         VARCHAR(1000),
    "isDeleted"         BIT           DEFAULT 0                                      NOT NULL,
    "orgId"             VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"           VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"        TIMESTAMP(0)  DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "domainId"          VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"          VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"        TIMESTAMP(0)  DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "projectName"       VARCHAR(1000),
    NOT                 CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_PROJECTINSTRUMENT" IS '项目仪器表';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENT"."administratorId" IS '管理人员Id';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENT"."administratorName" IS '管理人员名称';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENT"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENT"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENT"."domainId" IS '所属实验室id';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENT"."id" IS '主键';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENT"."inRemarks" IS '入库备注';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENT"."intQualified" IS '入库合格情况，0 - 不合格，1 - 合格';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENT"."isDeleted" IS '是否删除';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENT"."modifier" IS '最近修改人';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENT"."modifyDate" IS '最新修改时间';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENT"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENT"."outQualified" IS '出库合格情况，0 - 不合格，1 - 合格';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENT"."outRemarks" IS '出库备注';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENT"."useDate" IS '使用日期';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENT"."userIds" IS '使用人员id，多个用英文逗号间隔';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENT"."userNames" IS '使用人员名称，多个用英文逗号间隔';


CREATE TABLE "TB_LIM_PROJECTINSTRUMENTDETAILS"
(
    "id"                  VARCHAR(50)                                                 NOT NULL,
    "projectInstrumentId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "instrumentId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "inDate"              TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "outDate"             TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "inQualified"         INT          DEFAULT (-1)                                   NOT NULL,
    "outQualified"        INT          DEFAULT (-1)                                   NOT NULL,
    "isStorage"           BIT          DEFAULT 0                                      NOT NULL,
    "inPerson"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "outPerson"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "isConfirm"           BIT          DEFAULT 0                                      NOT NULL,
    "orgId"               VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"          TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"          TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT                   CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_PROJECTINSTRUMENTDETAILS" IS '项目仪器使用详细记录';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENTDETAILS"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENTDETAILS"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENTDETAILS"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENTDETAILS"."id" IS '主键';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENTDETAILS"."inDate" IS '入库日期';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENTDETAILS"."inPerson" IS '入库人';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENTDETAILS"."inQualified" IS '入库合格情况，0 - 不合格，1 - 合格';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENTDETAILS"."instrumentId" IS '仪器id';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENTDETAILS"."isConfirm" IS '是否出库确认';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENTDETAILS"."isStorage" IS '是否已入库 1：是 0：否';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENTDETAILS"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENTDETAILS"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENTDETAILS"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENTDETAILS"."outDate" IS '出库日期';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENTDETAILS"."outPerson" IS '出库人';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENTDETAILS"."outQualified" IS '出库合格情况，0 - 不合格，1 - 合格';
COMMENT
ON COLUMN "TB_LIM_PROJECTINSTRUMENTDETAILS"."projectInstrumentId" IS '项目仪器表id';


CREATE TABLE "TB_LIM_PROJECTTYPE"
(
    "id"         VARCHAR(50)                                                 NOT NULL,
    "parentId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "mark"       VARCHAR(255),
    "orderNum"   INT                                                         NOT NULL,
    "remark"     TEXT,
    "workflowId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "isDeleted"  TINYINT      DEFAULT 0                                      NOT NULL,
    "config"     CLOB,
    "orgId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "name"       VARCHAR(255),
    "alias"      VARCHAR(255),
    NOT          CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_PROJECTTYPE"."alias" IS '项目类型别名';
COMMENT
ON COLUMN "TB_LIM_PROJECTTYPE"."config" IS '配置信息';
COMMENT
ON COLUMN "TB_LIM_PROJECTTYPE"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_PROJECTTYPE"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_PROJECTTYPE"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_PROJECTTYPE"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_PROJECTTYPE"."isDeleted" IS '假删';
COMMENT
ON COLUMN "TB_LIM_PROJECTTYPE"."mark" IS '标识';
COMMENT
ON COLUMN "TB_LIM_PROJECTTYPE"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_PROJECTTYPE"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_PROJECTTYPE"."name" IS '项目类型名称';
COMMENT
ON COLUMN "TB_LIM_PROJECTTYPE"."orderNum" IS '排序值';
COMMENT
ON COLUMN "TB_LIM_PROJECTTYPE"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_PROJECTTYPE"."parentId" IS '父节点id';
COMMENT
ON COLUMN "TB_LIM_PROJECTTYPE"."remark" IS '备注';
COMMENT
ON COLUMN "TB_LIM_PROJECTTYPE"."workflowId" IS '工作流id';


CREATE TABLE "TB_LIM_PUBLISHSYSTEMVERSION"
(
    "id"             VARCHAR(50)                                                 NOT NULL,
    "title"          VARCHAR(200)                                                NOT NULL,
    "versionNum"     VARCHAR(100)                                                NOT NULL,
    "publishPerson"  VARCHAR(50)                                                 NOT NULL,
    "publishDate"    TIMESTAMP(0)                                                NOT NULL,
    "flaywayVersion" VARCHAR(50)                                                 NOT NULL,
    "isProduct"      BIT          DEFAULT 0                                      NOT NULL,
    "isPublish"      BIT          DEFAULT 0                                      NOT NULL,
    "isTemplate"     BIT          DEFAULT 0                                      NOT NULL,
    "isConfig"       BIT          DEFAULT 0                                      NOT NULL,
    "updateContent"  TEXT                                                        NOT NULL,
    "deployContent"  TEXT,
    "orgId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_PUBLISHSYSTEMVERSION" IS '版本发布管理表';
COMMENT
ON COLUMN "TB_LIM_PUBLISHSYSTEMVERSION"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_PUBLISHSYSTEMVERSION"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_PUBLISHSYSTEMVERSION"."deployContent" IS '部署注意事项';
COMMENT
ON COLUMN "TB_LIM_PUBLISHSYSTEMVERSION"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_PUBLISHSYSTEMVERSION"."flaywayVersion" IS 'flayway版本';
COMMENT
ON COLUMN "TB_LIM_PUBLISHSYSTEMVERSION"."id" IS '主键';
COMMENT
ON COLUMN "TB_LIM_PUBLISHSYSTEMVERSION"."isConfig" IS '是否产品 0否 1是';
COMMENT
ON COLUMN "TB_LIM_PUBLISHSYSTEMVERSION"."isProduct" IS '是否产品 0否 1是';
COMMENT
ON COLUMN "TB_LIM_PUBLISHSYSTEMVERSION"."isPublish" IS '是否产品 0否 1是';
COMMENT
ON COLUMN "TB_LIM_PUBLISHSYSTEMVERSION"."isTemplate" IS '是否产品 0否 1是';
COMMENT
ON COLUMN "TB_LIM_PUBLISHSYSTEMVERSION"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_PUBLISHSYSTEMVERSION"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_PUBLISHSYSTEMVERSION"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_PUBLISHSYSTEMVERSION"."publishDate" IS '发布日期';
COMMENT
ON COLUMN "TB_LIM_PUBLISHSYSTEMVERSION"."publishPerson" IS '发布人';
COMMENT
ON COLUMN "TB_LIM_PUBLISHSYSTEMVERSION"."title" IS '标题';
COMMENT
ON COLUMN "TB_LIM_PUBLISHSYSTEMVERSION"."updateContent" IS '更新内容';
COMMENT
ON COLUMN "TB_LIM_PUBLISHSYSTEMVERSION"."versionNum" IS '版本号';


CREATE TABLE "TB_LIM_RECANDPAYRECORD"
(
    "id"           VARCHAR(50)                                                  NOT NULL,
    "contractId"   VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "moneyType"    INT           DEFAULT (-1)                                   NOT NULL,
    "amount"       DECIMAL(18, 2)                                               NOT NULL,
    "operatorId"   VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "operatorName" VARCHAR(50),
    "operatorDate" TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "invoiceNum"   VARCHAR(50),
    "invoiceCode"  VARCHAR(50),
    "invoiceDate"  TIMESTAMP(0)  DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "isDeleted"    BIT           DEFAULT 0                                      NOT NULL,
    "orgId"        VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"      VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"   TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"     VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"     VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"   TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "remark"       VARCHAR(1000) DEFAULT '',
    "hasInvoice"   BIT           DEFAULT 0                                      NOT NULL,
    "isReceive"    BIT           DEFAULT 0                                      NOT NULL,
    "receiveDate"  TIMESTAMP(0)  DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "collectItem"  VARCHAR(255)  DEFAULT ''                                     NOT NULL,
    NOT            CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_RECANDPAYRECORD"."amount" IS '金额';
COMMENT
ON COLUMN "TB_LIM_RECANDPAYRECORD"."collectItem" IS '收款项';
COMMENT
ON COLUMN "TB_LIM_RECANDPAYRECORD"."contractId" IS '合同id';
COMMENT
ON COLUMN "TB_LIM_RECANDPAYRECORD"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_RECANDPAYRECORD"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_RECANDPAYRECORD"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_RECANDPAYRECORD"."hasInvoice" IS '是否开票';
COMMENT
ON COLUMN "TB_LIM_RECANDPAYRECORD"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_RECANDPAYRECORD"."invoiceCode" IS '发票代码';
COMMENT
ON COLUMN "TB_LIM_RECANDPAYRECORD"."invoiceDate" IS '开票日期';
COMMENT
ON COLUMN "TB_LIM_RECANDPAYRECORD"."invoiceNum" IS '发票号码';
COMMENT
ON COLUMN "TB_LIM_RECANDPAYRECORD"."isDeleted" IS '假删';
COMMENT
ON COLUMN "TB_LIM_RECANDPAYRECORD"."isReceive" IS '是否到款';
COMMENT
ON COLUMN "TB_LIM_RECANDPAYRECORD"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_RECANDPAYRECORD"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_RECANDPAYRECORD"."moneyType" IS '类型(枚举EnumMoneyType： 1.收款 2.付款 3.坏账)';
COMMENT
ON COLUMN "TB_LIM_RECANDPAYRECORD"."operatorDate" IS '操作日期';
COMMENT
ON COLUMN "TB_LIM_RECANDPAYRECORD"."operatorId" IS '操作人id';
COMMENT
ON COLUMN "TB_LIM_RECANDPAYRECORD"."operatorName" IS '操作人姓名';
COMMENT
ON COLUMN "TB_LIM_RECANDPAYRECORD"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_RECANDPAYRECORD"."receiveDate" IS '到款日期';
COMMENT
ON COLUMN "TB_LIM_RECANDPAYRECORD"."remark" IS '备注';


CREATE TABLE "TB_LIM_RECORDCONFIG"
(
    "id"             VARCHAR(50)                                                 NOT NULL,
    "recordName"     VARCHAR(100)                                                NOT NULL,
    "recordType"     INT          DEFAULT (-1)                                   NOT NULL,
    "reportConfigId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "sampleTypeId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "remark"         VARCHAR(255),
    "isDeleted"      BIT          DEFAULT 0                                      NOT NULL,
    "orgId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "sampleTypeIds"  VARCHAR(400),
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_RECORDCONFIG"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_RECORDCONFIG"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_RECORDCONFIG"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_RECORDCONFIG"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_RECORDCONFIG"."isDeleted" IS '假删';
COMMENT
ON COLUMN "TB_LIM_RECORDCONFIG"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_RECORDCONFIG"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_RECORDCONFIG"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_RECORDCONFIG"."recordName" IS '名称';
COMMENT
ON COLUMN "TB_LIM_RECORDCONFIG"."recordType" IS '记录单类型(枚举EnumRecordType：1:采样记录单,2:原始记录单,3:报告)';
COMMENT
ON COLUMN "TB_LIM_RECORDCONFIG"."remark" IS '备注';
COMMENT
ON COLUMN "TB_LIM_RECORDCONFIG"."reportConfigId" IS '报表模板Id';
COMMENT
ON COLUMN "TB_LIM_RECORDCONFIG"."sampleTypeId" IS '检测类型Id(小类)';
COMMENT
ON COLUMN "TB_LIM_RECORDCONFIG"."sampleTypeIds" IS '检测类型id列表，多个id用逗号隔开';


CREATE TABLE "TB_LIM_RECORDCONFIG2PARAMSCONFIG"
(
    "id"             VARCHAR(50)                                                NOT NULL,
    "recordConfigId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "paramsConfigId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_RECORDCONFIG2PARAMSCONFIG"."id" IS '主键id';
COMMENT
ON COLUMN "TB_LIM_RECORDCONFIG2PARAMSCONFIG"."paramsConfigId" IS '参数配置id';
COMMENT
ON COLUMN "TB_LIM_RECORDCONFIG2PARAMSCONFIG"."recordConfigId" IS '采样单配置id';


CREATE TABLE "TB_LIM_RECORDCONFIG2TEST"
(
    "id"             VARCHAR(50)                                                NOT NULL,
    "recordConfigId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "testId"         VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_RECORDCONFIG2TEST" IS '原始记录单与测试项目多对多关系';
COMMENT
ON COLUMN "TB_LIM_RECORDCONFIG2TEST"."id" IS '主键id';
COMMENT
ON COLUMN "TB_LIM_RECORDCONFIG2TEST"."recordConfigId" IS '记录单配置id';
COMMENT
ON COLUMN "TB_LIM_RECORDCONFIG2TEST"."testId" IS '测试项目id';


CREATE TABLE "TB_LIM_REPORTAPPLY"
(
    "id"             VARCHAR(50)                                                 NOT NULL,
    "reportConfigId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "module"         VARCHAR(50),
    "moduleName"     VARCHAR(50),
    "code"           VARCHAR(50),
    "name"           VARCHAR(100),
    "type"           INT          DEFAULT 0                                      NOT NULL,
    "isRedact"       INT          DEFAULT 0                                      NOT NULL,
    "isShow"         INT          DEFAULT 0                                      NOT NULL,
    "remark"         VARCHAR(500),
    "location"       VARCHAR(50),
    "orgId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "blankFill"      INT,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_REPORTAPPLY"."blankFill" IS '是否空白填充 1：是 0：否';
COMMENT
ON COLUMN "TB_LIM_REPORTAPPLY"."code" IS '控件编码';
COMMENT
ON COLUMN "TB_LIM_REPORTAPPLY"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_REPORTAPPLY"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_REPORTAPPLY"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_REPORTAPPLY"."id" IS '主键id';
COMMENT
ON COLUMN "TB_LIM_REPORTAPPLY"."isRedact" IS '是否可编辑';
COMMENT
ON COLUMN "TB_LIM_REPORTAPPLY"."isShow" IS '是否显示名称';
COMMENT
ON COLUMN "TB_LIM_REPORTAPPLY"."location" IS '地址';
COMMENT
ON COLUMN "TB_LIM_REPORTAPPLY"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_REPORTAPPLY"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_REPORTAPPLY"."module" IS '所属模块';
COMMENT
ON COLUMN "TB_LIM_REPORTAPPLY"."moduleName" IS '所属模块名称';
COMMENT
ON COLUMN "TB_LIM_REPORTAPPLY"."name" IS '显示名称';
COMMENT
ON COLUMN "TB_LIM_REPORTAPPLY"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_REPORTAPPLY"."remark" IS '备注';
COMMENT
ON COLUMN "TB_LIM_REPORTAPPLY"."reportConfigId" IS '配置id';
COMMENT
ON COLUMN "TB_LIM_REPORTAPPLY"."type" IS '控件类型';


CREATE TABLE "TB_LIM_REPORTCONFIG"
(
    "id"               VARCHAR(50)                                                 NOT NULL,
    "type"             INT          DEFAULT 1                                      NOT NULL,
    "reportCode"       VARCHAR(100),
    "templateName"     VARCHAR(100),
    "template"         VARCHAR(100),
    "outputName"       VARCHAR(100),
    "returnType"       VARCHAR(20),
    "method"           VARCHAR(255),
    "params"           VARCHAR(500),
    "pageConfig"       CLOB,
    "orderNum"         INT          DEFAULT 0                                      NOT NULL,
    "bizType"          INT          DEFAULT (-1)                                   NOT NULL,
    "remark"           VARCHAR(500),
    "isDeleted"        BIT          DEFAULT 0                                      NOT NULL,
    "orgId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "dataMethod"       VARCHAR(255),
    "typeCode"         VARCHAR(50),
    "strUrl"           VARCHAR(50),
    "isDefineFileName" BIT          DEFAULT 0                                      NOT NULL,
    "defineFileName"   VARCHAR(500),
    "beanName"         VARCHAR(500),
    "versionNum"       VARCHAR(100),
    "controlNum"       VARCHAR(100),
    "reportName"       VARCHAR(1000),
    "validate"         INT          DEFAULT 0,
    "usageNum"         INT,
    NOT                CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."beanName" IS '配置方法名称';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."bizType" IS '业务分类（枚举：EnumReportType（1.报表 2.原始记录单）';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."controlNum" IS '受控编号';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."dataMethod" IS '数据方法调用';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."defineFileName" IS '配置报表名称';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."isDefineFileName" IS '是否定义生成报表名称';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."isDeleted" IS '假删';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."method" IS '调用方法全名';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."orderNum" IS '排序值';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."outputName" IS '输出文件名';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."pageConfig" IS 'json格式的页面配置';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."params" IS '参数键值对设置';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."remark" IS '备注';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."reportCode" IS '报表编码';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."reportName" IS '报表名称（每个sheet页的名称用“;”分隔）';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."returnType" IS '返回文件类型（doc/docx/pdf……）';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."strUrl" IS '接口路径';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."template" IS '模版文件全名';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."templateName" IS '模板名称';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."type" IS '报表类型（枚举：EnumReportConfigType:1文件，2统计面板，3json数据源)';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."typeCode" IS '类型编码';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."usageNum" IS '使用次数';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."validate" IS '验证状态 0未验证 1已验证';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG"."versionNum" IS '版本号';


CREATE TABLE "TB_LIM_REPORTCONFIG2MODULE"
(
    "id"             VARCHAR(50)                                                NOT NULL,
    "reportConfigId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "reportModuleId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_REPORTCONFIG2MODULE" IS '报告组件配置表';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG2MODULE"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG2MODULE"."reportConfigId" IS '报表配置id';
COMMENT
ON COLUMN "TB_LIM_REPORTCONFIG2MODULE"."reportModuleId" IS '报告组件id（常量维护）';


CREATE TABLE "TB_LIM_REPORTMODULE"
(
    "id"                         VARCHAR(50)                                                 NOT NULL,
    "moduleCode"                 VARCHAR(100)                                                NOT NULL,
    "moduleName"                 VARCHAR(100)                                                NOT NULL,
    "tableName"                  VARCHAR(100)                                                NOT NULL,
    "sourceTableName"            VARCHAR(100),
    "sampleCount"                INT          DEFAULT 0                                      NOT NULL,
    "testCount"                  INT          DEFAULT 0                                      NOT NULL,
    "sonTableJson"               VARCHAR(500),
    "isCompound"                 BIT          DEFAULT 0                                      NOT NULL,
    "orgId"                      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"                    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"                 TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"                   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"                   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"                 TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "totalTest"                  VARCHAR(255),
    "auxiliaryInstrument"        VARCHAR(255),
    "conversionCalculationMode"  INT          DEFAULT 0                                      NOT NULL,
    "speedCalculationMode"       INT          DEFAULT 0                                      NOT NULL,
    "compoundAvgCalculationMode" INT          DEFAULT 0                                      NOT NULL,
    NOT                          CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_REPORTMODULE" IS '报告组件信息表';
COMMENT
ON COLUMN "TB_LIM_REPORTMODULE"."auxiliaryInstrument" IS '是否辅助仪器';
COMMENT
ON COLUMN "TB_LIM_REPORTMODULE"."compoundAvgCalculationMode" IS '化合物均值计算方式 枚举 EnumCompoundAvgCalculationMode：0.按样品, 1.按批次';
COMMENT
ON COLUMN "TB_LIM_REPORTMODULE"."conversionCalculationMode" IS '报告折算浓度计算方式（0.按样品, 1.按批次）';
COMMENT
ON COLUMN "TB_LIM_REPORTMODULE"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_REPORTMODULE"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_REPORTMODULE"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_REPORTMODULE"."id" IS '主键id';
COMMENT
ON COLUMN "TB_LIM_REPORTMODULE"."isCompound" IS '是否复合组件';
COMMENT
ON COLUMN "TB_LIM_REPORTMODULE"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_REPORTMODULE"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_REPORTMODULE"."moduleCode" IS '组件编码';
COMMENT
ON COLUMN "TB_LIM_REPORTMODULE"."moduleName" IS '组件名称';
COMMENT
ON COLUMN "TB_LIM_REPORTMODULE"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_REPORTMODULE"."sampleCount" IS '组件每页样品数量';
COMMENT
ON COLUMN "TB_LIM_REPORTMODULE"."sonTableJson" IS '子组件配置信息（适用于复合组件）';
COMMENT
ON COLUMN "TB_LIM_REPORTMODULE"."sourceTableName" IS '组件数据行表名称';
COMMENT
ON COLUMN "TB_LIM_REPORTMODULE"."speedCalculationMode" IS '报告排放速率计算方式（0.按样品, 1.按批次）';
COMMENT
ON COLUMN "TB_LIM_REPORTMODULE"."tableName" IS '组件主表名称';
COMMENT
ON COLUMN "TB_LIM_REPORTMODULE"."testCount" IS '组件每页测试项目数量';
COMMENT
ON COLUMN "TB_LIM_REPORTMODULE"."totalTest" IS '是否总称';


CREATE TABLE "TB_LIM_REPORTMODULE2GROUPTYPE"
(
    "id"                   VARCHAR(50)                                                NOT NULL,
    "reportConfigModuleId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "groupTypeName"        VARCHAR(255),
    "priority"             INT         DEFAULT (-1)                                   NOT NULL,
    NOT                    CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_REPORTMODULE2GROUPTYPE" IS '报告各个组件配置的分页方式表';
COMMENT
ON COLUMN "TB_LIM_REPORTMODULE2GROUPTYPE"."groupTypeName" IS '分页类型名称（包含数据源，属性名称，分页方式）';
COMMENT
ON COLUMN "TB_LIM_REPORTMODULE2GROUPTYPE"."id" IS '主键id';
COMMENT
ON COLUMN "TB_LIM_REPORTMODULE2GROUPTYPE"."priority" IS '优先级（最外层分页的优先级最高）';
COMMENT
ON COLUMN "TB_LIM_REPORTMODULE2GROUPTYPE"."reportConfigModuleId" IS '报告组件配置id';


CREATE TABLE "TB_LIM_SAMPLETYPE2TEST"
(
    "id"           VARCHAR(50)                                                NOT NULL,
    "sampleTypeId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "testId"       VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "parentId"     VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "timesOrder"   INT         DEFAULT 1                                      NOT NULL,
    "samplePeriod" INT         DEFAULT 1                                      NOT NULL,
    NOT            CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_SAMPLETYPE2TEST"."parentId" IS '测试项目总称id';
COMMENT
ON COLUMN "TB_LIM_SAMPLETYPE2TEST"."samplePeriod" IS '样品数';
COMMENT
ON COLUMN "TB_LIM_SAMPLETYPE2TEST"."sampleTypeId" IS '检测类型id';
COMMENT
ON COLUMN "TB_LIM_SAMPLETYPE2TEST"."testId" IS '测试项目id';
COMMENT
ON COLUMN "TB_LIM_SAMPLETYPE2TEST"."timesOrder" IS '批次';


CREATE TABLE "TB_LIM_SAMPLETYPEGROUP"
(
    "id"                 VARCHAR(50)                                                 NOT NULL,
    "parentId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "groupType"          INT          DEFAULT (-1)                                   NOT NULL,
    "groupName"          VARCHAR(100),
    "sampleTypeId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "fixer"              VARCHAR(1000),
    "containerName"      VARCHAR(255),
    "saveCondition"      VARCHAR(1000),
    "orderNum"           INT          DEFAULT 0                                      NOT NULL,
    "remark"             VARCHAR(1000),
    "volumeType"         VARCHAR(100),
    "orgId"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "pretreatmentMethod" VARCHAR(200) DEFAULT '',
    "sampleVolume"       VARCHAR(200) DEFAULT '',
    "containerStatus"    INT          DEFAULT 0                                      NOT NULL,
    NOT                  CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_SAMPLETYPEGROUP"."containerName" IS '容器名称';
COMMENT
ON COLUMN "TB_LIM_SAMPLETYPEGROUP"."containerStatus" IS '采样容器状态 EnumContainerStatus 1.完好无损 2.破损';
COMMENT
ON COLUMN "TB_LIM_SAMPLETYPEGROUP"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_SAMPLETYPEGROUP"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_SAMPLETYPEGROUP"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_SAMPLETYPEGROUP"."fixer" IS '固定剂';
COMMENT
ON COLUMN "TB_LIM_SAMPLETYPEGROUP"."groupName" IS '分组名称';
COMMENT
ON COLUMN "TB_LIM_SAMPLETYPEGROUP"."groupType" IS '分组类型（枚举EnumGroupType：1.分组规则，2.分组）';
COMMENT
ON COLUMN "TB_LIM_SAMPLETYPEGROUP"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_SAMPLETYPEGROUP"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_SAMPLETYPEGROUP"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_SAMPLETYPEGROUP"."orderNum" IS '排序值';
COMMENT
ON COLUMN "TB_LIM_SAMPLETYPEGROUP"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_SAMPLETYPEGROUP"."parentId" IS '父节点（Guid）';
COMMENT
ON COLUMN "TB_LIM_SAMPLETYPEGROUP"."pretreatmentMethod" IS '前处理方式';
COMMENT
ON COLUMN "TB_LIM_SAMPLETYPEGROUP"."remark" IS '备注';
COMMENT
ON COLUMN "TB_LIM_SAMPLETYPEGROUP"."sampleTypeId" IS '样品类型的id（Guid）';
COMMENT
ON COLUMN "TB_LIM_SAMPLETYPEGROUP"."sampleVolume" IS '采样体积';
COMMENT
ON COLUMN "TB_LIM_SAMPLETYPEGROUP"."saveCondition" IS '保存条件';
COMMENT
ON COLUMN "TB_LIM_SAMPLETYPEGROUP"."volumeType" IS '体积类型';


CREATE TABLE "TB_LIM_SAMPLETYPEGROUP2TEST"
(
    "sampleTypeGroupId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "testId"            VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "id"                VARCHAR(50)                                                NOT NULL,
    NOT                 CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_SAMPLETYPEGROUP2TEST" IS '测试项目分组信息和测试项目关系';
COMMENT
ON COLUMN "TB_LIM_SAMPLETYPEGROUP2TEST"."id" IS '主键';
COMMENT
ON COLUMN "TB_LIM_SAMPLETYPEGROUP2TEST"."sampleTypeGroupId" IS '分组的id';
COMMENT
ON COLUMN "TB_LIM_SAMPLETYPEGROUP2TEST"."testId" IS '测试项目id';


CREATE TABLE "TB_LIM_SERIALIDENTIFIERCONFIG"
(
    "id"            VARCHAR(50)                                                  NOT NULL,
    "configCode"    VARCHAR(100),
    "configName"    VARCHAR(100),
    "configType"    INT           DEFAULT 1                                      NOT NULL,
    "configRule"    VARCHAR(1000),
    "qcType"        INT           DEFAULT 0                                      NOT NULL,
    "qcGrade"       INT           DEFAULT 0                                      NOT NULL,
    "orderNum"      INT           DEFAULT 0                                      NOT NULL,
    "remark"        VARCHAR(1000),
    "orgId"         VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "isDeleted"     BIT           DEFAULT 0                                      NOT NULL,
    "creator"       VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"    TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"      VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"      VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"    TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "projectTypeId" VARCHAR(1000) DEFAULT '00000000-0000-0000-0000-000000000000',
    NOT             CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_SERIALIDENTIFIERCONFIG" IS '序列号定义配置';
COMMENT
ON COLUMN "TB_LIM_SERIALIDENTIFIERCONFIG"."configCode" IS '配置编号(预留)';
COMMENT
ON COLUMN "TB_LIM_SERIALIDENTIFIERCONFIG"."configName" IS '配置名称';
COMMENT
ON COLUMN "TB_LIM_SERIALIDENTIFIERCONFIG"."configRule" IS '配置规则';
COMMENT
ON COLUMN "TB_LIM_SERIALIDENTIFIERCONFIG"."configType" IS '1:项目编号,2:样品编号,3:质控样编号,4:送样单编号,5:报告编号,6:工作单编号';
COMMENT
ON COLUMN "TB_LIM_SERIALIDENTIFIERCONFIG"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_SERIALIDENTIFIERCONFIG"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_SERIALIDENTIFIERCONFIG"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_SERIALIDENTIFIERCONFIG"."id" IS '主键id';
COMMENT
ON COLUMN "TB_LIM_SERIALIDENTIFIERCONFIG"."isDeleted" IS '假删';
COMMENT
ON COLUMN "TB_LIM_SERIALIDENTIFIERCONFIG"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_SERIALIDENTIFIERCONFIG"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_SERIALIDENTIFIERCONFIG"."orderNum" IS '排序号';
COMMENT
ON COLUMN "TB_LIM_SERIALIDENTIFIERCONFIG"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_SERIALIDENTIFIERCONFIG"."qcGrade" IS '质控等级--只有当选择质控样编号的时候才启用（枚举EnumQCType：0.空白 1.平行 2.标准 3.加标）';
COMMENT
ON COLUMN "TB_LIM_SERIALIDENTIFIERCONFIG"."qcType" IS '质控类型--只有当选择质控样编号的时候才启用（枚举EnumQCGrade：0.外部质控  1.内部质控）';
COMMENT
ON COLUMN "TB_LIM_SERIALIDENTIFIERCONFIG"."remark" IS '备注';


CREATE TABLE "TB_LIM_SERIALNUMBERCONFIG"
(
    "id"               VARCHAR(50)                                                 NOT NULL,
    "serialNumberType" VARCHAR(100),
    "para0"            VARCHAR(100),
    "para1"            VARCHAR(100),
    "para2"            VARCHAR(100),
    "para3"            VARCHAR(100),
    "orgId"            VARCHAR(50),
    "lastUpdateTime"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "creator"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT                CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_SERIALNUMBERCONFIG" IS '流水号的配置';
COMMENT
ON COLUMN "TB_LIM_SERIALNUMBERCONFIG"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_SERIALNUMBERCONFIG"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_SERIALNUMBERCONFIG"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_SERIALNUMBERCONFIG"."id" IS '主键id';
COMMENT
ON COLUMN "TB_LIM_SERIALNUMBERCONFIG"."lastUpdateTime" IS '最新更新时间';
COMMENT
ON COLUMN "TB_LIM_SERIALNUMBERCONFIG"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_SERIALNUMBERCONFIG"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_SERIALNUMBERCONFIG"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_SERIALNUMBERCONFIG"."para0" IS '参数1';
COMMENT
ON COLUMN "TB_LIM_SERIALNUMBERCONFIG"."para1" IS '参数2';
COMMENT
ON COLUMN "TB_LIM_SERIALNUMBERCONFIG"."para2" IS '参数3';
COMMENT
ON COLUMN "TB_LIM_SERIALNUMBERCONFIG"."para3" IS '参数4';
COMMENT
ON COLUMN "TB_LIM_SERIALNUMBERCONFIG"."serialNumberType" IS '序号生成类型';


CREATE TABLE "TB_LIM_TEST"
(
    "id"                     VARCHAR(50)                                                   NOT NULL,
    "parentId"               VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "analyzeMethodId"        VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "redAnalyzeMethodName"   VARCHAR(255),
    "redCountryStandard"     VARCHAR(100),
    "analyzeItemId"          VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "redAnalyzeItemName"     VARCHAR(100),
    "fullPinYin"             VARCHAR(255),
    "pinYin"                 VARCHAR(100),
    "sampleTypeId"           VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "testCode"               VARCHAR(50),
    "dimensionId"            VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "examLimitValue"         VARCHAR(50),
    "validTime"              DECIMAL(18, 2) DEFAULT (-1.)                                  NOT NULL,
    "orderNum"               INT            DEFAULT 0                                      NOT NULL,
    "mostSignificance"       INT            DEFAULT (-1)                                   NOT NULL,
    "mostDecimal"            INT            DEFAULT (-1)                                   NOT NULL,
    "cert"                   INT            DEFAULT 4                                      NOT NULL,
    "testName"               VARCHAR(1000),
    "isOutsourcing"          BIT            DEFAULT 0                                      NOT NULL,
    "isCompleteField"        BIT            DEFAULT 0                                      NOT NULL,
    "isDeleted"              BIT            DEFAULT 0                                      NOT NULL,
    "isQCP"                  BIT            DEFAULT 0                                      NOT NULL,
    "isQCB"                  BIT            DEFAULT 0                                      NOT NULL,
    "isSeries"               BIT            DEFAULT 0                                      NOT NULL,
    "isUseFormula"           BIT            DEFAULT 0                                      NOT NULL,
    "examLimitValueLess"     VARCHAR(50),
    "samplePeriod"           INT,
    "lowerLimit"             VARCHAR(50),
    "totalTestName"          VARCHAR(1000),
    "isShowTotalTest"        BIT            DEFAULT 0                                      NOT NULL,
    "isTotalTest"            BIT            DEFAULT 0                                      NOT NULL,
    "isUseQTFormula"         BIT            DEFAULT 0                                      NOT NULL,
    "kValueFormat"           INT            DEFAULT (-1)                                   NOT NULL,
    "bValueFormat"           INT            DEFAULT (-1)                                   NOT NULL,
    "cValueFormat"           INT            DEFAULT (-1)                                   NOT NULL,
    "samplingCharge"         DECIMAL(18, 2)                                                NOT NULL,
    "testingCharge"          DECIMAL(18, 2)                                                NOT NULL,
    "reportDimensionId"      VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "reportMostSignificance" INT            DEFAULT (-1)                                   NOT NULL,
    "reportMostDecimal"      INT            DEFAULT (-1)                                   NOT NULL,
    "remark"                 VARCHAR(1000),
    "isInsUseRecord"         BIT            DEFAULT 0                                      NOT NULL,
    "isSubSync"              BIT            DEFAULT 0                                      NOT NULL,
    "inputMode"              INT            DEFAULT 0                                      NOT NULL,
    "domainCode"             VARCHAR(50),
    "redYearSn"              VARCHAR(100),
    "testTimelen"            INT            DEFAULT (-1)                                   NOT NULL,
    "basicWorkload"          DECIMAL(18, 2) DEFAULT (-1.)                                  NOT NULL,
    "unitWorkload"           DECIMAL(18, 2) DEFAULT (-1.)                                  NOT NULL,
    "externalId"             VARCHAR(100)   DEFAULT '00000000-0000-0000-0000-000000000000',
    "orgId"                  VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"                VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"             TIMESTAMP(0)   DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"               VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"               VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"             TIMESTAMP(0)   DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "reviseType"             INT            DEFAULT 1                                      NOT NULL,
    "kDecimalFormat"         INT            DEFAULT (-1)                                   NOT NULL,
    "bDecimalFormat"         INT            DEFAULT (-1)                                   NOT NULL,
    "cDecimalFormat"         INT            DEFAULT (-1)                                   NOT NULL,
    "calculateWay"           INT            DEFAULT 0                                      NOT NULL,
    "mergeBase"              INT                                                           NOT NULL,
    "analyseDayLen"          INT            DEFAULT 2                                      NOT NULL,
    "averageCompute"         INT            DEFAULT 0                                      NOT NULL,
    "validate"               INT            DEFAULT 0,
    "usageNum"               INT,
    "isQCTransport"          BIT            DEFAULT 0                                      NOT NULL,
    "isQCInstrument"         BIT            DEFAULT 0                                      NOT NULL,
    "airPollution"           VARCHAR(100)   DEFAULT '',
    "isQCLocal"              BIT            DEFAULT 0                                      NOT NULL,
    "isAbolish"              BIT            DEFAULT 0                                      NOT NULL,
    "isSci"                  BIT            DEFAULT 0                                      NOT NULL,
    "potencyComputeMode"     INT            DEFAULT 1                                      NOT NULL,
    "timesOrder"             INT            DEFAULT 1                                      NOT NULL,
    NOT                      CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_TEST"."airPollution" IS '空气污染物';
COMMENT
ON COLUMN "TB_LIM_TEST"."analyseDayLen" IS '分析时长（天数）';
COMMENT
ON COLUMN "TB_LIM_TEST"."analyzeItemId" IS '分析项目Id（Guid）';
COMMENT
ON COLUMN "TB_LIM_TEST"."analyzeMethodId" IS '分析方法Id（Guid）';
COMMENT
ON COLUMN "TB_LIM_TEST"."averageCompute" IS '均值计算方式（0：算术均值，1：原样值，2：几何均值）';
COMMENT
ON COLUMN "TB_LIM_TEST"."basicWorkload" IS '基础工作量（预留）';
COMMENT
ON COLUMN "TB_LIM_TEST"."bDecimalFormat" IS '截距小数位数';
COMMENT
ON COLUMN "TB_LIM_TEST"."bValueFormat" IS '截距有效位数';
COMMENT
ON COLUMN "TB_LIM_TEST"."calculateWay" IS '计算方式';
COMMENT
ON COLUMN "TB_LIM_TEST"."cDecimalFormat" IS '实数小数位数';
COMMENT
ON COLUMN "TB_LIM_TEST"."cert" IS '测试资质(枚举EnumCert：0非认可认证、1认证、2认可、4认证认可）';
COMMENT
ON COLUMN "TB_LIM_TEST"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_TEST"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_TEST"."cValueFormat" IS '实数有效位数';
COMMENT
ON COLUMN "TB_LIM_TEST"."dimensionId" IS '计量单位（Guid）';
COMMENT
ON COLUMN "TB_LIM_TEST"."domainCode" IS '实验室编号（预留）';
COMMENT
ON COLUMN "TB_LIM_TEST"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_TEST"."examLimitValue" IS '检出限';
COMMENT
ON COLUMN "TB_LIM_TEST"."examLimitValueLess" IS '小于检出限出证结果';
COMMENT
ON COLUMN "TB_LIM_TEST"."externalId" IS '关联系统测试项目编号';
COMMENT
ON COLUMN "TB_LIM_TEST"."fullPinYin" IS '分析项目全拼';
COMMENT
ON COLUMN "TB_LIM_TEST"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_TEST"."inputMode" IS '录入方式（预留）（常量Int，常量名称Lim_TestInputMode）（0：默认，1：生物多样性）';
COMMENT
ON COLUMN "TB_LIM_TEST"."isAbolish" IS '是否废止';
COMMENT
ON COLUMN "TB_LIM_TEST"."isCompleteField" IS '是否现场数据';
COMMENT
ON COLUMN "TB_LIM_TEST"."isDeleted" IS '是否删除';
COMMENT
ON COLUMN "TB_LIM_TEST"."isInsUseRecord" IS '是否填写仪器使用记录（预留）';
COMMENT
ON COLUMN "TB_LIM_TEST"."isOutsourcing" IS '是否分包';
COMMENT
ON COLUMN "TB_LIM_TEST"."isQCB" IS '是否做质控空白';
COMMENT
ON COLUMN "TB_LIM_TEST"."isQCInstrument" IS '是否仪器空白';
COMMENT
ON COLUMN "TB_LIM_TEST"."isQCLocal" IS '是否现场空白';
COMMENT
ON COLUMN "TB_LIM_TEST"."isQCP" IS '是否做质控平行';
COMMENT
ON COLUMN "TB_LIM_TEST"."isQCTransport" IS '是否运输空白';
COMMENT
ON COLUMN "TB_LIM_TEST"."isSci" IS '是否科学计数法';
COMMENT
ON COLUMN "TB_LIM_TEST"."isSeries" IS '是否做串联样';
COMMENT
ON COLUMN "TB_LIM_TEST"."isShowTotalTest" IS '是否显示总称';
COMMENT
ON COLUMN "TB_LIM_TEST"."isSubSync" IS '是否是检测机构传输过来的测试项目（预留）';
COMMENT
ON COLUMN "TB_LIM_TEST"."isTotalTest" IS '是否总称';
COMMENT
ON COLUMN "TB_LIM_TEST"."isUseFormula" IS '是否启用公式';
COMMENT
ON COLUMN "TB_LIM_TEST"."isUseQTFormula" IS '是否启用嵌套公式(检测结果计算出证结果)默认false';
COMMENT
ON COLUMN "TB_LIM_TEST"."kDecimalFormat" IS '斜率小数位数';
COMMENT
ON COLUMN "TB_LIM_TEST"."kValueFormat" IS '斜率有效位数';
COMMENT
ON COLUMN "TB_LIM_TEST"."lowerLimit" IS '测定下限';
COMMENT
ON COLUMN "TB_LIM_TEST"."mergeBase" IS '合并基数';
COMMENT
ON COLUMN "TB_LIM_TEST"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_TEST"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_TEST"."mostDecimal" IS '小数位数';
COMMENT
ON COLUMN "TB_LIM_TEST"."mostSignificance" IS '有效位数';
COMMENT
ON COLUMN "TB_LIM_TEST"."orderNum" IS '排序值';
COMMENT
ON COLUMN "TB_LIM_TEST"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_TEST"."parentId" IS '父级Id';
COMMENT
ON COLUMN "TB_LIM_TEST"."pinYin" IS '分析项目拼音缩写';
COMMENT
ON COLUMN "TB_LIM_TEST"."potencyComputeMode" IS '报告浓度计算方式(EnumComputeMode)';
COMMENT
ON COLUMN "TB_LIM_TEST"."redAnalyzeItemName" IS '分析项目名称';
COMMENT
ON COLUMN "TB_LIM_TEST"."redAnalyzeMethodName" IS '分析方法名称';
COMMENT
ON COLUMN "TB_LIM_TEST"."redCountryStandard" IS '国家标准';
COMMENT
ON COLUMN "TB_LIM_TEST"."redYearSn" IS '年份（预留）';
COMMENT
ON COLUMN "TB_LIM_TEST"."remark" IS '备注（预留）';
COMMENT
ON COLUMN "TB_LIM_TEST"."reportDimensionId" IS '报告计量单位（Guid）';
COMMENT
ON COLUMN "TB_LIM_TEST"."reportMostDecimal" IS '报告小数位数';
COMMENT
ON COLUMN "TB_LIM_TEST"."reportMostSignificance" IS '报告有效位数';
COMMENT
ON COLUMN "TB_LIM_TEST"."sampleTypeId" IS '样品类型（Guid）';
COMMENT
ON COLUMN "TB_LIM_TEST"."samplingCharge" IS '采样费金额';
COMMENT
ON COLUMN "TB_LIM_TEST"."testCode" IS '测试编码';
COMMENT
ON COLUMN "TB_LIM_TEST"."testingCharge" IS '检测费金额';
COMMENT
ON COLUMN "TB_LIM_TEST"."testName" IS '测试名称';
COMMENT
ON COLUMN "TB_LIM_TEST"."testTimelen" IS '分配时长（预留）';
COMMENT
ON COLUMN "TB_LIM_TEST"."timesOrder" IS '批次';
COMMENT
ON COLUMN "TB_LIM_TEST"."totalTestName" IS '总称（冗余分析项目名称）';
COMMENT
ON COLUMN "TB_LIM_TEST"."unitWorkload" IS '单位工作量（预留）';
COMMENT
ON COLUMN "TB_LIM_TEST"."usageNum" IS '使用次数';
COMMENT
ON COLUMN "TB_LIM_TEST"."validate" IS '验证状态 0未验证 1已验证';
COMMENT
ON COLUMN "TB_LIM_TEST"."validTime" IS '样品有效期（h）';


CREATE INDEX "IX_TB_LIM_Test" ON "TB_LIM_TEST" ("sampleTypeId" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "TB_LIM_TEST2INSTRUMENT"
(
    "id"           VARCHAR(50)                                                NOT NULL,
    "testId"       VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "instrumentId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "useType"      INT         DEFAULT (-1)                                   NOT NULL,
    "orgId"        VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    NOT            CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_TEST2INSTRUMENT"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_TEST2INSTRUMENT"."instrumentId" IS '仪器标识（Guid）';
COMMENT
ON COLUMN "TB_LIM_TEST2INSTRUMENT"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_TEST2INSTRUMENT"."testId" IS '测试标识（Guid）';
COMMENT
ON COLUMN "TB_LIM_TEST2INSTRUMENT"."useType" IS '使用类型（枚举EnumInsUseObjType：1采样，2：实验室分析，4：现场分析）';


CREATE TABLE "TB_LIM_TESTEXPAND"
(
    "id"                 VARCHAR(50)                                                NOT NULL,
    "testId"             VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "sampleTypeId"       VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "mostSignificance"   INT         DEFAULT (-1)                                   NOT NULL,
    "mostDecimal"        INT         DEFAULT (-1)                                   NOT NULL,
    "examLimitValue"     VARCHAR(50),
    "dimensionId"        VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "examLimitValueLess" VARCHAR(50),
    "samplePeriod"       INT,
    "lowerLimit"         VARCHAR(50),
    "orgId"              VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "potencyComputeMode" INT         DEFAULT 1                                      NOT NULL,
    "timesOrder"         INT         DEFAULT 1                                      NOT NULL,
    NOT                  CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_TESTEXPAND"."dimensionId" IS '单位（Guid）';
COMMENT
ON COLUMN "TB_LIM_TESTEXPAND"."examLimitValue" IS '检出限';
COMMENT
ON COLUMN "TB_LIM_TESTEXPAND"."examLimitValueLess" IS '小于检出限（预留）';
COMMENT
ON COLUMN "TB_LIM_TESTEXPAND"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_TESTEXPAND"."lowerLimit" IS '测定下限';
COMMENT
ON COLUMN "TB_LIM_TESTEXPAND"."mostDecimal" IS '小数位数';
COMMENT
ON COLUMN "TB_LIM_TESTEXPAND"."mostSignificance" IS '有效位数';
COMMENT
ON COLUMN "TB_LIM_TESTEXPAND"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_TESTEXPAND"."potencyComputeMode" IS '报告浓度计算方式(EnumComputeMode)';
COMMENT
ON COLUMN "TB_LIM_TESTEXPAND"."sampleTypeId" IS '样品类型Id（Guid）';
COMMENT
ON COLUMN "TB_LIM_TESTEXPAND"."testId" IS '测试Id（Guid）';
COMMENT
ON COLUMN "TB_LIM_TESTEXPAND"."timesOrder" IS '批次';


CREATE TABLE "TB_LIM_TESTOPERATELOG"
(
    "id"           VARCHAR(50)                                                NOT NULL,
    "tableName"    VARCHAR(50)                                                NOT NULL,
    "tableId"      VARCHAR(50)                                                NOT NULL,
    "operatorId"   VARCHAR(50)                                                NOT NULL,
    "operatorDate" TIMESTAMP(0)                                               NOT NULL,
    "operateType"  INT                                                        NOT NULL,
    "operateField" VARCHAR(50),
    "oldValue"     VARCHAR(1000),
    "newValue"     VARCHAR(1000),
    "orgId"        VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "domainId"     VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    NOT            CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_TESTOPERATELOG" IS '测试项目相关的表变动信息记录表';
COMMENT
ON COLUMN "TB_LIM_TESTOPERATELOG"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_TESTOPERATELOG"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_TESTOPERATELOG"."newValue" IS '新值';
COMMENT
ON COLUMN "TB_LIM_TESTOPERATELOG"."oldValue" IS '旧值';
COMMENT
ON COLUMN "TB_LIM_TESTOPERATELOG"."operateField" IS '操作字段';
COMMENT
ON COLUMN "TB_LIM_TESTOPERATELOG"."operateType" IS '操作类型';
COMMENT
ON COLUMN "TB_LIM_TESTOPERATELOG"."operatorDate" IS '操作时间';
COMMENT
ON COLUMN "TB_LIM_TESTOPERATELOG"."operatorId" IS '操作人标识';
COMMENT
ON COLUMN "TB_LIM_TESTOPERATELOG"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_TESTOPERATELOG"."tableId" IS '表主键';
COMMENT
ON COLUMN "TB_LIM_TESTOPERATELOG"."tableName" IS '表名';


CREATE TABLE "TB_LIM_TESTPOST"
(
    "id"             VARCHAR(50)                                                 NOT NULL,
    "postCode"       VARCHAR(100),
    "postName"       VARCHAR(100)                                                NOT NULL,
    "orderNum"       INT          DEFAULT 0                                      NOT NULL,
    "description"    VARCHAR(1000),
    "isDeleted"      BIT          DEFAULT 0                                      NOT NULL,
    "orgId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "postType"       INT          DEFAULT (-1)                                   NOT NULL,
    "chargePerson"   VARCHAR(50)  DEFAULT ''                                     NOT NULL,
    "chargePersonId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "car"            VARCHAR(50),
    "carId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000',
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_TESTPOST"."car" IS '采样车辆';
COMMENT
ON COLUMN "TB_LIM_TESTPOST"."carId" IS '采样车辆标识';
COMMENT
ON COLUMN "TB_LIM_TESTPOST"."chargePerson" IS '采样负责人';
COMMENT
ON COLUMN "TB_LIM_TESTPOST"."chargePersonId" IS '采样负责人标识';
COMMENT
ON COLUMN "TB_LIM_TESTPOST"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_TESTPOST"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_TESTPOST"."description" IS '岗位描述';
COMMENT
ON COLUMN "TB_LIM_TESTPOST"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_TESTPOST"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_TESTPOST"."isDeleted" IS '是否删除';
COMMENT
ON COLUMN "TB_LIM_TESTPOST"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_TESTPOST"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_TESTPOST"."orderNum" IS '排序值';
COMMENT
ON COLUMN "TB_LIM_TESTPOST"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_TESTPOST"."postCode" IS '岗位编码';
COMMENT
ON COLUMN "TB_LIM_TESTPOST"."postName" IS '岗位名称';
COMMENT
ON COLUMN "TB_LIM_TESTPOST"."postType" IS '岗位类型 EnumPostType 1：现场 2：分析';


CREATE TABLE "TB_LIM_TESTPOST2PERSON"
(
    "id"         VARCHAR(50)                                                NOT NULL,
    "testPostId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "personId"   VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "orderNum"   INT         DEFAULT 0                                      NOT NULL,
    NOT          CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_TESTPOST2PERSON"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_TESTPOST2PERSON"."orderNum" IS '排序值';
COMMENT
ON COLUMN "TB_LIM_TESTPOST2PERSON"."personId" IS '人员Id';
COMMENT
ON COLUMN "TB_LIM_TESTPOST2PERSON"."testPostId" IS '测试岗位Id';


CREATE TABLE "TB_LIM_TESTPOST2TEST"
(
    "id"         VARCHAR(50)                                                NOT NULL,
    "testPostId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "testId"     VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "orderNum"   INT         DEFAULT 0                                      NOT NULL,
    NOT          CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_TESTPOST2TEST"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_TESTPOST2TEST"."orderNum" IS '排序值';
COMMENT
ON COLUMN "TB_LIM_TESTPOST2TEST"."testId" IS '测试项目id';
COMMENT
ON COLUMN "TB_LIM_TESTPOST2TEST"."testPostId" IS '测试岗位Id';


CREATE TABLE "TB_LIM_TESTQCRANGE"
(
    "id"          VARCHAR(50)                                                 NOT NULL,
    "testId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "rangeConfig" VARCHAR(50),
    "relLimit"    VARCHAR(50),
    "absLimit"    VARCHAR(50),
    "qcGrade"     INT          DEFAULT (-1)                                   NOT NULL,
    "qcType"      INT          DEFAULT (-1)                                   NOT NULL,
    "orgId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT           CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_TESTQCRANGE"."absLimit" IS '绝对偏差';
COMMENT
ON COLUMN "TB_LIM_TESTQCRANGE"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_TESTQCRANGE"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_TESTQCRANGE"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_TESTQCRANGE"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_TESTQCRANGE"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_TESTQCRANGE"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_TESTQCRANGE"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_TESTQCRANGE"."qcGrade" IS '质控等级（枚举EnumQCGrade：1.外部质控  2.内部质控）';
COMMENT
ON COLUMN "TB_LIM_TESTQCRANGE"."qcType" IS '质控类型（枚举EnumQCType：1.平行 2.空白 4.加标 8.标样）';
COMMENT
ON COLUMN "TB_LIM_TESTQCRANGE"."rangeConfig" IS '数值范围';
COMMENT
ON COLUMN "TB_LIM_TESTQCRANGE"."relLimit" IS '平行：相对偏差，其他质控类型：偏差';
COMMENT
ON COLUMN "TB_LIM_TESTQCRANGE"."testId" IS '测试标识';


CREATE TABLE "TB_LIM_TESTQCREMINDCONFIG"
(
    "id"              VARCHAR(50)                                                 NOT NULL,
    "qcGrade"         INT          DEFAULT (-1)                                   NOT NULL,
    "qcType"          INT          DEFAULT (-1)                                   NOT NULL,
    "qcRemindPercent" INT          DEFAULT 10                                     NOT NULL,
    "isDefault"       BIT          DEFAULT 0                                      NOT NULL,
    "orgId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT               CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_TESTQCREMINDCONFIG"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_TESTQCREMINDCONFIG"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_TESTQCREMINDCONFIG"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_TESTQCREMINDCONFIG"."id" IS 'id';
COMMENT
ON COLUMN "TB_LIM_TESTQCREMINDCONFIG"."isDefault" IS '是否默认';
COMMENT
ON COLUMN "TB_LIM_TESTQCREMINDCONFIG"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_TESTQCREMINDCONFIG"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_TESTQCREMINDCONFIG"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_TESTQCREMINDCONFIG"."qcGrade" IS '质控等级（枚举EnumQCGrade：1.外部质控  2.内部质控）';
COMMENT
ON COLUMN "TB_LIM_TESTQCREMINDCONFIG"."qcRemindPercent" IS '质控百分比';
COMMENT
ON COLUMN "TB_LIM_TESTQCREMINDCONFIG"."qcType" IS '质控类型（枚举EnumQCType：1.平行 2.空白 4.加标 8.标样）';


CREATE TABLE "TB_LIM_TESTQCREMINDCONFIG2TEST"
(
    "configId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "testId"   VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "id"       VARCHAR(50)                                                NOT NULL,
    NOT        CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_TESTQCREMINDCONFIG2TEST"."configId" IS '质控比例配置id';
COMMENT
ON COLUMN "TB_LIM_TESTQCREMINDCONFIG2TEST"."testId" IS '测试项目id';


CREATE TABLE "TB_LIM_TRAINING"
(
    "id"              VARCHAR(50)                                                 NOT NULL,
    "trainingName"    VARCHAR(50)                                                 NOT NULL,
    "trainingDate"    TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "way"             VARCHAR(50),
    "times"           NUMBER(11,1),
    "lecturer"        VARCHAR(50),
    "recorder"        VARCHAR(50),
    "status"          INT          DEFAULT 0                                      NOT NULL,
    "auditInd"        BIT          DEFAULT 0                                      NOT NULL,
    "content"         VARCHAR(255),
    "planPeopleNums"  INT,
    "applicant"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "applicationDate" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00',
    "isDeleted"       BIT          DEFAULT 0,
    "orgId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT               CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_TRAINING"."applicant" IS '申请人';
COMMENT
ON COLUMN "TB_LIM_TRAINING"."applicationDate" IS '申请日期';
COMMENT
ON COLUMN "TB_LIM_TRAINING"."auditInd" IS '是否审批 0: 否  1：是';
COMMENT
ON COLUMN "TB_LIM_TRAINING"."content" IS '培训内容';
COMMENT
ON COLUMN "TB_LIM_TRAINING"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_TRAINING"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_TRAINING"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_TRAINING"."id" IS '主键';
COMMENT
ON COLUMN "TB_LIM_TRAINING"."isDeleted" IS '是否删除';
COMMENT
ON COLUMN "TB_LIM_TRAINING"."lecturer" IS '培训讲师';
COMMENT
ON COLUMN "TB_LIM_TRAINING"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_TRAINING"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_TRAINING"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_TRAINING"."planPeopleNums" IS '计划参加人数';
COMMENT
ON COLUMN "TB_LIM_TRAINING"."recorder" IS '记录人';
COMMENT
ON COLUMN "TB_LIM_TRAINING"."status" IS '培训状态  0: 未开展  1：已开展';
COMMENT
ON COLUMN "TB_LIM_TRAINING"."times" IS '培训时长';
COMMENT
ON COLUMN "TB_LIM_TRAINING"."trainingDate" IS '培训日期';
COMMENT
ON COLUMN "TB_LIM_TRAINING"."trainingName" IS '培训名称';
COMMENT
ON COLUMN "TB_LIM_TRAINING"."way" IS '培训方式  常量表示 LIM_Training';


CREATE TABLE "TB_LIM_TRAINING2PARTICIPANTS"
(
    "id"               VARCHAR(50) NOT NULL,
    "trainingId"       VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000',
    "participantsId"   VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000',
    "participantsName" VARCHAR(50),
    NOT                CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_TRAINING2PARTICIPANTS"."id" IS '主键';
COMMENT
ON COLUMN "TB_LIM_TRAINING2PARTICIPANTS"."participantsId" IS '参与人id';
COMMENT
ON COLUMN "TB_LIM_TRAINING2PARTICIPANTS"."participantsName" IS '参与人名称';
COMMENT
ON COLUMN "TB_LIM_TRAINING2PARTICIPANTS"."trainingId" IS '培训id';


CREATE TABLE "TB_LIM_VERSIONINFO"
(
    "id"         VARCHAR(50)                                                 NOT NULL,
    "version"    VARCHAR(50),
    "verValue"   VARCHAR(2000),
    "verUrl"     VARCHAR(255),
    "verTime"    TIMESTAMP(0)                                                NOT NULL,
    "verType"    VARCHAR(50)                                                 NOT NULL,
    "verCode"    VARCHAR(300),
    "orgId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "codeUrl"    VARCHAR(3000),
    NOT          CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "TB_LIM_VERSIONINFO"."codeUrl" IS '二维码链接';
COMMENT
ON COLUMN "TB_LIM_VERSIONINFO"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_VERSIONINFO"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_VERSIONINFO"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_VERSIONINFO"."id" IS '主键';
COMMENT
ON COLUMN "TB_LIM_VERSIONINFO"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_VERSIONINFO"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_VERSIONINFO"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_VERSIONINFO"."verCode" IS '版本二维码';
COMMENT
ON COLUMN "TB_LIM_VERSIONINFO"."version" IS '版本号';
COMMENT
ON COLUMN "TB_LIM_VERSIONINFO"."verTime" IS '上传时间';
COMMENT
ON COLUMN "TB_LIM_VERSIONINFO"."verType" IS '版本类型';
COMMENT
ON COLUMN "TB_LIM_VERSIONINFO"."verUrl" IS '附件路径';
COMMENT
ON COLUMN "TB_LIM_VERSIONINFO"."verValue" IS '更新内容';


CREATE TABLE "TB_LIM_WORKDAYCONFIG"
(
    "id"         VARCHAR(50)  NOT NULL,
    "workday"    VARCHAR(20)  NOT NULL,
    "weekendDay" VARCHAR(20)  NOT NULL,
    "year"       INT          NOT NULL,
    "orgId"      VARCHAR(50)  NOT NULL,
    "creator"    VARCHAR(50)  NOT NULL,
    "createDate" TIMESTAMP(0) NOT NULL,
    "domainId"   VARCHAR(50)  NOT NULL,
    "modifier"   VARCHAR(50)  NOT NULL,
    "modifyDate" TIMESTAMP(0) NOT NULL,
    NOT          CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_WORKDAYCONFIG" IS '工作休息日管理配置';
COMMENT
ON COLUMN "TB_LIM_WORKDAYCONFIG"."createDate" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_WORKDAYCONFIG"."creator" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_WORKDAYCONFIG"."domainId" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_WORKDAYCONFIG"."id" IS '主键';
COMMENT
ON COLUMN "TB_LIM_WORKDAYCONFIG"."modifier" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_WORKDAYCONFIG"."modifyDate" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_WORKDAYCONFIG"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_WORKDAYCONFIG"."weekendDay" IS '休息日（1：周日，2：周一，3：周二 ，4：周三，5：周四，6：周五，7：周六 以英文逗号拼接）';
COMMENT
ON COLUMN "TB_LIM_WORKDAYCONFIG"."workday" IS '工作日（1：周日，2：周一，3：周二 ，4：周三，5：周四，6：周五，7：周六 以英文逗号拼接）';
COMMENT
ON COLUMN "TB_LIM_WORKDAYCONFIG"."year" IS '年份';


