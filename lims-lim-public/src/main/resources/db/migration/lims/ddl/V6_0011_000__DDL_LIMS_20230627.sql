-- 首页待办数量表
create table TB_PRO_HomePendingNo
(
    id         varchar(50)                                                not null comment '主键' primary key,
    moduleCode   varchar(50)                                              not null comment '模块编码',
    nums  int(11) DEFAULT '0' NOT NULL  comment '待办数量',
    userId varchar(50) DEFAULT '00000000-0000-0000-0000-000000000000'     not null    COMMENT '用户id',
    orgId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    createDate datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifyDate datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;
