package com.sinoyd.lims.lim.controller;

import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.DocAuthorityCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoDocAuthority;
import com.sinoyd.lims.lim.dto.customer.DtoDocAuthorityTemp;
import com.sinoyd.lims.lim.dto.customer.DtoDocAuthorityValidate;
import com.sinoyd.lims.lim.service.DocAuthorityService;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;
import java.util.Map;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

/**
 * 文件夹权限配置
 * <AUTHOR>
 * @version V1.0.0 2019/5/14
 * @since V100R001
 */
@Api(tags = "文件夹权限配置: 文件夹权限配置服务")
@RestController
@RequestMapping("/api/lim/docAuthority")
@Validated
public class DocAuthorityController extends BaseJpaController<DtoDocAuthority, String, DocAuthorityService> {

    /**
     * 角色和角色拥有的权限
     *
     * @param criteria 角色权限查询条件
     * @return 返回权限集合
     */
    @ApiOperation(value = "查询角色和角色拥有的权限", notes = "查询角色和角色拥有的权限")
    @GetMapping("")
    public RestResponse<Map<String,Object>> getDocAuthority(DocAuthorityCriteria criteria) {

        RestResponse<Map<String, Object>> restResponse = new RestResponse<>();
        Map<String, Object> dataMaps = service.getAuthorityList(criteria);
        restResponse.setRestStatus(StringUtil.isEmpty(dataMaps) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(dataMaps);
        restResponse.setCount((Integer) dataMaps.get("totalCount"));
        return restResponse;
    }

    /**
     * 文件夹文件权限配置列表
     *
     * @param criteria
     * @return 返回权限配置列表
     */
    @ApiOperation(value = "查询文件夹文件权限配置列表", notes = "查询文件夹文件权限配置列表")
    @GetMapping("/authority")
    public RestResponse<List<DtoDocAuthority>> findByPage(DocAuthorityCriteria criteria) {

        RestResponse<List<DtoDocAuthority>> restResponse = new RestResponse<>();
        PageBean<DtoDocAuthority> page = super.getPageBean();
        service.findByPage(page, criteria);
        restResponse.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(page.getData());
        restResponse.setCount(page.getRowsCount());

        return restResponse;
    }

    /**
     * 文件夹、文件权限保存
     *
     * @param dtoDocAuthorityTemps 文件、权限的map集合
     * @return 新增的权限集合
     */
    @ApiOperation(value = "文件夹、文件权限保存", notes = "文件夹、文件权限保存")
    @PostMapping("")
    public Object create(@Validated @RequestBody List<DtoDocAuthorityTemp> dtoDocAuthorityTemps) {
        RestResponse restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.saveAll(dtoDocAuthorityTemps);
        restResponse.setCount(1);

        return restResponse;

    }

    /**
     * 权限验证
     *
     * @return 返回是否具有权限
     */
    @ApiOperation(value = "权限验证", notes = "权限验证")
    @PostMapping("/validateAuth")
    public RestResponse<Boolean> validateAuth(@RequestBody DtoDocAuthorityValidate dtoDocAuthorityValidate) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setData(service.validateAuth(dtoDocAuthorityValidate));
        return restResponse;
    }

    /**
     * 批量下载文件权限验证
     *
     * @return 能下载的文件标识
     */
    @ApiOperation(value = "批量下载文件权限验证", notes = "批量下载文件权限验证")
    @PostMapping("/validateFileBatchDownload")
    public RestResponse<List<String>> validateFileBatchDownload(@RequestBody List<String> ids) {
        RestResponse<List<String>> restResponse = new RestResponse<>();
        restResponse.setData(service.validateFileBatchDownload(ids));
        return restResponse;
    }
}