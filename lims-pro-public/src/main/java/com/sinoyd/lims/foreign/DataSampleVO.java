package com.sinoyd.lims.foreign;

import lombok.Data;

import java.util.Date;

/**
 * @description 样品信息
 * <AUTHOR>
 * @date 2023-11-07
 */
@Data
public class DataSampleVO {

    /**
     * id
     */
    private String id;

    /**
     * 样品编号
     */
    private String sampleCode;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 点位id
     */
    private String sampleFolderId;

    /**
     * 断面id
     */
    private String fixedPointId;

    /**
     * 频次
     */
    private Integer cycleOrder;

    /**
     * 周期数
     */
    private Integer timesOrder;

    /**
     * 样品数
     */
    private Integer sampleOrder;

    /**
     * 点位
     */
    private String redFolderName;

    /**
     * 检测类型
     */
    private String sampleTypeName;

    /**
     * 样品类别
     */
    private Integer sampleCategory;

    /**
     * 采样开始时间
     */
    private Date samplingTimeBegin;

    /**
     * 采样结束时间
     */
    private Date samplingTimeEnd;

    /**
     * 质控id
     */
    private String qcId;

    /**
     * 质控样的原样id
     */
    private String associateSampleId;

    /**
     * 包装/规格
     */
    private String pack;

    /**
     * 样品重量
     */
    private String sampleWeight;

    /**
     * 样品数量
     */
    private String weightOrQuantity;

    /**
     * 样品颜色
     */
    private String samColor;

    /**
     * 样品特征
     */
    private String sampleExplain;

    /**
     * 样品体积
     */
    private String volume;

    /**
     * 送样单号
     */
    private String recordCode;

    /**
     * 采样时间
     */
    private Date samplingTime;

    /**
     * 送样时间
     */
    private Date sendTime;

    /**
     * 送样负责人
     */
    private String senderName;

    /**
     * 接样日期
     */
    private Date receiveSampleDate;

    /**
     * 点位号
     */
    private String folderCode;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;
}
