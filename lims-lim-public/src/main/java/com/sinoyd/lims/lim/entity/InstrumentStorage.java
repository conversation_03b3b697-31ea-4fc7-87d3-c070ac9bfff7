package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * InstrumentStorage实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="InstrumentStorage")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class InstrumentStorage implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  InstrumentStorage() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 仪器采购明细标识（Guid）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("仪器采购明细标识（Guid）")
	private String purchaseDetailId;
    
    /**
    * 仪器Id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("仪器Id")
	private String instrumentId;
    
    /**
    * 本站编号
    */
    @Column(length=20)
    @ApiModelProperty("本站编号")
    @Length(message = "本站编号{validation.message.length}", max = 20)
    private String instrumentsCode;
    
    /**
    * 仪器类型（常量：LIM_InstrumentType）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("仪器类型（常量：LIM_InstrumentType）")
    private String instrumentTypeId;
    
    /**
    * 设备名称
    */
    @ApiModelProperty("设备名称")
    @Length(message = "设备名称{validation.message.length}", max = 255)
    private String instrumentName;
    
    /**
    * 规格型号
    */
    @ApiModelProperty("规格型号")
    @Length(message = "规格型号{validation.message.length}", max = 255)
    private String model;
    
    /**
    * 入库人id（Guid）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("入库人id（Guid）")
    private String operator;
    
    /**
    * 入库人姓名
    */
    @Column(length=50)
    @ApiModelProperty("入库人姓名")
	private String operatorName;
    
    /**
    * 入库数量
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("入库数量")
    private Integer storagNum;
    
    /**
    * 入库时间
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("入库时间")
    private Date storagTtime;
    
    /**
    * 入库结存
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("入库结存")
    private BigDecimal balance;
    
    /**
    * 单价
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("单价")
    private BigDecimal unitPrice;
    
    /**
    * 总价
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("总价")
    private BigDecimal totalPrice;
    
    /**
    * 供应商Id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("供应商Id")
    private String supplyCompanyId;
    
    /**
    * 供应商名称
    */
    @Column(length=100)
    @ApiModelProperty("供应商名称")
	private String supplyCompanyName;
    
    /**
    * 出厂编号
    */
    @Column(length=20)
    @ApiModelProperty("出厂编号")
    @Length(message = "出厂编号{validation.message.length}", max = 20)
    private String serialNo;
    
    /**
    * 制造厂商名称
    */
    @Column(length=100)
    @ApiModelProperty("制造厂商名称")
    @Length(message = "制造厂商名称{validation.message.length}", max = 100)
    private String factoryName;
    
    /**
    * 假删
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
	private Boolean isDeleted=false;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }