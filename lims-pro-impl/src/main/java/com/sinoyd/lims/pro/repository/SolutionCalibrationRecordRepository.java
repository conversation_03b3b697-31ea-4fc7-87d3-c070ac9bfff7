package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoSolutionCalibrationRecord;

import java.util.List;

/**
 * DtoSolutionCalibrationRecord数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2024/04/19
 * @since V100R001
 */
public interface SolutionCalibrationRecordRepository extends IBaseJpaRepository<DtoSolutionCalibrationRecord, String> {
    /**
     * 根据solutionCalibrationId查询
     * @param solutionCalibrationId 标定标识
     * @return 记录集合
     */
    List<DtoSolutionCalibrationRecord> findBySolutionCalibrationId(String solutionCalibrationId);
}
