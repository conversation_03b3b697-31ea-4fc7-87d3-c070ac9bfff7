package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsFormula;
import com.sinoyd.lims.lim.dto.customer.DtoParamsFormulaDetail;

import java.util.List;


/**
 * ParamsFormula操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/4
 * @since V100R001
 */
public interface ParamsFormulaService extends IBaseJpaService<DtoParamsFormula, String> {

    /**
     * 根据Id获取有假删的数据
     *
     * @param ids 方法ids
     * @return 返回带有假删的公式
     */
    List<DtoParamsFormula> findAllDeleted(List<String> ids);


    /**
     * 含假删的公式
     *
     * @return 返回公式
     */
    List<DtoParamsFormula> findAllDeleted();

    /**
     *  根据对象id获取相关的公式
     * @param objectIds 对象的id
     * @return 返回相应的公式
     */
    List<DtoParamsFormula> findByObjectIds(List<String> objectIds);

    /**
     * 获取参数公式明细
     *
     * @param formulaIds 公式id
     * @return 公式明细
     */
    List<DtoParamsFormulaDetail> findParamsFormulaDetails(List<String> formulaIds);

}