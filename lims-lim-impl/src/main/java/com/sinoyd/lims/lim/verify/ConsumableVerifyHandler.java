package com.sinoyd.lims.lim.verify;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.dto.customer.DtoImportConsumable;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.lims.lim.dto.customer.DtoImportConsumableExtend;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.utils.ImportUtils;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 消耗品/标准样品导入数据校验
 *
 * <AUTHOR>
 * @version V1.0.0 2022/3/24
 * @since V100R001
 */
@Data
public class ConsumableVerifyHandler implements IExcelVerifyHandler<DtoImportConsumable> {

    /**
     * 业务参数
     */
    private final Map<String,Boolean> relationMap;

    /**
     * 样品类型
     */
    private final String consumableType;

    /**
     * 消耗品等级
     */
    private final List<DtoCode> dbGrade;

    /**
     * 消耗品
     */
    private final List<DtoCode> dbConsType;

    /**
     * 系统内人员数据
     */
    private final List<DtoPerson> dbPerson;

    /**
     * 分包商数据
     */
    private final List<DtoEnterprise> supplierEntList;

    /**
     * 关联数据
     */
    private final List<DtoImportConsumableExtend> importConsumableExtends;

    public ConsumableVerifyHandler(Map<String, Boolean> relationMap, String consumableType, List<DtoCode> dbGrade,
                                   List<DtoCode> dbConsType, List<DtoPerson> dbPerson, List<DtoEnterprise> supplierEntList,
                                   List<DtoImportConsumableExtend> importConsumableExtends) {
        this.relationMap = relationMap;
        this.consumableType = consumableType;
        this.dbGrade = dbGrade;
        this.dbConsType = dbConsType;
        this.dbPerson = dbPerson;
        this.supplierEntList = supplierEntList;
        this.importConsumableExtends = importConsumableExtends;
    }

    /**
     * 临时数据
     */
    private List<DtoImportConsumable> consumableTempList = new ArrayList<>();

    /**
     * 工具类
     */
    private final ImportUtils importUtils = new ImportUtils();

    /**
     * 数据校验
     *
     * @param dto 导入数据
     * @return 校验结果
     */
    @Override
    public ExcelVerifyHandlerResult verifyHandler(DtoImportConsumable dto) {
        try {
            //跳过空行
            if (importUtils.checkObjectIsNull(dto)) {
                return new ExcelVerifyHandlerResult(true);
            }
            //前后去空格
            importUtils.strToTrim(dto);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }
        //校验结果
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        //校验错误信息
        StringBuilder failStr = new StringBuilder("第"+dto.getRowNum()+"行数据校验错误");
        //必填项判断
        if ("LIM_ConsumableCategory".equals(consumableType)){
            dto.setConsumableName(dto.getConsumableName2());
            dto.setCategoryId(dto.getCategoryId2());
            dto.getDetail().setExpiryDate(dto.getDetail().getExpiryDate2());
        }
        importUtils.checkIsNull(result,dto.getConsumableName(),"消耗品名称",failStr);
        importUtils.checkIsNull(result,dto.getInventory(),"入库数量",failStr);
        importUtils.checkIsNull(result,dto.getSendWarnUserName(),"管理员",failStr);
        importUtils.checkIsNull(result,dto.getDetail().getStorageDate(),"入库日期",failStr);
        if ("LIM_StandardCategory".equals(consumableType)){
            importUtils.checkIsNull(result,dto.getDetail().getExpiryDate(),"有效日期",failStr);
            importUtils.checkIsNull(result,dto.getConsumableCode(),"标样编号",failStr);
            importUtils.checkIsNull(result,dto.getIsLabEncryption(),"是否实验室加密",failStr);
            dto.setCodeInStation(dto.getStandardCodeInStation());
        }
        //获取导入的消耗品类型
        isExistCategory(dto,result,failStr);
        //判断管理人员是否存在
        isExistPerson(dto,result,failStr);
        //判断消耗品等级是否存在
        isExistGrade(dto,result,failStr);
        //判断有效日期前后性
        checkDateIsBefore(dto,result,failStr);
        //校验区间类型
        checkRangeType(dto, result, failStr);
        //格式校验
        importUtils.checkNumTwo(result,dto.getInventory(),"库存数量",failStr);
        importUtils.checkNumTwo(result,dto.getWarningNum(),"库存警告数量",failStr);
        importUtils.checkNumTwo(result,dto.getDetail().getUnitPrice(),"单价",failStr);
        importUtils.checkDateTwo(result,dto.getDetail().getExpiryDate(),"有效日期",failStr);
        importUtils.checkDateTwo(result,dto.getDetail().getStorageDate(),"入库日期",failStr);
        String failString = failStr.toString().replaceFirst("；",":");
        result.setMsg(failString);
        consumableTempList.add(dto);
        return result;
    }

    /**
     * 比较有效日期与入库日期
     * @param dto 导入数据
     * @param result 返回结果
     * @param failStr 校验错误信息
     */
    private void checkDateIsBefore(DtoImportConsumable dto,ExcelVerifyHandlerResult result,StringBuilder failStr){
        if (StringUtil.isNotEmpty(dto.getDetail().getExpiryDate()) && StringUtil.isNotEmpty(dto.getDetail().getStorageDate())){
            Date expiryDate = importUtils.stringToDateAllFormat(dto.getDetail().getExpiryDate());
            Date storageDate = importUtils.stringToDateAllFormat(dto.getDetail().getStorageDate());
            if (storageDate.compareTo(expiryDate) > 0){
                result.setSuccess(false);
                failStr.append("；有效日期不能小于入库日期");
            }
        }
    }

    /**
     * 判断消耗品等级是否存在
     * @param dto 导入数据
     * @param result 校验结果
     * @param failStr 校验错误信息
     */
    private void isExistGrade(DtoImportConsumable dto,ExcelVerifyHandlerResult result,StringBuilder failStr){
        if (StringUtil.isNotEmpty(dto.getGrade())){
            List<DtoCode> isExistGrade = dbGrade.stream().filter(p->dto.getGrade().equals(p.getDictName())).collect(Collectors.toList());
            if (StringUtil.isEmpty(isExistGrade)){
                result.setSuccess(false);
                failStr.append("；等级不存在");
            }
        }
    }

    /**
     * 判断管理员是否存在
     * @param dto 导入数据
     * @param result 校验结果
     * @param failStr 校验错误信息
     */
    private void isExistPerson(DtoImportConsumable dto,ExcelVerifyHandlerResult result,StringBuilder failStr){
        if (StringUtil.isNotEmpty(dto.getSendWarnUserName())){
            List<DtoPerson> isExistPerson = dbPerson.stream().filter(p->dto.getSendWarnUserName().equals(p.getCName())).collect(Collectors.toList());
            if (StringUtil.isEmpty(isExistPerson)){
                result.setSuccess(false);
                failStr.append("；管理人员不存在");
            }
        }
    }

    /**
     * 判断消耗品类型是否存在
     *
     * @param dto 导入数据
     * @param result 校验结果
     * @param failStr 校验错误信息
     */
    private void isExistCategory(DtoImportConsumable dto,ExcelVerifyHandlerResult result,StringBuilder failStr){
        List<String> categoryNames = new ArrayList<>();
        if ("LIM_ConsumableCategory".equals(consumableType)){
            categoryNames = importConsumableExtends.stream().map(DtoImportConsumableExtend::getConsumableCategory).filter(StringUtil::isNotEmpty).collect(Collectors.toList());
        }
        if ("LIM_StandardCategory".equals(consumableType)){
            categoryNames = importConsumableExtends.stream().map(DtoImportConsumableExtend::getConsumableStandardCategory).filter(StringUtil::isNotEmpty).collect(Collectors.toList());
        }

        //判断消耗品类型是否存在
        if (StringUtil.isNotEmpty(dto.getCategoryId())){
            if (!relationMap.get("isImportConsumableType")){
                List<DtoCode> isExistType = dbConsType.stream().filter(p->dto.getCategoryId().equals(p.getDictName())).collect(Collectors.toList());
                if (StringUtil.isEmpty(isExistType)){
                    result.setSuccess(false);
                    failStr.append("；消耗品类型不存在");
                }
            }else{
                List<String> isExistExtend = categoryNames.stream().filter(p->dto.getCategoryId().equals(p)).collect(Collectors.toList());
                if (StringUtil.isEmpty(isExistExtend)){
                    result.setSuccess(false);
                    failStr.append("；消耗品类型在关联表中不存在");
                }
            }
        }else{
            result.setSuccess(false);
            failStr.append("；消耗品类型不能为空");
        }
    }

    /**
     * 不确定度类型为区间时，高低点必填
     * @param result        返回结果
     * @param dto           导入数据
     * @param failStr       校验错误信息
     */
    private void checkRangeType(DtoImportConsumable dto, ExcelVerifyHandlerResult result, StringBuilder failStr) {
        if(StringUtil.isNotEmpty(dto.getUncertainTypeName())
                && EnumBase.EnumUncertainType.区间.getValue().equals(EnumBase.EnumUncertainType.getValueByName(dto.getUncertainTypeName()))
                &&(StringUtil.isEmpty(dto.getRangeLow())||StringUtil.isEmpty(dto.getRangeHigh()))){
            result.setSuccess(false);
            failStr.append("；不确定度类型为区间时，区间范围高低点必填");
        }
    }
}
