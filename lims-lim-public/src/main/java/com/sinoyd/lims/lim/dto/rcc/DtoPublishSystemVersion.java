package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.PublishSystemVersion;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 版本发布管理数据传输类
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/11/9
 **/
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_PublishSystemVersion")
@Data
public class DtoPublishSystemVersion extends PublishSystemVersion {
    private static final long serialVersionUID = 1L;
}
