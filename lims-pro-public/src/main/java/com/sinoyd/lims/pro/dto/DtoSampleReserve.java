package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.SampleReserve;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * DtoSampleReserve实体
 *
 * <AUTHOR>
 * @version V5.2.0 2022/6/20
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_SampleReserve")
@Data
@DynamicInsert
public class DtoSampleReserve extends SampleReserve {

    /**
     * 样品编号
     */
    @Transient
    private String SampleCode;

    /**
     * 样品id集合
     */
    @Transient
    private List<String> sampleIds;

    /**
     * 分析项目ID集合
     */
    @Transient
    private List<String> analyzeItemIds;

    /**
     * 子表数据集合
     */
    @Transient
    private List<DtoSampleReserve2Test> sampleReserve2Tests;


    /**
     * 操作人名称
     */
    @Transient
    private String reservePersonName;

    /**
     *  分组类型id集合
     */
    @Transient
    private List<String> sampleGroupIds;

    /**
     * 是否更新分析数据
     */
    @Transient
    private  Boolean isUpdateAnalyseData = true;

    /**
     * 是否同方法
     */
    @Transient
    private Boolean isSameMethod = false;
}
