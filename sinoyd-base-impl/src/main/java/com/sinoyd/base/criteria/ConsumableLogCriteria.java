package com.sinoyd.base.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Calendar;
import java.util.Date;

/**
 * 消耗领用记录查询条件
 * <AUTHOR>
 * @version V1.0.0 2019/3/11
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ConsumableLogCriteria extends BaseCriteria {

    private String consumableId;// 消耗品Id

    /**
     * 领取开始日期
     */
    private String startTime;

    /**
     * 领取结束日期
     */
    private String endTime;

    /**
     * 领取人id
     */
    private String recipientId;

    /**
     * 关键字（标样名称、标样编号、唯一性编号）
     */
    private String key;

    @Override
    public String getCondition() {
        values.clear(); // 清除条件数据
        StringBuilder condition = new StringBuilder();

        if (StringUtils.isNotNullAndEmpty(this.consumableId) &&
                !UUIDHelper.GUID_EMPTY.equals(this.consumableId)) {
            condition.append(" and consumableId = :consumableId");
            values.put("consumableId", this.consumableId);
        }

        if (StringUtil.isNotEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and occurrenceTime >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
            condition.append(" and occurrenceTime < :endTime");
            values.put("endTime", to);
        }

        if (StringUtil.isNotEmpty(key)) {
            condition.append(" and (exists (select 1 from DtoConsumable c where c.id = p.consumableId and (c.consumableName like :key " +
                    "or c.codeInStation like :key or c.consumableCode like :key)) or (exists (select 1 from DtoConsumable c, DtoConsumableDetail cd where c.id = p.consumableId and cd.parentId = c.id" +
                    " and (cd.productionCode like :key))))");
            values.put("key", "%" + this.key + "%");
        }

        if (StringUtils.isNotNullAndEmpty(this.recipientId) &&
                !UUIDHelper.GUID_EMPTY.equals(this.recipientId)) {
            condition.append(" and userId = :userId");
            values.put("userId", this.recipientId);
        }
        return condition.toString();
    }
}