package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoDetailAnalyseData;

import java.util.List;

/**
 * DetailAnalyseData数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2023/02/28
 * @since V100R001
 */
public interface DetailAnalyseDataRepository extends IBaseJpaRepository<DtoDetailAnalyseData, String> {

    /**
     * 根据样品id查询测试项目数据
     *
     * @param detailDataIds 样品id
     * @return 测试项目数据
     */
    List<DtoDetailAnalyseData> findByDetailDataIdIn(List<String> detailDataIds);
}
