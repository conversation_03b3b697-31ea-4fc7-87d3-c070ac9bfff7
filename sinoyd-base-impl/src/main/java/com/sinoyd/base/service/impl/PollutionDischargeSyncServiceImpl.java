package com.sinoyd.base.service.impl;

import com.sinoyd.base.dto.lims.DtoPollutionDischargeSync;
import com.sinoyd.base.repository.lims.PollutionDischargeSyncRepository;
import com.sinoyd.base.service.PollutionDischargeSyncService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 企业排污许可证服务接口实现
 *
 * <AUTHOR>
 * @version V5.2.0 2025/05/06
 * @since V100R001
 */
@Service
public class PollutionDischargeSyncServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoPollutionDischargeSync, String, PollutionDischargeSyncRepository> implements PollutionDischargeSyncService {
    @Override
    public List<DtoPollutionDischargeSync> findByEnterpriseIdIn(Collection<String> enterpriseIds) {
        return repository.findByEnterpriseIdIn(enterpriseIds);
    }

    @Override
    public List<DtoPollutionDischargeSync> findUnSuccessList() {
        List<DtoPollutionDischargeSync> syncList = repository.findByIsSuccessFalseOrderByRequestTime();
        List<DtoPollutionDischargeSync> retrunList = new ArrayList<>();
        //获取发起时间到现在时间大于30分钟的同步数据
        for (DtoPollutionDischargeSync sync : syncList) {
            if (sync.getRequestTime().getTime() + 15 * 60 * 1000 < System.currentTimeMillis()) {
                retrunList.add(sync);
            }
        }
        return retrunList;
    }
}
