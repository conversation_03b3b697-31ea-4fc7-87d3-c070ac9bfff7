package com.sinoyd.base.controller;

import com.sinoyd.base.criteria.IndustryTypeCriteria;
import com.sinoyd.base.dto.rcc.DtoIndustryType;
import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.base.service.IndustryTypeService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 行业类型控制器
 * <AUTHOR>
 * @version V1.0.0 2019/1/7
 * @since V100R001
 */
@Api(tags = "行业类型: 行业类型服务")
@RestController
@RequestMapping("/api/base/industryType")
@Validated
public class IndustryTypeController extends BaseJpaController<DtoIndustryType, String, IndustryTypeService> {

    /**
     * 根据id获取行业类型
     *
     * @param id 行业类型id
     * @return 行业类型实体
     */
    @ApiOperation(value = "按主键查询行业类型", notes = "按主键查询行业类型")
    @GetMapping("/{id}")
    public RestResponse<DtoIndustryType> getById(@PathVariable String id) {
        RestResponse<DtoIndustryType> restResponse = new RestResponse<DtoIndustryType>();
        DtoIndustryType industryType = service.findOne(id);
        restResponse.setData(industryType);
        restResponse.setRestStatus(StringUtil.isNull(industryType) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResponse;
    }

    /**
     * 根据parentId获取行业类型(备用,接口未定义,待沟通)
     *
     * @param parentId 行业类型parentId
     * @return 行业类型实体
     */
    @ApiOperation(value = "按parentId查询行业类型", notes = "按parentId查询行业类型")
    @GetMapping("/parentId/{parentId}")
    public RestResponse<DtoIndustryType> getByParentId(@PathVariable Integer parentId) {
        RestResponse<DtoIndustryType> restResponse = new RestResponse<>();
        //restResponse.setData(service.getByParentId(parentId));
        restResponse.setRestStatus(StringUtil.isNull(null) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResponse;
    }

    /**
     * 分页查询行业类型
     *
     * @param industryTypeCriteria 行业类型列表:关键字(行业类型名称、行业类型编号),排序,分页
     * @return 行业类型List
     */
    @ApiOperation(value = "分页动态条件查询行业类型", notes = "分页动态条件查询行业类型")
    @GetMapping("")
    public RestResponse<List<DtoIndustryType>> findByPage(IndustryTypeCriteria industryTypeCriteria) {
        RestResponse<List<DtoIndustryType>> restResponse = new RestResponse<>();
        PageBean<DtoIndustryType> pageBean = super.getPageBean();
        service.findByPage(pageBean, industryTypeCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(pageBean.getRowsCount());
        restResponse.setData(pageBean.getData());

        return restResponse;
    }

    /**
     * 新增行业类型
     * 若需要使用form-data传值,每一个参数注解应当使用@RequestParam去接收,就会出现参数过多的情况,感觉不太合适
     *
     * @param industryType 行业类型实体
     * @return 新增的行业类型实体
     */
    @ApiOperation(value = "新增行业类型", notes = "新增行业类型")
    @PostMapping("")
    public RestResponse<DtoIndustryType> create(@Validated @RequestBody DtoIndustryType industryType) {
        RestResponse<DtoIndustryType> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoIndustryType data = service.save(industryType);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 更新行业类型
     *
     * @param industryType 行业类型实体
     * @return 更新后的行业类型实体
     */
    @ApiOperation(value = "更新行业类型", notes = "更新行业类型")
    @Transactional
    @PutMapping("")
    public RestResponse<DtoIndustryType> update(@Validated @RequestBody DtoIndustryType industryType) {
        RestResponse<DtoIndustryType> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoIndustryType data = service.update(industryType);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 单个假删
     *
     * @param id 行业类型id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除行业类型", notes = "根据id删除行业类型")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setCount(service.logicDeleteById(id));

        return restResponse;
    }

    /**
     * 批量假删
     *
     * @param ids 行业类型ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除行业类型", notes = "根据id批量删除行业类型")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setCount(service.logicDeleteById(ids));
        return restResponse;
    }

    /**
     * 检测类型树
     *
     * @return
     */
    @ApiOperation(value = "检测类型树", notes = "检测类型树")
    @GetMapping("/tree")
    public RestResponse<List<TreeNode>> tree() {
        RestResponse<List<TreeNode>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.tree());

        return restResponse;
    }

    /**
     * 检测类型树（大类）
     *
     * @return
     */
    @ApiOperation(value = "大类检测类型树", notes = "大类检测类型树")
    @GetMapping("/bigTree")
    public RestResponse<List<TreeNode>> bigTree() {
        RestResponse<List<TreeNode>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.bigTree());
        return restResponse;
    }
}