package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;

/**
 * @description: 日志查询条件
 * @author: swj
 * @date: 2023-11-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LogForOrderFormCriteria extends BaseCriteria implements Serializable {

    /**
     * 操作人id
     */
    private String operatorId;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 对象id
     */
    private String objectId;

    @Override
    public String getCondition() {
        Calendar calendar = Calendar.getInstance();
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.operatorId)) {
            condition.append(" and a.operatorId = :operatorId");
            values.put("operatorId", this.operatorId);
        }
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date date = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and a.operateTime >= :startTime");
            values.put("startTime", date);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date date = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            date = calendar.getTime();
            condition.append(" and a.operateTime < :endTime");
            values.put("endTime", date);
        }
        if (StringUtil.isNotEmpty(this.objectId)) {
            condition.append(" and a.objectId = :objectId");
            values.put("objectId", this.objectId);
        }
        return condition.toString();
    }
}
