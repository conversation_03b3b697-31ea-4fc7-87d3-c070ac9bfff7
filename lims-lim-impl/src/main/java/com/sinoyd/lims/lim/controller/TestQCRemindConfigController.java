package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.TestQCRemindConfigCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoTestQCRemindConfig;
import com.sinoyd.lims.lim.service.TestQCRemindConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * TestQCRemindConfig服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/10/8
 * @since V100R001
 */
 @Api(tags = "示例: TestQCRemindConfig服务")
 @RestController
 @RequestMapping("api/lim/testQCRemindConfig")
 @Validated
 public class TestQCRemindConfigController extends BaseJpaController<DtoTestQCRemindConfig, String,TestQCRemindConfigService> {


    /**
     * 分页动态条件查询TestQCRemindConfig
     * @param testQCRemindConfigCriteria 条件参数
     * @return RestResponse<List<TestQCRemindConfig>>
     */
     @ApiOperation(value = "分页动态条件查询TestQCRemindConfig", notes = "分页动态条件查询TestQCRemindConfig")
     @GetMapping
     public RestResponse<List<DtoTestQCRemindConfig>> findByPage(TestQCRemindConfigCriteria testQCRemindConfigCriteria) {
         PageBean<DtoTestQCRemindConfig> pageBean = super.getPageBean();
         RestResponse<List<DtoTestQCRemindConfig>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, testQCRemindConfigCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询TestQCRemindConfig
     * @param id 主键id
     * @return RestResponse<DtoTestQCRemindConfig>
     */
     @ApiOperation(value = "按主键查询TestQCRemindConfig", notes = "按主键查询TestQCRemindConfig")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoTestQCRemindConfig> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoTestQCRemindConfig> restResponse = new RestResponse<>();
         DtoTestQCRemindConfig testQCRemindConfig = service.findOne(id);
         restResponse.setData(testQCRemindConfig);
         restResponse.setRestStatus(StringUtil.isNull(testQCRemindConfig) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增TestQCRemindConfig
     * @param testQCRemindConfig 实体列表
     * @return RestResponse<DtoTestQCRemindConfig>
     */
     @ApiOperation(value = "新增TestQCRemindConfig", notes = "新增TestQCRemindConfig")
     @PostMapping
     public RestResponse<DtoTestQCRemindConfig> create(@Validated @RequestBody DtoTestQCRemindConfig testQCRemindConfig) {
         RestResponse<DtoTestQCRemindConfig> restResponse = new RestResponse<>();
         restResponse.setData(service.save(testQCRemindConfig));
         return restResponse;
      }

     /**
     * 新增TestQCRemindConfig
     * @param testQCRemindConfig 实体列表
     * @return RestResponse<DtoTestQCRemindConfig>
     */
     @ApiOperation(value = "修改TestQCRemindConfig", notes = "修改TestQCRemindConfig")
     @PutMapping
     public RestResponse<DtoTestQCRemindConfig> update(@Validated @RequestBody DtoTestQCRemindConfig testQCRemindConfig) {
         RestResponse<DtoTestQCRemindConfig> restResponse = new RestResponse<>();
         restResponse.setData(service.update(testQCRemindConfig));
         return restResponse;
      }

    /**
     * "根据id批量删除TestQCRemindConfig
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除TestQCRemindConfig", notes = "根据id批量删除TestQCRemindConfig")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }