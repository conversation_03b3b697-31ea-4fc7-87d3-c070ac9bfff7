package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoReportModule2GroupType;

import java.util.List;


/**
 * 报告组件与分页方式的关联关系
 *
 * <AUTHOR>
 * @version V1.0.0 2022/12/09
 * @since V100R001
 */
public interface ReportModule2GroupTypeRepository extends IBaseJpaPhysicalDeleteRepository<DtoReportModule2GroupType, String> {

    /**
     * 根据报表组件配置id获取相应的数据
     *
     * @param reportConfigModuleId 报告组件配置id
     * @return 报告组件分页方式配置列表
     */
    List<DtoReportModule2GroupType> findByReportConfigModuleId(String reportConfigModuleId);

    /**
     * 根据报表组件配置id列表获取相应的数据
     *
     * @param reportConfigModuleIdList 报告组件配置id列表
     * @return 报告组件分页方式配置列表
     */
    List<DtoReportModule2GroupType> findByReportConfigModuleIdIn(List<String> reportConfigModuleIdList);

}