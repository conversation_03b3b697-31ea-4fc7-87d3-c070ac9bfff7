package com.sinoyd.base.criteria;

import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * EvaluationCriteria查询条件
 * <AUTHOR>
 * @version V1.0.0 2019/4/15
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EvaluationCriteriaCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 标准类型
    */
    private String categoryId;

    /**
     * 标准状态
     */
    private Integer status;


    /**
     * 检测类型id（大类）
     */
    private String sampleTypeId;

    /**
     * 关键字 标准代码、标准名称
     */
    private String key;

    private List<String> ids;

    /**
     * 标准类别
     */
    private Integer criteriaType;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(this.categoryId)
                && !this.categoryId.equals(UUIDHelper.GUID_EMPTY)) {
            condition.append(" and categoryId = :categoryId");
            values.put("categoryId", this.categoryId);
        }
        if (StringUtil.isNotNull(status) && status > 0) {
            condition.append(" and status = :status");
            values.put("status", this.status);
        }
        if (StringUtils.isNotNullAndEmpty(this.sampleTypeId)
                && !this.sampleTypeId.equals(UUIDHelper.GUID_EMPTY)) {
            condition.append(" and sampleTypeId = :sampleTypeId");
            values.put("sampleTypeId", this.sampleTypeId);
        }
        if (StringUtils.isNotNullAndEmpty(this.key)) {
            condition.append(" and ( name like :key or code like :key )");
            values.put("key", "%" + this.key + "%");
        }
        if (StringUtil.isNotEmpty(this.ids)){
            condition.append(" and id in :ids");
            values.put("ids", this.ids);
        }
        if (StringUtil.isNotNull(criteriaType)) {
            condition.append(" and criteriaType = :criteriaType");
            values.put("criteriaType", this.criteriaType);
        }
        return condition.toString();
    }
}