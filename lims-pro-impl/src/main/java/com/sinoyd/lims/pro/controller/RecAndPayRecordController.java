package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.RecAndPayRecordCriteria;
import com.sinoyd.lims.pro.dto.DtoRecAndPayRecord;
import com.sinoyd.lims.pro.service.RecAndPayRecordService;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 收付款记录接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2019-05-09
 * @since V100R001
 */
@RestController
@RequestMapping("/api/pro/recAndPayRecord")
public class RecAndPayRecordController extends BaseJpaController<DtoRecAndPayRecord, String, RecAndPayRecordService> {

    /**
     * 根据id获取收付款记录
     *
     * @param id 标识
     * @return  DtoRecAndPayRecord
     */
    @ApiOperation(value = "根据id获取收付款记录", notes = "根据id获取收付款记录")
    @GetMapping("/{id}")
    public RestResponse<DtoRecAndPayRecord> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoRecAndPayRecord> restResp = new RestResponse<>();
        DtoRecAndPayRecord record = service.findOne(id);
        restResp.setData(record);
        restResp.setRestStatus(StringUtil.isNull(record) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResp;
    }

    /**
     * 分页动态条件查询收付款记录
     *
     * @param criteria 检索参数
     * @return     List<DtoRecAndPayRecord>
     */
    @ApiOperation(value = "分页动态条件查询收付款记录", notes = "分页动态条件查询收付款记录")
    @GetMapping
    public RestResponse<List<DtoRecAndPayRecord>> findByPage(RecAndPayRecordCriteria criteria) {
        RestResponse<List<DtoRecAndPayRecord>> restResp = new RestResponse<>();
        PageBean<DtoRecAndPayRecord> page = super.getPageBean();
        service.findByPage(page, criteria);
        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());
        return restResp;
    }


    /**
     * 新增收付款记录
     *
     * @param entity DtoRecAndPayRecord
     * @return DtoRecAndPayRecord
     */
    @ApiOperation(value = "新增收付款记录", notes = "新增收付款记录")
    @PostMapping
    public RestResponse<DtoRecAndPayRecord> save(@RequestBody @Validated DtoRecAndPayRecord entity) {
        RestResponse<DtoRecAndPayRecord> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.save(entity));
        return restResp;
    }

    /**
     * 修改收付款记录
     *
     * @param entity DtoRecAndPayRecord
     * @return DtoRecAndPayRecord
     */
    @ApiOperation(value = "修改收付款记录", notes = "修改收付款记录")
    @PutMapping
    public RestResponse<DtoRecAndPayRecord> update(@RequestBody @Validated DtoRecAndPayRecord entity) {
        RestResponse<DtoRecAndPayRecord> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData( service.update(entity));
        return restResp;
    }


    /**
     * 批量删除收付款记录
     *
     * @param ids 标识数组
     * @return  String
     */
    @ApiOperation(value = "批量删除收付款记录", notes = "批量删除收付款记录")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setCount(service.logicDeleteById(ids));
        return restResp;
    }

    /**
     * 计算分析图表及汇总数据
     *
     * @param criteria 检索参数
     * @return     Map<String,Object>
     */
    @ApiOperation(value = "计算分析图表及汇总数据", notes = "计算分析图表及汇总数据")
    @GetMapping("/analyzeProfileData")
    public RestResponse<Map<String,Object>> analyzeProfileData(RecAndPayRecordCriteria criteria) {
        RestResponse<Map<String,Object>> restResp = new RestResponse<>();
        PageBean<DtoRecAndPayRecord> page = super.getPageBean();
        restResp.setData(service.analyzeProfileData(page,criteria));
        return restResp;
    }
}