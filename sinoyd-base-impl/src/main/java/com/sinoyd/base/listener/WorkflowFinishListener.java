package com.sinoyd.base.listener;

import com.sinoyd.SpringContextAware;
import com.sinoyd.boot.workflow.activiti.dto.ActTaskDTO;
import com.sinoyd.boot.workflow.activiti.service.IActProcessService;
import com.sinoyd.boot.workflow.activiti.service.IActTaskService;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.math.BigDecimal;

/**
 * WorkflowFinishListener 最后一步任务自动结束的监听器
 * <AUTHOR>
 * @version V1.0.0 2019/11/01
 * @since V100R001
 */
@Slf4j
@Component
public class WorkflowFinishListener implements TaskListener {

    @Transactional
    @Override
    public void notify(DelegateTask delegateTask) {
        ActTaskDTO actTask = new ActTaskDTO();
        IActTaskService actTaskService = SpringContextAware.getBean(IActTaskService.class);
        IActProcessService actProcessService = SpringContextAware.getBean(IActProcessService.class);
        CommonRepository commonRepository = SpringContextAware.getBean(CommonRepository.class);
        ProcessDefinition processDefinition = actProcessService.getProcessDefinition(delegateTask.getProcessDefinitionId());
        ProcessInstance processInstance = actProcessService.getProcessInstanceById(delegateTask.getProcessInstanceId());
        if (StringUtils.isNotNullAndEmpty(processDefinition.getDescription())) {
            //如何修改业务表里面的状态
            try {
                Class aClass = Class.forName(processDefinition.getDescription());//用描述作为包的命名
                Field[] fields = aClass.getSuperclass().getDeclaredFields();
                Object object = aClass.newInstance();
                for (Field field : fields) {
                    // 设置些属性是可以访问的
                    field.setAccessible(true);
                    if (field.getName().equals("status")) {
                        field.set(object, delegateTask.getName());
                    }
                    if (field.getName().equals("id")) {
                        field.set(object, processInstance.getBusinessKey());
                    }
                    //防止后面三种值有默认值，默认值覆盖数据库的值
                    Object objectValue = field.get(object);
                    if (objectValue instanceof BigDecimal) {
                        field.set(object, null);
                    } else if (objectValue instanceof Boolean) {
                        field.set(object, null);
                    } else if (objectValue instanceof Integer) {
                        field.set(object, null);
                    }
                }
                BaseEntity baseEntity = (BaseEntity) object;
                commonRepository.merge(baseEntity);
            } catch (Exception ex) {
                System.out.println(ex.getMessage());
            }
        }
        delegateTask.getProcessInstanceId();
        actTask.setTaskId(delegateTask.getId());
        actTask.setProcInsId(delegateTask.getProcessInstanceId());
        //最后将流程结束掉
        actTaskService.complete(actTask, false);
    }
}
