package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.dto.lims.DtoOAInstrumentPurchaseDetail;
import com.sinoyd.lims.lim.service.OAInstrumentPurchaseDetailService;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * OAInstrumentPurchaseDetailController
 *
 * <AUTHOR>
 * @version V1.0.0 2020/08/2020/8/18
 * @since V100R001
 */
@RestController
@RequestMapping("/api/lim/OAInstrumentPurchaseDetail")
@Validated
public class OAInstrumentPurchaseDetailController extends BaseJpaController<DtoOAInstrumentPurchaseDetail,String, OAInstrumentPurchaseDetailService> {

    /**
     * 修改仪器采购申请明细的剩余入库数量
     * @param oaInstrumentPurchaseDetail 仪器采购申请明细对象
     * @return RestResponse<DtoOAInstrumentPurchaseDetail>
     */
    @ApiOperation(value = "修改仪器采购申请明细的剩余入库数量", notes = "修改仪器采购申请明细的剩余入库数量")
    @PostMapping()
    public RestResponse<DtoOAInstrumentPurchaseDetail> updateSurplusNum(@Validated @RequestBody DtoOAInstrumentPurchaseDetail oaInstrumentPurchaseDetail){
        RestResponse<DtoOAInstrumentPurchaseDetail> rest = new RestResponse<>();
        DtoOAInstrumentPurchaseDetail dtoOAInstrumentPurchaseDetail = service.updateSurplusNum(oaInstrumentPurchaseDetail);
        rest.setRestStatus(StringUtil.isNull(dtoOAInstrumentPurchaseDetail) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        rest.setData(dtoOAInstrumentPurchaseDetail);
        return rest;
    }
}
