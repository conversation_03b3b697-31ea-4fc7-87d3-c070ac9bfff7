package com.sinoyd.lims.lim.data.mapper;

import com.sinoyd.lims.lim.dto.customer.DtoExportParams;
import com.sinoyd.lims.lim.dto.rcc.DtoParams;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

import java.util.List;

/**
 * 参数实体转导入导出实体映射接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/12/7
 * @since V100R001
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface ParamsMapper {


    /**
     * DtoParams 实例转换成DtoExportParams实例
     *
     * @param params 分析项目实体
     * @return DtoExportParams 实例
     */
    DtoExportParams toExportParams(DtoParams params);

    /**
     * DtoParams 实例集合转换成DtoExportParams 实例集合
     *
     * @param paramsList 分析项目实例集合
     * @return DtoExportParams 实例集合
     */
    @InheritConfiguration(name = "toExportParams")
    List<DtoExportParams> toExportParamsList(List<DtoParams> paramsList);


    /**
     * DtoExportParams 实例转换成DtoParams 实例
     *
     * @param exportParams 分析项目实体
     * @return DtoExportParams 实例
     */
    DtoParams toDtoParams(DtoExportParams exportParams);

    /**
     * DtoExportParams 实例集合转换成DtoParams 实例集合
     *
     * @param exportParamsList 分析项目导入导出实例集合
     * @return DtoParams 实例集合
     */
    @InheritConfiguration(name = "toDtoParams")
    List<DtoParams> toDtoParamsList(List<DtoExportParams> exportParamsList);

}
