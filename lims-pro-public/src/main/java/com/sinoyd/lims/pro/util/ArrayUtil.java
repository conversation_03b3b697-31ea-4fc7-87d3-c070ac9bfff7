package com.sinoyd.lims.pro.util;

import com.sinoyd.frame.base.entity.BaseEntity;

import java.util.*;

/**
 * 数组操作工具
 * <AUTHOR>
 * @version V1.0.0 2019/11/23
 * @since V100R001
 */
public class ArrayUtil {
    /**
     * 合并有序数组 作者：LeetCode
     *
     * @param obj1 数组1(元素个数需填充到数组1的非null个数与数组2长度之和)
     * @param m        数组1中的非null元素个数
     * @param obj2 数组2
     * @param n        数组2的长度
     * @param c        排序
     */
    public static <T extends BaseEntity> void merge(T[] obj1, int m, T[] obj2, int n, Comparator<T> c) {
        // two get pointers for comment1 and comment2
        int p1 = m - 1;
        int p2 = n - 1;
        // set pointer for comment1
        int p = m + n - 1;

        // while there are still elements to compare
        while ((p1 >= 0) && (p2 >= 0)) {
            // compare two elements from comment1 and comment2
            // and add the largest one in comment1
            obj1[p--] = c.compare(obj1[p1], obj2[p2]) < 0 ? obj2[p2--] : obj1[p1--];
        }

        // add missing elements from comment2
        System.arraycopy(obj2, 0, obj1, 0, p2 + 1);;
    }

    /**
     * 获取数组中出现次数最多的元素
     *
     * @param words 字符串数组
     * @return 出现次数最多的元素
     */
    public static String topFrequent(String[] words) {
        if (words.length == 0) {
            return "";
        }
        Map<String, Integer> count = new HashMap<>();
        for (String word : words) {
            count.put(word, count.getOrDefault(word, 0) + 1);
        }
        List<String> candidates = new ArrayList<>(count.keySet());
        candidates.sort(Comparator.comparing((String a) -> count.get(a)).reversed());

        return candidates.get(0);
    }
}
