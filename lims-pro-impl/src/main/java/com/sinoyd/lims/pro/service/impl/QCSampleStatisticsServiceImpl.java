package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.factory.quality.QualityBlank;
import com.sinoyd.base.factory.quality.QualityMark;
import com.sinoyd.base.factory.quality.QualityParallel;
import com.sinoyd.base.factory.quality.QualityStandard;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoTestQCRangeResult;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.TestQCRangeService;
import com.sinoyd.lims.pro.criteria.QcSampleCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoEvaluateRow;
import com.sinoyd.lims.pro.dto.customer.DtoQCSample;
import com.sinoyd.lims.pro.dto.customer.DtoQCSampleCensus;
import com.sinoyd.lims.pro.dto.customer.DtoQCSampleDetail;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 质控数据的统计
 *
 * <AUTHOR>
 * @version V1.0.0 2020/02/11
 * @since V100R001
 */
@Service
public class QCSampleStatisticsServiceImpl implements QCSampleStatisticsService {

    private CommonRepository commonRepository;

    private SampleRepository sampleRepository;

    private SampleTypeService sampleTypeService;

    private ReceiveSampleRecordRepository receiveSampleRecordRepository;

    private AnalyseDataRepository analyseDataRepository;

    private ProService proService;

    private QualityControlRepository qualityControlRepository;

    private TestQCRangeService testQCRangeService;

    private QualityControlService qualityControlService;

    private QualityControlEvaluateRepository qualityControlEvaluateRepository;

    private QualityControlEvaluateService qualityControlEvaluateService;

    private JdbcTemplate jdbcTemplate;

    @Override
    public void findByPage(PageBean<DtoQCSample> pageBean, BaseCriteria baseCriteria) {
        pageBean.setEntityName("VQCSample a");
        pageBean.setSelect("select a");
        commonRepository.findByPage(pageBean, baseCriteria);
    }

    /**
     * 查询所有的质控样评价统计数据
     *
     * @param pageBean     分页
     * @param baseCriteria 查询条件
     */
    @Override
    public void findQcSamples(PageBean<DtoQCSampleCensus> pageBean, BaseCriteria baseCriteria) {
        QcSampleCriteria qcSampleCriteria = (QcSampleCriteria) baseCriteria;
        StringBuilder sql = new StringBuilder("select a.id,a.code,s2.code as associateCode,a.redFolderName,a.redAnalyzeItems,a.qcGrade,a.qcType,a.typeName,a.status,a.associateSampleId,a.sampleTypeId,a.samplingTimeBegin from");
        sql.append("(select s.id,s.code,s.redFolderName,s.redAnalyzeItems,qc.qcGrade,qc.qcType,st.typeName,s.status,s.associateSampleId,st.id as sampleTypeId,s.samplingTimeBegin from")
                .append(" tb_pro_sample s left join tb_pro_qualitycontrol qc on s.qcId = qc.Id,tb_base_sampletype st")
                .append(" where st.id = s.sampleTypeId and s.sampleCategory in ( ")
                .append(EnumPRO.EnumSampleCategory.质控样.getValue()).append(",")
                .append(EnumPRO.EnumSampleCategory.串联样.getValue()).append(",")
                .append(EnumPRO.EnumSampleCategory.原样加原样.getValue()).append(",")
                .append(EnumPRO.EnumSampleCategory.比对样.getValue()).append(",")
                .append(EnumPRO.EnumSampleCategory.洗涤剂.getValue()).append(",")
                .append(EnumPRO.EnumSampleCategory.比对评价样.getValue())
                .append(") and s.isDeleted = 0) a")
                .append(" left join tb_pro_sample s2 on a.associateSampleId = s2.Id ")
                .append(" where 1=1");
        String condition = qcSampleCriteria.getCondition();
        String[] values = new String[]{};
        if (StringUtil.isNotEmpty(condition)) {
            sql.append(condition);
            values = qcSampleCriteria.getConditonValue();
        }
        List<DtoQCSampleCensus> qcSamples = jdbcTemplate.query(sql.toString(), values, (resultSet, i) -> new DtoQCSampleCensus(
                resultSet.getString("id"),
                resultSet.getString("code"),
                resultSet.getString("associateCode"),
                resultSet.getString("redFolderName"),
                resultSet.getString("redAnalyzeItems"),
                resultSet.getInt("qcGrade"),
                resultSet.getInt("qcType"),
                resultSet.getString("typeName"),
                resultSet.getString("status"),
                resultSet.getString("associateSampleId"),
                resultSet.getString("sampleTypeId")
        ));
        int pageNo = pageBean.getPageNo();
        int rowNum = pageBean.getRowsPerPage();
        //处理样品编号合并以及其他数据问题
        List<DtoQCSampleCensus> resultData = this.handleQcSamples(qcSamples);
        //处理条件
        resultData = qcSampleCriteria.getListBySpecialCondition(resultData);
        //排序
        resultData.sort(Comparator.comparing(DtoQCSampleCensus::getAnalyseTime).reversed()
                .thenComparing(DtoQCSampleCensus::getQcGrade)
                .thenComparing(DtoQCSampleCensus::getQcType));
        pageBean.setRowsCount(resultData.size());
        pageBean.setData(StringUtil.isNotEmpty(resultData) ? resultData.stream().skip((long) (pageNo - 1) * rowNum).limit(rowNum).collect(Collectors.toList()) : new ArrayList<>());
    }

    /**
     * 根据样品id查询质控评价详细
     *
     * @param sampleId 样品id
     * @return 质控评价详细
     */
    @Override
    public List<List<DtoEvaluateRow>> findDetails(String sampleId) {
        List<List<DtoEvaluateRow>> expendList = new ArrayList<>();
        List<String> sampleIds = new ArrayList<>();
        //如果样品编号是合并的，处理合并后的样品id
        if (sampleId.contains(",")) {
            sampleIds.addAll(Stream.of(sampleId.split(",")).collect(Collectors.toList()));
        } else {
            sampleIds.add(sampleId);
        }
        //获取所有的样品
        List<DtoSample> samples = sampleRepository.findByIds(sampleIds);
        Map<String, List<DtoSample>> sampleGroup = samples.stream().collect(Collectors.groupingBy(DtoSample::getId));
        //根据样品获取测试项目
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdIn(sampleIds);
        Map<String, List<DtoAnalyseData>> anaDataGroup = analyseDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleId));
        //获取测试项目id
        List<String> analyseDataIds = analyseDataList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
        //根据测试项目id获取质控评价数据
        List<DtoQualityControlEvaluate> qcEvaluates = qualityControlEvaluateRepository.findByObjectIdIn(analyseDataIds);
        Map<String, List<DtoQualityControlEvaluate>> qcEvaluatesGroup = qcEvaluates.stream().collect(Collectors.groupingBy(DtoQualityControlEvaluate::getObjectId));
        //获取质控评价数据
        for (String id : sampleIds) {
            Optional<DtoSample> sampleOp = sampleGroup.get(id).stream().findFirst();
            List<DtoAnalyseData> anaDataOfSample = anaDataGroup.get(id);
            for (DtoAnalyseData dtoAnalyseData : anaDataOfSample) {
                List<DtoQualityControlEvaluate> qcEvaluate = qcEvaluatesGroup.get(dtoAnalyseData.getId());
                if (StringUtil.isNotEmpty(qcEvaluate)) {
                    Optional<DtoQualityControlEvaluate> qcEvaluateOp = qcEvaluate.stream().findFirst();
                    if (sampleOp.isPresent() && qcEvaluateOp.isPresent()) {
                        List<DtoEvaluateRow> evaluateRows = qualityControlEvaluateService.getExpendInfo(qcEvaluateOp.get(), dtoAnalyseData, sampleOp.get());
                        expendList.add(evaluateRows);
                    }
                }
            }
        }
        return expendList;
    }

    /**
     * 处理样品编号以及其他数据列
     *
     * @param qcSamples 质控样数据
     * @return 处理后数据
     */
    private List<DtoQCSampleCensus> handleQcSamples(List<DtoQCSampleCensus> qcSamples) {
        List<DtoQCSampleCensus> resultData = new ArrayList<>();
        if (StringUtil.isNotEmpty(qcSamples)) {
            List<String> qcSampleIds = qcSamples.stream().map(DtoQCSampleCensus::getId).collect(Collectors.toList());
            //获取所有的质控样测试项目数据
            List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdIn(qcSampleIds);
            Map<String, List<DtoAnalyseData>> anaDataGroupBySampleId = analyseDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleId));
            //获取所有的质控样评价数据
            List<String> anaDataIds = analyseDataList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
            List<DtoQualityControlEvaluate> qcEvaluates = qualityControlEvaluateRepository.findByObjectIdIn(anaDataIds);
            //将样品编号为空的添加到结果集合中
            resultData.addAll(qcSamples.stream().filter(p -> StringUtil.isEmpty(p.getSampleCode())).collect(Collectors.toList()));
            //过滤样品编号为空的数据并按照样品编号分组
            //处理实验室批量添加的质控样，批量添加质控样时，根据不同的因子会创建多个编号相同的质控样，此处需要合并
            Map<String, List<DtoQCSampleCensus>> qcSampleGroupByCode = qcSamples.stream().filter(p -> StringUtil.isNotEmpty(p.getSampleCode())).collect(Collectors.groupingBy(DtoQCSampleCensus::getSampleCode));
            for (String qcSampleCode : qcSampleGroupByCode.keySet()) {
                DtoQCSampleCensus qcSampleTemp = new DtoQCSampleCensus();
                //获取样品编号相同的所有的质控样
                List<DtoQCSampleCensus> qcSamplesOfCode = qcSampleGroupByCode.get(qcSampleCode);
                //合并
                if (StringUtil.isNotEmpty(qcSamplesOfCode)) {
                    Optional<DtoQCSampleCensus> qcSampleOp = qcSamplesOfCode.stream().findFirst();
                    qcSampleOp.ifPresent(p -> BeanUtils.copyProperties(p, qcSampleTemp));
                    //合并测试项目名称
                    List<String> analyseItems = qcSamplesOfCode.stream().map(DtoQCSampleCensus::getRedAnalyseItems).collect(Collectors.toList());
                    qcSampleTemp.setRedAnalyseItems(String.join(",", analyseItems));
                    //合并质控样id
                    List<String> qcSampleIdOfCode = qcSamplesOfCode.stream().map(DtoQCSampleCensus::getId).collect(Collectors.toList());
                    qcSampleTemp.setId(String.join(",", qcSampleIdOfCode));
                    //处理分析人
                    List<DtoAnalyseData> anaDataOfQc = new ArrayList<>();
                    for (String qcSampleId : qcSampleIdOfCode) {
                        if (StringUtil.isNotEmpty(anaDataGroupBySampleId)) {
                            List<DtoAnalyseData> analyseDatas = anaDataGroupBySampleId.get(qcSampleId);
                            if (StringUtil.isNotEmpty(analyseDatas)) {
                                anaDataOfQc.addAll(analyseDatas);
                            }
                        }
                    }
                    Set<String> analystNames = anaDataOfQc.stream().map(DtoAnalyseData::getAnalystName).collect(Collectors.toSet());
                    qcSampleTemp.setAnalysePerson(String.join(",", analystNames));
                    //处理分析方法
                    if (StringUtil.isNotEmpty(anaDataOfQc)) {
                        List<String> analyseMethods = anaDataOfQc.stream().map(DtoAnalyseData::getRedAnalyzeMethodName).collect(Collectors.toList());
                        qcSampleTemp.setRedAnalyseMethods(StringUtil.isNotEmpty(analyseMethods) ? String.join(",", analyseMethods) : "");
                    }
                    //处理分析日期
                    List<String> analyzeTimes = anaDataOfQc.stream().map(p -> DateUtil.dateToString(p.getAnalyzeTime(), DateUtil.YEAR)).filter(p -> !"1753-01-01".equals(p)).distinct().collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(analyzeTimes)) {
                        if (analyzeTimes.size() > 1) {
                            Map<String, Object> analyseTimes = new HashMap<>();
                            String startTime = Collections.min(analyzeTimes);
                            String endTime = Collections.max(analyzeTimes);
                            analyseTimes.put("startTime", startTime);
                            analyseTimes.put("endTime", endTime);
                            qcSampleTemp.setAnalyseTimes(analyseTimes);
                            qcSampleTemp.setAnalyseTime(startTime + "~" + endTime);
                        } else {
                            Optional<String> analyzeTime = analyzeTimes.stream().findFirst();
                            qcSampleTemp.setAnalyseTime(analyzeTime.orElse(""));
                        }
                    }
                    //处理是否合格
                    List<String> anaDataIdsOfSample = anaDataOfQc.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
                    qcSampleTemp.setIsPass(EnumLIM.EnumQcSamplePassStatus.未评价.getValue());
                    if (StringUtil.isNotEmpty(qcEvaluates) && StringUtil.isNotEmpty(anaDataIdsOfSample)) {
                        List<DtoQualityControlEvaluate> qcEvaluateOfSample = qcEvaluates.stream().filter(p -> anaDataIdsOfSample.contains(p.getObjectId())).collect(Collectors.toList());
                        if (StringUtil.isNotEmpty(qcEvaluateOfSample)) {
                            Boolean isPass = null;
                            boolean isEmpty = false;
                            for (DtoQualityControlEvaluate dtoQualityControlEvaluate : qcEvaluateOfSample) {
                                if (StringUtils.isNotNullAndEmpty(dtoQualityControlEvaluate.getIsPass())) {
                                    if (!dtoQualityControlEvaluate.getIsPass()) {
                                        isPass = false;
                                        break;
                                    } else {
                                        isPass = true;
                                    }
                                }else {
                                    isEmpty = true;
                                }
                            }
                            if (StringUtils.isNotNullAndEmpty(isPass) && !isEmpty) {
                                qcSampleTemp.setIsPass(isPass ? EnumLIM.EnumQcSamplePassStatus.是.getValue() : EnumLIM.EnumQcSamplePassStatus.否.getValue());
                            }else if (StringUtils.isNotNullAndEmpty(isPass) && isEmpty){
                                qcSampleTemp.setIsPass(isPass ? EnumLIM.EnumQcSamplePassStatus.是.getValue() : EnumLIM.EnumQcSamplePassStatus.否.getValue());
                            }else if(StringUtils.isNull(isPass) && isEmpty){
                                qcSampleTemp.setIsPass(EnumLIM.EnumQcSamplePassStatus.未评价.getValue());
                            }
                        }
                    }
                }
                resultData.add(qcSampleTemp);
            }
        }
        handleNoneQcFields(resultData);
        return resultData;
    }

    /**
     * 处理质控类型为空的数据（没有qcId的质控样， 根据sampleCategory判断）
     *
     * @param resultData 质控样列表
     */
    private void handleNoneQcFields(List<DtoQCSampleCensus> resultData) {
        Set<String> qcSampleIds = resultData.stream().map(DtoQCSampleCensus::getId).collect(Collectors.toSet());
        List<DtoSample> sampleList = StringUtil.isNotEmpty(qcSampleIds) ? sampleRepository.findByIds(qcSampleIds) : new ArrayList<>();
        Map<String, Integer> sampleCategoryMap = sampleList.stream().collect(Collectors.toMap(DtoSample::getId, DtoSample::getSampleCategory));
        for (DtoQCSampleCensus qcSampleCensus : resultData) {
            if (new Integer(0).equals(qcSampleCensus.getQcType())
                    && new Integer(0).equals(qcSampleCensus.getQcGrade())) {
                Integer sampleCategory = sampleCategoryMap.get(qcSampleCensus.getId());
                if (sampleCategory != null) {
                    EnumPRO.EnumSampleCategory categoryE = EnumPRO.EnumSampleCategory.getByValue(sampleCategory);
                    qcSampleCensus.setQcType(categoryE.getQcType());
                    qcSampleCensus.setQcGrade(categoryE.getQcGrade());
                }
            }
        }
    }

    @Override
    public List<DtoSample> findSamples(List<String> sampleIds) {
        List<DtoSample> samples = sampleRepository.findByIds(sampleIds);
        List<String> receiveIds = samples.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getReceiveId())
                && !p.getReceiveId().equals(UUIDHelper.GUID_EMPTY)).map(DtoSample::getReceiveId).distinct().collect(Collectors.toList());
        List<String> sampleTypeIds = samples.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getSampleTypeId())
                && !p.getSampleTypeId().equals(UUIDHelper.GUID_EMPTY)).map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoReceiveSampleRecord> receiveSampleRecords = new ArrayList<>();
        if (receiveIds.size() > 0) {
            receiveSampleRecords = receiveSampleRecordRepository.findAll(receiveIds);
        }
        List<DtoSampleType> sampleTypes = new ArrayList<>();
        if (sampleTypeIds.size() > 0) {
            sampleTypes = sampleTypeService.findRedisByIds(sampleTypeIds);
        }
        for (DtoSample dtoSample : samples) {
            DtoReceiveSampleRecord receiveSampleRecord = receiveSampleRecords.stream().filter(p -> p.getId().equals(dtoSample.getReceiveId())).findFirst().orElse(null);
            String senderName = "";
            if (StringUtil.isNotNull(receiveSampleRecord)) {
                senderName = receiveSampleRecord.getSenderName();
                dtoSample.setSendTime(receiveSampleRecord.getSendTime());
            }
            dtoSample.setSenderName(senderName);
            String sampleTypeName = "";
            DtoSampleType dtoSampleType = sampleTypes.stream().filter(p -> p.getId().equals(dtoSample.getSampleTypeId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(dtoSampleType)) {
                sampleTypeName = dtoSampleType.getTypeName();
            }
            dtoSample.setSampleTypeName(sampleTypeName);
        }
        return samples;
    }

    // todo 这个方法是做什么的？
    @Override
    public List<DtoQCSampleDetail> findDetails(String sampleId, String associateSampleId) {
        List<DtoQCSampleDetail> details = new ArrayList<>();
        //质控数据
        List<DtoAnalyseData> qcAnalyseDataList = analyseDataRepository.findBySampleIdAndIsDeletedFalse(sampleId);

        List<String> qcIds = qcAnalyseDataList.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getQcId())
                && !p.getQcId().equals(UUIDHelper.GUID_EMPTY)).map(DtoAnalyseData::getQcId).collect(Collectors.toList());
        //原样数据
        List<DtoAnalyseData> yyAnalyseDataList = new ArrayList<>();
        if (StringUtils.isNotNullAndEmpty(associateSampleId)
                && !associateSampleId.equals(UUIDHelper.GUID_EMPTY)) {
            yyAnalyseDataList = analyseDataRepository.findBySampleIdAndIsDeletedFalse(associateSampleId);
        }

        List<DtoQualityControl> qualityControls = new ArrayList<>();
        if (qcIds.size() > 0) {
            qualityControls = qualityControlRepository.findAll(qcIds);
        }


        for (DtoAnalyseData analyseData : qcAnalyseDataList) {
            DtoQCSampleDetail dtoQCSampleDetail = new DtoQCSampleDetail();
            dtoQCSampleDetail.setQcAnalyseDataId(analyseData.getId());
            dtoQCSampleDetail.setQcTestOrignValue(analyseData.getTestOrignValue());
            dtoQCSampleDetail.setQcTestValue(analyseData.getTestValue());
            dtoQCSampleDetail.setQcTestValueDstr(analyseData.getTestValueDstr());
            dtoQCSampleDetail.setReaAnalyzeItemName(analyseData.getRedAnalyzeItemName());
            String deviationResult = ""; //偏差值
            String deviationRate = ""; //偏差率
            String deviationType = "";// 偏差类型
            String deviationRange = "";// 偏差范围
            String deviationRangeLimit = "";//偏差范围的要求
            Boolean isPass = null;
            DtoAnalyseData yyAnalyseData = yyAnalyseDataList.stream().filter(p -> p.getTestId().equals(analyseData.getTestId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(yyAnalyseData)) {
                dtoQCSampleDetail.setAssAnalyseDataId(yyAnalyseData.getId());
                dtoQCSampleDetail.setAssTestOrignValue(yyAnalyseData.getTestOrignValue());
                dtoQCSampleDetail.setAssTestValue(yyAnalyseData.getTestValue());
                dtoQCSampleDetail.setAssTestValueDstr(yyAnalyseData.getTestValueDstr());
                dtoQCSampleDetail.setAssAnalystName(yyAnalyseData.getAnalystName());
                dtoQCSampleDetail.setAssDimensionName(yyAnalyseData.getDimension());
                dtoQCSampleDetail.setAssAnalyzeTime(DateUtil.dateToString(yyAnalyseData.getAnalyzeTime(), DateUtil.YEAR));
            }
            DtoQualityControl qualityControl = qualityControls.stream().filter(p -> p.getId().equals(analyseData.getQcId())).findFirst().orElse(null);
            //#region 空白
            if (analyseData.getQcType().equals(new QualityBlank().qcTypeValue())
                    && analyseData.getQcGrade().equals(EnumLIM.EnumQCGrade.外部质控.getValue())) {
                isPass = proService.isExamValue(analyseData.getTestValueDstr(), analyseData.getExamLimitValue());
            }
            //#endregion
            //#region 标准
            else if (analyseData.getQcType().equals(new QualityStandard().qcTypeValue())) {
                if (StringUtil.isNotNull(qualityControl)) {
                    String qcValue = qualityControl.getQcValue();//标准值
                    String[] byArray = qcValue.split("±"); //含标准值及标准范围的数据
                    String standardValue = "";//标准值
                    String standardRange = "";//范围
                    if (byArray.length >= 1) {
                        standardValue = byArray[0];
                    }
                    if (byArray.length >= 2) {
                        standardRange = byArray[1];
                    }
                    if (StringUtils.isNotNullAndEmpty(standardValue)) {
                        try {
                            BigDecimal deviation = analyseData.getTestValueD().subtract(new BigDecimal(standardValue)); //偏差值
                            deviationResult = deviation.toString();
                            //数据不为0
                            if (analyseData.getTestValueD().compareTo(new BigDecimal(0)) != 0) {
                                BigDecimal deviationRateDecimal = deviation.divide(analyseData.getTestValueD()).multiply(new BigDecimal(100));
                                deviationRate = String.format("%.2f", deviationRateDecimal) + "%";

                                if (StringUtils.isNotNullAndEmpty(standardRange)) {
                                    //偏差大于范围的绝对值标识不合格
                                    if (deviation.compareTo(new BigDecimal(standardRange).abs()) >= 1) {
                                        isPass = false;
                                    }
                                }
                            }
                        } catch (Exception ex) {
                            System.out.println(ex.getMessage());
                        }
                    }
                    dtoQCSampleDetail.setStandardValue(qcValue);
                    dtoQCSampleDetail.setQcCode(qualityControl.getQcCode());
                }
            }
            //#endregion
            //#region 平行
            else if (analyseData.getQcType().equals(new QualityParallel().qcTypeValue())) {
                if (StringUtil.isNotNull(yyAnalyseData)) {
                    //偏差值
                    BigDecimal deviation = yyAnalyseData.getTestValueD().subtract(analyseData.getTestValueD());
                    deviationResult = deviation.toString();
                    //平均值
                    BigDecimal avg = (analyseData.getTestValueD().add(yyAnalyseData.getTestValueD())).divide(new BigDecimal(2));

                    DtoTestQCRangeResult qcRangeResult = testQCRangeService.qcRangeIsPass(analyseData.getTestId(), new QualityParallel().qcTypeValue(), avg, yyAnalyseData.getTestValueD(), analyseData.getTestValueD());


                    deviationRangeLimit = qcRangeResult.getRangeLimit();

                    deviationRange = qcRangeResult.getRangeConfig();

                    //得到偏差类型
                    Integer qcType = qcRangeResult.getRangeType();

                    deviationType = StringUtils.isNotNullAndEmpty(EnumLIM.EnumQCRangeType.getName(qcType)) ?
                            EnumLIM.EnumQCRangeType.getName(qcType) : "未配置";

                    if (StringUtils.isNotNullAndEmpty(deviationRange)) {
                        isPass = qcRangeResult.getIsPass();
                    }
                    BigDecimal rate = qcRangeResult.getRate();
                    if (StringUtil.isNotNull(rate)) {
                        deviationRate = String.format("%.2f", rate) + "%";
                    }
                }
            }
            //#endregion
            //#region
            else if (analyseData.getQcType().equals(new QualityMark().qcTypeValue())) {
                if (StringUtil.isNotNull(qualityControl)) {
                    String qcValue = qualityControl.getQcValue();//加入量
                    String qcTestValue = qualityControl.getQcTestValue();//测定值
                    String realSampleTestValue = qualityControl.getRealSampleTestValue();//样值
                    String qcVolume = qualityControl.getQcVolume(); //加入体积
                    Map<String, Object> map = qualityControlService.calculateJBValue(realSampleTestValue, qcTestValue, qcValue, "", UUIDHelper.GUID_EMPTY);
                    //增值
                    String qcAddValue = (String) map.get("qcAddedValue");
                    //加标回收率
                    deviationRate = (String) map.get("qcRecoverRate");
                    try {
                        DtoTestQCRangeResult qcRangeResult = testQCRangeService.calculateJBPass(new BigDecimal(qualityControl.getQcOriginValue()), new BigDecimal(deviationRate), analyseData.getTestId());


                        deviationRangeLimit = qcRangeResult.getRangeLimit();

                        deviationRange = qcRangeResult.getRangeConfig();

                        if (StringUtils.isNotNullAndEmpty(deviationRange)) {
                            isPass = qcRangeResult.getIsPass();
                        }

                    } catch (Exception ex) {
                        System.out.println(ex.getMessage());
                    }
                    dtoQCSampleDetail.setQcValue(qcValue);
                    dtoQCSampleDetail.setQcJBTestValue(qcTestValue);
                    dtoQCSampleDetail.setRealSampleTestValue(realSampleTestValue);
                    dtoQCSampleDetail.setQcVolume(qcVolume);
                    dtoQCSampleDetail.setQcAddValue(qcAddValue);
                }
            }
            //#endregion
            dtoQCSampleDetail.setDeviationType(deviationType);
            dtoQCSampleDetail.setDeviation(deviationResult);
            dtoQCSampleDetail.setDeviationRate(deviationRate);
            dtoQCSampleDetail.setRangeLimit(deviationRangeLimit);
            dtoQCSampleDetail.setRangeConfig(deviationRange);
            dtoQCSampleDetail.setQcAnalystName(analyseData.getAnalystName());
            dtoQCSampleDetail.setQcDimensionName(analyseData.getDimension());
            dtoQCSampleDetail.setQcAnalyzeTime(DateUtil.dateToString(analyseData.getAnalyzeTime(), DateUtil.YEAR));
            dtoQCSampleDetail.setIsPass(isPass);
            details.add(dtoQCSampleDetail);
        }
        return details;
    }

    @Autowired
    public void setCommonRepository(CommonRepository commonRepository) {
        this.commonRepository = commonRepository;
    }

    @Autowired
    public void setSampleRepository(SampleRepository sampleRepository) {
        this.sampleRepository = sampleRepository;
    }

    @Autowired
    @Lazy
    public void setSampleTypeService(SampleTypeService sampleTypeService) {
        this.sampleTypeService = sampleTypeService;
    }

    @Autowired
    public void setReceiveSampleRecordRepository(ReceiveSampleRecordRepository receiveSampleRecordRepository) {
        this.receiveSampleRecordRepository = receiveSampleRecordRepository;
    }

    @Autowired
    public void setAnalyseDataRepository(AnalyseDataRepository analyseDataRepository) {
        this.analyseDataRepository = analyseDataRepository;
    }

    @Autowired
    @Lazy
    public void setProService(ProService proService) {
        this.proService = proService;
    }

    @Autowired
    public void setQualityControlRepository(QualityControlRepository qualityControlRepository) {
        this.qualityControlRepository = qualityControlRepository;
    }

    @Autowired
    @Lazy
    public void setTestQCRangeService(TestQCRangeService testQCRangeService) {
        this.testQCRangeService = testQCRangeService;
    }

    @Autowired
    @Lazy
    public void setQualityControlService(QualityControlService qualityControlService) {
        this.qualityControlService = qualityControlService;
    }

    @Autowired
    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    @Autowired
    public void setQualityControlEvaluateRepository(QualityControlEvaluateRepository qualityControlEvaluateRepository) {
        this.qualityControlEvaluateRepository = qualityControlEvaluateRepository;
    }

    @Autowired
    public void setQualityControlEvaluateService(QualityControlEvaluateService qualityControlEvaluateService) {
        this.qualityControlEvaluateService = qualityControlEvaluateService;
    }
}
