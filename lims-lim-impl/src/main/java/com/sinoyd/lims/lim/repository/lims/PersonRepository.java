package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * 人员管理仓储
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/5
 * @since V100R001
 */
public interface PersonRepository extends IBaseJpaRepository<DtoPerson, String> {


    /**
     * 更新时判断是否存在同编号
     *
     * @param userNo 人员编号
     * @param id     要更新的分析方法的id
     * @return 相同名称的分析方法数量
     */
    @Query("select count(p.id) from DtoPerson p where p.isDeleted = 0 and p.userNo = :userNo and p.id <> :id")
    Integer getCountByUserNoAndId(@Param("userNo") String userNo, @Param("id") String id);


    /**
     * 根据Id及状态过滤相应的人员信息
     *
     * @param ids    人员的ids
     * @param status 状态
     * @return 返回想要的人员信息
     */
    @Query("select p from DtoPerson p where p.isDeleted = 0 and p.id in :ids and p.status in :status order by p.cName")
    List<DtoPerson> findPersonByStatusAndId(@Param("ids") List<String> ids, @Param("status") List<Integer> status);

    /**
     * 根据状态过滤想要的人员信息
     *
     * @param status 状态信息
     * @return 返回想要的人员信息
     */
    @Query("select p from DtoPerson p where p.isDeleted = 0 and p.status in :status order by p.cName")
    List<DtoPerson> findPersonByStatus(@Param("status") List<Integer> status);

    /**
     * 根据状态过滤想要的人员信息
     *
     * @param names 状态信息
     * @return 返回想要的人员信息
     */
    @Query("select p from DtoPerson p where p.isDeleted = 0 and p.cName in :cName order by p.cName")
    List<DtoPerson> findPersonByCName(@Param("cName") List<String> names);


    /**
     * 重写获取所有的方法
     *
     * @return 返回未删除的人员信息
     */
    @Override
    @Query("select p from DtoPerson p where p.isDeleted = 0")
    List<DtoPerson> findAll();

    /**
     * 重写获取所有的方法
     *
     * @param ids 人员ids
     * @return 返回未删除的人员信息
     */
    @Override
    @Query("select p from DtoPerson p where p.isDeleted = 0 and p.id in :ids")
    List<DtoPerson> findAll(@Param("ids") Iterable<String> ids);

    /**
     * 返回所有的带假删的人员信息
     *
     * @return 返回带删除的人员信息
     */
    @Query("select p from DtoPerson p")
    List<DtoPerson> findAllDeleted();

    /**
     * 返回所以的带删除的人员信息
     *
     * @param ids 人员的ids
     * @return 返回带删除的人员信息
     */
    @Query("select p from DtoPerson p where p.id in :ids")
    List<DtoPerson> findAllDeleted(@Param("ids") List<String> ids);


    /**
     * 根据人员id查询姓名
     *
     * @param id 人员id
     * @return 返回姓名
     */
    @Query("select p.cName from DtoPerson p where p.id=:id")
    String findPersonNameById(@Param("id") String id);

    /**
     * 查询姓名存在,拼音不存在的人员
     *
     * @return
     */
    @Query("select p from DtoPerson p where (p.fullPinYin is null or p.fullPinYin = '') and p.cName is not null and p.isDeleted = 0")
    List<DtoPerson> findByPinYinIsNotExit();

    /**
     * 根据id集合获取用户
     *
     * @param ids id集合
     * @return 返回查询结果集
     */
    List<DtoPerson> findByIdIn(List<String> ids);

    /**
     * 根据姓名获取id
     * @return
     */
    @Query("select p.id from DtoPerson p where p.isDeleted = 0 and p.cName = :cName")
    String findPersonIdByCName(@Param("cName") String name);
}