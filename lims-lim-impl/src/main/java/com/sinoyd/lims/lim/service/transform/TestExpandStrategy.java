package com.sinoyd.lims.lim.service.transform;


import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.data.mapper.TestExpandMapper;
import com.sinoyd.lims.lim.dto.customer.*;
import com.sinoyd.lims.lim.dto.lims.DtoTestExpand;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.TestExpandRepository;
import com.sinoyd.lims.lim.verify.TransformTestExpandVerifyHandler;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 测试项目数据迁移测试项目拓展导入策略类
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/9/25
 */
@Component
@Order(70)
public class TestExpandStrategy implements TransformImportStrategy {
    /**
     * sheet页序号
     */
    private static final int sheetIndex = 3;

    private TestExpandRepository testExpandRepository;

    private TransformTestExpandVerifyHandler transformTestExpandVerifyHandler;

    private TestExpandMapper testExpandMapper;

    @Override
    @Transactional
    public void importSheetData(InputStream inputStream, HttpServletResponse response, ImportParams params) throws Exception {
        //参数初始化
        handleInit();
        params.setStartSheetIndex(sheetIndex);
        params.setVerifyHandler(transformTestExpandVerifyHandler);
        //获取校验结果集
        ExcelImportResult<DtoExportTestExpand> result = ExcelImportUtil.importExcelMore(inputStream, DtoExportTestExpand.class, params);
        // 检验失败抛出异常并返回错误文档
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(sheetIndex, "导入错误信息");
            PoiExcelUtils.downLoadExcel("测试项目导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        //构建可保存数据
        List<DtoTestExpand> waitSaveList = new ArrayList<>();
        buildRightData(result, waitSaveList);
        //数据保存
        testExpandRepository.save(waitSaveList);
    }

    @Override
    public void getAddData(Map<String, DtoBaseData> substituteMap, Map<String, DtoBaseData> sampleTypeBindMap, DtoDataSyncParams dtoDataSyncParams, DtoTestDependentData testDependentData, DtoImportTestTemp importTestTemp, DtoTestDependentData exportData) {
        List<DtoExportTestExpand> testExpandList = testDependentData.getTestExpandList();
        if (StringUtil.isNotEmpty(testExpandList)) {

            // 导出数据
            List<DtoExportTest> testList = exportData.getTestList();
            List<String> exportTestIds = testList.stream().map(DtoExportTest::getId).collect(Collectors.toList());

            // 导入数据,处理检查后的量纲和绑定新的检测类型
            List<DtoImportCheck> importChecks = dtoDataSyncParams.getImportChecks();
            List<DtoDataCheck> dimensionCheck = importChecks.stream().filter(p -> p.getCheckItem().equals(EnumLIM.EnumImportTestType.量纲表.getCheckItem()))
                    .flatMap(p -> p.getDataChecks().stream()).collect(Collectors.toList());
            Map<String, DtoDataCheck> dimensionCheckMap = dimensionCheck.stream().collect(Collectors.toMap(DtoDataCheck::getName, p -> p));
            List<DtoTestExpand> importTestExpands = testExpandList.stream()
                    .filter(p -> !exportTestIds.contains(p.getTestId()))
                    .map(p -> {
                        DtoTestExpand testExpand = testExpandMapper.toDtoTestExpand(p);
                        DtoDataCheck orDefault = dimensionCheckMap.getOrDefault(p.getDimensionId(), new DtoDataCheck());
                        testExpand.setDimensionId(orDefault.getId());
                        DtoBaseData baseData = sampleTypeBindMap.getOrDefault(testExpand.getSampleTypeId(), new DtoBaseData());
                        testExpand.setSampleTypeId(baseData.getTargetId());
                        return testExpand;
                    }).collect(Collectors.toList());
            importTestTemp.setTestExpandTemps(importTestExpands);
            // 导出数据处理
            if (StringUtil.isNotEmpty(exportTestIds)) {
                // 过滤出需要导出的测试项目拓展数据
                List<DtoExportTestExpand> exportTestExpands = testExpandList.stream().filter(p -> exportTestIds.contains(p.getTestId())).collect(Collectors.toList());
                exportData.setTestExpandList(exportTestExpands);

                // 筛选导出数据中的量纲和参数
                List<String> dimensionIds = exportTestExpands.stream().map(DtoExportTestExpand::getDimensionId).distinct().collect(Collectors.toList());
                List<DtoExportDimension> dimensionList = exportData.getDimensionList();
                // 去除待导入的；量纲和参数
                importTestTemp.getDimensionTemps().removeIf(p -> dimensionIds.contains(p.getId()));
                List<DtoExportDimension> exportDimensions = testDependentData.getDimensionList().stream().filter(p -> dimensionIds.contains(p.getId())).collect(Collectors.toList());
                dimensionList.addAll(exportDimensions);
                exportData.setDimensionList(dimensionList.stream().distinct().collect(Collectors.toList()));
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importData(DtoImportTestTemp importTestTemp, WebSocketServer webSocketServer) {
        List<DtoTestExpand> testExpandTemps = importTestTemp.getTestExpandTemps();
        if (StringUtil.isNotEmpty(testExpandTemps)) {
            //已同步记录数
            int i = 0;
            for (DtoTestExpand testExpand : testExpandTemps) {
                testExpandRepository.save(testExpand);
                webSocketServer.sendMessage(getMessage(testExpandTemps.size(), ++i));
            }
        } else {
            webSocketServer.sendMessage(getMessage(0, 0));
        }
    }

    @Override
    public int getAddDataOrderNum() {
        return EnumLIM.EnumImportTestType.测试拓展表.getValue();
    }

    @Override
    public String getTableName() {
        return EnumLIM.EnumImportTestType.测试拓展表.getSource();
    }


    @Override
    public String getTableRemark() {
        return EnumLIM.EnumImportTestType.测试拓展表.name();
    }

    @Override
    public List<DtoImportCheck> check(DtoDataSyncParams dtoDataSyncParams, DtoTestDependentData testDependentData) {
        return null;
    }

    /**
     * 校验容器初始化
     */
    private void handleInit() {
        transformTestExpandVerifyHandler = new TransformTestExpandVerifyHandler();
        transformTestExpandVerifyHandler.setRepoDataList(testExpandRepository.findAll());
        transformTestExpandVerifyHandler.setSheetExistDataList(new ArrayList<>());
    }

    /**
     * 构建可保存数据
     *
     * @param result       导入结果集
     * @param waitSaveList 待保存结果容器
     */
    private void buildRightData(ExcelImportResult<DtoExportTestExpand> result, List<DtoTestExpand> waitSaveList) {
        List<DtoExportTestExpand> importList = result.getList();
        //跳过空行
        importList.removeIf(p -> StringUtil.isEmpty(p.getId()));
        for (DtoExportTestExpand exportTestExpand : importList) {
            DtoTestExpand dtoTestExpand = new DtoTestExpand();
            BeanUtils.copyProperties(exportTestExpand, dtoTestExpand);
            waitSaveList.add(dtoTestExpand);
        }
    }

    @Autowired
    public void setTestExpandRepository(TestExpandRepository testExpandRepository) {
        this.testExpandRepository = testExpandRepository;
    }

    @Autowired
    public void setTestExpandMapper(TestExpandMapper testExpandMapper) {
        this.testExpandMapper = testExpandMapper;
    }
}
