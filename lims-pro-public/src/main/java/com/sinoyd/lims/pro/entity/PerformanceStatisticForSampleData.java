package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;


/**
 * PerformanceStatisticForSampleData实体
 * <AUTHOR>
 * @version V1.0.0 2020/2/21
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="PerformanceStatisticForSampleData")
 @Data
 public  class PerformanceStatisticForSampleData implements BaseEntity,Serializable {

    private static final long serialVersionUID = 1L;

    public PerformanceStatisticForSampleData() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 送样单id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("送样单id")
    private String receiveId;

    /**
     * 项目id
     */
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("项目id")
    private String projectId;

    /**
     * 送样单号
     */
    @Column(length = 20)
    @ApiModelProperty("送样单号")
    private String recordCode;

    /**
     * 送样登记人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("采样人员id")
    private String samplingPersonId;

    /**
     * 送样人
     */
    @Column(length = 100)
    @ApiModelProperty("送样人")
    private String senderName;

    /**
     * 分析日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("送样日期")
    private Date sendTime;


    /**
     * 采样日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("采样日期")
    private Date samplingTime;

    /**
     * 检测类型id
     */
    @Column(length = 50)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("检测类型id")
    private String sampleTypeId;

    /**
     * 样品数
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("样品数")
    private Integer sample = 0;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

}