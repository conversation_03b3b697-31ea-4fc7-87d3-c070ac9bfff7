package com.sinoyd.base.configuration;

import org.owasp.esapi.reference.DefaultSecurityConfiguration;
import java.io.InputStream;

public class ESAPIConfiguration extends DefaultSecurityConfiguration {

    @Override
    public InputStream getResourceStream(String filename) {
        // 通过类加载器读取resource目录下的配置文件，这个filename就是ESAPI.properties
        return ClassLoader.getSystemResourceAsStream(filename);
    }
}
