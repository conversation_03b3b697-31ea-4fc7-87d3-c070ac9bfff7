package com.sinoyd.lims.monitor.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.monitor.dto.lims.DtoOicInformation;

import java.util.List;


/**
 * OicInformation数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
public interface OicInformationRepository extends IBaseJpaPhysicalDeleteRepository<DtoOicInformation, String> {

    /**
     * 通过点位id列表获取
     * @param fixedPointIdList 点位id列表
     * @return 在线仪器数据集合
     */
    List<DtoOicInformation> findByFixedPointIdIn(List<String> fixedPointIdList);


    /**
     * 根据点位id查询在线仪器
     * @param fixedPointId 点位id
     * @return 在线仪器列表
     */
    List<DtoOicInformation> findByFixedPointId(String fixedPointId);

}