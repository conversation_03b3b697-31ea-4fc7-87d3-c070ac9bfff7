CREATE  TRIGGER "BEFORE_DELETE_RECORDCON<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>NF<PERSON>"
    BEFORE  DELETE
    ON "TB_LIM_RECORDCONFIG2PARAMSCONFIG"
    referencing OLD ROW AS "OLD" NEW ROW AS "NEW"

 for each row

BEGIN

INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_recordconfig2paramsconfig', old.id, '00000000-0000-0000-0000-000000000000', now(), 2, null, null, null,
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000');
End;
