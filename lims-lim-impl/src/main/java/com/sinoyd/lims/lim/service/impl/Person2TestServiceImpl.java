package com.sinoyd.lims.lim.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoPerson2Test;
import com.sinoyd.lims.lim.dto.customer.DtoPerson2TestChange;
import com.sinoyd.lims.lim.entity.Person2Test;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.Person2TestRepository;
import com.sinoyd.lims.lim.service.Person2TestService;
import com.sinoyd.lims.lim.service.PersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.sinoyd.lims.lim.enums.EnumLIM.EnumLIMRedis;
import static com.sinoyd.lims.lim.enums.EnumLIM.EnumLIMRedisChannel;

/**
 * 测试人员配置
 * <AUTHOR>
 * @version V1.0.0 2019/2/1
 * @since V100R001
 */
@Service
public class Person2TestServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoPerson2Test, String, Person2TestRepository>
        implements Person2TestService {

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    @Autowired
    @Lazy
    private PersonService personService;

    @Autowired
    private RedisTemplate redisTemplate;

    // 查询可测试人员
    @Override
    public List<DtoPerson2Test> getTestPersons(String testId, String sampleTypeId) {
        List<DtoPerson2Test> person2Tests = repository.findByTestIdAndSampleTypeId(testId, sampleTypeId);
        return person2Tests;
    }

    @Override
    public List<DtoPerson2Test> findByTestIds(List<String> testIds) {
        if (StringUtil.isNotNull(testIds) && testIds.size() > 0) {
            List<DtoPerson2Test> person2Tests = repository.findByTestIdIn(testIds);
            // 赋值人员信息
            List<String> personIds = person2Tests.parallelStream().map(DtoPerson2Test::getPersonId).distinct().collect(Collectors.toList());
            List<DtoPerson> personList = personIds.size() > 0 ? personService.findAllDeleted(personIds) : new ArrayList<>();
            for (DtoPerson2Test p2t : person2Tests) {
                DtoPerson person = personList.parallelStream().filter(p -> p.getId().equals(p2t.getPersonId())).findFirst().orElse(null);
                if (StringUtil.isNotNull(person)) {
                    p2t.setCName(person.getCName());
                    p2t.setDomainId(person.getDomainId());
                }
            }
            return person2Tests;
        }
        return new ArrayList<>();
    }

    // 删除测试人员配置
    @Transactional
    @Override
    public Boolean delete(String testId, String sampleTypeId) {
        Boolean flag = false;
        if (repository.deleteByTestIdAndSampleTypeId(testId, sampleTypeId) > 0) {
            List<DtoPerson2Test> person2Tests = deleteRedis(testId, sampleTypeId);
            saveRedis(person2Tests, testId);
            flag = true;
        }
        return flag;
    }

    @Transactional
    @Override
    public List<DtoPerson2Test> save(List<DtoPerson2Test> list) {
        List<DtoPerson2Test> newList = new ArrayList<>();
        if (list.size() > 0) {
            String testId = list.get(0).getTestId();
            String sampleTypeId = list.get(0).getSampleTypeId();

            String oldPersonId = UUIDHelper.GUID_EMPTY;
            String newPersonId = UUIDHelper.GUID_EMPTY;

            //获取原来的第一负责人
            List<DtoPerson2Test> oldList = repository.findByTestIdAndSampleTypeId(testId, sampleTypeId);
            if (oldList.size() > 0) {
                oldList = oldList.stream().filter(Person2Test::getIsDefaultPerson).collect(Collectors.toList());
                if (oldList.size() > 0) {
                    oldPersonId = oldList.get(0).getPersonId();
                }
            }

            //先删除相关配置
            repository.deleteByTestIdAndSampleTypeId(testId, sampleTypeId);

            //重新保存最新配置
            int orderNum = 100;
            for (DtoPerson2Test item : list) {
                if (item.getIsDefaultPerson()) {
                    item.setOrderNum(100);
                    newPersonId = item.getPersonId();
                } else {
                    item.setOrderNum(--orderNum);
                }
                if (StringUtil.isNull(item.getSampleTypeId())) {
                    item.setSampleTypeId(UUIDHelper.GUID_EMPTY);
                }
                newList.add(super.save(item));

            }

            //当第一负责人发生变化时，修改未填写数据的所有相关第一负责人
            if (!oldPersonId.equals(UUIDHelper.GUID_EMPTY)
                    && !newPersonId.equals(UUIDHelper.GUID_EMPTY)
                    && !oldPersonId.equals(newPersonId)) {
                List<DtoPerson2TestChange> person2TestChanges = new ArrayList<>();
                List<String> sampleTypes;
                DtoSampleType dtoSampleType = sampleTypeService.findOne(sampleTypeId);
                if (StringUtil.isNotNull(dtoSampleType)) {
                    if (dtoSampleType.getCategory().equals(EnumBase.EnumSampleTypeCategory.检测类型大类.getValue())) {
                        //找出哪些样品类型相关的测试项目需要调整
                        List<String> notInIds = new ArrayList<>();
                        List<DtoPerson2Test> tList = repository.findByTestId(testId);
                        tList = tList.stream().filter(p -> !p.getSampleTypeId().equals(UUIDHelper.GUID_EMPTY)).collect(Collectors.toList());
                        if (tList.size() > 0) {
                            notInIds = tList.stream().map(DtoPerson2Test::getSampleTypeId).distinct().collect(Collectors.toList());
                        }
                        final List<String> ids = notInIds;
                        List<DtoSampleType> sList = sampleTypeService.findAll().stream().filter(p -> !p.getIsDeleted() && !ids.contains(p.getId())
                                && p.getCategory().equals(EnumBase.EnumSampleTypeCategory.检测类型小类.getValue())).collect(Collectors.toList());
                        if (sList.size() > 0) {
                            sampleTypes = sList.stream().map(DtoSampleType::getId).distinct().collect(Collectors.toList());
                            person2TestChanges.add(setPerson2TestChange(testId, sampleTypes, newPersonId));
                        }
                    }
                } else {
                    List<String> sampleTypeIds = new ArrayList<>();
                    sampleTypeIds.add(sampleTypeId);
                    person2TestChanges.add(setPerson2TestChange(testId, sampleTypeIds, newPersonId));
                }
                if (person2TestChanges.size() > 0) {
                    //通知业务流程变动负责人
                    redisTemplate.convertAndSend(EnumLIMRedisChannel.LIM_Person2Test_Save.name(), JsonStream.serialize(person2TestChanges));
                }
            }
            saveRedis(newList, testId);
        }
        return newList;
    }

    @Transactional
    @Override
    public List<DtoPerson2Test> save(List<DtoPerson2Test> dtoPerson2Tests, List<String> testIds) {
        List<DtoPerson2Test> person2Tests = new ArrayList<>();
        if (testIds.size() > 0 && dtoPerson2Tests.size() > 0) {

            //模版数据
            DtoPerson2Test person2TestTemplate = dtoPerson2Tests.stream().filter(Person2Test::getIsDefaultPerson).findFirst().orElse(null);

            String sampleTypeId = dtoPerson2Tests.get(0).getSampleTypeId();

            //变动的测试项目
            List<String> changeTestIds = new ArrayList<>();

            String newPersonId = UUIDHelper.GUID_EMPTY;

            //新的默认人员
            if (StringUtil.isNotNull(person2TestTemplate)) {
                newPersonId = person2TestTemplate.getPersonId();
            }
            List<DtoPerson2Test> oldList = repository.findByTestIdInAndSampleTypeId(testIds, sampleTypeId);

            for (String testId : testIds) {
                if (oldList.size() > 0) {
                    DtoPerson2Test oldPerson2Test = oldList.stream().filter(p -> p.getTestId().equals(testId) && p.getIsDefaultPerson()).findFirst().orElse(null);
                    if (StringUtil.isNotNull(oldPerson2Test)) {
                        String oldPersonId = oldPerson2Test.getPersonId();
                        //说明有变动
                        if (!oldPersonId.equals(UUIDHelper.GUID_EMPTY)
                                && !newPersonId.equals(UUIDHelper.GUID_EMPTY)
                                && !oldPersonId.equals(newPersonId)) {
                            changeTestIds.add(testId);
                        }
                    }
                }
                List<DtoPerson2Test> newList = new ArrayList<>();
                for (DtoPerson2Test person2Test : dtoPerson2Tests) {
                    DtoPerson2Test item = new DtoPerson2Test();
                    item.setTestId(testId);
                    item.setIsDefaultPerson(person2Test.getIsDefaultPerson());
                    item.setIsDefaultAuditPerson(person2Test.getIsDefaultAuditPerson());
                    item.setOrderNum(person2Test.getOrderNum());
                    item.setPersonId(person2Test.getPersonId());
                    if (StringUtil.isNull(person2Test.getSampleTypeId())) {
                        item.setSampleTypeId(UUIDHelper.GUID_EMPTY);
                    } else {
                        item.setSampleTypeId(person2Test.getSampleTypeId());
                    }
                    newList.add(item);
                }
                person2Tests.addAll(newList);
                saveRedis(newList, testId);
            }

            //先清除该检测类型相关的测试下的人员配置
            repository.deleteByTestIdInAndSampleTypeId(testIds, sampleTypeId);

            //重新保存最新配置
            repository.save(person2Tests);

            //说明有变动的测试项目id
            if (changeTestIds.size() > 0) {
                List<DtoPerson2TestChange> person2TestChanges = new ArrayList<>();
                DtoSampleType dtoSampleType = sampleTypeService.findOne(sampleTypeId);
                if (StringUtil.isNotNull(dtoSampleType)) {
                    if (dtoSampleType.getCategory().equals(EnumBase.EnumSampleTypeCategory.检测类型大类.getValue())) {
                        List<DtoPerson2Test> tAllList = repository.findByTestIdIn(changeTestIds);
                        List<DtoSampleType> sAllList = sampleTypeService.findAll();
                        for (String testId : changeTestIds) {
                            List<String> sampleTypes;
                            List<DtoPerson2Test> tList = tAllList.stream().filter(p -> p.getTestId().equals(testId)
                                    && !p.getSampleTypeId().equals(UUIDHelper.GUID_EMPTY)).collect(Collectors.toList());
                            List<String> notInIds = tList.stream().map(DtoPerson2Test::getSampleTypeId).distinct().collect(Collectors.toList());
                            List<DtoSampleType> sList = sAllList.stream().filter(p -> !p.getIsDeleted() && !notInIds.contains(p.getId())
                                    && p.getCategory().equals(EnumBase.EnumSampleTypeCategory.检测类型小类.getValue())).collect(Collectors.toList());
                            if (sList.size() > 0) {
                                sampleTypes = sList.stream().map(DtoSampleType::getId).distinct().collect(Collectors.toList());
                                person2TestChanges.add(setPerson2TestChange(testId, sampleTypes, newPersonId));
                            }
                        }
                    }
                } else {
                    for (String testId : changeTestIds) {
                        List<String> sampleTypeIds = new ArrayList<>();
                        sampleTypeIds.add(sampleTypeId);
                        person2TestChanges.add(setPerson2TestChange(testId, sampleTypeIds, newPersonId));
                    }
                }
                if (person2TestChanges.size() > 0) {
                    //通知业务流程变动负责人
                    redisTemplate.convertAndSend(EnumLIMRedisChannel.LIM_Person2Test_Save.name(), JsonStream.serialize(person2TestChanges));
                }
            }
        }
        return person2Tests;
    }

    @Override
    public List<DtoPerson2Test> findRedisByTestIds(List<String> testIds) {
        String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIMRedis.LIM_OrgId_Person2Test.getValue());
        List<Object> dataList = redisTemplate.opsForHash().multiGet(key, testIds);
        return setMaps(dataList,testIds);
    }

    @Override
    public  List<String> findSampleTypeIdByTestId(String testId) {
        return repository.findSampleTypeIdByTestId(testId);
    }

    @Override
    public List<DtoPerson2Test> findRedisByTestId(String testId) {
        String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_Person2Test.getValue());
        Object json = redisTemplate.opsForHash().get(key, testId);
        if (StringUtils.isNotNullAndEmpty(json)) {
            //解析相应的配置数据
            TypeLiteral<List<DtoPerson2Test>> typeLiteral = new TypeLiteral<List<DtoPerson2Test>>() {
            };
            List<DtoPerson2Test> item = JsonIterator.deserialize(json.toString(), typeLiteral);
            return item;
        }
        List<DtoPerson2Test> person2Tests = repository.findByTestId(testId);
        saveRedis(person2Tests, testId);
        return person2Tests;
    }

    // 新增测试人员配置
    @Override
    @Transactional
    public DtoPerson2Test save(DtoPerson2Test person2Test) {
        List<DtoPerson2Test> exist = repository.findByTestIdAndSampleTypeId(person2Test.getTestId(),
                person2Test.getSampleTypeId());
        if (StringUtil.isNotNull(exist)) {
            throw new BaseException("测试人员信息重复");
        }
        return super.save(person2Test);
    }

    /**
     * 根据检测小类id及测试项目集合返回对应的第一负责人
     *
     * @param sampleTypeId 检测小类ID
     * @param testIds      测试项目ID集合
     * @return 返回第一负责人
     */
    @Override
    public List<DtoPerson2Test> findBySampleTypeAndTestIds(String sampleTypeId, Collection<String> testIds) {
        List<DtoPerson2Test> p2tList = repository.findByIsDefaultPersonTrueAndTestIdIn(testIds);
        DtoSampleType samType = sampleTypeService.findOne(sampleTypeId);
        //包含小类的测试项目id集合
        List<String> smallTestIds = p2tList.parallelStream().filter(p -> p.getSampleTypeId().equals(sampleTypeId)).map(DtoPerson2Test::getTestId).collect(Collectors.toList());
        p2tList = p2tList.parallelStream().filter(p -> p.getSampleTypeId().equals(sampleTypeId) ||
                (!smallTestIds.contains(p.getTestId()) && p.getSampleTypeId().equals(samType.getParentId()))).collect(Collectors.toList());

        List<String> personIds = p2tList.parallelStream().map(DtoPerson2Test::getPersonId).distinct().collect(Collectors.toList());
        List<DtoPerson> personList = personIds.size() > 0 ? personService.findAllDeleted(personIds) : new ArrayList<>();
        for (DtoPerson2Test p2t : p2tList) {
            DtoPerson person = personList.parallelStream().filter(p -> p.getId().equals(p2t.getPersonId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(person)) {
                p2t.setCName(person.getCName());
                p2t.setDomainId(person.getDomainId());
            }
        }

        return p2tList;
    }

    /**
     * 根据测试项目id查询第一负责人员
     *
     * @param testId 测试项目id
     * @return 第一负责人
     */
    @Override
    public List<DtoPerson2Test> findByIsDefaultPersonTrueAndTestId(String testId) {
        return repository.findByIsDefaultPersonTrueAndTestId(testId);
    }

    /**
     * 保存相应的redis数据
     *
     * @param person2Test 测试人员岗位信息
     */
    private void saveRedis(List<DtoPerson2Test> person2Test, String testId) {
        String key = EnumLIMRedis.getRedisKey(EnumLIMRedis.LIM_OrgId_Person2Test.getValue());
        Object json = redisTemplate.opsForHash().get(key, testId);
        List<DtoPerson2Test> newPerson2Tests = new ArrayList<>();
        if (StringUtils.isNotNullAndEmpty(json)) {
            //解析相应的配置数据
            TypeLiteral<List<DtoPerson2Test>> typeLiteral = new TypeLiteral<List<DtoPerson2Test>>() {
            };

            List<DtoPerson2Test> oldPerson2Tests = JsonIterator.deserialize(json.toString(), typeLiteral);

            newPerson2Tests.addAll(person2Test);

            for (DtoPerson2Test dtoPerson2Test : oldPerson2Tests) {

                Optional<DtoPerson2Test> optionalPerson2Test = newPerson2Tests.stream().filter(p -> p.getTestId().equals(testId)
                        && p.getSampleTypeId().equals(dtoPerson2Test.getSampleTypeId())).findFirst();
                if (!optionalPerson2Test.isPresent()) { //不存在的加入到数据中
                    newPerson2Tests.add(dtoPerson2Test);
                }
            }
        }
        redisTemplate.opsForHash().put(key, testId, JsonStream.serialize(newPerson2Tests));
    }

    /**
     * 先清除测试人员岗位信息
     *
     * @param testId       测试项目ID
     * @param sampleTypeId 检测类型ID
     * @return 测试人员岗位信息
     */
    private List<DtoPerson2Test> deleteRedis(String testId, String sampleTypeId) {
        List<DtoPerson2Test> person2Tests = new ArrayList<>();
        String key = EnumLIMRedis.getRedisKey(EnumLIMRedis.LIM_OrgId_Person2Test.getValue());
        Object json = redisTemplate.opsForHash().get(key, testId);
        if (StringUtils.isNotNullAndEmpty(json)) {
            //解析相应的配置数据
            TypeLiteral<List<DtoPerson2Test>> typeLiteral = new TypeLiteral<List<DtoPerson2Test>>() {
            };
            person2Tests = JsonIterator.deserialize(json.toString(), typeLiteral);
            Optional<DtoPerson2Test> optionalPerson2Test = person2Tests.stream().filter(p -> p.getTestId().equals(testId)
                    && p.getSampleTypeId().equals(sampleTypeId)).findFirst();
            if (optionalPerson2Test.isPresent()) {
                person2Tests.remove(optionalPerson2Test.get());
            }
        }
        return person2Tests;
    }

    /**
     * 从redis中获取相应的数据
     *
     * @param dataList 数据集合
     * @return 返回排口相应的数据
     */
    private List<DtoPerson2Test> setMaps(List<Object> dataList,List<String> testIds) {
        List<DtoPerson2Test> itemList = new ArrayList<>();
        TypeLiteral<List<DtoPerson2Test>> typeLiteral = new TypeLiteral<List<DtoPerson2Test>>() {
        };
        List<String> existIds = new ArrayList<>();
        for (Object s : dataList) {
            if (StringUtil.isNotNull(s)) {
                try {
                    List<DtoPerson2Test> item = JsonIterator.deserialize(s.toString(), typeLiteral);
                    if (StringUtil.isNotNull(item)) {
                        existIds.addAll(item.stream().map(DtoPerson2Test::getTestId).distinct().collect(Collectors.toList()));
                        itemList.addAll(item);
                    }
                } catch (Exception ex) {
                    System.out.println(ex.getMessage());
                }
            }
        }
        List<String> newIds = testIds.stream().filter(p -> !existIds.contains(p)).collect(Collectors.toList());
        if (newIds.size() > 0) {
            List<DtoPerson2Test> dtoTests = repository.findByTestIdIn(newIds);
            itemList.addAll(dtoTests);
            for (String testId : newIds) {
                List<DtoPerson2Test> person2Tests = dtoTests.stream().filter(p -> p.getTestId().equals(testId)).collect(Collectors.toList());
                saveRedis(person2Tests, testId);
            }
        }
        return itemList;
    }


    /**
     * 设置变更的测试人员
     *
     * @param testId        测试项目id
     * @param sampleTypeIds 检测类型id
     * @param personId      人员id
     * @return 返回变更的测试人员
     */
    private DtoPerson2TestChange setPerson2TestChange(String testId, List<String> sampleTypeIds, String personId) {
        DtoPerson2TestChange dtoPerson2TestChange = new DtoPerson2TestChange();
        dtoPerson2TestChange.setTestId(testId);
        dtoPerson2TestChange.setSampleTypeIds(sampleTypeIds);
        dtoPerson2TestChange.setCreator(PrincipalContextUser.getPrincipal().getUserId());
        dtoPerson2TestChange.setOrgId(PrincipalContextUser.getPrincipal().getOrgId());
        dtoPerson2TestChange.setPersonId(personId);
        return dtoPerson2TestChange;
    }
}