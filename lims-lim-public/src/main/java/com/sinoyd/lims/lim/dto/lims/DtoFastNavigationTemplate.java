package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.FastNavigationTemplate;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoFastNavigationTemplate实体
 * <AUTHOR>
 * @version V1.0.0 2020/3/27
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_FastNavigationTemplate")
 @Data
 @DynamicInsert
 public  class DtoFastNavigationTemplate extends FastNavigationTemplate {
   private static final long serialVersionUID = 1L;
 }