package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 分析人员分析进度
 * <AUTHOR>
 * @version V1.0.0 2019/12/02
 * @since V100R001
 */
@Data
public class DtoAnalyseProgress {

    /**
     * 待检样品
     */
    private Map<String,Object> awaitSample=new HashMap<>();


    /**
     * 待检检测单
     */
    private Map<String,Object> awaitWorkSheet=new HashMap<>();

    /**
     * 审核检测单
     */
    private Map<String,Object> auditWorkSheet=new HashMap<>();

    /**
     * 完成检测单
     */
    private Map<String,Object> finishWorkSheet=new HashMap<>();
}
