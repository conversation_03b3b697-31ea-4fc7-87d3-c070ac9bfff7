package com.sinoyd.lims.pro.listener;

import com.sinoyd.SpringContextAware;
import com.sinoyd.lims.pro.dto.DtoOATask;
import com.sinoyd.lims.pro.service.OAProcFileControlService;
import com.sinoyd.lims.pro.service.OATaskService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;

/**
 * 文件的受控通知
 *
 * <AUTHOR>
 * @version V1.0.0 2020-04-02
 * @since V100R001
 */
public class OAProcFileControlListener implements ExecutionListener {

    @Override
    public void notify(DelegateExecution delegateExecution) throws Exception {
        //业务数据id
        String businessKey = delegateExecution.getProcessBusinessKey();
        OATaskService oaTaskService = SpringContextAware.getBean(OATaskService.class);
        DtoOATask oaTask = oaTaskService.findOne(businessKey);
        OAProcFileControlService oaProcFileControlService = SpringContextAware.getBean(OAProcFileControlService.class);
        oaProcFileControlService.normalCloseTaskNotify(oaTask);
    }
}
