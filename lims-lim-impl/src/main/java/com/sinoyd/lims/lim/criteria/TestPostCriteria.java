package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 测试岗位管理查询条件
 * <AUTHOR>
 * @version v1.0.0 2022/4/21
 * @since v100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TestPostCriteria extends BaseCriteria {
    
    /**
     * 岗位名称
     */
    private String testPostName;

    /**
     * 关键字（分析项目名称、分析项目拼音检索）
     */
    private String analyzeItemKey;

    /**
     * 关键字（分析方法相关关键字检索）
     */
    private String analyzeMethodKey;

    @Override
    public String getCondition() {
        values.clear(); // 清除条件数据
        StringBuilder condition = new StringBuilder();

        // 关键字模糊查找
        if (StringUtils.isNotNullAndEmpty(testPostName)) {
            condition.append(" and (testPostName like :testPostName)");
            values.put("testPostName", "%" + this.testPostName + "%");
        }
        condition.append(" and isDeleted = 0 ");
        return condition.toString();
    }
}