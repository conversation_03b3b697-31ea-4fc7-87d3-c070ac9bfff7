package com.sinoyd.base.core;

/**
 * Base常量
 * <p>
 *
 * <AUTHOR>
 * @version V1.0.0 2018/12/12
 * @since V100R001
 */
public class BaseCodeHelper {

    /**
     * 文件类型-登记文档
     */
    public static final String DOCUMENT_EXTEND_REGISTER = "BASE_DocumentExtendType_DJWD";

    /**
     * 文件类型-图片视频
     */
    public static final String DOCUMENT_EXTEND_PHOTO_VIDEO = "BASE_DocumentExtendType_TPSP";

    /**
     * 人员签名
     */
    public static final String DOCUMENT_EXTEND_TYPE_SIGNATURE = "BASE_DocumentExtendType_Signature";

    /**
     * 文件类型-原始记录单报告
     */
    public static final String DOCUMENT_EXTEND_ORIGINAL_RECORD = "BASE_DocumentExtendType_YYJLDBG";

    /**
     * 文件类型-采样单附件
     */
    public static final String DOCUMENT_SAMPLE_RECORD = "sampleRecord";

    /**
     * 文件类型-采样单其他附件
     */
    public static final String DOCUMENT_SAMPLE_OTHERATTACH = "otherAttachments";

    /**
     * 文件类型-采样单测点示意图
     */
    public static final String DOCUMENT_SAMPLE_POINTPIC = "pointPic";

    /**
     * 文件类型-执法附件
     */
    public static final String DOCUMENT_SAMPLE_LAWENFORCEMENTANNEX = "lawEnforcementAnnex";

    /**
     * 文件类型-归档报告
     */
    public static final String DOCUMENT_EXTEND_REPORT = "BASE_DocumentExtendType_GDBG";

    /**
     * 文件类型-上报报表
     */
    public static final String DOCUMENT_EXTEND_REPORT_UP = "BASE_DocumentExtendType_SBBG";

    /**
     * 文件类型-电子报告
     */
    public static final String DOCUMENT_EXTEND_ELECTRONIC_REPORT = "BASE_DocumentExtendType_DZBG";

    /**
     * 文件类型-点位示意图附件
     */
    public static final String DOCUMENT_FOLDER_SKETCH = "BASE_DocumentExtendType_DWSYT";

    /**
     * 文件类型-手动生成的报告
     */
    public static final String DOCUMENT_GENERATE_REPORT = "BASE_DocumentExtendType_SCBG";

    /**
     * 文件类型-踏勘附件
     */
    public static final String DOCUMENT_EXTEND_EXPLORE = "BASE_DocumentExtendType_TKWD";

    /**
     * 仪器解析附件
     */
    public static final String DOCUMENT_WORKSHEETFOLDER_ANALYSEIS = "PRP_WorkSheetFolderType_YQJX";

    /**
     * 采样单签名
     */
    public static final String DOCUMENT_RECEIVESAMPLERECORD_SIGNERATURE = "PRO_RecordSignature_QM";

    /**
     * 采样点位图片
     */
    public static final String DOCUMENT_SAMPLEFOLDER_SAMPLINGPICTURE = "PRO_MobileFolder_DW";

    /**
     * 采样点位——前
     */
    public static final String DOCUMENT_SAMPLEFOLDER_SAMPLINGPICTURE_FRONT = "PRO_MobileFolder_DW_FRONT";
    /**
     * 采样点位——中
     */
    public static final String DOCUMENT_SAMPLEFOLDER_SAMPLINGPICTURE_IN = "PRO_MobileFolder_DW_IN";
    /**
     * 采样点位——后
     */
    public static final String DOCUMENT_SAMPLEFOLDER_SAMPLINGPICTURE_AFTER = "PRO_MobileFolder_DW_AFTER";

    /**
     * 移动端采样附件
     */
    public static final String DOCUMENT_MOBILERECORD = "PRO_MobileRecord_Delivery";

    /**
     * 样品类型水大类
     */
    public static final String SAMPLETYPE_BIGCODE_WATER = "BASE_BigSampleType_WATER";

    /**
     * 样品类型废水
     */
    public static final String SAMPLETYPE_WASTER_WATER = "BASE_SampleType_WasterWater";

    /**
     * 样品类型废水
     */
    public static final String RECEIVESAMPLERECORD_BACK_TYPE = "审核退回";

    /**
     * 消耗品种类
     */
    public static final String ConsumableCategory = "LIM_ConsumableCategory";

    /**
     * 消耗品等级
     */
    public static final String ConsumableGrade = "LIM_ConsumableGrade";

    /**
     * 上岗证类型
     */
    public static final String PersonCertType = "LIM_PersonCertType";

    /**
     * 移动端配置显示图片
     */
    public static final String DOCUMENT_MONITOR_ICON = "LIM_MobileRecord_Icon";

    /**
     * 最大行数
     */
    public static final Integer MAX_ROWS = 100000;

    /**
     * 文件类型-采购项目附件
     */
    public static final String DOCUMENT_EXTEND_PURCHASEPROJECT = "PRO_DocumentExtendType_CGYT";

    /**
     * 文件类型-采购项目信息附件
     */
    public static final String DOCUMENT_EXTEND_PURCHASEPROJECTINFO = "PRO_DocumentExtendType_CGXX";

    /**
     * 文档类型-培训管理附件
     */
    public static final String DOCUMENT_EXTEND_TRAINING = "BASE_DocumentExtendType_PXFJ";

    public static final String DOCUMENT_EXTEND_RECEIVE_RECORD = "BASE_DocumentExtendType_ReceiveSampleRecord";

    /**
     * 文件类型-项目电子表单
     */
    public static final String DOCUMENT_EXTEND_PROJECT_SHEET = "BASE_DocumentExtendType_DZBD";

    /**
     * 文件类型-项目电子交接单
     */
    public static final String DOCUMENT_EXTEND_DELIVERY_SPREAD = "BASE_DocumentExtendType_DZJJD";
}