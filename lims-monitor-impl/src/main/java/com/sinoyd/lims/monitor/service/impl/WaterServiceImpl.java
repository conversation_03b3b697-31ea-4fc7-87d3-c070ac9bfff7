package com.sinoyd.lims.monitor.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.monitor.dto.rcc.DtoWater;
import com.sinoyd.lims.monitor.dto.rcc.DtoWaterExpand;
import com.sinoyd.lims.monitor.repository.rcc.WaterExpandRepository;
import com.sinoyd.lims.monitor.repository.rcc.WaterRepository;
import com.sinoyd.lims.monitor.service.WaterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;


/**
 * Water操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@Service
public class WaterServiceImpl extends BaseJpaServiceImpl<DtoWater, String, WaterRepository> implements WaterService {

    @Autowired
    private WaterExpandRepository waterExpandRepository;

    @Autowired
    private WaterRepository waterRepository;

    @Override
    public void findByPage(PageBean<DtoWater> pb, BaseCriteria waterCriteria) {
        pb.setEntityName("DtoWater a");
        pb.setSelect("select a ");
        comRepository.findByPage(pb, waterCriteria);
    }

    @Override
    public DtoWater findOne(String id) {
        //通过id找到当前对象
        DtoWater dtoWater = waterRepository.findOne(id);
        if (dtoWater != null) {
            //通过当前对象的parentId，找到父级对象
            DtoWater dtoWater1 = waterRepository.findOne(dtoWater.getParentId());
            if (dtoWater1 != null) {
                //将父级对象的waterName，放到水体管理的所属水体字段
                dtoWater.setBelongingWater(dtoWater1.getWaterName());
            }
        }
        DtoWaterExpand dtoWaterExpand = waterExpandRepository.findByWaterId(dtoWater.getId());
        if (dtoWaterExpand != null) {
            dtoWater.setDtoWaterExpand(dtoWaterExpand);
        } else {
            dtoWaterExpand = new DtoWaterExpand();
            dtoWaterExpand.setWaterId(dtoWater.getId());
            dtoWater.setDtoWaterExpand(dtoWaterExpand);
        }
        return dtoWater;
    }

    @Transactional
    @Override
    public DtoWater save(DtoWater entity) {
        //根据类别和版本号查找是否存在
        if (StringUtil.isNotEmpty(entity.getWaterCode())) {
            Integer num = repository.countByWaterCode(entity.getWaterCode());
            if (num != 0) {
                throw new BaseException("已存在水体编码为" + entity.getWaterCode() + "的水体");
            }
        }
        if ("所有水体".equals(entity.getBelongingWater())) {
            entity.setParentId(UUIDHelper.GUID_EMPTY);
        }
        super.save(entity);
        //如果DtoWater的dtoWaterExpand对象不为空，就开始新增
        if (entity.getDtoWaterExpand() != null) {
            entity.getDtoWaterExpand().setWaterId(entity.getId());
            waterExpandRepository.save(entity.getDtoWaterExpand());
        }
        return entity;
    }

    @Transactional
    @Override
    public DtoWater update(DtoWater entity) {
        DtoWater dtoWater = repository.findOne(entity.getId());
        if (!dtoWater.getWaterCode().equals(entity.getWaterCode())) {
            //根据类别和版本号查找是否存在
            if (StringUtil.isNotEmpty(entity.getWaterCode())) {
                Integer num = repository.countByWaterCode(entity.getWaterCode());
                if (num != 0) {
                    throw new BaseException("已存在水体编码为" + entity.getWaterCode() + "的水体");
                }
            }
        }
        //如果entity的dtoWaterExpand对象不为空，就开始修改
        if (entity.getDtoWaterExpand() != null) {
            waterExpandRepository.save(entity.getDtoWaterExpand());
        }
        super.save(entity);
        return entity;
    }

    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> idList = new ArrayList<>();
        List<String> list = (List<String>) ids;
        String id = list.get(0);
        idList.add(id);
        this.getId(idList, id);
        return super.logicDeleteById(idList);
    }

    private void getId(List<String> ids, String id) {
        List<DtoWater> list = waterRepository.findByParentId(id);
        for (DtoWater dtoWater : list) {
            String id1 = dtoWater.getId();
            ids.add(id1);
            this.getId(ids, id1);
        }
    }

    @Override
    public List<DtoWater> getTableTree(String key) {
        if (StringUtil.isNotEmpty(key)){
            List<DtoWater> list = waterRepository.findByWaterNameLikeOrWaterCodeLike(key, key);
            return list;
        }
        List<DtoWater> list = waterRepository.findAll();
        return this.getTableTree(list);
    }

    /**
     * 所有目录集合
     *
     * @param dtoWaterList
     * @return DtoWater集合
     */
    private List<DtoWater> getTableTree(List<DtoWater> dtoWaterList) {
        List<DtoWater> list = new ArrayList<>();

        // 获取所有的一级目录
        for (DtoWater dtoWater : dtoWaterList) {
            //获取所有的parentId
            String parentId = dtoWater.getParentId();
            //如果parent不为空且为000....的就是一级目录，放到list中
            if (parentId != null && parentId.equals(UUIDHelper.GUID_EMPTY)) {
                list.add(dtoWater);
            }
        }
        // 获取一级目录的子目录
        for (DtoWater dtoWater : list) {
            dtoWater.setChirdList(getSubList(dtoWater.getId(), dtoWaterList));
        }
        return list;
    }

    // 找子目录
    private List<DtoWater> getSubList(String id, List<DtoWater> dtoWaterList) {
        List<DtoWater> chirdList = new ArrayList<>();
        // 子目录下的直接子目录
        for (DtoWater dtoWater : dtoWaterList) {
            String parentId1 = dtoWater.getParentId();
            if (parentId1 != null && parentId1.equals(id)) {
                chirdList.add(dtoWater);
            }
        }
        // 子数据的间接子目录
        for (DtoWater dtoWater : chirdList) {
            dtoWater.setChirdList(getSubList(dtoWater.getId(), dtoWaterList));
        }
        // 递归结束条件，子目录数量为0
        if (chirdList.size() == 0) {
            return chirdList;
        }
        return chirdList;
    }

    @Override
    public List<DtoWater> findWaterType(String waterType) {
        if("all".equals(waterType)){
            return repository.findByIsEnabledTrueOrderByWaterName();
        }
        return repository.findByWaterTypeAndIsEnabledTrueOrderByWaterName(waterType);
    }

    @Override
    public List<DtoWater> findWaterList() {
        return repository.findByIsEnabledTrueOrderByWaterName();
    }
}