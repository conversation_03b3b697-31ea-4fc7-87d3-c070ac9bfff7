package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoAnalyseAchievement2Person;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 *  分析绩效管理service
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2023/03/13
 */
public interface AnalyseAchievement2PersonService extends IBaseJpaService<DtoAnalyseAchievement2Person, String> {

    /**
     * 选择人员
     * @param personIds 人员id
     */
    void selectPerson(List<String> personIds);

    /**
     * 校验选择人员
     * @param personIds 人员id
     */
    void verifySelectPerson(List<String> personIds);

    /**
     * 更新数据
     * @param year 年份
     */
    void updateData(Integer year, List<String> ids);

    /**
     * 按月份统计合同金额
     * @return Map<String, BigDecimal>
     */
    Map<String, BigDecimal> chartForPerMonth();

}
