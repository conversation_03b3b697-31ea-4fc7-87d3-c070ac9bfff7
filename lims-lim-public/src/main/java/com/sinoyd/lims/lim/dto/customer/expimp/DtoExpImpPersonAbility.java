package com.sinoyd.lims.lim.dto.customer.expimp;


import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.base.dto.customer.PoiBaseEntity;
import lombok.Data;

/**
 * 人员检测能力导入导出实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/13
 * @since V100R001
 */
@Data
public class DtoExpImpPersonAbility extends PoiBaseEntity {

    /**
     * 主键id
     */
    @Excel(name = "主键id（新增时不填）", orderNum = "10", width = 20)
    private String id;


    /**
     * 姓名
     */
    @Excel(name = "姓名(必填)", orderNum = "15", width = 20)
    private String name;

    /**
     * 分析项目
     */
    @Excel(name = "项目(必填)", orderNum = "20", width = 20)
    private String redAnalyzeItemName;

    /**
     * 方法名称
     */
    @Excel(name = "方法标准(必填)", orderNum = "30", width = 20)
    private String redAnalyzeMethodName;

    /**
     * 标准
     */
    @Excel(name = "标准编号", orderNum = "40", width = 20)
    private String redCountryStandard;


    /**
     * 检测类型名称
     */
    @Excel(name = "检测类型(必填)", orderNum = "50", width = 20)
    private String sampleTypeName;
    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 人员的证书编号
     */
    @Excel(name = "证书编号(必填)", orderNum = "60", width = 20)
    private String personCertCode;

    /**
     * 证书获得日期
     */
    @Excel(name = "发证日期(必填)", orderNum = "70", width = 20)
    private String achieveDate;

    /**
     * 有效期至
     */
    @Excel(name = "有效期至", orderNum = "80", width = 20)
    private String certEffectiveTime;

    /**
     * 类别
     */
    @Excel(name = "类别(必填)", orderNum = "90", width = 20)
    private String abilityTypeName;

    private String abilityType;

}
