package com.sinoyd.lims.lim.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.service.PersonFaceMsgService;
import com.sinoyd.lims.lim.criteria.PersonFaceMsgCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoPersonFaceMsg;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * PersonFaceMsg服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2022/9/26
 * @since V100R001
 */
 @Api(tags = "示例: PersonFaceMsg服务")
 @RestController
 @RequestMapping("api/lim/personFaceMsg")
 public class PersonFaceMsgController extends BaseJpaController<DtoPersonFaceMsg, String,PersonFaceMsgService> {


    /**
     * 分页动态条件查询PersonFaceMsg
     * @param personFaceMsgCriteria 条件参数
     * @return RestResponse<List<PersonFaceMsg>>
     */
     @ApiOperation(value = "分页动态条件查询PersonFaceMsg", notes = "分页动态条件查询PersonFaceMsg")
     @GetMapping
     public RestResponse<List<DtoPersonFaceMsg>> findByPage(PersonFaceMsgCriteria personFaceMsgCriteria) {
         PageBean<DtoPersonFaceMsg> pageBean = super.getPageBean();
         RestResponse<List<DtoPersonFaceMsg>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, personFaceMsgCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询PersonFaceMsg
     * @param id 主键id
     * @return RestResponse<DtoPersonFaceMsg>
     */
     @ApiOperation(value = "按主键查询PersonFaceMsg", notes = "按主键查询PersonFaceMsg")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoPersonFaceMsg> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoPersonFaceMsg> restResponse = new RestResponse<>();
         DtoPersonFaceMsg personFaceMsg = service.findOne(id);
         restResponse.setData(personFaceMsg);
         restResponse.setRestStatus(StringUtil.isNull(personFaceMsg) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增PersonFaceMsg
     * @param personFaceMsg 实体列表
     * @return RestResponse<DtoPersonFaceMsg>
     */
     @ApiOperation(value = "新增PersonFaceMsg", notes = "新增PersonFaceMsg")
     @PostMapping
     public RestResponse<DtoPersonFaceMsg> create(@RequestBody DtoPersonFaceMsg personFaceMsg) {
         RestResponse<DtoPersonFaceMsg> restResponse = new RestResponse<>();
         restResponse.setData(service.save(personFaceMsg));
         return restResponse;
      }

     /**
     * 新增PersonFaceMsg
     * @param personFaceMsg 实体列表
     * @return RestResponse<DtoPersonFaceMsg>
     */
     @ApiOperation(value = "修改PersonFaceMsg", notes = "修改PersonFaceMsg")
     @PutMapping
     public RestResponse<DtoPersonFaceMsg> update(@RequestBody DtoPersonFaceMsg personFaceMsg) {
         RestResponse<DtoPersonFaceMsg> restResponse = new RestResponse<>();
         restResponse.setData(service.update(personFaceMsg));
         return restResponse;
      }

    /**
     * "根据id批量删除PersonFaceMsg
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除PersonFaceMsg", notes = "根据id批量删除PersonFaceMsg")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }