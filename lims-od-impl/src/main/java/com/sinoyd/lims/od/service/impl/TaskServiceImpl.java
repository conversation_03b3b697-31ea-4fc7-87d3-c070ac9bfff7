package com.sinoyd.lims.od.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.od.dto.DtoLabGroup;
import com.sinoyd.lims.od.dto.DtoOdPerson;
import com.sinoyd.lims.od.dto.DtoOdSample;
import com.sinoyd.lims.od.dto.DtoTask;
import com.sinoyd.lims.od.enums.EnumOds;
import com.sinoyd.lims.od.repository.TaskRepository;
import com.sinoyd.lims.od.service.LabGroupService;
import com.sinoyd.lims.od.service.OdPersonService;
import com.sinoyd.lims.od.service.OdSampleService;
import com.sinoyd.lims.od.service.TaskService;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.DtoWorkSheet;
import com.sinoyd.lims.pro.dto.DtoWorkSheetFolder;
import com.sinoyd.lims.pro.dto.customer.DtoAnalyseDataTemp;
import com.sinoyd.lims.pro.repository.AnalyseDataRepository;
import com.sinoyd.lims.pro.repository.SampleRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 嗅辨任务接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
@Service
@Slf4j
@SuppressWarnings("all")
public class TaskServiceImpl extends BaseJpaServiceImpl<DtoTask, String, TaskRepository> implements TaskService {


    private OdPersonService odPersonService;

    private PersonRepository personRepository;

    private OdSampleService odSampleService;

    private AnalyseDataRepository analyseDataRepository;

    private SampleRepository sampleRepository;

    private LabGroupService labGroupService;


    @Override
    public void findByPage(PageBean<DtoTask> page, BaseCriteria criteria) {
        page.setEntityName("DtoTask a");
        page.setSelect("select a");
        super.findByPage(page, criteria);
        List<DtoTask> taskList = page.getData();
        List<String> gasMixerIds = taskList.stream().map(DtoTask::getGasMixerId).distinct().collect(Collectors.toList());
        List<DtoPerson> personList = StringUtil.isNotEmpty(gasMixerIds) ? personRepository.findAll(gasMixerIds) : new ArrayList<>();
        taskList.forEach(task -> {
            personList.stream().filter(p -> p.getId().equals(task.getGasMixerId())).findFirst().ifPresent(person -> {
                task.setGasMixerName(person.getCName());
            });
        });

    }

    @Override
    @Transactional
    public DtoTask save(DtoTask entity) {
        List<DtoOdPerson> personList = saveOdPerson(entity);
        entity.setTaskState(EnumOds.EnumTaskState.未完成.getValue());
        DtoTask dtoTask = super.save(entity);
        dtoTask.setOdPersonList(personList);
        return dtoTask;
    }

    @Override
    public DtoTask findOne(String key) {
        DtoTask task = super.findOne(key);
        String gasMixerId = task.getGasMixerId();
        DtoPerson person = personRepository.findOne(gasMixerId);
        task.setGasMixerName(StringUtil.isNotNull(person) ? person.getCName() : "");
        task.setOdPersonList(odPersonService.findByTaskIdOrderBySn(key));
        // 判断是否可以修改嗅辨员
        task.setIsAllow(checkIsAllow(task));
        return task;
    }

    @Override
    @Transactional
    public DtoTask update(DtoTask entity) {
        List<DtoOdPerson> personList = entity.getOdPersonList();
        List<DtoOdPerson> oldPersonList = odPersonService.findByTaskIdOrderBySn(entity.getId());
        if (StringUtil.isEmpty(oldPersonList)) {
            personList = saveOdPerson(entity);
        } else {
            List<DtoOdPerson> updatePersonList = new ArrayList<>();
            for (DtoOdPerson odPerson : oldPersonList) {
                personList.stream().filter(p -> p.getPersonSn().equals(odPerson.getPersonSn())).findFirst().ifPresent(person -> {
                    if (!odPerson.getPersonId().equals(person.getPersonId())) {
                        odPerson.setPersonId(person.getPersonId());
                        updatePersonList.add(odPerson);
                    }
                });
            }
            // 更新嗅辨人员
            if (StringUtil.isNotEmpty(updatePersonList)) {
                odPersonService.save(updatePersonList);
            }
        }
        return super.update(entity);
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> idList = (List<String>) ids;
        List<DtoOdSample> odSamples = odSampleService.findByTaskIdIn(idList);
        // 删除样品
        if (StringUtil.isNotEmpty(odSamples)) {
            odSampleService.logicDeleteById(odSamples.stream().map(DtoOdSample::getId).collect(Collectors.toList()));
        }
        return super.logicDeleteById(ids);
    }

    @Override
    public List<Map<String, Object>> findTaskType() {
        EnumOds.EnumTaskType[] values = EnumOds.EnumTaskType.values();
        return Arrays.stream(values).map(p -> (Map<String, Object>) new HashMap<String, Object>() {{
            put("name", p.name());
            put("value", p.getValue());
            put("num", p.getNum());
        }}).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void autoCreateTask(Map<String, Object> source) {
        // 测试项目Ids
        List<String> testIds = (List<String>) source.get("testIds");
        // 检测单
        List<DtoWorkSheetFolder> workSheetFolderList = (List<DtoWorkSheetFolder>) source.get("workSheetFolderList");
        List<String> workSheetFolderIds = workSheetFolderList.stream().map(DtoWorkSheetFolder::getId).collect(Collectors.toList());
        // 分析数据集合
        List<DtoAnalyseData> analyseDataList = StringUtil.isNotEmpty(workSheetFolderIds) ?
                analyseDataRepository.findByWorkSheetFolderIdIn(workSheetFolderIds) : new ArrayList<>();
        analyseDataList = analyseDataList.stream().filter(p -> testIds.contains(p.getTestId())).collect(Collectors.toList());
        Map<String, List<DtoAnalyseData>> analyzeDataMap = analyseDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getWorkSheetFolderId));
        // 样品数据
        List<String> sampleIdList = analyseDataList.stream().map(DtoAnalyseData::getSampleId).collect(Collectors.toList());
        List<DtoSample> sampleList = StringUtil.isNotEmpty(sampleIdList) ? sampleRepository.findAll(sampleIdList) : new ArrayList<>();

        // 定义嗅辨任务和嗅辨样品容器
        List<DtoTask> taskList = new ArrayList<>();
        List<DtoOdSample> odSamples = new ArrayList<>();
        // 需要根据检测单拆分嗅辨任务
        for (DtoWorkSheetFolder workSheetFolder : workSheetFolderList) {
            List<DtoAnalyseData> dataList = analyzeDataMap.get(workSheetFolder.getId());
            List<String> sampleIds = dataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
            List<DtoSample> dtoSamples = sampleList.stream().filter(p -> sampleIds.contains(p.getId())).collect(Collectors.toList());

            if (StringUtil.isNotEmpty(dtoSamples)) {
                DtoTask task = new DtoTask();
                task.setTaskName(workSheetFolder.getWorkSheetCode() + "嗅辨任务");
                task.setOdDate(workSheetFolder.getAnalyzeTime());
                task.setGasMixerId(workSheetFolder.getAnalystId());
                task.setTaskState(EnumOds.EnumTaskState.未完成.getValue());
                taskList.add(task);
                for (DtoSample dtoSample : dtoSamples) {
                    DtoOdSample odSample = new DtoOdSample();
                    odSample.setSampleState(EnumOds.EnumOdsampleState.未完成.getValue());
                    odSample.setSampleCode(dtoSample.getCode());
                    odSample.setTaskId(task.getId());
                    odSample.setSamplingDate(dtoSample.getSamplingTimeBegin());
                    odSamples.add(odSample);
                }
            }
        }
        if (StringUtil.isNotEmpty(taskList)) {
            repository.save(taskList);
        }
        if (StringUtil.isNotEmpty(odSamples)) {
            odSampleService.save(odSamples);
        }
    }

    @Override
    @Transactional
    public void deleteByWork(String workSheetCode) {
       List<DtoTask> taskList =  repository.findByTaskNameLike(workSheetCode);
       if (StringUtil.isNotEmpty(taskList)){
           logicDeleteById(taskList.stream().map(DtoTask::getId).collect(Collectors.toList()));
       }
    }

    /**
     * 保存嗅辨员
     *
     * @param task 嗅辨任务实体
     */
    private List<DtoOdPerson> saveOdPerson(DtoTask task) {
        List<DtoOdPerson> personList = task.getOdPersonList();
        if (StringUtil.isNotEmpty(personList)) {
            // 当旧数据为空时，需要先新增
            for (DtoOdPerson dtoOdPerson : personList) {
                dtoOdPerson.setTaskId(task.getId());
            }
            personList = odPersonService.save(personList);
        }
        return personList;
    }

    /**
     * 检查是否可以修改嗅辨员
     *
     * @param task 嗅辨任务实体
     * @return 是否
     */
    private boolean checkIsAllow(DtoTask task) {
        boolean isAllow = true;
        List<DtoOdSample> odSamples = odSampleService.findByTaskIdIn(Collections.singletonList(task.getId()));
        List<String> odSampleOds = odSamples.stream().map(DtoOdSample::getId).collect(Collectors.toList());
        List<DtoLabGroup> labGroupList = labGroupService.findBySampleIdIn(odSampleOds);
        // 任务下的所有样品中存在实验组时。不允许修改嗅辨员
        if (StringUtil.isNotEmpty(labGroupList)) {
            isAllow = false;
        }
        return isAllow;
    }

    @Autowired
    @Lazy
    public void setOdPersonService(OdPersonService odPersonService) {
        this.odPersonService = odPersonService;
    }

    @Autowired
    @Lazy
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }

    @Autowired
    @Lazy
    public void setOdSampleService(OdSampleService odSampleService) {
        this.odSampleService = odSampleService;
    }

    @Autowired
    @Lazy
    public void setAnalyseDataRepository(AnalyseDataRepository analyseDataRepository) {
        this.analyseDataRepository = analyseDataRepository;
    }

    @Autowired
    @Lazy
    public void setSampleRepository(SampleRepository sampleRepository) {
        this.sampleRepository = sampleRepository;
    }

    @Autowired
    @Lazy
    public void setLabGroupService(LabGroupService labGroupService) {
        this.labGroupService = labGroupService;
    }
}
