package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.Arrange2Method;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 采样方法实体
 *
 * <AUTHOR>
 * @version V1.0.0 2024/7/25
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_Arrange2Method")
@Data
@DynamicInsert
public class DtoArrange2Method extends Arrange2Method {

    /**
     * 检测类型名称
     */
    @Transient
    private String sampleTypeName;

}
