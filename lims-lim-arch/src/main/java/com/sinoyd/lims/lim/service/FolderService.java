package com.sinoyd.lims.lim.service;

import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoFolder;

import java.util.List;

/**
 * 文件夹接口
 *
 * <AUTHOR>
 * @version V1.0.0 2018/12/6
 * @since V100R001
 */
public interface FolderService extends IBaseJpaService<DtoFolder, String> {
    /**
     * 【徐竹】 过时：树结构在前台处理，后台只提供List
     * <p>
     * 获取文件夹树
     * <p>
     * 筛选条件在<i>criteria</i>中，可以根据所属文件夹、文档名称检索
     * <p>
     * 建议使用递归实现，而且应该定义一个{@code TreeNode }实体，或者在Folder实体里面加上childList字段，返回Folder就可以
     *
     * @param criteria 筛选条件
     * @return 文件夹的{@code TreeNode }List
     */
    // Collection<TreeNode> getFolderTree(BaseCriteria criteria);

    // 【徐竹】 过时：树结构在前台处理，后台只提供List


    /**
     * 实验室管理文档管理附件路径
     *
     * @param folderId 文件夹id
     * @return 返回实验室文档管理路径
     */
    String getDocumentAttachPath(String folderId);

    /**
     * 文件夹树(非应急文件)
     *
     * @return List<TreeNode>
     */
    List<TreeNode> folderTree();

}