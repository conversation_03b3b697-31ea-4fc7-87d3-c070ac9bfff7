package com.sinoyd.lims.lim.service.homeTaskNumber;

import com.sinoyd.lims.lim.configuration.HomeModule;
import com.sinoyd.lims.lim.dto.customer.DtoTaskNum;
import com.sinoyd.lims.lim.enums.EnumLIM;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 首页实验室检测代办数字缓存刷新
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2022/10/26
 */
@Component
public class AwaitWorkSheetTask extends AbsTaskNumber{

    /**
     * 刷新卡片的代办数据
     *
     * @param homeModule 模块数据
     */
    @Override
    public void refreshCardNum(HomeModule homeModule, String orgId, String userId) {
        //获取数据库中的数量
        List<DtoTaskNum> taskNumList = this.getTaskNum(homeModule, orgId, userId, new ArrayList<>());
        String redisKey = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_AwaitWorkSheetDetail.getValue());
        for (DtoTaskNum taskNum : taskNumList) {
            saveCardInfo(taskNum.getUserId(), redisKey,"awaitWorkSheetCount","awaitWorkSheetDetail", taskNum.getCount());
        }
    }

    /**
     * 获取缓存数据集合
     *
     * @param homeModule 模块编码
     * @param orgId      组织Id
     * @param userId     用户Id
     * @param outTypeIds 不包含的项目类型
     * @return 结果集
     */
    @Override
    public List<DtoTaskNum> getTaskNum(HomeModule homeModule, String orgId, String userId, List<String> outTypeIds) {
        //创建查询sql语句
        StringBuilder stringBuilder = new StringBuilder("select a.analystId as userId,count(a.id) as count from");
        stringBuilder.append(" TB_PRO_WorkSheetFolder a");
        stringBuilder.append(" where 1=1 and a.orgId = ?");
        stringBuilder.append(" and a.workStatus in (1,2,6)");
        stringBuilder.append(" group by a.analystId");
        //执行sql语句
        return jdbcTemplate.query(stringBuilder.toString(),
                new String[]{orgId},
                (resultSet, i) -> new DtoTaskNum(resultSet.getString("userId"),resultSet.getLong("count")));
    }

    /**
     * 获取模块编码
     *
     * @return 模块编码
     */
    @Override
    public String getModuleCode() {
        return EnumLIM.EnumHomeTaskModule.实验室检测.getValue();
    }
}
