package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.SampleDispose2Test;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoSampleDispose2Test实体
 * <AUTHOR>
 * @version V1.0.0 2021/11/8
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_SampleDispose2Test")
 @Data
 @DynamicInsert
 public  class DtoSampleDispose2Test extends SampleDispose2Test {
   private static final long serialVersionUID = 1L;
 }