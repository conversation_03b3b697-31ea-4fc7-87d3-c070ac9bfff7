package com.sinoyd.lims.api.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.api.dto.vo.ProjectVO;
import com.sinoyd.lims.api.dto.vo.WWFolderVO;
import com.sinoyd.lims.api.dto.vo.WWProjectVO;
import com.sinoyd.lims.api.service.MobileWWService;
import com.sinoyd.lims.instrument.parse.dto.DtoTcpData;
import com.sinoyd.lims.pro.dto.DtoSampleGroup;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 仪器出入库移动端内容
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
@Api(tags = "示例: homeMonitor")
@RestController
@RequestMapping("api/monitor/wwservice")
public class MobileWWServiceController extends ExceptionHandlerController<MobileWWService> {

    @ApiOperation(value = "返回任务下发信息", notes = "返回任务下发信息")
    @PostMapping("/wwProjectInfo")
    public RestResponse<WWProjectVO> findByProAndFolder(@RequestBody ProjectVO projectVO) {
        RestResponse<WWProjectVO> response = new RestResponse<>();
        response.setData(service.findByProAndFolder(projectVO));
        return response;
    }

    @ApiOperation(value = "保存任务下发和点位关系", notes = "保存任务下发和点位关系")
    @PostMapping("/fodlerWWInfo")
    public RestResponse<Void> saveFolderPeriodWWInfo(@RequestBody ProjectVO projectVO) {
        RestResponse<Void> response = new RestResponse<>();
        service.saveFolderPeriodWWInfo(projectVO);
        return response;
    }

    @ApiOperation(value = "根据下发任务查询仪器解析内容", notes = "根据下发任务查询仪器解析内容")
    @PostMapping("/wwDataInfo")
    public RestResponse<List<WWFolderVO>> findWWInfo(@RequestBody ProjectVO projectVO) {
        RestResponse<List<WWFolderVO>> response = new RestResponse<>();
        response.setData(service.findWWInfo(projectVO));
        return response;
    }

    @ApiOperation(value = "根据样品id获取样品分组信息", notes = "根据样品id获取样品分组信息")
    @GetMapping("/groupInfo/{sampleId}")
    public RestResponse<List<DtoSampleGroup>> findBySampleId(@PathVariable(name = "sampleId") String sampleId) {
        RestResponse<List<DtoSampleGroup>> response = new RestResponse<>();
        response.setData(service.findBySampleId(sampleId));
        return response;
    }

    @ApiOperation(value = "同步解析内容", notes = "同步解析内容")
    @PostMapping("/syncParamsInfo")
    public RestResponse<Void> syncParamsInfo(@RequestBody ProjectVO projectVO) {
        RestResponse<Void> response = new RestResponse<>();
        service.syncParamsInfo(projectVO);
        return response;
    }
}
