package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentUseRecord2Sample;
import com.sinoyd.lims.lim.repository.lims.InstrumentUseRecord2SampleRepository;
import com.sinoyd.lims.lim.service.InstrumentUseRecord2SampleService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * InstrumentUseRecord2Sample操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/12/3
 * @since V100R001
 */
 @Service
public class InstrumentUseRecord2SampleServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoInstrumentUseRecord2Sample,String,InstrumentUseRecord2SampleRepository> implements InstrumentUseRecord2SampleService {

    @Override
    public void findByPage(PageBean<DtoInstrumentUseRecord2Sample> pb, BaseCriteria instrumentUseRecord2SampleCriteria) {
        pb.setEntityName("DtoInstrumentUseRecord2Sample a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, instrumentUseRecord2SampleCriteria);
    }

    @Override
    public List<DtoInstrumentUseRecord2Sample> findByInstrumentUseRecordId(String instrumentUseRecordId) {
        return repository.findByInstrumentUseRecordId(instrumentUseRecordId);
    }
}