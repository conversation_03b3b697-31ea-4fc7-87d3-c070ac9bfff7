package com.sinoyd.lims.lim.verify;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.utils.ImportUtils;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 企业导入数据校验
 *
 * <AUTHOR>
 * @version V1.0.0 2022/9/20
 * @since V100R001
 */
@Component
@Data
public class MpnConfigVerifyHandle implements IExcelVerifyHandler<Map<String, Object>> {

    /**
     * 工具类
     */
    private final ImportUtils importUtils = new ImportUtils();

    /**
     * 行号
     */
    private ThreadLocal<Integer> lineNoTl = new ThreadLocal<>();

    /**
     * 人员导入数据校验
     *
     * @param map 导入数据
     * @return 校验结果
     */
    @Override
    public ExcelVerifyHandlerResult verifyHandler(Map<String, Object> map) {
        if (null == lineNoTl.get()) {
            lineNoTl.set(2);
        } else {
            lineNoTl.set(lineNoTl.get() + 1);
        }

        //校验结果
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        StringBuilder errorMsg = new StringBuilder();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            if (!"95%置信上限".equals(key) && !"95%置信下限".equals(key)) {
                if (!(entry.getValue() != null && StringUtil.isNotEmpty(entry.getValue().toString()))) {
                    errorMsg.append(key).append("不能为空");
                }
            }
        }
        if (StringUtil.isNotEmpty(errorMsg.toString())) {
            result.setSuccess(false);
            result.setMsg("第" + lineNoTl.get() + "行" + errorMsg.toString());
        }
        return result;
    }


}
