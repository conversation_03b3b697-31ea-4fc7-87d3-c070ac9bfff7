package com.sinoyd.lims.lim.core;

/**
 * Lim常量(在接口内定义常量，避免初始化)
 * <AUTHOR>
 * @version V1.0.0 2018/12/12
 * @since V100R001
 */
public class LimCodeHelper {

    /**
     * 费用审批权限
     */
    public static final String COST_APPROVE_AUTH = "pro_costInfoApprove_show";

    /**
     * 现场数据审核权限
     */
    public static final String LOCAL_AUDIT_AUTH = "pro_localDataAudit_show";

    /**
     * 报告签发权限
     */
    public static final String REPORT_SIGN_AUTH = "pro_reportSign_show";

    /**
     * 文件权限常量编码
     */
    public static final String LIM_DocumentAuthorityType = "LIM_DocumentAuthorityType";


    /**
     * 文档权限（验证是否文件管理员权限)
     */
    public static final String LIM_DocAuthority_Show = "lim_docAuthority_show";

    /**
     * 收款合同
     */
    public static final String CollectionContract = "LIM_ContractType_SK";

    /**
     * 付款合同
     */
    public static final String PayContract = "LIM_ContractType_WY";

    /**
     * 人员关键岗位常量信息
     */
    public static final String PersonPost = "LIM_KeyPost";

    /**
     * 技术岗位的常量信息
     */
    public static final  String TechnicalTitle="LIM_TechnicalTitle";
    /**
     * 试剂配置操作权限
     */
    public static final String REAGENT_CONFIG_ALL = "lim_analyzeMethodReagentConfig_all";

    /**
     * 流水号标识
     */
    public static final String SN = "sn";

    /**
     * 所有的日期格式
     */
    public static final String[] DATE_FORMAT = {"yyyy", "yy", "MM", "M", "dd", "d", "yyyyMM", "yyyyMMdd", "yyMMdd"};

    /**
     * 培训方式的常量信息
     */
    public static final  String TRAINING="LIM_Training";

    /**
     * 登录页是否显示二维码开关标识
     */
    public static final String LOGIN_QRCODE = "sys.login.QRCode";

    /**
     * 项目编号
     */
    public static final String[] PROJECT_FORMAT = {
            "projectType.mark",
            "project.inceptTime(yyyy)",
            "project.inceptTime(yy)",
            "project.inceptTime(MM)",
            "project.inceptTime(M)",
            "project.inceptTime(dd)",
            "project.inceptTime(d)",
            "time(yyyy)",
            "time(yyyy)",
            "time(yy)",
            "time(MM)",
            "time(M)",
            "time(dd)",
            "time(d)",
            "Project"
    };

    /**
     * 质控样编号的格式
     */
    public static final String[] QC_FORMAT = {
            "project.projectCode",
            "sample.samplingTimeBegin(yyyy)",
            "sample.samplingTimeBegin(yy)",
            "sample.samplingTimeBegin(MM)",
            "sample.samplingTimeBegin(M)",
            "sample.samplingTimeBegin(dd)",
            "sample.samplingTimeBegin(d)",
            "sampleType.shortName",
            "sampleType.bigShortName",
            "projectType.mark",
            "person.userNo",
            "sampleFolder.folderCode",
            "workSheet.code",
            "standardSampleCode",
            "sample.associateSampleCode",
            "time(yyyy)",
            "time(yyyy)",
            "time(yy)",
            "time(MM)",
            "time(M)",
            "time(dd)",
            "time(d)",
            "Sample",
            "InnerSample",
            "receiveSampleRecord.sendTime(yyyy)",
            "receiveSampleRecord.sendTime(yy)",
            "receiveSampleRecord.sendTime(MM)",
            "receiveSampleRecord.sendTime(M)",
            "receiveSampleRecord.sendTime(dd)",
            "receiveSampleRecord.sendTime(d)"
    };

    /**
     * 原样的编号格式
     */
    public static final String[] ASSOCIATE_FORMAT = {
            "project.projectCode",
            "sampleType.shortName",
            "sampleType.bigShortName",
            "projectType.mark",
            "person.userNo",
            "sampleFolder.folderCode",
            "sample.associateSampleCode",
            "sample.samplingTimeBegin(yyyy)",
            "sample.samplingTimeBegin(yy)",
            "sample.samplingTimeBegin(MM)",
            "sample.samplingTimeBegin(M)",
            "sample.samplingTimeBegin(dd)",
            "sample.samplingTimeBegin(d)",
            "time(yyyy)",
            "time(yy)",
            "time(MM)",
            "time(M)",
            "time(dd)",
            "time(d)",
            "Sample",
            "receiveSampleRecord.sendTime(yyyy)",
            "receiveSampleRecord.sendTime(yy)",
            "receiveSampleRecord.sendTime(MM)",
            "receiveSampleRecord.sendTime(M)",
            "receiveSampleRecord.sendTime(dd)",
            "receiveSampleRecord.sendTime(d)"
    };

    /**
     * 送样单编号格式
     */
    public static final String[] RECEVIESAMPLE_FORMAT = {
            "time(yyyy)",
            "time(yy)",
            "time(MM)",
            "time(M)",
            "time(dd)",
            "time(d)",
            "RecevieSampleRecord"
    };

    /**
     * 报告格式
     */
    public static final String[] REPORT_FORMAT = {
            "project.projectCode",
            "reportType.year",
            "reportType.name",
            "time(yyyy)",
            "time(yy)",
            "time(MM)",
            "time(M)",
            "time(dd)",
            "time(d)",
            "Report",
            "report.reportCode"
    };

    /**
     * 检测单编号格式
     */
    public static final String[] WORKSHEET_FORMAT = {
            "time(yyyy)",
            "time(yy)",
            "time(MM)",
            "time(M)",
            "time(dd)",
            "time(d)",
            "WorkSheetFolder"
    };

    /**
     * 检测单编号格式
     */
    public static final String[] ORDERCONTRACT_FORMAT = {
            "time(yyyy)",
            "time(yy)",
            "time(MM)",
            "time(M)",
            "time(dd)",
            "time(d)",
            "OrderContract"
    };

    /**
     * 监测计划子系统字典类型编码
     */
    public interface MonitorCodeTypes {
        String POLLUTANT_FOLDER_TYPE = "LIM_PollutionPointType"; //污染源点位类型
        String ENV_QUALITY_FOLDER_TYPE = "LIM_EnvQualityPointType"; //环境质量点位类型
        String FOLDER_LEVEL = "LIM_ControlLevel"; //点位等级
    }


    /**
     * 远大管理员角色编码
     */
    public static final String SINOYD_ADMINISTRATORS = "Sinoyd_Administrators";
}