package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 样品分配返回实体
 * <AUTHOR>
 * @version V1.0.0 2019/12/06
 * @since V100R001
 */
@Data
public class DtoSampleAssignTemp {

    /**
     * 是否质控任务
     */
    private Boolean isQM;

    /**
     * 测试项目集合
     */
    private List<Map<String,Object>> test = new ArrayList<>();

    /**
     * 人员集合
     */
    private List<Map<String,Object>> person = new ArrayList<>();

    /**
     * 分析项目集合
     */
    private List<Map<String,Object>> analyzeItem = new ArrayList<>();

    /**
     * 分析方法集合
     */
    private List<Map<String,Object>> analyzeMethod = new ArrayList<>();

}
