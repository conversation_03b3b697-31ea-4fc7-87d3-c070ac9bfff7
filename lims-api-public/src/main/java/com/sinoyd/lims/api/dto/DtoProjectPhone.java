package com.sinoyd.lims.api.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;


@Data
public class DtoProjectPhone {

    /**
     * 项目id
     */
    private String id;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 送样单编号
     */
    private String receiveCode;

    /**
     * 受检单位id
     */
    private String inspectedEntId;

    /**
     * 受检单位名称
     */
    private String inspectedEntName;

    /**
     * 登记时间
     */
    private String inputTime;

    /**
     * 项目类型id
     */
    private String projectTypeId;

    /**
     * 项目类型名称
     */
    private String projectTypeName;

    /**
     * 状态
     */
    private String status;

    /**
     * 项目状态
     */
    private String projectStatus;

    /**
     * 等级
     */
    private Integer grade;

    /**
     * 送样单id
     */
    private List<String> receiveIds;
}
