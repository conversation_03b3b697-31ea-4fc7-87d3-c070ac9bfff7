package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoNewSearchPlan;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * 查新计划仓储
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
public interface NewSearchPlanRepository extends IBaseJpaRepository<DtoNewSearchPlan, String> {

    /**
     * 根据计划名称判断
     *
     * @Param planName 计划名称
     * @Param id 主键id
     */
    @Query("select count(p) from DtoNewSearchPlan p where p.isDeleted <> 1 and p.planName = :planName and p.id != :id")
    Integer countByPlanName(@Param("planName") String planName, @Param("id") String id);

}
