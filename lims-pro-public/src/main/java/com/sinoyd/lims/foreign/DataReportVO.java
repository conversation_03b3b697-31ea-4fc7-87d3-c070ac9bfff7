package com.sinoyd.lims.foreign;

import lombok.Data;

import java.util.Date;

/**
* 报告信息
* <AUTHOR>
* @version V1.0.0 2023/11/7
* @since V100R001
*/
@Data
public class DataReportVO {

    /**
     * id
     */
    private String id;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 报告编号
     */
    private String code;

    /**
     * 报告编制日期
     */
    private Date reportDate;

    /**
     * 报告签发日期
     */
    private Date SignDate;

    /**
     * 报告签发人
     */
    private String SignPerson;

    /**
     * 报告类型
     */
    private String reportType;

    /**
     * 报告年份
     */
    private Integer reportYear;

    /**
     * 分析项目排序id
     */
    private String analyseItemSortId;

    /**
     * 点位排序id
     */
    private String folderSortId;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;
}
