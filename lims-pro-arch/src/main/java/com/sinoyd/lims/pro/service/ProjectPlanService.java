package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.pro.dto.DtoProjectPlan;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.List;


/**
 * ProjectPlan操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface ProjectPlanService extends IBaseJpaService<DtoProjectPlan, String> {

    /**
     * 根据项目id获取项目方案信息
     * @param projectIds 项目ids
     * @return 项目方案信息集合
     */
    List<DtoProjectPlan> findByProjectIds(List<String> projectIds);
}