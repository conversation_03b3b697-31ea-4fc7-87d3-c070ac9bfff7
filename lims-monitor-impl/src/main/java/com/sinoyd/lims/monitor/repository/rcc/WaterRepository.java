package com.sinoyd.lims.monitor.repository.rcc;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.monitor.dto.rcc.DtoWater;

import java.util.List;


/**
 * Water数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
public interface WaterRepository extends IBaseJpaRepository<DtoWater, String> {

    /**
     * 根据水体编码查找对象
     * @param waterCode
     * @return Integer
     */
    Integer countByWaterCode(String waterCode);


    /**
     * 按照 parentId查询DtoWater集合
     *
     * @param parentId
     * @return DtoWater
     */
    List<DtoWater> findByParentId(String parentId);

    /**
     * 根据水体类型编码查询
     *
     * @param waterType 水体类型编码
     * @return 水体列表
     */
    List<DtoWater> findByWaterTypeAndIsEnabledTrueOrderByWaterName(String waterType);

    /**
     * 查询所有启用的水体
     *
     * @return 水体列表
     */
    List<DtoWater> findByIsEnabledTrueOrderByWaterName();

    /**
     * 根据水体名称和水体编码模糊查询
     * @param waterName waterCode
     * @return 水体列表
     */
    List<DtoWater> findByWaterNameLikeOrWaterCodeLike(String waterName,String waterCode);

}