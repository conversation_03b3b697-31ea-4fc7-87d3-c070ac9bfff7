package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.SamplingPersonConfigService;
import com.sinoyd.lims.pro.criteria.SamplingPersonConfigCriteria;
import com.sinoyd.lims.pro.dto.DtoSamplingPersonConfig;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * SamplingPersonConfig服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: SamplingPersonConfig服务")
 @RestController
 @RequestMapping("api/pro/samplingPersonConfig")
 public class SamplingPersonConfigController extends BaseJpaController<DtoSamplingPersonConfig, String,SamplingPersonConfigService> {


    /**
     * 分页动态条件查询SamplingPersonConfig
     * @param samplingPersonConfigCriteria 条件参数
     * @return RestResponse<List<SamplingPersonConfig>>
     */
     @ApiOperation(value = "分页动态条件查询SamplingPersonConfig", notes = "分页动态条件查询SamplingPersonConfig")
     @GetMapping
     public RestResponse<List<DtoSamplingPersonConfig>> findByPage(SamplingPersonConfigCriteria samplingPersonConfigCriteria) {
         PageBean<DtoSamplingPersonConfig> pageBean = super.getPageBean();
         RestResponse<List<DtoSamplingPersonConfig>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, samplingPersonConfigCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询SamplingPersonConfig
     * @param id 主键id
     * @return RestResponse<DtoSamplingPersonConfig>
     */
     @ApiOperation(value = "按主键查询SamplingPersonConfig", notes = "按主键查询SamplingPersonConfig")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoSamplingPersonConfig> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoSamplingPersonConfig> restResponse = new RestResponse<>();
         DtoSamplingPersonConfig samplingPersonConfig = service.findOne(id);
         restResponse.setData(samplingPersonConfig);
         restResponse.setRestStatus(StringUtil.isNull(samplingPersonConfig) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增SamplingPersonConfig
     * @param samplingPersonConfig 实体列表
     * @return RestResponse<DtoSamplingPersonConfig>
     */
     @ApiOperation(value = "新增SamplingPersonConfig", notes = "新增SamplingPersonConfig")
     @PostMapping
     public RestResponse<DtoSamplingPersonConfig> create(@RequestBody  @Validated DtoSamplingPersonConfig samplingPersonConfig) {
         RestResponse<DtoSamplingPersonConfig> restResponse = new RestResponse<>();
         restResponse.setData(service.save(samplingPersonConfig));
         return restResponse;
      }

     /**
     * 新增SamplingPersonConfig
     * @param samplingPersonConfig 实体列表
     * @return RestResponse<DtoSamplingPersonConfig>
     */
     @ApiOperation(value = "修改SamplingPersonConfig", notes = "修改SamplingPersonConfig")
     @PutMapping
     public RestResponse<DtoSamplingPersonConfig> update(@RequestBody  @Validated DtoSamplingPersonConfig samplingPersonConfig) {
         RestResponse<DtoSamplingPersonConfig> restResponse = new RestResponse<>();
         restResponse.setData(service.update(samplingPersonConfig));
         return restResponse;
      }

    /**
     * "根据id批量删除SamplingPersonConfig
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除SamplingPersonConfig", notes = "根据id批量删除SamplingPersonConfig")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }