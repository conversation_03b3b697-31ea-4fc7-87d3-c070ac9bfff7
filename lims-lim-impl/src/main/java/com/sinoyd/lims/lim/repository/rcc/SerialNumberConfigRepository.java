package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoSerialNumberConfig;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;


/**
 * SerialNumberConfig数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
public interface SerialNumberConfigRepository extends IBaseJpaPhysicalDeleteRepository<DtoSerialNumberConfig, String>, LimsRepository<DtoSerialNumberConfig, String> {
    /**
     * 根据序列号类型获取相应数据
     *
     * @param serialNumberType 序列号值
     * @return 返回相应数据
     */
    DtoSerialNumberConfig findBySerialNumberType(String serialNumberType);

    /**
     * 根据序列号类型获取相应数据
     *
     * @param serialNumberTypes 序列号类型集合
     * @return 返回相应数据
     */
    List<DtoSerialNumberConfig> findBySerialNumberTypeIn(Collection<String> serialNumberTypes);

    /**
     * 根据序列号类型获取相应数据列表，该API为南通一体化增加
     *
     * @param serialNumberType 序列号值
     * @return 返回相应数据集合
     */
    List<DtoSerialNumberConfig> findBySerialNumberTypeOrderBySerialNumberType(String serialNumberType);

    /**
     * 根据序列号类型和参数2获取相应数据
     *
     * @param serialNumberType 序列号值
     * @param para2            参数2
     * @return 返回相应数据
     */
    DtoSerialNumberConfig findBySerialNumberTypeAndPara2(String serialNumberType, String para2);

    /**
     * 根据序列号类型统计记录数
     *
     * @param serialNumberType 序列号类型
     * @return 记录数
     */
    Integer countBySerialNumberType(String serialNumberType);


    @Transactional
    @Modifying
    @Query("update DtoSerialNumberConfig a set a.para1=:para1,a.para2=:para2,a.lastUpdateTime=:lastUpdateTime where a.id=:id")
    Integer update(@Param("id") String id, @Param("para1") String para1, @Param("para2") String para2, @Param("lastUpdateTime") Date lastUpdateTime);
}