package com.sinoyd.base.service.impl;

import com.sinoyd.base.dto.lims.DtoInstrument2Detector;
import com.sinoyd.base.repository.lims.Instrument2DetectorRepository;
import com.sinoyd.base.service.Instrument2DetectorService;
import com.sinoyd.base.service.InstrumentService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;


/**
 * 仪器关联多检测器管理实现
 *
 * <AUTHOR>
 * @version V1.0.0 2019-04-29
 * @since V100R001
 */
@Service
public class Instrument2DetectorServiceImpl extends BaseJpaServiceImpl<DtoInstrument2Detector, String, Instrument2DetectorRepository>
        implements Instrument2DetectorService {

    private InstrumentService instrumentService;

    @Override
    public void findByPage(PageBean<DtoInstrument2Detector> page, BaseCriteria criteria) {
        // 设置查询的实体类名及别名
        page.setEntityName("DtoInstrument2Detector a");
        // 设置查询返回的字段、实体别名表示所有字段
        page.setSelect("select a");
        super.findByPage(page, criteria);
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> idList = (List<String>) ids;
        List<DtoInstrument2Detector> instrument2Detectors = repository.findAll(idList);
        
        if (StringUtil.isNotEmpty(instrument2Detectors)) {
            DtoInstrument2Detector instrument2Detector = instrument2Detectors.get(0);
            // 根据
            List<DtoInstrument2Detector> allIns2DetectorList = repository.findByInstrumentId(instrument2Detector.getInstrumentId());
            allIns2DetectorList.removeIf(p -> idList.contains(p.getId()));
            // 所有检测器全部删除后，多检测器开关置为否
            if (StringUtil.isEmpty(allIns2DetectorList)) {
                instrumentService.resetDetectorSwitch(instrument2Detector.getInstrumentId());
            }
        }
        return super.logicDeleteById(ids);
    }

    @Autowired
    @Lazy
    public void setInstrumentService(InstrumentService instrumentService) {
        this.instrumentService = instrumentService;
    }
}