delete
from t_sys_config
where configKey = 'sys.history.comparison';

INSERT INTO t_sys_config(id, configName, configKey, configValue, enabled, sortNum, note, createDate, createUserGuid,
                         createUserName,
                         updateDate, updateUserGuid, updateUserName, orgGuid, deptGuid, deleted)
VALUES ('87aa5668eaac47ea9289644224aa6ad9', '是否检测显示评价提醒及历史极值', 'sys.history.comparison', 'true', 1, 0,
        '“评价提醒”、“历史数据”选项卡，在检测中环节是否能够查看或隐藏、是否在检测中提交的时候，超标及历史极值判定的提醒。', '2024-11-07 09:15:43',
        '59141356591b48e18e139aa54d9dd351', '超级管理员', NULL, NULL, NULL, '5f7bcf90feb545968424b0a872863876', NULL, 0);


delete
from t_sys_config
where configKey = 'sys.orientation.disposition';

INSERT INTO t_sys_config(id, configName, configKey, configValue, enabled, sortNum, note, createDate, createUserGuid,
                         createUserName,
                         updateDate, updateUserGuid, updateUserName, orgGuid, deptGuid, deleted)
VALUES ('e802815e04ea4d66aa10be7d0963a30e', '定位服务判定', 'sys.orientation.disposition',
        '{\"定位模式\":\"单模\",\"优先调用卫星\":\"北斗\"}', 1, 0,
        '移动端定位服务判定', '2024-11-07 09:15:43', '59141356591b48e18e139aa54d9dd351', '超级管理员', NULL, NULL, NULL,
        '5f7bcf90feb545968424b0a872863876', NULL, 0);
