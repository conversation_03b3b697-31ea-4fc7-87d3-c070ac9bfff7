package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.InstrumentInspectCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentInspect;
import com.sinoyd.lims.lim.service.InstrumentInspectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 仪器期间勘查接口定义
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-30
 * @since V100R001
 */
@Api(tags = "仪器期间勘查")
@Validated
@RestController
@RequestMapping("/api/lim/instrumentInspect")
public class InstrumentInspectController extends BaseJpaController<DtoInstrumentInspect, String, InstrumentInspectService>{

    /**
     * 根据id获取期间勘查信息
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id获取期间勘查信息", notes = "根据id获取期间勘查信息")
    @GetMapping("/{id}")
    public RestResponse<DtoInstrumentInspect> find(@PathVariable(name = "id") String id){

        RestResponse<DtoInstrumentInspect> restResp = new RestResponse<>();
        DtoInstrumentInspect record = service.findOne(id);
        restResp.setData(record);

        restResp.setRestStatus(StringUtil.isNull(record) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }

    /**
     * 分页动态条件查询期间勘查信息
     * @param criteria
     * @return
     */
    @ApiOperation(value = "分页动态条件查询期间勘查信息", notes = "分页动态条件查询期间勘查信息")
    @GetMapping
    public RestResponse<List<DtoInstrumentInspect>> findByPage(InstrumentInspectCriteria criteria){

        RestResponse<List<DtoInstrumentInspect>> restResp = new RestResponse<>();

        PageBean<DtoInstrumentInspect> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 新增期间勘查记录
     * @param entity
     * @return
     */
    @ApiOperation(value = "新增期间勘查记录", notes = "新增期间勘查记录")
    @PostMapping
    public RestResponse<DtoInstrumentInspect> save(@Validated @RequestBody DtoInstrumentInspect entity) {

        RestResponse<DtoInstrumentInspect> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoInstrumentInspect data = service.save(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 修改期间勘查信息
     * @param entity
     * @return
     */
    @ApiOperation(value = "修改期间勘查信息", notes = "修改期间勘查信息")
    @PutMapping
    public RestResponse<DtoInstrumentInspect> update(@Validated @RequestBody DtoInstrumentInspect entity) {

        RestResponse<DtoInstrumentInspect> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoInstrumentInspect data = service.update(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 根据id批量删除期间勘查信息
     * @param ids
     * @return
     */
    @ApiOperation(value = "根据id批量删除期间勘查信息", notes = "根据id批量删除期间勘查信息")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);

        return restResp;
    }

}