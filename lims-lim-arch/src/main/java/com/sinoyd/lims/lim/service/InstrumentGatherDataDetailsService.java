package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentGatherDataDetails;

/**
 * 仪器接入数据操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/17
 * @since V100R001
 */
public interface InstrumentGatherDataDetailsService extends IBaseJpaService<DtoInstrumentGatherDataDetails, String> {


}