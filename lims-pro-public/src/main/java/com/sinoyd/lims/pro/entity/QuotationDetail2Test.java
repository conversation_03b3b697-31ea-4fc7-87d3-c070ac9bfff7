package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;


/**
 * QuotationDetail2Test实体
 * <AUTHOR>
 * @version V1.0.0 2022/5/23
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="QuotationDetail2Test")
 @Data
 public  class QuotationDetail2Test implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

 

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("")
	private String detailId;
    
    /**
    * 
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("")
	private String testId;
    
 }