package com.sinoyd.lims.lim.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * ItemRelation实体
 * <AUTHOR>
 * @version V1.0.0 2023/11/1
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="ItemRelation")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class ItemRelation implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  ItemRelation() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
       this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 分析项目Id
    */
    @Column(length=500,nullable=false)
    @ApiModelProperty("分析项目Id")
    private String leftFormula;
    
    /**
    * 分析项目名称
    */
    @Column(length=500)
    @ApiModelProperty("分析项目名称")
    @Length(message = "分析项目名称{validation.message.length}", max = 500)
    private String rightFormula;
    
    /**
    * 公式
    */
    @Column(length=500)
    @ApiModelProperty("公式")
    @Length(message = "公式{validation.message.length}", max = 500)
    private String formula;
    
    /**
    * 公式（1.等于，2.大于，3.小于等）
    */
    @Column(nullable=false)
    @ApiModelProperty("公式（1.等于，2.大于，3.小于等）")
    private Integer symbolType;
    
    /**
    * 配置日期
    */
    @Column(nullable=false)
    @ApiModelProperty("配置日期")
    private Date configDate;
    
    /**
    * 类型（枚举EnumAnalyzeItemRelationType：1.自检，2.上报）
    */
    @Column(nullable=false)
    @ApiModelProperty("类型（枚举EnumAnalyzeItemRelationType：1.自检，2.上报）")
    private Integer type;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }