package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文件查询条件
 * <AUTHOR>
 * @version V1.0.0 2019/3/12
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FolderCriteria extends BaseCriteria {
    private String key;

    private String parentId;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(parentId)) {
            condition.append(" and parentId = :parentId");
            values.put("parentId", parentId);
        }
        if (StringUtils.isNotNullAndEmpty(key)) {
            condition.append(" and folderName like :key");
            values.put("key", "%" + key + "%");
        }
        return condition.toString();
    }

}