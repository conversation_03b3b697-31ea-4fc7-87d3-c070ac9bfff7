package com.sinoyd.base.service.impl;

import com.sinoyd.base.dto.rcc.DtoSubstitute;
import com.sinoyd.base.repository.rcc.SubstituteRepository;
import com.sinoyd.base.service.SubstituteService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * Substitute操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
@Service
public class SubstituteServiceImpl extends BaseJpaServiceImpl<DtoSubstitute, String, SubstituteRepository> implements SubstituteService {

    /***
     * 分页查询
     *
     * @param page 分页条件
     * @param criteria 查询条件
     */
    @Override
    public void findByPage(PageBean<DtoSubstitute> page, BaseCriteria criteria) {
        // 设置查询的实体类名及别名
        page.setEntityName("DtoSubstitute a");
        // 设置查询返回的字段、实体别名表示所有字段
        page.setSelect("select a ");
        super.findByPage(page, criteria);
        List<DtoSubstitute> substituteList = page.getData();
        for (DtoSubstitute dtoSubstitute : substituteList) {
            dtoSubstitute.setDimensionName(StringUtil.isEmpty(dtoSubstitute.getDimensionName()) ? "" : dtoSubstitute.getDimensionName());
        }
        page.setData(substituteList);
    }

    /**
     * 新增替代物
     */
    @Transactional
    @Override
    public DtoSubstitute save(DtoSubstitute entity) {
        //判断替代物是否已存在
        if (checkCasCode(entity.getCasCode())) {
            throw new BaseException("已存在相同CAS号的替代物!");
        }
        entity.setIsDeleted(false);
        return super.save(entity);
    }

    /**
     * 修改替代物
     */
    @Transactional
    @Override
    public DtoSubstitute update(DtoSubstitute entity) {
        DtoSubstitute oriSubstitute = repository.findOne(entity.getId());
        if (StringUtil.isNull(oriSubstitute) || oriSubstitute.getIsDeleted()) {
            throw new BaseException("替代物不存在或已被删除!");
        }
        //修改了cas号则要校验重复
        if (!oriSubstitute.getCasCode().equals(entity.getCasCode())
                && checkCasCode(entity.getCasCode())) {
            throw new BaseException("已存在相同CAS号的替代物!");
        }
        return repository.save(entity);
    }

    /**
     * 根据主键id查询替代物
     */
    @Override
    public DtoSubstitute findOne(String id) {
        DtoSubstitute dtoSubstitute = super.findOne(id);
        if (StringUtil.isNull(dtoSubstitute) || dtoSubstitute.getIsDeleted()) {
            throw new BaseException("替代物信息不存在!");
        }
        return dtoSubstitute;
    }

    /**
     * 校验cas号是否已存在
     */
    private boolean checkCasCode(String casCode) {
        List<DtoSubstitute> oriCasSubstituteList = repository.findByCasCodeAndIsDeleted(casCode, false);
        if (StringUtil.isNotEmpty(oriCasSubstituteList)) {
            for (DtoSubstitute dto : oriCasSubstituteList) {
                if (casCode.equals(dto.getCasCode())) {
                    return true;
                }
            }
        }
        return false;
    }
}