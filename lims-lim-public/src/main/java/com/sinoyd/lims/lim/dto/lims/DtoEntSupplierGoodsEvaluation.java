package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.EntSupplierGoodsEvaluation;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoEntSupplierGoodsEvaluation实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_EntSupplierGoodsEvaluation")
@Data
@DynamicInsert
public class DtoEntSupplierGoodsEvaluation extends EntSupplierGoodsEvaluation {

    @Transient
    private String goodsName;
}