package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.repository.lims.InstrumentRepository;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.criteria.EnvironmentalRecordCriteria;
import com.sinoyd.lims.lim.dto.lims.*;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.*;
import com.sinoyd.lims.lim.service.EnvironmentalRecordService;
import com.sinoyd.lims.lim.service.InstrumentUseRecord2SampleService;
import com.sinoyd.lims.lim.service.InstrumentUseRecordService;
import com.sinoyd.lims.lim.service.TestService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 环境管理-环境信息
 *
 * <AUTHOR>
 * @version V1.0.0 2019/3/12
 * @since V100R001
 */
@Service
public class EnvironmentalRecordServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoEnvironmentalRecord, String, EnvironmentalRecordRepository> implements EnvironmentalRecordService {

    @Autowired
    @Lazy
    private InstrumentUseRecordService instrumentUseRecordService;

    @Autowired
    private InstrumentUseRecord2SampleRepository instrumentUseRecord2SampleRepository;

    @Autowired
    @Lazy
    private InstrumentUseRecord2SampleService instrumentUseRecord2SampleService;

    @Autowired
    private InstrumentUseRecordRepository instrumentUseRecordRepository;

    @Autowired
    @Lazy
    private TestService testService;

    @Autowired
    private InstrumentRepository instrumentRepository;

    @Autowired
    private EnvironmentalRecord2SampleRepository environmentalRecord2SampleRepository;

    @Autowired
    private EnvironmentalRecord2TestRepository environmentalRecord2TestRepository;

    /**
     * 分页查询
     */
    @Override
    public void findByPage(PageBean<DtoEnvironmentalRecord> page, BaseCriteria criteria) {
        page.setEntityName("DtoEnvironmentalRecord p");
        page.setSelect("select p");

        super.findByPage(page, criteria);

        List<DtoEnvironmentalRecord> datas = page.getData();

        EnvironmentalRecordCriteria envCriteria = (EnvironmentalRecordCriteria) criteria;

        if (!envCriteria.getObjectType().equals(EnumLIM.EnumEnvRecObjType.所有.getValue())) {
            if (StringUtil.isNotNull(page.getData()) && page.getData().size() > 0) {
                List<String> environmentalManageIds = page.getData().stream().map(DtoEnvironmentalRecord::getId).collect(Collectors.toList());
                List<DtoInstrumentUseRecord> useRecords = instrumentUseRecordService.findByEnvironmentalManageIds(environmentalManageIds);
                if (envCriteria.getObjectType().equals(EnumLIM.EnumEnvRecObjType.采样.getValue())) {
                    useRecords = useRecords.stream().filter(p -> p.getObjectType().equals(EnumLIM.EnumEnvRecObjType.采样.getValue())).collect(Collectors.toList());
                    this.findExpandDataSampling(page, useRecords);
                } else if (envCriteria.getObjectType().equals(EnumLIM.EnumEnvRecObjType.现场分析.getValue())) {
                    useRecords = useRecords.stream().filter(p -> p.getObjectType().equals(EnumLIM.EnumEnvRecObjType.现场分析.getValue())).collect(Collectors.toList());
                    this.newFindExpandDataLocal(page, useRecords);
                } else if (envCriteria.getObjectType().equals(EnumLIM.EnumEnvRecObjType.实验室分析.getValue())) {
                    useRecords = useRecords.stream().filter(p -> p.getObjectType().equals(EnumLIM.EnumEnvRecObjType.实验室分析.getValue())).collect(Collectors.toList());
                    this.findExpandDataAnalyse(page, useRecords);
                }
            }
        }
    }

    /**
     * 获取环境记录
     *
     * @param id 环境记录id
     * @return 环境记录
     */
    @Override
    public DtoEnvironmentalRecord findRecord(String id) {
        DtoEnvironmentalRecord dto = repository.findOne(id);
        if (StringUtil.isNotNull(dto)) {
            List<DtoInstrumentUseRecord> useRecords = instrumentUseRecordRepository.findByEnvironmentalManageId(id);
            useRecords = useRecords.stream().filter(p -> p.getObjectType().equals(dto.getObjectType())).collect(Collectors.toList());
            if (dto.getObjectType().equals(EnumLIM.EnumEnvRecObjType.采样.getValue()) || dto.getObjectType().equals(EnumLIM.EnumEnvRecObjType.现场分析.getValue())) {
                List<DtoInstrumentUseRecord2Sample> i2rList = instrumentUseRecord2SampleRepository.findByInstrumentUseRecordId(useRecords.stream().map(DtoInstrumentUseRecord::getId).findFirst().orElse(""));
                dto.setInstrumentUseRecord(useRecords);
                dto.setSampleIds(i2rList.stream().map(DtoInstrumentUseRecord2Sample::getSampleId).distinct().collect(Collectors.toList()));
                dto.setTestIds(useRecords.stream().map(DtoInstrumentUseRecord::getTestIds).distinct().collect(Collectors.toList()));
            } else if (dto.getObjectType().equals(EnumLIM.EnumEnvRecObjType.实验室分析.getValue())) {
                dto.setInstrumentUseRecord(useRecords);
            }
        }
        return dto;
    }

    @Transactional
    @Override
    public DtoEnvironmentalRecord save(DtoEnvironmentalRecord dto) {
        //采样记录、一个仪器一条使用记录
        //现场分析、一个仪器一个测试项目一条使用记录
        //实验室分析、一个仪器一条记录，对应检测单下所有测试项目
        if (dto.getObjectType().equals(EnumLIM.EnumEnvRecObjType.现场分析.getValue())
                && StringUtil.isEmpty(dto.getTestIds())) {
            throw new BaseException("请选择测试项目！");
        }
        DtoEnvironmentalRecord envRecord = super.findOne(dto.getId());
        List<String> testIds = dto.getTestIds();
//        if (dto.getObjectType().equals(EnumLIM.EnumEnvRecObjType.现场分析.getValue()) || dto.getObjectType().equals(EnumLIM.EnumEnvRecObjType.采样.getValue())) {
//            testIds = dto.getTestIds();
//        } else if (dto.getObjectType().equals(EnumLIM.EnumEnvRecObjType.实验室分析.getValue())) {
//            testIds = dto.getTestIds();
//            //实验室分析新增仪器使用记录不存储测试项目id列表
////            testIds.add("");
//        }
        List<DtoInstrumentUseRecord2Sample> i2sList = new ArrayList<>();

        List<DtoInstrumentUseRecord> useRecordList = new ArrayList<>();
        for (DtoInstrumentUseRecord useRecord : dto.getInstrumentUseRecord()) {
            for (String testId : testIds) {
                DtoInstrumentUseRecord record = new DtoInstrumentUseRecord();
                record.setTestIds(testId);
                record.setInsOriginDate(useRecord.getInsOriginDate());
                record.setBeforeUseSituation(useRecord.getBeforeUseSituation());
                record.setBeforeAfterSituation(useRecord.getBeforeAfterSituation());
                record.setIsAssistInstrument(useRecord.getIsAssistInstrument());
                record.setRemark(useRecord.getRemark());
                record.setInstrumentId(useRecord.getInstrumentId());
                record.setStartTime(dto.getStartTime());
                record.setEndTime(dto.getEndTime());
                record.setTemperature(dto.getTemperature());
                record.setHumidity(dto.getHumidity());
                record.setPressure(dto.getPressure());
                record.setObjectId(dto.getObjectId());
                record.setObjectType(dto.getObjectType());
                record.setEnvironmentalManageId(dto.getId());
                record.setUsePersonId(dto.getUsePersonId());
                useRecordList.add(record);
                if (StringUtil.isNotNull(dto.getSampleIds()) && dto.getSampleIds().size() > 0) {
                    for (String sampleId : dto.getSampleIds()) {
                        DtoInstrumentUseRecord2Sample i2s = new DtoInstrumentUseRecord2Sample();
                        i2s.setSampleId(sampleId);
                        i2s.setInstrumentUseRecordId(record.getId());
                        i2sList.add(i2s);
                    }
                }
            }
        }
        List<DtoEnvironmentalRecord2Test> waitSaveRecord2TestList = new ArrayList<>();
        List<DtoEnvironmentalRecord2Sample> waitSaveRecord2SampleList = new ArrayList<>();
        if (EnumLIM.EnumEnvRecObjType.现场分析.getValue().equals(dto.getObjectType())) {
            for (String testId : testIds) {
                DtoEnvironmentalRecord2Test environmentalRecord2Test = new DtoEnvironmentalRecord2Test();
                environmentalRecord2Test.setEnvironmentalRecordId(dto.getId());
                environmentalRecord2Test.setTestId(testId);
                waitSaveRecord2TestList.add(environmentalRecord2Test);
            }
            for (String sampleId : dto.getSampleIds()) {
                DtoEnvironmentalRecord2Sample environmentalRecord2Sample = new DtoEnvironmentalRecord2Sample();
                environmentalRecord2Sample.setEnvironmentalRecordId(dto.getId());
                environmentalRecord2Sample.setSampleId(sampleId);
                waitSaveRecord2SampleList.add(environmentalRecord2Sample);
            }
        }
        if (StringUtil.isNotNull(envRecord)) {//若存在该环境记录，需先删除该环境信息下的仪器使用记录及关联
            List<String> environmentalManageIds = new ArrayList<>();
            environmentalManageIds.add(envRecord.getId());
            this.deleteDetail(environmentalManageIds);
        }
        if (i2sList.size() > 0) {
            //保存仪器使用记录与样品的关联
            instrumentUseRecord2SampleService.save(i2sList);
        }

        if (useRecordList.size() > 0) {
            //保存仪器使用记录
            instrumentUseRecordService.save(useRecordList);
        }
        if (StringUtil.isNotEmpty(waitSaveRecord2TestList)) {
            environmentalRecord2TestRepository.save(waitSaveRecord2TestList);
        }
        if (StringUtil.isNotEmpty(waitSaveRecord2SampleList)) {
            environmentalRecord2SampleRepository.save(waitSaveRecord2SampleList);
        }
        if (StringUtil.isNull(envRecord)) {
            super.save(dto);//新增
        } else {
            super.update(dto);//更新
        }
        return dto;
    }

    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> envRecordIds = new ArrayList<>();
        for (Object id : ids) {
            envRecordIds.add(String.valueOf(id));
        }
        this.deleteDetail(envRecordIds);
        return super.logicDeleteById(envRecordIds);
    }

    @Transactional
    public void deleteDetail(List<String> environmentalManageIds) {
        List<DtoInstrumentUseRecord> insRecords = instrumentUseRecordService.findByEnvironmentalManageIds(environmentalManageIds);
        if (insRecords.size() > 0) {
            List<String> insRecordIdList = insRecords.stream().map(DtoInstrumentUseRecord::getId).collect(Collectors.toList());
            instrumentUseRecordService.logicDeleteById(insRecordIdList);
            instrumentUseRecord2SampleRepository.deleteByInstrumentUseRecordIdIn(insRecordIdList);
        }
        List<DtoEnvironmentalRecord2Sample> waitDelRecord2SampleList = environmentalRecord2SampleRepository.findAllByEnvironmentalRecordIdIn(environmentalManageIds);
        environmentalRecord2SampleRepository.delete(waitDelRecord2SampleList);
        List<DtoEnvironmentalRecord2Test> waitDelRecord2TestList = environmentalRecord2TestRepository.findAllByEnvironmentalRecordIdIn(environmentalManageIds);
        environmentalRecord2TestRepository.delete(waitDelRecord2TestList);
    }

    /**
     * 获取与指定环境记录相冲突的仪器使用记录
     *
     * @param record 环境记录
     */
    @Override
    public List<DtoInstrumentUseRecord> getConflictRecords(DtoEnvironmentalRecord record) {
        List<String> instrumentIds = record.getInstrumentUseRecord().stream().map(DtoInstrumentUseRecord::getInstrumentId).collect(Collectors.toList());
        if (StringUtil.isEmpty(instrumentIds)) {
            throw new BaseException("未选择仪器");
        }
        List<DtoInstrumentUseRecord> useRecords = instrumentUseRecordService.findByInstrumentIdInAndObjectTypeAndStartTimeBeforeAndEndTimeAfter(instrumentIds,
                record.getObjectType(), record.getStartTime(), record.getEndTime());

        //2022-06-09 排除操作人和当前工作单
        useRecords = useRecords.stream().filter(p -> !p.getObjectId().equals(record.getObjectId()) &&
                !p.getStartTime().after(record.getEndTime()) &&
                !p.getEndTime().before(record.getStartTime()) &&
                !p.getUsePersonId().equals(record.getUsePersonId())).collect(Collectors.toList());

        return useRecords;
    }

    @Override
    @Transactional
    public DtoEnvironmentalRecord newBatchSave(DtoEnvironmentalRecord environmentalRecord) {
        List<String> batchSaveIds = environmentalRecord.getBatchSaveIds();
        if (batchSaveIds != null && !batchSaveIds.isEmpty()) {
            //批量设置
            List<DtoEnvironmentalRecord> originList = repository.findAll(batchSaveIds);
            newBatchSavePropertyCopy(environmentalRecord, originList);
            repository.save(originList);
        } else {
            //行编辑
            // 获取仪器使用记录,同步温度湿度时间
            List<DtoInstrumentUseRecord> instrumentUseRecordList = instrumentUseRecordRepository.findByEnvironmentalManageId(environmentalRecord.getId());
            if (StringUtil.isNotEmpty(instrumentUseRecordList)) {
                instrumentUseRecordList.forEach(record -> {
                    record.setHumidity(environmentalRecord.getHumidity());
                    record.setTemperature(environmentalRecord.getTemperature());
                    record.setPressure(environmentalRecord.getPressure());
                    record.setStartTime(environmentalRecord.getStartTime());
                    record.setEndTime(environmentalRecord.getEndTime());
                });
                instrumentUseRecordRepository.save(instrumentUseRecordList);
            }
            repository.save(environmentalRecord);
        }
        return null;
    }

    @Override
    @Transactional
    public List<DtoEnvironmentalRecord> addByLocalTests(String subId, List<DtoTest> testList) {
        List<DtoEnvironmentalRecord> environmentalRecordList = new ArrayList<>();
        List<DtoEnvironmentalRecord2Test> waitSaveRecord2TestList = new ArrayList<>();
        for (DtoTest test : testList) {
            DtoEnvironmentalRecord environmentalRecord = new DtoEnvironmentalRecord();
            environmentalRecord.setObjectId(subId);
            environmentalRecord.setObjectType(EnumLIM.EnumEnvRecObjType.现场分析.getValue());
            environmentalRecordList.add(environmentalRecord);

            DtoEnvironmentalRecord2Test environmentalRecord2Test = new DtoEnvironmentalRecord2Test();
            environmentalRecord2Test.setEnvironmentalRecordId(environmentalRecord.getId());
            environmentalRecord2Test.setTestId(test.getId());
            waitSaveRecord2TestList.add(environmentalRecord2Test);
        }
        environmentalRecord2TestRepository.save(waitSaveRecord2TestList);
        return repository.save(environmentalRecordList);
    }

    @Override
    @Transactional
    public DtoEnvironmentalRecord copy(DtoEnvironmentalRecord environmentalRecord) {
        String sourceId = environmentalRecord.getSourceId();
        List<String> targetIds = environmentalRecord.getTargetIds();
        DtoEnvironmentalRecord sourceEnv = repository.findOne(sourceId);
        List<DtoEnvironmentalRecord> targetEnvList = repository.findAll(targetIds);
        // 先删除环境关联仪器使用记录数据
        List<DtoInstrumentUseRecord> instrumentUseRecordList = instrumentUseRecordRepository.findByEnvironmentalManageIdIn(targetIds);
        if (StringUtil.isNotEmpty(instrumentUseRecordList)) {
            List<String> instrumentUseRecordIdList = instrumentUseRecordList.stream().map(DtoInstrumentUseRecord::getId).collect(Collectors.toList());
            List<DtoInstrumentUseRecord2Sample> instrumentUseRecord2SampleList = instrumentUseRecord2SampleRepository.findByInstrumentUseRecordIdIn(instrumentUseRecordIdList);
            instrumentUseRecord2SampleRepository.delete(instrumentUseRecord2SampleList);
            instrumentUseRecordRepository.delete(instrumentUseRecordList);
        }
        // 仪器使用记录数据源
        List<DtoInstrumentUseRecord> sourceInstrumentUseRecodeList = instrumentUseRecordRepository.findByEnvironmentalManageId(sourceId);
        List<DtoEnvironmentalRecord2Test> environmentalRecord2Tests = environmentalRecord2TestRepository.findAllByEnvironmentalRecordIdIn(targetIds);
        Map<String, List<DtoEnvironmentalRecord2Test>> env2TestMap = environmentalRecord2Tests.stream().collect(Collectors.groupingBy(DtoEnvironmentalRecord2Test::getEnvironmentalRecordId));

        List<DtoInstrumentUseRecord> insertInstrumentUseList = new ArrayList<>();
        // 环境数据复制
        for (DtoEnvironmentalRecord targetEnv : targetEnvList) {
            targetEnv.setTemperature(sourceEnv.getTemperature());
            targetEnv.setHumidity(sourceEnv.getHumidity());
            targetEnv.setTemperature(sourceEnv.getTemperature());
            targetEnv.setPressure(sourceEnv.getPressure());
            targetEnv.setStartTime(sourceEnv.getStartTime());
            targetEnv.setEndTime(sourceEnv.getEndTime());
            // 仪器使用记录复制
            List<String> testIds = env2TestMap.get(targetEnv.getId()).stream().map(DtoEnvironmentalRecord2Test::getTestId).collect(Collectors.toList());
            for (String testId : testIds) {
                for (DtoInstrumentUseRecord instrumentUseRecord : sourceInstrumentUseRecodeList) {
                    DtoInstrumentUseRecord insert = new DtoInstrumentUseRecord();
                    BeanUtils.copyProperties(instrumentUseRecord, insert, "id", "testId", "environmentalManageId");
                    insert.setTestIds(testId);
                    insert.setEnvironmentalManageId(targetEnv.getId());
                    insertInstrumentUseList.add(insert);
                }
            }
        }
        repository.save(targetEnvList);
        instrumentUseRecordRepository.save(insertInstrumentUseList);
        return environmentalRecord;
    }

    /**
     * 关联数据调整
     *
     * @param environmentalRecord 属性
     * @param originList          原始数据
     */
    private void newBatchSavePropertyCopy(DtoEnvironmentalRecord environmentalRecord, List<DtoEnvironmentalRecord> originList) {
        List<String> originIds = originList.stream().map(DtoEnvironmentalRecord::getId).collect(Collectors.toList());
        //判断关联样品是否有变化
        List<String> sampleIds = environmentalRecord.getSampleIds();
        boolean sampleChangeFlag = sampleIds != null && !sampleIds.isEmpty();
        List<DtoEnvironmentalRecord2Sample> waitDelRecord2SampleList = environmentalRecord2SampleRepository.findAllByEnvironmentalRecordIdIn(originIds);
        if (sampleChangeFlag) {
            //删除原来与样品的关联
            environmentalRecord2SampleRepository.delete(waitDelRecord2SampleList);
        }
        //判断关联测试项目是否有变化
        List<String> testIds = environmentalRecord.getTestIds();
        boolean testChangeFlag = testIds != null && !testIds.isEmpty();
        List<DtoEnvironmentalRecord2Test> waitDelRecord2TestList = environmentalRecord2TestRepository.findAllByEnvironmentalRecordIdIn(originIds);
        if (testChangeFlag) {
            //删除原来与测试项目的关联
            environmentalRecord2TestRepository.delete(waitDelRecord2TestList);
        }
        //样品变换或测试项目变化还需同时调整仪器相关的数据
        List<DtoInstrumentUseRecord> instrumentUseRecordList = instrumentUseRecordRepository.findByEnvironmentalManageIdIn(originIds);
        if (sampleChangeFlag || testChangeFlag) {
            List<String> instrumentUseRecordIdList = instrumentUseRecordList.stream().map(DtoInstrumentUseRecord::getId).collect(Collectors.toList());
            List<DtoInstrumentUseRecord2Sample> instrumentUseRecord2SampleList = instrumentUseRecord2SampleRepository.findByInstrumentUseRecordIdIn(instrumentUseRecordIdList);
            instrumentUseRecord2SampleRepository.delete(instrumentUseRecord2SampleList);
            instrumentUseRecordRepository.delete(instrumentUseRecordList);
        }
        //新增与样品关联容器
        List<DtoEnvironmentalRecord2Sample> waitSaveRecord2SampleList = new ArrayList<>();
        //新增与测试项目关联容器
        List<DtoEnvironmentalRecord2Test> waitSaveRecord2TestList = new ArrayList<>();
        //仪器使用记录容器
        List<DtoInstrumentUseRecord> waitSaveUseRecordList = new ArrayList<>();
        List<DtoInstrumentUseRecord2Sample> i2sList = new ArrayList<>();
        for (DtoEnvironmentalRecord origin : originList) {
            //属性字段
            origin.setStartTime(environmentalRecord.getStartTime());
            origin.setEndTime(environmentalRecord.getEndTime());
            origin.setTemperature(environmentalRecord.getTemperature());
            origin.setHumidity(environmentalRecord.getHumidity());
            origin.setPressure(environmentalRecord.getPressure());
            //与样品关联
            if (sampleChangeFlag) {
                for (String sampleId : sampleIds) {
                    DtoEnvironmentalRecord2Sample environmentalRecord2Sample = new DtoEnvironmentalRecord2Sample();
                    environmentalRecord2Sample.setEnvironmentalRecordId(origin.getId());
                    environmentalRecord2Sample.setSampleId(sampleId);
                    waitSaveRecord2SampleList.add(environmentalRecord2Sample);
                }
            }
            //与测试项目关联
            if (testChangeFlag) {
                for (String testId : testIds) {
                    DtoEnvironmentalRecord2Test environmentalRecord2Test = new DtoEnvironmentalRecord2Test();
                    environmentalRecord2Test.setEnvironmentalRecordId(origin.getId());
                    environmentalRecord2Test.setTestId(testId);
                    waitSaveRecord2TestList.add(environmentalRecord2Test);
                }
            }

            List<String> oldTestIdsList = waitDelRecord2TestList.stream().map(DtoEnvironmentalRecord2Test::getTestId).distinct().collect(Collectors.toList());
            List<String> oldSampleIds = waitDelRecord2SampleList.stream().map(DtoEnvironmentalRecord2Sample::getSampleId).distinct().collect(Collectors.toList());
            List<String> useRecordTestIds = testChangeFlag ? testIds : oldTestIdsList;
            List<String> useRecordSampleIds = testChangeFlag ? sampleIds : oldSampleIds;
            //原数据是 一仪器一测试项目 一记录 要分组过滤
            Map<String, List<DtoInstrumentUseRecord>> instrumentMap = instrumentUseRecordList.stream().filter(r -> origin.getId().equals(r.getEnvironmentalManageId()))
                    .collect(Collectors.groupingBy(DtoInstrumentUseRecord::getInstrumentId));
            List<DtoInstrumentUseRecord> useRecordList = new ArrayList<>();
            instrumentMap.forEach((k, v) -> useRecordList.add(v.get(0)));
            for (DtoInstrumentUseRecord useRecord : useRecordList) {
                for (String testId : useRecordTestIds) {
                    DtoInstrumentUseRecord record = new DtoInstrumentUseRecord();
                    record.setTestIds(testId);
                    record.setInsOriginDate(useRecord.getInsOriginDate());
                    record.setBeforeUseSituation(useRecord.getBeforeUseSituation());
                    record.setBeforeAfterSituation(useRecord.getBeforeAfterSituation());
                    record.setIsAssistInstrument(useRecord.getIsAssistInstrument());
                    record.setRemark(useRecord.getRemark());
                    record.setInstrumentId(useRecord.getInstrumentId());
                    record.setStartTime(environmentalRecord.getStartTime());
                    record.setEndTime(environmentalRecord.getEndTime());
                    record.setTemperature(environmentalRecord.getTemperature());
                    record.setHumidity(environmentalRecord.getHumidity());
                    record.setPressure(environmentalRecord.getPressure());
                    record.setObjectId(origin.getObjectId());
                    record.setObjectType(origin.getObjectType());
                    record.setEnvironmentalManageId(origin.getId());
                    record.setUsePersonId(origin.getUsePersonId());
                    waitSaveUseRecordList.add(record);
                    if (StringUtil.isNotNull(useRecordSampleIds) && !useRecordSampleIds.isEmpty()) {
                        for (String sampleId : useRecordSampleIds) {
                            DtoInstrumentUseRecord2Sample i2s = new DtoInstrumentUseRecord2Sample();
                            i2s.setSampleId(sampleId);
                            i2s.setInstrumentUseRecordId(record.getId());
                            i2sList.add(i2s);
                        }
                    }
                }
            }

        }
        environmentalRecord2SampleRepository.save(waitSaveRecord2SampleList);
        environmentalRecord2TestRepository.save(waitSaveRecord2TestList);
        instrumentUseRecord2SampleService.save(i2sList);
        instrumentUseRecordService.save(waitSaveUseRecordList);
    }

    /**
     * 绑定采样仪器使用记录
     *
     * @param page       数据
     * @param useRecords 仪器使用记录
     */
    private void findExpandDataSampling(PageBean<DtoEnvironmentalRecord> page, List<DtoInstrumentUseRecord> useRecords) {
        //与现场分析一样处理
        this.findExpandDataLocal(page, useRecords);
    }

    /**
     * 绑定现场分析仪器使用记录
     *
     * @param page       数据
     * @param useRecords 仪器使用记录
     */
    private void findExpandDataLocal(PageBean<DtoEnvironmentalRecord> page, List<DtoInstrumentUseRecord> useRecords) {
        loadInspectResult(useRecords);
        //按照环境信息id分组随机取一个仪器使用记录id，然后获取对应的仪器使用记录与样品关联的数据
        List<String> testIdList = useRecords.stream().map(DtoInstrumentUseRecord::getTestIds).distinct().collect(Collectors.toList());
        List<DtoTest> testList = testService.findRedisByIds(testIdList);
        List<String> useRecordIds = useRecords.stream().collect(Collectors.groupingBy(DtoInstrumentUseRecord::getEnvironmentalManageId,
                Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0)))).values()
                .stream().map(DtoInstrumentUseRecord::getId).collect(Collectors.toList());
        List<DtoInstrumentUseRecord2Sample> i2sList = instrumentUseRecord2SampleRepository.findByInstrumentUseRecordIdIn(useRecordIds);

        Map<String, List<DtoInstrumentUseRecord>> recordMap = useRecords.stream().collect(Collectors.groupingBy(DtoInstrumentUseRecord::getEnvironmentalManageId));
        for (DtoEnvironmentalRecord record : page.getData()) {
            if (recordMap.containsKey(record.getId())) {
                List<DtoInstrumentUseRecord> list = recordMap.get(record.getId());
                //testIds绑定的为一条测试项目id
                List<String> testIds = list.stream().map(DtoInstrumentUseRecord::getTestIds).distinct().collect(Collectors.toList());
                record.setTestIds(testIds);
                record.setRedAnalyzeItemNames(String.join(",", testList.stream().filter(p -> testIds.contains(p.getId())).map(DtoTest::getRedAnalyzeItemName).collect(Collectors.toList())));
                record.setInstrumentUseRecord(list.stream().filter(p -> p.getTestIds().equals(testIds.get(0))).collect(Collectors.toList()));

                List<String> recordIds = list.stream().map(DtoInstrumentUseRecord::getId).collect(Collectors.toList());
                record.setSampleIds(
                        i2sList.stream().filter(p -> recordIds.contains(p.getInstrumentUseRecordId())).map(DtoInstrumentUseRecord2Sample::getSampleId).distinct().collect(Collectors.toList())
                );
            }
        }
    }

    /**
     * 绑定现场分析仪器使用记录
     *
     * @param page 数据
     */
    private void newFindExpandDataLocal(PageBean<DtoEnvironmentalRecord> page, List<DtoInstrumentUseRecord> useRecords) {
        List<DtoEnvironmentalRecord> list = page.getData();
        if (!list.isEmpty()) {
            List<String> environmentalRecordIds = list.stream().map(DtoEnvironmentalRecord::getId).collect(Collectors.toList());
            List<DtoEnvironmentalRecord2Sample> allRecord2SampleList = environmentalRecord2SampleRepository.findAllByEnvironmentalRecordIdIn(environmentalRecordIds);
            List<DtoEnvironmentalRecord2Test> allRecord2TestList = environmentalRecord2TestRepository.findAllByEnvironmentalRecordIdIn(environmentalRecordIds);
            List<String> allTestIds = allRecord2TestList.stream().map(DtoEnvironmentalRecord2Test::getTestId).collect(Collectors.toList());
            List<DtoTest> allTestList = testService.findRedisByIds(allTestIds);
            for (DtoEnvironmentalRecord record : list) {
                List<String> testIds = allRecord2TestList.stream().filter(r -> record.getId().equals(r.getEnvironmentalRecordId())).map(DtoEnvironmentalRecord2Test::getTestId).collect(Collectors.toList());
                record.setTestIds(testIds);
                record.setRedAnalyzeItemNames(allTestList.stream().filter(p -> testIds.contains(p.getId()))
                        .map(DtoTest::getRedAnalyzeItemName).distinct().sorted().collect(Collectors.joining(",")));
                record.setSampleIds(
                        allRecord2SampleList.stream().filter(r -> record.getId().equals(r.getEnvironmentalRecordId())).map(DtoEnvironmentalRecord2Sample::getSampleId)
                                .collect(Collectors.toList())
                );
                List<DtoInstrumentUseRecord> singleUseRecordList = useRecords.stream().filter(r -> record.getId().equals(r.getEnvironmentalManageId())).collect(Collectors.toList());
                Map<String, List<DtoInstrumentUseRecord>> instrumentMap = singleUseRecordList.stream().collect(Collectors.groupingBy(DtoInstrumentUseRecord::getInstrumentId));
                List<DtoInstrumentUseRecord> useRecordList = new ArrayList<>();
                instrumentMap.forEach((k, v) -> useRecordList.add(v.get(0)));
                record.setInstrumentUseRecord(useRecordList.stream().sorted(Comparator.nullsLast(Comparator.comparing(DtoInstrumentUseRecord::getInstrumentName))).collect(Collectors.toList()));
            }
        }
    }

    /**
     * 绑定实验室分析仪器使用记录
     *
     * @param page       数据
     * @param useRecords 仪器使用记录
     */
    private void findExpandDataAnalyse(PageBean<DtoEnvironmentalRecord> page, List<DtoInstrumentUseRecord> useRecords) {
        loadInspectResult(useRecords);
        Set<String> tIds = useRecords.stream().map(DtoInstrumentUseRecord::getTestIdArray).flatMap(Collection::stream).collect(Collectors.toSet());
        List<DtoTest> testList = testService.findAll(tIds);
        Map<String, List<DtoInstrumentUseRecord>> recordMap = useRecords.stream().collect(Collectors.groupingBy(DtoInstrumentUseRecord::getEnvironmentalManageId));
        for (DtoEnvironmentalRecord record : page.getData()) {
            record.setRedAnalyzeItemNames("");
            if (recordMap.containsKey(record.getId())) {
                List<DtoInstrumentUseRecord> list = recordMap.get(record.getId());
                List<String> testIds = list.stream().map(DtoInstrumentUseRecord::getTestIds).distinct().collect(Collectors.toList());
                // 旧数据
                if (testIds.stream().allMatch(StringUtil::isEmpty)) {
                    List<String> oldTestIds = list.stream().flatMap(p -> p.getTestIdArray().stream()).distinct().collect(Collectors.toList());
                    record.setRedAnalyzeItemNames(testList.stream().filter(p -> oldTestIds.contains(p.getId())).map(DtoTest::getRedAnalyzeItemName).distinct().collect(Collectors.joining(";")));
                    record.setTestIds(oldTestIds);
                } else {
                    record.setRedAnalyzeItemNames(testList.stream().filter(p -> testIds.contains(p.getId())).map(DtoTest::getRedAnalyzeItemName).distinct().collect(Collectors.joining(";")));
                    record.setTestIds(testIds);
                }
                record.setInstrumentUseRecord(list.stream().filter(p -> p.getTestIds().equals(testIds.get(0))).collect(Collectors.toList()));
            }
        }
    }

    /**
     * 加载检定结果
     *
     * @param useRecords 使用记录
     */
    private void loadInspectResult(List<DtoInstrumentUseRecord> useRecords) {
        if (!useRecords.isEmpty()) {
            List<String> instrumentIds = useRecords.parallelStream().map(DtoInstrumentUseRecord::getInstrumentId).distinct().collect(Collectors.toList());
            List<DtoInstrument> instruments = instrumentRepository.findAll(instrumentIds);
            useRecords.forEach(p -> {
                Optional<DtoInstrument> instrumentOptional = instruments.parallelStream().filter(m -> m.getId().equals(p.getInstrumentId())).findFirst();
                instrumentOptional.ifPresent(m -> p.setInspectResult(m.getOriginResult()));
            });
        }
    }
}