package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoOADepartmentExpend;
import com.sinoyd.lims.pro.dto.DtoOATask;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;
import com.sinoyd.lims.pro.entity.OATask;

/**
 * 部门支出
   业务操作接口
 * <AUTHOR>
 * @version V1.0.0 2019-03-26
 * @since V100R001
 */
public interface OADepartmentExpendService extends IBaseJpaService<DtoOADepartmentExpend, String> {
    /**
     * 添加部门支出并启动流程
     *
     * @param taskDto 参数实体
     * @return 流程实例ID
     */
    String startProcess(DtoOATaskCreate<DtoOADepartmentExpend> taskDto);

    /**
     * 查询任务详细信息
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    DtoOATaskDetail<DtoOADepartmentExpend, String> findOATaskDetail(String taskId);


    /**
     * 对部门支出进行确认
     *
     * @param id 主键id
     */
    void confirm(String id);


    /**
     * 同步部分支出
     *
     * @param id     主键id
     * @param oaTask 任务
     */
    void syncOtherExpenditure(String id, DtoOATask oaTask);

    /**
     * 保存为草稿
     * @param taskDto  表单数据
     * @return         oa任务
     */
    OATask saveAsDraft(DtoOATaskCreate<DtoOADepartmentExpend> taskDto);

    /**
     * 保存为草稿
     * @param taskDto  表单数据
     * @return         oa任务
     */
    OATask draftSave(DtoOATaskCreate<DtoOADepartmentExpend> taskDto);

    /**
     * 草稿提交
     * @param taskDto  表单数据
     * @return         oa任务
     */
    String draftSubmit(DtoOATaskCreate<DtoOADepartmentExpend> taskDto);
}
