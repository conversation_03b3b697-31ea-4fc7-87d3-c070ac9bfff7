package com.sinoyd.lims.foreign.service.impl;

import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.foreign.DataInstrumentUseRecordVO;
import com.sinoyd.lims.foreign.service.DataInstrumentUseRecordService;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 仪器信息接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Service
public class DataInstrumentUseRecordServiceImpl extends AbsQueryDataServiceImpl<DataInstrumentUseRecordVO> implements DataInstrumentUseRecordService {

    private PersonRepository personRepository;

    @Override
    public StringBuilder getSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("select t2.instrumentName,t2.model,t2.instrumentsCode,t2.serialNo,t1.* ");
        sql.append(" from  TB_LIM_InstrumentUseRecord t1 INNER JOIN TB_BASE_Instrument t2 on t1.instrumentId =  t2.id ");
        sql.append(" WHERE 1 = 1 ");
        return sql;
    }

    @Override
    protected void appendCondition(StringBuilder sql, Map<String, Object> map, String condition, String paramName) {
        super.appendCondition(sql, map, "AND (t1.modifyDate > :syncTime or t2.modifyDate > :syncTime) ", "syncTime");
        super.appendCondition(sql, map, "AND t1.objectId in (:objectIds)", "objectIds");

    }

    @Override
    protected RowMapper<DataInstrumentUseRecordVO> getEntityRowMapper() {
        return (rs, i) -> {
            DataInstrumentUseRecordVO instrumentUseRecordVO = new DataInstrumentUseRecordVO();
            instrumentUseRecordVO.setId(UUIDHelper.NewID());
            instrumentUseRecordVO.setInstrumentId(rs.getString("instrumentId"));
            instrumentUseRecordVO.setInstrumentName(rs.getString("instrumentName"));
            instrumentUseRecordVO.setInstrumentModel(rs.getString("model"));
            instrumentUseRecordVO.setInstrumentCode(rs.getString("instrumentsCode"));
            instrumentUseRecordVO.setInstrumentSerialNo(rs.getString("serialNo"));
            instrumentUseRecordVO.setObjectId(rs.getString("objectId"));
            instrumentUseRecordVO.setObjectType(rs.getInt("objectType"));
            instrumentUseRecordVO.setUsePersonName(rs.getString("usePersonId"));
            instrumentUseRecordVO.setStartTime(rs.getDate("startTime"));
            instrumentUseRecordVO.setEndTime(rs.getDate("endTime"));
            instrumentUseRecordVO.setTestIds(rs.getString("testIds"));
            instrumentUseRecordVO.setTemperature(rs.getString("temperature"));
            instrumentUseRecordVO.setHumidity(rs.getString("humidity"));
            instrumentUseRecordVO.setPressure(rs.getString("pressure"));
            instrumentUseRecordVO.setBeforeUseSituation(rs.getString("beforeUseSituation"));
            instrumentUseRecordVO.setBeforeAfterSituation(rs.getString("beforeAfterSituation"));
            instrumentUseRecordVO.setIsAssistInstrument(rs.getBoolean("isAssistInstrument"));
            instrumentUseRecordVO.setOrgId(rs.getString("orgId"));
            instrumentUseRecordVO.setDomainId(rs.getString("domainId"));
            instrumentUseRecordVO.setSyncTime(new Date());
            return instrumentUseRecordVO;
        };
    }

    @Override
    protected void individuationData(Map<String, Object> map,List<DataInstrumentUseRecordVO> list) {
        List<DtoPerson> personList = personRepository.findAllDeleted();
        Map<String, String> personMap = personList.stream().collect(Collectors.toMap(DtoPerson::getId, DtoPerson::getCName));
        list.forEach(p -> p.setUsePersonName(personMap.getOrDefault(p.getUsePersonName(), "")));
    }


    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }
}
