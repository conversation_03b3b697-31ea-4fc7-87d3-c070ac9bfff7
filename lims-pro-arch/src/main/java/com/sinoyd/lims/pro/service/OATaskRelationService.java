package com.sinoyd.lims.pro.service;

import java.util.List;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoOATaskRelation;

/**
 * 审批任务关联业务业务操作接口
 * <AUTHOR>
 * @version V1.0.0 2019-03-26
 * @since V100R001
 */
public interface OATaskRelationService extends IBaseJpaService<DtoOATaskRelation, String>
{
    /**
     * 根据任务Id查询关联信息
     * @param taskId 审批任务ID
     * @return
     */
    DtoOATaskRelation findByTaskId(String taskId);

    /**
     * 根据任务Id查询关联列表信息
     * @param taskId 审批任务ID
     * @return
     */
    List<DtoOATaskRelation> findListByTaskId(String taskId);
}
