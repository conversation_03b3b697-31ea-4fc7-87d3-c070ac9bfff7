package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.ProjectPlan2Person;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * DtoProjectPlan2Person实体
 * <AUTHOR>
 * @version V1.0.0 2024/03/05
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "tb_pro_projectplan2person")
 @Data
 @DynamicInsert
 public  class DtoProjectPlan2Person extends ProjectPlan2Person {
}