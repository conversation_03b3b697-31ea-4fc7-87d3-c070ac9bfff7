package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoMpnConfig;

import java.util.List;

/**
 * MpnConfig仓储
 *
 * <AUTHOR>
 * @version V1.0.0 2025/02/12
 * @since V100R001
 */
public interface MpnConfigRepository extends IBaseJpaPhysicalDeleteRepository<DtoMpnConfig, String> {


    /**
     * 根据测试项目ids查询MNP配置
     *
     * @param testIds 测试项目ids
     * @return MNP配置集合
     */
    List<DtoMpnConfig> findByTestIdIn(List<String> testIds);
}
