package com.sinoyd.lims.pro.dto;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.dto.customer.DtoReceiveSampleRecordTemp;
import com.sinoyd.lims.pro.entity.ReceiveSampleRecord;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * DtoReceiveSampleRecord实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_ReceiveSampleRecord")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoReceiveSampleRecord extends ReceiveSampleRecord {
    private static final long serialVersionUID = 1L;

    public void loadFromTemp(DtoReceiveSampleRecordTemp temp) {
        if (StringUtil.isNotNull(temp.getSenderId())) {
            this.setSenderId(temp.getSenderId());
        }
        if (StringUtil.isNotNull(temp.getSenderName())) {
            this.setSenderName(temp.getSenderName());
        }
        if (StringUtil.isNotNull(temp.getSendTime())) {
            this.setSendTime(temp.getSendTime());
        }
        if (StringUtil.isNotNull(temp.getSamplingTime())) {
            this.setSamplingTime(temp.getSamplingTime());
        }
        if (StringUtil.isNotNull(temp.getReceiveType())) {
            this.setReceiveType(temp.getReceiveType());
        }
        if (StringUtil.isNotNull(temp.getProjectId())) {
            this.setProjectId(temp.getProjectId());
        }
        if (StringUtil.isNotNull(temp.getReceiveRemark())) {
            this.setRemark(temp.getReceiveRemark());
        }
        if (StringUtil.isNotNull(temp.getSamplingPersonIds())) {
            this.samplingPersonIds = temp.getSamplingPersonIds();
        }
        if (StringUtil.isNotNull(temp.getReceiveType())) {
            this.carIds = temp.getCarIds();
        }
    }

    /**
     * 采样人id集合
     */
    @Transient
    private List<String> samplingPersonIds = new ArrayList<>();

    /**
     * 采样人名称集合
     */
    @Transient
    private List<String> samplingPersonNames = new ArrayList<>();
    /**
     * 采样车辆集合
     */
    @Transient
    private List<String> carIds = new ArrayList<>();

    /**
     * 采样车辆名称集合
     */
    @Transient
    private List<String> carNames = new ArrayList<>();

    /**
     * 样品个数
     */
    @Transient
    private Integer sampleNum;

    /**
     * 是否复制样品
     */
    @Transient
    private Boolean isCopySample = false;

    /**
     * 检测类型ids(逗号隔开)
     */
    @Transient
    private String sampleTypeIds;

    /**
     * 检测类型
     */
    @Transient
    private List<DtoSampleType> sampleTypes = new ArrayList<>();

    /**
     * 项目编号
     */
    @Transient
    private String projectCode;

    /**
     * 项目名称
     */
    @Transient
    private String projectName;

    /**
     * 项目类型id
     */
    @Transient
    private String projectTypeId;

    /**
     * 项目类型编码
     */
    @Transient
    private String projectTypeCode;

    /**
     * 项目类型名称
     */
    @Transient
    private String projectTypeName;

    /**
     * 项目等级
     */
    @Transient
    private Integer grade;

    /**
     * 委托方id
     */
    @Transient
    private String customerId;

    /**
     * 委托方名称
     */
    @Transient
    private String customerName;

    /**
     * 意见
     */
    @Transient
    private String comment;

    /**
     * 登记日期
     */
    @Transient
    private Date inceptTime;

    /**
     * 是否重点关注
     */
    @Transient
    private Boolean isStress;

    /**
     * 联系人
     */
    @Transient
    private String linkMan;

    /**
     * 联系方式
     */
    @Transient
    private String linkPhone;

    /**
     * 扫码个数
     */
    @Transient
    private Long scanCount;

    /**
     * 未扫码个数
     */
    @Transient
    private Long noScanCount;

    /**
     * 扫码总数
     */
    @Transient
    private Integer sunScanCount;

    /**
     * 最后结束时间
     */
    @Transient
    private Date deadLine;

    /**
     * 报告时间
     */
    @Transient
    private Date reportDate;

    /**
     * 复制次数(实际项目不用，只是为了测试组方便造数据使用)
     */
    @Transient
    private Integer copyTimes;

    /**
     * 项目类型名称
     */
    @Transient
    private String extendStr1;

    /**
     * 接样人名称
     */
    @Transient
    private String recipientName;

    /**
     * 受检方联系人
     */
    @Transient
    private String inspectedLinkMan;

    /**
     *  受检方联系电话
     */
    @Transient
    private String inspectedLinkPhone;

    /**
     *  受检方地址
     */
    @Transient
    private String inspectedAddress;

    /**
     * 受检单位
     */
    @Transient
    private String inspectedEnt;

    /**
     * 受检单位id
     */
    @Transient
    private String inspectedEntId;

    /**
     * 采样人集合
     */
    @Transient
    private List<DtoSamplingPersonConfig> samplingPersonList= new ArrayList<>();

    /**
     * 点位名称集合
     */
    @Transient
    private List<String> watchSpots;
}