package com.sinoyd.lims.foreign;

import lombok.Data;

import java.util.Date;

/**
* 报告样品信息
* <AUTHOR>
* @version V1.0.0 2023/11/7
* @since V100R001
*/
@Data
public class DataReportSampleInfoVO {
    /**
     * id
     */
    private String id;

    /**
     * 报告id
     */
    private String reportId;

    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 样品编号
     */
    private String sampleCode;

    /**
     * 样品备注
     */
    private String sampleRemark;

    /**
     * 报告点位信息
     */
    private String reportFolderId;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;
}
