package com.sinoyd.lims.pro.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;

/**
 * FlowCalibration实体
 * <AUTHOR>
 * @version V1.0.0
 * @since 2024/11/14
 */
@EqualsAndHashCode(callSuper = true)
@MappedSuperclass
@ApiModel(description="FlowCalibration")
@Data
@EntityListeners(AuditingEntityListener.class)
public class FlowCalibration extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public FlowCalibration() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 仪器id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("instrumentId")
    private String instrumentId;

    /**
     * 校准时间
     */
    @Column(nullable = false)
    @ColumnDefault("1753-01-01")
    @ApiModelProperty("校准时间")
    private Date calibrationDate;

    /**
     * 校准人id，多个英文逗号拼接
     */
    @Column(length = 1000, nullable = false)
    @ColumnDefault("''")
    @ApiModelProperty("校准人id，多个英文逗号拼接")
    //@NotBlank(message = "校准人id，多个英文逗号拼接{validation.message.blank}")
    @Length(message = "校准人id，多个英文逗号拼接{validation.message.length}", max = 1000)
    private String calibrationPeople;

    /**
     * 校准类型 字典 PRO_FlowCalibrationType
     */
    @ApiModelProperty("校准类型")
    private Integer calibrationType;

    /**
     * 校准类型名称
     */
    @Column(length = 50)
    @ApiModelProperty("校准类型名称")
    @Length(message = "校准类型名称{validation.message.length}", max = 50)
    private String calibrationTypeName;

    /**
     * 送样单标识,多个英文逗号拼接
     */
    @ApiModelProperty("送样单标识,多个英文逗号拼接")
    private String receiveId;

    /**
     * 假删字段
     */
    @Column(nullable = false)
    @ApiModelProperty("假删字段")
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;
}
