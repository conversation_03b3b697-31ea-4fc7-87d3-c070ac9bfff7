package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTemp;

import java.util.List;

/**
 *  SamplingFrequencyTempRepository
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2023/08/02
 */
public interface SamplingFrequencyTempRepository extends IBaseJpaPhysicalDeleteRepository<DtoSamplingFrequencyTemp, String> {

    /**
     * 获取点位下最大周期对应的频次
     *
     * @param sampleFolderTempId 点位id
     * @return 最大周期频次
     */
    List<DtoSamplingFrequencyTemp> findBySampleFolderTempIdOrderByPeriodCountDesc(String sampleFolderTempId);

    /**
     * 获取点位周期下最大次数对应的频次
     *
     * @param sampleFolderTempId 点位id
     * @param periodCount    周期
     * @return 最大次数频次
     */
    List<DtoSamplingFrequencyTemp> findBySampleFolderTempIdAndPeriodCountOrderByTimePerPeriodDesc(String sampleFolderTempId, Integer periodCount);

    /**
     * 获取点位下周期频次
     *
     * @param sampleFolderTempIds 点位模板id
     * @return List<DtoSamplingFrequencyTemp>
     */
    List<DtoSamplingFrequencyTemp> findBySampleFolderTempIdIn(List<String> sampleFolderTempIds);

    /**
     * 根据点位模板id删除数据
     *
     * @param sampleFolderTempIds 点位模板id
     */
    void deleteBySampleFolderTempIdIn(List<String> sampleFolderTempIds);

    /**
     * 查询对应点位周期下的频次
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @return 返回对应点位周期下的频次
     */
    List<DtoSamplingFrequencyTemp> findBySampleFolderTempIdAndPeriodCount(String sampleFolderId, Integer periodCount);

}
