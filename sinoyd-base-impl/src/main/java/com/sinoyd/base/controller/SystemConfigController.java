package com.sinoyd.base.controller;

import com.sinoyd.base.dto.lims.DtoSystemConfig;
import com.sinoyd.base.service.SystemConfigService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.frame.controller.BaseJpaController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 系统信息管理配置接口
 * <AUTHOR>
 * @version V1.0.0
 * @since 2022/12/8
 */
@Api(tags = "系统信息管理配置接口")
@RestController
@RequestMapping("/api/base/systemConfig")
@Validated
public class SystemConfigController extends BaseJpaController<DtoSystemConfig,String, SystemConfigService> {

    /**
     * 查询系统信息管理配置记录
     *
     * @return RestResponse<DtoSystemConfig> 系统信息管理配置DTO响应
     */
    @ApiOperation(value = "查询系统信息管理配置记录", notes = "查询系统信息管理配置记录")
    @GetMapping
    public RestResponse<DtoSystemConfig> findAll() {
        RestResponse<DtoSystemConfig> restResp = new RestResponse<>();
        DtoSystemConfig systemConfig = service.findSystemConfigOne();
        restResp.setRestStatus(systemConfig == null ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(systemConfig);
        return restResp;
    }

    /**
     * 新增或修改系统信息管理配置
     *
     * @param dto DtoSystemConfig 系统信息管理配置DTO对象
     * @return RestResponse<DtoSystemConfig> 系统信息管理配置DTO响应
     */
    @ApiOperation(value = "新增或修改系统信息管理配置", notes = "新增或修改系统信息管理配置")
    @PostMapping
    public RestResponse<DtoSystemConfig> save(@Validated @RequestBody DtoSystemConfig dto) {
        RestResponse<DtoSystemConfig> restResp = new RestResponse<>();
        DtoSystemConfig saveSystemConfig = service.save(dto);
        restResp.setData(saveSystemConfig);
        return restResp;
    }

    @ApiOperation(value = "在线编辑方式", notes = "在线编辑方式")
    @GetMapping("/configMode")
    public RestResponse<Map<String,Boolean>> configMode() {
        RestResponse<Map<String,Boolean>> restResp = new RestResponse<>();
        Map<String,Boolean> mode = service.configMode();
        restResp.setData(mode);
        return restResp;
    }

    @ApiOperation(value = "水印全局配置", notes = "水印全局配置")
    @GetMapping("/watermark")
    public RestResponse<String> getWatermarkConfig() {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setData(service.getWatermarkConfig());
        return restResp;
    }
}
