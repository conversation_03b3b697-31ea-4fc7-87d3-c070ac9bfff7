package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.NoticeCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoNotice;
import com.sinoyd.lims.lim.service.NoticeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 公告管理
 * <AUTHOR> 修改：guqx
 * @version V1.0.0 2019/3/11
 * @since V100R001
 */
@Api(tags = "公告管理: 公告管理服务")
@RestController
@RequestMapping("/api/lim/notice")
@Validated
public class NoticeController extends BaseJpaController<DtoNotice, String, NoticeService>{


     /**
     * 新增
     * @param notice
     */
    @ApiOperation(value = "新增公告", notes = "新增公告")
    @PostMapping("")
    public RestResponse<DtoNotice> create(@Validated @RequestBody DtoNotice notice)
    {
        RestResponse<DtoNotice> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        DtoNotice data = service.save(notice);
        restResp.setData(data);
        restResp.setCount(1);
       
        return restResp;
    }

    /**
     * 置顶
     * @param id
     */
    @ApiOperation(value = "置顶公告", notes = "置顶公告")
    @PostMapping("/makeTop")
    public Object makeTop(String id)
    {
        RestResponse<DtoNotice> restResp = new RestResponse<>();
        DtoNotice notice = service.makeTop(id);
        restResp.setData(notice);
        restResp.setRestStatus(ERestStatus.SUCCESS);
       
        return restResp;
    }

    /**
     * 取消置顶
     * @param id
     */
    @ApiOperation(value = "取消公告置顶", notes = "取消公告置顶")
    @PostMapping("/cancelTop")
    public Object cancelTop(String id)
    {
        RestResponse<DtoNotice> restResp = new RestResponse<>();
        DtoNotice notice = service.cancelTop(id);
        restResp.setData(notice);
        restResp.setRestStatus(ERestStatus.SUCCESS);
        
        return restResp;
    }

    /**
     * 修改
     *  @param notice
     */
    @ApiOperation(value = "修改公告", notes = "修改公告")
    @PutMapping("")
    public RestResponse<DtoNotice> update(@Validated @RequestBody DtoNotice notice)
    {
        RestResponse<DtoNotice> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        DtoNotice data = service.update(notice);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 单个删除 真
     * @param id
     */
    @ApiOperation(value = "根据id删除公告", notes = "根据id删除公告")
    @DeleteMapping("/{id}")
    public RestResponse<String> deleteOne(@PathVariable String id)
    {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);
       
        return restResp;
    }
    /**
     * 单个获取
     * @param id
     */
    @ApiOperation(value = "按主键id查询公告", notes = "按主键id查询公告")
    @GetMapping("/{id}")
    public RestResponse<DtoNotice> getById(@PathVariable String id)
    {
        RestResponse<DtoNotice> restResp = new RestResponse<>();
        DtoNotice notice = service.findOne(id);
        restResp.setData(notice);
        restResp.setRestStatus(StringUtil.isNull(notice) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
       
        return restResp;
    }

    /**
     * 批量获取
     * @param noticeCriteria
     */
    @ApiOperation(value = "分页动态条件查询公告", notes = "分页动态条件查询公告")
    @GetMapping("")
    public RestResponse<List<DtoNotice>> findByPage(NoticeCriteria noticeCriteria)
    {
        RestResponse<List<DtoNotice>> restResponse = new RestResponse<>();
        PageBean<DtoNotice> pb = super.getPageBean();
        service.findByPage(pb, noticeCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pb.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pb.getData());
        restResponse.setCount(pb.getRowsCount());
        
        return restResponse;
    }
}