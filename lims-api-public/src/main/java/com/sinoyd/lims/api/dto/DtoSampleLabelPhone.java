package com.sinoyd.lims.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.Column;
import java.util.Date;

@Data
public class DtoSampleLabelPhone {
    /**
     * 样品编号
     */
    private String sampleCode;


    /**
     * 点位名称
     */
    private String redFolderName;


    /**
     * 采样时间
     */
    private Date samplingTimeBegin;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 数据id
     */
    private String anaId;

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 检测类型名称
     */
    private String sampleTypeName;

    /**
     * 检测类型大类id
     */
    private String bigSampleTypeId;

    /**
     * 分析项目名称
     */
    private String redAnalyzeItemName;

    /**
     * 是否分包
     */
    private Boolean isOutsourcing;


    /**
     * 不分组的标识
     */
    private String mark;

    /**
     * 分组的标识
     */
    private String groupMark;

    /**
     * 固定剂的信息
     */
    private String fixer;

    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 分组id
     */
    private String groupId;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 容器名称
     */
    private String containerName;

    /**
     * 分组备注
     */
    private String remark;

    /**
     * 前处理方式
     */
    private String pretreatmentMethod;

    /**
     * 采样体积
     */
    private String sampleVolume;

    /**
     * 采样容器状态 EnumContainerStatus 1.完好无损 2.破损
     */
    private Integer containerStatus;

    /**
     * 系统名称全写
     */
    private String fullName;

    /**
     * 系统名称简写
     */
    private String shortName;

    /**
     * 欢迎登陆语
     */
    private String welcomeWord;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 企业地址
     */
    private String companyAddress;

    /**
     * 企业邮编
     */
    private String companyPostCode;

    /**
     * 企业联系方式
     */
    private String companyPhone;

    /**
     * 企业英文名称
     */
    private String companyEnglishName;

    /**
     * 保存条件
     */
    private String saveCondition;

    /**
     * 该构造函数用到 DSSampleLabelServiceImpl  findSampleLabelData 方法，双方要改需要同步修改
     *
     * @param sampleId           样品id
     * @param sampleCode         样品编号
     * @param redFolderName      点位名称
     * @param samplingTimeBegin  采样时间
     * @param testId             测试项目id
     * @param anaId              分析数据id
     * @param sampleTypeId       检测类型id
     * @param sampleTypeName     检测类型名称
     * @param bigSampleTypeId    检测类型大类id
     * @param redAnalyzeItemName 分析项目名称
     * @param isOutsourcing      是否分包
     * @param receiveId          送样单id
     */
    public DtoSampleLabelPhone(String sampleId,
                               String sampleCode,
                               String redFolderName,
                               Date samplingTimeBegin,
                               String testId,
                               String anaId,
                               String sampleTypeId,
                               String sampleTypeName,
                               String bigSampleTypeId,
                               String redAnalyzeItemName,
                               Boolean isOutsourcing,
                               String receiveId
    ) {
        this.sampleId = sampleId;
        this.setSampleCode(sampleCode);
        this.setRedFolderName(redFolderName);
        this.setSamplingTimeBegin(samplingTimeBegin);
        this.setTestId(testId);
        this.setAnaId(anaId);
        this.setSampleTypeId(sampleTypeId);
        this.setSampleTypeName(sampleTypeName);
        this.setBigSampleTypeId(bigSampleTypeId);
        this.setRedAnalyzeItemName(redAnalyzeItemName);
        this.setIsOutsourcing(isOutsourcing);
        this.receiveId = receiveId;
    }
}
