package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoNewSearchResult;
import com.sinoyd.lims.lim.dto.lims.DtoNewSearchTask;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.NewSearchResultRepository;
import com.sinoyd.lims.lim.repository.lims.NewSearchTaskRepository;
import com.sinoyd.lims.lim.service.MessageSendRecordService;
import com.sinoyd.lims.lim.service.NewSearchTaskService;
import com.sinoyd.lims.lim.service.PersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 查新任务接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
@Service
public class NewSearchTaskServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoNewSearchTask, String, NewSearchTaskRepository> implements NewSearchTaskService {


    private NewSearchResultRepository newSearchResultRepository;

    private PersonService personService;

    private MessageSendRecordService messageSendRecordService;

    @Override
    public void findByPage(PageBean<DtoNewSearchTask> page, BaseCriteria criteria) {
        page.setEntityName("DtoNewSearchTask t");
        page.setSelect("select t");
        super.findByPage(page, criteria);
        List<DtoNewSearchTask> resultList = page.getData();
        List<String> executorIds = resultList.stream().map(DtoNewSearchTask::getExecutor).distinct().collect(Collectors.toList());
        List<DtoPerson> personList = StringUtil.isNotEmpty(executorIds) ? personService.findAll(executorIds) : new ArrayList<>();
        for (DtoNewSearchTask task : resultList) {
            // 执行人
            personList.stream().filter(p -> task.getExecutor().equals(p.getId())).findFirst().ifPresent(person -> {
                task.setExecutorName(person.getCName());
            });

        }
    }

    @Override
    public DtoNewSearchTask findOne(String key) {
        DtoNewSearchTask task = super.findOne(key);
        // 执行人姓名
        DtoPerson person = StringUtil.isNotEmpty(task.getExecutor()) ? personService.findOne(task.getExecutor()) : new DtoPerson();
        task.setExecutorName(person.getCName());

        // 查新结果
        List<DtoNewSearchResult> resultList = newSearchResultRepository.findByTaskId(task.getId());
        task.setNewSearchResultList(resultList);
        return task;
    }

    @Override
    @Transactional
    public DtoNewSearchTask submit(Map<String, Object> submitMap) {
        String taskId = submitMap.get("id").toString();
        String confirmId = submitMap.get("confirmId").toString();
        DtoNewSearchTask task = repository.findOne(taskId);
        List<DtoNewSearchResult> resultList = newSearchResultRepository.findByTaskId(taskId);
        if (StringUtil.isEmpty(resultList)) {
            throw new BaseException("未填写标准发布信息，不允许提交！");
        }
        task.setStatus(EnumLIM.EnumNewSearchStatus.已处理.getValue());
        // 更新查新结果为待处理
        resultList.forEach(p -> {
            p.setStatus(EnumLIM.EnumNewSearchStatus.待处理.getValue());
            p.setConfirmId(confirmId);
        });
        newSearchResultRepository.save(resultList);
        // 创建提醒消息
        messageSendRecordService.createMessage(EnumLIM.MessageType.标准查新确认.getValue(),
                String.format("您有%s条标准查新记录待确认，请及时前往处理。", resultList.size()),
                confirmId);
        return super.save(task);
    }

    @Autowired
    public void setNewSearchResultRepository(NewSearchResultRepository newSearchResultRepository) {
        this.newSearchResultRepository = newSearchResultRepository;
    }

    @Autowired
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }

    @Autowired
    public void setMessageSendRecordService(MessageSendRecordService messageSendRecordService) {
        this.messageSendRecordService = messageSendRecordService;
    }
}
