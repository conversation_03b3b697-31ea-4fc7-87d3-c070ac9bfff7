package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 仪器接入数据详情查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/17
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InstrumentGatherDataDetailsCriteria extends BaseCriteria {


    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();

        return condition.toString();
    }
}