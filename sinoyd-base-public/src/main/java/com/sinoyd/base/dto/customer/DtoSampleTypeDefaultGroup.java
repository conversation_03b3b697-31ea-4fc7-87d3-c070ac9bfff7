package com.sinoyd.base.dto.customer;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 样品大类和默认样品分组的对应关系
 * <AUTHOR>
 * @version V1.0.0 2022/02/24
 * @since V100R001
 */
@Data
public class DtoSampleTypeDefaultGroup {

    /**
     * 样品大类id
     */
    private String bigSampleTypeId;

    /**
     * 默认标签分组id
     */
    private String defaultLabelGroupId;

    /**
     * 默认标签分组名称
     */
    private String defaultLabelGroupName;

    /**
     * 默认现场分组id
     */
    private String localLabelGroupId;

    /**
     * 默认现场分组名称
     */
    private String localLabelGroupName;
}
