package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.lims.pro.dto.DtoSample;
import lombok.Data;
import org.aspectj.lang.annotation.DeclareAnnotation;
import org.hibernate.validator.constraints.Length;

import java.lang.reflect.Field;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 外部样品更新信息（字段与DtoOutSample中的基本字段保持一致）
 *
 * <AUTHOR>
 * @version V1.0.0 2019/1/7
 * @since V100R001
 */
@Data
public class DtoOutSampleSave {

    public String getField() {
        try {
            Field[] fields = this.getClass().getDeclaredFields();
            for (Field field : fields) {
                if (!"id".equals(field.getName()) && StringUtil.isNotNull(field.get(this))) {
                    return field.getName();
                }
            }
        } catch (SecurityException | IllegalAccessException | IllegalArgumentException e) {
            throw new BaseException("[" + this.getClass().getName() + "] not exist json field.");
        }
        return null;
    }

    /**
     * 获取修改的字段
     *
     * @param sample 样品信息
     * @return 修改的字段
     */
    public String getFieldByOldSample(DtoSample sample) {
        try {
            Field[] fields = this.getClass().getDeclaredFields();
            //样品的数据
            Map<String, Object> samMaps = entityToMap(sample);
            for (Field field : fields) {
                if (!"id".equals(field.getName()) && StringUtil.isNotNull(field.get(this))) {
                    if (samMaps.containsKey(field.getName())) {
                        Object keyValue = samMaps.get(field.getName());
                        if (StringUtil.isNotNull(keyValue)) {
                            if ("redFolderName".equals(field.getName())) {
                                keyValue = keyValue.toString().replace(String.format("(%s-%s-%s)", samMaps.get("cycleOrder"),
                                        samMaps.get("timesOrder"), samMaps.get("sampleOrder")), "");
                            }
                            if (!field.get(this).equals(keyValue)) {
                                return field.getName();
                            }
                        }
                    }
                }
            }
        } catch (SecurityException | IllegalAccessException | IllegalArgumentException e) {
            throw new BaseException("[" + this.getClass().getName() + "] not exist json field.");
        }
        return null;
    }

    /**
     * 将sample实体转换成Map
     *
     * @param obj 实体
     * @param <T> 实体类型
     * @return samMap
     * @throws IllegalAccessException 报错
     */
    private static <T> Map<String, Object> entityToMap(T obj) throws IllegalAccessException {
        if (obj == null) {
            return null;
        }
        Map<String, Object> map = new HashMap<>();

        Field[] declaredFields = obj.getClass().getSuperclass().getDeclaredFields();
        for (Field field : declaredFields) {
            field.setAccessible(true);
            map.put(field.getName(), field.get(obj));
        }

        return map;
    }

    public void loadFromSave(DtoSample sample) {
        if (StringUtils.isNotNullAndEmpty(this.code)) {
            sample.setCode(this.code);
        }
        if (StringUtils.isNotNullAndEmpty(this.redFolderName)) {
            sample.setRedFolderName(this.redFolderName);
        }
        if (StringUtils.isNotNullAndEmpty(this.inspectedEntId)) {
            sample.setInspectedEntId(this.inspectedEntId);
        }
        if (StringUtils.isNotNullAndEmpty(this.inspectedEnt)) {
            sample.setInspectedEnt(this.inspectedEnt);
        }
        if (StringUtil.isNotNull(this.cycleOrder)) {
            sample.setCycleOrder(this.cycleOrder);
        }
        if (StringUtil.isNotNull(this.timesOrder)) {
            sample.setTimesOrder(this.timesOrder);
        }
        if(StringUtil.isNotNull(this.sampleOrder)){
            sample.setSampleOrder(this.sampleOrder);
        }
        if (StringUtils.isNotNullAndEmpty(this.samColor)) {
            sample.setSamColor(this.samColor);
        }
        if (StringUtils.isNotNullAndEmpty(this.sampleExplain)) {
            sample.setSampleExplain(this.sampleExplain);
        }
        if (StringUtils.isNotNullAndEmpty(this.pack)) {
            sample.setPack(this.pack);
        }
        if (StringUtils.isNotNullAndEmpty(this.samplingTimeBegin)) {
            sample.setSamplingTimeBegin(this.samplingTimeBegin);
        }
    }

    /**
     * 样品id
     */
    @DeclareAnnotation(value = "样品id")
    private String id;

    /**
     * 样品编号
     */
    @DeclareAnnotation(value = "样品编号")
    @Length(message = "样品编号{validation.message.length}", max = 50)
    private String code;

    /**
     * 点位名称
     */
    @DeclareAnnotation(value = "点位名称")
    @Length(message = "冗余-点位{validation.message.length}", max = 100)
    private String redFolderName;

    /**
     * 受检单位id
     */
    @DeclareAnnotation(value = "受检单位")
    private String inspectedEntId;

    /**
     * 受检单位名称
     */
    @DeclareAnnotation(value = "受检单位")
    @Length(message = "受检单位{validation.message.length}", max = 100)
    private String inspectedEnt;

    /**
     * 周期
     */
    @DeclareAnnotation(value = "周期")
    private Integer cycleOrder;

    /**
     * 次数
     */
    @DeclareAnnotation(value = "次数")
    private Integer timesOrder;

    /**
     * 样品数
     */
    @DeclareAnnotation(value = "样品数")
    private Integer sampleOrder;

    /**
     * 样品颜色
     */
    @DeclareAnnotation(value = "样品颜色")
    @Length(message = "颜色{validation.message.length}", max = 50)
    private String samColor;

    /**
     * 样品性状
     */
    @DeclareAnnotation(value = "样品性状")
    @Length(message = "样品特征{validation.message.length}", max = 1000)
    private String sampleExplain;

    /**
     * 样品包装
     */
    @DeclareAnnotation(value = "样品包装")
    @Length(message = "包装/规格{validation.message.length}", max = 50)
    private String pack;

    /**
     * 经度
     */
    @DeclareAnnotation(value = "经度")
    @Length(message = "经度（实际）{validation.message.length}", max = 20)
    private String lon;

    /**
     * 纬度
     */
    @DeclareAnnotation(value = "纬度")
    @Length(message = "纬度（实际）{validation.message.length}", max = 20)
    private String lat;

    /**
     * 采样日期
     */
    @DeclareAnnotation(value = "采样日期")
    private Date samplingTimeBegin;
}
