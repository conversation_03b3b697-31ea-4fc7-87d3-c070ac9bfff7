package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.OrderQuotationService;
import com.sinoyd.lims.pro.criteria.OrderQuotationCriteria;
import com.sinoyd.lims.pro.dto.DtoOrderQuotation;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * OrderQuotation服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2022/5/18
 * @since V100R001
 */
 @Api(tags = "示例: OrderQuotation服务")
 @RestController
 @RequestMapping("api/pro/orderQuotation")
 public class OrderQuotationController extends BaseJpaController<DtoOrderQuotation, String,OrderQuotationService> {


    /**
     * 分页动态条件查询OrderQuotation
     * @param orderQuotationCriteria 条件参数
     * @return RestResponse<List<OrderQuotation>>
     */
     @ApiOperation(value = "分页动态条件查询OrderQuotation", notes = "分页动态条件查询OrderQuotation")
     @GetMapping
     public RestResponse<List<DtoOrderQuotation>> findByPage(OrderQuotationCriteria orderQuotationCriteria) {
         PageBean<DtoOrderQuotation> pageBean = super.getPageBean();
         RestResponse<List<DtoOrderQuotation>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, orderQuotationCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询OrderQuotation
     * @param id 主键id
     * @return RestResponse<DtoOrderQuotation>
     */
     @ApiOperation(value = "按主键查询OrderQuotation", notes = "按主键查询OrderQuotation")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoOrderQuotation> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoOrderQuotation> restResponse = new RestResponse<>();
         DtoOrderQuotation orderQuotation = service.findOne(id);
         restResponse.setData(orderQuotation);
         restResponse.setRestStatus(StringUtil.isNull(orderQuotation) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增OrderQuotation
     * @param orderQuotation 实体列表
     * @return RestResponse<DtoOrderQuotation>
     */
     @ApiOperation(value = "新增OrderQuotation", notes = "新增OrderQuotation")
     @PostMapping
     public RestResponse<DtoOrderQuotation> create(@RequestBody DtoOrderQuotation orderQuotation) {
         RestResponse<DtoOrderQuotation> restResponse = new RestResponse<>();
         restResponse.setData(service.save(orderQuotation));
         return restResponse;
      }

     /**
     * 新增OrderQuotation
     * @param orderQuotation 实体列表
     * @return RestResponse<DtoOrderQuotation>
     */
     @ApiOperation(value = "修改OrderQuotation", notes = "修改OrderQuotation")
     @PutMapping
     public RestResponse<DtoOrderQuotation> update(@RequestBody DtoOrderQuotation orderQuotation) {
         RestResponse<DtoOrderQuotation> restResponse = new RestResponse<>();
         restResponse.setData(service.update(orderQuotation));
         return restResponse;
      }

    /**
     * "根据id批量删除OrderQuotation
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除OrderQuotation", notes = "根据id批量删除OrderQuotation")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }