package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.pro.dto.DtoSamplingAchievementDetails;
import com.sinoyd.lims.pro.repository.SamplingAchievementDetailsRepository;
import com.sinoyd.lims.pro.service.SamplingAchievementDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * SamplingAchievementDetails操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/03/14
 * @since V100R001
 */
@Service
public class SamplingAchievementDetailsServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoSamplingAchievementDetails, String, SamplingAchievementDetailsRepository> implements SamplingAchievementDetailsService {

    private PersonService personService;

    private SampleTypeService sampleTypeService;

    @Override
    public void findByPage(PageBean<DtoSamplingAchievementDetails> page, BaseCriteria criteria) {
        page.setEntityName("DtoSamplingAchievementDetails a");
        page.setSelect("select a");
        page.setSort("a.samplingTime-, a.recordCode-, a.sampleCode");
        super.findByPage(page, criteria);
        fillingTransientFields(page.getData());
    }

    @Override
    public void export(HttpServletResponse response, BaseCriteria criteria) {
        PageBean<DtoSamplingAchievementDetails> page = new PageBean<>();
        page.setPageNo(1);
        page.setRowsPerPage(Integer.MAX_VALUE);
        page.setSort("a.samplingTime-, a.recordCode-, a.sampleCode");
        findByPage(page, criteria);
        PoiExcelUtils.exportExcel(page.getData(), null, "采样绩效明细表格", DtoSamplingAchievementDetails.class, "采样绩效明细表格" + "_" + DateUtil.nowTime("yyyyMMddHHmmss"), response);
    }

    /**
     * 填充冗余字段
     *
     * @param details 合同明细
     */
    private void fillingTransientFields(List<DtoSamplingAchievementDetails> details) {
        List<String> sampleTypeIds = details.stream().map(DtoSamplingAchievementDetails::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypes = StringUtil.isNotEmpty(sampleTypeIds) ? sampleTypeService.findAll(sampleTypeIds) : new ArrayList<>();
        Set<String> personIds = new HashSet<>();
        details.forEach(d -> {
            personIds.addAll(Arrays.asList(d.getSamplingPersonIds().split(",")));
        });
        List<DtoPerson> personList = StringUtil.isNotEmpty(personIds) ? personService.findAll(personIds) : new ArrayList<>();
        details.forEach(d -> {
            List<String> personIds2Detail = Arrays.asList(d.getSamplingPersonIds().split(","));
            List<String> personNames = personList.stream().filter(p -> personIds2Detail.contains(p.getId())).map(DtoPerson::getCName).collect(Collectors.toList());
            d.setSamplingPersonNames(String.join(",", personNames));
            Optional<DtoSampleType> sampleType = sampleTypes.stream().filter(s -> s.getId().equals(d.getSampleTypeId())).findFirst();
            sampleType.ifPresent(s -> d.setSampleTypeName(s.getTypeName()));
        });
    }

    @Autowired
    @Lazy
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }

    @Autowired
    @Lazy
    public void setSampleTypeService(SampleTypeService sampleTypeService) {
        this.sampleTypeService = sampleTypeService;
    }
}
