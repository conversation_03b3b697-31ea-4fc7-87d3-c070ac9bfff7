package com.sinoyd.lims.pro.dto.customer;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinoyd.lims.lim.dto.customer.DtoRecordConfigTest;
import com.sinoyd.lims.lim.dto.customer.DtoTestFormulaParamsConfig;
import com.sinoyd.lims.pro.dto.DtoParamsData;
import com.sinoyd.lims.pro.dto.DtoSample;
import lombok.Data;

import java.util.*;

/**
 * 检测单打开的dto
 * <AUTHOR>
 * @version V1.0.0 2019/12/02
 * @since V100R001
 */
@Data
public class DtoWorkSheetProperty {


    /**
     * 原始记录单配置信息
     */
    private List<DtoRecordConfigTest> recordConfigs = new ArrayList<>();

    /**
     * 相关的分析数据
     */
    private List<Map<String, Object>> analyseData = new ArrayList<>();


    /**
     * 公式相关参数
     */
    private List<DtoTestFormulaParamsConfig> paramsConfig = new ArrayList<>();

    /**
     * 样品数据
     */
    private List<DtoSample> samples = new ArrayList<>();


    /**
     * 检测单相关参数数据
     */
    private List<DtoParamsData> worksheetParamsData = new ArrayList<>();

    /**
     * 相关检测单测试数据
     */
    private DtoWorkSheetTest test = new DtoWorkSheetTest();

    /**
     * 需要计算的参数
     */
    private List<DtoParamsData> paramsData = new ArrayList<>();

    /**
     * 是否允许更换公式
     */
    private Boolean isAlterFormula;


    /**
     * 是否允许更新校准曲线
     */
    private Boolean isAlterCurve;

    /**
     * 分析方法名称
     */
    private String redAnalyzeMethodName;

    /**
     * 分析方法标准号
     */
    private String redCountryStandard;


    /**
     * 剩余数据信息
     */
    private List<Map<String, Object>> surplusAnalyseData = new ArrayList<>();

    /**
     * 分析时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date analyzeTime;


    /**
     * 检测单id
     */
    private String workSheetFolderId;

    /**
     * 分析人员姓名
     */
    private String analystId;


    /**
     * 分析人员id
     */
    private String analystName;


    /**
     * 审核人id
     */
    private String auditorId;


    /**
     * 审核人姓名
     */
    private String auditorName;

    /**
     * 记录单id
     */
    private String recordId;

    /**
     * 排序id
     */
    private String sortId;

    /**
     * 是否样品显示
     */
    private Boolean isSample;
}
