package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.WorkSheet;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import java.util.Date;


/**
 * DtoWorkSheet实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_WorkSheet")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoWorkSheet extends WorkSheet {
    private static final long serialVersionUID = 1L;

    /**
     * 检测单创建时间
     */
    @Transient
    private Date createDate;

    /**
     * 工作单中分析项目的排序值
     */
    @Transient
    private int analyseItemOrderNum;

    @Transient
    private String displayAnaName;

    @Transient
    private String worksheetCurveId;

    public DtoWorkSheet() {

    }

    /**
     * 用于WorkSheetFolderServiceImpl下findLastWorkSheetParams方法
     *
     * @param id         子检测单id
     * @param parentId   检测单id
     * @param testId     测试项目id
     * @param createDate 创建日期
     */
    public DtoWorkSheet(String id, String parentId, String testId, Date createDate) {
        this.setId(id);
        this.setParentId(parentId);
        this.setTestId(testId);
        this.createDate = createDate;
    }
}