package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.MessageSendRecordCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoMessageSendRecord;
import com.sinoyd.lims.lim.service.MessageSendRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 消息用户访问接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022-09-22
 */
@Api(tags = "消息用户访问接口")
@RestController
@RequestMapping("api/lim/messageSendRecord")
public class MessageSendRecordController extends BaseJpaController<DtoMessageSendRecord, String, MessageSendRecordService> {

    /**
     * 分页动态条件查询消息记录
     *
     * @param messageSendRecordCriteria 查询条件
     * @return 结果
     */
    @ApiOperation(value = "分页动态条件查询消息记录", notes = "分页动态条件查询消息记录")
    @GetMapping
    public RestResponse<List<DtoMessageSendRecord>> findByPage(MessageSendRecordCriteria messageSendRecordCriteria) {
        PageBean<DtoMessageSendRecord> pageBean = super.getPageBean();
        RestResponse<List<DtoMessageSendRecord>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, messageSendRecordCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 取消或者关注
     *
     * @param ids       记录id
     * @param isConcern 是否关注
     * @return 结果
     */
    @ApiOperation(value = "取消或者关注", notes = "取消或者关注")
    @PutMapping("/concern/{isConcern}")
    public RestResponse<Boolean> concernSendRecord(@RequestBody List<String> ids,
                                                   @PathVariable(name = "isConcern") Boolean isConcern) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setData(service.concernSendRecord(ids, isConcern));
        return restResponse;
    }

    /**
     * 设置已读
     *
     * @param id 主键id
     * @return 结果
     */
    @ApiOperation(value = "设置已读", notes = "设置已读")
    @PutMapping("/{id}")
    public RestResponse<Boolean> setupRead(@PathVariable(name = "id") String id) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setData(service.setupRead(id));
        return restResponse;
    }

    /**
     * 选中设置已读
     *
     * @param ids 主键ids
     * @return 结果
     */
    @ApiOperation(value = "选中设置已读", notes = "选中设置已读")
    @PutMapping("/option")
    public RestResponse<Boolean> setupRead(@RequestBody List<String> ids) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setData(service.setupReadByIds(ids));
        return restResponse;
    }

    /**
     * 设置全部已读
     *
     * @return 结果
     */
    @ApiOperation(value = "设置全部已读", notes = "设置全部已读")
    @PutMapping("/read")
    public RestResponse<Boolean> setupRead() {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setData(service.setupReadAll());
        return restResponse;
    }

    /**
     * 获取当前用户的消息数
     *
     * @return 结果
     */
    @ApiOperation(value = "获取当前用户的消息数", notes = "获取当前用户的消息数")
    @GetMapping("/count")
    public RestResponse<Integer> messageCount() {
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(service.queryLoginUserMsgCount());
        return restResponse;
    }

    /**
     * 根据类型获取当前用户的消息数
     *
     * @return 结果
     */
    @ApiOperation(value = "根据类型获取当前用户的消息数", notes = "根据类型获取当前用户的消息数")
    @PostMapping("/count")
    public RestResponse<Integer> messageCountByType(@RequestBody List<String> messageTypes) {
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(service.queryMessageCountByType(messageTypes));
        return restResponse;
    }
}
