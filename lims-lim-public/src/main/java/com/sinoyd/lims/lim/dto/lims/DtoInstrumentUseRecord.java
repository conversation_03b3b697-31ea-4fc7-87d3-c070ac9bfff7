package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.entity.InstrumentUseRecord;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * DtoInstrumentUseRecord实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_InstrumentUseRecord")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoInstrumentUseRecord extends InstrumentUseRecord {

    /*
     * 仪器名称
     */
    @Transient
    private String instrumentName;

    /*
     * 仪器编号
     */
    @Transient
    private String instrumentsCode;

    /*
     * 规格型号
     */
    @Transient
    private String model;

    /**
     * 出厂编号
     */
    @Transient
    private String serialNo;

    /*
     * 检定情况
     */
    @Transient
    private Integer inspectResult;


    /**
     * 相关的样品ids
     */
    @Transient
    private List<String> sampleIds = new ArrayList<>();

    /**
     * 使用人
     */
    @Transient
    private String usePerson;


    /**
     * 测试项目名称
     */
    @Transient
    private String testName;

    /**
     * 过期日期（溯源）
     */
    @Transient
    private Date originEndDate;

    /**
     * 测试项目id集合
     */
    @Transient
    private List<String> testIdArray = new ArrayList<>();

    /**
     * 默认构造函数
     */
    public DtoInstrumentUseRecord() {

    }


    //该方法主要用到InstrumentUseRecordServiceImpl的findByPage方法，如果这个改动，使用方法的地方也要修改
    public DtoInstrumentUseRecord(String id,
                                  String instrumentId,
                                  String objectId,
                                  String environmentalManageId,
                                  Integer objectType, String usePersonId,
                                  Date startTime, Date endTime, String testIds, String temperature,
                                  String humidity, String pressure, String beforeUseSituation, String beforeAfterSituation,
                                  Boolean isAssistInstrument, String remark, Date insOriginDate,
                                  String instrumentName, String instrumentsCode, String model, Integer inspectResult, String serialNo, Date originEndDate) {
        this.setId(id);
        this.setInstrumentId(instrumentId);
        this.setObjectId(objectId);
        this.setEnvironmentalManageId(environmentalManageId);
        this.setObjectType(objectType);
        this.setUsePersonId(usePersonId);
        this.setStartTime(startTime);
        this.setEndTime(endTime);
        this.setTestIds(testIds);
        this.setTemperature(temperature);
        this.setHumidity(humidity);
        this.setPressure(pressure);
        this.setBeforeUseSituation(beforeUseSituation);
        this.setBeforeAfterSituation(beforeAfterSituation);
        this.setIsAssistInstrument(isAssistInstrument);
        this.setRemark(remark);
        this.setInsOriginDate(insOriginDate);
        this.setInstrumentName(instrumentName);
        this.setInstrumentsCode(instrumentsCode);
        this.setModel(model);
        this.setInspectResult(inspectResult);
        this.setSerialNo(serialNo);
        this.setOriginEndDate(originEndDate);
    }

    /**
     * 获取测试项目id列表
     *
     * @return 测试项目id列表
     */
    public List<String> getTestIdList() {
        if (StringUtil.isEmpty(getTestIds())) {
            return new ArrayList<>();
        }
        return Stream.of(getTestIds().split(",")).collect(Collectors.toList());
    }
}