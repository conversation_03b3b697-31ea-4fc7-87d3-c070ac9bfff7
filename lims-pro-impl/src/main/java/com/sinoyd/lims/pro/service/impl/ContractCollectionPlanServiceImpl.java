package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.pro.dto.DtoContractCollectionPlan;
import com.sinoyd.lims.pro.dto.DtoOrderContract;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.ContractCollectionPlanRepository;
import com.sinoyd.lims.pro.repository.OrderContractRepository;
import com.sinoyd.lims.pro.service.ContractCollectionPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 收款计划接口实现
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-05-10
 * @since V100R001
 */
@Service
public class ContractCollectionPlanServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoContractCollectionPlan, String, ContractCollectionPlanRepository>
        implements ContractCollectionPlanService {

    private OrderContractRepository orderContractRepository;

    private PersonService personService;

    @Override
    public void findByPage(PageBean<DtoContractCollectionPlan> page, BaseCriteria criteria) {
        //分页查询
        page.setEntityName("DtoContractCollectionPlan x,DtoOrderContract o");
        page.setSelect("select x");
        super.findByPage(page, criteria);
        //拓展关联数据
        List<DtoContractCollectionPlan> list = page.getData();
        List<String> orderContractIdList = list.stream().map(DtoContractCollectionPlan::getContractId).distinct().collect(Collectors.toList());
        List<DtoOrderContract> dtoOrderContractList = orderContractIdList.isEmpty() ? new ArrayList<>() : orderContractRepository.findAll(orderContractIdList);
        List<DtoPerson> people = personService.findAll();
        for (DtoContractCollectionPlan dtoContractCollectionPlan : list) {
            DtoOrderContract dtoOrderContract = dtoOrderContractList.stream().filter(c -> c.getId().equals(dtoContractCollectionPlan.getContractId())).findFirst().orElse(null);
            if (dtoOrderContract != null) {
                //合同名称 合同编号 甲方名称
                dtoContractCollectionPlan.setContractCode(dtoOrderContract.getContractCode());
                dtoContractCollectionPlan.setContractName(dtoOrderContract.getContractName());
                dtoContractCollectionPlan.setFirstEntName(dtoOrderContract.getFirstEntName());
                //业务员
                List<String> signPersonIds = Arrays.asList(dtoOrderContract.getSignPersonId().split(","));
                String signPerson = people.stream().filter(p -> signPersonIds.contains(p.getId())).map(DtoPerson::getCName).collect(Collectors.joining(","));
                dtoContractCollectionPlan.setSalesPersonName(signPerson);
            }
        }
    }

    @Override
    public Map<String, Object> analyzeProfileData(PageBean<DtoContractCollectionPlan> page, BaseCriteria criteria) {
        //结果容器
        Map<String, Object> resMap = new HashMap<>();
        //原始数据集
        page.setRowsPerPage(9999);
        findByPage(page, criteria);
        List<DtoContractCollectionPlan> origeinDataList = page.getData();
        //列表汇总
        calProfileData(resMap, origeinDataList);
        //每月数据
        calPerMonthData(resMap, origeinDataList);
        return resMap;
    }

    /**
     * 计算汇总数据
     *
     * @param resMap          结果容器
     * @param origeinDataList 原始数据集
     */
    private void calPerMonthData(Map<String, Object> resMap, List<DtoContractCollectionPlan> origeinDataList) {
        //已到款
        BigDecimal recTotal = new BigDecimal("0.00");
        //部分到款
        BigDecimal partRecTotal = new BigDecimal("0.00");
        //未到款
        BigDecimal notRecTotal = new BigDecimal("0.00");
        for (DtoContractCollectionPlan contractCollectionPlan : origeinDataList) {
            if (EnumPRO.EnumContractCollectionStatus.已收款.getValue().equals(contractCollectionPlan.getStatus())) {
                recTotal = recTotal.add(contractCollectionPlan.getAmount());
            }
            if (EnumPRO.EnumContractCollectionStatus.部分收款.getValue().equals(contractCollectionPlan.getStatus())) {
                partRecTotal = partRecTotal.add(contractCollectionPlan.getAmount());
            }
            if (EnumPRO.EnumContractCollectionStatus.未收款.getValue().equals(contractCollectionPlan.getStatus())) {
                notRecTotal = notRecTotal.add(contractCollectionPlan.getAmount());
            }
        }
        resMap.put("recTotal", recTotal.doubleValue());
        resMap.put("partRecTotal", partRecTotal.doubleValue());
        resMap.put("notRecTotal", notRecTotal.doubleValue());
    }

    /**
     * 计算每月数据
     *
     * @param resMap          结果容器
     * @param origeinDataList 原始数据集
     */
    private void calProfileData(Map<String, Object> resMap, List<DtoContractCollectionPlan> origeinDataList) {
        origeinDataList.forEach(o -> o.setBelongMonth(DateUtil.dateToString(o.getCollectDate(), "yyyy.MM")));
        Map<String, List<DtoContractCollectionPlan>> groupMap = origeinDataList.stream().collect(Collectors.groupingBy(DtoContractCollectionPlan::getBelongMonth));
        Map<String, Object> perMonthRecMap = new HashMap<>();
        for (Map.Entry<String, List<DtoContractCollectionPlan>> entry : groupMap.entrySet()) {
            List<DtoContractCollectionPlan> monthRec = entry.getValue();
            BigDecimal monthRecAmount = new BigDecimal("0.00");
            for (DtoContractCollectionPlan rec : monthRec) {
                //BUG2023121999027 右上角的统计图改为：“每月计划收款统计图”，统计收款计划上的“计划收款”金额，而不是统计到款金额。
                monthRecAmount = monthRecAmount.add(rec.getAmount());
            }
            perMonthRecMap.put(entry.getKey(), monthRecAmount.doubleValue());
        }
        resMap.put("perMonthData", perMonthRecMap);
    }

    @Override
    public List<String> getCollectItemSelectList(String orderContractId) {
        List<String> list = new ArrayList<>();
        if (UUIDHelper.GUID_EMPTY.equals(orderContractId)) {
            List<String> originCollectItemList = Arrays.stream(EnumPRO.EnumCollectItem.values()).map(EnumPRO.EnumCollectItem::name).collect(Collectors.toList());
            List<String> existCollectItemList = findAll().stream().map(DtoContractCollectionPlan::getCollectItem).collect(Collectors.toList());
            list.addAll(originCollectItemList);
            list.addAll(existCollectItemList);
        } else {
            List<String> existCollectItemList = repository.findAllByContractIdIn(Collections.singletonList(orderContractId)).stream()
                    .map(DtoContractCollectionPlan::getCollectItem).collect(Collectors.toList());
            list.addAll(existCollectItemList);
        }
        list = list.stream().filter(StringUtil::isNotEmpty).distinct().sorted().collect(Collectors.toList());
        return list;
    }

    @Autowired
    public void setOrderContractRepository(OrderContractRepository orderContractRepository) {
        this.orderContractRepository = orderContractRepository;
    }

    @Autowired
    @Lazy
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }
}