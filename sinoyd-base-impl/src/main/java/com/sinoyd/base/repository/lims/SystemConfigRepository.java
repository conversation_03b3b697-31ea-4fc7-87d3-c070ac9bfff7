package com.sinoyd.base.repository.lims;

import com.sinoyd.base.dto.lims.DtoSystemConfig;
import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;


/**
 * SystemConfig数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2022/12/8
 */
public interface SystemConfigRepository extends IBaseJpaPhysicalDeleteRepository<DtoSystemConfig, String> , LimsRepository<DtoSystemConfig,String> {


}