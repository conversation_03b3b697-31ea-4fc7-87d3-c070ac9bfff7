package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.List;


/**
 * 项目查询条件环境质量
 * <AUTHOR>
 * @version V1.0.0 2021年04月27日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EnvironmentStatisticsCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 项目名称和项目编号关键字
     */
    private String key;

    /**
     * 点位名称和点位编号关键字
     */
    private String pointKey;

    /**
     * 监测计划表id
     */
    private List<String> fixedPropertyId;



    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.id = s.projectId ");

        if (StringUtil.isNotEmpty(fixedPropertyId)){
            condition.append(" and a.id = p.projectId ");
            condition.append(" and p.fixedPropertyId in :fixedPropertyId ");
            values.put("fixedPropertyId", fixedPropertyId);
        }
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and a.inputTime >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
            condition.append(" and a.inputTime < :endTime");
            values.put("endTime", to);
        }
        if(StringUtil.isNotEmpty(this.key)){
            condition.append(" and (a.projectCode like :key or  a.projectName like :key) ");
            values.put("key", "%" + this.key + "%");
        }
        if(StringUtil.isNotEmpty(this.pointKey)){
            condition.append(" and (s.watchSpot like :pointKey or  s.folderCode like :pointKey) ");
            values.put("pointKey", "%" + this.pointKey + "%");
        }
        return condition.toString();
    }
}