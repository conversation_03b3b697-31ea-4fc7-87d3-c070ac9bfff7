package com.sinoyd.lims.od.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoTraining;
import com.sinoyd.lims.od.dto.DtoSampleResult;

import java.util.List;

/**
 * SampleResultRepository
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
public interface SampleResultRepository extends IBaseJpaRepository<DtoSampleResult, String> {


    /**
     * 根据样品id查询
     *
     * @param sampleId 样品id
     * @return 样品结果
     */
    DtoSampleResult findBySampleId(String sampleId);

    /**
     * 根据样品ids查询
     *
     * @param sampleIds 样品ids
     * @return 样品结果集合
     */
    List<DtoSampleResult> findBySampleIdIn(List<String> sampleIds);
}
