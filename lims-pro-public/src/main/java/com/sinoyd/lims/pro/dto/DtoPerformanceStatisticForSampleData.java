package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.PerformanceStatisticForSampleData;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import java.util.Date;


/**
 * DtoPerformanceStatisticForSampleData实体
 * <AUTHOR>
 * @version V1.0.0 2020/2/21
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_PerformanceStatisticForSampleData")
 @Data
 @DynamicInsert
 public  class DtoPerformanceStatisticForSampleData extends PerformanceStatisticForSampleData {
    private static final long serialVersionUID = 1L;

    /**
     * 项目编号
     */
    @Transient
    private String projectCode;

    /**
     * 项目名称
     */
    @Transient
    private String projectName;

    /**
     * 项目类型id
     */
    @Transient
    private String projectTypeId;

    /**
     * 项目类型名称
     */
    @Transient
    private String projectTypeName;

    /**
     * 委托方id
     */
    @Transient
    private String customerId;

    /***
     * 委托方的名称
     */
    @Transient
    private String customerName;

    /**
     * 检测类型的名称
     */
    @Transient
    private String sampleTypeName;

    /**
     * 默认的构造函数
     */
    public DtoPerformanceStatisticForSampleData(){

    }

    /**
     * 该构造函数主要用到 PerformanceStatisticForSampleDataServiceImpl 下面 findList查询的方法，主要是查询采样项目的绩效
     * @param id 主键Id
     * @param recordCode 送样单编号
     * @param sendTime 送样时间
     * @param sampleTypeId 检测类型id
     * @param sampleTypeName 检测类型名称
     * @param sample 样品数
     * @param projectId 项目id
     * @param projectCode 项目编号
     * @param projectName 项目名称
     * @param projectTypeId 项目类型id
     * @param projectTypeName 项目类型名称
     * @param customerId 委托方id
     * @param customerName 委托方名称
     * @param senderName 送样人员名称
     */
    public DtoPerformanceStatisticForSampleData(String id,
                                                String samplingPersonId,
                                                String receiveId,
                                                String recordCode,
                                                Date sendTime,
                                                String sampleTypeId,
                                                String sampleTypeName,
                                                Integer sample,
                                                String projectId,
                                                String projectCode,
                                                String projectName,
                                                String projectTypeId,
                                                String projectTypeName,
                                                String customerId,
                                                String customerName,
                                                String senderName

    ) {
        this.setId(id);
        this.setReceiveId(receiveId);
        this.setSamplingPersonId(samplingPersonId);
        this.setRecordCode(recordCode);
        this.setSenderName(senderName);
        this.setSampleTypeId(sampleTypeId);
        this.setSampleTypeName(sampleTypeName);
        this.setSendTime(sendTime);
        this.setSample(sample);
        this.setProjectId(projectId);
        this.setProjectCode(projectCode);
        this.setProjectName(projectName);
        this.setProjectTypeId(projectTypeId);
        this.setProjectTypeName(projectTypeName);
        this.setCustomerId(customerId);
        this.setCustomerName(customerName);
    }

    /**
     * 构造函数 工作量统计总计行
     */
    public DtoPerformanceStatisticForSampleData(Integer sample){
        this.setSample(sample);
    }
}