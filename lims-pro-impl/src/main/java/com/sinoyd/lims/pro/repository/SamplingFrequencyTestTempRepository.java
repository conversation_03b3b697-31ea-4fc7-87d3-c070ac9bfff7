package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTestTemp;

import java.util.Collection;
import java.util.List;

/**
 *  SamplingFrequencyTestTempRepository
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2023/08/02
 */
public interface SamplingFrequencyTestTempRepository extends IBaseJpaPhysicalDeleteRepository<DtoSamplingFrequencyTestTemp, String> {

    /**
     * 根据周期频次模板id查找数据
     *
     * @param frequencyTempIds 周期频次模板id
     * @return List<DtoSamplingFrequencyTestTemp>
     */
    List<DtoSamplingFrequencyTestTemp> findBySamplingFrequencyTempIdIn(Collection<String> frequencyTempIds);

    /**
     * 根据周期频次id删除测试项目数据
     *
     * @param frequencyTempIds 周期频次id
     */
    void deleteBySamplingFrequencyTempIdIn(List<String> frequencyTempIds);

}
