package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.CommentService;
import com.sinoyd.lims.pro.criteria.CommentCriteria;
import com.sinoyd.lims.pro.dto.DtoComment;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * Comment服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: Comment服务")
 @RestController
 @RequestMapping("api/pro/comment")
 public class CommentController extends BaseJpaController<DtoComment, String,CommentService> {
    /**
     * 分页动态条件查询Comment
     *
     * @param commentCriteria 条件参数
     * @return RestResponse<List < Comment>>
     */
    @ApiOperation(value = "分页动态条件查询Comment", notes = "分页动态条件查询Comment")
    @GetMapping
    public RestResponse<List<DtoComment>> findByPage(CommentCriteria commentCriteria) {
        PageBean<DtoComment> pageBean = super.getPageBean();
        RestResponse<List<DtoComment>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, commentCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 获取评论与日志
     * @param objectId 关联id
     * @param objectType 关联类型
     * @return RestResponse<String>
     */
    @ApiOperation(value = "获取评论与日志", notes = "获取评论与日志")
    @GetMapping(path = "/all")
    public RestResponse<DtoComment[]> createReportCode(@RequestParam(name = "objectId") String objectId,@RequestParam(name = "objectType") Integer objectType) {
        RestResponse<DtoComment[]> restResponse = new RestResponse<>();
        restResponse.setData(service.findAllComment(objectId, objectType));
        return restResponse;
    }

    /**
     * 新增评论
     *
     * @param comment 实体列表
     * @return RestResponse<DtoComment>
     */
    @ApiOperation(value = "新增评论", notes = "新增评论")
    @PostMapping
    public RestResponse<DtoComment> create(@RequestBody @Validated DtoComment comment) {
        RestResponse<DtoComment> restResponse = new RestResponse<>();
        restResponse.setData(service.save(comment));
        return restResponse;
    }

    /**
     * 刪除评论
     *
     * @param id 评论id
     */
    @ApiOperation(value = "刪除评论", notes = "刪除评论")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable(name = "id") String id) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setCount(service.logicDeleteById(id));
        return restResp;
    }
}