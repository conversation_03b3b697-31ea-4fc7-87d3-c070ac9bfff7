package com.sinoyd.lims.foreign.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.foreign.DataOutSorceDataVO;
import com.sinoyd.lims.foreign.DataReportSampleInfoVO;
import com.sinoyd.lims.foreign.service.DataOutSorceDataService;
import com.sinoyd.lims.foreign.service.DataReportSampleInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 报告样品信息服务
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Api(tags = "示例: dataReportSampleInfo服务")
@RestController
@RequestMapping("api/pro/foreign/dataReportSampleInfo")
public class DataReportSampleInfoController extends ExceptionHandlerController<DataReportSampleInfoService> {


    /**
     * 数据同步
     *
     * @param map 同步参数
     * @return RestResponse<List < DataAnalyseDataVO>>
     */
    @ApiOperation(value = "数据同步", notes = "数据同步")
    @PostMapping("/syncData")
    public RestResponse<List<DataReportSampleInfoVO>> getSyncData(@RequestBody Map<String, Object> map) {
        List<DataReportSampleInfoVO> curveVOList = service.getSyncData(map);
        RestResponse<List<DataReportSampleInfoVO>> response = new RestResponse();
        response.setData(curveVOList);
        return response;
    }
}
