package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoReportAchievement2Person;

import java.util.List;
import java.util.Map;

/**
 *  报告绩效管理service
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2023/03/13
 */
public interface ReportAchievement2PersonService extends IBaseJpaService<DtoReportAchievement2Person, String> {

    /**
     * 选择人员
     * @param personIds 人员id
     */
    void selectPerson(List<String> personIds);

    /**
     * 校验选择人员
     * @param personIds 人员id
     */
    void verifySelectPerson(List<String> personIds);

    /**
     * 更新数据
     * @param year 年份
     */
    void updateData(Integer year, List<String> ids);

    /**
     * 按月份统计
     * @return Map<String, Integer>
     */
    Map<String, Long> chartForPerMonth();

}
