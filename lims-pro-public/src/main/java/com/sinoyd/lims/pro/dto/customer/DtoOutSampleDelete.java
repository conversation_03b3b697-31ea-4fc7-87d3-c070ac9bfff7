package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoSample;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 外部样品数据
 * <AUTHOR>
 * @version V1.0.0 2020/2/5
 * @since V100R001
 */
@Data
public class DtoOutSampleDelete {
    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 原样id
     */
    private List<String> sampleIds = new ArrayList<>();

    /**
     * 点位id
     */
    private List<String> sampleFolderIds = new ArrayList<>();

    /**
     * 频次id
     */
    private List<String> samplingFrequencyIds = new ArrayList<>();

    /**
     * 质控样id
     */
    private List<String> associateSampleIds = new ArrayList<>();

    /**
     * 室内样id
     */
    private List<String> innerSampleIds = new ArrayList<>();

    /**
     * 质控id
     */
    private List<String> qcIds = new ArrayList<>();

    /**
     * 数据id
     */
    private List<String> analyseDataIds = new ArrayList<>();

    /**
     * 室内质控数据id
     */
    private List<String> innerAnalyseDataIds = new ArrayList<>();

    /**
     * 分析人id
     */
    private List<String> analystIds = new ArrayList<>();

    /**
     * 分析数据类型
     */
    private List<DtoAnalyseData> analyseDatas = new ArrayList<>();

    /**
     * 检测单id集合
     */
    private List<String> workSheetFolderIds = new ArrayList<>();

    /**
     * 获取所有现场样品id
     *
     * @return 所有样品id
     */
    public List<String> getAllLocalSampleIds() {
        List<String> ids = new ArrayList<>(this.sampleIds);
        ids.addAll(this.associateSampleIds);
        return ids;
    }

    /**
     * 获取所有样品id
     *
     * @return 所有样品id
     */
    public List<String> getAllSampleIds() {
        List<String> ids = new ArrayList<>(this.sampleIds);
        ids.addAll(this.associateSampleIds);
        ids.addAll(this.innerSampleIds);
        return ids;
    }
}
