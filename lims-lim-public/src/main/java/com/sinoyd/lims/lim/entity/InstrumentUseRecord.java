package com.sinoyd.lims.lim.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;


/**
 * InstrumentUseRecord实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="InstrumentUseRecord")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class InstrumentUseRecord extends LimsBaseEntity {

   private static final long serialVersionUID = 1L;

    public  InstrumentUseRecord() {
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
    * 仪器id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("仪器id")
	private String instrumentId;

    /**
    * 表单id（送样单-采样、领样单-现场分析、检测单-实验室）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("表单id（送样单-采样、领样单-现场分析、检测单-实验室）")
	private String objectId;

    /**
    * 环境记录id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("环境记录id")
	private String environmentalManageId;

    /**
    * 使用对象类型（枚举EnumInsUseObjType：1采样，2：实验室分析，4：现场分析）
    */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("使用对象类型（枚举EnumInsUseObjType：1采样，2：实验室分析，4：现场分析）")
    private Integer objectType;

    /**
    * 使用人id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("使用人id")
    private String usePersonId;

    /**
    * 开始使用时间
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("开始使用时间")
    private Date startTime;

    /**
    * 结束使用时间
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("结束使用时间")
    private Date endTime;

    /**
    * 测试项目id支持多条
    */
    @Column(length=1000)
    @ApiModelProperty("测试项目id支持多条")
    @Length(message = "测试项目id支持多条{validation.message.length}", max = 1000)
    private String testIds;

    /**
    * 温度
    */
    @Column(length=50)
    @ApiModelProperty("温度")
    @Length(message = "温度{validation.message.length}", max = 1000)
    private String temperature;

    /**
    * 湿度
    */
    @Column(length=50)
    @ApiModelProperty("湿度")
    @Length(message = "湿度{validation.message.length}", max = 1000)
    private String humidity;

    /**
    * 大气压
    */
    @Column(length=50)
    @ApiModelProperty("大气压")
    @Length(message = "大气压{validation.message.length}", max = 1000)
    private String pressure;

    /**
    * 使用前情况
    */
    @ApiModelProperty("使用前情况")
    @Length(message = "使用前情况{validation.message.length}", max = 1000)
    private String beforeUseSituation;

    /**
    * 使用后情况
    */
    @ApiModelProperty("使用后情况")
    @Length(message = "测试项目id支持多条{validation.message.length}", max = 1000)
    private String beforeAfterSituation;

    /**
    * 是否辅助仪器
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否辅助仪器")
    private Boolean isAssistInstrument;

    /**
    * 备注
    */
    @Column(length=1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
    private String remark;

    /**
    * 仪器的有效期
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("仪器的有效期")
    private Date insOriginDate;

    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;

    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;

    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;

    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;

    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;

    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;

 }