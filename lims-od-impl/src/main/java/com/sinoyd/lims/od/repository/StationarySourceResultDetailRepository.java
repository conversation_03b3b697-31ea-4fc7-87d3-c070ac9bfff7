package com.sinoyd.lims.od.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoTraining;
import com.sinoyd.lims.od.dto.DtoStationarySourceResultDetail;

import java.util.List;

/**
 * StationarySourceResultDetailRepository
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
public interface StationarySourceResultDetailRepository extends IBaseJpaRepository<DtoStationarySourceResultDetail, String> {


    /**
     * 根据固定源结果查询详情
     *
     * @param resultId 固定源结果id
     * @return 固定源结果详情
     */
    List<DtoStationarySourceResultDetail> findByResultId(String resultId);

    /**
     * 根据固定源结果查询详情
     *
     * @param resultIds 固定源结果ids
     * @return 固定源结果详情
     */
    List<DtoStationarySourceResultDetail> findByResultIdIn(List<String> resultIds);
}
