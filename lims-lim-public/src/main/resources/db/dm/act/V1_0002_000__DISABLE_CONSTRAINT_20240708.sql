ALTER TABLE ACT_RU_JOB DISABLE CONSTRAINT ACT_FK_JOB_EXCEPTION;
ALTER TABLE ACT_RU_EXECUTION DISABLE CONSTRAINT ACT_FK_EXE_SUPER;
ALTER TABLE ACT_RU_EXECUTION DISABLE CONSTRAINT ACT_FK_EXE_PROCDEF;
ALTER TABLE ACT_RU_EXECUTION DISABLE CONSTRAINT ACT_FK_EXE_PARENT;
ALTER TABLE ACT_RU_EXECUTION DISABLE CONSTRAINT ACT_FK_EXE_PROCINST;
ALTER TABLE ACT_RE_MODEL DISABLE CONSTRAINT ACT_FK_MODEL_SOURCE_EXTRA;
ALTER TABLE ACT_RE_MODEL DISABLE CONSTRAINT ACT_FK_MODEL_SOURCE;
ALTER TABLE ACT_RE_MODEL DISABLE CONSTRAINT ACT_FK_MODEL_DEPLOYMENT;
ALTER TABLE ACT_GE_BYTEARRAY DISABLE CONSTRAINT ACT_FK_BYTEARR_DEPL;
ALTER TABLE ACT_PROCDEF_INFO DISABLE CONSTRAINT ACT_FK_INFO_JSON_BA;
ALTER TABLE ACT_PROCDEF_INFO DISABLE CONSTRAINT ACT_FK_INFO_PROCDEF;
ALTER TABLE ACT_RU_EVENT_SUBSCR DISABLE CONSTRAINT ACT_FK_EVENT_EXEC;
ALTER TABLE ACT_RU_VARIABLE DISABLE CONSTRAINT ACT_FK_VAR_BYTEARRAY;
ALTER TABLE ACT_RU_VARIABLE DISABLE CONSTRAINT ACT_FK_VAR_PROCINST;
ALTER TABLE ACT_RU_VARIABLE DISABLE CONSTRAINT ACT_FK_VAR_EXE;
ALTER TABLE ACT_RU_IDENTITYLINK DISABLE CONSTRAINT ACT_FK_ATHRZ_PROCEDEF;
ALTER TABLE ACT_RU_IDENTITYLINK DISABLE CONSTRAINT ACT_FK_IDL_PROCINST;
ALTER TABLE ACT_RU_IDENTITYLINK DISABLE CONSTRAINT ACT_FK_TSKASS_TASK;
ALTER TABLE ACT_RU_TASK DISABLE CONSTRAINT ACT_FK_TASK_PROCDEF;
ALTER TABLE ACT_RU_TASK DISABLE CONSTRAINT ACT_FK_TASK_PROCINST;
ALTER TABLE ACT_RU_TASK DISABLE CONSTRAINT ACT_FK_TASK_EXE;
ALTER TABLE ACT_ID_MEMBERSHIP DISABLE CONSTRAINT ACT_FK_MEMB_GROUP;
ALTER TABLE ACT_ID_MEMBERSHIP DISABLE CONSTRAINT ACT_FK_MEMB_USER;
