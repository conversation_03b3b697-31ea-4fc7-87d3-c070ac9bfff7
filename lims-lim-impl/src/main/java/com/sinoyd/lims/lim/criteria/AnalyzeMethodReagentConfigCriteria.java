package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Calendar;
import java.util.Date;

/**
 * 试剂配置记录查询条件
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-05-14
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AnalyzeMethodReagentConfigCriteria extends BaseCriteria {

    /**
     * 开始时间
     */
    private String dtBegin;

    /**
     * 结束时间
     */
    private String dtEnd;

    /**
     * 分析方法id
     */
    private String analyzeMethodId;

    /**
     * 关键字：分析方法名称、标准编号、试剂名称、试剂规格、配制过程
     */
    private String key;

    /**
     * 试剂类型（1：一般试剂 2：标准溶液）
     */
    private Integer reagentType;

    /**
     * 0：未过期 1：已过期
     */
    private Integer status = -1;


    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and analyzeMethod.id = analyzeMethodReagentConfig.analyzeMethodId");

        if (StringUtil.isNotEmpty(this.analyzeMethodId) && !UUIDHelper.GUID_EMPTY.equals(this.analyzeMethodId)) {
            condition.append(" and analyzeMethodReagentConfig.analyzeMethodId = :analyseMethodId");
            values.put("analyseMethodId", this.analyzeMethodId);
        }
        if (StringUtil.isNotEmpty(this.dtBegin)) {
            Date from = DateUtil.stringToDate(this.dtBegin, DateUtil.YEAR);
            condition.append(" and analyzeMethodReagentConfig.configDate >= :dtBegin");
            values.put("dtBegin", from);
        }
        if (StringUtil.isNotEmpty(this.dtEnd)) {
            Date to = DateUtil.stringToDate(this.dtEnd, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
            condition.append(" and analyzeMethodReagentConfig.configDate < :dtEnd");
            values.put("dtEnd", to);
        }
        // 关键字模糊查找
        if (StringUtils.isNotNullAndEmpty(this.key)) {
            condition.append(" and (analyzeMethodReagentConfig.reagentName like :key");
            condition.append(" or analyzeMethodReagentConfig.reagentSpecification like :key ");
            condition.append(" or analyzeMethodReagentConfig.configurationSolution like :key ");
            condition.append(" or analyzeMethodReagentConfig.course like :key");
            condition.append(" or analyzeMethodReagentConfig.context like :key)");
            values.put("key", "%" + this.key + "%");
        }
        if (StringUtil.isNotNull(this.reagentType) && !this.reagentType.equals(0)) {
            condition.append(" and analyzeMethodReagentConfig.reagentType = :reagentType ");
            values.put("reagentType", this.reagentType);
        }
        if (StringUtil.isNotNull(this.status) && !this.status.equals(-1)) {
    		if (this.status.equals(0)) {
                condition.append(" and TO_DAYS(NOW()) <= TO_DAYS(expiryDate) ");
            } else if (this.status.equals(1)) {
                condition.append(" and TO_DAYS(NOW()) > TO_DAYS(expiryDate) ");
            }
        }
        condition.append(" and analyzeMethodReagentConfig.isDeleted = 0 ");
        condition.append(" and analyzeMethod.isDeleted = 0 ");
        return condition.toString();
    }
}


