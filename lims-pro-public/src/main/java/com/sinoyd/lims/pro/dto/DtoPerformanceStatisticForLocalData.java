package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.PerformanceStatisticForLocalData;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoPerformanceStatisticForLocalData实体
 * <AUTHOR>
 * @version V1.0.0 2020/2/21
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_PerformanceStatisticForLocalData")
 @Data
 @DynamicInsert
 public  class DtoPerformanceStatisticForLocalData extends PerformanceStatisticForLocalData {
    private static final long serialVersionUID = 1L;

    /**
     * 构造函数 默认
     */
    public DtoPerformanceStatisticForLocalData(){

    }

    /**
     * 构造函数 工作量统计总计行
     */
    public DtoPerformanceStatisticForLocalData(Integer sample,Integer localeGap,Integer parallel,Integer valid){
        this.setSample(sample);
        this.setLocaleGap(localeGap);
        this.setParallel(parallel);
        this.setValid(valid);
    }
    /**
     * 检测类型名称
     */
    @Transient
    private String sampleTypeName;
}