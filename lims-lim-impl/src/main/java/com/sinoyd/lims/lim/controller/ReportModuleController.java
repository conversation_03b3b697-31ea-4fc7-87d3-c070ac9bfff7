package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.ReportModuleCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoReportModule;
import com.sinoyd.lims.lim.service.ReportModuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * ReportModule服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2020/10/14
 * @since V100R001
 */
@Api(tags = "示例: ReportModule服务")
@RestController
@RequestMapping("api/lim/reportModule")
@Validated
public class ReportModuleController extends BaseJpaController<DtoReportModule, String, ReportModuleService> {


    /**
     * 分页动态条件查询ReportModule
     *
     * @param reportModuleCriteria 条件参数
     * @return RestResponse<List < ReportModule>>
     */
    @ApiOperation(value = "分页动态条件查询ReportModule", notes = "分页动态条件查询ReportModule")
    @GetMapping
    public RestResponse<List<DtoReportModule>> findByPage(ReportModuleCriteria reportModuleCriteria) {
        PageBean<DtoReportModule> pageBean = super.getPageBean();
        RestResponse<List<DtoReportModule>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, reportModuleCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询ReportModule
     *
     * @param id 主键id
     * @return RestResponse<DtoReportModule>
     */
    @ApiOperation(value = "按主键查询reportModule", notes = "按主键查询按主键查询reportModule")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoReportModule> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoReportModule> restResponse = new RestResponse<>();
        DtoReportModule reportModule = service.findOne(id);
        restResponse.setData(reportModule);
        restResponse.setRestStatus(StringUtil.isNull(reportModule) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }


    /**
     * 新增ReportModule
     *
     * @param reportModule 实体列表
     * @return RestResponse<DtoReportModule>
     */
    @ApiOperation(value = "新增ReportModule", notes = "新增ReportModule")
    @PostMapping
    public RestResponse<DtoReportModule> create(@Validated @RequestBody DtoReportModule reportModule) {
        RestResponse<DtoReportModule> restResponse = new RestResponse<>();
        restResponse.setData(service.save(reportModule));
        return restResponse;
    }

    /**
     * 修改ReportModule
     *
     * @param ReportModule 实体列表
     * @return RestResponse<DtoReportModule>
     */
    @ApiOperation(value = "修改ReportModule", notes = "修改ReportModule")
    @PutMapping
    public RestResponse<DtoReportModule> update(@Validated @RequestBody DtoReportModule ReportModule) {
        RestResponse<DtoReportModule> restResponse = new RestResponse<>();
        restResponse.setData(service.update(ReportModule));
        return restResponse;
    }

    /**
     * "根据id批量删除ReportModule
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除ReportModule", notes = "根据id批量删除ReportModule")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.deleteModule(ids);
        restResp.setCount(count);
        return restResp;
    }
}