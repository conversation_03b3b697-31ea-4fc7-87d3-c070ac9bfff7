package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;


/**
 * Project2Report实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/04/25
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "Project2Report")
@Data
@EntityListeners(AuditingEntityListener.class)
public class Project2Report implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    private String projectId;

    /**
     * 报告id
     */
    @Column(length = 50)
    @ApiModelProperty("报告id")
    private String reportId;

    /**
     * 上传状态 0 :未上传 1：已上传
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("上传状态 0 :未上传 1：已上传")
    private Integer uploadStatus;
}