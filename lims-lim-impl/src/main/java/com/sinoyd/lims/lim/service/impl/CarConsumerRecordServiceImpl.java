package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.dto.DtoUser;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.UserService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.criteria.CarConsumerRecordCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoCarConsumerRecord;
import com.sinoyd.lims.lim.dto.lims.DtoCarManage;
import com.sinoyd.lims.lim.repository.lims.CarConsumerRecordRepository;
import com.sinoyd.lims.lim.service.CarConsumerRecordService;

import com.sinoyd.lims.lim.service.CarManageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 车辆消费记录接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2019-05-10
 * @since V100R001
 */
@Service
public class CarConsumerRecordServiceImpl
        extends BaseJpaPhysicalDeleteServiceImpl<DtoCarConsumerRecord, String, CarConsumerRecordRepository>
        implements CarConsumerRecordService {

    @Autowired
    private UserService userService;

    @Autowired
    @Lazy
    private CarManageService carManageService;

    @Autowired
    private CodeService codeService;

    /**
     * 分页查询
     */
    @Override
    public void findByPage(PageBean pageBean, BaseCriteria criteria) {
        // 设置查询的实体类名及别名
        pageBean.setEntityName("DtoCarConsumerRecord p");
        // 设置查询返回的字段、实体别名表示所有字段
        pageBean.setSelect("select p");

        CarConsumerRecordCriteria carCriteria = (CarConsumerRecordCriteria) criteria;
        if (StringUtils.isNotNullAndEmpty(carCriteria.getDeptId()) && !UUIDHelper.GUID_EMPTY.equals(carCriteria.getDeptId())) {
            List<DtoUser> users = userService.findByDeptGuid(carCriteria.getDeptId());
            if ( StringUtil.isNotEmpty(users)) {
                carCriteria.setSalesManIds(users.stream().map(DtoUser::getId).collect(Collectors.toList()));
            }
        }

        super.findByPage(pageBean, carCriteria);
    }

    @Override
    public DtoCarConsumerRecord findOne(String id) {
        DtoCarConsumerRecord dtoCarConsumerRecord = super.findOne(id);

        if (StringUtil.isNotNull(dtoCarConsumerRecord)) {
            String carCode = "";//车辆编号
            String typeName = "";//类型名称
            DtoCarManage dtoCarManage = carManageService.findOne(dtoCarConsumerRecord.getCarId());
            if (StringUtil.isNotNull(dtoCarManage)) {
                carCode = dtoCarManage.getCarCode();
            }
            DtoCode dtoCode = codeService.findByCode(dtoCarConsumerRecord.getType());

            if (StringUtil.isNotNull(dtoCode)) {
                typeName = dtoCode.getDictName();
            }
            dtoCarConsumerRecord.setCarCode(carCode);
            dtoCarConsumerRecord.setTypeName(typeName);
        }
        dtoCarConsumerRecord.setFormatDate(DateUtil.nowTime(DateUtil.YEAR_NO_SPRIT));
        return dtoCarConsumerRecord;
    }
}