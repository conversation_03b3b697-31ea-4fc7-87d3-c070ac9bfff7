package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoContractCollectionPlan;

import java.util.List;

/**
 * 收款计划仓储
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-05-10
 * @since V100R001
 */
public interface ContractCollectionPlanRepository
        extends IBaseJpaPhysicalDeleteRepository<DtoContractCollectionPlan, String> {


    /**
     * 查询相应的合同的收款计划
     *
     * @param contractIds 合同ids
     * @return 返回相应的收款计划
     */
    List<DtoContractCollectionPlan> findAllByContractIdIn(List<String> contractIds);
}