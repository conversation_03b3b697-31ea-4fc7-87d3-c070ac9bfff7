package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoSamplingArrange;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 采样安排计划接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/08
 * @since V100R001
 */
public interface SamplingArrangeService extends IBaseJpaService<DtoSamplingArrange, String> {
    /**
     * 批量保存采样安排
     * @param dtoSamplingArrange 安排属性实体
     */
    DtoSamplingArrange batchSaveArrange(DtoSamplingArrange dtoSamplingArrange);

    /**
     * 批量取消安排
     * @param ids 安排标识列表
     */
    void batchDelArrange(List<String> ids);

    /**
     * 采样计划预览列表
     * @param data          查询数据
     * @param previewData   结果数据集
     */
    void collectPreviewData(List<DtoSamplingArrange> data, List<Map<String, Object>> previewData);

    /**
     * 完整安排,设置安排状态为是
     * @param ids 标识列表
     */
    void submitArrange(List<String> ids);

    /**
     * 两级展示采样啊拿牌
     * @param pageBean                   临时容器
     * @param samplingArrangeCriteria    参数
     */
    PageBean<DtoProject>  showTree( PageBean<DtoSamplingArrange> pageBean, BaseCriteria samplingArrangeCriteria);

    /**
     * 根据采样计划id查询数据
     * @param samplingPlanId 采样计划id
     * @return  DtoSamplingArrange
     */
    DtoSamplingArrange findBySamplingPlanId(String samplingPlanId);

    /**
     * 提交审核操作
     *
     * @param samplingPlanIds 采样计划id
     * @param status 审核状态
     */
    void submit(List<String> samplingPlanIds, String status);

    /**
     * 附件
     * @param id 参数
     * @return DtoSamplingArrange
     */
    DtoSamplingArrange findAttachPath(String id);

    /**
     * 下载采样计划安排表
     * @param criteria 查询条件
     * @param response 响应流
     */
    void download(BaseCriteria criteria, HttpServletResponse response);

    /**
     * 点位周期动态分页查询
     * @param folderPeriodCriteria 查询条件
     * @return 安排列表
     */
    void folderPeriodPageQuery(PageBean<DtoSamplingArrange> pageBean, BaseCriteria folderPeriodCriteria);
}
