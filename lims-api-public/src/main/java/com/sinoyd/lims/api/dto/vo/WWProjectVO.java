package com.sinoyd.lims.api.dto.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class WWProjectVO {

    /**
     * 任务Id
     */
    private String taskId;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 合同性质
     */
    private String contractNature;

    /**
     * 合同附件
     */
    private String contractAdd;

    /**
     * 甲方法人
     */
    private String legalPersonA;

    /**
     * 乙方法人
     */
    private String legalPersonB;

    /**
     * 甲方机构名称
     */
    private String institutionNameA;

    /**
     * 乙方机构名称
     */
    private String institutionNameB;

    /**
     * 甲方企业类型
     */
    private String enterpriseTypeA;

    /**
     * 乙方机构类别
     */
    private String enterpriseTypeB;

    /**
     * 履行周期开始时间
     */
    private Date periodStartTime;

    /**
     * 履行周期结束时间
     */
    private Date periodEndTime;

    /**
     * 合同金额
     */
    private BigDecimal contractSum;

    /**
     * 累计任务金额
     */
    private BigDecimal cumulativeTaskSum;

    /**
     * 已绑定任务数
     */
    private Integer bindTaskNum;

    /**
     * 采集单总数
     */
    private Integer collectNumber;

    /**
     * 采集单号
     */
    private String collectNO;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目编号
     */
    private String projectNumber;
}
