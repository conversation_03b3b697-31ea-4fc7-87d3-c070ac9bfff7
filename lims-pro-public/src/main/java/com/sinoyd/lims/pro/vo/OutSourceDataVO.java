package com.sinoyd.lims.pro.vo;

import com.sinoyd.lims.pro.dto.DtoOutSourceData;
import lombok.Data;

import java.util.List;

/**
 * 数据分包前端传输类
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023/2/8
 **/
@Data
public class OutSourceDataVO {

    /**
     * 数据分包list
     */
    private List<DtoOutSourceData> dtoList;

    /**
     * 分析方法名称
     */
    private String analyzeMethodName;

    /**
     * 分析方法id
     */
    private String analyzeMethodId;

    /**
     * 量纲名称
     */
    private String dimensionName;

    /**
     * 量纲id
     */
    private String dimensionId;
}
