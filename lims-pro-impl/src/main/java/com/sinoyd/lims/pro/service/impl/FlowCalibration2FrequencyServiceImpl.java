package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.FlowCalibration2FrequencyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * DtoFlowCalibration2Frequency操作接口实现
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2024/11/18
 */
@Service
@Slf4j
public class FlowCalibration2FrequencyServiceImpl
        extends BaseJpaPhysicalDeleteServiceImpl<DtoFlowCalibration2Frequency,String, FlowCalibration2FrequencyRepository>
        implements FlowCalibration2FrequencyService {

    private SampleFolderRepository sampleFolderRepository;

    private ProjectRepository projectRepository;

    private SampleTypeRepository sampleTypeRepository;

    private SamplingFrequencyTestRepository samplingFrequencyTestRepository;

    private SamplingFrequencyRepository samplingFrequencyRepository;

    private TestRepository testRepository;

    private SampleRepository sampleRepository;

    private ReceiveSampleRecordRepository receiveSampleRecordRepository;

    private PersonRepository personRepository;

    private FlowCalibrationRepository flowCalibrationRepository;

    @Override
    public void findByPage(PageBean<DtoFlowCalibration2Frequency> page, BaseCriteria criteria) {
        page.setEntityName("DtoFlowCalibration2Frequency f");
        page.setSelect("select f");
        super.findByPage(page, criteria);
        List<DtoFlowCalibration2Frequency> list = page.getData();
        loadTransientFields(list);
        //列表按采样日期倒序、检测类型、送样单号、点位名称顺序排列；
        list.sort(Comparator.comparing(DtoFlowCalibration2Frequency::getActualDateStr,Comparator.nullsLast(Comparator.reverseOrder()))
                .thenComparing(DtoFlowCalibration2Frequency::getSampleType,Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(DtoFlowCalibration2Frequency::getRecordCode,Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(DtoFlowCalibration2Frequency::getWatchSpot,Comparator.nullsLast(Comparator.naturalOrder())));
    }

    @Override
    public List<DtoFlowCalibration2Frequency> findByReceive(String receiveId, String flowCalibrationId) {
        List<DtoSample> samples = sampleRepository.findByReceiveId(receiveId);
        List<String> keys = samples.stream().filter(v->StringUtil.isNotEmpty(v.getSampleFolderId())&&v.getCycleOrder()!=null)
                .map(v->v.getSampleFolderId()+"_"+v.getCycleOrder()).distinct().collect(Collectors.toList());
        List<DtoFlowCalibration2Frequency> list = repository.findByFlowCalibrationIdIn(Collections.singletonList(flowCalibrationId))
                .stream().filter(v->StringUtil.isNotEmpty(v.getSampleFolderId())&&v.getPeriodCount()!=null
                        &&keys.contains(v.getSampleFolderId()+"_"+v.getPeriodCount())).collect(Collectors.toList());
        loadTransientFields(list);
        return list;
    }

    @Override
    @Transactional
    public DtoFlowCalibration2Frequency save(DtoFlowCalibration2Frequency entity) {
        if(repository.countByFlowCalibrationIdAndSampleFolderIdAndPeriodCountAndIdNot(entity.getFlowCalibrationId(),
                entity.getSampleFolderId(),entity.getPeriodCount(),entity.getId())>0){
            throw new BaseException("已关联过该点位周期，不可重复关联");
        }
        DtoFlowCalibration2Frequency rt = super.save(entity);
        maintenanceReceiveId(Collections.singletonList(entity.getFlowCalibrationId()));
        return rt;
    }

    @Override
    @Transactional
    public List<DtoFlowCalibration2Frequency> save(Collection<DtoFlowCalibration2Frequency> entities) {
        List<DtoFlowCalibration2Frequency> existList = repository.findAll();
        entities.forEach(v->{
            if(existList.stream().anyMatch(e->e.getFlowCalibrationId().equals(v.getFlowCalibrationId())
                    &&e.getSampleFolderId().equals(v.getSampleFolderId())
                    &&e.getPeriodCount().equals(v.getPeriodCount())
                    &&!e.getId().equals(v.getId()))){
                throw new BaseException("已关联过该点位周期，不可重复关联");
            }
        });
        List<DtoFlowCalibration2Frequency> list = super.save(entities);
        maintenanceReceiveId(list.stream().map(DtoFlowCalibration2Frequency::getFlowCalibrationId).collect(Collectors.toList()));
        return list;
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<?> ids) {
        List<DtoFlowCalibration2Frequency> list = repository.findAll((Iterable<String>) ids);
        maintenanceReceiveId(list.stream().map(DtoFlowCalibration2Frequency::getFlowCalibrationId).collect(Collectors.toList()));
        return super.logicDeleteById(ids);
    }

    /**
     * 维护记录表上的receiveId
     * @param flowCalibrationIds 流量校准标识集合
     */
    private void maintenanceReceiveId(List<String> flowCalibrationIds){
        if(StringUtil.isNotEmpty(flowCalibrationIds)){
            List<DtoFlowCalibration> saveList = new ArrayList<>();
            List<DtoFlowCalibration> flowCalibrationList = flowCalibrationRepository.findAll(flowCalibrationIds);
            List<DtoFlowCalibration2Frequency> flowCalibration2FrequencyList = repository.findByFlowCalibrationIdIn(flowCalibrationIds);
            List<String> allSampleFolderIds = flowCalibration2FrequencyList.stream().map(DtoFlowCalibration2Frequency::getSampleFolderId).collect(Collectors.toList());
            List<DtoSample> allSampleList =  sampleRepository.findBySampleFolderIdIn(allSampleFolderIds);
            for (DtoFlowCalibration flowCalibration:flowCalibrationList) {
                List<String> sampleFolderIds = flowCalibration2FrequencyList.stream().filter(f->flowCalibration.getId().equals(f.getFlowCalibrationId()))
                        .map(DtoFlowCalibration2Frequency::getSampleFolderId).collect(Collectors.toList());
                if(StringUtil.isNotEmpty(sampleFolderIds)){
                    Set<String> receiveIds = new HashSet<>();
                    List<DtoSample> sampleList =  allSampleList.stream().filter(s->sampleFolderIds.contains(s.getSampleFolderId())).collect(Collectors.toList());
                    flowCalibration2FrequencyList.forEach(f->sampleList.stream().filter(s->f.getSampleFolderId().equals(s.getSampleFolderId())
                            &&f.getPeriodCount().equals(s.getCycleOrder())).findFirst().ifPresent(s->receiveIds.add(s.getReceiveId())));
                    flowCalibration.setReceiveId(receiveIds.isEmpty()?UUIDHelper.GUID_EMPTY:String.join(",",receiveIds));
                }else{
                    flowCalibration.setReceiveId(UUIDHelper.GUID_EMPTY);
                }
                saveList.add(flowCalibration);
            }
            flowCalibrationRepository.save(saveList);
        }

    }

    /**
     * 填充附加显示字段
     * @param list 原始数据
     */
    private void loadTransientFields(List<DtoFlowCalibration2Frequency> list){
        if(StringUtil.isNotEmpty(list)){
            List<String> folderIdList = list.stream().map(DtoFlowCalibration2Frequency::getSampleFolderId).collect(Collectors.toList());
            List<DtoSampleFolder> dtoSampleFolderList = sampleFolderRepository.findAll(folderIdList);
            List<String> projectIds = dtoSampleFolderList.stream().map(DtoSampleFolder::getProjectId).collect(Collectors.toList());
            List<DtoProject> dtoProjectList =StringUtil.isNotEmpty(projectIds)? projectRepository.findAll(projectIds):new ArrayList<>();
            List<DtoSampleType> dtoSampleTypeList = sampleTypeRepository.findAll();
            List<DtoSamplingFrequency> dtoSamplingFrequencyList = samplingFrequencyRepository.findBySampleFolderIdIn(folderIdList);
            List<DtoSamplingFrequencyTest> dtoSamplingFrequencyTestList = samplingFrequencyTestRepository.findBySampleFolderIdIn(folderIdList);
            List<DtoTest> dtoTestList = testRepository.findAll();
            List<DtoSample> sampleList = sampleRepository.findBySampleFolderIdIn(folderIdList);
            List<DtoReceiveSampleRecord> recordList = receiveSampleRecordRepository.findAll();
            List<DtoPerson> personList = personRepository.findAll();
            for (DtoFlowCalibration2Frequency c2f:list) {
                //周期文本
                c2f.setPeriodName("第" + c2f.getPeriodCount() + "周期");
                DtoSampleFolder dtoSampleFolder = dtoSampleFolderList.stream().filter(f -> f.getId().equals(c2f.getSampleFolderId()))
                        .findFirst().orElse(null);
                if (dtoSampleFolder != null) {
                    //点位名称
                    c2f.setWatchSpot(dtoSampleFolder.getWatchSpot());
                    DtoProject dtoProject = dtoProjectList.stream().filter(p -> p.getId().equals(dtoSampleFolder.getProjectId())).findFirst().orElse(null);
                    if (dtoProject != null) {
                        //项目编号 项目名称
                        c2f.setProjectCode(dtoProject.getProjectCode());
                        c2f.setProjectName(dtoProject.getProjectName());
                    }
                    //检测类型
                    dtoSampleTypeList.stream().filter(t -> t.getId().equals(dtoSampleFolder.getSampleTypeId())).findFirst()
                            .ifPresent(dtoSampleType->c2f.setSampleType(dtoSampleType.getTypeName()));
                    //分析项目名称
                    List<String> tempFrequencyIdList = dtoSamplingFrequencyList.stream().filter(a -> a.getSampleFolderId().equals(c2f.getSampleFolderId())
                            && a.getPeriodCount().equals(c2f.getPeriodCount())).map(DtoSamplingFrequency::getId).collect(Collectors.toList());
                    List<String> testIds = dtoSamplingFrequencyTestList.stream().filter(t -> tempFrequencyIdList.contains(t.getSamplingFrequencyId()))
                            .map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList());
                    List<DtoTest> testList = dtoTestList.stream().filter(t -> testIds.contains(t.getId())).collect(Collectors.toList());
                    List<String> analyzeItemList = testList.stream().map(DtoTest::getRedAnalyzeItemName).distinct().sorted().collect(Collectors.toList());
                    c2f.setRedAnalyzeItemName(String.join("、", analyzeItemList));
                    // 采样人员
                    List<DtoSample> folderSampleList = sampleList.stream().filter(v->StringUtil.isNotEmpty(v.getSampleFolderId())
                            &&v.getSampleFolderId().equals(c2f.getSampleFolderId())).collect(Collectors.toList());
                    Set<String> samplingPersonIds = folderSampleList.stream().map(DtoSample::getSamplingPersonId).filter(StringUtil::isNotEmpty).collect(Collectors.toSet());
                    List<String> names = personList.stream().filter(v->samplingPersonIds.contains(v.getId())).map(DtoPerson::getCName)
                            .distinct().sorted().collect(Collectors.toList());
                    c2f.setActualPeopleStr(String.join("、",names));
                    //采样时间
                    List<String> dates = folderSampleList.stream().filter(v->v.getSamplingTimeBegin()!=null)
                            .map(v-> DateUtil.dateToString(v.getSamplingTimeBegin(),DateUtil.YEAR)).distinct().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
                    c2f.setActualDateStr(String.join("、",dates));
                    //送样单号
                    List<String> receiveIds = folderSampleList.stream().map(DtoSample::getReceiveId).collect(Collectors.toList());
                    List<String> recordCodes = recordList.stream().filter(v->receiveIds.contains(v.getId())).map(DtoReceiveSampleRecord::getRecordCode)
                            .distinct().sorted().collect(Collectors.toList());
                    c2f.setRecordCode(String.join("、",recordCodes));
                }
            }
        }
    }

    @Autowired
    public void setSampleFolderRepository(SampleFolderRepository sampleFolderRepository) {
        this.sampleFolderRepository = sampleFolderRepository;
    }

    @Autowired
    public void setProjectRepository(ProjectRepository projectRepository) {
        this.projectRepository = projectRepository;
    }

    @Autowired
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    public void setSamplingFrequencyTestRepository(SamplingFrequencyTestRepository samplingFrequencyTestRepository) {
        this.samplingFrequencyTestRepository = samplingFrequencyTestRepository;
    }

    @Autowired
    public void setSamplingFrequencyRepository(SamplingFrequencyRepository samplingFrequencyRepository) {
        this.samplingFrequencyRepository = samplingFrequencyRepository;
    }

    @Autowired
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    public void setSampleRepository(SampleRepository sampleRepository) {
        this.sampleRepository = sampleRepository;
    }

    @Autowired
    public void setReceiveSampleRecordRepository(ReceiveSampleRecordRepository receiveSampleRecordRepository) {
        this.receiveSampleRecordRepository = receiveSampleRecordRepository;
    }

    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }

    @Autowired
    public void setFlowCalibrationRepository(FlowCalibrationRepository flowCalibrationRepository) {
        this.flowCalibrationRepository = flowCalibrationRepository;
    }
}
