package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsPartFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsTestFormula;
import com.sinoyd.lims.lim.dto.customer.DtoParamsFormulaDetail;
import com.sinoyd.lims.lim.repository.rcc.ParamsFormulaRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsPartFormulaRepository;
import com.sinoyd.lims.lim.service.ParamsConfigService;
import com.sinoyd.lims.lim.service.ParamsFormulaService;
import com.sinoyd.lims.lim.service.ParamsTestFormulaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * ParamsFormula操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/4
 * @since V100R001
 */
@Service
public class ParamsFormulaServiceImpl extends BaseJpaServiceImpl<DtoParamsFormula, String, ParamsFormulaRepository> implements ParamsFormulaService {

    @Autowired
    @Lazy
    private ParamsTestFormulaService paramsTestFormulaService;

    @Autowired
    @Lazy
    private ParamsConfigService paramsConfigService;

    @Autowired
    private ParamsPartFormulaRepository paramsPartFormulaRepository;

    @Override
    public void findByPage(PageBean<DtoParamsFormula> pb, BaseCriteria paramsFormulaCriteria) {
        pb.setEntityName("DtoParamsFormula a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, paramsFormulaCriteria);
    }

    /**
     * 新增参数公式信息
     *
     * @param paramsFormula 公式
     * @return 返回更新后的公式
     */
    @Transactional
    @Override
    public DtoParamsFormula save(DtoParamsFormula paramsFormula) {

        DtoParamsFormula item = super.save(paramsFormula);
        DtoParamsConfig config = paramsConfigService.findOne(item.getObjectId());
        if ( StringUtil.isNotNull(config)) {
            config.setFormulaId(item.getId());
            paramsConfigService.update(config);
        }
        saveParamsTestFormula(paramsFormula.getParamsTestFormulaList(), item.getId());
        return item;
    }


    /**
     * 更新参数公式信息
     *
     * @param paramsFormula 公式
     * @return 返回更新后的公式
     */
    @Transactional
    @Override
    public DtoParamsFormula update(DtoParamsFormula paramsFormula) {

        //清空公式相关参数，重新保存
        paramsTestFormulaService.deleteByObjId(paramsFormula.getId());
        saveParamsTestFormula(paramsFormula.getParamsTestFormulaList(), paramsFormula.getId());
        return repository.save(paramsFormula);
    }


    @Override
    public List<DtoParamsFormula> findByObjectIds(List<String> objectIds) {
        if (StringUtil.isNotNull(objectIds) && objectIds.size() > 0) {
            return repository.findByObjectIds(objectIds);
        }
        return new ArrayList<>();
    }

    /**
     * 保存测试项目公式
     *
     * @param paramsTestFormulaList 测试项目公式参数
     * @param id                    公式id
     */
    private void saveParamsTestFormula(List<DtoParamsTestFormula> paramsTestFormulaList, String id) {
        if (StringUtil.isNotNull(paramsTestFormulaList) && paramsTestFormulaList.size() > 0) {
            List<DtoParamsTestFormula> paramTestFormulas = new ArrayList<>();
            for (DtoParamsTestFormula params : paramsTestFormulaList) {
                DtoParamsTestFormula newItem = new DtoParamsTestFormula();
                newItem.setObjId(id);
                newItem.setParamsId(params.getParamsId());
                newItem.setParamsName(params.getParamsName());
                newItem.setAlias(params.getAlias());
                newItem.setDimensionId(params.getDimensionId());
                newItem.setDimension(params.getDimension());
                paramTestFormulas.add(newItem);
            }
            paramsTestFormulaService.save(paramTestFormulas);
        }
    }

    @Override
    public List<DtoParamsFormula> findAllDeleted(List<String> ids) {
        return repository.findAllDeleted(ids);
    }

    @Override
    public List<DtoParamsFormula> findAllDeleted() {
        return repository.findAllDeleted();
    }

    /**
     * 获取参数公式明细
     *
     * @param formulaIds 公式id
     * @return 公式明细
     */
    @Override
    public  List<DtoParamsFormulaDetail> findParamsFormulaDetails(List<String> formulaIds) {
        List<DtoParamsFormula> paramsFormulaList = super.findAll(formulaIds);
        List<DtoParamsPartFormula> paramsPartFormulaList = paramsPartFormulaRepository.findByFormulaIdIn(formulaIds);

        List<DtoParamsFormulaDetail> list = new ArrayList<>();
        for (DtoParamsFormula paramsFormula : paramsFormulaList) {
            DtoParamsFormulaDetail detail = new DtoParamsFormulaDetail();
            detail.setId(paramsFormula.getId());
            detail.setFormula(paramsFormula.getFormula());
            detail.setParamsPartFormula(paramsPartFormulaList.stream().filter(p -> p.getFormulaId().equals(paramsFormula.getId())).collect(Collectors.toList()));
            list.add(detail);
        }
        return list;
    }
}