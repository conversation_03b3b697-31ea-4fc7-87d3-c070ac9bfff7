package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;


/**
 * CommentComplimentDetail实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="CommentComplimentDetail")
 @Data
 public  class CommentComplimentDetail implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  CommentComplimentDetail() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 评论Id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("评论Id")
	private String commentId;
    
    /**
    * 操作人Id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("操作人Id")
	private String complimentorId;
    
    /**
    * 操作人的姓名
    */
    @Column(length=50)
    @ApiModelProperty("操作人的姓名")
    @Length(message = "操作人的姓名{validation.message.length}", max = 50)
	private String complimentorName;
    
    /**
    * 操作次数（考虑一个人多次点赞）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("操作次数（考虑一个人多次点赞）")
	private Integer complimentNumber;
    
    /**
    * 操作时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @ApiModelProperty("操作时间")
	private Date complimentDate;
    
    /**
    * 评论类型（0：赞 1：踩）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("评论类型（0：赞 1：踩）")
	private Integer complimentType;
    
    /**
    * 内容
    */
    @ApiModelProperty("内容")
	private String option;
    
    /**
    * 备注
    */
    @Column(length=1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
	private String remark;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
 }