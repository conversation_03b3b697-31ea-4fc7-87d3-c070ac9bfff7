package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.lims.DtoConsumable;
import com.sinoyd.base.service.ConsumableService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoOAConsumablePickListsDetail;
import com.sinoyd.lims.lim.service.OAConsumablePickListsDetailService;
import com.sinoyd.lims.pro.dto.DtoOATask;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;
import com.sinoyd.lims.pro.dto.DtoOATaskRelation;
import com.sinoyd.lims.pro.entity.OATask;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumOATaskType;
import com.sinoyd.lims.pro.repository.OATaskRelationRepository;
import com.sinoyd.lims.pro.service.OAConsumablePickListsService;
import com.sinoyd.lims.pro.service.OATaskRelationService;
import com.sinoyd.lims.pro.service.OATaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 领料申请业务操作接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2019-04-03
 * @since V100R001
 */
@Service

public class OAConsumablePickListsServiceImpl implements OAConsumablePickListsService {
    /**
     * 审批任务服务
     */
    @Autowired
    @Lazy
    private OATaskService oaTaskService;

    /**
     * 审批任务关联服务
     */
    @Autowired
    @Lazy
    private OATaskRelationService oaTaskRelationService;

    @Autowired
    private OATaskRelationRepository oaTaskRelationRepository;

    /**
     * 领料服务
     */
    @Autowired
    @Lazy
    private OAConsumablePickListsDetailService oaConsumablePickListsDetailService;

    /**
     * 领料服务
     */
    @Autowired
    @Lazy
    private ConsumableService consumableService;

    @Override
    public void findConsumableByPage(PageBean<DtoConsumable> pageBean, BaseCriteria baseCriteria) {
        consumableService.findByPage(pageBean, baseCriteria);
        List<DtoConsumable> consumables = pageBean.getData();
        //获取到所有的消耗品相关的领料信息
        Set<String> consumableIds = consumables.stream().map(DtoConsumable::getId).collect(Collectors.toSet());
        List<DtoOAConsumablePickListsDetail> pickList = oaConsumablePickListsDetailService.findByConsumableIds(consumableIds);
        //处理领料详情的冗余数据
        loadPickDetailTransientFields(pickList);
        //处理可领用库存
        for (DtoConsumable consumable : consumables) {
            BigDecimal approvalNum = BigDecimal.ZERO;
            for (BigDecimal materialNum : pickList.stream().filter(p -> consumable.getId().equals(p.getConsumableId())
                    && EnumPRO.EnumOATaskStatus.审批中.getValue().equals(p.getOaTaskStatus()))
                    .map(DtoOAConsumablePickListsDetail::getMaterialNum).collect(Collectors.toList())) {
                approvalNum = approvalNum.add(materialNum);
            }
            consumable.setPickUpInventory(consumable.getInventory().subtract(approvalNum));
        }
        pageBean.setData(consumables);
    }

    @Override
    @Transactional
    public String startProcess(DtoOATaskCreate<List<DtoOAConsumablePickListsDetail>> taskDto) {
        Map<String, Object> vars = new HashMap<>();
        DtoOATask oaTask = oaTaskService.startProcess(EnumOATaskType.领料, taskDto, vars);
        List<DtoOAConsumablePickListsDetail> data = taskDto.getData();
        List<DtoOATaskRelation> taskRelations = new ArrayList<>();
        for (DtoOAConsumablePickListsDetail consumablePickListsDetail : data) {
            consumablePickListsDetail.setId(UUIDHelper.NewID());
            DtoOATaskRelation taskRelation = new DtoOATaskRelation();
            taskRelation.setTaskId(oaTask.getId());
            taskRelation.setObjectId(consumablePickListsDetail.getId());
            taskRelations.add(taskRelation);
        }
        // 添加领料记录
        oaConsumablePickListsDetailService.save(data);

        // 添加审批任务关联信息
        oaTaskRelationService.save(taskRelations);

        return oaTask.getId();
    }

    @Override
    public DtoOATaskDetail<List<DtoOAConsumablePickListsDetail>, List<DtoConsumable>> findOATaskDetail(String taskId) {
        DtoOATask task = oaTaskService.findByTaskId(taskId);

        DtoOATaskDetail<List<DtoOAConsumablePickListsDetail>, List<DtoConsumable>> taskDetail = new DtoOATaskDetail<>();
        taskDetail.setTask(task);
        taskDetail.setAttach(oaTaskService.getOATaskAttachDTO(task.getProcInstId()));

        List<DtoOATaskRelation> relations = oaTaskRelationService.findListByTaskId(taskId);

        // 领料信息
        List<DtoOAConsumablePickListsDetail> consumables = new ArrayList<>();

        // 消耗品信息
        List<DtoConsumable> consumableList = new ArrayList<>();

        List<String> objectIds = relations.stream().map(DtoOATaskRelation::getObjectId).distinct().collect(Collectors.toList());
        if (objectIds.size() > 0) {
            consumables.addAll(oaConsumablePickListsDetailService.findAll(objectIds));
            List<String> consumableIds = consumables.stream().map(DtoOAConsumablePickListsDetail::getConsumableId).distinct().collect(Collectors.toList());
            if (consumableIds.size() > 0) {
                consumableList.addAll(consumableService.findAll(consumableIds));
            }
        }

        // 设置详细信息
        taskDetail.setDetail(consumables);

        // 设置扩展信息
        taskDetail.setExtend(consumableList);

        return taskDetail;
    }

    @Override
    @Transactional
    public OATask saveAsDraft(DtoOATaskCreate<List<DtoOAConsumablePickListsDetail>> taskDto) {
        Map<String, Object> vars = new HashMap<>();
        DtoOATask oaTask = oaTaskService.saveAsDraft(EnumOATaskType.领料, taskDto, vars);
        saveTaskRelation(taskDto, oaTask);
        return oaTask;
    }

    @Override
    @Transactional
    public OATask draftSave(DtoOATaskCreate<List<DtoOAConsumablePickListsDetail>> taskDto) {
        //保存审批信息
        DtoOATask oaTask = oaTaskService.DraftSave(taskDto);
        //移除原关联关系
        List<DtoOATaskRelation> relationList = oaTaskRelationService.findListByTaskId(oaTask.getId());
        oaTaskRelationService.delete(relationList);
        //保存新关联关系
        saveTaskRelation(taskDto, oaTask);
        return oaTask;
    }

    @Override
    @Transactional
    public String draftSubmit(DtoOATaskCreate<List<DtoOAConsumablePickListsDetail>> taskDto) {
        return oaTaskService.draftSubmit(taskDto, new HashMap<>());
    }

    /**
     * 重新保存审批任务关联关系
     *
     * @param taskDto 审批任务数据
     * @param oaTask  保存后的审批任务数据
     */
    private void saveTaskRelation(DtoOATaskCreate<List<DtoOAConsumablePickListsDetail>> taskDto, DtoOATask oaTask) {
        List<DtoOAConsumablePickListsDetail> data = taskDto.getData();
        List<DtoOATaskRelation> taskRelations = new ArrayList<>();
        for (DtoOAConsumablePickListsDetail consumablePickListsDetail : data) {
            DtoOATaskRelation taskRelation = new DtoOATaskRelation();
            taskRelation.setTaskId(oaTask.getId());
            taskRelation.setObjectId(consumablePickListsDetail.getId());
            taskRelations.add(taskRelation);
        }
        // 更新领料记录
        oaConsumablePickListsDetailService.save(data);
        // 添加审批任务关联信息
        oaTaskRelationService.save(taskRelations);
    }

    /**
     * 加载领料审批详情的冗余数据
     *
     * @param pickListsDetails 领料详情集合
     */
    private void loadPickDetailTransientFields(Collection<DtoOAConsumablePickListsDetail> pickListsDetails) {
        List<String> pickDetailIds = pickListsDetails.stream().map(DtoOAConsumablePickListsDetail::getId).collect(Collectors.toList());
        List<DtoOATaskRelation> relationList = oaTaskRelationRepository.findByObjectIdIn(pickDetailIds);
        Map<String, DtoOATaskRelation> relationMap = relationList.stream().collect(Collectors.toMap(DtoOATaskRelation::getObjectId, p -> p, (p1, p2) -> p1));
        Set<String> oaTaskIds = relationList.stream().map(DtoOATaskRelation::getTaskId).collect(Collectors.toSet());
        //获取审批任务数据
        List<DtoOATask> oaTaskList = oaTaskService.findAll(oaTaskIds);
        Map<String, DtoOATask> oaTaskMap = oaTaskList.stream().collect(Collectors.toMap(DtoOATask::getId, p -> p));
        for (DtoOAConsumablePickListsDetail detail : pickListsDetails) {
            DtoOATaskRelation oaRelation = relationMap.get(detail.getId());
            if (oaRelation != null) {
                DtoOATask oaTask = oaTaskMap.get(oaRelation.getTaskId());
                if (oaTask != null) {
                    detail.setOaTaskStatus(oaTask.getDataStatus());
                }
            }
        }
    }
}