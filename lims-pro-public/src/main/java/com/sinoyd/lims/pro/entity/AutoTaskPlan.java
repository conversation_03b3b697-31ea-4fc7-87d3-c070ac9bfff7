package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;

/**
 * AutoTaskPlan实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/07
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "AutoTaskPlan")
@Data
@EntityListeners(AuditingEntityListener.class)
public class AutoTaskPlan implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public AutoTaskPlan() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 任务名称
     */
    @Column(length = 50)
    @ApiModelProperty("任务名称")
    //@NotBlank(message = "计划名称{validation.message.blank}")
    @Length(message = "计划名称{validation.message.length}", max = 50)
    private String taskName;

    /**
     * 任务编码
     */
    @Column(length = 50)
    @ApiModelProperty("任务编码")
    //@NotBlank(message = "计划编码{validation.message.blank}")
    @Length(message = "计划编码{validation.message.length}", max = 50)
    private String taskCode;

    /**
     * 项目类型id
     */
    @Column(length = 50)
    @ApiModelProperty("项目类型id")
    private String projectTypeId;

    /**
     * 执行周期
     */
    @Column(length = 50)
    @ApiModelProperty("执行周期")
    private Integer dealCycle;

    /**
     * 执行数量
     */
    @Column(length = 50)
    @ApiModelProperty("执行数量")
    private Integer dealNum;

    /**
     * 每月执行日期
     */
    @Column(length = 50)
    @ApiModelProperty("每月执行日期")
    @Length(message = "每月执行日期{validation.message.length}", max = 50)
    private String dealDate;

    /**
     * 项目编号 自动生成 手动生成
     */
    @Column(length = 50)
    @ApiModelProperty("项目编号 自动生成 手动生成")
    private Integer projectCodeRule;

    /**
     * 登记人
     */
    @Column(length = 50)
    @ApiModelProperty("登记人")
    private String inputPersonId;

    /**
     * 负责人
     */
    @Column(length = 50)
    @ApiModelProperty("负责人")
    private String leaderId;

    /**
     * 是否着重关注
     */
    @Column(length = 50)
    @ApiModelProperty("是否着重关注")
    private Boolean isStress;

    /**
     * 项目等级
     */
    @Column(length = 50)
    @ApiModelProperty("项目等级")
    private Integer grade;

    /**
     * 应用月份(","隔开)
     */
    @Column(length = 50)
    @ApiModelProperty("应用月份(','隔开)")
    @Length(message = "应用月份(','隔开){validation.message.length}", max = 100)
    private String month;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}
