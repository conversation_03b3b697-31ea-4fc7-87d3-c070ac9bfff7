package com.sinoyd.lims.lim.service;

import com.sinoyd.base.dto.rcc.DtoEvaluationAnalyzeItem;
import com.sinoyd.base.dto.rcc.DtoEvaluationCriteria;
import com.sinoyd.base.dto.rcc.DtoEvaluationLevel;
import com.sinoyd.base.dto.rcc.DtoEvaluationValue;
import com.sinoyd.base.dto.customer.DtoImportEvaluationCriteria;

import java.util.List;

/**
 * 评价标准导入接口
 *
 * <AUTHOR>
 * @version V1.0.0 2022/9/22
 * @since V100R001
 */
public interface ImportEvaluationCriteriaService extends ImportExcelService<DtoImportEvaluationCriteria, DtoEvaluationCriteria>{
    void addData(List<DtoEvaluationLevel> levels, List<DtoEvaluationAnalyzeItem> analyzeItems, List<DtoEvaluationValue> values);

    void getLevelListFullName(List<DtoEvaluationLevel> dbLevels);

}
