-- 审核时间字段添加默认值
ALTER TABLE tb_qa_planinternalaudit
    MODIFY COLUMN auditTime DATETIME DEFAULT '1753-01-01 00:00:00' comment '审核时间';
-- 新增字段
ALTER TABLE tb_qa_planinternalaudit
    ADD COLUMN auditLeader varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' comment '评审组长';
ALTER TABLE tb_qa_planinternalaudit
    ADD COLUMN auditTimeStr varchar(2000) NOT NULL DEFAULT '' comment '审核日期';
ALTER TABLE tb_qa_planinternalaudit
    ADD COLUMN auditLocation varchar(2000) NOT NULL DEFAULT '' comment '审核地点';
ALTER TABLE tb_qa_planinternalaudit
    ADD COLUMN workPlan varchar(2000) NOT NULL DEFAULT '' comment '工作计划';

-- 仪器多检测器新增字段
ALTER TABLE tb_base_instrument
    ADD COLUMN isManyDetector bit(1) NOT NULL DEFAULT b'0' comment '是否多检测器';
-- 检定校准关联多检测器
ALTER TABLE TB_LIM_InstrumentCheckRecord
    ADD COLUMN instrument2DetectorId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' comment '仪器关联多检测器id';
-- 年度计划管理多检测器
ALTER TABLE TB_QA_YearlyInstrumentConfirmPlan
    ADD COLUMN instrument2DetectorId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' comment '仪器关联多检测器id';
ALTER TABLE TB_QA_YearlyInstrumentConfirmPlan
    ADD COLUMN detectorName varchar(255) NOT NULL default '' COMMENT '检测器名称';

-- 仪器管理多检测器表
CREATE TABLE TB_BASE_Instrument2Detector
(
    id            varchar(50)    NOT NULL COMMENT 'id',
    instrumentId  varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '仪器Id',
    detectorName  varchar(255)   NOT NULL default '' COMMENT '检测器名称',
    originCyc     decimal(18, 1) NOT NULL DEFAULT '12.0' COMMENT '溯源周期(月)',
    originType    int(11) NOT NULL DEFAULT '-1' COMMENT '溯源方式(枚举：EnumOriginType：1检定、2校准、3自校)',
    originUnit    varchar(100)            DEFAULT '' COMMENT '溯源单位',
    originDate    datetime       NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '最近日期（溯源）',
    originEndDate datetime       NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '过期日期（溯源）',
    originResult  int(11) NOT NULL DEFAULT '1' COMMENT '溯源结果(枚举：EnumOriginResult：1合格、0不合格)',
    originRemark  varchar(1000)           DEFAULT '' COMMENT '溯源备注',
    isDeleted     bit(1)         NOT NULL DEFAULT b'0' COMMENT '是否删除',
    orgId         varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator       varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate    datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    domainId      varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier      varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate    datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='仪器关联多检测器溯源';
