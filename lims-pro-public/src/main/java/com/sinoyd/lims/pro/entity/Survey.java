package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;


/**
 * Survey实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="Survey")
 @Data
 public  class Survey implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  Survey() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 项目ID
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("项目ID")
	private String projectId;
    
    /**
    * 踏勘时间
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("踏勘时间")
	private Date surveyTime;
    
    /**
    * 踏勘人id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("踏勘人id")
	private String surveyUserId;
    
    /**
    * 现场踏勘情况（详细文本信息）
    */
    @Column(length=1000)
    @ApiModelProperty("现场踏勘情况（详细文本信息）")
    @Length(message = "现场踏勘情况（详细文本信息）{validation.message.length}", max = 1000)
	private String surveyNote;
    
    /**
    * 企业存在问题(原常量数值+是否合格 数值存放)
    */
    @Column(length=1000)
    @ApiModelProperty("企业存在问题(原常量数值+是否合格 数值存放)")
    @Length(message = "企业存在问题(原常量数值+是否合格 数值存放){validation.message.length}", max = 1000)
	private String problem;
    
    /**
    * 企业签字人
    */
    @Column(length=50)
    @ApiModelProperty("企业签字人")
    @Length(message = "企业签字人{validation.message.length}", max = 50)
	private String entDelegate;
    
    /**
    * 签字确认时间
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("签字确认时间")
	private Date confirmTime;
    
    /**
    * 备注
    */
    @Column(length=1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
	private String remark;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
 }