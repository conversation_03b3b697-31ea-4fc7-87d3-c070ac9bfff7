package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoOAInstrumentRepairApply;
import com.sinoyd.lims.lim.repository.lims.OAInstrumentRepairApplyRepository;
import com.sinoyd.lims.lim.service.OAInstrumentRepairApplyService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.stereotype.Service;


/**
 * OAInstrumentRepairApply操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/4/3
 * @since V100R001
 */
 @Service
public class OAInstrumentRepairApplyServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoOAInstrumentRepairApply,String,OAInstrumentRepairApplyRepository> implements OAInstrumentRepairApplyService {

    @Override
    public void findByPage(PageBean<DtoOAInstrumentRepairApply> pb, BaseCriteria oAInstrumentRepairApplyCriteria) {
        pb.setEntityName("DtoOAInstrumentRepairApply a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, oAInstrumentRepairApplyCriteria);
    }
}