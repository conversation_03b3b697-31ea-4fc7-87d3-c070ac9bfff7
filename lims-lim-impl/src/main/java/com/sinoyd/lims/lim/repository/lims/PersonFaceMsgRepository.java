package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoPersonFaceMsg;


/**
 * PersonFaceMsg数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2022/9/26
 * @since V100R001
 */
public interface PersonFaceMsgRepository extends IBaseJpaRepository<DtoPersonFaceMsg, String> {

    /**
     * 通过人员id获取face信息
     * @param personId 人员id
     * @return face信息
     */
    DtoPersonFaceMsg findByPersonId(String personId);
}