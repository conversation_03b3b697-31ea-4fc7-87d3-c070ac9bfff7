-- ocr对象
CREATE TABLE tb_lim_ocrConfig (
                                    id varchar(50) NOT NULL COMMENT 'id',
                                    configName varchar(255) NOT NULL COMMENT '对象名称',
                                    isDeleted bit ( 1 ) NOT NULL DEFAULT b'0' COMMENT '假删字段',
                                    orgId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
                                    domainId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
                                    creator VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
                                    createDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    modifier VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
                                    modifyDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                    PRIMARY KEY (id)
);
-- ocr对象参数
CREATE TABLE tb_lim_ocrConfigParam (
                                         id varchar(50) NOT NULL COMMENT 'id',
                                         configId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT 'OCR对象标识',
                                         paramName varchar(255) NOT NULL  COMMENT '参数名称',
                                         paramNameAlias varchar(255) NOT NULL  COMMENT '参数别名',
                                         paramType int(10) NOT NULL DEFAULT '-1' COMMENT '类型（枚举EnumOcrConfigParamType：样品参数1、现场数据2、现场公式3）',
                                         dimension varchar(50) DEFAULT '' COMMENT '量纲',
                                         regexBegin varchar(50) DEFAULT '' COMMENT '正则开始',
                                         regexEnd varchar(50) DEFAULT '' COMMENT '正则结束',
                                         analyzeItemId varchar(4000) COMMENT '分析项目标识',
                                         PRIMARY KEY (id)
);
-- ocr对象参数识别数据
CREATE TABLE tb_lim_ocrConfigParamData (
                                             id varchar(50) NOT NULL COMMENT 'id',
                                             configId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT 'OCR对象标识',
                                             configParamId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT 'OCR对象参数标识',
                                             sampleId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '样品标识',
                                             collectNo varchar(255) NOT NULL  COMMENT '采集编号',
                                             saveValue varchar(255) NOT NULL  COMMENT '参数值',
                                             filePath varchar(500) NOT NULL DEFAULT '' COMMENT '上传文件保存路径',
                                             orgId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
                                             domainId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
                                             creator VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
                                             createDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                             modifier VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
                                             modifyDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                             PRIMARY KEY (id)
);
-- ocr对象参数识别数据历史表
CREATE TABLE tb_lim_ocrConfigParamDataHistory (
                                           id varchar(50) NOT NULL COMMENT 'id',
                                           configId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT 'OCR对象标识',
                                           configParamId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT 'OCR对象参数标识',
                                           sampleId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '样品标识',
                                           collectNo varchar(255) NOT NULL  COMMENT '采集编号',
                                           saveValue varchar(255) NOT NULL  COMMENT '参数值',
                                           filePath varchar(500) NOT NULL DEFAULT '' COMMENT '上传文件保存路径',
                                           orgId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
                                           domainId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
                                           creator VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
                                           createDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           modifier VARCHAR ( 50 ) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
                                           modifyDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                           PRIMARY KEY (id)
);