package com.sinoyd.lims.od.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.od.dto.DtoEnvGasResult;
import com.sinoyd.lims.od.dto.DtoLabSeq;

/**
 * 环境空气结果操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
public interface EnvGasResultService extends IBaseJpaService<DtoEnvGasResult, String> {

    /**
     * 环境空气计算M值
     *
     * @param labSeq 实验次序
     */
    void calculateMValue(DtoLabSeq labSeq);
}
