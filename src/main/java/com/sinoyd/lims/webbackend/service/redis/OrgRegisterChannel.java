package com.sinoyd.lims.webbackend.service.redis;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * 企业的注册通道
 */
@Component
@Service
public class OrgRegisterChannel {


    /**
     * 进行企业注册
     *
     * @param message 框架注册完之后，进行通知的消息
     */
    @Async
    public void register(String message) {
        //取出当前的orgId

        //调用接口
    }
}
