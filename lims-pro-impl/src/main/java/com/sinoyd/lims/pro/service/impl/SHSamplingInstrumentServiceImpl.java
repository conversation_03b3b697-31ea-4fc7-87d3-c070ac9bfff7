package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.service.InstrumentService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.pro.dto.DtoSHSamplingInstrumentNew;
import com.sinoyd.lims.pro.repository.SHSamplingInstrumentNewRepository;
import com.sinoyd.lims.pro.service.SHSamplingInstrumentNewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * SHSamplingPerson操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2022/11/28
 * @since V100R001
 */
@Service
public class SHSamplingInstrumentServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoSHSamplingInstrumentNew,String, SHSamplingInstrumentNewRepository> implements SHSamplingInstrumentNewService {

    private InstrumentService instrumentService;

    @Override
    public void findByPage(PageBean<DtoSHSamplingInstrumentNew> page, BaseCriteria criteria) {
        page.setSelect("select a");
        page.setEntityName("DtoSHSamplingInstrumentNew a");
        super.findByPage(page, criteria);
        fillingTransientFields(page.getData());
    }

    @Override
    public List<DtoSHSamplingInstrumentNew> save(Collection<DtoSHSamplingInstrumentNew> entities) {
        List<DtoSHSamplingInstrumentNew> instrumentList = (List)entities;
        List<String> existInstrumentIds = repository.findByTaskId(instrumentList.get(0).getTaskId()).stream().map(DtoSHSamplingInstrumentNew::getInstrumentId).collect(Collectors.toList());
        List<DtoSHSamplingInstrumentNew> addList = new ArrayList<>();
        entities.forEach(e->{
            if(!existInstrumentIds.contains(e.getInstrumentId())) {
                addList.add(e);
            }
        });
        List<DtoSHSamplingInstrumentNew> result = new ArrayList<>();
        if(StringUtil.isNotEmpty(addList)) {
            result = super.save(addList);
        }
        return result;
    }

    /**
     * 填充冗余字段
     *
     * @param data 数据
     */
    private void fillingTransientFields(List<DtoSHSamplingInstrumentNew> data) {
        List<DtoInstrument> instruments = instrumentService.findAll();
        data.forEach(d->{
            Optional<DtoInstrument> instrument = instruments.stream().filter(i->i.getId().equals(d.getInstrumentId())).findFirst();
            instrument.ifPresent(p->{
                d.setCode(p.getSerialNo());
                d.setInstrumentName(p.getInstrumentName());
                d.setModel(p.getModel());
                d.setRegulateId(p.getRegulateId());
                d.setRegulateName(p.getRegulateName());
            });
        });
    }


    @Autowired
    @Lazy
    public void setInstrumentService(InstrumentService instrumentService) {
        this.instrumentService = instrumentService;
    }
}
