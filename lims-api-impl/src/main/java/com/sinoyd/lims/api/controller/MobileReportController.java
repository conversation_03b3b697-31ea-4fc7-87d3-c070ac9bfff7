package com.sinoyd.lims.api.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.api.service.MobileReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 报表生成移动端内容
 *
 * <AUTHOR>
 * @version V1.0.0 2023/05/08
 * @since V100R001
 */
@Api(tags = "示例: mobileReport")
@RestController
@RequestMapping("api/mobileReport/generate")
public class MobileReportController extends ExceptionHandlerController<MobileReportService> {

    @PostMapping("/{code}")
    public RestResponse<String> generate(@RequestBody Map<String, Object> map,
                                         @PathVariable(name = "code") String code,
                                         HttpServletRequest request) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setData(service.generate(code, map, request));
        return restResponse;
    }

    @PostMapping("/export/{code}")
    public RestResponse<String> generateFile(@RequestBody Map<String, Object> map,
                                             @PathVariable(name = "code") String code,
                                             HttpServletRequest request,
                                             HttpServletResponse response) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setData(service.generateFile(code, map, request, response));
        return restResponse;
    }

    @ApiOperation(value = "获取采样单报表模板配置", notes = "获取采样单报表模板配置")
    @GetMapping("/getSamplingReportConfigs")
    public RestResponse<List<Map<String, String>>> getSamplingReportConfigs(@RequestParam String receiveId,
                                                                            @RequestParam String module,
                                                                            @RequestParam String location) {
        RestResponse<List<Map<String, String>>> restResp = new RestResponse<>();
        restResp.setData(service.getSamplingReport(receiveId, module, location));
        return restResp;
    }

    /**
     * 获取报表名称
     *
     * @param map      参数信息
     * @param configId 配置id
     * @return 报表名称
     */
    @ApiOperation(value = "获取报表名称", notes = "获取报表名称")
    @PostMapping("/documentFileName/{configId}")
    public RestResponse<Map<String, String>> documentFileName(@RequestBody Map<String, Object> map,
                                                              @PathVariable(name = "configId") String configId) {
        RestResponse<Map<String, String>> restResp = new RestResponse<>();
        restResp.setData(service.documentFileName(map, configId));
        return restResp;
    }

}
