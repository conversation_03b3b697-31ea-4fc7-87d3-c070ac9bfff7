package com.sinoyd.lims.pro.dto;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 审批任务附件传输实体
 * <AUTHOR>
 * @version V1.0.0 2019-03-26
 * @since V100R001
 */
@Data
public class DtoOATaskAttach implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 附件ID */
    private String id;
    
    /** 附件名称 */
    private String name;
    
    /** 附件类型 */
    private String type;
    
    /** 上传人 */
    private String userId;
    
    /** 上传时间 */
    private Date time;
}