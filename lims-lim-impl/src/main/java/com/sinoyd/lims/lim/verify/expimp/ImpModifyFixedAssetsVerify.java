package com.sinoyd.lims.lim.verify.expimp;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.lims.lim.dto.customer.expimp.DtoExpImpFixedProperty;
import com.sinoyd.lims.lim.dto.lims.DtoFixedProperty;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.utils.ImportUtils;
import lombok.Data;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 固定资产导入更新数据校验器
 *
 * <AUTHOR>
 * @version V1.0.0 2023/12/12
 * @since V100R001
 */
@Component
@Data
public class ImpModifyFixedAssetsVerify implements IExcelVerifyHandler<DtoExpImpFixedProperty> {

    /**
     * 临时数据
     */
    private ThreadLocal<List<DtoExpImpFixedProperty>> fixedAssetsTl = new ThreadLocal<>();
    private ThreadLocal<List<DtoEnterprise>> enterpriseTl = new ThreadLocal<>();
    private ThreadLocal<List<DtoCode>> codeTl = new ThreadLocal<>();
    private ThreadLocal<List<DtoDepartment>> deptTl = new ThreadLocal<>();
    private ThreadLocal<List<DtoPerson>> personTl = new ThreadLocal<>();
    private ThreadLocal<List<DtoFixedProperty>> fixedAllTl = new ThreadLocal<>();

    /**
     * 工具类
     */
    private final ImportUtils importUtils = new ImportUtils();


    @Override
    public ExcelVerifyHandlerResult verifyHandler(DtoExpImpFixedProperty dto) {
        //region 导入数据处理
        try {
            //跳过空行
            if (importUtils.checkObjectIsNull(dto)) {
                return new ExcelVerifyHandlerResult(true);
            }
            //前后去空格
            importUtils.strToTrim(dto);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }
        //endregion

        // 默认值
        defaultValue(dto);
        //region 参数
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        //校验错误总数据
        StringBuilder failStr = new StringBuilder("第" + dto.getRowNum() + "行数据校验有误");

        // 获取供应商、科室、类型，
        List<DtoEnterprise> dtoEnterprises = enterpriseTl.get();
        List<DtoCode> codeList = codeTl.get();
        List<DtoDepartment> dtoDepartments = deptTl.get();
        List<DtoPerson> personList = personTl.get();
        // 系统中固定资产
        List<DtoFixedProperty> propertyList = fixedAllTl.get();
        //获取临时仪器数据集
        List<DtoExpImpFixedProperty> importFixedAssets = fixedAssetsTl.get();
        if (StringUtil.isEmpty(importFixedAssets)) {
            importFixedAssets = new ArrayList<>();
        }
        // 必填校验
        importUtils.checkIsNull(result, dto.getAssetsName(), "固定资产名称", failStr);
        importUtils.checkIsNull(result, dto.getAssetsNo(), "资产编号", failStr);
        importUtils.checkIsNull(result, dto.getAssetsType(), "资产类型", failStr);
        importUtils.checkIsNull(result, dto.getDeptId(), "所属科室", failStr);
        importUtils.checkIsNull(result, dto.getStatus(), "资产状态", failStr);
        importUtils.checkIsNull(result, dto.getManager(), "管理人员", failStr);
        importUtils.checkNumTwo(result,dto.getPurchasePrice(),"采购价格",failStr);
        //校验部门是否存在
        isExistDept(result, dtoDepartments, dto, failStr);
        //校验仪器类型是否存在
        isExistAssetsType(result, codeList, dto, failStr);
        // 校验供应商是否存在
        isExistEnterprises(result, dtoEnterprises, dto, failStr);
        // 校验管理人员是否存在
        isExistManager(result, personList, dto, failStr);
        // 校验重复数据
        isRepeatData(result, dto, importFixedAssets, propertyList, failStr);
        // 校验采购价格
        // 采购时间格式校验
        importUtils.checkDateTwo(result, dto.getPurchaseDate(), ",采购时间", failStr);

        String failString = failStr.toString().replaceFirst("；", ":");
        result.setMsg(failString);
        importFixedAssets.add(dto);
        fixedAssetsTl.set(importFixedAssets);

        return result;
    }


    /**
     * 默认值
     *
     * @param fixedAssets 导入数据
     */
    private void defaultValue(DtoExpImpFixedProperty fixedAssets) {
        fixedAssets.setStatus(StringUtil.isEmpty(fixedAssets.getStatus()) ? "1" : fixedAssets.getStatus());
        if (EnumLIM.EnumAssetsStatus.使用中.name().equals(fixedAssets.getStatus())) {
            fixedAssets.setStatus("1");
        }
        if (EnumLIM.EnumAssetsStatus.已报废.name().equals(fixedAssets.getStatus())) {
            fixedAssets.setStatus("2");
        }

    }

    /**
     * 校验采管理人员
     *
     * @param result      校验结果
     * @param personList  所有人员
     * @param fixedAssets 当属固定资产数据
     * @param failStr     校验错误信息
     */
    private void isExistManager(ExcelVerifyHandlerResult result, List<DtoPerson> personList, DtoExpImpFixedProperty fixedAssets, StringBuilder failStr) {
        if (StringUtil.isNotEmpty(fixedAssets.getManager())) {
            List<DtoPerson> dtoPeoples = personList.stream().filter(p -> p.getCName().equals(fixedAssets.getManager())).collect(Collectors.toList());
            if (StringUtil.isEmpty(dtoPeoples)) {
                result.setSuccess(false);
                failStr.append("；管理人员不存在");
            }
        }
    }

    /**
     * 校验采购价格格式
     *
     * @param result      校验结果
     * @param fixedAssets 当属固定资产数据
     * @param failStr     校验错误信息
     */
    private void existPurchasePrice(ExcelVerifyHandlerResult result, DtoExpImpFixedProperty fixedAssets, StringBuilder failStr) {
        if (StringUtil.isNotEmpty(fixedAssets.getPurchasePrice())) {
            if (!NumberUtils.isCreatable(fixedAssets.getPurchasePrice())) {
                result.setSuccess(false);
                failStr.append("；采购价格格式不正确");
            }
        }
    }

    /**
     * 校验供应商导入
     *
     * @param result         校验结果
     * @param dtoEnterprises 供应商信息
     * @param fixedAssets    当属固定资产数据
     * @param failStr        校验错误信息
     */
    private void isExistEnterprises(ExcelVerifyHandlerResult result, List<DtoEnterprise> dtoEnterprises, DtoExpImpFixedProperty fixedAssets, StringBuilder failStr) {

        if (StringUtil.isNotEmpty(fixedAssets.getSupplier())) {
            List<DtoEnterprise> enterprises = dtoEnterprises.stream().filter(p -> fixedAssets.getSupplier().equals(p.getName())).collect(Collectors.toList());
            if (StringUtil.isEmpty(enterprises)) {
                result.setSuccess(false);
                failStr.append("；供应商不存在");
            }
        }
    }

    /**
     * 校验资产类型导入
     *
     * @param result      校验结果
     * @param codeList    所有类型
     * @param fixedAssets 当属固定资产数据
     * @param failStr     校验错误信息
     */
    private void isExistAssetsType(ExcelVerifyHandlerResult result, List<DtoCode> codeList, DtoExpImpFixedProperty fixedAssets, StringBuilder failStr) {

        if (StringUtil.isNotEmpty(fixedAssets.getAssetsType())) {
            List<DtoCode> isExistDict = codeList.stream().filter(p -> fixedAssets.getAssetsType().equals(p.getDictName())).collect(Collectors.toList());
            if (StringUtil.isEmpty(isExistDict)) {
                result.setSuccess(false);
                failStr.append("；资产类型不存在");
            }
        }
    }


    /**
     * 判断科室导入
     *
     * @param result      校验结果
     * @param dbDept      所有科室
     * @param fixedAssets 当属固定资产数据
     * @param failStr     校验错误信息
     */
    private void isExistDept(ExcelVerifyHandlerResult result, List<DtoDepartment> dbDept, DtoExpImpFixedProperty fixedAssets, StringBuilder failStr) {

        //region 判断部门是否存在
        if (StringUtil.isNotEmpty(fixedAssets.getDeptId())) {
            List<DtoDepartment> isExistDept = dbDept.stream().filter(p -> fixedAssets.getDeptId().equals(p.getDeptName())).collect(Collectors.toList());
            if (StringUtil.isEmpty(isExistDept)) {
                result.setSuccess(false);
                failStr.append("；科室不存在");
            }
            //endregion
        }
    }

    /**
     * 判断重复数据
     *
     * @param result            校验结果
     * @param fixedAssets       本次实体
     * @param importFixedAssets 导入的固定资产
     * @param propertyList      数据库中的固定资产
     * @param failStr           检验错误信息
     */
    private void isRepeatData(ExcelVerifyHandlerResult result, DtoExpImpFixedProperty fixedAssets, List<DtoExpImpFixedProperty> importFixedAssets, List<DtoFixedProperty> propertyList, StringBuilder failStr) {
        //region 判断资产编号是否重复
        if (StringUtil.isNotEmpty(fixedAssets.getAssetsNo())) {
            List<Integer> repeatNum = importFixedAssets.stream().filter(p -> fixedAssets.getAssetsNo().equals(p.getAssetsNo())).map(DtoExpImpFixedProperty::getRowNum).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(repeatNum)) {
                result.setSuccess(false);
                failStr.append("；资产编号与第").append(repeatNum).append("行重复");
            }
        }
        // id 不为空时校验资产编号与系统中数据是否重复
        if (StringUtil.isEmpty(fixedAssets.getId()) && StringUtil.isNotEmpty(fixedAssets.getAssetsNo())) {
            List<DtoFixedProperty> collect = propertyList.stream().filter(p -> fixedAssets.getAssetsNo().equals(p.getAssetsNo())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(collect)) {
                result.setSuccess(false);
                failStr.append("；资产编号已存在");
            }
        }
        //endregion
    }
}
