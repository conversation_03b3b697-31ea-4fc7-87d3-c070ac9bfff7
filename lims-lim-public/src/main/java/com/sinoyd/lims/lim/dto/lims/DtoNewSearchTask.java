package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.NewSearchTask;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 查新任务
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_NewSearchTask")
@Data
@DynamicInsert
public class DtoNewSearchTask extends NewSearchTask {

    /**
     * 执行人名称
     */
    @Transient
    private String executorName;

    /**
     * 查新结果集合
     */
    @Transient
    private List<DtoNewSearchResult> newSearchResultList;
}
