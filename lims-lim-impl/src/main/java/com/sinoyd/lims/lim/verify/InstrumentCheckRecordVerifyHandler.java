package com.sinoyd.lims.lim.verify;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.customer.DtoImportInstrumentCheckRecord;
import com.sinoyd.lims.lim.utils.ImportUtils;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 仪器检定校准导入数据校验
 *
 * <AUTHOR>
 * @version V1.0.0 2022/9/13
 * @since V100R001
 */
@Component
@Data
public class InstrumentCheckRecordVerifyHandler implements IExcelVerifyHandler<DtoImportInstrumentCheckRecord> {

    private ThreadLocal<List<DtoPerson>> dbPersonTl = new ThreadLocal<>();

    private ThreadLocal<List<DtoInstrument>> dbInstrumentTl = new ThreadLocal<>();

    private ThreadLocal<String> instrumentIdTl = new ThreadLocal<>();

    /**
     * 工具类
     */
    private final ImportUtils importUtils = new ImportUtils();

    @Override
    public ExcelVerifyHandlerResult verifyHandler(DtoImportInstrumentCheckRecord dto) {
        //region 导入数据处理
        try {
            //跳过空行
            if (importUtils.checkObjectIsNull(dto)) {
                return new ExcelVerifyHandlerResult(true);
            }
            //前后去空格
            importUtils.strToTrim(dto);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }
        //endregion
        //region 参数
        List<DtoPerson> personList = dbPersonTl.get();

        List<DtoInstrument> instrumentList = dbInstrumentTl.get();

        String instrumentId = instrumentIdTl.get();
        //校验结果
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        //校验错误信息
        StringBuilder failStr = new StringBuilder("第" + dto.getRowNum() + "行数据校验错误");
        //endregion

        //region 判断数据是否存在
        //判断仪器是否存在
        isExistInstrument(instrumentList, dto, failStr, result);
        //endregion

        //region 必填项判断
        String originCycStr = StringUtil.isNotNull(dto.getOriginCyc()) ? dto.getOriginCyc().toString() : "";
        if (StringUtil.isEmpty(instrumentId)) {
            importUtils.checkIsNull(result, dto.getInstrumentCode(), "本站编号", failStr);
        }
        importUtils.checkIsNull(result, originCycStr, "溯源周期", failStr);
        importUtils.checkIsNull(result, dto.getOriginTypeName(), "溯源方式", failStr);
        importUtils.checkIsNull(result, dto.getCheckDeptName(), "检定/校准单位名称", failStr);
        importUtils.checkIsNull(result, dto.getCheckTime(), "检定/校准日期", failStr);
        importUtils.checkIsNull(result, dto.getCheckEndDate(), "检定/校准有效期", failStr);
        importUtils.checkIsNull(result, dto.getUsabilityStr(), "适用性", failStr);
        importUtils.checkIsNull(result, dto.getCheckResultStr(), "检定结果", failStr);
        //endregion

        //region 格式判断
        importUtils.checkDateTwo(result, dto.getCheckTime(), "检定/校准日期", failStr);
        importUtils.checkDateTwo(result, dto.getCheckEndDate(), "有效日期", failStr);
        //endregion

        this.replaceMsg(dto);
        String failString = failStr.toString().replaceFirst("；", ":");
        result.setMsg(failString);

        return result;
    }

    /**
     * 判断仪器是否存在
     *
     * @param instrumentList 仪器集合
     * @param checkRecord    导入数据
     * @param failStr        错误字符串
     * @param result         数据校验结果
     */
    private void isExistInstrument(List<DtoInstrument> instrumentList, DtoImportInstrumentCheckRecord checkRecord, StringBuilder failStr, ExcelVerifyHandlerResult result) {
        String instrumentStr = checkRecord.getInstrumentCode();
        //存放不存在的仪器名称
        List<String> notExistInsName = new ArrayList<>();
        Map<String, List<DtoInstrument>> instrumentMap = instrumentList.stream().collect(Collectors.groupingBy(DtoInstrument::getInstrumentsCode));
        if (StringUtil.isNotEmpty(instrumentStr)) {
            //分割仪器编号
            List<String> instrumentCodes = importUtils.getSplitList(instrumentStr);
            //判断仪器编号是否存在
            for (String instrumentCode : instrumentCodes) {
                List<DtoInstrument> instrumentByCode = instrumentMap.get(instrumentCode);
                if (StringUtil.isEmpty(instrumentByCode)) {
                    notExistInsName.add(instrumentCode);
                }
            }
        }
        if (StringUtil.isNotEmpty(notExistInsName)) {
            failStr.append("；").append(notExistInsName).append("仪器不存在,");
            result.setSuccess(false);
        }

    }

    /**
     * 处理数据
     *
     * @param checkRecord 数据
     */
    private void replaceMsg(DtoImportInstrumentCheckRecord checkRecord) {
        //处理溯源方式
        if (StringUtil.isNotEmpty(checkRecord.getOriginTypeName())) {
            checkRecord.setOriginType(EnumBase.EnumOriginType.valueOf(checkRecord.getOriginTypeName()).getValue());
        }
        //处理适用性
        if (StringUtil.isNotEmpty(checkRecord.getUsabilityStr())) {
            checkRecord.setUsability("是".equals(checkRecord.getUsabilityStr()));
        }
        //处理检定结果
        if (StringUtil.isNotEmpty(checkRecord.getCheckResultStr())) {
            checkRecord.setCheckResult("合格".equals(checkRecord.getCheckResultStr()) ? 1 : 0);
        }
    }
}
