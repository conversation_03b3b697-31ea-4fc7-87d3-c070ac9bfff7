package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.InstrumentGatherData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Map;


/**
 * InstrumentGatherData实体
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/17
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_InstrumentGatherData")
@Data
@DynamicInsert
public class DtoInstrumentGatherData extends InstrumentGatherData {

    /**
     * 采样编号
     */
    @Transient
    private String content;

    /**
     * 采样编号
     */
    @Transient
    private String sampleCode;


}