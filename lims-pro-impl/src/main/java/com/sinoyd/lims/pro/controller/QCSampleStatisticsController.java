package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.pro.criteria.QCSampleStatisticsCriteria;
import com.sinoyd.lims.pro.criteria.QcSampleCriteria;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.customer.DtoEvaluateRow;
import com.sinoyd.lims.pro.dto.customer.DtoQCSample;
import com.sinoyd.lims.pro.dto.customer.DtoQCSampleDetail;
import com.sinoyd.lims.pro.dto.customer.DtoQCSampleCensus;
import com.sinoyd.lims.pro.service.QCSampleStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * qcCompare 质控样的数据统计
 * <AUTHOR>
 * @version V1.0.0 2020/02/12
 * @since V100R001
 */
@Api(tags = "示例: 质控样的数据统计服务")
@RestController
@RequestMapping("api/pro/qcSampleStatistics")
public class QCSampleStatisticsController extends ExceptionHandlerController<QCSampleStatisticsService> {

    @ApiOperation(value = "质控样的数据统计", notes = "质控样的数据统计")
    @GetMapping
    public RestResponse<List<DtoQCSample>> findByPage(QCSampleStatisticsCriteria qcCompareStatisticsCriteria) {
        RestResponse<List<DtoQCSample>> restResp = new RestResponse<>();
        PageBean<DtoQCSample> pageBean = super.getPageBean();
        service.findByPage(pageBean, qcCompareStatisticsCriteria);
        restResp.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(pageBean.getData());
        restResp.setCount(pageBean.getRowsCount());
        return restResp;
    }

    @ApiOperation(value = "质控样的数据统计", notes = "质控样的数据统计")
    @GetMapping("/qcSamples")
    public RestResponse<List<DtoQCSampleCensus>> findQcSamplesByPage(QcSampleCriteria qcSampleCriteria) {
        RestResponse<List<DtoQCSampleCensus>> restResp = new RestResponse<>();
        PageBean<DtoQCSampleCensus> pageBean = super.getPageBean();
        service.findQcSamples(pageBean, qcSampleCriteria);
        restResp.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(pageBean.getData());
        restResp.setCount(pageBean.getRowsCount());
        return restResp;
    }

    @ApiOperation(value = "质控评价的数据详情", notes = "质控评价的数据详情")
    @GetMapping("/qcDetail")
    public RestResponse<List<List<DtoEvaluateRow>>> findDetails(@RequestParam("sampleId") String sampleId){
        RestResponse<List<List<DtoEvaluateRow>>> restResp = new RestResponse<>();
        List<List<DtoEvaluateRow>> details = service.findDetails(sampleId);
        restResp.setRestStatus(StringUtil.isEmpty(details) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(details);
        restResp.setCount(details.size());
        return restResp;
    }

    @ApiOperation(value = "相应的样品数据", notes = "相应的样品数据")
    @PostMapping
    public RestResponse<List<DtoSample>> findSamples(@RequestBody List<String> ids) {
        RestResponse<List<DtoSample>> restResp = new RestResponse<>();
        List<DtoSample> samples = service.findSamples(ids);
        restResp.setRestStatus(StringUtil.isEmpty(samples) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(samples);
        return restResp;
    }


    @ApiOperation(value = "质控样的数据详情", notes = "质控样的数据详情")
    @GetMapping("/detail")
    public RestResponse<List<DtoQCSampleDetail>> findDetails(@RequestParam("sampleId") String sampleId,
                                                             @RequestParam("associateSampleId") String associateSampleId) {
        RestResponse<List<DtoQCSampleDetail>> restResp = new RestResponse<>();
        List<DtoQCSampleDetail> details = service.findDetails(sampleId, associateSampleId);
        restResp.setRestStatus(StringUtil.isEmpty(details) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(details);
        restResp.setCount(details.size());
        return restResp;
    }
}
