-- 添加报告是否展示全程序空白样参数设置
INSERT INTO T_SYS_Config(id, configName, configKey, configValue, enabled, sortNum, note,
                                         createDate, createUserGuid, createUserName, updateDate, updateUserGuid,
                                         updateUserName, orgGuid, deptGuid, deleted)
VALUES ('333d15a4f14c41e9aeb1db71801d6585', '报告是否展示全程序空白样', 'sys_pro_report_showOutBlank', 'true', 1, 0,
        '开关开启时，报告中默认会展示全程序空白样，关闭时不展示', '2025-01-16 14:32:03', '59141356591b48e18e139aa54d9dd351', '超级管理员', NULL, NULL,
        NULL, '5f7bcf90feb545968424b0a872863876', NULL, 0);