package com.sinoyd.lims.lim.service.redis;

import com.jsoniter.JsonIterator;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.util.PinYinUtil;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.TestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 测试项目接收通道
 * <AUTHOR>
 * @version V1.0.0 2019/11/28
 * @since V100R001
 */
@Component
@Service
public class TestChannel {

    private TestService testService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Async
    @Transactional
    public void updateAnalyzeItem(String analyzeItemJson) {

        TypeLiteral<DtoAnalyzeItem> typeLiteral = new TypeLiteral<DtoAnalyzeItem>() {
        };

        if (StringUtils.isNotNullAndEmpty(analyzeItemJson)) {
            DtoAnalyzeItem dtoAnalyzeItem = JsonIterator.deserialize(JsonIterator.deserialize(analyzeItemJson).toString(), typeLiteral);
            if (StringUtil.isNotNull(dtoAnalyzeItem)) {

                CurrentPrincipalUser user = new CurrentPrincipalUser();
                user.setOrgId(dtoAnalyzeItem.getOrgId());
                UsernamePasswordAuthenticationToken token = new UsernamePasswordAuthenticationToken(user, null);
                SecurityContext ctx = SecurityContextHolder.getContext();
                ctx.setAuthentication(token);

                String fullPinYin = PinYinUtil.getFullSpell(dtoAnalyzeItem.getAnalyzeItemName());
                String pinYin = PinYinUtil.getFirstSpell(dtoAnalyzeItem.getAnalyzeItemName());
                List<DtoTest> testList = testService.findByAnalyzeItemId(dtoAnalyzeItem.getId());
                List<String> testIds = new ArrayList<>();
                for (DtoTest dtoTest : testList) {
                    String analyzeItemName = dtoTest.getRedAnalyzeItemName();
                    String statisticalAlias = dtoTest.getItemStatisticalAlias();

                    //不相等说明需要修改
                    if (!analyzeItemName.equals(dtoAnalyzeItem.getAnalyzeItemName())) {
                        testService.updateAnalyzeItemInfo(dtoTest.getId(), dtoAnalyzeItem.getAnalyzeItemName() + '-' + dtoTest.getRedAnalyzeMethodName() + ' ' + ( StringUtil.isNull(dtoTest.getRedCountryStandard()) ? "" : dtoTest.getRedCountryStandard()),
                                dtoAnalyzeItem.getAnalyzeItemName(), fullPinYin, pinYin);
                        testIds.add(dtoTest.getId());
                    }
                    if((statisticalAlias==null&&dtoAnalyzeItem.getStatisticalAlias()!=null)||(statisticalAlias!=null&&!statisticalAlias.equals(dtoAnalyzeItem.getStatisticalAlias()))){
                        testService.updateAnalyzeItemStatisticalAlias(dtoTest.getId(), dtoAnalyzeItem.getStatisticalAlias());
                        testIds.add(dtoTest.getId());
                    }
                }
                String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_Test.getValue());
                // 如果testIds为空进行toArray操作会报错, 进行非空判断
                if (StringUtil.isNotEmpty(testIds)) {
                    redisTemplate.opsForHash().delete(key, testIds.toArray());
                }
            }
        }
    }

    @Autowired
    @Lazy
    public void setTestService(TestService testService) {
        this.testService = testService;
    }
}
