package com.sinoyd.lims.pro.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@Accessors(chain = true)
public class OrderQuotationAnlVo extends BaseOrderCustomerAnalyzeVO {

    /**
     * 签单总额
     */
    private Integer signTotal;

    /**
     * 签单数
     */
    private Integer signNum;

    /**
     * 签单同比上年
     */
    private Integer signCompare;

    /**
     * 订单总额
     */
    private Integer orderTotal;

    /**
     * 订单数
     */
    private Integer orderNum;

    /**
     * 订单同比上年
     */
    private Integer orderCompare;
}
