package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.ReceiveSampleRecordParamTemplate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;


/**
 * DtoReceiveSampleRecordParamTemplate实体
 * <AUTHOR>
 * @version V1.0.0 2023/09/19
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_ReceiveSampleRecordParamTemplate")
 @Data
 @DynamicInsert
 public  class DtoReceiveSampleRecordParamTemplate extends ReceiveSampleRecordParamTemplate {

    /**
     * 模板参数列表
     */
    @Transient
    private List<DtoReceiveSampleRecordParamInfo> paramList;

    /**
     * 创建人名称
     */
    @Transient
    private String createUserName;

    /**
     * 样品类型名称
     */
    @Transient
    private String sampleTypeName;
 }