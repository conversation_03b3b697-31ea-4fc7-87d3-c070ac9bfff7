package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

/**
 * 项目数量统计的结果数据
 */
@Data
public class DtoProjectCountStatistic {


    /**
     * 项目类型id
     */
    private String projectTypeId;

    /**
     * 项目类型名称
     */
    private String projectTypeName;

    /**
     * 项目类型父类
     */
    private String parentName;


    /**
     * 项目数量
     */
    private Long projectCount;

    public DtoProjectCountStatistic(String projectTypeId,
                                    String projectTypeName,
                                    String parentName,
                                    Long projectCount) {
        this.projectTypeId = projectTypeId;
        this.projectTypeName = projectTypeName;
        this.parentName = parentName;
        this.projectCount = projectCount;
    }
}
