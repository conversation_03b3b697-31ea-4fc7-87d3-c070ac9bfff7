package com.sinoyd.lims.pro.criteria;

import java.io.Serializable;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 审批任务流程日志查询条件
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-15
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OATaskHandleLogCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 审批任务ID
     */
    private String taskId;

    @Override
    public String getCondition() {        
        StringBuilder conditionSb = new StringBuilder();
        
        if(StringUtil.isNotEmpty(taskId))
        {
            conditionSb.append(" and taskId = :taskId");
            values.put("taskId", this.taskId);
        }
        
        return conditionSb.toString();
    }
}