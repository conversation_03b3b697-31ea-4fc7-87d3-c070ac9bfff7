package com.sinoyd.lims.pro.vo;

import lombok.Data;

/**
 * 监测点位统计点位日期数据VO
 *
 * <AUTHOR>
 * @version V5.2.0 2025/4/14
 * @since V100R001
 */
@Data
public class FixedPointStatDateVO {

    /**
     * 日期
     */
    private String date;

    /**
     * 日期值
     */
    private String value;

    /**
     * 是否零点
     */
    private Boolean isZeroPoint = false;

    /**
     * 构造方法
     *
     * @param date        日期
     * @param value       日期值
     * @param isZeroPoint 是否零点
     */
    public FixedPointStatDateVO(String date, String value, Boolean isZeroPoint) {
        this.date = date;
        this.value = value;
        this.isZeroPoint = isZeroPoint;
    }
}
