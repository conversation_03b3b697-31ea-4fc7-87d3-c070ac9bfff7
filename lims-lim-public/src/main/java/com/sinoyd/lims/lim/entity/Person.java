package com.sinoyd.lims.lim.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.math.BigDecimal;
import java.util.Date;


/**
 * Person实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "Person")
@Data
@EntityListeners(AuditingEntityListener.class)
public class Person extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public Person() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 姓名
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("姓名")
    @Length(message = "姓名{validation.message.length}", max = 50)
    private String cName;

    /**
     * 拼音
     */
    @Column(length = 100)
    @ApiModelProperty("拼音")
    @Length(message = "拼音{validation.message.length}", max = 100)
    private String pinYin;

    /**
     * 全拼
     */
    @Column(length = 100)
    @ApiModelProperty("全拼")
    @Length(message = "全拼{validation.message.length}", max = 100)
    private String fullPinYin;

    /**
     * 编号
     */
    @Column(length = 20)
    @ApiModelProperty("编号")
    @Length(message = "编号{validation.message.length}", max = 40)
    private String userNo;

    /**
     * 所属科室（Guid）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属科室（Guid）")
    private String deptId;

    /**
     * 职务（常量（Guid）：LIM_Post）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("职务（常量（Guid）：LIM_Post）")
    private String postId;

    /**
     * 职称（常量（Guid）：LIM_TechnicalTitle）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("职称（常量（Guid）：LIM_TechnicalTitle）")
    private String technicalTitleId;

    /**
     * 职称获得日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("职称获得日期")
    private Date technicalTitleDate;

    /**
     * 英文名
     */
    @Column(length = 50)
    @ApiModelProperty("英文名")
    @Length(message = "英文名{validation.message.length}", max = 50)
    private String eName;

    /**
     * 出生日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("出生日期")
    private Date birthDay;

    /**
     * 个人邮箱
     */
    @Column(length = 100)
    @ApiModelProperty("个人邮箱")
    @Length(message = "个人邮箱{validation.message.length}", max = 100)
    private String email;

    /**
     * 性别（枚举EnumSex：  1代表男   2代表女）
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("性别（枚举EnumSex：  1代表男   2代表女）")
    private Integer sex;

    /**
     * 状态（枚举EnumPersonStatus:1代表在职 2代表离职 3代表休假）
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("状态（枚举EnumPersonStatus:1代表在职 2代表离职 3代表休假）")
    private Integer status;

    /**
     * 身份证
     */
    @Column(length = 20)
    @ApiModelProperty("身份证")
    @Length(message = "身份证{validation.message.length}", max = 20)
    private String idCard;

    /**
     * 政治面貌
     */
    @Column(length = 20)
    @ApiModelProperty("政治面貌")
    @Length(message = "政治面貌{validation.message.length}", max = 20)
    private String politicalFace;

    /**
     * 民族
     */
    @Column(length = 20)
    @ApiModelProperty("民族")
    @Length(message = "民族{validation.message.length}", max = 20)
    private String volk;

    /**
     * 籍贯
     */
    @Column(length = 100)
    @ApiModelProperty("籍贯")
    @Length(message = "籍贯{validation.message.length}", max = 100)
    private String nativePlace;

    /**
     * 档案所在地
     */
    @Column(length = 100)
    @ApiModelProperty("档案所在地")
    @Length(message = "档案所在地{validation.message.length}", max = 100)
    private String archivesPlace;

    /**
     * 户籍所在地
     */
    @Column(length = 100)
    @ApiModelProperty("户籍所在地")
    @Length(message = "户籍所在地{validation.message.length}", max = 100)
    private String homeTown;

    /**
     * 国籍
     */
    @Column(length = 20)
    @ApiModelProperty("国籍")
    @Length(message = "国籍{validation.message.length}", max = 20)
    private String nation;

    /**
     * 人员签名图片路径
     */
    @Column(length = 500)
    @ApiModelProperty("人员签名图片路径")
    @Length(message = "人员签名图片路径{validation.message.length}", max = 500)
    private String signature;

    /**
     * 签名密码
     */
    @Column(length = 20)
    @ApiModelProperty("签名密码")
    @Length(message = "签名密码{validation.message.length}", max = 20)
    private String codeSigning;

    /**
     * 人员头像图片路径
     */
    @Column(length = 500)
    @ApiModelProperty("人员头像图片路径")
    @Length(message = "人员头像图片路径{validation.message.length}", max = 500)
    private String photoUrl;

    /**
     * 排序值
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("排序值")
    private Integer orderNum;

    /**
     * 学历（常量（Guid）：LIM_Degree：大专、本科、硕士研究生、博士研究生）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("学历（常量（Guid）：LIM_Degree：大专、本科、硕士研究生、博士研究生）")
    @Length(message = "学历{validation.message.length}", max = 50)
    private String degree;

    /**
     * 毕业院校
     */
    @Column(length = 50)
    @ApiModelProperty("毕业院校")
    @Length(message = "毕业院校{validation.message.length}", max = 50)
    private String school;

    /**
     * 专业
     */
    @Column(length = 50)
    @ApiModelProperty("专业")
    @Length(message = "专业{validation.message.length}", max = 50)
    private String specialty;

    /**
     * 手机
     */
    @Column(length = 50)
    @ApiModelProperty("手机")
    @Length(message = "手机{validation.message.length}", max = 50)
    private String mobile;

    /**
     * 固定电话
     */
    @Column(length = 50)
    @ApiModelProperty("固定电话")
    @Length(message = "固定电话{validation.message.length}", max = 50)
    private String homeTel;

    /**
     * 出生地
     */
    @Column(length = 100)
    @ApiModelProperty("出生地")
    @Length(message = "出生地{validation.message.length}", max = 100)
    private String birthPlace;

    /**
     * 住址
     */
    @Column(length = 100)
    @ApiModelProperty("住址")
    @Length(message = "住址{validation.message.length}", max = 100)
    private String homeAddress;

    /**
     * 紧急联络人
     */
    @Column(length = 50)
    @ApiModelProperty("紧急联络人")
    @Length(message = "紧急联络人{validation.message.length}", max = 50)
    private String emergentLinkMan;

    /**
     * 联络方法
     */
    @Column(length = 50)
    @ApiModelProperty("联络方法")
    @Length(message = "联络方法{validation.message.length}", max = 50)
    private String contactMethod;

    /**
     * 准入证书
     */
    @Column(length = 100)
    @ApiModelProperty("准入证书")
    @Length(message = "准入证书{validation.message.length}", max = 100)
    private String certificateNO;

    /**
     * 准入证书获取时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("准入证书获取时间")
    private Date certificateDate;

    /**
     * 在岗时间
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("在岗时间")
    private BigDecimal yearsInThePosition;

    /**
     * 入职年限（参加工作时间）
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("入职年限（参加工作时间）")
    private Date workStartTime;

    /**
     * 入职时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("入职时间")
    private Date joinCompanyTime;

    /**
     * 开始工作时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("开始工作时间")
    private Date beginWorkTime;

    /**
     * 离职时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("离职时间")
    private Date leaveCompanyTime;

    /**
     * 入党日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("入党日期")
    private Date joinPartyDate;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;

    /**
     * 关键岗位
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("关键岗位")
    private Integer keyPost;

    /**
     * 技术能力
     */
    @ApiModelProperty("技术能力")
    @Length(message = "技术能力{validation.message.length}", max = 255)
    private String tecCompetence;

    /**
     * 从事检测和/或校准工作方面的职责
     */
    @ApiModelProperty("从事检测和/或校准工作方面的职责")
    @Length(message = "从事检测和/或校准工作方面的职责{validation.message.length}", max = 255)
    private String testResWork;

    /**
     * 检测和/或校准策划和结果评价方面的职责
     */
    @ApiModelProperty("检测和/或校准策划和结果评价方面的职责")
    @Length(message = "检测和/或校准策划和结果评价方面的职责{validation.message.length}", max = 255)
    private String testResEvaluation;

    /**
     * 提交意见和解释的职责
     */
    @ApiModelProperty("提交意见和解释的职责")
    @Length(message = "提交意见和解释的职责{validation.message.length}", max = 255)
    private String submissionDuty;

    /**
     * 方法改进新方法制定和确认方面的职责
     */
    @ApiModelProperty("方法改进新方法制定和确认方面的职责")
    @Length(message = "方法改进新方法制定和确认方面的职责{validation.message.length}", max = 255)
    private String developMethodRes;

    /**
     * 所需的专业知识和经验
     */
    @ApiModelProperty("所需的专业知识和经验")
    @Length(message = "所需的专业知识和经验{validation.message.length}", max = 255)
    private String experienceRequired;

    /**
     * 资格和培训计划
     */
    @ApiModelProperty("资格和培训计划")
    @Length(message = "资格和培训计划{validation.message.length}", max = 255)
    private String trainingPrograms;

    /**
     * 管理职责
     */
    @ApiModelProperty("管理职责")
    @Length(message = "管理职责{validation.message.length}", max = 255)
    private String manageRes;

    /**
     * 备注说明
     */
    @ApiModelProperty("备注说明")
    @Length(message = "备注说明{validation.message.length}", max = 255)
    private String remark;

    /**
     * 关联系统人员编号
     */
    @Column(length = 50)
    @ApiModelProperty("关联系统人员编号")
    private String externalId;

    /**
     * 监管平台id
     */
    @Column(length=50)
    @ApiModelProperty("监管平台id")
    private String regulateId;

    /**
     * 监管平台名称
     */
    @Column(length=50)
    @ApiModelProperty("监管平台名称")
    @Length(message = "监管平台名称{validation.message.length}", max = 50)
    private String regulateName;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}