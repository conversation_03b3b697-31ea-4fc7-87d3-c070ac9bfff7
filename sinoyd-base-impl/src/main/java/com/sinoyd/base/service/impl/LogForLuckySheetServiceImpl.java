package com.sinoyd.base.service.impl;

import com.sinoyd.base.dto.lims.DtoLogForLuckySheet;
import com.sinoyd.base.repository.lims.LogForLuckySheetRepository;
import com.sinoyd.base.service.LogForLuckySheetService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import org.springframework.stereotype.Service;

/**
 * logForLuckySheet操作记录服务
 * <AUTHOR>
 * @version V1.0.0 2022/10/24
 * @since V100R001
 */
@Service
public class LogForLuckySheetServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoLogForLuckySheet,String,LogForLuckySheetRepository> implements LogForLuckySheetService {

    @Override
    public void findByPage(PageBean<DtoLogForLuckySheet> page, BaseCriteria criteria) {
        page.setEntityName("DtoLogForLuckySheet p");
        page.setSelect("select p");
        super.findByPage(page, criteria);
    }
}
