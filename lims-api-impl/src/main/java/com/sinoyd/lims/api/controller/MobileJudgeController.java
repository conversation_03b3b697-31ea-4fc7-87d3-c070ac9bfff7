package com.sinoyd.lims.api.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.api.service.MobileJudgeService;
import com.sinoyd.lims.pro.dto.DtoSampleJudgeData;
import com.sinoyd.lims.pro.service.SampleJudgeDataService;
import com.sinoyd.lims.pro.vo.SampleJudgeDataVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api(tags = "示例: MobileJudge服务")
@RestController
@RequestMapping("api/field/judge")
public class MobileJudgeController extends BaseJpaController<DtoSampleJudgeData, String, MobileJudgeService> {

    private SampleJudgeDataService sampleJudgeDataService;

    @GetMapping("/getAnaItem")
    public RestResponse<List<Map<String, String>>> getAnalyzeItemByFolderId(@RequestParam("folderId") String folderId, @RequestParam("cycValue") Integer cycValue) {
        RestResponse<List<Map<String, String>>> response = new RestResponse<>();
        response.setData(service.getAnalyzeItemByFolderId(folderId, cycValue));
        return response;
    }

    @GetMapping("/findData")
    public RestResponse<List<DtoSampleJudgeData>> findByFolderIdForApp(@RequestParam("folderId") String folderId, @RequestParam("cycValue") Integer cycValue, @RequestParam("testId") String testId) {
        RestResponse<List<DtoSampleJudgeData>> response = new RestResponse<>();
        response.setData(service.findByFolderIdForApp(folderId, cycValue, testId));
        return response;
    }

    /**
     * 根据id批量删除
     *
     * @param vo id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping("/delete")
    public RestResponse<String> deleteForApp(@RequestBody SampleJudgeDataVo vo) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(vo.getIds());
        restResp.setCount(count);
        return restResp;
    }

    @ApiOperation(value = "批量保存", notes = "批量保存")
    @PutMapping("/update")
    public RestResponse<List<DtoSampleJudgeData>> updateForApp(@RequestBody SampleJudgeDataVo vo) {
        RestResponse<List<DtoSampleJudgeData>> response = new RestResponse<>();
        response.setData(service.update(vo.getDataList()));
        return response;
    }

    @ApiOperation(value = "添加质控样", notes = "添加质控样")
    @PostMapping("/addQcSample")
    public RestResponse<Void> addQcSample(@RequestBody  @Validated SampleJudgeDataVo vo) {
        RestResponse<Void> response = new RestResponse<>();
        sampleJudgeDataService.addQcSample(vo.getQcGrade(), vo.getQcType(), vo.getStandardCode(), vo.getExpectedValue(), vo.getNum(), vo.getIds());
        return response;
    }

    @PostMapping("/cancellation")
    public RestResponse<Boolean> cancellationData(@RequestBody SampleJudgeDataVo vo) {
        RestResponse<Boolean> response = new RestResponse<>();
        sampleJudgeDataService.cancellationData(vo.getIds());
        response.setData(Boolean.TRUE);
        return response;
    }

    @PostMapping("/cancellationCancel")
    public RestResponse<Boolean> cancellationCancelData(@RequestBody SampleJudgeDataVo vo) {
        RestResponse<Boolean> response = new RestResponse<>();
        sampleJudgeDataService.cancellationCancelData(vo.getIds());
        response.setData(Boolean.TRUE);
        return response;
    }

    /**
     * 更新是否不参与评价状态
     *
     * @param vo           比对数据ids
     * @param isNotEvaluate 是否不参与
     * @return 结果
     */
    @ApiOperation(value = "更新是否不参与评价状态", notes = "更新是否不参与评价状态")
    @PostMapping("/evaluateState/{isNotEvaluate}")
    public RestResponse<Boolean> updateEvaluateState(@RequestBody SampleJudgeDataVo vo, @PathVariable("isNotEvaluate") Boolean isNotEvaluate) {
        RestResponse<Boolean> response = new RestResponse<>();
        sampleJudgeDataService.updateEvaluateState(vo.getIds(), isNotEvaluate);
        response.setData(Boolean.TRUE);
        return response;
    }

    @Autowired
    @Lazy
    public void setSampleJudgeDataService(SampleJudgeDataService sampleJudgeDataService) {
        this.sampleJudgeDataService = sampleJudgeDataService;
    }
}
