package com.sinoyd.lims.foreign.service.impl;

import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.foreign.DataWorksheetReagentVO;
import com.sinoyd.lims.foreign.service.DataWorksheetReagentService;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * 试剂配置记录信息接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Service
public class DataWorksheetReagentServiceImpl extends AbsQueryDataServiceImpl<DataWorksheetReagentVO> implements DataWorksheetReagentService {

    @Override
    public StringBuilder getSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("select id,workSheetFolderId,reagentConfigId,reagent,context,");
        sql.append(" reagentName,reagentSpecification,configurationSolution,configDate,");
        sql.append(" expiryDate,course,diluent,reagentType,suitItem,orgId,domainId");
        sql.append(" from TB_PRO_WorkSheetReagent ");
        sql.append(" WHERE 1 = 1 ");
        return sql;
    }

    @Override
    protected void appendCondition(StringBuilder sql, Map<String, Object> map, String paramName, String fieldName) {
        super.appendCondition(sql, map, " AND modifyDate > :syncTime", "syncTime");
        super.appendCondition(sql, map, " AND workSheetFolderId in (:workSheetFolderIds) ", "workSheetFolderIds");
    }

    @Override
    protected RowMapper<DataWorksheetReagentVO> getEntityRowMapper() {
        return (rs, i) -> {
            DataWorksheetReagentVO worksheetReagentVO = new DataWorksheetReagentVO();
            worksheetReagentVO.setId(UUIDHelper.NewID());
            worksheetReagentVO.setWorkSheetFolderId(rs.getString("workSheetFolderId"));
            worksheetReagentVO.setReagentConfigId(rs.getString("reagentConfigId"));
            worksheetReagentVO.setReagent(rs.getString("reagent"));
            worksheetReagentVO.setContext(rs.getString("context"));
            worksheetReagentVO.setReagentName(rs.getString("reagentName"));
            worksheetReagentVO.setReagentSpecification(rs.getString("reagentSpecification"));
            worksheetReagentVO.setConfigurationSolution(rs.getString("configurationSolution"));
            worksheetReagentVO.setConfigDate(rs.getDate("configDate"));
            worksheetReagentVO.setExpiryDate(rs.getDate("expiryDate"));
            worksheetReagentVO.setCourse(rs.getString("course"));
            worksheetReagentVO.setDiluent(rs.getString("diluent"));
            worksheetReagentVO.setReagentType(rs.getInt("reagentType"));
            worksheetReagentVO.setSuitItem(rs.getString("suitItem"));
            worksheetReagentVO.setOrgId(rs.getString("orgId"));
            worksheetReagentVO.setDomainId(rs.getString("domainId"));
            worksheetReagentVO.setSyncTime(new Date());
            return worksheetReagentVO;
        };
    }
}
