package com.sinoyd.lims.lim.service.impl;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.service.AnalyzeItemService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.criteria.AnalyzeItemSortDetailCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoAnalyzeItemSortDetail;
import com.sinoyd.lims.lim.repository.lims.AnalyzeItemSortDetailRepository;
import com.sinoyd.lims.lim.service.AnalyzeItemSortDetialService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 分析项目排序详情
 * <AUTHOR>
 * @version V1.0.0 2019/1/17
 * @since V100R001
 */
@Service
public class AnalyzeItemSortDetailServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoAnalyzeItemSortDetail, String, AnalyzeItemSortDetailRepository> implements AnalyzeItemSortDetialService {

    private AnalyzeItemService analyzeItemService;

    @Override
    public void findByPage(PageBean<DtoAnalyzeItemSortDetail> pageBean, BaseCriteria criteria) {
        pageBean.setEntityName("DtoAnalyzeItemSortDetail a, DtoAnalyzeItem b");
        pageBean.setSelect("select a.id, a.sortId, a.analyzeItemId,b.analyzeItemName,a.orderNum,a.orgId");
        super.findByPage(pageBean, criteria);
        List<DtoAnalyzeItemSortDetail> dataList = pageBean.getData();
        List<DtoAnalyzeItemSortDetail> newDataList = new ArrayList<>();

        Iterator<DtoAnalyzeItemSortDetail> iterator = dataList.iterator();
        // 循环迭代获取JPQL中查询返回的属性

        while (iterator.hasNext()) {
            Object obj = iterator.next();
            Object[] objData = (Object[]) obj;
            // 按查询顺序下标一一获取
            DtoAnalyzeItemSortDetail dtoAnalyzeItemSortDetail = new DtoAnalyzeItemSortDetail();
            dtoAnalyzeItemSortDetail.setId((String) objData[0]);
            dtoAnalyzeItemSortDetail.setSortId((String) objData[1]);
            dtoAnalyzeItemSortDetail.setAnalyzeItemId((String) objData[2]);
            dtoAnalyzeItemSortDetail.setAnalyzeItemName((String) objData[3]);
            dtoAnalyzeItemSortDetail.setOrderNum((Integer) objData[4]);
            dtoAnalyzeItemSortDetail.setOrgId((String) objData[5]);
            newDataList.add(dtoAnalyzeItemSortDetail);
        }

        pageBean.setData(newDataList);
    }

    /**
     * 获取分析项目排序详情列表
     * 
     * @param sortId
     * @return
     */
    @Override
    public List<DtoAnalyzeItemSortDetail> getSortDetailList(String sortId) {
        try {
            //排序查询前，延时200毫秒
            for (int i = 0; i < 200; i++) {
                TimeUnit.MILLISECONDS.sleep(1);
            }
        } catch (Exception ex) {

        }
        PageBean<DtoAnalyzeItemSortDetail> pageBean = new PageBean<>();
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        pageBean.setSort("a.orderNum-");
        AnalyzeItemSortDetailCriteria analyzeItemSortDetailCriteria = new AnalyzeItemSortDetailCriteria();
        analyzeItemSortDetailCriteria.setSortId(sortId);
        findByPage(pageBean, analyzeItemSortDetailCriteria);
        return pageBean.getData();
    }

    /**
     * 保存分析项目排序详情 analyzeItems 分析项目集合 sortId 排序id
     */
    // @Override
    // public Integer saveSortDetials(Integer sortId, Collection<AnalyzeItem>
    // analyzeItems) {
    // ArrayList<AnalyzeItemSortDetail> details = new
    // ArrayList<AnalyzeItemSortDetail>();
    // Integer index =
    // analyzeItemSortDetailRepository.getMinNum(sortId);//排序的最大值,依次递减
    // if(index==null)
    // {
    // index = 10000;
    // }
    // for (AnalyzeItem analyzeItem : analyzeItems) {
    // AnalyzeItemSortDetail detail = new AnalyzeItemSortDetail();
    // detail.setAnalyzeItemId(analyzeItem.getId());
    // detail.setAnalyzeItemName(analyzeItem.getAnalyzeItemName());
    // detail.setOrderNum(index);
    // detail.setSortId(sortId);
    // detail.setIsDeleted(false);
    // details.add(detail);
    // index--;
    // }
    // analyzeItemSortDetailRepository.save(details);
    // return details.size();
    // }

    //TODO:李川元 此处的排序顺序应该是后台处理还是前台设置?


    /**
     * 保存分析项目排序详情
     */
    @Transactional
    @Override
    public Integer saveSortDetails(String sortId, Collection<DtoAnalyzeItem> entities) {

        List<DtoAnalyzeItemSortDetail> details = new ArrayList<>();

        Integer index = 10000;
        repository.deleteBySortId(sortId);

        List<String> itemIds = entities.stream().map(DtoAnalyzeItem::getId).collect(Collectors.toList());
        List<DtoAnalyzeItem> analyzeItemById = analyzeItemService.findAll(itemIds);
        for (String analyzeItemId : itemIds) {
            DtoAnalyzeItem dtoAnalyzeItem = analyzeItemById.stream().filter(p -> p.getId().equals(analyzeItemId)).findFirst().orElse(null);
            if (StringUtil.isNotNull(dtoAnalyzeItem)){
                DtoAnalyzeItemSortDetail dtoAnalyzeItemSortDetail = new DtoAnalyzeItemSortDetail();
                dtoAnalyzeItemSortDetail.setAnalyzeItemName(dtoAnalyzeItem.getAnalyzeItemName());
                dtoAnalyzeItemSortDetail.setSortId(sortId);
                dtoAnalyzeItemSortDetail.setAnalyzeItemId(dtoAnalyzeItem.getId());
                dtoAnalyzeItemSortDetail.setOrderNum(index);
                details.add(dtoAnalyzeItemSortDetail);
            }
            index--;
        }
        List<DtoAnalyzeItemSortDetail> save = repository.save(details);
        return save.size();
    }

    @Autowired
    @Lazy
    public void setAnalyzeItemService(AnalyzeItemService analyzeItemService) {
        this.analyzeItemService = analyzeItemService;
    }
}