package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.pro.dto.DtoComment;
import com.sinoyd.lims.pro.dto.customer.DtoLog;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.CommentRepository;
import com.sinoyd.lims.pro.service.CommentService;
import com.sinoyd.lims.pro.service.NewLogService;
import com.sinoyd.lims.pro.util.ArrayUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 评论操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/14
 * @since V100R001
 */
@Service
public class CommentServiceImpl extends BaseJpaServiceImpl<DtoComment, String, CommentRepository> implements CommentService {

    @Autowired
    @Lazy
    private NewLogService newLogService;

    @Override
    public void findByPage(PageBean<DtoComment> pb, BaseCriteria commentCriteria) {
        pb.setEntityName("DtoComment a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, commentCriteria);
    }

    /**
     * 返回评论与日志
     *
     * @param objectId   关联id
     * @param objectType 关联类型
     * @return 评论与日志
     */
    @Override
    public DtoComment[] findAllComment(String objectId, Integer objectType) {
        // 查询项目评论
        List<DtoComment> commentList = repository.findByObjectIdAndObjectTypeOrderByCommentTime(objectId, objectType);
        // 查询项目相关日志
        List<DtoLog> logList = newLogService.findCommentLog(objectId, objectType);
        // 将项目日志中的修改项目,进行过滤
        List<DtoLog> logs = logList.stream().filter(item -> !item.getOperateInfo().equals(EnumPRO.EnumLogOperateType.修改项目.toString())).collect(Collectors.toList());
        // 按照操作时间进行排序
        logs.sort(Comparator.comparing(DtoLog::getOperateTime));
        List<DtoComment> commentLogs = new ArrayList<>();
        for (DtoLog log : logs) {
            DtoComment comment = new DtoComment(log, objectId, objectType);
            commentLogs.add(comment);
        }
        DtoComment[] logArr = commentLogs.toArray(new DtoComment[logs.size()]);
        DtoComment[] comments = commentList.toArray(new DtoComment[commentList.size() + logs.size()]);
        ArrayUtil.merge(comments, commentList.size(), logArr, logArr.length, Comparator.comparing(DtoComment::getCommentTime));
        return comments;
    }

    @Override
    public DtoComment save(DtoComment comment) {
        comment.setCommentTime(new Date());
        return super.save(comment);
    }

    /**
     * 删除留言
     */
    @Transactional
    @Override
    public <K extends Serializable> Integer logicDeleteById(K id) {
        String idStr = String.valueOf(id);
        DtoComment comment = this.findOne(idStr);
        if (StringUtil.isNull(comment)) {
            throw new BaseException("该评论不是留言或已删除！");
        }
        String userId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getUserId() : "";
        if (!comment.getCommentPersonId().equals(userId)) {
            throw new BaseException("您无法删除别人的留言！");
        }
        return super.logicDeleteById(idStr);
    }
}