package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoNoticeMsg;
import com.sinoyd.lims.lim.repository.lims.NoticeMsgRepository;
import com.sinoyd.lims.lim.service.NoticeMsgService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;


/**
 * NoticeMsg操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/22
 * @since V100R001
 */
@Service
public class NoticeMsgServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoNoticeMsg, String, NoticeMsgRepository> implements NoticeMsgService {

    @Override
    public void findByPage(PageBean<DtoNoticeMsg> pb, BaseCriteria noticeMsgCriteria) {
        pb.setEntityName("DtoNoticeMsg a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, noticeMsgCriteria);
    }

    /**
     * 创建公告留言
     */
    @Transactional
    @Override
    public DtoNoticeMsg save(DtoNoticeMsg noticeMsg) {
        //获取当前人
        noticeMsg.setMessagePersonId(PrincipalContextUser.getPrincipal().getUserId());
        noticeMsg.setMessagePerson(PrincipalContextUser.getPrincipal().getUserName());
        noticeMsg.setMsgTime(new Date());
        return super.save(noticeMsg);
    }

}