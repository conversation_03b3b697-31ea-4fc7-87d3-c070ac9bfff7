package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;

import com.sinoyd.lims.lim.dto.lims.DtoExamineType;
import com.sinoyd.lims.lim.dto.lims.DtoExamineTypeRecord;
import com.sinoyd.lims.lim.repository.lims.ExamineTypeRecordRepository;
import com.sinoyd.lims.lim.repository.lims.ExamineTypeRepository;
import com.sinoyd.lims.lim.service.ExamineTypeService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 考核类型操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/09/14
 * @since V100R001
 */
@Service
public class ExamineTypeServiceImpl extends BaseJpaServiceImpl<DtoExamineType, String, ExamineTypeRepository> implements ExamineTypeService {

    @Autowired
    private ExamineTypeRecordRepository examineTypeRecordRepository;

    @Override
    @Transactional
    public <K extends Serializable> Integer logicDeleteById(K id) {
        DtoExamineType entity = repository.findOne((String)id);
        if(StringUtil.isNull(entity)){
            throw new BaseException("未查询到考核内容！");
        }
        //大类级联删除小类，并级联删除已填写的记录
        List<String> typeIdList = new ArrayList<>();
        typeIdList.add(entity.getId());
        List<DtoExamineType> secondaryList = repository.findAllByParentId(entity.getId());
        if(!secondaryList.isEmpty()){
            List<String> secondaryIds = secondaryList.stream().map(DtoExamineType::getId).collect(Collectors.toList());
            super.logicDeleteById(secondaryIds);
            typeIdList.addAll(secondaryIds);
        }
        List<DtoExamineTypeRecord> typeRecordList  = examineTypeRecordRepository.findAllByExamineTypeIdIn(typeIdList);
        if(!typeRecordList.isEmpty()){
            List<String> recordIds = typeRecordList.stream().map(DtoExamineTypeRecord::getId).collect(Collectors.toList());
            examineTypeRecordRepository.logicDeleteById(recordIds,new Date());
        }
        repository.save(secondaryList);
        return super.logicDeleteById(id);
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<?> ids) {
        //级联删除所有关联的考核项目填写记录
        List<DtoExamineTypeRecord> typeRecordList  = examineTypeRecordRepository.findAllByExamineTypeIdIn((List<String>) ids);
        if(!typeRecordList.isEmpty()){
            List<String> recordIds = typeRecordList.stream().map(DtoExamineTypeRecord::getId).collect(Collectors.toList());
            examineTypeRecordRepository.logicDeleteById(recordIds,new Date());
        }
        return super.logicDeleteById(ids);
    }

    @Override
    @Transactional
    public DtoExamineType copyExamineType(String id) {
        DtoExamineType origin = repository.findOne(id);
        if(StringUtil.isNull(origin)){
            throw new BaseException("未查询到考核内容！");
        }
        DtoExamineType entity = new DtoExamineType();
        BeanUtils.copyProperties(origin,entity);
        String newId = UUIDHelper.NewID();
        entity.setId(newId);
        // 大项需同步复制小项
        if("00000000-0000-0000-0000-000000000000".equals(entity.getParentId())){
            List<DtoExamineType> copyList = new ArrayList<>();
            List<DtoExamineType> secondaryList = repository.findAllByParentId(origin.getId());
            for (DtoExamineType secondary:secondaryList) {
                DtoExamineType secondaryCopy = new DtoExamineType();
                BeanUtils.copyProperties(secondary,secondaryCopy);
                secondaryCopy.setId(UUIDHelper.NewID());
                secondaryCopy.setParentId(newId);
                secondaryCopy.setProgress(0);
                secondaryCopy.setCreateDate(new Date());
                secondaryCopy.setCreator(null);
                secondaryCopy.setModifier(null);
                secondaryCopy.setModifyDate(new Date());
                copyList.add(secondaryCopy);
            }
            repository.save(copyList);
        }
        entity.setProgress(0);
        entity.setCreateDate(new Date());
        entity.setCreator(null);
        entity.setModifier(null);
        entity.setModifyDate(new Date());
        return repository.save(entity);
    }
}
