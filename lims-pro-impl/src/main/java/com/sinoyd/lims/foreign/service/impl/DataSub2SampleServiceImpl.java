package com.sinoyd.lims.foreign.service.impl;

import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.foreign.DataSub2SampleVO;
import com.sinoyd.lims.foreign.service.DataSub2SampleService;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * 领样单和样品关系接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Service
public class DataSub2SampleServiceImpl extends AbsQueryDataServiceImpl<DataSub2SampleVO> implements DataSub2SampleService {


    @Override
    public StringBuilder getSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("select t1.id,t1.receiveId,t3.sampleId,");
        sql.append(" t1.orgId,t1.domainId");
        sql.append(" from TB_PRO_ReceiveSubSampleRecord t1 ");
        sql.append(" LEFT JOIN TB_PRO_ReceiveSampleRecord t2 on t1.receiveId = t2.id ");
        sql.append(" LEFT JOIN TB_PRO_ReceiveSubSampleRecord2Sample t3 on t1.id = t3.receiveSubSampleRecordId ");
        sql.append(" WHERE 1 = 1 ");
        return sql;
    }

    @Override
    protected void appendCondition(StringBuilder sql, Map<String, Object> map, String paramName, String fieldName) {
        super.appendCondition(sql, map, " AND t1.modifyDate > :syncTime ", "syncTime");
        super.appendCondition(sql, map, " AND t3.sampleId in (:sampleIds)", "sampleIds");
        super.appendCondition(sql, map, " AND t1.receiveId in (:receiveIds) ", "receiveIds");
    }

    @Override
    protected RowMapper<DataSub2SampleVO> getEntityRowMapper() {
        return (rs, i) -> {
            DataSub2SampleVO sub2SampleVO = new DataSub2SampleVO();
            sub2SampleVO.setId(UUIDHelper.NewID());
            sub2SampleVO.setReceiveId(rs.getString("receiveId"));
            sub2SampleVO.setSampleId(rs.getString("sampleId"));
            sub2SampleVO.setSubId(rs.getString("id"));
            sub2SampleVO.setOrgId(rs.getString("orgId"));
            sub2SampleVO.setDomainId(rs.getString("domainId"));
            sub2SampleVO.setSyncTime(new Date());
            return sub2SampleVO;
        };
    }

}
