package com.sinoyd.lims.pro.controller;

import com.sinoyd.base.criteria.EnterpriseCriteria;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoProject2Customer;
import com.sinoyd.lims.pro.service.MultiPollutionEnterpriseService;
import com.sinoyd.lims.pro.service.ProjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 污染源多企业服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@Api(tags = "示例: 污染源多企业服务")
@RestController
@RequestMapping("api/pro/project/v2")
public class MultiPollutionEnterpriseController extends BaseJpaController<DtoProject, String, ProjectService> {

    private MultiPollutionEnterpriseService multiPollutionEnterpriseService;

    /**
     * 判断是否已经选择企业
     *
     * @param projectIds 项目id
     * @throws Exception 错误内容
     */
    @ApiOperation(value = "判断是否已经选择企业", notes = "判断是否已经选择企业")
    @PostMapping("/judgmentEnterprise")
    public RestResponse<Boolean> judgmentEnterprise(@RequestBody List<String> projectIds) throws Exception {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        multiPollutionEnterpriseService.judgmentEnterprise(projectIds);
        restResponse.setData(Boolean.TRUE);
        return restResponse;
    }

    /**
     * 根据主项目拆分子项目
     *
     * @param projectId 主项目id
     */
    @ApiOperation(value = "根据主项目拆分子项目", notes = "根据主项目拆分子项目")
    @PostMapping("/splitSubProject/{projectId}")
    public RestResponse<Boolean> splitSubProject(@PathVariable("projectId") String projectId) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        multiPollutionEnterpriseService.splitSubProject(projectId);
        restResponse.setData(Boolean.TRUE);
        return restResponse;
    }

    /**
     * 删除多企业污染源项目
     *
     * @param ids 项目ids
     */
    @ApiOperation(value = "删除多企业污染源项目", notes = "删除多企业污染源项目")
    @DeleteMapping("/deleteProjectByIds")
    public RestResponse<Boolean> logicDeleteByIds(@RequestBody List<String> ids) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        multiPollutionEnterpriseService.logicDeleteByIds(ids);
        restResponse.setData(Boolean.TRUE);
        return restResponse;
    }

    /**
     * 添加企业
     *
     * @param project2CustomerList 企业信息
     */
    @ApiOperation(value = "添加企业", notes = "添加企业")
    @PostMapping("/addEnterprise")
    public RestResponse<Boolean> addEnterprise(@RequestBody List<DtoProject2Customer> project2CustomerList) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        multiPollutionEnterpriseService.addEnterprise(project2CustomerList);
        restResponse.setData(Boolean.TRUE);
        return restResponse;
    }

    /**
     * 根据id删除企业
     *
     * @param proEntIds ids
     */
    @ApiOperation(value = "根据id删除企业", notes = "根据id删除企业")
    @DeleteMapping("/deleteEnterprise")
    public RestResponse<Boolean> deleteEnterprise(@RequestBody List<String> proEntIds) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        multiPollutionEnterpriseService.deleteEnterprise(proEntIds);
        restResponse.setData(Boolean.TRUE);
        return restResponse;
    }

    /**
     * 查询多企业项目中的企业信息
     *
     * @param enterpriseCriteria 查询条件
     */
    @ApiOperation(value = "查询多企业项目中的企业信息", notes = "查询多企业项目中的企业信息")
    @GetMapping("/enterpriseByProId")
    public RestResponse<List<DtoEnterprise>> findByPage(EnterpriseCriteria enterpriseCriteria) {
        RestResponse<List<DtoEnterprise>> restResp = new RestResponse<>();
        PageBean<DtoEnterprise> pb = super.getPageBean();
        multiPollutionEnterpriseService.findByPage(pb, enterpriseCriteria);
        restResp.setData(pb.getData());
        restResp.setCount(pb.getRowsCount());
        return restResp;
    }

    @Autowired
    @Lazy
    public void setMultiPollutionEnterpriseService(MultiPollutionEnterpriseService multiPollutionEnterpriseService) {
        this.multiPollutionEnterpriseService = multiPollutionEnterpriseService;
    }
}
