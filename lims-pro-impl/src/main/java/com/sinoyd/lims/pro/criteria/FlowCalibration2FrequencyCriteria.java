package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 流量校准关联点位周期查询条件
 * <AUTHOR>
 * @version V1.0.0
 * @since   2024/11/18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FlowCalibration2FrequencyCriteria extends BaseCriteria {
    /**
     * 流量校准标识
     */
    private String flowCalibrationId;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if(StringUtil.isNotEmpty(flowCalibrationId)){
            condition.append(" and f.flowCalibrationId = :flowCalibrationId");
            values.put("flowCalibrationId",flowCalibrationId);
        }
        return condition.toString();
    }
}
