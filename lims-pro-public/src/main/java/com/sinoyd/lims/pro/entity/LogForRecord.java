package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.configuration.PrincipalContextUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import java.util.Date;


/**
 * LogForRecord实体
 * <AUTHOR>
 * @version V1.0.0 2019/12/12
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="LogForRecord")
 @Data
 public  class LogForRecord implements BaseEntity,Serializable {

    private static final long serialVersionUID = 1L;

    public  LogForRecord() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }
    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 操作者Id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("操作者Id")
    private String operatorId;

    /**
     * 操作者名字
     */
    @Column(length = 50)
    @ApiModelProperty("操作者名字")
    private String operatorName;

    /**
     * 操作时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @ApiModelProperty("操作时间")
    private Date operateTime;

    /**
     * 操作类型（新建、保存、修改等）
     */
    @Column(length = 500)
    @ApiModelProperty("操作类型（新建、保存、修改等）")
    private String operateInfo;

    /**
     * 下一步操作人Id
     */
    @Column(length = 50)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("下一步操作人Id")
    private String nextOperatorId;

    /**
     * 下一步操作人名字
     */
    @Column(length = 50)
    @ApiModelProperty("下一步操作人名字")
    private String nextOperatorName;

    /**
     * 日志类型（如项目的方案、合同，样品的信息、检测项目）
     */
    @Column(nullable = false)
    @ApiModelProperty("日志类型（如项目的方案、合同，样品的信息、检测项目）")
    private Integer logType;

    /**
     * 对象id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("对象id")
    private String objectId;

    /**
     * 对象类型（检测单、项目、数据等）
     */
    @Column(nullable = false)
    @ApiModelProperty("对象类型（检测单、项目、数据等）")
    private Integer objectType;

    /**
     * 说明
     */
    @Column(length = 4000)
    @ApiModelProperty("说明")
    private String comment;

    /**
     * 意见（评审意见等）
     */
    @Column(length = 1000)
    @ApiModelProperty("意见（评审意见等）")
    private String opinion;

    /**
     * 备注
     */
    @Column(length = 1000)
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

}