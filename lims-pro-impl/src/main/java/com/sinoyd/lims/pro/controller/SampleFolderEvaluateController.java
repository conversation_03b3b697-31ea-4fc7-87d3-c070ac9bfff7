package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.dto.DtoSampleFolderEvaluate;
import com.sinoyd.lims.pro.service.SampleFolderEvaluateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * SampleFolderEvaluate服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2023/06/13
 * @since V100R001
 */
@Api(tags = "示例: SampleJudgeData服务")
@RestController
@RequestMapping("api/pro/sampleFolderEvaluate")
public class SampleFolderEvaluateController extends BaseJpaController<DtoSampleFolderEvaluate, String, SampleFolderEvaluateService> {

    @ApiOperation(value = "保存评价记录", notes = "保存评价记录")
    @PostMapping
    public RestResponse<DtoSampleFolderEvaluate> save(@RequestBody DtoSampleFolderEvaluate sampleFolderEvaluate) {
        RestResponse<DtoSampleFolderEvaluate> response = new RestResponse<>();
        response.setData(service.save(sampleFolderEvaluate));
        return response;
    }

    @ApiOperation(value = "保存评价记录", notes = "保存评价记录")
    @PostMapping("/saveList")
    public RestResponse<List<DtoSampleFolderEvaluate>> saveList(@RequestBody List<DtoSampleFolderEvaluate> folderEvaluateList) {
        RestResponse<List<DtoSampleFolderEvaluate>> response = new RestResponse<>();
        response.setData(service.saveList(folderEvaluateList));
        return response;
    }
}
