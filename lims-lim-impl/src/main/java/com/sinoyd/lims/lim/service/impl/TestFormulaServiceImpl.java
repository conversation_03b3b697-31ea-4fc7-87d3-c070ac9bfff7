package com.sinoyd.lims.lim.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.base.dto.customer.DtoImportTestFormulaForUpdate;
import com.sinoyd.base.dto.customer.DtoImportTestFormulaParams;
import com.sinoyd.base.dto.customer.DtoImportTestFormulaRevise;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.criteria.TestFormulaCriteria;
import com.sinoyd.lims.lim.dto.customer.DtoTestFormula;
import com.sinoyd.lims.lim.dto.customer.DtoTestFormulaTheSame;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsPartFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsTestFormula;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.ParamsFormulaRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsPartFormulaRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsTestFormulaRepository;
import com.sinoyd.lims.lim.service.AnalyzeMethodService;
import com.sinoyd.lims.lim.service.DownLoadFormulaTemplateService;
import com.sinoyd.lims.lim.service.TestFormulaService;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 测试公式
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@Service
public class TestFormulaServiceImpl implements TestFormulaService {

    @Autowired
    private ParamsFormulaRepository paramsFormulaRepository;

    @Autowired
    private ParamsPartFormulaRepository paramsPartFormulaRepository;

    @Autowired
    private ParamsTestFormulaRepository paramsTestFormulaRepository;

    @Autowired
    private CommonRepository commonRepository;

    @Autowired
    @Lazy
    private TestService testService;

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    @Autowired
    private RedisTemplate redisTemplate;

    private ImportUtils importUtils;

    private DownLoadFormulaTemplateService downLoadFormulaTemplateService;

    @PersistenceContext(unitName = "primaryEntityManagerFactory")
    private EntityManager entityManager;


    @Autowired
    @Lazy
    private AnalyzeMethodService analyzeMethodService;

    @Override
    public void findByPage(PageBean<DtoTestFormula> pageBean, BaseCriteria baseCriteria) {

        StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.lim.dto.customer.DtoTestFormula(");
        stringBuilder.append("a.id,a.objectId,b.redAnalyzeItemName,b.redAnalyzeMethodName,b.redCountryStandard,a.formula,a.sampleTypeId,");
        stringBuilder.append("c.typeName,a.configDate,b.analyzeMethodId)");

        // 设置查询的实体类名及别名
        pageBean.setEntityName(getFindByPageEntityName());

        pageBean.setSelect(stringBuilder.toString());

        commonRepository.findByPage(selectEntityManager(), pageBean, baseCriteria);
        TestFormulaCriteria testFormulaCriteria = (TestFormulaCriteria) baseCriteria;
        if (testFormulaCriteria.getIsCombo()) {
            return;
        }
        List<DtoTestFormula> testFormulaList = pageBean.getData();


        List<String> ids = testFormulaList.stream().map(DtoTestFormula::getId).collect(Collectors.toList());
        List<DtoParamsPartFormula> paramsPartFormulas = new ArrayList<>();
        if (ids.size() > 0) {
            paramsPartFormulas = paramsPartFormulaRepository.findByFormulaIdIn(ids);
        }

        Iterator<DtoTestFormula> iterator = testFormulaList.iterator();
        List<DtoTestFormula> newList = new ArrayList<>();
        while (iterator.hasNext()) {
            DtoTestFormula dtoTestFormula = iterator.next();
            List<DtoParamsPartFormula> dtoParamsPartFormulas = paramsPartFormulas.stream().filter(p -> p.getFormulaId().equals(dtoTestFormula.getId())).collect(Collectors.toList());
            newList.add(getDtoTestFormula(dtoTestFormula, dtoParamsPartFormulas));
        }

        processMethodState(testFormulaList);
        pageBean.setData(newList);
    }

    @Transactional
    @Override
    public DtoTestFormula saveTestFormula(DtoTestFormula dtoTestFormula) {
        List<DtoParamsTestFormula> paramsTestFormulas = dtoTestFormula.getParamsTestFormulas();
        if (StringUtil.isNotNull(paramsTestFormulas) && paramsTestFormulas.size() > 0) {
            if (paramsTestFormulas.stream().map(DtoParamsTestFormula::getAlias).distinct().collect(Collectors.toList()).size() <
                    paramsTestFormulas.stream().map(DtoParamsTestFormula::getAlias).collect(Collectors.toList()).size()) {
                throw new BaseException("存在重复的参数别名,请修改参数别名");
            }
        }
        DtoParamsFormula dtoParamsFormula = new DtoParamsFormula();
        if (StringUtils.isNotNullAndEmpty(dtoTestFormula.getId())) {
            dtoParamsFormula.setId(dtoTestFormula.getId());
        } else {//用于返回给前端
            dtoTestFormula.setId(dtoParamsFormula.getId());
        }
        dtoParamsFormula.setObjectId(dtoTestFormula.getTestId());
        dtoParamsFormula.setFormula(dtoTestFormula.getFormula());
        dtoParamsFormula.setConfigDate(new Date());
        dtoParamsFormula.setOrignFormula(dtoTestFormula.getOrignFormula());
        dtoParamsFormula.setOrignFormulatType(dtoTestFormula.getOrignFormulatType());
        dtoParamsFormula.setObjectType(EnumLIM.EnumParamsFormulaObjectType.测试公式.getValue());
        dtoParamsFormula.setSampleTypeId(dtoTestFormula.getSampleTypeId());
        DtoParamsFormula item = paramsFormulaRepository.save(dtoParamsFormula);
        //清空公式相关参数，重新保存
        //paramsTestFormulaRepository.deleteByObjId(dtoParamsFormula.getId());
        if (StringUtil.isNotNull(paramsTestFormulas) && paramsTestFormulas.size() > 0) {
            for (DtoParamsTestFormula dtoParamsTestFormula : paramsTestFormulas) {
                dtoParamsTestFormula.setObjId(item.getId());
                dtoParamsTestFormula.setId(UUIDHelper.NewID());
                dtoParamsTestFormula.setSourceType(EnumLIM.EnumSourceType.无.getValue());
            }
            paramsTestFormulaRepository.save(paramsTestFormulas);
        }
        //新增的时候不需要保存部分公式，公式单独存储
        this.saveParamsPartFormula(dtoTestFormula, dtoParamsFormula.getId());
        return dtoTestFormula;
    }

    @Transactional
    @Override
    public DtoTestFormula updateTestFormula(DtoTestFormula dtoTestFormula) {

        List<DtoParamsTestFormula> paramsTestFormulas = dtoTestFormula.getParamsTestFormulas();
        if (StringUtil.isNotNull(paramsTestFormulas) && paramsTestFormulas.size() > 0) {
            if (paramsTestFormulas.stream().map(DtoParamsTestFormula::getAlias).distinct().collect(Collectors.toList()).size() <
                    paramsTestFormulas.stream().map(DtoParamsTestFormula::getAlias).collect(Collectors.toList()).size()) {
                throw new BaseException("存在重复的参数别名,请修改参数别名");
            }
        }

        DtoParamsFormula dtoParamsFormula = paramsFormulaRepository.findOne(dtoTestFormula.getId());
        dtoParamsFormula.setObjectId(dtoTestFormula.getTestId());
        dtoParamsFormula.setFormula(dtoTestFormula.getFormula());
        dtoParamsFormula.setConfigDate(dtoTestFormula.getConfigDate());
        dtoParamsFormula.setOrignFormula(dtoTestFormula.getOrignFormula());
        dtoParamsFormula.setOrignFormulatType(dtoTestFormula.getOrignFormulatType());
        dtoParamsFormula.setSampleTypeId(dtoTestFormula.getSampleTypeId());
        paramsFormulaRepository.save(dtoParamsFormula);
        //清空公式相关参数，重新保存
        //paramsTestFormulaRepository.deleteByObjId(dtoParamsFormula.getId());
        if (StringUtil.isNotNull(paramsTestFormulas) && paramsTestFormulas.size() > 0) {
            for (DtoParamsTestFormula dtoParamsTestFormula : paramsTestFormulas) {
                dtoParamsTestFormula.setObjId(dtoParamsFormula.getId());
                dtoParamsTestFormula.setSourceType(EnumLIM.EnumSourceType.无.getValue());
                if (!StringUtil.isNotNull(dtoParamsTestFormula.getSlashValue())) {
                    dtoParamsTestFormula.setSlashValue(-1);
                }
            }
            paramsTestFormulaRepository.save(paramsTestFormulas);
        }
        //清除部分公式，重新保存
        //paramsPartFormulaRepository.deleteByObjId(dtoParamsFormula.getId());
        //修改保存不要保存部分公式，公式部分单独接口保存
        saveParamsPartFormula(dtoTestFormula, dtoTestFormula.getId());
        return dtoTestFormula;
    }

    @Override
    public DtoTestFormula findOne(String id) {
        DtoParamsFormula dtoParamsFormula = paramsFormulaRepository.findOne(id);
        if (StringUtil.isNotNull(dtoParamsFormula)) {
            DtoSampleType dtoSampleType = sampleTypeService.findOne(dtoParamsFormula.getSampleTypeId());
            DtoTestFormula dtoTestFormula = new DtoTestFormula();
            dtoTestFormula.setId(id);
            dtoTestFormula.setTestId(dtoParamsFormula.getObjectId());
            loadTestInfo4TestFormula(dtoTestFormula);
            if (StringUtil.isNotNull(dtoSampleType)) {
                dtoTestFormula.setSampleTypeName(dtoSampleType.getTypeName());
            }
            dtoTestFormula.setOrignFormula(dtoParamsFormula.getOrignFormula());
            dtoTestFormula.setOrignFormulatType(dtoParamsFormula.getOrignFormulatType());
            dtoTestFormula.setSampleTypeId(dtoParamsFormula.getSampleTypeId());
            dtoTestFormula.setFormula(dtoParamsFormula.getFormula());
            dtoTestFormula.setConfigDate(dtoParamsFormula.getConfigDate());
            List<DtoParamsPartFormula> paramsPartFormulas = paramsPartFormulaRepository.findByFormulaId(id);
            List<DtoParamsTestFormula> paramsTestFormulas = paramsTestFormulaRepository.findByObjId(id);
            DtoTestFormula item = getDtoTestFormula(dtoTestFormula, paramsPartFormulas);
            paramsTestFormulas.forEach(p -> {
                if (StringUtil.isNotNull(p.getSlashValue()) && p.getSlashValue().equals(-1)) {
                    p.setSlashValue(null);
                }
            });
            item.setParamsTestFormulas(paramsTestFormulas);
            return item;
        }
        return null;
    }

    @Override
    @Transactional
    public Integer delete(List<String> ids) {
        return paramsFormulaRepository.logicDeleteById(ids, new Date());
    }

    @Override
    public Boolean isSameTestFormula(DtoTestFormulaTheSame dtoTestFormulaTheSame) {
        List<String> testIds = dtoTestFormulaTheSame.getTestIds();
        String formula = dtoTestFormulaTheSame.getFormula();
        //参数公式
        List<DtoParamsFormula> paramsFormulas = paramsFormulaRepository.findByObjectIds(testIds);

        //相同公式的参数
        List<DtoParamsFormula> newParamsFormulas = paramsFormulas.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getFormula()) && p.getFormula().equals(formula)).collect(Collectors.toList());

        //公式是否相同
        for (String testId : testIds) {
            //如果不存在直接退出
            Optional<DtoParamsFormula> paramsFormulaOption = newParamsFormulas.stream().filter(p -> p.getFormula().equals(formula) && p.getObjectId().equals(testId)).findFirst();
            if (!paramsFormulaOption.isPresent()) {
                return false;
            }
        }
        return true;
    }

    @Transactional
    @Override
    public void copyTestFormula(String formulaId, String sampleTypeId, List<String> testIds) {
        DtoTestFormula item = findOne(formulaId);
        if (StringUtil.isNotNull(item)) {
            if (StringUtil.isNotEmpty(item.getTestId())) {
                testIds.remove(item.getTestId());
            }
            if (StringUtil.isNotEmpty(testIds)) {
                List<DtoParamsFormula> paramsFormulas = paramsFormulaRepository.findByObjectIdsAndSampleTypeId(testIds, item.getSampleTypeId());
                List<DtoParamsPartFormula> paramsPartFormulasAll = new ArrayList<>();
                List<DtoParamsFormula> paramsFormulaAll = new ArrayList<>();
                List<DtoParamsTestFormula> paramsTestFormulasAll = new ArrayList<>();
                List<String> existFormulaTestIdList = new ArrayList<>();
                for (String testId : testIds) {
                    DtoParamsFormula paramsFormula = paramsFormulas.stream().filter(p -> p.getFormula().equals(item.getFormula())
                            && p.getObjectId().equals(testId)).findFirst().orElse(null);
                    if (StringUtil.isNotNull(paramsFormula)) {
                        existFormulaTestIdList.add(testId);
                    }
                }
                if (StringUtil.isNotEmpty(existFormulaTestIdList)) {
                    List<DtoTest> existFormulaTestList = testService.findAll(existFormulaTestIdList);
                    List<String> existFormulaAnaItemNameList = existFormulaTestList.stream().map(DtoTest::getRedAnalyzeItemName).distinct().collect(Collectors.toList());
                    throw new BaseException("测试项目: " + String.join("、", existFormulaAnaItemNameList) + " 的公式已经存在");
                }

                for (String testId : testIds) {
                    DtoParamsFormula dtoParamsFormula = new DtoParamsFormula();
                    dtoParamsFormula.setObjectId(testId);
                    dtoParamsFormula.setFormula(item.getFormula());
                    dtoParamsFormula.setConfigDate(new Date());
                    dtoParamsFormula.setOrignFormula(item.getOrignFormula());
                    dtoParamsFormula.setOrignFormulatType(item.getOrignFormulatType());
                    dtoParamsFormula.setObjectType(EnumLIM.EnumParamsFormulaObjectType.测试公式.getValue());
                    dtoParamsFormula.setSampleTypeId(sampleTypeId);
                    paramsFormulaAll.add(dtoParamsFormula);
                    List<DtoParamsPartFormula> paramsPartFormulas = new ArrayList<>();
                    paramsPartFormulas.addAll(item.getParamsPartFormulas());
                    paramsPartFormulas.addAll(item.getParamsPartFormulasBOD5());
                    paramsPartFormulas.addAll(item.getParamsPartFormulasCL());
                    paramsPartFormulas.addAll(item.getParamsPartFormulasConversion());
                    paramsPartFormulas.addAll(item.getParamsPartFormulasJB());
                    paramsPartFormulas.addAll(item.getParamsPartFormulasRevision());
                    for (DtoParamsPartFormula paramsPartFormula : paramsPartFormulas) {
                        DtoParamsPartFormula newParamsPartFormula = new DtoParamsPartFormula();
                        newParamsPartFormula.setFormulaId(dtoParamsFormula.getId());
                        newParamsPartFormula.setId(UUIDHelper.NewID());
                        newParamsPartFormula.setFormula(paramsPartFormula.getFormula());
                        newParamsPartFormula.setMostDecimal(paramsPartFormula.getMostDecimal());
                        newParamsPartFormula.setMostSignificance(paramsPartFormula.getMostSignificance());
                        newParamsPartFormula.setOrderNum(paramsPartFormula.getOrderNum());
                        newParamsPartFormula.setFormulaType(paramsPartFormula.getFormulaType());
                        newParamsPartFormula.setParamsName(paramsPartFormula.getParamsName());
                        newParamsPartFormula.setDetectionLimit(paramsPartFormula.getDetectionLimit());
                        newParamsPartFormula.setCalculationMode(paramsPartFormula.getCalculationMode());
                        newParamsPartFormula.setUseTestLimit(paramsPartFormula.getUseTestLimit());
                        paramsPartFormulasAll.add(newParamsPartFormula);
                    }
                    List<DtoParamsTestFormula> paramsTestFormulas = item.getParamsTestFormulas();
                    for (DtoParamsTestFormula paramsTestFormula : paramsTestFormulas) {
                        DtoParamsTestFormula dtoParamsTestFormula = new DtoParamsTestFormula();
                        dtoParamsTestFormula.setObjId(dtoParamsFormula.getId());
                        dtoParamsTestFormula.setId(UUIDHelper.NewID());
                        dtoParamsTestFormula.setParamsId(paramsTestFormula.getParamsId());
                        dtoParamsTestFormula.setParamsName(paramsTestFormula.getParamsName());
                        dtoParamsTestFormula.setAlias(paramsTestFormula.getAlias());
                        dtoParamsTestFormula.setDefaultValue(paramsTestFormula.getDefaultValue());
                        dtoParamsTestFormula.setOrderNum(paramsTestFormula.getOrderNum());
                        dtoParamsTestFormula.setAliasInReport(paramsTestFormula.getAliasInReport());
                        dtoParamsTestFormula.setDimension(paramsTestFormula.getDimension());
                        dtoParamsTestFormula.setDimensionId(paramsTestFormula.getDimensionId());
                        dtoParamsTestFormula.setSourceType(paramsTestFormula.getSourceType());
                        dtoParamsTestFormula.setIsMust(paramsTestFormula.getIsMust());
                        dtoParamsTestFormula.setIsEditable(paramsTestFormula.getIsEditable());
                        dtoParamsTestFormula.setDetectionLimit(paramsTestFormula.getDetectionLimit());
                        dtoParamsTestFormula.setCalculationMode(paramsTestFormula.getCalculationMode());
                        paramsTestFormulasAll.add(dtoParamsTestFormula);
                    }
                }
                if (paramsFormulaAll.size() > 0) {
                    commonRepository.insertBatch(paramsFormulaAll);
                }
                if (paramsPartFormulasAll.size() > 0) {
                    commonRepository.insertBatch(paramsPartFormulasAll);
                }
                if (paramsTestFormulasAll.size() > 0) {
                    commonRepository.insertBatch(paramsTestFormulasAll);
                }
                String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_ParamsPartFormula.getValue());
                //将数据保存到redis
                for (DtoParamsFormula dtoParamsFormula : paramsFormulaAll) {
                    DtoTestFormula dtoTestFormula = new DtoTestFormula();
                    dtoTestFormula.setId(dtoParamsFormula.getId());
                    dtoTestFormula.setTestId(dtoParamsFormula.getObjectId());
                    List<DtoParamsPartFormula> paramsPartFormulas = paramsPartFormulasAll.stream()
                            .filter(p -> p.getFormulaId().equals(dtoParamsFormula.getId())).collect(Collectors.toList());
                    //将数据存储到redis中
                    if (paramsPartFormulas.size() > 0) {
                        redisTemplate.opsForHash().put(key, dtoParamsFormula.getId(), JsonStream.serialize(paramsPartFormulas));
                    } else {
                        //删除指定的数据源
                        redisTemplate.opsForHash().delete(key, dtoParamsFormula.getId());
                    }
                }
            }
        }
    }

    @Override
    public String getFindByPageEntityName() {
        return "DtoParamsFormula a,DtoTest b,DtoSampleType c";
    }

    @Override
    public EntityManager selectEntityManager() {
        return this.entityManager;
    }

    @Override
    public void loadTestInfo4TestFormula(DtoTestFormula formula) {
        DtoTest dtoTest = testService.findOne(formula.getTestId());
        if (StringUtil.isNotNull(dtoTest)) {
            formula.setRedAnalyzeItemName(dtoTest.getRedAnalyzeItemName());
            formula.setRedAnalyzeMethodName(dtoTest.getRedAnalyzeMethodName());
            formula.setRedCountryStandard(dtoTest.getRedCountryStandard());
        }
    }


    @Transactional
    @Override
    public DtoParamsPartFormula saveParamsPartFormula(DtoParamsPartFormula paramsPartFormula) {
        DtoParamsPartFormula item = commonRepository.merge(paramsPartFormula);
        String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_ParamsPartFormula.getValue());
        Object json = redisTemplate.opsForHash().get(key, paramsPartFormula.getFormulaId());
        if (StringUtils.isNotNullAndEmpty(json)) {
            try {
                TypeLiteral<List<DtoParamsPartFormula>> typeLiteral = new TypeLiteral<List<DtoParamsPartFormula>>() {
                };
                List<DtoParamsPartFormula> paramsPartFormulas = JsonIterator.deserialize((String) json, typeLiteral);
                paramsPartFormulas = paramsPartFormulas.stream().filter(p -> p.getFormulaType().equals(EnumLIM.EnumPartFormulaType.参数公式.getValue())
                        && item.getParamsName().equals(p.getParamsName())).collect(Collectors.toList());
                paramsPartFormulas.add(item);
                redisTemplate.opsForHash().put(key, paramsPartFormula.getFormulaId(), JsonStream.serialize(paramsPartFormulas));
            } catch (Exception ex) {
                List<DtoParamsPartFormula> paramsPartFormulaList = paramsPartFormulaRepository.findByFormulaId(paramsPartFormula.getFormulaId());
                redisTemplate.opsForHash().put(key, paramsPartFormula.getFormulaId(), JsonStream.serialize(paramsPartFormulaList));
            }
        } else { //重新把数据保存redis中
            List<DtoParamsPartFormula> paramsPartFormulaList = paramsPartFormulaRepository.findByFormulaId(paramsPartFormula.getFormulaId());
            redisTemplate.opsForHash().put(key, paramsPartFormula.getFormulaId(), JsonStream.serialize(paramsPartFormulaList));
        }
        return item;
    }

    /**
     * 保存部分参数公式
     *
     * @param dtoTestFormula 公式数据
     * @param id             公式id
     */
    @Override
    @Transactional
    public void saveParamsPartFormula(DtoTestFormula dtoTestFormula, String id) {
        String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_ParamsPartFormula.getValue());
        List<DtoParamsPartFormula> paramsPartFormulaAll = new ArrayList<>();
        List<DtoParamsPartFormula> paramsPartFormulasRevision = dtoTestFormula.getParamsPartFormulasRevision();
        if (StringUtil.isNotNull(paramsPartFormulasRevision) && paramsPartFormulasRevision.size() > 0) {
            //paramsPartFormulaRepository.deleteAllByFormulaIdAndFormulaType(id, EnumLIM.EnumPartFormulaType.修约公式.getValue());
            for (DtoParamsPartFormula dtoParamsPartFormula : paramsPartFormulasRevision) {
                dtoParamsPartFormula.setFormulaId(id);
                if (!(StringUtil.isNotEmpty(dtoParamsPartFormula.getId()) && !dtoParamsPartFormula.getId().equals(UUIDHelper.GUID_EMPTY))) {
                    dtoParamsPartFormula.setId(UUIDHelper.NewID());
                }
                dtoParamsPartFormula.setFormulaType(EnumLIM.EnumPartFormulaType.修约公式.getValue());
                paramsPartFormulaAll.add(dtoParamsPartFormula);
            }
        }
        List<DtoParamsPartFormula> paramsPartFormulasBOD5 = dtoTestFormula.getParamsPartFormulasBOD5();
        if (StringUtil.isNotNull(paramsPartFormulasBOD5) && paramsPartFormulasBOD5.size() > 0) {
            //paramsPartFormulaRepository.deleteAllByFormulaIdAndFormulaType(id, EnumLIM.EnumPartFormulaType.BOD5公式.getValue());
            for (DtoParamsPartFormula dtoParamsPartFormula : paramsPartFormulasBOD5) {
                dtoParamsPartFormula.setFormulaId(id);
                if (!(StringUtil.isNotEmpty(dtoParamsPartFormula.getId()) && !dtoParamsPartFormula.getId().equals(UUIDHelper.GUID_EMPTY))) {
                    dtoParamsPartFormula.setId(UUIDHelper.NewID());
                }
                dtoParamsPartFormula.setFormulaType(EnumLIM.EnumPartFormulaType.BOD5公式.getValue());
                paramsPartFormulaAll.add(dtoParamsPartFormula);
            }
        }
        List<DtoParamsPartFormula> paramsPartFormulasJB = dtoTestFormula.getParamsPartFormulasJB();
        if (StringUtil.isNotNull(paramsPartFormulasJB) && paramsPartFormulasJB.size() > 0) {
            //paramsPartFormulaRepository.deleteAllByFormulaIdAndFormulaType(id, EnumLIM.EnumPartFormulaType.加标公式.getValue());
            for (DtoParamsPartFormula dtoParamsPartFormula : paramsPartFormulasJB) {
                dtoParamsPartFormula.setFormulaId(id);
                if (!(StringUtil.isNotEmpty(dtoParamsPartFormula.getId()) && !dtoParamsPartFormula.getId().equals(UUIDHelper.GUID_EMPTY))) {
                    dtoParamsPartFormula.setId(UUIDHelper.NewID());
                }
                dtoParamsPartFormula.setFormulaType(EnumLIM.EnumPartFormulaType.加标公式.getValue());
                paramsPartFormulaAll.add(dtoParamsPartFormula);
            }
        }
        List<DtoParamsPartFormula> paramsPartFormulasCL = dtoTestFormula.getParamsPartFormulasCL();
        if (StringUtil.isNotNull(paramsPartFormulasCL) && paramsPartFormulasCL.size() > 0) {
            //paramsPartFormulaRepository.deleteAllByFormulaIdAndFormulaType(id, EnumLIM.EnumPartFormulaType.串联公式.getValue());
            for (DtoParamsPartFormula dtoParamsPartFormula : paramsPartFormulasCL) {
                dtoParamsPartFormula.setFormulaId(id);
                if (!(StringUtil.isNotEmpty(dtoParamsPartFormula.getId()) && !dtoParamsPartFormula.getId().equals(UUIDHelper.GUID_EMPTY))) {
                    dtoParamsPartFormula.setId(UUIDHelper.NewID());
                }
                dtoParamsPartFormula.setFormulaType(EnumLIM.EnumPartFormulaType.串联公式.getValue());
                paramsPartFormulaAll.add(dtoParamsPartFormula);
            }
        }
        List<DtoParamsPartFormula> paramsPartFormulasConversion = dtoTestFormula.getParamsPartFormulasConversion();
        if (StringUtil.isNotNull(paramsPartFormulasConversion) && paramsPartFormulasConversion.size() > 0) {
            //paramsPartFormulaRepository.deleteAllByFormulaIdAndFormulaType(id, EnumLIM.EnumPartFormulaType.折算公式.getValue());
            for (DtoParamsPartFormula dtoParamsPartFormula : paramsPartFormulasConversion) {
                dtoParamsPartFormula.setFormulaId(id);
                if (!(StringUtil.isNotEmpty(dtoParamsPartFormula.getId()) && !dtoParamsPartFormula.getId().equals(UUIDHelper.GUID_EMPTY))) {
                    dtoParamsPartFormula.setId(UUIDHelper.NewID());
                }
                dtoParamsPartFormula.setFormulaType(EnumLIM.EnumPartFormulaType.折算公式.getValue());
                paramsPartFormulaAll.add(dtoParamsPartFormula);
            }
        }
        List<DtoParamsPartFormula> paramsPartFormulas = dtoTestFormula.getParamsPartFormulas();
        if (StringUtil.isNotNull(paramsPartFormulas) && paramsPartFormulas.size() > 0) {
            //paramsPartFormulaRepository.deleteAllByFormulaIdAndFormulaType(id, EnumLIM.EnumPartFormulaType.参数公式.getValue());
            for (DtoParamsPartFormula dtoParamsPartFormula : paramsPartFormulas) {
                dtoParamsPartFormula.setFormulaId(id);
                if (!(StringUtil.isNotEmpty(dtoParamsPartFormula.getId()) && !dtoParamsPartFormula.getId().equals(UUIDHelper.GUID_EMPTY))) {
                    dtoParamsPartFormula.setId(UUIDHelper.NewID());
                }
                dtoParamsPartFormula.setFormulaType(EnumLIM.EnumPartFormulaType.参数公式.getValue());
                paramsPartFormulaAll.add(dtoParamsPartFormula);
            }
        }
        //将数据存储到redis中
        if (paramsPartFormulaAll.size() > 0) {
            List<DtoParamsPartFormula> paramsPartFormulaList = paramsPartFormulaRepository.saveWithNull(paramsPartFormulaAll);
            redisTemplate.opsForHash().put(key, id, JsonStream.serialize(paramsPartFormulaList));
        } else {
            redisTemplate.opsForHash().delete(key, id); //删除指定的数据源
        }
    }

    /**
     * 公式导出
     *
     * @param pb       分页条件
     * @param criteria 筛选条件
     * @param response 响应体
     */
    @Override
    public void export(PageBean<DtoTestFormula> pb, BaseCriteria criteria, HttpServletResponse response) {
        // 获取条件下的所有公式数据
        pb.setPageNo(1);
        pb.setRowsPerPage(Integer.MAX_VALUE);
        TestFormulaCriteria testFormulaCriteria = (TestFormulaCriteria) criteria;
        findByPage(pb, testFormulaCriteria);
        List<DtoTestFormula> testFormulas = pb.getData();
        List<DtoImportTestFormulaForUpdate> importTestFormulaForUpdates = new ArrayList<>();
        List<DtoImportTestFormulaParams> importTestFormulaParams = new ArrayList<>();
        List<DtoImportTestFormulaRevise> importTestFormulaRevises = new ArrayList<>();
        if (StringUtil.isNotEmpty(testFormulas)) {
            List<String> testIds = testFormulas.stream().map(DtoTestFormula::getTestId).collect(Collectors.toList());
            // 测试项目
            List<DtoTest> dtoTests = testService.findAll(testIds);
            // 计算方式枚举
            Map<Integer, String> enumModeMap = new HashMap<>();
            for (EnumLIM.EnumCalculationMode value : EnumLIM.EnumCalculationMode.values()) {
                enumModeMap.put(value.getValue(), value.name());
            }
            // 公式参数
            List<String> formulaIds = testFormulas.stream().map(DtoTestFormula::getId).collect(Collectors.toList());
            List<DtoParamsTestFormula> paramsTestFormulas = paramsTestFormulaRepository.findByObjIdIn(formulaIds);
            Map<String, List<DtoParamsTestFormula>> testFormulaToMap = paramsTestFormulas.stream().collect(Collectors.groupingBy(DtoParamsTestFormula::getObjId));

            for (DtoTestFormula testFormula : testFormulas) {
                Optional<DtoTest> dtoTest = dtoTests.stream().filter(p -> p.getId().equals(testFormula.getTestId())).findFirst();

                // 公式数据赋值
                formulaAssignment(testFormula, dtoTest, importTestFormulaForUpdates, enumModeMap, paramsTestFormulas);
                if (testFormulaCriteria.getIsExportExtend()) {
                    // 参数公式
                    List<DtoParamsPartFormula> csFormulas = testFormula.getParamsPartFormulas();
                    // 参数拓展赋值
                    paramsExportAssignment(testFormula, dtoTest, testFormulaToMap, csFormulas, importTestFormulaParams);
                }
                // 修约公式赋值
                xyFormulaExportAssignment(testFormula, dtoTest, importTestFormulaRevises);
            }
        }
        Map<String, List<?>> sheetDataMap = new LinkedHashMap<>();
        Map<String, Class<?>> sheetClassMap = new HashMap<>();
        sheetDataMap.put("公式配置", importTestFormulaForUpdates);
        sheetDataMap.put("参数扩展配置", importTestFormulaParams);
        sheetDataMap.put("修约公式", importTestFormulaRevises);
        sheetClassMap.put("公式配置", DtoImportTestFormulaForUpdate.class);
        sheetClassMap.put("参数扩展配置", DtoImportTestFormulaParams.class);
        sheetClassMap.put("修约公式", DtoImportTestFormulaRevise.class);

        Workbook workBook = importUtils.getWorkBook(sheetDataMap, sheetClassMap);
        // 设置下拉框信息
        downLoadFormulaTemplateService.processDropList(workBook, 1, 4, 11, 0);
        downLoadFormulaTemplateService.processDropList(workBook, 1, 4, 0, 1);
        downLoadFormulaTemplateService.processDropList(workBook, 1, 4, 0, 2);
        PoiExcelUtils.downLoadExcel("公式配置", response, workBook);
    }


    @Override
    @Transactional
    public void removeFormulaByType(String id, Integer formulaType) {
        paramsPartFormulaRepository.deleteAllByFormulaIdAndFormulaType(id, formulaType);
    }

    /**
     * 处理测试项目方法状态
     *
     * @param testFormulas 测试项目公式集合
     */
    private void processMethodState(List<DtoTestFormula> testFormulas) {
        List<String> analyzeMethodIds = testFormulas.stream().map(DtoTestFormula::getAnalyzeMethodId).distinct().collect(Collectors.toList());
        List<DtoAnalyzeMethod> analyzeMethodList = StringUtil.isNotEmpty(analyzeMethodIds) ? analyzeMethodService.findAllDeleted(analyzeMethodIds) : new ArrayList<>();
        Map<String, DtoAnalyzeMethod> analyzeMethodMap = analyzeMethodList.stream().collect(Collectors.toMap(DtoAnalyzeMethod::getId, p -> p));
        for (DtoTestFormula testFormula : testFormulas) {
            DtoAnalyzeMethod analyzeMethod = analyzeMethodMap.get(testFormula.getAnalyzeMethodId());
            if (StringUtil.isNotNull(analyzeMethod)) {
                //是否停用状态标记
                testFormula.setIsDeactivate(EnumLIM.EnumAnalyzeMethodStatus.停用.getValue().equals(analyzeMethod.getStatus()));
            }
        }
    }

    /**
     * 得到是否有部分公式
     *
     * @param dtoTestFormula        原始公式
     * @param dtoParamsPartFormulas 部分公式
     * @return 返回组合的公式
     */
    private DtoTestFormula getDtoTestFormula(DtoTestFormula dtoTestFormula, List<DtoParamsPartFormula> dtoParamsPartFormulas) {
        Boolean isRevision = false;//是否修约
        Boolean isJB = false;//是否加标
        Boolean isBOD5 = false;//是否BOD5
        Boolean isCL = false;//是否串联
        Boolean isConversion = false;//是否折算
        if (StringUtil.isNotNull(dtoParamsPartFormulas) && dtoParamsPartFormulas.size() > 0) { //如果有一种公式的才判断，否则默认都没有配置
            List<DtoParamsPartFormula> revisionList = dtoParamsPartFormulas.stream().filter(p -> p.getFormulaType().equals(EnumLIM.EnumPartFormulaType.修约公式.getValue())).collect(Collectors.toList());
            if (revisionList.size() > 0) {
                isRevision = true;
            }
            dtoTestFormula.setParamsPartFormulasRevision(revisionList);
            List<DtoParamsPartFormula> jbList = dtoParamsPartFormulas.stream().filter(p -> p.getFormulaType().equals(EnumLIM.EnumPartFormulaType.加标公式.getValue())).collect(Collectors.toList());
            if (jbList.size() > 0) {
                isJB = true;
            }
            dtoTestFormula.setParamsPartFormulasJB(jbList);
            List<DtoParamsPartFormula> bod5List = dtoParamsPartFormulas.stream().filter(p -> p.getFormulaType().equals(EnumLIM.EnumPartFormulaType.BOD5公式.getValue())).collect(Collectors.toList());
            if (bod5List.size() > 0) {
                isBOD5 = true;
            }
            dtoTestFormula.setParamsPartFormulasBOD5(bod5List);
            List<DtoParamsPartFormula> clList = dtoParamsPartFormulas.stream().filter(p -> p.getFormulaType().equals(EnumLIM.EnumPartFormulaType.串联公式.getValue())).collect(Collectors.toList());
            if (clList.size() > 0) {
                isCL = true;
            }
            dtoTestFormula.setParamsPartFormulasCL(clList);

            List<DtoParamsPartFormula> conversionList = dtoParamsPartFormulas.stream().filter(p -> p.getFormulaType().equals(EnumLIM.EnumPartFormulaType.折算公式.getValue())).collect(Collectors.toList());
            if (conversionList.size() > 0) {
                isConversion = true;
            }
            dtoTestFormula.setParamsPartFormulasConversion(conversionList);

            //参数公式
            List<DtoParamsPartFormula> paramsPartFormulas = dtoParamsPartFormulas.stream().filter(p -> p.getFormulaType().equals(EnumLIM.EnumPartFormulaType.参数公式.getValue())).collect(Collectors.toList());
            dtoTestFormula.setParamsPartFormulas(paramsPartFormulas);
        }
        dtoTestFormula.setIsRevision(isRevision);
        dtoTestFormula.setIsBOD5(isBOD5);
        dtoTestFormula.setIsCL(isCL);
        dtoTestFormula.setIsJB(isJB);
        dtoTestFormula.setIsConversion(isConversion);
        return dtoTestFormula;
    }

    /**
     * 导出公式数据赋值
     *
     * @param testFormula                 测试项目公式
     * @param dtoTest                     测试项目
     * @param importTestFormulaForUpdates 导出公式实体
     * @param enumModeMap                 计算方式枚举
     * @param paramsTestFormulas          参数公式
     */
    private void formulaAssignment(DtoTestFormula testFormula, Optional<DtoTest> dtoTest, List<DtoImportTestFormulaForUpdate> importTestFormulaForUpdates,
                                   Map<Integer, String> enumModeMap, List<DtoParamsTestFormula> paramsTestFormulas) {
        DtoImportTestFormulaForUpdate formulaForUpdate = new DtoImportTestFormulaForUpdate();
        // 测得量公式--加标公式
        List<DtoParamsPartFormula> cdlFormulas = testFormula.getParamsPartFormulasJB();
        // 串联出证公式
        List<DtoParamsPartFormula> clFormulas = testFormula.getParamsPartFormulasCL();

        formulaForUpdate.setId(testFormula.getId());
        // 测试项目检测类型
        dtoTest.ifPresent(test -> {
            formulaForUpdate.setSampleType(test.getSampleTypeName());
            formulaForUpdate.setAnalyzeItem(test.getRedAnalyzeItemName());
            formulaForUpdate.setAnalyzeMethod(test.getRedAnalyzeMethodName());
        });

        formulaForUpdate.setSampleTypeForFormula(testFormula.getSampleTypeName());
        formulaForUpdate.setFormula(testFormula.getFormula());
        List<String> paramNames = paramsTestFormulas.stream().filter(p -> testFormula.getId().equals(p.getObjId())).map(DtoParamsTestFormula::getParamsName).collect(Collectors.toList());
        formulaForUpdate.setParams(StringUtil.isNotEmpty(paramNames) ? paramNames.stream().map(p -> "[" + p + "]").collect(Collectors.joining("、")) : "");
        // 测得量公式，加标公式
        Optional<DtoParamsPartFormula> cdlFormula = cdlFormulas.stream().filter(p -> testFormula.getId().equals(p.getFormulaId())).findFirst();
        cdlFormula.ifPresent(cdl -> {
            formulaForUpdate.setPartFormula(StringUtil.isNotNull(cdl.getFormula()) ? cdl.getFormula() : "");
            formulaForUpdate.setMostSignificance(StringUtil.isNotNull(cdl.getMostSignificance()) ? cdl.getMostSignificance() : -1);
            formulaForUpdate.setMostDecimal(StringUtil.isNotNull(cdl.getMostDecimal()) ? cdl.getMostDecimal() : -1);
            formulaForUpdate.setDetectionLimit(StringUtil.isNotNull(cdl.getDetectionLimit()) ? cdl.getDetectionLimit() : "");
            formulaForUpdate.setCalculationMode(StringUtil.isNotNull(cdl.getCalculationMode()) ? enumModeMap.get(cdl.getCalculationMode()) : "");
        });

        // 串联出证公式
        Optional<DtoParamsPartFormula> clFormula = clFormulas.stream().filter(p -> testFormula.getId().equals(p.getFormulaId())).findFirst();
        clFormula.ifPresent(cl -> {
            formulaForUpdate.setSeriesFormula(StringUtil.isNotNull(cl.getFormula()) ? cl.getFormula() : "");
        });
        importTestFormulaForUpdates.add(formulaForUpdate);
    }


    /**
     * 参数拓展数据赋值
     *
     * @param testFormula             测试项目公式
     * @param dtoTest                 测试项目
     * @param testFormulaToMap        测试项目公式参数
     * @param csFormulas              参数公式
     * @param importTestFormulaParams 单数扩展导出实体
     */
    private void paramsExportAssignment(DtoTestFormula testFormula, Optional<DtoTest> dtoTest, Map<String, List<DtoParamsTestFormula>> testFormulaToMap,
                                        List<DtoParamsPartFormula> csFormulas, List<DtoImportTestFormulaParams> importTestFormulaParams) {
        // 参数扩展配置页
        List<DtoParamsTestFormula> dtoParamsTestFormulas = testFormulaToMap.get(testFormula.getId());
        if (StringUtil.isNotEmpty(dtoParamsTestFormulas)) {
            dtoParamsTestFormulas = dtoParamsTestFormulas.stream().sorted(Comparator.comparing(DtoParamsTestFormula::getOrderNum).reversed()).collect(Collectors.toList());
            for (DtoParamsTestFormula paramsTestFormula : dtoParamsTestFormulas) {
                DtoImportTestFormulaParams testFormulaParams = new DtoImportTestFormulaParams();
                testFormulaParams.setId(testFormula.getId());
                dtoTest.ifPresent(test -> {
                    testFormulaParams.setSampleType(test.getSampleTypeName());
                    testFormulaParams.setAnalyzeItem(test.getRedAnalyzeItemName());
                    testFormulaParams.setAnalyzeMethod(test.getRedAnalyzeMethodName());
                });
                testFormulaParams.setSampleTypeForFormula(testFormula.getSampleTypeName());
                testFormulaParams.setFormula(testFormula.getFormula());
                testFormulaParams.setParamName("[" + paramsTestFormula.getParamsName() + "]");
                testFormulaParams.setDefaultValue(paramsTestFormula.getDefaultValue());
                testFormulaParams.setSortNo(paramsTestFormula.getOrderNum());
                testFormulaParams.setDimension(StringUtil.isNotNull(paramsTestFormula.getDimension()) ? paramsTestFormula.getDimension() : "");
                // 参数公式
                Optional<DtoParamsPartFormula> csFormula = csFormulas.stream().filter(p -> paramsTestFormula.getParamsName().equals(p.getParamsName()) && testFormula.getId().equals(p.getFormulaId())).findFirst();
                csFormula.ifPresent(cs -> {
                    testFormulaParams.setPartFormula(StringUtil.isNotNull(cs.getFormula()) ? cs.getFormula() : "");
                    testFormulaParams.setMostSignificance(StringUtil.isNotNull(cs.getMostSignificance()) ? cs.getMostSignificance() : -1);
                    testFormulaParams.setMostDecimal(StringUtil.isNotNull(cs.getMostDecimal()) ? cs.getMostDecimal() : -1);
                });
                importTestFormulaParams.add(testFormulaParams);
            }
        }
    }

    /**
     * 修约公式赋值
     *
     * @param testFormula              测试项目公式
     * @param dtoTest                  测试项目
     * @param importTestFormulaRevises 导出实体
     */
    private void xyFormulaExportAssignment(DtoTestFormula testFormula, Optional<DtoTest> dtoTest, List<DtoImportTestFormulaRevise> importTestFormulaRevises) {
        // 修约公式
        List<DtoParamsPartFormula> xyFormulas = testFormula.getParamsPartFormulasRevision();
        for (DtoParamsPartFormula xyFormula : xyFormulas) {
            DtoImportTestFormulaRevise testFormulaRevise = new DtoImportTestFormulaRevise();

            testFormulaRevise.setId(testFormula.getId());
            // 测试项目检测类型
            dtoTest.ifPresent(test -> {
                testFormulaRevise.setSampleType(test.getSampleTypeName());
                testFormulaRevise.setAnalyzeItem(test.getRedAnalyzeItemName());
                testFormulaRevise.setAnalyzeMethod(test.getRedAnalyzeMethodName());
            });

            testFormulaRevise.setSampleTypeForFormula(testFormula.getSampleTypeName());
            testFormulaRevise.setFormula(testFormula.getFormula());
            testFormulaRevise.setReviseFormula(StringUtil.isNotNull(xyFormula.getFormula()) ? xyFormula.getFormula() : "");
            testFormulaRevise.setReviseMostSignificance(StringUtil.isNotNull(xyFormula.getMostSignificance()) ? xyFormula.getMostSignificance() : -1);
            testFormulaRevise.setReviseMostDecimal(StringUtil.isNotNull(xyFormula.getMostDecimal()) ? xyFormula.getMostDecimal() : -1);
            importTestFormulaRevises.add(testFormulaRevise);
        }
    }


    @Autowired
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }

    @Autowired
    public void setDownLoadFormulaTemplateService(DownLoadFormulaTemplateService downLoadFormulaTemplateService) {
        this.downLoadFormulaTemplateService = downLoadFormulaTemplateService;
    }
}
