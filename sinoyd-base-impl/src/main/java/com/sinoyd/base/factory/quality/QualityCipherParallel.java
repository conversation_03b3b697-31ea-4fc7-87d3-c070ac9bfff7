package com.sinoyd.base.factory.quality;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.dto.customer.DtoQualityConfig;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.dto.vo.OrderReviseVO;
import com.sinoyd.base.factory.task.QualityControlKind;
import com.sinoyd.frame.service.CalculationService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class QualityCipherParallel extends QualityControlKind {

    /**
     * 平行样类别
     *
     * @return 平行样类别
     */
    @Override
    public Integer qcTypeValue() {
        return 2097152;
    }

    /**
     * 返回对应的质控名称
     *
     * @return 质控名称
     */
    @Override
    public String qcTypeName() {
        return "密码平行";
    }

    /**
     * 样品类别
     *
     * @param qcGrade 质控类型
     * @return 质控类别
     */
    @Override
    public String getSampleProperty(Integer qcGrade) {
        return "密码平行";
    }

    /**
     * 记录
     *
     * @param map 参数
     * @return 记录内容
     */
    @Override
    public String getFormt(Map<String, String> map) {
        return "增加了密码平行样qcCode，原样为oldCode。";
    }

    /**
     * 样品名称
     *
     * @param folderName 原样名称
     * @param qcGrade    质控类型
     * @return 样品名称
     */
    @Override
    public String getRedFolderName(String folderName, Integer qcGrade) {
        return "密码平行";
    }

    @Override
    public DtoQualityConfig getQualityConfig() {
        return getConfig("QualityCipherParallel");
    }

    @Override
    public DtoQualityConfig getQualityConfig(List<OrderReviseVO> orderReviseVOList) {
        return getConfig("QualityCipherParallel", orderReviseVOList);
    }

    @Override
    public Boolean deviationQualified(DtoQualityControlLimit controlLimit, String value) {
        CalculationService calculationService = SpringContextAware.getBean(CalculationService.class);
        return deviationQualified(controlLimit, value, calculationService);
    }
}
