package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.rcc.DtoAppConfig;
import com.sinoyd.lims.lim.repository.rcc.AppConfigRepository;
import com.sinoyd.lims.lim.service.AppConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * app应用配置实现类
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/1/12
 */
@Service
public class AppConfigServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoAppConfig, String, AppConfigRepository> implements AppConfigService {

    /**
     * 分页获取分析方法
     *
     * @param page     分页条件
     * @param criteria 查询条件
     */
    @Override
    public void findByPage(PageBean<DtoAppConfig> page, BaseCriteria criteria) {
        // 设置查询的实体类名及别名
        page.setEntityName("DtoAppConfig a");
        // 设置查询返回的字段
        page.setSelect("select a");
        super.findByPage(page, criteria);
    }

    @Override
    @Transactional
    public DtoAppConfig save(DtoAppConfig entity) {
        checkCodeOnly(entity);
        return super.save(entity);

    }

    @Override
    @Transactional
    public DtoAppConfig update(DtoAppConfig entity) {
        checkCodeOnly(entity);
        return super.save(entity);
    }

    /**
     * 根据id查找文件路径
     *
     * @param id 主键id
     * @return app应用配置Dto
     */
    @Override
    public DtoAppConfig findAttachment(String id) {
        return super.findOne(id);
    }

    /**
     * 查询应用配置
     * @return 配置内容
     */
    @Override
    public List<DtoAppConfig> findAllConfig(){
        return repository.findByStatusTrue();
    }

    /**
     * 检查code编码唯一性
     *
     * @param entity app应用配置dto
     */
    private void checkCodeOnly(DtoAppConfig entity) {
        Integer count = repository.countByCodeAndIdNot(entity.getCode(), entity.getId());
        if (count.compareTo(0) > 0) {
            throw new BaseException("应用编码值重复");
        }
    }
}