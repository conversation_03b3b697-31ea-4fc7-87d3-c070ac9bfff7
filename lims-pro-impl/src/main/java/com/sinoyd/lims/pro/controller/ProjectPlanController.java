package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.ProjectPlanService;
import com.sinoyd.lims.pro.criteria.ProjectPlanCriteria;
import com.sinoyd.lims.pro.dto.DtoProjectPlan;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * ProjectPlan服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: ProjectPlan服务")
 @RestController
 @RequestMapping("api/pro/projectPlan")
 public class ProjectPlanController extends BaseJpaController<DtoProjectPlan, String,ProjectPlanService> {


    /**
     * 分页动态条件查询ProjectPlan
     * @param projectPlanCriteria 条件参数
     * @return RestResponse<List<ProjectPlan>>
     */
     @ApiOperation(value = "分页动态条件查询ProjectPlan", notes = "分页动态条件查询ProjectPlan")
     @GetMapping
     public RestResponse<List<DtoProjectPlan>> findByPage(ProjectPlanCriteria projectPlanCriteria) {
         PageBean<DtoProjectPlan> pageBean = super.getPageBean();
         RestResponse<List<DtoProjectPlan>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, projectPlanCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询ProjectPlan
     * @param id 主键id
     * @return RestResponse<DtoProjectPlan>
     */
     @ApiOperation(value = "按主键查询ProjectPlan", notes = "按主键查询ProjectPlan")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoProjectPlan> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoProjectPlan> restResponse = new RestResponse<>();
         DtoProjectPlan projectPlan = service.findOne(id);
         restResponse.setData(projectPlan);
         restResponse.setRestStatus(StringUtil.isNull(projectPlan) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增ProjectPlan
     * @param projectPlan 实体列表
     * @return RestResponse<DtoProjectPlan>
     */
     @ApiOperation(value = "新增ProjectPlan", notes = "新增ProjectPlan")
     @PostMapping
     public RestResponse<DtoProjectPlan> create(@RequestBody @Validated DtoProjectPlan projectPlan) {
         RestResponse<DtoProjectPlan> restResponse = new RestResponse<>();
         restResponse.setData(service.save(projectPlan));
         return restResponse;
      }

     /**
     * 新增ProjectPlan
     * @param projectPlan 实体列表
     * @return RestResponse<DtoProjectPlan>
     */
     @ApiOperation(value = "修改ProjectPlan", notes = "修改ProjectPlan")
     @PutMapping
     public RestResponse<DtoProjectPlan> update(@RequestBody @Validated DtoProjectPlan projectPlan) {
         RestResponse<DtoProjectPlan> restResponse = new RestResponse<>();
         restResponse.setData(service.update(projectPlan));
         return restResponse;
      }

    /**
     * "根据id批量删除ProjectPlan
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除ProjectPlan", notes = "根据id批量删除ProjectPlan")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }