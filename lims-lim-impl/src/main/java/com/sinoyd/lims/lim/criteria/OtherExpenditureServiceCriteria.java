package com.sinoyd.lims.lim.criteria;

import java.util.Calendar;
import java.util.Date;

import com.sinoyd.base.core.BaseCodeHelper;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 非合同支出管理
 * <AUTHOR> 修改：徐肖波
 * @version v1.0.0 2019/3/2
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OtherExpenditureServiceCriteria extends BaseCriteria {

    /**
     * 操作开始时间
     */
    private String dtBegin;
    /**
     * 操作结束时间
     */
    private String dtEnd;
    /**
     * 关键字（项目编号、项目名称、单位、说明）
     */
    private String key;
    /**
     * 类型（-1所有 1差旅费、2油费、3话费）
     */
    private String type;
    /**
     * 操作（收/付款）人id
     */
    private String operatorId;
    /**
     * 所属部门id
     */
    private String deptId;
    /**
     * 支出种类（枚举1支出，2收入）
     */
    private Integer category;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(key)) {
            condition.append(" and (projectCode like :key or projectName like :key or entName like :key or explain like :key)");
            values.put("key", "%" + this.key + "%");
        }
        if (StringUtils.isNotNullAndEmpty(operatorId) && !UUIDHelper.GUID_EMPTY.equals(this.operatorId)) {
            condition.append(" and operatorId = :operatorId");
            values.put("operatorId", this.operatorId);
        }
        if (StringUtils.isNotNullAndEmpty(dtBegin)) {
            condition.append(" and operateDate >= :dtBegin");
            values.put("dtBegin", DateUtil.stringToDate(this.dtBegin, DateUtil.YEAR));
        }
        if (StringUtils.isNotNullAndEmpty(dtEnd)) {
            Date to = DateUtil.stringToDate(this.dtEnd, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and operateDate < :dtEnd");
            values.put("dtEnd", c.getTime());
        }
        if (StringUtils.isNotNullAndEmpty(deptId) && !UUIDHelper.GUID_EMPTY.equals(this.deptId)) {
            condition.append(" and deptId = :deptId");
            values.put("deptId", this.deptId);
        }
        if (StringUtils.isNotNullAndEmpty(type) && !UUIDHelper.GUID_EMPTY.equals(this.type)) {
            condition.append(" and paytype = :type");
            values.put("type", this.type);
        }
        if ( StringUtil.isNotNull(category) && category.intValue() != -1) {
            condition.append(" and category = :category");
            values.put("category", this.category);
        }
        return condition.toString();
    }
}