package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoTestPost2Test;

import java.util.Collection;
import java.util.List;

/**
 * 测试岗位测试项目配置仓储
 *
 * <AUTHOR>
 * @version V1.0.0 2022/4/21
 * @since V100R001
 */
public interface TestPost2TestRepository extends IBaseJpaPhysicalDeleteRepository<DtoTestPost2Test, String> {

    /**
     * 根据测试岗位id查找
     *
     * @param testPostId 测试岗位id
     * @return 测试岗位人员配置列表
     */
    List<DtoTestPost2Test> findByTestPostId(String testPostId);

    /**
     * 根据测试岗位id列表查找
     *
     * @param testPostIdList 测试岗位id列表
     * @return 测试岗位人员配置列表
     */
    List<DtoTestPost2Test> findByTestPostIdIn(List<String> testPostIdList);

    /**
     * 根据测试项目d列表查找
     *
     * @param testIdList 测试项目id列表
     * @return 测试岗位人员配置列表
     */
    List<DtoTestPost2Test> findByTestIdIn(Collection<String> testIdList);

    /**
     * 根据测试岗位id删除
     *
     * @param testPostIdList 测试岗位id列表
     * @return 删除的数量
     */
    int deleteByTestPostIdIn(List<String> testPostIdList);

    /**
     * 根据采样小组id和测试项目id删除数据
     *
     * @param testPostId  采样小组id
     * @param testIds     测试项目id
     */
    void deleteByTestPostIdAndTestIdIn(String testPostId, List<String> testIds);
}