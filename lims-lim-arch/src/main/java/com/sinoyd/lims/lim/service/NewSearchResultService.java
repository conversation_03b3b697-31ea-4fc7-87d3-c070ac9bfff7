package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoNewSearchResult;

import java.util.List;

/**
 * 查新结果接口
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
public interface NewSearchResultService extends IBaseJpaService<DtoNewSearchResult, String> {

    /**
     * 提交查新结果
     *
     * @param ids 主键ids
     * @return 结果集合
     */
    List<DtoNewSearchResult> submit(List<String> ids);

    /**
     * 退回结果
     *
     * @param ids 主键ids
     * @return 结果集合
     */
    List<DtoNewSearchResult> backResult(List<String> ids);

    /**
     * 查询附件路径
     *
     * @param id 主键id
     * @return 附件路径
     */
    DtoNewSearchResult findAttachPath(String id);
}
