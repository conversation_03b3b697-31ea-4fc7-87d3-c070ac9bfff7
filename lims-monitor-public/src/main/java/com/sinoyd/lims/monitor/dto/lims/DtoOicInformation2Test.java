package com.sinoyd.lims.monitor.dto.lims;

import com.sinoyd.lims.monitor.entity.OicInformation2Test;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoOicInformation2Test实体
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_MONITOR_OicInformation2Test")
 @Data
 @DynamicInsert
 public  class DtoOicInformation2Test extends OicInformation2Test {
   private static final long serialVersionUID = 1L;
 }