package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.OcrConfigRecord;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * DtoOcrConfigRecord
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "tb_lim_ocrConfigRecord")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoOcrConfigRecord extends OcrConfigRecord {
    /**
     * 识别数据
     */
    @Transient
    private List<DtoOcrConfigParamData> paramDataList;

    /**
     * 对象名称
     */
    @Transient
    private String configName;

    /**
     * 解析人名称
     */
    @Transient
    private String creatorName;
}
