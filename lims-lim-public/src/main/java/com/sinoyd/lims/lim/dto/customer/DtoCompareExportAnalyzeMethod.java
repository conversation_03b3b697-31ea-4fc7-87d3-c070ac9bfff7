package com.sinoyd.lims.lim.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

import java.util.Date;

/**
 * 分析方法比较相似度导出实体
 *
 * <AUTHOR>
 * @date V1.0.0 2024/01/03
 * @version: V100R001
 */
@Data
public class DtoCompareExportAnalyzeMethod {

    /**
     * 主键id
     */
    @Excel(name = "主键id", orderNum = "10",width = 40)
    private String id = UUIDHelper.NewID();

    /**
     * 方法名称
     */
    @Excel(name = "方法名称", orderNum = "20",width = 55)
    private String methodName;

    /**
     * 标准编号
     */
    @Excel(name = "标准编号", orderNum = "30",width = 20)
    private String countryStandard;


    /**
     * 标准实施日期
     */
    @Excel(name = "标准实施日期", orderNum = "50",width = 20)
    private Date effectiveDate;

    /**
     * 受控编号
     */
    @Excel(name = "受控编号", orderNum = "60",width = 20)
    private String methodCode;
}
