package com.sinoyd.lims.lim.service;

import com.sinoyd.lims.lim.dto.rcc.DtoVersionInfo;

/**
 * 登陆页二维码操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/19
 * @since V100R001
 */
public interface LoginQRCodeService {

    /**
     * 获取最新的版本信息
     *
     * @return 信息
     */
    String findNewVersionInfo();

    /**
     * 处理移动端下载路径
     *
     * @param versionInfo 版本信息
     */
    void processUrl(DtoVersionInfo versionInfo);

    /**
     * 登录验证码配置
     *
     * @return 登录验证码配置
     */
    String getCaptchaConfig();
}
