
delete from TB_LIM_ReportConfig where id = '7146c6a6-6e6c-4494-b16c-e587dba931a4';

-- 添加报表模板配置(测试项目导出)
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl)
VALUES ('7146c6a6-6e6c-4494-b16c-e587dba931a4', 1, 'TestExport', '测试项目导出_模板.xls', 'LIMReportForms/测试项目导出_模板.xls', 'output/LIMReportForms/测试项目导出.xls', 'application/excel', 'com.sinoyd.lims.lim.service.TestExportService', '{"sort":"sampleTypeId+,orderNum-"}', '', 0, 1, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-12-05 10:17:38', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-12-05 17:00:40', 'com.sinoyd.lims.lim.criteria.TestCriteria', 'LIMReportForms', 'export/TestExport');

delete from TB_LIM_ReportApply where id = '1fef623e-4e1b-471c-906a-d5870d3d778c';

-- 添加报表配置应用场景(测试项目导出)
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location)
VALUES ('1fef623e-4e1b-471c-906a-d5870d3d778c', '7146c6a6-6e6c-4494-b16c-e587dba931a4', 'TestManage', '测试项目管理', 'TestExport', '导出', 0, 0, 1, '', '');