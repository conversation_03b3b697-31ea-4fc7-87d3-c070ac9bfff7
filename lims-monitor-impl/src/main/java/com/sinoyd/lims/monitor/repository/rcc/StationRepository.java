package com.sinoyd.lims.monitor.repository.rcc;

import com.sinoyd.lims.monitor.dto.rcc.DtoStation;
import com.sinoyd.frame.repository.IBaseJpaRepository;


/**
 * Station数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
public interface StationRepository extends IBaseJpaRepository<DtoStation, String> {

    /**
     *  根据测站代码判断是否重复
     * @param stcode
     * @return 实体
     */
    Integer countByStcode(String stcode);

    /**
     * 通过stcode查询对象
     * @param stcode
     * @return 实体
     */
    DtoStation findByStcode(String stcode);


}