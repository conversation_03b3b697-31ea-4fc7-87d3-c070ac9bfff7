-- ----------------------------------------------
-- -------- 归属于LIMS部分的表相关DDL脚本 -----------
-- -------------- Quartz服务相关表 ----------------
-- ----------------------------------------------

create table qrtz_calendars
(
    SCHED_NAME    varchar(120) not null comment '计划名称',
    CALENDAR_NAME varchar(200) not null comment '触发器名称',
    CALENDAR      blob         not null,
    primary key (SCHED_NAME, CALENDAR_NAME)
) comment '以 Blob 类型存储 Quartz 的 Calendar 信息';

create table qrtz_fired_triggers
(
    SCHED_NAME        varchar(120) not null comment '计划名称',
    ENTRY_ID          varchar(95)  not null comment '组标识',
    TRIGGER_NAME      varchar(200) not null comment '触发器名称',
    TRIGGER_GROUP     varchar(200) not null comment '触发器组',
    INSTANCE_NAME     varchar(200) not null comment '当前实例的名称',
    FIRED_TIME        bigint(13)   not null comment '当前执行时间',
    SCHED_TIME        bigint(13)   not null comment '计划时间',
    PRIORITY          int          not null comment '权重',
    STATE             varchar(16)  not null comment '状态',
    JOB_NAME          varchar(200) null comment '作业名称',
    JOB_GROUP         varchar(200) null comment '作业组',
    IS_NONCONCURRENT  varchar(1)   null comment '是否并行',
    REQUESTS_RECOVERY varchar(1)   null comment '是否要求唤醒',
    primary key (SCHED_NAME, ENTRY_ID)
) comment '存储与已触发的 Trigger 相关的状态信息，以及相联 Job的执行信息';

create index IDX_QRTZ_FT_INST_JOB_REQ_RCVRY
    on qrtz_fired_triggers (SCHED_NAME, INSTANCE_NAME, REQUESTS_RECOVERY);

create index IDX_QRTZ_FT_JG
    on qrtz_fired_triggers (SCHED_NAME, JOB_GROUP);

create index IDX_QRTZ_FT_J_G
    on qrtz_fired_triggers (SCHED_NAME, JOB_NAME, JOB_GROUP);

create index IDX_QRTZ_FT_TG
    on qrtz_fired_triggers (SCHED_NAME, TRIGGER_GROUP);

create index IDX_QRTZ_FT_TRIG_INST_NAME
    on qrtz_fired_triggers (SCHED_NAME, INSTANCE_NAME);

create index IDX_QRTZ_FT_T_G
    on qrtz_fired_triggers (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP);

create table qrtz_job_details
(
    SCHED_NAME        varchar(120) not null comment '计划名称',
    JOB_NAME          varchar(200) not null comment '作业名称',
    JOB_GROUP         varchar(200) not null comment '作业组',
    DESCRIPTION       varchar(250) null comment '描述',
    JOB_CLASS_NAME    varchar(250) not null comment '作业程序集名称',
    IS_DURABLE        varchar(1)   not null comment '是否持久',
    IS_NONCONCURRENT  varchar(1)   not null comment '是否并行',
    IS_UPDATE_DATA    varchar(1)   not null comment '是否更新',
    REQUESTS_RECOVERY varchar(1)   not null comment '是否要求唤醒',
    JOB_DATA          blob         null,
    primary key (SCHED_NAME, JOB_NAME, JOB_GROUP)
) comment '自定义触发器';

create table qrtz_locks
(
    SCHED_NAME varchar(120) not null comment '计划名称',
    LOCK_NAME  varchar(40)  not null comment '锁名称',
    primary key (SCHED_NAME, LOCK_NAME)
) comment '存储程序的悲观锁的信息(假如使用了悲观锁)';

create table qrtz_paused_trigger_grps
(
    SCHED_NAME    varchar(120) not null comment '计划名称',
    TRIGGER_GROUP varchar(200) not null comment '触发器组',
    primary key (SCHED_NAME, TRIGGER_GROUP)
) comment '存储已暂停的 Trigger组的信息';

create table qrtz_scheduler_state
(
    SCHED_NAME        varchar(120) not null comment '计划名称',
    INSTANCE_NAME     varchar(200) not null comment '实例名称',
    LAST_CHECKIN_TIME bigint(13)   not null comment '最后的检查时间',
    CHECKIN_INTERVAL  bigint(13)   not null comment '检查间隔',
    primary key (SCHED_NAME, INSTANCE_NAME)
) comment '存储少量的有关 Scheduler 的状态信息，和别的Scheduler实例(假如是用于一个集群中)';

create table qrtz_triggers
(
    SCHED_NAME     varchar(120) not null comment '计划名称',
    TRIGGER_NAME   varchar(200) not null comment '触发器名称',
    TRIGGER_GROUP  varchar(200) not null comment '触发器组',
    JOB_NAME       varchar(200) not null comment '作业名称',
    JOB_GROUP      varchar(200) not null comment '作业组',
    DESCRIPTION    varchar(250) null comment '描述',
    NEXT_FIRE_TIME bigint(13)   null comment '下次执行时间',
    PREV_FIRE_TIME bigint(13)   null comment '前一次执行时间',
    PRIORITY       int          null comment '优先权',
    TRIGGER_STATE  varchar(16)  not null comment '触发器状态',
    TRIGGER_TYPE   varchar(8)   not null comment '触发器类型',
    START_TIME     bigint(13)   not null comment '开始时间',
    END_TIME       bigint(13)   null comment '结束时间',
    CALENDAR_NAME  varchar(200) null comment '日历名称',
    MISFIRE_INSTR  smallint(2)  null comment '失败次数',
    JOB_DATA       blob         null comment '作业数据',
    primary key (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP),
    constraint qrtz_triggers_ibfk_1
        foreign key (SCHED_NAME, JOB_NAME, JOB_GROUP) references qrtz_job_details (SCHED_NAME, JOB_NAME, JOB_GROUP)
) comment '触发器的基本信息';

create table qrtz_blob_triggers
(
    SCHED_NAME    varchar(120) not null comment '计划名称',
    TRIGGER_NAME  varchar(200) not null comment '触发器名称',
    TRIGGER_GROUP varchar(200) not null comment '触发器组',
    BLOB_DATA     blob         null comment '保存triggers 一些信息',
    primary key (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP),
    constraint qrtz_blob_triggers_ibfk_1
        foreign key (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP) references qrtz_triggers (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP)
)
    comment '自定义触发器';

create index SCHED_NAME
    on qrtz_blob_triggers (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP);

create table qrtz_cron_triggers
(
    SCHED_NAME      varchar(120) not null comment '计划名称',
    TRIGGER_NAME    varchar(200) not null comment '触发器名称',
    TRIGGER_GROUP   varchar(200) not null comment '触发器组',
    CRON_EXPRESSION varchar(120) not null comment '时间表达式',
    TIME_ZONE_ID    varchar(80)  null comment '时区ID',
    primary key (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP),
    constraint qrtz_cron_triggers_ibfk_1
        foreign key (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP) references qrtz_triggers (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP)
) comment '存储 Cron Trigger，包括Cron表达式和时区信息';

create table qrtz_simple_triggers
(
    SCHED_NAME      varchar(120) not null comment '计划名称',
    TRIGGER_NAME    varchar(200) not null comment '触发器名称',
    TRIGGER_GROUP   varchar(200) not null comment '触发器组',
    REPEAT_COUNT    bigint(7)    not null comment '重复次数',
    REPEAT_INTERVAL bigint(12)   not null comment '触发次数',
    TIMES_TRIGGERED bigint(10)   not null comment '重复间隔',
    primary key (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP),
    constraint qrtz_simple_triggers_ibfk_1
        foreign key (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP) references qrtz_triggers (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP)
) comment '存储简单的Trigger，包括重复次数，间隔，以及已触的次数';

create table qrtz_simprop_triggers
(
    SCHED_NAME    varchar(120)   not null comment '计划名称',
    TRIGGER_NAME  varchar(200)   not null comment '触发器名称',
    TRIGGER_GROUP varchar(200)   not null comment '触发器组',
    STR_PROP_1    varchar(512)   null comment '根据不同的trigger类型存放各自的参数',
    STR_PROP_2    varchar(512)   null comment '根据不同的trigger类型存放各自的参数',
    STR_PROP_3    varchar(512)   null comment '根据不同的trigger类型存放各自的参数',
    INT_PROP_1    int            null comment '根据不同的trigger类型存放各自的参数',
    INT_PROP_2    int            null comment '根据不同的trigger类型存放各自的参数',
    LONG_PROP_1   bigint         null comment '根据不同的trigger类型存放各自的参数',
    LONG_PROP_2   bigint         null comment '根据不同的trigger类型存放各自的参数',
    DEC_PROP_1    decimal(13, 4) null comment '根据不同的trigger类型存放各自的参数',
    DEC_PROP_2    decimal(13, 4) null comment '根据不同的trigger类型存放各自的参数',
    BOOL_PROP_1   varchar(1)     null comment '根据不同的trigger类型存放各自的参数',
    BOOL_PROP_2   varchar(1)     null comment '根据不同的trigger类型存放各自的参数',
    primary key (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP),
    constraint qrtz_simprop_triggers_ibfk_1
        foreign key (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP) references qrtz_triggers (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP)
) comment '存储CalendarIntervalTrigger和DailyTimeIntervalTrigger两种类型的触发器';

create index IDX_QRTZ_T_C
    on qrtz_triggers (SCHED_NAME, CALENDAR_NAME);

create index IDX_QRTZ_T_G
    on qrtz_triggers (SCHED_NAME, TRIGGER_GROUP);

create index IDX_QRTZ_T_J
    on qrtz_triggers (SCHED_NAME, JOB_NAME, JOB_GROUP);

create index IDX_QRTZ_T_JG
    on qrtz_triggers (SCHED_NAME, JOB_GROUP);

create index IDX_QRTZ_T_NEXT_FIRE_TIME
    on qrtz_triggers (SCHED_NAME, NEXT_FIRE_TIME);

create index IDX_QRTZ_T_NFT_MISFIRE
    on qrtz_triggers (SCHED_NAME, MISFIRE_INSTR, NEXT_FIRE_TIME);

create index IDX_QRTZ_T_NFT_ST
    on qrtz_triggers (SCHED_NAME, TRIGGER_STATE, NEXT_FIRE_TIME);

create index IDX_QRTZ_T_NFT_ST_MISFIRE
    on qrtz_triggers (SCHED_NAME, MISFIRE_INSTR, NEXT_FIRE_TIME, TRIGGER_STATE);

create index IDX_QRTZ_T_NFT_ST_MISFIRE_GRP
    on qrtz_triggers (SCHED_NAME, MISFIRE_INSTR, NEXT_FIRE_TIME, TRIGGER_GROUP, TRIGGER_STATE);

create index IDX_QRTZ_T_N_G_STATE
    on qrtz_triggers (SCHED_NAME, TRIGGER_GROUP, TRIGGER_STATE);

create index IDX_QRTZ_T_N_STATE
    on qrtz_triggers (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP, TRIGGER_STATE);

create index IDX_QRTZ_T_STATE
    on qrtz_triggers (SCHED_NAME, TRIGGER_STATE);

