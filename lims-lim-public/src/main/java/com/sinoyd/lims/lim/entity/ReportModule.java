package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;


/**
 * ReportModule实体
 *
 * <AUTHOR>
 * @version V1.0.0 2022/12/09
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "ReportModule")
@Data
public class ReportModule implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public ReportModule() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 组件编码
     */
    @Column(length = 100, nullable = false)
    @ApiModelProperty("组件编码")
    @Length(message = "组件编码{validation.message.length}", max = 100)
    private String moduleCode;

    /**
     * 组件名称
     */
    @Column(length = 100, nullable = false)
    @ApiModelProperty("组件名称")
    @Length(message = "组件名称{validation.message.length}", max = 100)
    private String moduleName;

    /**
     * 组件主表名称
     */
    @Column(length = 100, nullable = false)
    @ApiModelProperty("组件主表名称")
    @Length(message = "组件主表名称{validation.message.length}", max = 100)
    private String tableName;


    /**
     * 组件数据行表名称
     */
    @Column(length = 100)
    @ApiModelProperty("组件数据行表名称")
    @Length(message = "组件数据行表名称{validation.message.length}", max = 100)
    private String sourceTableName;

    /**
     * 每页样品数量
     */
    @Column
    @ApiModelProperty("每页样品数量")
    private Integer sampleCount;

    /**
     * 每页分析项目数量
     */
    @Column
    @ApiModelProperty("每页分析项目数量")
    private Integer testCount;

    /**
     * 子组件配置信息（适用于复合组件）
     */
    @Column(length = 500)
    @ApiModelProperty("子组件配置信息（适用于复合组件）")
    @Length(message = "子组件配置信息{validation.message.length}", max = 500)
    private String sonTableJson;

    /**
     * 是否复合组件
     */
    @ColumnDefault("0")
    @ApiModelProperty("是否复合组件")
    private Boolean isCompound = false;

    /**
     * 是否总称
     */
    @ColumnDefault("1")
    @ApiModelProperty("是否总称")
    private Boolean totalTest = true;

    /**
     * 是否辅助仪器
     */
    @ColumnDefault("1")
    @ApiModelProperty("是否辅助仪器")
    private Boolean auxiliaryInstrument = false;

    /**
     * 折算浓度计算方式：枚举 EnumConversionCalculationMode：0.按样品, 1.按批次
     */
    @Column
    @ColumnDefault("0")
    @ApiModelProperty("折算浓度计算方式（枚举 EnumConversionCalculationMode：0.按样品, 1.按批次）")
    private Integer conversionCalculationMode;

    /**
     * 排放速率计算方式：枚举 EnumSpeedCalculationMode：0.按样品, 1.按批次
     */
    @Column
    @ColumnDefault("0")
    @ApiModelProperty("排放速率计算方式（枚举 EnumSpeedCalculationMode：0.按样品, 1.按批次）")
    private Integer speedCalculationMode;

    /**
     * 化合物均值计算方式：枚举 EnumCompoundAvgCalculationMode：0.按样品, 1.按批次
     */
    @Column
    @ColumnDefault("0")
    @ApiModelProperty("化合物均值计算方式（枚举 EnumCompoundAvgCalculationMode：0.按样品, 1.按批次）")
    private Integer compoundAvgCalculationMode;

    /**
     * 烟气参数拆分方式：枚举 EnumGasParamSplitMode：0.按样品, 1.按批次
     */
    @Column
    @ColumnDefault("1")
    @ApiModelProperty("烟气参数拆分方式（枚举 EnumGasParamSplitMode：0.按样品, 1.按批次）")
    private Integer gasParamSplitMode;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;
}