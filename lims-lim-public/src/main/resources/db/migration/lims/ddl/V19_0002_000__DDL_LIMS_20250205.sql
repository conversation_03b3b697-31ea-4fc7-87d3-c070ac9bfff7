-- 人员培训计划
CREATE TABLE TB_QA_YearlyStaffTrainingPlan
(
    id             varchar(50)             primary key COMMENT '标识',
    planId         varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '年度计划标识',
    detailId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '计划标识',
    name           varchar(50)             COMMENT '培训名称',
    content        varchar(500)            COMMENT '培训内容',
    people         varchar(500)            COMMENT '参训人员',
    deptId         varchar(50)             COMMENT '责任部门',
    timeRequire    varchar(50)             COMMENT '时间要求/完成时间',
    method         varchar(50)             COMMENT '培训方式',
    target         varchar(50)             COMMENT '培训对象',
    personId       varchar(50)             COMMENT '责任人标识',
    completion     varchar(500)            COMMENT '完成情况',
    planType       int(10)                 NOT NULL DEFAULT 0 COMMENT '计划状态,0计划内/1计划外',
    status         int(10)                 NOT NULL DEFAULT 0 COMMENT '执行状态,0执行中/1完成',
    executeType    int(10)                 NOT NULL DEFAULT 0 COMMENT '计划类型,0计划/1执行',
    isDeleted      bit(1)                  NOT NULL DEFAULT 0 COMMENT '是否删除',
    orgId          varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator        varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate     datetime(0)             NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate     datetime(0)             NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间'
) COMMENT '人员培训计划';
-- 仪器期间核查计划
CREATE TABLE TB_QA_YearlyInstrumentCheckPlan
(
    id             varchar(50)             primary key COMMENT '标识',
    planId         varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '年度计划标识',
    detailId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '计划标识',
    instrumentId   varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '仪器标识',
    planCheckTime  datetime(0)             COMMENT '计划核查时间/核查日期',
    content        varchar(500)            COMMENT '核查内容',
    deptId         varchar(50)             COMMENT '责任部门',
    personId       varchar(50)             COMMENT '责任人标识/核查人员标识',
    method         varchar(255)            COMMENT '核查方法',
    isQualified    bit(1)                  NOT NULL DEFAULT 1 COMMENT '核查结果，true合格/false不合格',
    completion     varchar(500)            COMMENT '完成情况',
    planType       int(10)                 NOT NULL DEFAULT 0 COMMENT '计划状态,0计划内/1计划外',
    status         int(10)                 NOT NULL DEFAULT 0 COMMENT '执行状态,0执行中/1完成',
    executeType    int(10)                 NOT NULL DEFAULT 0 COMMENT '计划类型,0计划/1执行',
    isDeleted      bit(1)                  NOT NULL DEFAULT 0 COMMENT '是否删除',
    orgId          varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator        varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate     datetime(0)             NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate     datetime(0)             NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间'
) COMMENT '仪器期间核查计划';
-- 仪器检定校准计划
CREATE TABLE TB_QA_YearlyInstrumentConfirmPlan
(
    id             varchar(50)             primary key COMMENT '标识',
    planId         varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '年度计划标识',
    detailId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '计划标识',
    instrumentId   varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '仪器标识',
    checkTime      datetime(0)             COMMENT '检定校准日期',
    originType     int(10)                 NOT NULL DEFAULT 1 COMMENT '溯源方式(枚举：EnumOriginType:1检定、2校准、3自校)',
    frequency      int(10)                 COMMENT '周期',
    isQualified    bit(1)                  NOT NULL DEFAULT 1 COMMENT '检定结果，true合格/false不合格',
    enterpriseId   varchar(50)             COMMENT '检定单位',
    deptId         varchar(50)             COMMENT '责任部门',
    personId       varchar(50)             COMMENT '责任人标识',
    indicator      varchar(50)             COMMENT '技术指标',
    basis          varchar(500)            COMMENT '确认依据',
    completion     varchar(500)            COMMENT '完成情况',
    planType       int(10)                 NOT NULL DEFAULT 0 COMMENT '计划状态,0计划内/1计划外',
    status         int(10)                 NOT NULL DEFAULT 0 COMMENT '执行状态,0执行中/1完成',
    executeType    int(10)                 NOT NULL DEFAULT 0 COMMENT '计划类型,0计划/1执行',
    isDeleted      bit(1)                  NOT NULL DEFAULT 0 COMMENT '是否删除',
    orgId          varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator        varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate     datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate     datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间'
) COMMENT '仪器检定校准计划';
-- 标物期间核查计划
CREATE TABLE TB_QA_YearlyStandardCheckPlan
(
    id             varchar(50) primary key COMMENT '标识',
    planId         varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '年度计划标识',
    detailId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '计划标识',
    consumeId      varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '标准物质标识',
    planCheckTime  datetime(0)             COMMENT '计划核查时间/核查日期',
    content        varchar(500)            COMMENT '核查内容',
    deptId         varchar(50)             COMMENT '责任部门',
    personId       varchar(50)             COMMENT '责任人标识/核查人员标识',
    method         varchar(255)            COMMENT '核查方法',
    isQualified    bit(1)                  NOT NULL DEFAULT 1 COMMENT '核查结果，true合格/false不合格',
    completion     varchar(500)            COMMENT '完成情况',
    planType       int(10)                 NOT NULL DEFAULT 0 COMMENT '计划状态,0计划内/1计划外',
    status         int(10)                 NOT NULL DEFAULT 0 COMMENT '执行状态,0执行中/1完成',
    executeType    int(10)                 NOT NULL DEFAULT 0 COMMENT '计划类型,0计划/1执行',
    isDeleted      bit(1)                  NOT NULL DEFAULT 0 COMMENT '是否删除',
    orgId          varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator        varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate     datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate     datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间'
) COMMENT '标物期间核查计划';
