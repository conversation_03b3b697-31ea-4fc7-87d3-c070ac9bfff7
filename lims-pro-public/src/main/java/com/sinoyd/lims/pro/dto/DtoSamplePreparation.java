package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.SamplePreparation;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0.0 2023/03/17
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_SamplePreparation")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoSamplePreparation extends SamplePreparation {

    /**
     * 关联的样品id集合（批量制备时使用）
     */
    @Transient
    private List<String> sampleIds;

    /**
     * 仪器信息
     */
    @Transient
    private List<Map<String, String>> instrumentData;
}
