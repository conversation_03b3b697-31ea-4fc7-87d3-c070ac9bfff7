package com.sinoyd.lims.strategy.context;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.vo.FixedPointStatTestVO;

import java.util.List;

/**
 * 监测点位统计策略管理上下文
 *
 * <AUTHOR>
 * @version V5.2.0 2025/4/15
 * @since V100R001
 */
public interface FixedPointStatContext {

    /**
     * 监测点位统计
     *
     * @param criteria 查询条件
     * @param code     统计类型编码，枚举管理{@link com.sinoyd.lims.pro.enums.EnumPRO.EnumFixedPointStatType}
     * @param pb       分页参数
     * @return 统计结果
     */
    List<FixedPointStatTestVO> stat(BaseCriteria criteria, String code, PageBean<DtoAnalyseData> pb);
}
