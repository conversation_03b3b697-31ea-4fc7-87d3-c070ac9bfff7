package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 数据分包查询条件
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/02/13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OutSourceDataCriteria extends BaseCriteria implements Serializable {

    /**
     * 状态（0未完成 1完成）
     */
    private Integer state;

    /***
     * 项目关键字（项目名称、项目编号、委托单位）
     */
    private String projectKey;

    /**
     * 样品关键字（样品编号、点位名称）
     */
    private String sampleKey;

    /**
     * 分析项目名称
     */
    private String analyseItem;

    /**
     * 分析方法名称
     */
    private String analyzeMethod;

    private List<String> analyzeDataIds;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.sampleId = s.id ");
        condition.append(" AND (a.isOutsourcing = 1 or a.isSamplingOut = 1)");
        condition.append(" AND a.isDeleted = 0  ");
        condition.append(" AND s.isDeleted = 0 ");
        condition.append(" AND s.code IS NOT NULL ");
        condition.append(" AND s.code != '' ");
        condition.append(" AND s.sampleTypeId = t.id ");
        condition.append(" AND t.isDeleted = 0 ");
        if (state != null) {
            if (state.equals(0)) {
                condition.append(" and not exists(select 1 from DtoOutSourceData d where d.analyseDataId = a.id and d.state=1)");
            } else {
                condition.append(" and exists(select 1 from DtoOutSourceData d where d.analyseDataId = a.id and d.state=1)");
            }
        }
        if (StringUtil.isNotEmpty(projectKey)) {
            condition.append(" and exists(select 1 from DtoProject p where p.id = s.projectId and ( p.projectName like :projectKey or p.projectCode " +
                    "like :projectKey or p.customerName like :projectKey))");
            values.put("projectKey", "%" + projectKey + "%");
        }
        if (StringUtil.isNotEmpty(sampleKey)) {
            condition.append(" and (s.code like :sampleKey or s.redFolderName like :sampleKey)");
            values.put("sampleKey", "%" + sampleKey + "%");
        }
        if (StringUtil.isNotEmpty(analyseItem)) {
            condition.append(" and a.redAnalyzeItemName like :analyseItem");
            values.put("analyseItem", "%" + analyseItem + "%");
        }
        if (StringUtil.isNotEmpty(analyzeMethod)) {
            condition.append(" and (a.redAnalyzeMethodName like :analyzeMethod or exists(select 1 from DtoOutSourceData o " +
                    "where o.analyseDataId = a.id and o.analyzeMethodName like :analyzeMethod))");
            values.put("analyzeMethod", "%" + analyzeMethod + "%");
        }
        if (StringUtil.isNotEmpty(analyzeDataIds)) {
            condition.append(" and a.id in :analyzeDataIds");
            values.put("analyzeDataIds", analyzeDataIds);
        }
        return condition.toString();
    }
}
