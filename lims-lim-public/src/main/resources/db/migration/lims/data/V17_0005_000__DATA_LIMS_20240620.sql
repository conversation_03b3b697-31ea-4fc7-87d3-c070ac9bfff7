-- 有机单组分原始记录单报表模板配置
delete from TB_LIM_ReportConfig where reportCode = 'WorkSheetDZFDTDW';

INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName,
                                returnType, method, params, pageConfig, orderNum, bizType,
                                remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl,
                                isDefineFileName, defineFileName, beanName, versionNum,
                                controlNum, reportName, validate, usageNum)
VALUES ('050a49f8-bcbc-49f6-87e1-e6ce114aea8d', 1, 'WorkSheetDZFDTDW', 'SINOYD-LIMS-FX-74-01有机单组分（带替代物）_模板.xlsx',
        'WorkSheet/SINOYD-LIMS-FX-74-01有机单组分（带替代物）_模板.xlsx', 'output/WorkSheet/SINOYD-LIMS-FX-74-01有机单组分（带替代物）_模板.xlsx',
        'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '',
        '{\"originalRecordType\" : \"lineExtendBySampleOnly\", \"workSheetDataSourceType\" : \"YJDZFWorkSheetDataSourceImpl\"}',
        0, 2, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-18 15:43:48',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-20 08:41:34',
        'workSheetFolderId,type', 'WorkSheet', 'WorkSheetDZFDTDW', b'0', '', NULL, '', '', '', 1, 1);
