package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.InstrumentGatherDataCriteria;
import com.sinoyd.lims.lim.dto.customer.DtoInstrumentGatherDataVo;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentGatherData;
import com.sinoyd.lims.lim.service.InstrumentGatherDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 仪器接入数据接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/17
 * @since V100R001
 */
@Api(tags = "示例: 仪器接入数据接口定义")
@RestController
@RequestMapping("api/lim/instrumentGatherData")
@Validated
public class InstrumentGatherDataController extends BaseJpaController<DtoInstrumentGatherData, String, InstrumentGatherDataService> {

    /**
     * 分页动态条件查询InstrumentGatherData
     *
     * @param InstrumentGatherDataCriteria 条件参数
     * @return RestResponse<List < InstrumentGather>>
     */
    @ApiOperation(value = "分页动态条件查询InstrumentGatherData", notes = "分页动态条件查询InstrumentGatherData")
    @GetMapping
    public RestResponse<List<DtoInstrumentGatherData>> findByPage(InstrumentGatherDataCriteria InstrumentGatherDataCriteria) {
        PageBean<DtoInstrumentGatherData> pageBean = super.getPageBean();
        RestResponse<List<DtoInstrumentGatherData>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, InstrumentGatherDataCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 分页查询参结果数据
     *
     * @param InstrumentGatherDataCriteria 结果数据传输实体VO
     * @return 参数和实时数据
     */
    @ApiOperation(value = "分页查询参结果数据", notes = "分页查询参结果数据")
    @GetMapping("/resultData")
    public RestResponse<DtoInstrumentGatherDataVo> findResultDataPage(InstrumentGatherDataCriteria InstrumentGatherDataCriteria) {
        PageBean<DtoInstrumentGatherData> pageBean = super.getPageBean();
        RestResponse<DtoInstrumentGatherDataVo> restResponse = new RestResponse<>();
        DtoInstrumentGatherDataVo gatherDataVo = service.findResultDataPage(pageBean, InstrumentGatherDataCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(gatherDataVo);
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 查询实时数据
     *
     * @param instrumentGatherDataVo 参数传输实体VO
     * @return 参数和实时数据
     */
    @ApiOperation(value = "查询参数数据和实时数据", notes = "查询参数数据和实时数据")
    @GetMapping("/realTimeData")
    public RestResponse<Map<String, List<Map<String, Object>>>> findRealTimeData(DtoInstrumentGatherDataVo instrumentGatherDataVo) {
        RestResponse<Map<String, List<Map<String, Object>>>> restResp = new RestResponse<>();
        Map<String, List<Map<String, Object>>> instrumentGatherDataVos = service.findRealTimeData(instrumentGatherDataVo);
        restResp.setData(instrumentGatherDataVos);
        restResp.setRestStatus(StringUtil.isNull(instrumentGatherDataVos) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResp;
    }


    /**
     * 查询参数数据
     *
     * @param instrumentGatherDataVo 参数传输实体VO
     * @return 参数和实时数据
     */
    @ApiOperation(value = "查询参数数据", notes = "查询参数数据")
    @GetMapping("/paramData")
    public RestResponse<Map<String, List<Map<String, Object>>>> findParamData(DtoInstrumentGatherDataVo instrumentGatherDataVo) {
        RestResponse<Map<String, List<Map<String, Object>>>> restResp = new RestResponse<>();
        Map<String, List<Map<String, Object>>> instrumentGatherDataVos = service.findParamData(instrumentGatherDataVo);
        restResp.setData(instrumentGatherDataVos);
        restResp.setRestStatus(StringUtil.isNull(instrumentGatherDataVos) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResp;
    }


    /**
     * 数据刷新
     *
     * @param instrumentGatherDataVo 传输实体
     * @return 新增的实体
     */
    @ApiOperation(value = "数据刷新", notes = "数据刷新")
    @PostMapping("/refreshData")
    public RestResponse<Void> refreshData(@RequestBody DtoInstrumentGatherDataVo instrumentGatherDataVo) {
        RestResponse<Void> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.refreshData(instrumentGatherDataVo);
        return restResponse;
    }


    /**
     * 查询日志
     *
     * @param instrumentGatherDataCriteria 查询条件
     * @return 参数和实时数据
     */
    @ApiOperation(value = "查询日志", notes = "查询日志")
    @GetMapping("/logs")
    public RestResponse<List<DtoInstrumentGatherData>> findLogsPage(InstrumentGatherDataCriteria instrumentGatherDataCriteria) {
        PageBean<DtoInstrumentGatherData> pageBean = super.getPageBean();
        RestResponse<List<DtoInstrumentGatherData>> restResponse = new RestResponse<>();
        service.findLogsPage(pageBean, instrumentGatherDataCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }




    /**
     * excel导出结果数据
     *
     * @param instrumentGatherDataCriteria 查询条件
     * @param response                     响应数据
     */
    @ApiOperation(value = "excel导出结果数据", notes = "excel导出结果数据")
    @GetMapping("/resultData/export")
    public void exportResultData(InstrumentGatherDataCriteria instrumentGatherDataCriteria, HttpServletResponse response) {
        service.exportResultData(instrumentGatherDataCriteria, response);
    }

    /**
     * excel导出日志数据
     *
     * @param instrumentGatherDataCriteria 查询条件
     * @param response                     响应数据
     */
    @ApiOperation(value = "excel导出日志数据", notes = "excel导出日志数据")
    @GetMapping("/logs/export")
    public void exportLogs(InstrumentGatherDataCriteria instrumentGatherDataCriteria, HttpServletResponse response) {
        service.exportLogs(instrumentGatherDataCriteria, response);
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id查询", notes = "根据id查询")
    @GetMapping("/{id}")
    public RestResponse<DtoInstrumentGatherData> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoInstrumentGatherData> restResp = new RestResponse<>();
        DtoInstrumentGatherData entity = service.findOne(id);
        restResp.setData(entity);
        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }


    /**
     * 新增
     *
     * @param InstrumentGather 实体
     * @return 新增的实体
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("")
    public RestResponse<DtoInstrumentGatherData> create(@Validated @RequestBody DtoInstrumentGatherData InstrumentGather) {
        RestResponse<DtoInstrumentGatherData> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoInstrumentGatherData data = service.save(InstrumentGather);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 更新
     *
     * @param InstrumentGather 实体
     * @return 更新后的实体
     */
    @ApiOperation(value = "更新", notes = "更新")
    @PutMapping("")
    public RestResponse<DtoInstrumentGatherData> update(@Validated @RequestBody DtoInstrumentGatherData InstrumentGather) {
        RestResponse<DtoInstrumentGatherData> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoInstrumentGatherData data = service.update(InstrumentGather);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 单个假删
     *
     * @param id 参数id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除", notes = "根据id批量删除")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.logicDeleteById(id);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 批量假删
     *
     * @param ids 参数ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除", notes = "根据id批量删除")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResponse.setCount(count);

        return restResponse;
    }


}
