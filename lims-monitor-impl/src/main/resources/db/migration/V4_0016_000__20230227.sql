-- 修改消耗品清单报表模板配置调用方法
update TB_LIM_ReportConfig
set method = 'com.sinoyd.lims.report.service.exports.ConsumableExportService'
where reportCode = 'Consumable';

-- 修改消耗品/标准物质领用记录表报表模板配置调用方法
update TB_LIM_ReportConfig
set method = 'com.sinoyd.lims.report.service.exports.ConsumableLogExportService'
where reportCode = 'ConsumableLog';

-- 修改人员一览表报表模板配置调用方法
update TB_LIM_ReportConfig
set method = 'com.sinoyd.lims.report.service.exports.PersonExportService'
where reportCode = 'Person';

-- 修改仪器一览表报表模板配置调用方法
update TB_LIM_ReportConfig
set method = 'com.sinoyd.lims.report.service.exports.InstrumentExportService'
where reportCode = 'Instrument';

-- 修改仪器使用记录模板配置调用方法
update TB_LIM_ReportConfig
set method = 'com.sinoyd.lims.report.service.exports.InstrumentUseRecordExportService'
where reportCode = 'InstrumentUseRecord';

-- 修改项目详细数据/项目参数数据报表模板配置调用方法
update TB_LIM_ReportConfig
set method = 'com.sinoyd.lims.report.service.exports.DetailDataExportService'
where reportCode in ('DetailDataProject', 'DetailParamsDataProject');

-- 修改环境质量数据统计报表模板配置调用方法
update TB_LIM_ReportConfig
set method = 'com.sinoyd.lims.report.service.exports.EnvironmentStatisticsExportService'
where reportCode = 'EnvironmentStatistics';

-- 修改订单明细导出报表模板配置调用方法
update TB_LIM_ReportConfig
set method = 'com.sinoyd.lims.report.service.exports.OrderFormExportService'
where reportCode = 'OrderForm';

-- 修改仪器出入库导出报表模板配置调用方法
update TB_LIM_ReportConfig
set method = 'com.sinoyd.lims.report.service.exports.ProjectInstrumentQueryReportService'
where reportCode = 'ProjectInstrument';

-- 修改留样处置导出报表模板配置调用方法
update TB_LIM_ReportConfig
set method = 'com.sinoyd.lims.report.service.exports.SampleDisposeReportService'
where reportCode = 'SampleDispose';

-- 修改留样处置导出报表模板配置调用方法
update TB_LIM_ReportConfig
set method = 'com.sinoyd.lims.report.service.exports.TestExportService'
where reportCode = 'TestExport';

-- 添加评价标准导出报表
-- 报表模板配置
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName,
                                returnType, method, params, pageConfig, orderNum, bizType,
                                remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl,
                                isDefineFileName, defineFileName, beanName, versionNum,
                                controlNum)
VALUES ('80298b64-3579-48fe-aba4-54f966924ed6', 1, 'EvaluationCriteria', '评价标准信息_模板.xlsx',
        'LIMReportForms/评价标准信息_模板.xlsx', 'output/LIMReportForms/评价标准信息.xlsx', 'application/excel',
        'com.sinoyd.lims.report.service.exports.ExportEvaluationCriteriaService', '{\"sort\":\"name\"}', '', 0, 1, '',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-27 18:38:43',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-27 18:43:29',
        'com.sinoyd.lims.lim.criteria.EnvironmentalCriteria', 'LIMReportForms', 'export/EvaluationCriteria', b'0', '',
        '', '', '');
-- 场景配置
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate)
VALUES ('cc9c6d84-bde6-4cac-a523-d434e41a40c7', '80298b64-3579-48fe-aba4-54f966924ed6', 'EvaluationCriteria', '评价标准',
        'EvaluationCriteria', '导出', 0, 0, 1, '', '', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2023-02-27 18:54:29', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2023-02-27 18:54:29');
