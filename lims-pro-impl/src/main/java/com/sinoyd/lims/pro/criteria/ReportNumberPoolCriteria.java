package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * ReportNumberPool查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2023/8/1
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportNumberPoolCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    private String reportTypeId;

    private Integer year;

    private String code;

    private Integer status;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.reportTypeId)) {
            condition.append(" and a.reportTypeId = :reportTypeId");
            values.put("reportTypeId", this.reportTypeId);
        }

        if (StringUtil.isNotNull(this.year)) {
            condition.append(" and a.year = :year");
            values.put("year", this.year);
        }

        if (StringUtil.isNotNull(this.code)) {
            condition.append(" and a.code like :code");
            values.put("code", "%" + this.code + "%");
        }

        if (StringUtil.isNotNull(this.status)) {
            condition.append(" and a.status = :status");
            values.put("status", this.status);
        }

        return condition.toString();
    }
}
