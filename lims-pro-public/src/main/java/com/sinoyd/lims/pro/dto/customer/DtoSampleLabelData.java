package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.Date;

/**
 * 样品标签的实体数据
 *
 * <AUTHOR>
 * @version V1.0.0 2023/06/14
 * @since V100R001
 */
@Data
public class DtoSampleLabelData {

    /**
     * 样品编号
     */
    private String sampleCode;


    /**
     * 点位名称
     */
    private String redFolderName;


    /**
     * 采样时间
     */
    private Date samplingTimeBegin;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 数据id
     */
    private String anaId;

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 检测类型名称
     */
    private String sampleTypeName;

    /**
     * 检测类型大类id
     */
    private String bigSampleTypeId;

    /**
     * 分析项目名称
     */
    private String redAnalyzeItemName;

    /**
     * 是否分包
     */
    private Boolean isOutsourcing;

    /**
     * 前处理方式
     */
    private String pretreatmentMethod;

    /**
     * 采样体积
     */
    private String sampleVolume;

    /**
     * 不分组的标识
     */
    private String mark;

    /**
     * 分组的标识
     */
    private String groupMark;

    /**
     * 固定剂的信息
     */
    private String fixer;

    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 分组id
     */
    private String groupId;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 容器名称
     */
    private String containerName;

    /**
     * 分组备注
     */
    private String remark;

    /**
     * 受检单位
     */
    private String inspectedEnt;

    /**
     * 保存条件
     */
    private String storageConditions;

    /**
     * 样品状态
     */
    private String status;

    /**
     * 点位id
     */
    private String sampleFolderId;

    /**
     * 样品分组上的保存条件
     */
    private String saveCondition;

    /**
     * 采样人员id
     */
    private String samplingPersons;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 是否采样分包
     */
    private Boolean isSamplingOut;

    /**
     * 采样容器状态 EnumContainerStatus 1.完好无损 2.破损
     */
    private Integer containerStatus;

    /**
     *
     * 该构造函数用到 DSSampleLabelServiceImpl  findSampleLabelData 方法，双方要改需要同步修改
     *
     * @param sampleId           样品id
     * @param sampleCode         样品编号
     * @param redFolderName      点位名称
     * @param samplingTimeBegin  采样时间
     * @param testId             测试项目id
     * @param anaId              分析数据id
     * @param sampleTypeId       检测类型id
     * @param sampleTypeName     检测类型名称
     * @param bigSampleTypeId    检测类型大类id
     * @param redAnalyzeItemName 分析项目名称
     * @param isOutsourcing      是否分包
     * @param receiveId          送样单id
     * @param inspectedEnt       受检单位
     * @param storageConditions  保存条件
     * @param status             样品状态
     * @param sampleFolderId     点位id
     */
    public DtoSampleLabelData(String sampleId,
                              String sampleCode,
                              String redFolderName,
                              Date samplingTimeBegin,
                              String testId,
                              String anaId,
                              String sampleTypeId,
                              String sampleTypeName,
                              String bigSampleTypeId,
                              String redAnalyzeItemName,
                              Boolean isOutsourcing,
                              String receiveId,
                              String inspectedEnt,
                              String storageConditions,
                              String status,
                              String sampleFolderId,
                              Boolean isSamplingOut
    ) {
        this.setSampleId(sampleId);
        this.setSampleCode(sampleCode);
        this.setRedFolderName(redFolderName);
        this.setSamplingTimeBegin(samplingTimeBegin);
        this.setTestId(testId);
        this.setAnaId(anaId);
        this.setSampleTypeId(sampleTypeId);
        this.setSampleTypeName(sampleTypeName);
        this.setBigSampleTypeId(bigSampleTypeId);
        this.setRedAnalyzeItemName(redAnalyzeItemName);
        this.setIsOutsourcing(isOutsourcing);
        this.setReceiveId(receiveId);
        this.setInspectedEnt(inspectedEnt);
        this.setStorageConditions(storageConditions);
        this.setStatus(status);
        this.setSampleFolderId(sampleFolderId);
        this.setIsSamplingOut(isSamplingOut);
    }

    public DtoSampleLabelData(){}
}
