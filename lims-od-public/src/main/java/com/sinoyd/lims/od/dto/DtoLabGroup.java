package com.sinoyd.lims.od.dto;

import com.sinoyd.lims.od.entity.LabGroup;
import com.sinoyd.lims.od.entity.SampleResult;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * LabGroup实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/3
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Ods_LabGroup")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoLabGroup extends LabGroup {

    /**
     * 固定源结果数据
     */
    @Transient
    private DtoStationarySourceResult stationarySourceResult;

    /**
     * 环境空气结果
     */
    @Transient
    private DtoEnvGasResult envGasResult;
}
