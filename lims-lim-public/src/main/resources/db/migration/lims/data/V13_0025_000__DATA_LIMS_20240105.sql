-- 红外（非分散）分光光度法分析原始记录（水）新记录单模板配置
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName, validate, usageNum)
VALUES ('b4138920-8f84-4f77-96ba-a46d27e3d5e3', 1, 'WorkSheetHWFGGDFNew',
        'SINOYD-LIMS-FX-40-01 红外(非分散)分光光度法分析原始记录（水）新_模板.xls',
        'WorkSheet/SINOYD-LIMS-FX-40-01 红外(非分散)分光光度法分析原始记录（水）新_模板.xls',
        'output/WorkSheet/红外(非分散)分光光度法分析原始记录（水）新_模板.xlsx', 'application/excel',
        'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '',
        '{\"originalRecordType\" : \"lineExtendBySampleTest\",  \"workSheetDataSourceType\" : \"HWFGGDNewWorkSheetDataSourceImpl\",  \"qCGroupByTest\" : \"1\", \"qCAnaLmtCnt\" : \"1\" }',
        0, 2, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-01-04 10:53:41',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-01-04 17:10:57',
        'workSheetFolderId,type', 'WorkSheet', 'WorkSheetHWFGGDFNew', b'0', '', NULL, '', '', '', 0, NULL);