package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.rcc.DtoCost;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.repository.rcc.CostRepository;
import com.sinoyd.lims.lim.service.CostService;
import com.sinoyd.lims.lim.service.TestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 检测费操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/15
 * @since V100R001
 */
@Service
public class CostServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoCost, String, CostRepository> implements CostService {

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    private TestService testService;

    @Override
    public void findByPage(PageBean<DtoCost> pb, BaseCriteria costCriteria) {
        pb.setEntityName("DtoCost a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, costCriteria);
    }

    @Transactional
    @Override
    public List<DtoCost> save(Collection<DtoCost> entities) {
        List<DtoCost> list = new ArrayList<>();

        for (DtoCost entity : entities) {
            if (StringUtils.isNotNullAndEmpty(entity.getId())) { //主键id存在，表明用于更新
                DtoCost cost = super.update(entity);
                list.add(cost);
            } else {//主键id为空或null，表明用于新增，前端需将id置为空传参
                entity.setId(UUIDHelper.NewID());
                DtoCost cost = super.save(entity);
                list.add(cost);
            }
        }

        return list;
    }

    /**
     * 根据检测类型返回检测费配置
     *
     * @param sampleTypeId 检测类型id
     * @param analyzeItemKey          分析项目、拼音
     * @param analyzeMethodKey        分析方法、标准编号
     * @return 根据检测类型返回检测费配置
     */
    @Override
    public List<DtoCost> findBySampleType(String sampleTypeId, String analyzeItemKey,String analyzeMethodKey,Integer cert) {
        DtoSampleType samType = sampleTypeService.findOne(sampleTypeId);
        if (StringUtil.isNull(samType)) {
            throw new BaseException("仅检测类型的节点下可以配置检测费！");
        }

        if (samType.getCategory().equals(EnumBase.EnumSampleTypeCategory.检测类型大类.getValue())) {
            List<DtoTest> tests = this.getTestList(samType.getId(), analyzeItemKey,analyzeMethodKey,cert);
            return this.findByBigSampleType(sampleTypeId, tests);
        } else if (samType.getCategory().equals(EnumBase.EnumSampleTypeCategory.检测类型小类.getValue())) {
            List<DtoTest> tests = this.getTestList(samType.getParentId(), analyzeItemKey,analyzeMethodKey,cert);
            return this.findBySmallSampleType(sampleTypeId, samType.getParentId(), tests);
        }
        return new ArrayList<>();
    }

    /**
     * 根据测试项目id集合返回检测费配置
     *
     * @param testIds 测试项目id集合
     * @return 返回测试项目id集合的检测费配置
     */
    @Override
    public List<DtoCost> findByTestIdIn(Collection<String> testIds) {
        return repository.findByTestIdIn(testIds);
    }

    //#region 私有方法

    /**
     * 根据检测类型关键字返回测试项目
     *
     * @param sampleTypeId 检测类型id
     * @param analyzeItemKey           分析项目、拼音
     * @param analyzeMethodKey         分析方法、标准编号
     * @return 测试项目列表
     */
    private List<DtoTest> getTestList(String sampleTypeId, String analyzeItemKey,String analyzeMethodKey,Integer cert) {
        /*不调用测试项目的公共方法，因为不分页且关键字本身无法按照索引查询，所以该查询考虑模糊搜索也是按照检测类型的索引全部检索，不如直接将所有检测类型下的测试项目全部查出来后再处理。*/
        List<DtoTest> testList = testService.findBySampleTypeId(sampleTypeId);
        if (StringUtils.isNotNullAndEmpty(analyzeItemKey)) {
            testList = testList.stream().filter(p -> (StringUtil.isNotEmpty(p.getRedAnalyzeItemName()) && p.getRedAnalyzeItemName().contains(analyzeItemKey))
                    || (StringUtil.isNotEmpty(p.getPinYin()) && p.getPinYin().contains(analyzeItemKey))
                    || (StringUtil.isNotEmpty(p.getFullPinYin()) && p.getFullPinYin().contains(analyzeItemKey)))
                    .collect(Collectors.toList());
        }
        if(StringUtil.isNotEmpty(analyzeMethodKey)){
            testList = testList.stream().filter(p -> (StringUtil.isNotEmpty(p.getRedAnalyzeMethodName()) && p.getRedAnalyzeMethodName().contains(analyzeMethodKey))
                    || (StringUtil.isNotEmpty(p.getRedCountryStandard()) && p.getRedCountryStandard().contains(analyzeMethodKey)))
                    .collect(Collectors.toList());
        }
        // 检测资质过滤
        if(StringUtil.isNotNull(cert)){
            testList = testList.stream().filter(p -> p.getCert().equals(cert)).collect(Collectors.toList());
        }
        testList.sort(Comparator.comparing(DtoTest::getOrderNum).reversed());
        return testList;
    }

    /**
     * 根据检测小类返回检测费配置
     *
     * @param sampleTypeId    检测类型id
     * @param bigSampleTypeId 检测大类id
     * @param tests           测试项目列表
     * @return 检测费配置
     */
    private List<DtoCost> findBySmallSampleType(String sampleTypeId, String bigSampleTypeId, List<DtoTest> tests) {
        List<DtoCost> costs = repository.findBySampleTypeId(sampleTypeId);
        Map<String, DtoCost> costMap = costs.stream().collect(Collectors.toMap(DtoCost::getTestId, cost -> cost));
        List<DtoCost> bigCosts = repository.findBySampleTypeId(bigSampleTypeId);
        Map<String, DtoCost> bigCostMap = bigCosts.stream().collect(Collectors.toMap(DtoCost::getTestId, cost -> cost));
        return getCost(sampleTypeId, tests, costMap, bigCostMap);
    }

    /**
     * 根据检测大类返回检测费配置
     *
     * @param sampleTypeId 检测类型id
     * @param tests        测试项目列表
     * @return 检测费配置
     */
    private List<DtoCost> findByBigSampleType(String sampleTypeId, List<DtoTest> tests) {
        List<DtoCost> costs = repository.findBySampleTypeId(sampleTypeId);
        //将list转为map
        Map<String, DtoCost> costMap = costs.stream().collect(Collectors.toMap(DtoCost::getTestId, cost -> cost));
        return getCost(sampleTypeId, tests, costMap, null);
    }

    /**
     * 获取检测费配置
     *
     * @param sampleTypeId 检测类型id
     * @param tests        测试项目
     * @param costMap      检测费map
     * @param bigCostMap   大类的检测费map
     * @return 检测费配置
     */
    private List<DtoCost> getCost(String sampleTypeId, List<DtoTest> tests, Map<String, DtoCost> costMap, Map<String, DtoCost> bigCostMap) {
        List<DtoCost> list = new ArrayList<>();
        for (DtoTest test : tests) {
            DtoCost cost = new DtoCost();
            if (costMap.containsKey(test.getId())) {
                cost = costMap.get(test.getId());
            } else {
                cost.setId("");
                cost.setTestId(test.getId());
                cost.setRedAnalyzeItemName(test.getRedAnalyzeItemName());
                cost.setRedAnalyzeMethodName(test.getRedAnalyzeMethodName());
                cost.setRedCountryStandard(test.getRedCountryStandard());
                cost.setSampleTypeId(sampleTypeId);
            }

            if (StringUtil.isNotNull(bigCostMap) && bigCostMap.containsKey(test.getId())) {
                DtoCost bigCost = bigCostMap.get(test.getId());
                cost.setBigSamplingCost(bigCost.getSamplingCost());
                cost.setBigAnalyzeCost(bigCost.getAnalyzeCost());
            }
            cost.setCert(test.getCert());
            list.add(cost);
        }
        //BUG2024011999463 【重要】【2024-1-21】【马川江】【检测费用配置】测试项目列表，按分析方法、分析项目顺序排列
        list.sort(Comparator.comparing(DtoCost::getRedAnalyzeMethodName).thenComparing(DtoCost::getRedAnalyzeItemName));
        return list;
    }

    //#endregion

    @Autowired
    @Lazy
    public void setTestService(TestService testService) {
        this.testService = testService;
    }
}