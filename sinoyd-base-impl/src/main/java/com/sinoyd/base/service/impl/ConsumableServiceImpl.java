package com.sinoyd.base.service.impl;

import com.sinoyd.base.core.BaseCodeHelper;
import com.sinoyd.base.criteria.ConsumableCriteria;
import com.sinoyd.base.dto.customer.DtoStandardTemp;
import com.sinoyd.base.dto.lims.DtoConsumable;
import com.sinoyd.base.dto.lims.DtoConsumableDetail;
import com.sinoyd.base.dto.lims.DtoConsumableOfMixed;
import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.entity.ConsumableDetail;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.lims.ConsumableDetailRepository;
import com.sinoyd.base.repository.lims.ConsumableOfMixedRepository;
import com.sinoyd.base.repository.lims.ConsumableRepository;
import com.sinoyd.base.repository.rcc.AnalyzeItemRepository;
import com.sinoyd.base.service.ConsumableDetailService;
import com.sinoyd.base.service.ConsumableOfMixedService;
import com.sinoyd.base.service.ConsumableService;
import com.sinoyd.base.service.DimensionService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.DepartmentService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.PinYinUtil;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 消耗品管理
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/5
 * @since V100R001
 */
@Service
@Slf4j
public class ConsumableServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoConsumable, String, ConsumableRepository> implements ConsumableService {
    @Autowired
    protected ConsumableDetailRepository consumableDetailRepository;

    @Autowired
    protected ConsumableDetailService consumableDetailService;

    @Autowired
    private CodeService codeService;

    @Autowired
    private ConsumableOfMixedRepository consumableOfMixedRepository;

    @Autowired
    @Lazy
    private ConsumableOfMixedService consumableOfMixedService;

    @Autowired
    @Lazy
    protected DimensionService dimensionService;

    private CommonRepository commonRepository;

    private DepartmentService departmentService;

    @Autowired
    private AnalyzeItemRepository analyzeItemRepository;

    /**
     * 分页查询消耗品列表
     */
    @Override
    public void findByPage(PageBean<DtoConsumable> pageBean, BaseCriteria baseCriteria) {
        // 设置查询的实体类名及别名
        pageBean.setEntityName("DtoConsumable x");
        // 设置查询返回的字段、实体别名表示所有字段
        pageBean.setSelect("select x");
        int pageNo = pageBean.getPageNo();
        int rowsPerPage = pageBean.getRowsPerPage();

        ConsumableCriteria criteria = (ConsumableCriteria) baseCriteria;
        if (StringUtil.isNotEmpty(criteria.getDeptId()) && !UUIDHelper.GUID_EMPTY.equals(criteria.getDeptId())) {
            List<String> personIdList = commonRepository.find("select a.id from DtoPerson a where a.isDeleted = 0 and a.deptId = :deptId",
                    Collections.singletonMap("deptId", criteria.getDeptId()));
            personIdList = personIdList.stream().filter(p -> StringUtil.isNotEmpty(p) && !UUIDHelper.GUID_EMPTY.equals(p)).collect(Collectors.toList());
            criteria.setSendWarnUserIdList(personIdList);
        }
        String inventoryStatus = criteria.getInventoryStatus();
        String compoundId = criteria.getCompoundId();
        if ((StringUtil.isNotEmpty(inventoryStatus) && !"-1".equals(inventoryStatus))
                ||(Boolean.TRUE.equals(criteria.getIsStandard())&&StringUtil.isNotEmpty(compoundId)&&!UUIDHelper.GUID_EMPTY.equals(compoundId))) {
            pageBean.setPageNo(1);
            pageBean.setRowsPerPage(Integer.MAX_VALUE);
        }

        int alertDays = 0;
        DtoCode dtoCode = codeService.findByCode("BASE_ExpirationAlertTime_Standard");
        if (StringUtil.isNotNull(dtoCode)) {
            alertDays = Integer.valueOf(dtoCode.getDictValue());
        }
        Calendar expiryDateCalendar = new GregorianCalendar();
        Date date = new Date();
        expiryDateCalendar.setTime(date);
        //当前日期加上预警天数
        expiryDateCalendar.add(Calendar.DATE, alertDays);
        Map<String, Object> values = baseCriteria.getValues();
        values.put("expireDate", expiryDateCalendar.getTime());
        super.findByPage(pageBean, baseCriteria);

        List<DtoConsumable> list = pageBean.getData();
        List<DtoCode> consumableCategoryList = codeService.findCodes(BaseCodeHelper.ConsumableCategory);
        List<DtoCode> consumableGradeList = codeService.findCodes(BaseCodeHelper.ConsumableGrade);
        if (StringUtil.isNotEmpty(list)) {
            List<String> ids = list.stream().map(DtoConsumable::getId).collect(Collectors.toList());
            List<DtoConsumableDetail> details = consumableDetailRepository.findByParentIds(ids);
            List<DtoConsumableOfMixed> consumableOfMixedList = consumableOfMixedRepository.findByConsumableIdIn(ids);
            List<String> warnUserIdList = list.stream().map(DtoConsumable::getSendWarnUserId).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
            Map<String, String> userIdDeptMap = new HashMap<>();
            Map<String,String> itemNameMap = analyzeItemRepository.findAll().stream().collect(Collectors.toMap(DtoAnalyzeItem::getId, DtoAnalyzeItem::getAnalyzeItemName));
            consumableOfMixedList.forEach(m -> m.setAnalyzeItemName(itemNameMap.getOrDefault(m.getAnalyzeItemId(),"")));
            if (StringUtil.isNotEmpty(warnUserIdList)) {
                List<Object[]> objects = commonRepository.find("select a.id, a.deptId from DtoPerson a where a.isDeleted = 0 and a.id in :ids",
                        Collections.singletonMap("ids", warnUserIdList));
                objects.forEach(p -> userIdDeptMap.put(p[0].toString(), p[1].toString()));
            }
            Map<String, String> departmentMap = departmentService.findAll().stream().collect(Collectors.toMap(DtoDepartment::getId, DtoDepartment::getDeptName));
            for (DtoConsumable item : list) {
                List<DtoConsumableDetail> detailList = details.stream().filter(p -> p.getParentId().equals(item.getId())).collect(Collectors.toList());
                BigDecimal inventory = new BigDecimal("0");
                //查询所有库存
                inventory = detailList.stream().map(ConsumableDetail::getStorage).reduce(BigDecimal.ZERO, BigDecimal::add);
                item.setInventory(inventory);
                item.setExpireAlertDays(String.valueOf(alertDays));
                DtoConsumableDetail detail = detailList.stream().findFirst().orElse(null);
                item.setDetail(detail);
                //计算过期状态
                item.setExpireStatus(calExpireStatus(detail,expiryDateCalendar.getTime()));
                item.setKeepPlace(detailList.stream().map(DtoConsumableDetail::getKeepPlace).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.joining(";")));
                // 类型名称
                DtoCode dtoCodeCategory = consumableCategoryList.stream().filter(p -> p.getDictCode().equals(item.getCategoryId())).findFirst().orElse(null);
                if (StringUtil.isNotNull(dtoCodeCategory)) {
                    item.setCategoryName(dtoCodeCategory.getDictName());
                }
                // 等级名称
                DtoCode dtoCodeGrade = consumableGradeList.stream().filter(p -> p.getDictCode().equals(item.getGrade())).findFirst().orElse(null);
                if (StringUtil.isNotNull(dtoCodeGrade)) {
                    item.setCategoryName(dtoCodeGrade.getDictName());
                }
                //混标信息
                if(Boolean.TRUE.equals(item.getIsMixedStandard())){
                    item.setConsumableOfMixedList(consumableOfMixedList.stream().filter(c->item.getId().equals(c.getConsumableId())).collect(Collectors.toList()));
                }
                //浓度范围
                handleRangeText(item);
                String warnUserId = StringUtil.isNotEmpty(item.getSendWarnUserId()) ? item.getSendWarnUserId() : "";
                String deptId = userIdDeptMap.getOrDefault(warnUserId, "");
                item.setDeptName(departmentMap.getOrDefault(deptId, ""));
                //化合物名称
                item.setCompoundName(itemNameMap.getOrDefault(item.getCompoundId(), ""));
            }

            //如果库存状态查询条件不为空,需要按照库存状态进行过滤
            //传了化合物标识，与化合物标识相关的表样排前面
            if ((StringUtil.isNotEmpty(inventoryStatus) && !"-1".equals(inventoryStatus))
                    ||(Boolean.TRUE.equals(criteria.getIsStandard())&&StringUtil.isNotEmpty(compoundId)&&!UUIDHelper.GUID_EMPTY.equals(compoundId))) {
                if(StringUtil.isNotEmpty(inventoryStatus) && !"-1".equals(inventoryStatus)){
                    if ("0".equals(inventoryStatus)) {
                        //无库存
                        list = list.stream().filter(p -> p.getInventory().compareTo(BigDecimal.ZERO) == 0).collect(Collectors.toList());
                    } else if ("1".equals(inventoryStatus)) {
                        //低库存（库存大于0，且小于警告数量）
                        list = list.stream().filter(p -> p.getInventory().compareTo(BigDecimal.ZERO) > 0 && p.getInventory().compareTo(p.getWarningNum()) < 0).collect(Collectors.toList());
                    } else {
                        //有库存
                        list = list.stream().filter(p -> p.getInventory().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
                    }
                }
                if(Boolean.TRUE.equals(criteria.getIsStandard())&&StringUtil.isNotEmpty(compoundId)&&!UUIDHelper.GUID_EMPTY.equals(compoundId)){
                    List<DtoConsumable> newList = new ArrayList<>();
                    List<DtoConsumable> relatedList = list.stream().filter(p -> {
                        if(Boolean.TRUE.equals(p.getIsMixedStandard())){
                            return StringUtil.isNotEmpty(p.getConsumableOfMixedList())&&p.getConsumableOfMixedList().stream().anyMatch(m -> compoundId.equals(m.getAnalyzeItemId()));
                        }else{
                            return compoundId.equals(p.getCompoundId());
                        }
                    }).sorted(Comparator.comparing(DtoConsumable::getModifyDate).reversed()).collect(Collectors.toList());
                    List<String> relatedIdList = relatedList.stream().map(DtoConsumable::getId).collect(Collectors.toList());
                    List<DtoConsumable> notRelatedList = list.stream().filter(p -> !relatedIdList.contains(p.getId()))
                            .sorted(Comparator.comparing(DtoConsumable::getModifyDate).reversed()).collect(Collectors.toList());
                    newList.addAll(relatedList);
                    newList.addAll(notRelatedList);
                    list = newList;
                }
                pageBean.setPageNo(pageNo);
                pageBean.setRowsPerPage(rowsPerPage);
                pageNo = pageNo - 1;
                if (pageNo < 0) {
                    pageNo = 0;
                }
                pageBean.setRowsCount(list.size());
                list = list.stream().skip((long) pageNo * rowsPerPage).limit(rowsPerPage).collect(Collectors.toList());

            }
        }
        pageBean.setData(list);// 重新封装到pageBean返回到上层
    }

    /**
     * 计算过期状态
     * @param detail 详情数据
     * @param configExpireDate 即将到期日期
     * @return 过期状态
     */
    private String calExpireStatus(DtoConsumableDetail detail,Date configExpireDate) {
        Date date1753 = DateUtil.stringToDate("1753-01-01", DateUtil.YEAR);
        if(detail==null||detail.getExpiryDate()==null||date1753.compareTo(detail.getExpiryDate())==0){
            return "正常";
        }else{
            Date now = new Date();
            // 即将过期 正常 交集部分显示即将过期
            if(detail.getExpiryDate().compareTo(now)>=0&&detail.getExpiryDate().compareTo(configExpireDate)<=0){
                return "即将过期";
            }
            if(detail.getExpiryDate().compareTo(now)>=0){
                return "正常";
            }
            if(detail.getExpiryDate().compareTo(now)<0){
                return "已过期";
            }
            return "正常";
        }
    }

    /**
     * 通过详单parentId查询消耗品信息
     */
    // public void findConsumableByParentId(String parentId) 
    // {
    //     repository.findConsumableByParentId(parentId);
    // }

    /**
     * 新增消耗品数据,级联新增消耗品详情
     */
    @Override
    @Transactional
    public DtoConsumable save(DtoConsumable consumable) {
        // 不再进行消耗品编号唯一性判断
//        DtoConsumable exist = repository.findByCodeInStationAndId(consumable.getCodeInStation(), consumable.getId());
//        if (StringUtil.isNotNull(exist)) {
//            throw new BaseException("已存在相同编号的消耗品！" + exist.getCodeInStation());
//        }
        if (StringUtil.isNotNull(consumable.getDetail())) {
            consumable.setInventory(consumable.getDetail().getStorage());
        }
        if (StringUtils.isNotNullAndEmpty(consumable.getConsumableName())) {
            consumable.setFullPinYin(PinYinUtil.getFullSpell(consumable.getConsumableName()));
            consumable.setPinYin(PinYinUtil.getFirstSpell(consumable.getConsumableName()));
        }
        DtoConsumable dto = super.save(consumable);
        if (StringUtil.isNotNull(consumable.getDetail())) {
            DtoConsumableDetail detail = consumable.getDetail();
            detail.setParentId(dto.getId());
            detail.setUnitName(dto.getUnit());
            consumableDetailService.saveAndChangeConsumable(detail);
//            consumableDetailService.save(detail);
        }
        if (StringUtils.isNotNullAndEmpty(consumable.getUnitId()) && !UUIDHelper.GUID_EMPTY.equals(consumable.getUnitId())) {
            dimensionService.incrementOrderNum(Collections.singletonList(consumable.getUnitId()));
        }
        //新增标样时直接添加混标信息
        if(Boolean.TRUE.equals(consumable.getIsStandard())&&Boolean.TRUE.equals(consumable.getIsMixedStandard())
                &&StringUtil.isNotEmpty(consumable.getConsumableOfMixedList())){
            consumable.getConsumableOfMixedList().forEach(item->{
                item.setConsumableId(dto.getId());
                consumableOfMixedService.save(item);
            });
        }

        return dto;
    }

    /**
     * 删除消耗品
     */
    @Transactional
    @Override
    public void delete(String id) {
//        super.logicDeleteById(id);
        super.delete(id);
    }

    /**
     * 修改消耗品
     */
    @Transactional
    @Override
    public DtoConsumable update(DtoConsumable consumable) {
        // 不再做编号的唯一性校验
//        DtoConsumable exist = repository.findByCodeInStationAndId(consumable.getCodeInStation(), consumable.getId());
//        if (StringUtil.isNotNull(exist) && !exist.getId().equals(consumable.getId())) {
//            throw new BaseException("系统已存在相同编号的消耗品:" + exist.getCodeInStation());
//        }
        if (StringUtils.isNotNullAndEmpty(consumable.getConsumableName())) {
            consumable.setFullPinYin(PinYinUtil.getFullSpell(consumable.getConsumableName()));
            consumable.setPinYin(PinYinUtil.getFirstSpell(consumable.getConsumableName()));
        }
        return repository.save(consumable);
    }

    /**
     * 新增标准样品信息
     */
    @Transactional
    @Override
    public DtoConsumable createStandard(DtoConsumable consumable) {
        // 不再做编号的唯一性校验
//        DtoConsumable exist = repository.findByCodeInStationAndId(consumable.getCodeInStation(), consumable.getId());
//        if (StringUtil.isNotNull(exist)) {
//            throw new BaseException("已存在相同编号的标准物质！" + exist.getCodeInStation());
//        }

        DtoConsumableDetail detail = consumable.getDetail();
        consumable.setDetail(null);
        consumable.setIsStandard(true);
        consumable.setInventory(detail.getStorage());
        DtoConsumable dtoConsumable = super.save(consumable);

        detail.setId(dtoConsumable.getId());
        detail.setParentId(dtoConsumable.getId());
        detail.setConsumableName(dtoConsumable.getConsumableName());
        detail.setCodeInStation(dtoConsumable.getCodeInStation());
        detail.setSpecification(dtoConsumable.getSpecification());
        detail.setUnitName(dtoConsumable.getUnit());
        detail.setUnitId(dtoConsumable.getUnitId());
        DtoConsumableDetail dtoConsumableDetail = consumableDetailService.save(detail);

        dtoConsumable.setDetail(dtoConsumableDetail);
        if (StringUtils.isNotNullAndEmpty(consumable.getUnitId()) && !UUIDHelper.GUID_EMPTY.equals(consumable.getUnitId())) {
            dimensionService.incrementOrderNum(Collections.singletonList(consumable.getUnitId()));
        }
        //新增标样时直接添加混标信息
        if(Boolean.TRUE.equals(consumable.getIsStandard())&&Boolean.TRUE.equals(consumable.getIsMixedStandard())
                &&StringUtil.isNotEmpty(consumable.getConsumableOfMixedList())){
            consumable.getConsumableOfMixedList().forEach(item->{
                item.setConsumableId(dtoConsumable.getId());
                consumableOfMixedService.save(item);
            });
        }

        return dtoConsumable;


//        DtoConsumable dtoConsumable = repository.findOne(consumable.getId());
//        DtoConsumableDetail consumableDetail = consumableDetailService.findOne(consumable.getId());
//        DtoStandardTemp standardTemp = new DtoStandardTemp();
//        if (consumableDetail != null) {
//            standardTemp = getStandardTemp(dtoConsumable, consumableDetail);
//        }
//        return standardTemp;
    }
    // @Override
    // public DtoStandardTemp createStandard(String consumalbeForm, String consumableDetailForm) {
    //     ObjectMapper mapper = new ObjectMapper();
    //     StandardTemp standardTemp = new  StandardTemp();
    //     mapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));//时间格式
    //     mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    //     try {
    //         Consumable consumable = mapper.readValue(consumalbeForm, Consumable.class);
    //         ConsumableDetail consumableDetail = mapper.readValue(consumableDetailForm, ConsumableDetail.class);
    //         consumable.setInventory(consumableDetail.getInventory());
    //         consumableDetail.setParentId(consumable.getRowGuid());
    //         consumableDetailRepository.save(consumableDetail);
    //         consumableRepository.save(consumable);
    //         //赋值
    //         standardTemp = getStandardTemp(consumable,consumableDetail);
    //     } catch (Exception e) {
    //         throw new BaseException(e);
    //     }
    //     return standardTemp;
    // }

    /**
     * 获取标准样品信息
     */
    @Override
    public DtoStandardTemp getStandardById(String id) {
        DtoStandardTemp standardTemp = new DtoStandardTemp();
        DtoConsumable consumable = repository.findOne(id);
        DtoConsumableDetail consumableDetail = consumableDetailService.findOne(consumable.getId());
        if (StringUtil.isNotNull(consumableDetail)) {
            standardTemp = getStandardTemp(consumable, consumableDetail);
        }
        return standardTemp;
    }
    // @Override
    // public DtoStandardTemp getStandardById(String rowGuid) {
    //     StandardTemp standardTemp = new  StandardTemp();
    //     Consumable consumable =consumableRepository.getByRowGuid(rowGuid);
    //     List<ConsumableDetail> consumableDetailList = consumableDetailRepository.getListByParentId(consumable.getRowGuid());
    //     if(consumableDetailList.size() > 0){
    //         //赋值
    //         standardTemp = getStandardTemp(consumable,consumableDetailList.get(0));
    //     }
    //     return standardTemp;
    // }

    /**
     * 修改标准样品信息
     */
    @Transactional
    @Override
    public DtoConsumable updateStandard(DtoConsumable consumable) {
        DtoConsumableDetail detail = consumable.getDetail();
        consumable.setDetail(null);
        if (!consumable.getInventory().equals(detail.getStorage())) {
            consumable.setInventory(detail.getStorage());
        }
        DtoConsumable dtoConsumable = update(consumable);

        detail = consumableDetailService.addSupplier(detail);
        DtoConsumableDetail dtoConsumableDetail = comRepository.merge(detail);

        dtoConsumable.setDetail(dtoConsumableDetail);

        return dtoConsumable;

//        DtoStandardTemp standardTemp = new DtoStandardTemp();
//        DtoConsumable dtoConsumable = repository.findOne(consumable.getId());
//        DtoConsumableDetail consumableDetail = consumableDetailService.findOne(consumable.getId());
//        if (consumableDetail != null) {
//            standardTemp = getStandardTemp(dtoConsumable, consumableDetail);
//        }
//        return standardTemp;
    }

    @Override
    @Transactional
    public void copy(String id, Integer times) {
        DtoConsumable consumable = repository.findOne(id);
        List<DtoConsumableOfMixed> consumableOfMixedList = consumableOfMixedRepository.findByConsumableId(id);
        List<DtoConsumableDetail> details = consumableDetailService.findByParentIds(Collections.singletonList(id));
        List<DtoConsumable> newConsumableList = new ArrayList<>();
        List<DtoConsumableDetail> newDetails = new ArrayList<>();
        List<DtoConsumableOfMixed> newConsumableOfMixedList = new ArrayList<>();
        for (Integer i = 0; i < times; i++) {
            DtoConsumable newConsumable = new DtoConsumable();
            BeanUtils.copyProperties(consumable, newConsumable, "id", "creator", "createDate", "modifier", "modifyDate", "orgId", "domainId");
            newConsumableList.add(newConsumable);
            for (DtoConsumableDetail detail : details) {
                DtoConsumableDetail newDetail = new DtoConsumableDetail();
                BeanUtils.copyProperties(detail, newDetail, "id", "parentId");
                newDetail.setParentId(newConsumable.getId());
                if (newConsumable.getIsStandard()) {
                    newDetail.setId(newConsumable.getId());
                }
                newDetails.add(newDetail);
            }
            for (DtoConsumableOfMixed consumableOfMixed : consumableOfMixedList) {
                DtoConsumableOfMixed newConsumableOfMixed = new DtoConsumableOfMixed();
                BeanUtils.copyProperties(consumableOfMixed, newConsumableOfMixed, "id", "consumableId");
                newConsumableOfMixed.setConsumableId(newConsumable.getId());
                newConsumableOfMixedList.add(newConsumableOfMixed);
            }
        }
        if (StringUtil.isNotEmpty(newConsumableList)) {
            repository.save(newConsumableList);
        }
        if (StringUtil.isNotEmpty(newDetails)) {
            consumableDetailRepository.save(newDetails);
        }
        if (StringUtil.isNotEmpty(newConsumableOfMixedList)) {
            consumableOfMixedRepository.save(newConsumableOfMixedList);
        }
    }

    @Override
    public DtoConsumable byConsumableCode(DtoConsumable dtoConsumable) {
        DtoConsumable consumable = repository.findByConsumableCodeAndIsStandardTrue(dtoConsumable.getConsumableCode()).stream().findFirst().orElse(null);
        if (StringUtil.isNotNull(consumable)){
            DtoConsumableDetail detail =  consumableDetailRepository.findByParentId(consumable.getId()).stream().findFirst().orElse(null);
            consumable.setDetail(detail);
        }
        return consumable;
    }

    // @Override
    // public DtoStandardTemp updateStandard(String consumalbeForm, String consumableDetailForm) {
    //     ObjectMapper mapper = new ObjectMapper();
    //     StandardTemp standardTemp = new  StandardTemp();
    //     mapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));//时间格式
    //     mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    //     try {
    //         Consumable consumable = mapper.readValue(consumalbeForm, Consumable.class);
    //         ConsumableDetail consumableDetail = mapper.readValue(consumableDetailForm, ConsumableDetail.class);
    //         commonRepository.merge(consumableDetail);
    //         commonRepository.merge(consumable);
    //         //赋值
    //         standardTemp = getStandardTemp(consumable,consumableDetail);
    //     } catch (Exception e) {
    //         throw new BaseException(e);
    //     }
    //     return standardTemp;
    // }

    /**
     * 组装标准样品信息
     */
    public DtoStandardTemp getStandardTemp(DtoConsumable consumable, DtoConsumableDetail consumableDetail) {
        DtoStandardTemp standardTemp = new DtoStandardTemp();
        //TODO:【刘梧桐】需要修改字段类型IsMixedStandard为Boolean，是否需要新增parentId
        standardTemp.setConsumableName(consumable.getConsumableName());
        standardTemp.setCodeInStation(consumable.getCodeInStation());
        standardTemp.setConsumableCode(consumable.getConsumableCode());
        standardTemp.setCodeInStation(consumable.getCodeInStation());
        standardTemp.setUnit(consumable.getUnit());
        standardTemp.setUnitId(consumable.getUnitId());
        standardTemp.setGrade(consumable.getGrade());
        standardTemp.setCategoryId(consumable.getCategoryId());
        standardTemp.setWarningNum(consumable.getWarningNum());
        standardTemp.setSendWarnUserId(consumable.getSendWarnUserId());
        standardTemp.setIsPoison(consumable.getIsPoison());
        standardTemp.setDilutedSolution(consumable.getDilutedSolution());
        standardTemp.setDilutionMethod(consumable.getDilutionMethod());
        standardTemp.setConcentration(consumable.getConcentration());
        standardTemp.setUncertainty(consumable.getUncertainty());
        standardTemp.setKeepCondition(consumable.getKeepCondition());
        // standardTemp.setIsMixedStandard(consumable.getIsMixedStandard());
        standardTemp.setSafetyInstruction(consumable.getSafetyInstruction());
        standardTemp.setRemark(consumable.getRemark());
        standardTemp.setInventory(consumableDetail.getInventory());
        standardTemp.setUnitPrice(consumableDetail.getUnitPrice());
        standardTemp.setProductionCode(consumableDetail.getProductionCode());
        standardTemp.setManufacturerName(consumableDetail.getManufacturerName());
        standardTemp.setStorageDate(consumableDetail.getStorageDate());
        standardTemp.setExpiryDate(consumableDetail.getExpiryDate());
        standardTemp.setCheckerId(consumableDetail.getCheckerId());
        standardTemp.setSupplierId(consumableDetail.getSupplierId());
        standardTemp.setSupplierName(consumableDetail.getSupplierName());
        standardTemp.setCheckerResult(consumableDetail.getCheckerResult());
        standardTemp.setPurchasingDate(consumableDetail.getPurchasingDate());
        standardTemp.setAppearance(consumableDetail.getAppearance());
        standardTemp.setCheckItem(consumableDetail.getCheckItem());
        standardTemp.setBuyReason(consumableDetail.getBuyReason());
        standardTemp.setKeepPlace(consumableDetail.getKeepPlace());
        return standardTemp;
    }

    /**
     * 重新实现（为了返回调用该类的时候可以直接使用）
     *
     * @param id 主键id
     * @return 返回消耗对象
     */
    @Override
    public DtoConsumable findOne(String id) {
        DtoConsumable consumable = super.findOne(id);
        if (StringUtil.isNull(consumableDetailService)){
            log.error("=========================================consumableDetailService注入为空==============================================");
        }
        DtoConsumableDetail dtoConsumableDetail = consumableDetailService.findOne(id);
        if (StringUtil.isNotNull(dtoConsumableDetail)) {
            consumable.setDetail(dtoConsumableDetail);
        }
        return consumable;
    }

    /**
     * 处理浓度范围文本显示
     * 要对浓度范围做兼容处理，包括浓度【1±0.1mg/L】百分比【1mg±1%】、区间【0.9~1.1mg/L】，如果是混标，则显示“混标”这两个字
     * @param consumable 消耗品
     */
    private void handleRangeText(DtoConsumable consumable){
        String rangText = "";
        if(consumable.getIsMixedStandard()){
            rangText = "混标";
        }else{
            if(EnumBase.EnumUncertainType.浓度.getValue().equals(consumable.getUncertainType())){
                rangText = String.format("%s±%s%s",consumable.getConcentration(),consumable.getUncertainty(),
                        StringUtil.isNotEmpty(consumable.getDimensionName())?consumable.getDimensionName():"");
            }else if(EnumBase.EnumUncertainType.百分比.getValue().equals(consumable.getUncertainType())){
                rangText = consumable.getConcentration()+(StringUtil.isNotEmpty(consumable.getDimensionName())?consumable.getDimensionName():"")
                        +"±"+consumable.getUncertainty()+"%";
            }else if(EnumBase.EnumUncertainType.区间.getValue().equals(consumable.getUncertainType())){
                rangText = String.format("%s~%s%s",consumable.getRangeLow(),consumable.getRangeHigh(),
                        StringUtil.isNotEmpty(consumable.getDimensionName())?consumable.getDimensionName():"");
            }
        }
        consumable.setRangeText(rangText);
    }

    @Autowired
    public void setCommonRepository(CommonRepository commonRepository) {
        this.commonRepository = commonRepository;
    }

    @Autowired
    @Lazy
    public void setDepartmentService(DepartmentService departmentService) {
        this.departmentService = departmentService;
    }
}