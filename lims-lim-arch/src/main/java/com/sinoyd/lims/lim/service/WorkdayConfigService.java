package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoWorkdayConfig;

/**
 * 工作休息日管理配置接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/1/18
 */
public interface WorkdayConfigService extends IBaseJpaService<DtoWorkdayConfig, String> {

    /**
     * 根据年份查询
     * @param year 年份
     * @return 工作休息日管理配置dto
     */
    DtoWorkdayConfig findByYear(Integer year);



}