package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 文件管理
 * <AUTHOR>
 * @version V1.0.0 2019/3/5
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FileControlApplyDetailCriteria extends BaseCriteria {

    /**
     * 文件类型id
     */
    private String fileType;
    /**
     * 关键字：文件名称、文件编号、受控编号
     */
    private String key;
    /**
     * 受控状态（参见前台枚举，-1代表所有）
     */
    private Integer status;
    /**
     * 发放回收记录id
     */
    private String grantId;

    /**
     * 排除相应的状态
     */
    private List<Integer> noStatus;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(key)) {
            condition.append(" and (fileName like :key or fileCode like :key or controlCode like :key)");
            values.put("key", "%" + this.key + "%");
        }
        if (StringUtil.isNotNull(status) && !status.equals(-1)) {
            condition.append(" and (status = :status)");
            values.put("status", status);
        }
        if (StringUtils.isNotNullAndEmpty(fileType) && !UUIDHelper.GUID_EMPTY.equals(this.fileType)) {
            condition.append(" and (fileType = :fileType)");
            values.put("fileType", this.fileType);
        }
        if (StringUtils.isNotNullAndEmpty(grantId)) {
            condition.append(" and grantId = :grantId ");
            values.put("grantId", this.grantId);
        }
        if (StringUtil.isNotNull(noStatus) && noStatus.size() > 0) {
            condition.append(" and (status not in :noStatus)");
            values.put("noStatus", noStatus);
        }

        condition.append(" and isDeleted = 0");
        return condition.toString();
    }
}