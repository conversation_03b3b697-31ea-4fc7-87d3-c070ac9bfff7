package com.sinoyd.lims.api.controller;

import com.sinoyd.base.dto.customer.DtoSampleTypeDefaultGroup;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.SampleTypeGroupCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoSampleTypeGroup;
import com.sinoyd.lims.lim.service.SampleTypeGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 样品分组配置
 * <AUTHOR>
 * @version V1.0.0 2019/5/14
 * @since V100R001
 */
@Api(tags = "样品分组: 样品分组管理服务")
@RestController
@RequestMapping("/api/mobile/sampleTypeGroup")
@Validated
public class MobileSampleTypeGroupController extends BaseJpaController<DtoSampleTypeGroup, String, SampleTypeGroupService> {
    /**
     * 根据检测类型大类id列表获取检测类型默认的标签分组
     *
     * @param bigSampleTypeIdList 检测类型大类id列表
     * @return 返回检测类型
     */
    @ApiOperation(value = "根据检测类型大类id列表获取检测类型默认的标签分组", notes = "根据检测类型大类id列表获取检测类型默认的标签分组")
    @PostMapping("/getDefaultGroup")
    public RestResponse<List<DtoSampleTypeDefaultGroup>> findDefaultSampleGroup(@RequestBody List<String> bigSampleTypeIdList) {
        RestResponse<List<DtoSampleTypeDefaultGroup>> restResponse = new RestResponse<>();
        List<DtoSampleTypeDefaultGroup> resMap = service.findDefaultSampleGroup(bigSampleTypeIdList);
        restResponse.setData(resMap);
        restResponse.setRestStatus(StringUtil.isNull(resMap) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 样品分组列表
     *
     * @param sampleTypeGroupCriteria 样品分组查询条件
     * @return 样品分组列表
     */
    @ApiOperation(value = "分页动态条件查询样品分组", notes = "分页动态条件查询样品分组")
    @GetMapping("")
    public RestResponse<List<DtoSampleTypeGroup>> findByPage(SampleTypeGroupCriteria sampleTypeGroupCriteria) {
        RestResponse<List<DtoSampleTypeGroup>> restResponse = new RestResponse<>();
        PageBean<DtoSampleTypeGroup> page = super.getPageBean();
        service.findByPage(page, sampleTypeGroupCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(page.getData());
        restResponse.setCount(page.getRowsCount());

        return restResponse;
    }
}
