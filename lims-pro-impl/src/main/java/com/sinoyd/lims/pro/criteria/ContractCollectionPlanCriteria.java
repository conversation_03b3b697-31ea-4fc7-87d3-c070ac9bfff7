package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;

import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 收款计划查询条件
 * <AUTHOR> 修改：徐肖波
 * @version V1.0.0 2019/2/2
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ContractCollectionPlanCriteria extends BaseCriteria {

    /**
     * 合同Id
     */
    private String contractId;


    /**
     * 计划收款日期开始
     */
    private String collectDateStart;

    /**
     * 计划收款日期结束
     */
    private String collectDateStartEnd;

    /**
     * 业务员
     */
    private List<String> salesPersonIds;

    /**
     * 合同名称合同编号
     */
    private String key;

    /**
     * 甲方名称
     */
    private String firstEntName;

    /**
     * 收款状态
     */
    private Integer status;

    private Boolean isProfile = false;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        condition.append(" and x.contractId = o.id");
        //仅统计合同状态为“已签订”的合同记录
        if (Boolean.TRUE.equals(isProfile)) {
            condition.append(" and o.contractStatus = :contractStatus");
            values.put("contractStatus", EnumPRO.EnumOrderSignStatus.已签订.getValue());
        }
        //关联合同标识
        if (StringUtils.isNotNullAndEmpty(contractId)
                && !UUIDHelper.GUID_EMPTY.equals(this.contractId)) {
            condition.append(" and (contractId = :contractId)");
            values.put("contractId", this.contractId);
        }
        //收款状态
        if (StringUtil.isNotNull(status)) {
            condition.append(" and x.status = :status");
            values.put("status", this.status);
        }
        //合同信息(合同名称/合同编号)
        if (StringUtil.isNotEmpty(key)) {
            condition.append(" and (o.contractCode like :key or o.contractName like :key)");
            values.put("key", "%" + this.key + "%");
        }
        //甲方名称
        if (StringUtil.isNotEmpty(firstEntName)) {
            condition.append(" and o.firstEntName like :firstEntName");
            values.put("firstEntName", "%" + this.firstEntName + "%");
        }
        //业务员
        if (salesPersonIds != null && !salesPersonIds.isEmpty()) {
            condition.append(" and exists(select 1 from DtoOrderContract oc where x.contractId = oc.id and ( ");
            for (int i = 0; i < this.salesPersonIds.size(); i++) {
                String signPersonId = this.salesPersonIds.get(i);
                String key = "salesPersonId" + i;
                condition.append(" oc.signPersonId like :").append(key);
                values.put(key, "%" + signPersonId + "%");
                if (i < this.salesPersonIds.size() - 1) {
                    condition.append(" or ");
                }
            }
            condition.append(" ))");
        }
        //计划收款日期开始
        if (StringUtil.isNotEmpty(collectDateStart)) {
            Date from = DateUtil.stringToDate(this.collectDateStart, DateUtil.YEAR);
            condition.append(" and x.collectDate >= :collectDateStart");
            values.put("collectDateStart", from);
        }
        //计划收款日期结束
        if (StringUtil.isNotEmpty(collectDateStartEnd)) {
            Date to = DateUtil.stringToDate(this.collectDateStartEnd, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and x.collectDate < :collectDateStartEnd");
            values.put("collectDateStartEnd", c.getTime());
        }
        return condition.toString();
    }
}