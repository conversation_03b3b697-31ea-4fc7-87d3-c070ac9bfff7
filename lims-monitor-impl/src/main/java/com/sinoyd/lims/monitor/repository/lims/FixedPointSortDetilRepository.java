package com.sinoyd.lims.monitor.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.monitor.dto.lims.DtoFixedPointSortDetil;

import java.util.List;


/**
 * FixedPointSortDetil数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
public interface FixedPointSortDetilRepository extends IBaseJpaPhysicalDeleteRepository<DtoFixedPointSortDetil, String> {

    /**
     * 通过sortId 查询实体
     * @param sortId
     * @return 实体集合
     */
    List<DtoFixedPointSortDetil> findBySortId(String sortId);


    /**
     * 根据sortId集合删除该排序下所有的点位信息
     *
     * @param sortId 排序id
     * @return 返回分析项目id
     */
    void deleteBySortIdIn(List<String> sortId);



    /**
     * 根据sortId删除该排序下所有的点位信息
     *
     * @param sortId 排序id
     * @return 返回分析项目id
     */
    void deleteBySortId(String sortId);

    /**
     * 通过集合sortId查询所有的点位排序表
     * @param sortId
     * @return DtoFixedPointSortDetil集合
     */
    List<DtoFixedPointSortDetil> findBySortIdIn(List<String> sortId);



}