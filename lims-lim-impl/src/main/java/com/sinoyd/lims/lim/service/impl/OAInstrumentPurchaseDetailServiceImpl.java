package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoOAInstrumentPurchaseDetail;
import com.sinoyd.lims.lim.repository.lims.OAInstrumentPurchaseDetailRepository;
import com.sinoyd.lims.lim.service.OAInstrumentPurchaseDetailService;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 仪器采购明细 业务操作接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2019-04-03
 * @since V100R001
 */
@Service
public class OAInstrumentPurchaseDetailServiceImpl
        extends BaseJpaServiceImpl<DtoOAInstrumentPurchaseDetail, String, OAInstrumentPurchaseDetailRepository>
        implements OAInstrumentPurchaseDetailService {

    /**
     * 修改仪器采购申请明细的未入库数量
     *
     * @param oaInstrumentPurchaseDetail 仪器采购申请明细对象
     * @return 仪器采购申请明细对象
     */
    @Transactional
    @Override
    public DtoOAInstrumentPurchaseDetail updateSurplusNum(DtoOAInstrumentPurchaseDetail oaInstrumentPurchaseDetail) {
        oaInstrumentPurchaseDetail.setSurplusNum(oaInstrumentPurchaseDetail.getSurplusNum() - 1);
        oaInstrumentPurchaseDetail.setPurchaseNum(oaInstrumentPurchaseDetail.getPurchaseNum() + 1);
        return super.update(oaInstrumentPurchaseDetail);
    }
}
