package com.sinoyd.lims.lim.service;

import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.lim.dto.customer.expimp.DtoExpImpEnterprise;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 企业客户导入导出接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/13
 * @since V100R001
 */
public interface ExpImpEnterpriseService extends ImportExcelService<DtoExpImpEnterprise, DtoEnterprise> {
    /**
     * 导出
     *
     * @param baseCriteria 请求参数
     * @param response     响应体
     * @param sheetNames   sheet页名称
     * @param fileName     附件名称
     */
    void export(BaseCriteria baseCriteria, HttpServletResponse response, Map<String, String> sheetNames, String fileName);

}
