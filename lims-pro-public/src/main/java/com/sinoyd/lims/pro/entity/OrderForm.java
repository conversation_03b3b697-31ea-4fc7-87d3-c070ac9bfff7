package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * OrderForm实体
 *
 * <AUTHOR>
 * @version V1.0.0 2022/5/18
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "OrderForm")
@Data
@EntityListeners(AuditingEntityListener.class)
public class OrderForm implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public OrderForm() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 订单号
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("订单号")
    //@NotBlank(message = "订单号{validation.message.blank}")
    @Length(message = "订单号{validation.message.length}", max = 50)
    private String orderCode;

    /**
     * 业务类型
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("业务类型")
    private String projectTypeId;

    /**
     * 订单名称
     */
    @ApiModelProperty("订单名称")
    @Length(message = "订单名称{validation.message.length}", max = 50)
    private String orderName;

    /**
     * 业务员Id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("业务员Id")
    private String salesPersonId;

    /**
     * 业务员名称
     */
    @Column(length = 50)
    @ApiModelProperty("业务员名称")
    @Length(message = "业务员名称{validation.message.length}", max = 50)
    private String salesPersonName;

    /**
     * 登记人员id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("登记人员id")
    private String registrantId;

    /**
     * 登记人员名称
     */
    @Column(length = 50)
    @ApiModelProperty("登记人员名称")
    @Length(message = "登记人员名称{validation.message.length}", max = 50)
    private String registrantName;

    /**
     * 订单日期
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @ApiModelProperty("订单日期")
    private Date orderDate;

    /**
     * 报价期限
     */
    @Column(nullable = false)
    @ColumnDefault("30")
    @ApiModelProperty("报价期限")
    private Integer timeLimit;

    /**
     * 客户Id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("客户Id")
    private String enterpriseId;

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    @Length(message = "客户名称{validation.message.length}", max = 50)
    private String enterpriseName;

    /**
     * 联系人
     */
    @Column(length = 50)
    @ApiModelProperty("联系人")
    @Length(message = "联系人{validation.message.length}", max = 50)
    private String linkPerson;

    /**
     * 联系电话
     */
    @Column(length = 50)
    @ApiModelProperty("联系电话")
    @Length(message = "联系电话{validation.message.length}", max = 50)
    private String linkPhone;

    /**
     * 地址
     */
    @Column(length = 50)
    @ApiModelProperty("地址")
    @Length(message = "地址{validation.message.length}", max = 50)
    private String address;

    /**
     * 推送状态
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("推送状态")
    private Integer pushStatus;

    /**
     * 订单状态
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("订单状态")
    private Integer orderStatus;

    /**
     * 流程类型
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("流程类型")
    private Integer flowType;

    /**
     * 一审人员
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("一审人员")
    private String firstPersonId;

    /**
     * 二审人员
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("二审人员")
    private String secondPersonId;

    /**
     * 三审人员
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("三审人员")
    private String threePersonId;


    /**
     * 客户订单编号
     */
    @Column(length = 50)
    @ApiModelProperty("客户订单编号")
    @Length(message = "客户订单编号{validation.message.length}", max = 50)
    private String customerOrderNo;

    /**
     * 行政区域id
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("行政区域id")
    private String areaId;

    /**
     * 受检单位Id
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("受检单位Id")
    private String inspectedEntId;

    /**
     * 受检单位
     */
    @Column(length=100)
    @ApiModelProperty("受检单位")
    @Length(message = "受检单位{validation.message.length}", max = 100)
    private String inspectedEnt;

    /**
     * 受检方联系人
     */
    @Column(length=50)
    @ApiModelProperty("受检方联系人")
    @Length(message = "受检方联系人{validation.message.length}", max = 50)
    private String inspectedLinkMan;

    /**
     * 受检方联系电话
     */
    @Column(length=50)
    @ApiModelProperty("受检方联系电话")
    @Length(message = "受检方联系电话{validation.message.length}", max = 50)
    private String inspectedLinkPhone;

    /**
     * 受检方地址
     */
    @Column(length=100)
    @ApiModelProperty("受检方地址")
    @Length(message = "受检方地址{validation.message.length}", max = 100)
    private String inspectedAddress;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
    private String remark;

    /**
     * 是否删除
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;


    /**
     * 签单状态
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("签单状态")
    private Integer grantStatus;

    /**
     * 签单日期
     */
    @Column(nullable = false)
    @ColumnDefault("1753-01-01")
    @LastModifiedDate
    @ApiModelProperty("签单日期")
    private Date signDate;

}