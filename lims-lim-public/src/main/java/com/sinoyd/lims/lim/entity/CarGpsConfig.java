package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * CarGpsConfig实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="CarGpsConfig")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class CarGpsConfig implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  CarGpsConfig() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 车辆id（Guid）
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("车辆id（Guid）")
	private String carId;
    
    /**
    * gps型号
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("gps型号")
    @Length(message = "gps型号{validation.message.length}", max = 50)
	private String gpsModel;
    
    /**
    * gps编号（MN号）
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("gps编号（MN号）")
    @Length(message = "gps编号{validation.message.length}", max = 50)
	private String gpsCode;
    
    /**
    * gps密码
    */
    @Column(length=50)
    @ApiModelProperty("gps密码")
    @Length(message = "gps密码{validation.message.length}", max = 50)
    private String gpsPwd;
    
    /**
    * SIM号码
    */
    @Column(length=50)
    @ApiModelProperty("SIM号码")
    @Length(message = "SIM号码{validation.message.length}", max = 50)
    private String simNumber;
    
    /**
    * 限速
    */
    @Column(length=50)
    @ApiModelProperty("限速")
    @Length(message = "限速{validation.message.length}", max = 50)
    private String rateLimited;
    
    /**
    * 采样地点范围(m)
    */
    @Column(nullable=false)
    @ColumnDefault("500")
    @ApiModelProperty("采样地点范围(m)")
    private BigDecimal rangeConfig;
    
    /**
    * gps的接受频率(min)
    */
    @Column(length=20)
    @ColumnDefault("'5'")
    @ApiModelProperty("gps的接受频率(min)")
    @Length(message = "gps的接受频率(min){validation.message.length}", max = 20)
    private String gpsFrequency;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }