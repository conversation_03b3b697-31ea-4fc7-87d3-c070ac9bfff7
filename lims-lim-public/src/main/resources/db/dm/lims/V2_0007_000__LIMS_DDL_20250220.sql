CREATE TABLE "TB_LIM_MPNCONFIG"
(
    "ID" VARCHAR(50) NOT NULL,
    "TESTID" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "PARAM1ID" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "PARAM2ID" VARCHAR(50),
    "PARAM3ID" VARCHAR(50),
    "RESULTPARAMID" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "ORGID" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "CREATOR" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "CREATEDATE" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "DOMAINID" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "MODIFIER" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "MODIFYDATE" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "TB_LIM_MPNCONFIG" IS 'MPN配置';

COMMENT ON COLUMN "TB_LIM_MPNCONFIG"."ID" IS '主键';

COMMENT ON COLUMN "TB_LIM_MPNCONFIG"."TESTID" IS '测试项目ID';

COMMENT ON COLUMN "TB_LIM_MPNCONFIG"."PARAM1ID" IS '参数1';

COMMENT ON COLUMN "TB_LIM_MPNCONFIG"."PARAM2ID" IS '参数2';

COMMENT ON COLUMN "TB_LIM_MPNCONFIG"."PARAM3ID" IS '参数3';

COMMENT ON COLUMN "TB_LIM_MPNCONFIG"."RESULTPARAMID" IS '结果参数';

COMMENT ON COLUMN "TB_LIM_MPNCONFIG"."ORGID" IS '组织机构id';

COMMENT ON COLUMN "TB_LIM_MPNCONFIG"."CREATOR" IS '创建人';

COMMENT ON COLUMN "TB_LIM_MPNCONFIG"."CREATEDATE" IS '创建时间';

COMMENT ON COLUMN "TB_LIM_MPNCONFIG"."DOMAINID" IS '所属实验室';

COMMENT ON COLUMN "TB_LIM_MPNCONFIG"."MODIFIER" IS '修改人';

COMMENT ON COLUMN "TB_LIM_MPNCONFIG"."MODIFYDATE" IS '修改时间';

CREATE TABLE "TB_LIM_MPNCONFIGDETAILS"
(
    "ID" VARCHAR(50) NOT NULL,
    "MPNCONFIGID" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "PARAM1VALUE" VARCHAR(50) DEFAULT '' NOT NULL,
    "PARAM2VALUE" VARCHAR(50) DEFAULT '',
    "PARAM3VALUE" VARCHAR(50) DEFAULT '',
    "RESULTPARAMVALUE" VARCHAR(50) DEFAULT '' NOT NULL,
    "CONFIDENCELOW" VARCHAR(50) DEFAULT '',
    "CONFIDENCEUP" VARCHAR(50) DEFAULT '',
    "ORDERNUM" INT,
    NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "TB_LIM_MPNCONFIGDETAILS" IS 'MPN配置详情';

COMMENT ON COLUMN "TB_LIM_MPNCONFIGDETAILS"."ID" IS '主键';

COMMENT ON COLUMN "TB_LIM_MPNCONFIGDETAILS"."MPNCONFIGID" IS 'MNP配置ID';

COMMENT ON COLUMN "TB_LIM_MPNCONFIGDETAILS"."PARAM1VALUE" IS '参数1值';

COMMENT ON COLUMN "TB_LIM_MPNCONFIGDETAILS"."PARAM2VALUE" IS '参数2值';

COMMENT ON COLUMN "TB_LIM_MPNCONFIGDETAILS"."PARAM3VALUE" IS '参数3值';

COMMENT ON COLUMN "TB_LIM_MPNCONFIGDETAILS"."RESULTPARAMVALUE" IS '结果值';

COMMENT ON COLUMN "TB_LIM_MPNCONFIGDETAILS"."CONFIDENCELOW" IS '95%置信下限';

COMMENT ON COLUMN "TB_LIM_MPNCONFIGDETAILS"."CONFIDENCEUP" IS '95%置信上限';

COMMENT ON COLUMN "TB_LIM_MPNCONFIGDETAILS"."ORDERNUM" IS '排序值';

