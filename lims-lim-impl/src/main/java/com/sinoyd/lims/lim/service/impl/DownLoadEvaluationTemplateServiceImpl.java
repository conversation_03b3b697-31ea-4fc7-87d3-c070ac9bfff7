package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.dto.customer.DtoImportEnterprise;
import com.sinoyd.base.dto.customer.DtoImportEvaluationCriteria;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.lims.lim.service.DownLoadEnterpriseTemplateService;
import com.sinoyd.lims.lim.service.DownLoadEvaluationTemplateService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class DownLoadEvaluationTemplateServiceImpl implements DownLoadEvaluationTemplateService {
    //region 注入
    private ImportUtils importUtils;
    //endregion

    @Override
    public void downLoadTemplate(HttpServletResponse response, Map<String, String> sheetNames, String fileName) {
        // 获取空数据
        List<DtoImportEvaluationCriteria> emptyList = getNullIns();
        //endregion

        //region 赋值工作单并下载
        // 获取需要下载的工作单
        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoImportEvaluationCriteria.class, emptyList);
        // 下载Excel返回响应流
        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
        //endregion
    }

    /**
     * 获取空仪器检定数据
     *
     * @return 空list
     */
    private List<DtoImportEvaluationCriteria> getNullIns() {
        List<DtoImportEvaluationCriteria> enterpriseList = new ArrayList<>();
        DtoImportEvaluationCriteria evaluationCriteria = new DtoImportEvaluationCriteria();
        enterpriseList.add(evaluationCriteria);
        return enterpriseList;
    }

    @Autowired
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }
}
