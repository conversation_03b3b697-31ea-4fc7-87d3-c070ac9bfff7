package com.sinoyd.lims.lim.data.sync.strategy;

import com.sinoyd.base.dto.rcc.DtoEvaluationAnalyzeItem;
import com.sinoyd.base.dto.rcc.DtoEvaluationCriteria;
import com.sinoyd.base.repository.rcc.EvaluationAnalyzeItemRepository;
import com.sinoyd.base.service.EvaluationAnalyzeItemService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.lims.lim.data.sync.dto.DtoDataCompareResult;
import com.sinoyd.lims.lim.data.sync.strategy.base.AbsDataSync;
import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 评价标准限值同步
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/5/23
 */
@Component
@DependsOn({"springContextAware"})
@Order(19)
@Slf4j
public class EvaluationAnalyzeItemSync extends AbsDataSync<DtoEvaluationAnalyzeItem> {

    @Autowired
    private EvaluationAnalyzeItemService service;

    @Autowired
    private EvaluationAnalyzeItemRepository repository;

    @Autowired
    private EvaluationSync evaluationSync;
    /**
     * 数据比较
     *
     * @param evaluationIds 需要同步的测试项目id
     * @return 比较结果
     */
    @Override
    public List<DtoDataCompareResult<DtoEvaluationAnalyzeItem>> compareData(List<String> evaluationIds) {
        List<DtoEvaluationAnalyzeItem> projectData = service.findAll();
        List<DtoEvaluationAnalyzeItem> standardData = queryStandardData();
        if (StringUtil.isNotEmpty(standardData) && StringUtil.isNotEmpty(evaluationIds)){
            standardData = standardData.stream().filter(p->evaluationIds.contains(p.getEvaluationId())).collect(Collectors.toList());
        }
        //判断评价标准是否被删除
        List<DtoEvaluationCriteria> evaluations = evaluationSync.queryStandardData();
        if (StringUtil.isNotEmpty(evaluations)){
            List<String> evaluationIdsTemp = evaluations.stream().map(DtoEvaluationCriteria::getId).collect(Collectors.toList());
            List<DtoEvaluationAnalyzeItem> temps = standardData.stream().filter(p->!evaluationIdsTemp.contains(p.getEvaluationId())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(temps)){
                List<String> tempsIds = temps.stream().map(DtoEvaluationAnalyzeItem::getId).collect(Collectors.toList());
                standardData.removeIf(p->tempsIds.contains(p.getId()));
            }
        }else{
            standardData = null;
        }
        return compareData(standardData,projectData);
    }

    /**
     * 同步数据
     *
     * @param evaluationIds         需要同步的数据id集合
     * @param webSocketServer websockets服务端
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncData(List<String> evaluationIds, WebSocketServer webSocketServer) {
        List<DtoDataCompareResult<DtoEvaluationAnalyzeItem>> compareResult = compareData(evaluationIds);
        Optional<DtoDataCompareResult<DtoEvaluationAnalyzeItem>> resultOptional = compareResult.parallelStream().filter(p -> COMPARE_CATEGORY[0].equals(p.getCategory())
                && p.getCount() > 0).findFirst();
        resultOptional.ifPresent(r -> {
            //已同步记录数
            int i = 0;
            DtoEvaluationAnalyzeItem errorDto = null;
            try {
                for (DtoEvaluationAnalyzeItem dtoEvaluationAnalyzeItem : r.getAddDataList()) {
                    errorDto = dtoEvaluationAnalyzeItem;
                    if (repository.findOne(dtoEvaluationAnalyzeItem.getId()) != null) {
                        service.update(dtoEvaluationAnalyzeItem);
                    } else {
                        service.save(dtoEvaluationAnalyzeItem);
                    }
                    webSocketServer.sendMessage(String.format("同步" + getItemName() + "(%d/%d)", ++i, r.getCount()));
                }
            } catch (Exception e) {
                if (errorDto != null) {
                    webSocketServer.sendMessage("同步" + getItemName() + "发生错误, ID = " + errorDto.getId() + ", 错误信息: "
                            + e.getMessage());
                }
                log.error(e.getMessage(), e);
                throw new BaseException("同步" + getItemName() + "发生错误");
            }
        });
    }

    @Override
    public boolean mustSync() {
        return true;
    }

    @Override
    public String getItemName() {
        return EnumLIM.EnumDataSyncType.评价标准分析项目.name();
    }

    @Override
    public Integer getOrderNum() {
        return EnumLIM.EnumDataSyncType.评价标准分析项目.getValue();
    }

    @Override
    public String getStandardDataQueryUrl() {
        return "/api/sinoyd-lims/base/evaluationAnalyzeItem/all";
    }

    @Override
    public List<DtoEvaluationAnalyzeItem> queryStandardData() {
        String url = standardDataConfig.getHost() + getStandardDataQueryUrl();
        List<Map<String, Object>> resultMapList = (List<Map<String, Object>>) queryStandardData(url).getBody().get("data");
        return convertObject(resultMapList);
    }

    @Override
    public Integer getSyncDataType() {
        return EnumLIM.EnumDataSyncType.评价标准分析项目.getValue();
    }
}
