package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;


/**
 * DocAuthority实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="DocAuthority")
 @Data
 public  class DocAuthority implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  DocAuthority() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 文件夹，文件Id（Guid）
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("文件夹，文件Id（Guid）")
	private String objectId;
    
    /**
    * 权限Id（常量Guid，常量名称 LIM_AuthType）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("权限Id（常量Guid，常量名称 LIM_AuthType）")
    private String authId;
    
    /**
    * 权限名称
    */
    @Column(length=250)
    @ApiModelProperty("权限名称")
	private String authName;
    
    /**
    * 角色Id（Guid）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("角色Id（Guid）")
    private String roleId;
    
    /**
    * 角色名称
    */
    @Column(length=250)
    @ApiModelProperty("角色名称")
	private String roleName;
    
    /**
    * 权限状态
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("权限状态")
    private Boolean authState;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
 }