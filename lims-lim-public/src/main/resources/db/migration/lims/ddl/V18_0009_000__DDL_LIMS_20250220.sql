CREATE TABLE TB_LIM_MpnConfig
(
    id            varchar(50) NOT NULL COMMENT '主键',
    testId        varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '测试项目ID',
    param1Id      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '参数1',
    param2Id      varchar(50)          DEFAULT NULL COMMENT '参数2',
    param3Id      varchar(50)          DEFAULT NULL COMMENT '参数3',
    resultParamId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '结果参数',
    orgId         varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator       varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    domainId      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='MPN配置';

CREATE TABLE TB_LIM_MpnConfigDetails
(
    id               varchar(50) NOT NULL COMMENT '主键',
    mpnConfigId      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT 'MNP配置ID',
    param1Value      varchar(50) NOT NULL DEFAULT '' COMMENT '参数1值',
    param2Value      varchar(50)          DEFAULT '' COMMENT '参数2值',
    param3Value      varchar(50)          DEFAULT '' COMMENT '参数3值',
    resultParamValue varchar(50) NOT NULL DEFAULT '' COMMENT '结果值',
    confidenceLow    varchar(50)          DEFAULT '' COMMENT '95%置信下限',
    confidenceUp     varchar(50)          DEFAULT '' COMMENT '95%置信上限',
    orderNum         int(11) DEFAULT NULL COMMENT '排序值',
    PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='MPN配置详情';