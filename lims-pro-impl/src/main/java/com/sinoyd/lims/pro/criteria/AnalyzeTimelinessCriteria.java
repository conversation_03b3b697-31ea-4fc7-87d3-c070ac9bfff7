package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 及时率统计查询条件
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/1/31
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class AnalyzeTimelinessCriteria extends BaseCriteria implements Serializable {

    /**
     * 采样日期(范围开始)
     */
    private String startSamplingDate;

    /**
     * 采样日期（范围结束）
     */
    private String endSamplingDate;

    /**
     * 检测类型(批量）
     */
    private List<String> sampleTypeIds;

    /**
     * 分析人员id
     */
    private String analystId;

    /**
     * 数据状态（对图表不过滤）
     * 0 未超期 1即将超期 2超期
     */
    private Integer dataStatus;

    /**
     * 关键字（对图表不过滤）
     */
    private String key;

    /**
     * 超期预警时间
     */
    private Long warnDay;

    /**
     * 是否是数据列表
     */
    private Boolean isDataList = false;

    private Boolean isSql = false;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.testIsDeleted = 0 ");
        condition.append(" and a.isDeleted = 0 ");
        condition.append(" and a.qcGrade!=2 ");
        // condition.append(" and a.isCompleteField=0 ");
        condition.append(" and a.analyzeTime != '1753-01-01 00:00:00'");
//        if (isSql) {
//            condition.append(" and exists(select 1 from TB_PRO_ReceiveSampleRecord rs where a.receiveId = rs.id and rs.status != '新建')");
//        } else {
//            condition.append(" and exists(select 1 from DtoReceiveSampleRecord rs where a.receiveId = rs.id and rs.status != '新建')");
//        }
//        if (isDataList) {
//            condition.append(" and type.id = a.sampleTypeId ");
//            condition.append(" and type.isDeleted = 0 ");
//        }

        if (StringUtil.isNotEmpty(startSamplingDate)) {
            Date from = DateUtil.stringToDate(this.startSamplingDate, DateUtil.YEAR);
            condition.append(" and a.samplingTimeBegin >= :startSamplingDate");
            values.put("startSamplingDate", from);
        }
        if (StringUtil.isNotEmpty(endSamplingDate)) {
            Date to = DateUtil.stringToDate(this.endSamplingDate, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and a.samplingTimeBegin < :endSamplingDate");
            values.put("endSamplingDate", c.getTime());
        }
        if (StringUtil.isNotEmpty(sampleTypeIds)) {
            condition.append(" and a.sampleTypeId in (");
            int i = 1;
            for (String sampleTypeId : sampleTypeIds) {
                String temp = "";
                if (i == sampleTypeIds.size()) {
                    temp = "'" + sampleTypeId + "'";
                } else {
                    temp = "'" + sampleTypeId + "',";
                }
                condition.append(temp);
                i++;
            }
            condition.append(" ) ");
        }
        if (StringUtil.isNotEmpty(analystId)) {
            condition.append(" and a.analystId= :analystId");
            values.put("analystId", analystId);
        }
        if (dataStatus != null && isDataList) {
            if (dataStatus == 0) {
//                condition.append(" and TO_DAYS(a.analyzeTime)+:warnDay -TO_DAYS(s.samplingTimeBegin)-t.analyseDayLen<0");
//                values.put("warnDay", warnDay);
            } else if (dataStatus == 2) {
//                condition.append(" and TO_DAYS(a.analyzeTime)-TO_DAYS(s.samplingTimeBegin)-t.analyseDayLen>0");
            } else {
//                condition.append(" and (TO_DAYS(s.samplingTimeBegin)+t.analyseDayLen-TO_DAYS(a.analyzeTime)>=0 " +
//                        "and TO_DAYS(s.samplingTimeBegin)+t.analyseDayLen-TO_DAYS(a.analyzeTime)<=:warnDay and a.dataStatus!=16)");
                condition.append(" and a.dataStatus!=16)");
            }
        }
        if (StringUtil.isNotEmpty(key) && isDataList) {
            condition.append(" and (a.redAnalyzeItemName like :key or s.code like :key)");
            values.put("key", "%" + key + "%");
        }
        return condition.toString();
    }

    /**
     * 获取当前查询条件的及时率redis缓存key
     *
     * @return redis缓存key
     */
    public String getTimelinessChartKey() {
        //仪表盘与 分析日期区间/检测类型/分析人员 有关
        return "PRO:analyzeTimelinessChart" + this;
    }

    /**
     * 获取当前查询条件的柱状图redis缓存key
     *
     * @return redis缓存key
     */
    public String getHistogramChartKey() {
        //柱状图与 分析日期区间/检测类型
        return "PRO:analyzeHistogramChart" + getHistogramChartPartKey();
    }

    /**
     * 获取及时率信息表redis缓存key
     *
     * @return redis缓存key
     */
    public String getQueryTimelinessKey() {
        //及时率列表与 分析日期区间/检测类型/分析人员 有关
        return "PRO:analyzeTimeliness" + this;
    }

    private String getHistogramChartPartKey(){
        String str = "";
        if (StringUtil.isNotEmpty(startSamplingDate)) {
            str = str + ":" + startSamplingDate;
        }
        if (StringUtil.isNotEmpty(endSamplingDate)) {
            str = str + ":" + endSamplingDate;
        }
        if (StringUtil.isNotEmpty(sampleTypeIds)) {
            String partKey = sampleTypeIds.stream().sorted().collect(Collectors.joining(":"));
            str = str + ":" + partKey;
        }
        return str;
    }

    /**
     * toString方法
     *
     * @return 对象的str描述
     */
    public String toString() {
        String str = getHistogramChartPartKey();
        if (StringUtil.isNotEmpty(analystId)) {
            str = str + ":" + analystId;
        }
        return str;
    }

}