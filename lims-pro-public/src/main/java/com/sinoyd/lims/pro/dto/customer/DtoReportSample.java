package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

@Data
public class DtoReportSample {
    /**
     * 样品id
     */
    private String id;

    /**
     * 报告id
     */
    private String reportId;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 报告数量
     */
    private Integer reportNum;

    /**
     * 样品变更状态
     */
    private Integer sampleChangeStatus;

    /**
     * 报告变更状态
     */
    private Integer reportChangeStatus;

    /**
     * 该构造函数主要用到 ProServiceImpl 下面 updateReportSample 方法，如果修改，对应的应用地方也要调整
     * @param id 样品id
     * @param reportId 报告id
     * @param projectId 项目id
     * @param reportNum 报告文档个数
     * @param sampleChangeStatus 样品变更状态
     * @param reportChangeStatus 报告变更状态
     */
    public DtoReportSample(String id, String reportId, String projectId, Integer reportNum, Integer sampleChangeStatus, Integer reportChangeStatus) {
        this.id = id;
        this.reportId = reportId;
        this.projectId = projectId;
        this.reportNum = reportNum;
        this.sampleChangeStatus = sampleChangeStatus;
        this.reportChangeStatus = reportChangeStatus;
    }
}
