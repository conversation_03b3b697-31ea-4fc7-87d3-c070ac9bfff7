delete
from tb_lim_reportconfig
where id in (
             'fd1b5201-fef3-4a41-8a8c-6c461eb52775',
             'b4e4cb32-39e0-4dc8-bc4c-c917c482e929'
    );
-- 内审和不符合项报表模配置
    INSERT
INTO tb_lim_reportconfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                         pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                         modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                         beanName, versionNum, controlNum, reportName, validate, usageNum, versionNumLocation,
                         controlNumLocation)
VALUES ('fd1b5201-fef3-4a41-8a8c-6c461eb52775', 1, 'InternalAuditIssues', 'GL-005 质量管理体系内审存在问题汇总表.xlsx',
    'LIMReportForms/GL-005 质量管理体系内审存在问题汇总表.xlsx', 'output/LIMReportForms/GL-005 质量管理体系内审存在问题汇总表.xlsx',
    'application/excel', 'com.sinoyd.lims.report.service.limReportForms.InternalAuditIssuesService',
    '{ "isPageSig": true}', '', 0, 1, '', 0, '5f7bcf90feb545968424b0a872863876',
    '0ec468da-8386-4007-9c37-98d26ce5e2dc', '2025-04-16 19:27:37', '5f7bcf90feb545968424b0a872863876',
    '0ec468da-8386-4007-9c37-98d26ce5e2dc', '2025-04-16 19:27:37', '', 'LIMReportForms', 'InternalAuditIssues',
    0, '', NULL, '', '', '', 0, NULL, '中上', '左上');
INSERT INTO tb_lim_reportconfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName, validate, usageNum, versionNumLocation,
                                controlNumLocation)
VALUES ('b4e4cb32-39e0-4dc8-bc4c-c917c482e929', 1, 'NotConformItemSheet', 'GL-003 质量管理体系不符合报告单.xlsx',
        'LIMReportForms/GL-003 质量管理体系不符合报告单.xlsx', 'output/LIMReportForms/GL-003 质量管理体系不符合报告单.xlsx',
        'application/excel', 'com.sinoyd.lims.report.service.limReportForms.NotConformItemSheetService',
        '{ "isPageSig": true}', '', 0, 1, '', 0, '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-04-16 16:14:59', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-04-16 16:14:59', '', 'LIMReportForms', 'NotConformItemSheet', 0,
        '', NULL, '', '', '', 0, NULL, '中上', '左上');


-- 内审和不符合项报表应用场景

delete
from tb_lim_reportapply
where id in (
             '23579de9-184c-436b-8b1d-6bedfc5549ac',
             'c43f73bc-27bd-48b0-88ed-d7b9220336d5',
             '947caf1c-94bd-49d2-8323-28c3c9355679'
    );
INSERT INTO tb_lim_reportapply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('23579de9-184c-436b-8b1d-6bedfc5549ac', 'fd1b5201-fef3-4a41-8a8c-6c461eb52775', 'PlanInternalReport',
        '编制内审报告V2', 'InternalAuditIssues', '生成', 0, 0, 1, '', '内审计划信息:内审分工', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-04-16 19:28:30', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-04-16 19:28:30', NULL);
INSERT INTO tb_lim_reportapply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('c43f73bc-27bd-48b0-88ed-d7b9220336d5', 'b4e4cb32-39e0-4dc8-bc4c-c917c482e929', 'NotConformItemInquiry',
        '不符合项进度查询', 'NotConformItemSheet', '生成', 0, 0, 1, '', '不符合项进度查询', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-04-16 17:26:01', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-04-16 17:26:01', NULL);
INSERT INTO tb_lim_reportapply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('947caf1c-94bd-49d2-8323-28c3c9355679', 'b4e4cb32-39e0-4dc8-bc4c-c917c482e929', 'PlanInternalReport',
        '编制内审报告V2', 'NotConformItemSheet', '生成', 0, 0, 1, '', '内审计划信息:不符合项', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-04-16 16:20:16', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-04-16 16:20:16', NULL);
