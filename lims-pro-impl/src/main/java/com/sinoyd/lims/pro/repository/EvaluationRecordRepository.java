package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoEvaluationRecord;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;


/**
 * 评价记录数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/12
 * @since V100R001
 */
public interface EvaluationRecordRepository extends IBaseJpaRepository<DtoEvaluationRecord, String> {


    /**
     * 根据关联id和点位计划删除评价记录
     *
     * @param objectIds  关联id集合
     * @param folderPlan 点位计划
     */
    @Transactional
    @Modifying
    @Query("update DtoEvaluationRecord as a set a.isDeleted = true," +
            "a.modifier = :modifier,a.modifyDate= :modifyDate where a.objectId in :objectIds and a.folderPlan = :folderPlan")
    void deleteByObjectIdInAndFolderPlan(@Param("objectIds") Collection objectIds,
                                         @Param("folderPlan") Integer folderPlan,
                                         @Param("modifier") String modifier,
                                         @Param("modifyDate") Date modifyDate);

    /**
     * 根据关联id、关联类型、点位计划查询评价记录
     *
     * @param objectIds  关联id集合
     * @param objectType 关联类型
     * @param folderPlan 点位计划
     * @return 对应的评价记录
     */
    List<DtoEvaluationRecord> findByObjectIdInAndObjectTypeAndFolderPlan(Collection<String> objectIds, Integer objectType, Integer folderPlan);

    /**
     * 根据关联对象id，关联对象类型查询评价记录
     *
     * @param objectIds  关联对象id集合
     * @param objectType 关联对象类型
     * @return 查询到的评价记录
     */
    List<DtoEvaluationRecord> findByObjectIdInAndObjectType(Collection<String> objectIds, Integer objectType);

    /**
     * 根据关联对象id和关联测试项目找到对应的评价记录
     * @param objectIds 对象id集合
     * @param testIds 测试项目id集合
     * @return 评价记录
     */
    List<DtoEvaluationRecord> findByObjectIdInAndTestIdIn(Collection<String> objectIds,Collection<String> testIds);

    /**
     * 根据关联对象id找到对应的评价记录
     * @param objectIds 对象id集合
     * @return 评价记录
     */
    List<DtoEvaluationRecord> findByObjectIdIn(Collection<String> objectIds);
}