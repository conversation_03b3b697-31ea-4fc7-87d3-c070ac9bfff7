package com.sinoyd.lims.lim.dto.customer;

import com.sinoyd.lims.lim.dto.lims.DtoInstrumentUseRecord;
import lombok.Data;

import java.util.List;

/**
 * 现场任务批量新增仪器使用记录接口传输对象
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2025/03/05
 */
@Data
public class DtoInstrumentUseRecordBath {

    /**
     * 环境院记录id
     */
    private String environmentalRecordId;

    /**
     * 仪器使用记录对象列表
     */
    private List<DtoInstrumentUseRecord> useRecordList;
}
