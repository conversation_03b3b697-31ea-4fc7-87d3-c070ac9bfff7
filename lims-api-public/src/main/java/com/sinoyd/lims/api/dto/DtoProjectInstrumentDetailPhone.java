package com.sinoyd.lims.api.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 仪器出入库详情移动端实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
@Data
public class DtoProjectInstrumentDetailPhone {

    /**
     * 详情记录
     */
    private String id;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 仪器编码
     */
    private String instrumentCode;

    /**
     * 规格型号
     */
    private String model;

    /**
     * 出入库id
     */
    private String projectInstrumentId;

    /**
     * 仪器id
     */
    private String instrumentId;

    /**
     * 仪器id列表
     */
    private List<String> instrumentIdList;

    /**
     * 出库人id
     */
    private String outPerson;

    /**
     * 出库人姓名
     */
    private String outPersonName;

    /**
     * 出库日期
     */
    private Date outDate;

    /**
     * 出库合格情况，0 - 不合格，1 - 合格
     */
    private Integer outQualified;

    /**
     * 入库人id
     */
    private String inPerson;

    /**
     * 入库人姓名
     */
    private String inPersonName;

    /**
     * 入库日期
     */
    private Date inDate;

    /**
     * 入库合格情况，0 - 不合格，1 - 合格
     */
    private Integer inQualified;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 使用人名称
     */
    private String userNames;

    /**
     * 出入库状态
     */
    private Boolean isStorage;

    /**
     * 制造厂商名称
     */
    private String factoryName;

    /**
     * 删除记录ids
     */
    private List<String> ids;

    public DtoProjectInstrumentDetailPhone() {
    }

    public DtoProjectInstrumentDetailPhone(String id, String projectInstrumentId, String instrumentId, String outPerson, Date outDate, Integer outQualified, String inPerson,
                                           Date inDate, Integer inQualified, String projectName, String userNames, Boolean isStorage) {
        this.id = id;
        this.projectInstrumentId = projectInstrumentId;
        this.instrumentId = instrumentId;
        this.outPerson = outPerson;
        this.outDate = outDate;
        this.outQualified = outQualified;
        this.inPerson = inPerson;
        this.inDate = inDate;
        this.inQualified = inQualified;
        this.projectName = projectName;
        this.userNames = userNames;
        this.isStorage = isStorage;
    }
}
