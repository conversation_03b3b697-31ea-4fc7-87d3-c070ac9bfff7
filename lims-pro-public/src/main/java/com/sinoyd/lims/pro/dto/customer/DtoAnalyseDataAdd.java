package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.lims.lim.dto.lims.DtoPerson2Test;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.lims.DtoTestExpand;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoQualityControl;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoSample;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 指标添加装载数据结构
 * <AUTHOR>
 * @version V1.0.0 2020/6/13
 * @since V100R001
 */
@Data
public class DtoAnalyseDataAdd {

    /**
     * 构造函数
     */
    public DtoAnalyseDataAdd() {

    }

    /**
     * 构造函数
     *
     * @param project             项目
     * @param sample              样品
     * @param receiveSampleRecord 送样单
     * @param isCheckSample       是否纠正状态
     */
    public DtoAnalyseDataAdd(DtoProject project, List<DtoSample> sample, List<DtoReceiveSampleRecord> receiveSampleRecord, Boolean isCheckSample) {
        this.project = project;
        this.sample = sample;
        this.receiveSampleRecord = receiveSampleRecord;
        this.isCheckSample = isCheckSample;
    }

    /**
     * 构造函数
     *
     * @param project       项目
     * @param sample        样品
     * @param test          指标
     * @param isCheckSample 是否纠正状态
     */
    public DtoAnalyseDataAdd(DtoProject project, DtoSample sample, List<DtoTest> test, Boolean isCheckSample) {
        //排除已被删除的指标
        test = test.stream().filter(p -> !p.getIsDeleted()).collect(Collectors.toList());

        this.project = project;
        this.sample.add(sample);
        this.test = test;
        this.isCheckSample = isCheckSample;

        this.sample2Test.put(sample.getId(), test);
    }

    /**
     * 构造函数
     *
     * @param project             项目
     * @param sample              样品
     * @param receiveSampleRecord 送样单
     * @param test                指标
     * @param isCheckSample       是否纠正状态
     */
    public DtoAnalyseDataAdd(DtoProject project, DtoSample sample, DtoReceiveSampleRecord receiveSampleRecord, List<DtoTest> test, Boolean isCheckSample) {
        //排除已被删除的指标
        test = test.stream().filter(p -> !p.getIsDeleted()).collect(Collectors.toList());

        this.project = project;
        this.sample.add(sample);
        this.receiveSampleRecord.add(receiveSampleRecord);
        this.test = test;
        this.isCheckSample = isCheckSample;

        this.sample2Test.put(sample.getId(), test);
    }

    /**
     * 构造函数，所有样品添加相同的测试项目
     *
     * @param project             项目
     * @param sample              样品
     * @param receiveSampleRecord 送样单
     * @param isCheckSample       是否纠正状态
     */
    public DtoAnalyseDataAdd(DtoProject project, List<DtoSample> sample, DtoReceiveSampleRecord receiveSampleRecord, List<DtoTest> test, Boolean isCheckSample) {
        //排除已被删除的指标
        test = test.stream().filter(p -> !p.getIsDeleted()).collect(Collectors.toList());

        this.project = project;
        this.sample = sample;
        this.receiveSampleRecord.add(receiveSampleRecord);
        this.test = test;
        this.isCheckSample = isCheckSample;

        for (DtoSample s : sample) {
            this.sample2Test.put(s.getId(), test);
        }
    }

    /**
     * 构造函数，所有样品添加相同的测试项目
     *
     * @param project             项目
     * @param sample              样品
     * @param receiveSampleRecord 送样单
     * @param test                指标
     * @param isCheckSample       是否纠正状态
     */
    public DtoAnalyseDataAdd(DtoProject project, List<DtoSample> sample, List<DtoReceiveSampleRecord> receiveSampleRecord, List<DtoTest> test, Boolean isCheckSample) {
        //排除已被删除的指标
        test = test.stream().filter(p -> !p.getIsDeleted()).collect(Collectors.toList());

        this.project = project;
        this.sample = sample;
        this.receiveSampleRecord = receiveSampleRecord;
        this.test = test;
        this.isCheckSample = isCheckSample;

        for (DtoSample s : sample) {
            this.sample2Test.put(s.getId(), test);
        }
    }

    /**
     * 样品id--仅接收参数用
     */
    private String sampleId;

    /**
     * 测试项目id集合-仅接收参数用
     */
    private List<String> testIds = new ArrayList<>();

    /**
     * 是否同点位添加-仅接收参数用
     */
    private Boolean isAddForFolder = false;

    /**
     * 是否添加关联样测试项目
     */
    private Boolean isAddAssociateTest = true;

    /**
     * 是否限制唯一测试项目
     */
    private Boolean isUniqueTest = true;

    /**
     * 项目
     */
    private DtoProject project;

    /**
     * 样品
     */
    private List<DtoSample> sample = new ArrayList<>();

    /**
     * 关联样品
     */
    private List<DtoSample> localAssociateSample = new ArrayList<>();

    /**
     * 送样单
     */
    private List<DtoReceiveSampleRecord> receiveSampleRecord = new ArrayList<>();

    /**
     * 测试项目
     */
    private List<DtoTest> test = new ArrayList<>();

    /**
     * 是否纠正状态
     */
    private Boolean isCheckSample = true;

    /**
     * 样品与指标的关联
     */
    private Map<String, List<DtoTest>> sample2Test = new HashMap<>();

    /**
     * 测试项目id与测试项目扩展的关联
     */
    private Map<String, DtoTestExpand> test2TestExpand = new HashMap<>();

    /**
     * 测试项目id与第一人员的关联
     */
    private Map<String, DtoPerson2Test> person2TestMap = new HashMap<>();

    /**
     * 样品id与已存在的指标关联
     */
    private Map<String, List<String>> sample2ExistTest = new HashMap<>();

    /**
     * 样品id与质控信息关联
     */
    private Map<String, DtoQualityControl> sample2QualityControl = new HashMap<>();

    /**
     * 获取所有指标
     *
     * @return 指标
     */
    public List<String> getAllTestIds() {
        return test.stream().map(DtoTest::getId).distinct().collect(Collectors.toList());
    }

    /**
     * 添加样品指标
     *
     * @param sampleId 样品id
     * @param testList 指标
     */
    public void putSampleTestList(String sampleId, List<DtoTest> testList) {
        //排除已被删除的指标
        testList = testList.stream().filter(p -> !p.getIsDeleted()).collect(Collectors.toList());
        this.sample2Test.put(sampleId, testList);
        for (DtoTest dto : testList) {
            if (this.test.stream().noneMatch(p -> p.getId().equals(dto.getId()))) {
                this.test.add(dto);
            }
        }
    }

    /**
     * 设置测试扩展
     *
     * @param testExpandList 测试扩展列表
     */
    public void setTestExpandList(List<DtoTestExpand> testExpandList) {
        this.test2TestExpand = testExpandList.stream().collect(Collectors.toMap(DtoTestExpand::getTestId, expand -> expand));
    }

    /**
     * 设置测试人员配置
     *
     * @param p2tList 测试人员配置列表
     */
    public void setPerson2TestList(List<DtoPerson2Test> p2tList) {
        this.person2TestMap = p2tList.stream().collect(Collectors.toMap(DtoPerson2Test::getTestId, p2t -> p2t));
    }

    /**
     * 设置样品已存在指标
     *
     * @param sampleId 样品id
     * @param testId   指标id
     */
    public void putSampleTestId(String sampleId, String testId) {
        if (this.sample2ExistTest.containsKey(sampleId)) {
            this.sample2ExistTest.get(sampleId).add(testId);
        } else {
            List<String> testIds = new ArrayList<>();
            testIds.add(testId);
            this.sample2ExistTest.put(sampleId, testIds);
        }
    }

    /**
     * 是否存在样品指标
     *
     * @param sampleId 样品id
     * @param testId   指标id
     * @return 是否存在
     */
    public Boolean existTest(String sampleId, String testId) {
        if (this.sample2ExistTest.containsKey(sampleId)) {
            return this.sample2ExistTest.get(sampleId).contains(testId);
        }
        return false;
    }

    /**
     * 是否存在样品指标
     *
     * @param sampleId 样品id
     * @param analyzeItemId   因子Id
     * @return 是否存在
     */
    public Boolean existItem(String sampleId, String analyzeItemId,List<DtoTest> allTestList) {
        if (this.sample2ExistTest.containsKey(sampleId)) {
            List<String> existTestIds = this.sample2ExistTest.get(sampleId);
            List<String> existItemIds = allTestList.stream().filter(v->existTestIds.contains(v.getId())).map(DtoTest::getAnalyzeItemId).collect(Collectors.toList());
            return existItemIds.contains(analyzeItemId);
        }
        return false;
    }

    /**
     * 设置样品的质控关联
     *
     * @param sampleId       样品id
     * @param qualityControl 质控信息
     */
    public void putSampleQualityControl(String sampleId, DtoQualityControl qualityControl) {
        this.sample2QualityControl.put(sampleId, qualityControl);
    }
}
