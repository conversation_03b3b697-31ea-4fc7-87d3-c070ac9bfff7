package com.sinoyd.lims.monitor.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.base.dto.customer.PoiBaseEntity;
import lombok.Data;

/**
 * 环境质量点位实体
 *
 * @version V1.0.0 2023/12/27
 * @author: hukq
 * @since V100R001
 */
@Data
public class DtoExpImpFixedPointHJ extends PoiBaseEntity {

    /**
     * 主键id
     */
    @Excel(name = "主键id(新增时不填)", orderNum = "90", width = 24)
    private String id;

    /**
     * 点位类型名称
     */
    @Excel(name = "点位类型(必填)", orderNum = "100", width = 24)
    private String folderTypeName;

    /**
     * 点位类型
     */
    private String folderType;

    /**
     * 类型（枚举：环境质量 1 污染源 2）
     */
    private Integer pointType = 1;

    /**
     * 点位编号
     */
    @Excel(name = "点位编号", orderNum = "200", width = 24)
    private String pointCode;

    /**
     * 点位名称
     */
    @Excel(name = "点位名称(必填)", orderNum = "300", width = 22)
    private String pointName;

    /**
     * 测站名称
     */
    @Excel(name = "所属测站(必填)", orderNum = "400", width = 22)
    private String stationName;

    /**
     * 测站id
     */
    private String stationId;

    /**
     * 等级显示名称
     */
    @Excel(name = "等级", orderNum = "500", width = 18)
    private String levelName;

    /**
     * 等级 常量
     */
    private String level;

    /**
     * 经度
     */
    @Excel(name = "经度", orderNum = "600", width = 18)
    private String lon;

    /**
     * 纬度
     */
    @Excel(name = "纬度", orderNum = "700", width = 18)
    private String lat;

    /**
     * 地址
     */
    @Excel(name = "位置", orderNum = "800", width = 22)
    private String villageCode;


    /**
     * 所属区域(省)
     */
    @Excel(name = "所属区域(省)", orderNum = "810", width = 22)
    private String provinceAreaName;

    /**
     * 省区域id
     */
    private String provinceId;

    /**
     * 所属区域(市)
     */
    @Excel(name = "所属区域(市)", orderNum = "820", width = 22)
    private String cityAreaName;

    /**
     * 市区域id
     */
    private String cityId;

    /**
     * 所属区域(县、区)
     */
    @Excel(name = "所属区域(县、区)", orderNum = "830", width = 22)
    private String areaName;

    /**
     * 县,区区域id
     */
    private String areaId;


    /**
     * 启用状态
     */
    @Excel(name = "启用状态(是,否)", orderNum = "900", width = 22)
    private String enabledStatus;

    /**
     * 排序值
     */
    @Excel(name = "排序值",orderNum = "905",width = 22)
    private Integer orderNum;

    /**
     * 备注
     */
    @Excel(name = "备注", orderNum = "910", width = 22)
    private String remark;

    /**
     * 是否启用
     */
    private Boolean isEnabled = true;

}
