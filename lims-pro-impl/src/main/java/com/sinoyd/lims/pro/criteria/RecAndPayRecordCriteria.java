package com.sinoyd.lims.pro.criteria;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 收付款记录
 * <AUTHOR>
 * @version V1.0.0 2019年3月4日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RecAndPayRecordCriteria extends BaseCriteria {

    /**
     * 收款类型（1收款  3坏账)
     */
    private Integer moneyType;

    /**
     * 合同信息（合同编号、合同名称）
     */
    private String key;

    /**
     * 业务员
     */
    private List<String> salesPersonIds;

    /**
     * 是否到款
     */
    private Boolean isReceive;

    /**
     * 是否开票
     */
    private Boolean hasInvoice;

    /**
     * 合同id
     */
    private String contractId;

    /**
     * 甲方名称
     */
    private String firstEntName;

    /**
     * 日期区间开始
     */
    private String dtBegin;

    /**
     * 日期区间结束
     */
    private String dtEnd;

    /**
     * 是否为开票日期
     */
    private Boolean isInvoice;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        condition.append(" and p.contractId = o.id ");
        //合同关联
        if (StringUtil.isNotEmpty(contractId)) {
            condition.append(" and p.contractId = :contractId ");
            values.put("contractId", this.contractId);
        }
        //合同信息
        if (StringUtil.isNotEmpty(key)) {
            condition.append(" and (o.contractCode like :key or o.contractName like :key)");
            values.put("key", "%" + this.key + "%");
        }
        //业务员
        if (salesPersonIds != null && !salesPersonIds.isEmpty()) {
            condition.append(" and exists(select 1 from DtoOrderContract oc where p.contractId = oc.id and ( ");
            for (int i = 0; i < this.salesPersonIds.size(); i++) {
                String signPersonId = this.salesPersonIds.get(i);
                String key = "salesPersonId" + i;
                condition.append(" oc.signPersonId like :").append(key);
                values.put(key, "%" + signPersonId + "%");
                if (i < this.salesPersonIds.size() - 1) {
                    condition.append(" or ");
                }
            }
            condition.append(" ))");
        }
        //收款类型
        if (moneyType != null) {
            condition.append(" and p.moneyType = :moneyType ");
            values.put("moneyType", this.moneyType);
        }
        //是否到款
        if (isReceive != null) {
            condition.append(" and p.isReceive = :isReceive ");
            values.put("isReceive", this.isReceive);
        }
        //是否开票
        if (hasInvoice != null) {
            condition.append(" and p.hasInvoice = :hasInvoice ");
            values.put("hasInvoice", hasInvoice);
        }

        //甲方名称
        if (StringUtil.isNotEmpty(firstEntName)) {
            condition.append(" and o.firstEntName like :firstEntName");
            values.put("firstEntName", "%" + this.firstEntName + "%");
        }
        //日期区间
        if (isInvoice != null) {
            if (isInvoice) {
                //选择“开票日期”不统计未开票的数据
                condition.append(" and p.hasInvoice = :hasInvoice ");
                values.put("hasInvoice", true);
                //开票日期区间
                if (StringUtils.isNotNullAndEmpty(dtBegin)) {
                    condition.append(" and p.invoiceDate >= :dtBegin");
                    values.put("dtBegin", DateUtil.stringToDate(this.dtBegin, DateUtil.YEAR));
                }
                if (StringUtils.isNotNullAndEmpty(dtEnd)) {
                    Date to = DateUtil.stringToDate(this.dtEnd, DateUtil.YEAR);
                    Calendar c = Calendar.getInstance();
                    c.setTime(to);
                    c.add(Calendar.DAY_OF_YEAR, 1);
                    condition.append(" and p.invoiceDate < :dtEnd");
                    values.put("dtEnd", c.getTime());
                }
            } else {
                //选择“到款日期”不统计未到款的数据
                condition.append(" and p.isReceive = :isReceive ");
                values.put("isReceive", true);
                //到款日期区间
                if (StringUtils.isNotNullAndEmpty(dtBegin)) {
                    condition.append(" and p.receiveDate >= :dtBegin");
                    values.put("dtBegin", DateUtil.stringToDate(this.dtBegin, DateUtil.YEAR));
                }
                if (StringUtils.isNotNullAndEmpty(dtEnd)) {
                    Date to = DateUtil.stringToDate(this.dtEnd, DateUtil.YEAR);
                    Calendar c = Calendar.getInstance();
                    c.setTime(to);
                    c.add(Calendar.DAY_OF_YEAR, 1);
                    condition.append(" and p.receiveDate < :dtEnd");
                    values.put("dtEnd", c.getTime());
                }
            }
        }
        return condition.toString();
    }
}