package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.pro.dto.DtoQuotationDetail2Test;
import com.sinoyd.lims.pro.repository.QuotationDetail2TestRepository;
import com.sinoyd.lims.pro.service.QuotationDetail2TestService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.stereotype.Service;


/**
 * QuotationDetail2Test操作接口
 * <AUTHOR>
 * @version V1.0.0 2022/5/23
 * @since V100R001
 */
 @Service
public class QuotationDetail2TestServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoQuotationDetail2Test,String,QuotationDetail2TestRepository> implements QuotationDetail2TestService {

    @Override
    public void findByPage(PageBean<DtoQuotationDetail2Test> pb, BaseCriteria quotationDetail2TestCriteria) {
        pb.setEntityName("DtoQuotationDetail2Test a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, quotationDetail2TestCriteria);
    }
}