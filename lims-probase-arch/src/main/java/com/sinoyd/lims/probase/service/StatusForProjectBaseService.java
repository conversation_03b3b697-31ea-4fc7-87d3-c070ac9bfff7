package com.sinoyd.lims.probase.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoProjectPlan;
import com.sinoyd.lims.pro.dto.DtoStatusForProject;
import com.sinoyd.lims.pro.enums.EnumPRO;

public interface StatusForProjectBaseService extends IBaseJpaService<DtoStatusForProject, String> {
    /**
     * 创建状态
     *
     * @param projectId 项目id
     * @param module    模块编码
     */
    void createStatus(String projectId, String module);

    /**
     * 新增状态
     * @param projectId 项目id
     * @param module 模块编码
     * @return 状态信息
     */
    DtoStatusForProject statusForProject(String projectId, String module);

    /**
     * 办结项目状态
     *
     * @param project 项目
     */
    void finishStatus(DtoProject project);

    /**
     * 核对项目状态
     *
     * @param project 项目
     */
    void checkStatus(DtoProject project);

    /**
     * 获取通用待处理状态
     *
     * @param project       项目实体
     * @param projectStatus 比较的状态
     */
    Integer getModuleStatus(DtoProject project, EnumPRO.EnumProjectStatus projectStatus);

    /**
     * 获取项目登记待处理状态
     *
     * @param project 项目实体
     */
    Integer getRegisterStatus(DtoProject project);

    /**
     * 获取采样准备待处理状态
     *
     * @param project 项目实体
     * @param plan    项目计划实体
     */
    Integer getPrepareStatus(DtoProject project, DtoProjectPlan plan);

    /**
     * 获取委托现场送样待处理状态
     *
     * @param project 项目实体
     * @param plan    项目计划实体
     */
    Integer getLocalSendStatus(DtoProject project, DtoProjectPlan plan);

    /**
     * 获取编制报告待处理状态
     *
     * @param project 项目实体
     */
    Integer getReportStatus(DtoProject project);

    /**
     * 获取项目办结待处理状态
     *
     * @param project 项目实体
     */
    Integer getProjectEndStatus(DtoProject project);
}
