package com.sinoyd.base.interceptor;

import com.sinoyd.base.filter.RequestWrapper;
import com.sinoyd.boot.auth.common.dto.IJwt;
import com.sinoyd.boot.auth.common.util.JwtUtil;
import com.sinoyd.boot.auth.server.config.JwtUserConfig;
import com.sinoyd.boot.common.util.JsonUtil;
import com.sinoyd.boot.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 接口防抖拦截器
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/10/31
 **/
@Component
@Slf4j
public class AvoidResubmitInterceptor implements HandlerInterceptor {

    private RedisTemplate redisTemplate;
    private JwtUserConfig jwtUserConfig;

    @Value("${avoid.resubmit.enabled:false}")
    private boolean avoidResubmit;

    @Value("${avoid.resubmit.excludeUri:}")
    private String excludeUri;

    @Override
    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o) throws Exception {
        String token = httpServletRequest.getHeader("Authorization");
        String uri = httpServletRequest.getRequestURI();
        boolean isExclude = false;
        if(StringUtil.isNotEmpty(excludeUri)) {
            String[] excludeUris = excludeUri.split(",");
            for(String excludeUri : excludeUris) {
                if(uri.startsWith(excludeUri)) {
                    isExclude = true;
                    break;
                }
            }
        }

        String requestMethod = httpServletRequest.getMethod();
        if (avoidResubmit && (HttpMethod.POST.name().equals(requestMethod) || HttpMethod.PUT.name().equals(requestMethod) || HttpMethod.DELETE.name().equals(requestMethod)) && StringUtil.isNotEmpty(token) && !isExclude) {
            IJwt jwt = JwtUtil.getJwtFromToken(token, jwtUserConfig.getUserPubKey());
            String loginId = jwt.getLoginID();
            String redisKey = loginId + ":" + requestMethod + ":" + httpServletRequest.getRequestURL();

            RequestWrapper requestWrapper = new RequestWrapper(httpServletRequest);
            String body = requestWrapper.getBody();
            Enumeration<String> paramNames = requestWrapper.getParameterNames();

            List<Object> paramValues = new ArrayList<>();
            while (paramNames.hasMoreElements()) {
                String paramName = paramNames.nextElement();
                paramValues.add(requestWrapper.getParameter(paramName));
            }
            if(StringUtil.isNotEmpty(paramValues)){
                String paramJson = JsonUtil.toJson(paramValues);
                if (StringUtil.isNotEmpty(body)) {
                    body = body + ":" + paramJson;
                }else{
                    body = paramJson;
                }
            }

            if (redisTemplate.hasKey(redisKey)) {
                List<Object> values = redisTemplate.opsForList().range(redisKey, 0, -1);
                if (StringUtil.isNotEmpty(values) && values.contains(body)) {
                    throw new RuntimeException("请勿过于频繁操作!");
                }
            }else{
                redisTemplate.opsForList().leftPush(redisKey, body);
                redisTemplate.expire(redisKey, 1000, TimeUnit.MILLISECONDS);
            }
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) throws Exception {

    }

    @Autowired
    public void setRedisTemplate(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Autowired
    public void setJwtUserConfig(JwtUserConfig jwtUserConfig) {
        this.jwtUserConfig = jwtUserConfig;
    }
}
