package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoNewSearchResult;
import com.sinoyd.lims.lim.dto.lims.DtoNewSearchTask;

import java.util.Map;

/**
 * 查新任务接口
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
public interface NewSearchTaskService extends IBaseJpaService<DtoNewSearchTask, String> {

    /**
     * 提交查新任务
     *
     * @param submitMap 提交参数
     * @return 查新任务实体
     */
    DtoNewSearchTask submit(Map<String,Object> submitMap);
}
