package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.io.Serializable;
import java.util.*;

/**
 * 报告样品选择查询结构体
 * <AUTHOR>
 * @version V1.0.0 2019/11/14
 * @since V100R001
 */
@Data
public class DtoReportDetailQuery implements Serializable {    
    private static final long serialVersionUID = 1L;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 报告id
     */
    private String reportId;

    /**
     * 报告id集合
     */
    private List<String> reportIds = new ArrayList<>();

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 关键字 样品编号、点位名称
     */
    private String key;

    /**
     * 样品状态
     */
    private Integer status;

    /**
     * 出证状态
     */
    private Integer certificateStatus;
}