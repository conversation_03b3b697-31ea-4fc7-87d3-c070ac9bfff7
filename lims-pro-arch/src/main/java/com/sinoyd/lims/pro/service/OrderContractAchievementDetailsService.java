package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoOrderContractAchievementDetails;

import javax.servlet.http.HttpServletResponse;

/**
 *  合同绩效管理明细service
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2023/03/13
 */
public interface OrderContractAchievementDetailsService extends IBaseJpaService<DtoOrderContractAchievementDetails, String> {

    /**
     * 导出
     * @param response 响应
     * @param criteria 查询条件
     */
    void export(HttpServletResponse response, BaseCriteria criteria);

}
