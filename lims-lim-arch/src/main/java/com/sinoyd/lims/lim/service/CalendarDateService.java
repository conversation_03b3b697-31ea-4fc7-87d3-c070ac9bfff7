package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoCalendarDate;
import com.sinoyd.lims.lim.dto.rcc.DtoHolidayConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoWorkdayConfig;

import java.util.Date;
import java.util.List;

/**
 * 日历日期接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/1/19
 */
public interface CalendarDateService extends IBaseJpaService<DtoCalendarDate, String> {

    /**
     * 根据年份查询数据
     *
     * @param year 年份
     * @return 日历日期list
     */
    List<DtoCalendarDate> findByYear(Integer year);

    /**
     * 根据年份初始化日历日期数据
     *
     * @param year 年份
     * @return List<DtoCalendarDate>
     */
    List<DtoCalendarDate> initCalendarDate(Integer year);

    /**
     * 根据时间区间来查询数据
     *
     * @param beginDate 开始时间
     * @param endDate   结束时间
     * @return 日历日期List
     */
    List<DtoCalendarDate> findByCalendarDateBetween(Date beginDate, Date endDate);

    /**
     * 根据工作日配置刷新日历
     *
     * @param fromDate         开始日期
     * @param endDate          结束日期
     * @param dtoWorkdayConfig 工作日配置信息
     */
    void refreshCalendarDate(Date fromDate, Date endDate, DtoWorkdayConfig dtoWorkdayConfig);

    /**
     * 根据节假日配置刷新日历
     *
     * @param dtoHolidayConfigList 节假日list
     */
    void refreshCalendarDate(List<DtoHolidayConfig> dtoHolidayConfigList);

}