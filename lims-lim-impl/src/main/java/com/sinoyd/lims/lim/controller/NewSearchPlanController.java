package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.NewSearchPlanCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoNewSearchPlan;
import com.sinoyd.lims.lim.service.NewSearchPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 查新计划接口服务
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
@Api(tags = "查新计划接口服务")
@RestController
@RequestMapping("api/lim/newSearchPlan")
@Validated
public class NewSearchPlanController extends BaseJpaController<DtoNewSearchPlan, String, NewSearchPlanService> {
    /**
     * 分页动态条件查询newSearchPlan
     *
     * @param newSearchPlanCriteria 条件参数
     * @return RestResponse<List < newSearchPlan>>
     */
    @ApiOperation(value = "分页动态条件查询", notes = "分页动态条件查询")
    @GetMapping
    public RestResponse<List<DtoNewSearchPlan>> findByPage(NewSearchPlanCriteria newSearchPlanCriteria) {
        PageBean<DtoNewSearchPlan> pageBean = super.getPageBean();
        RestResponse<List<DtoNewSearchPlan>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, newSearchPlanCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 根据id查询查新计划
     *
     * @param id
     * @return 计划实体
     */
    @ApiOperation(value = "根据id查询查新计划", notes = "根据id查询查新计划")
    @GetMapping("/{id}")
    public RestResponse<DtoNewSearchPlan> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoNewSearchPlan> restResp = new RestResponse<>();
        DtoNewSearchPlan entity = service.findOne(id);
        restResp.setData(entity);
        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }


    /**
     * 新增查新计划
     *
     * @param newSearchPlan 查新计划实体
     * @return 新增的查新计划实体
     */
    @ApiOperation(value = "新增查新计划", notes = "新增查新计划")
    @PostMapping("")
    public RestResponse<DtoNewSearchPlan> create(@Validated @RequestBody DtoNewSearchPlan newSearchPlan) {
        RestResponse<DtoNewSearchPlan> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoNewSearchPlan data = service.save(newSearchPlan);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 提交查新计划
     *
     * @param id 主键id
     * @return 查新计划实体
     */
    @ApiOperation(value = "提交查新计划", notes = "提交查新计划")
    @PostMapping("/submit/{id}")
    public RestResponse<Map<String, Object>> submit(@PathVariable("id") String id) {
        RestResponse<Map<String, Object>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Map<String, Object> map = service.submit(id);
        restResponse.setData(map);
        return restResponse;
    }

    /**
     * 停用查新计划
     *
     * @param id 主键id
     * @return 查新计划实体
     */
    @ApiOperation(value = "停用查新计划", notes = "停用查新计划")
    @PostMapping("/deactivate/{id}")
    public RestResponse<String> deactivate(@PathVariable("id") String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        String string = service.deactivate(id);
        restResponse.setData(string);
        return restResponse;
    }




    /**
     * 更新查新计划
     *
     * @param newSearchPlan 查新计划实体
     * @return 更新后的查新计划实体
     */
    @ApiOperation(value = "更新查新计划", notes = "更新查新计划")
    @PutMapping("")
    public RestResponse<DtoNewSearchPlan> update(@Validated @RequestBody DtoNewSearchPlan newSearchPlan) {
        RestResponse<DtoNewSearchPlan> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoNewSearchPlan data = service.update(newSearchPlan);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 单个假删
     *
     * @param id 参数id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.logicDeleteById(id);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 批量假删
     *
     * @param ids 参数ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResponse.setCount(count);

        return restResponse;
    }


}
