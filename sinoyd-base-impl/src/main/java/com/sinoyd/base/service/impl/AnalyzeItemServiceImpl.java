package com.sinoyd.base.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.rcc.AnalyzeItemRepository;
import com.sinoyd.base.service.AnalyzeItemService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.PinYinUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分析项目管理接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2019/1/30
 * @since V100R001
 */
@Service
public class AnalyzeItemServiceImpl extends BaseJpaServiceImpl<DtoAnalyzeItem, String, AnalyzeItemRepository>
        implements AnalyzeItemService {

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    /**
     * 新增分析项目
     */
    @Override
    @Transactional
    public DtoAnalyzeItem save(DtoAnalyzeItem entity) {
        entity.setAnalyzeItemName(entity.getAnalyzeItemName().trim());
        Integer count = repository.getCountByName(entity.getId(), entity.getAnalyzeItemName());
        if (count > 0) {
            throw new BaseException("已存在相同名称的分析项目！");
        }
        if (StringUtils.isNotNullAndEmpty(entity.getAnalyzeItemName())) {
            entity.setFullPinYin(PinYinUtil.getFullSpell(entity.getAnalyzeItemName()));
            entity.setPinYin(PinYinUtil.getFirstSpell(entity.getAnalyzeItemName()));
        }
        DtoAnalyzeItem item = super.save(entity);
        saveRedis(item);
        return item;
    }

    /**
     * 修改分析项目
     */
    @Transactional
    @Override
    public DtoAnalyzeItem update(DtoAnalyzeItem entity) {
        entity.setAnalyzeItemName(entity.getAnalyzeItemName().trim());
        Integer count = repository.getCountByName(entity.getId(), entity.getAnalyzeItemName());
        if (count > 0) {
            throw new BaseException("已存在相同名称的分析项目！");
        }
        if (StringUtils.isNotNullAndEmpty(entity.getAnalyzeItemName())) {
            entity.setFullPinYin(PinYinUtil.getFullSpell(entity.getAnalyzeItemName()));
            entity.setPinYin(PinYinUtil.getFirstSpell(entity.getAnalyzeItemName()));
        }
        DtoAnalyzeItem item = super.update(entity);
        saveRedis(item);
        //利用通知的方式，告知相关的关联表，分析项目名称修改了
        redisTemplate.convertAndSend(EnumBase.EnumBASRedisChannel.BAS_AnalyzeItem_Update.name(), JsonStream.serialize(item));
        return item;
    }

    /**
     * 根据分析项目名称获取分析项目
     *
     * @param analyzeItemName 分析项目名称
     * @return 返回分析项目
     */
    @Override
    public DtoAnalyzeItem getByAnalyzeItemName(String analyzeItemName) {
        return repository.getByAnalyzeItemName(analyzeItemName);
    }

    @Override
    public DtoAnalyzeItem findOne(String id) {
        DtoAnalyzeItem dtoAnalyzeItem = super.findOne(id);
        saveRedis(dtoAnalyzeItem);
        return dtoAnalyzeItem;
    }

    /**
     * 根据分析项目id获取分析项目
     *
     * @param ids 分析项目ids
     * @return 返回分析项目
     */
    @Override
    public List<DtoAnalyzeItem> findByIds(Collection<String> ids) {
        return repository.findAll(ids);
    }

    @Override
    public List<DtoAnalyzeItem> findRedisByIds(List<String> ids) {
        String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_AnalyzeItem.getValue());
        List<Object> dataList = redisTemplate.opsForHash().multiGet(key, ids);
        return setMaps(dataList, ids);
    }

    @Override
    public List<DtoAnalyzeItem> findAllDeleted(List<String> ids) {
        return repository.findAllDeleted(ids);
    }

    @Override
    public List<DtoAnalyzeItem> findAllDeleted() {
        return repository.findAllDeleted();
    }

    /**
     * 为分析项目存在,拼音不存在的分析项目添加全拼
     *
     * @return
     */
    @Transactional
    @Override
    public List<DtoAnalyzeItem> changePinYinFull() {
        List<DtoAnalyzeItem> dtoAnalyzeItems = repository.findByAnalyzeItemNameNotNullAndFullPinYinIsNull();
        if (StringUtil.isNotEmpty(dtoAnalyzeItems)) {
            for (DtoAnalyzeItem dtoAnalyzeItem : dtoAnalyzeItems) {
                dtoAnalyzeItem.setFullPinYin(PinYinUtil.getFullSpell(dtoAnalyzeItem.getAnalyzeItemName()));
                dtoAnalyzeItem.setPinYin(PinYinUtil.getFirstSpell(dtoAnalyzeItem.getAnalyzeItemName()));
            }
            int max = 0;
            for (DtoAnalyzeItem dtoAnalyzeItem : dtoAnalyzeItems) {
                int length = dtoAnalyzeItem.getFullPinYin().length();
                if (max < length) {
                    max = length;
                }
            }
            return super.update(dtoAnalyzeItems);
        }
        return null;
    }


    @Override
    public void findByPage(PageBean<DtoAnalyzeItem> page, BaseCriteria criteria) {
        // 设置查询的实体类名及别名
        page.setEntityName("DtoAnalyzeItem x");
        // 设置查询返回的字段、实体别名表示所有字段
        page.setSelect("select x");

        super.findByPage(page, criteria);
    }

    @Override
    @Transactional
    public <K extends Serializable> Integer logicDeleteById(K id) {
        return this.logicDeleteById(Collections.singletonList(id));
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<?> ids) {
        verifyDelete(ids);
        return super.logicDeleteById(ids);
    }

    private void verifyDelete(Collection<?> ids) {
        List<DtoAnalyzeItem> analyzeItems = StringUtil.isNotEmpty(ids) ? repository.findAll((List<String>) ids) : new ArrayList<>();
        List<String> notPassIds = new ArrayList<>();
        for (Object id : ids) {
            String sql = "SELECT COUNT(1) FROM TB_LIM_Test WHERE analyzeItemId = :analyzeItemId AND isDeleted = 0";
            Integer count = namedParameterJdbcTemplate.queryForObject(sql, Collections.singletonMap("analyzeItemId", id), Integer.class);
            if (count > 0) {
                notPassIds.add(id.toString());
            }
        }
        if (StringUtil.isNotEmpty(notPassIds)) {
            throw new BaseException(String.format("分析项目[%s]下存在测试项目，不能删除", analyzeItems.stream().filter(a -> notPassIds.contains(a.getId())).map(DtoAnalyzeItem::getAnalyzeItemName).collect(Collectors.joining(","))));
        }
    }

    /**
     * 保存相应的redis数据
     *
     * @param item 分析项目的实体对象
     */
    private void saveRedis(DtoAnalyzeItem item) {
        String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_AnalyzeItem.getValue());
        redisTemplate.opsForHash().put(key, item.getId(), JsonStream.serialize(item));
    }

    /**
     * 从redis中获取相应的数据
     *
     * @param dataList 数据集合
     * @return 返回测试项目数据
     */
    private List<DtoAnalyzeItem> setMaps(List<Object> dataList, List<String> ids) {
        List<DtoAnalyzeItem> itemList = new ArrayList<>();
        TypeLiteral<DtoAnalyzeItem> typeLiteral = new TypeLiteral<DtoAnalyzeItem>() {
        };
        List<String> existIds = new ArrayList<>();
        for (Object s : dataList) {
            if (StringUtil.isNotNull(s)) {
                try {
                    DtoAnalyzeItem item = JsonIterator.deserialize(s.toString(), typeLiteral);
                    if (StringUtil.isNotNull(item)) {
                        existIds.add(item.getId());
                        itemList.add(item);
                    }
                } catch (Exception ex) {
                    System.out.println(ex.getMessage());
                }
            }
        }
        //将未缓存到redis的数据缓存起来（需要假删数据，防止业务库调用之后找不到这些数据）
        List<String> newIds = ids.stream().filter(p -> !existIds.contains(p)).collect(Collectors.toList());
        if (newIds.size() > 0) {
            List<DtoAnalyzeItem> dtoAnalyzeItems = repository.findAllDeleted(newIds);
            itemList.addAll(dtoAnalyzeItems);
            Map<String, Object> map = new HashMap<>();
            for (DtoAnalyzeItem dtoAnalyzeItem : dtoAnalyzeItems) {
                map.put(dtoAnalyzeItem.getId(), JsonStream.serialize(dtoAnalyzeItem));
            }
            String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_AnalyzeItem.getValue());
            redisTemplate.opsForHash().putAll(key, map);

        }
        return itemList;
    }
}