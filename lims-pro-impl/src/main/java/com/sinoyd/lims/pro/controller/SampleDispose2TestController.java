package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.SampleDispose2TestService;
import com.sinoyd.lims.pro.criteria.SampleDispose2TestCriteria;
import com.sinoyd.lims.pro.dto.DtoSampleDispose2Test;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * SampleDispose2Test服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2021/11/8
 * @since V100R001
 */
 @Api(tags = "示例: SampleDispose2Test服务")
 @RestController
 @RequestMapping("api/pro/sampleDispose2Test")
 public class SampleDispose2TestController extends BaseJpaController<DtoSampleDispose2Test, String,SampleDispose2TestService> {


    /**
     * 分页动态条件查询SampleDispose2Test
     * @param sampleDispose2TestCriteria 条件参数
     * @return RestResponse<List<SampleDispose2Test>>
     */
     @ApiOperation(value = "分页动态条件查询SampleDispose2Test", notes = "分页动态条件查询SampleDispose2Test")
     @GetMapping
     public RestResponse<List<DtoSampleDispose2Test>> findByPage(SampleDispose2TestCriteria sampleDispose2TestCriteria) {
         PageBean<DtoSampleDispose2Test> pageBean = super.getPageBean();
         RestResponse<List<DtoSampleDispose2Test>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, sampleDispose2TestCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询SampleDispose2Test
     * @param id 主键id
     * @return RestResponse<DtoSampleDispose2Test>
     */
     @ApiOperation(value = "按主键查询SampleDispose2Test", notes = "按主键查询SampleDispose2Test")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoSampleDispose2Test> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoSampleDispose2Test> restResponse = new RestResponse<>();
         DtoSampleDispose2Test sampleDispose2Test = service.findOne(id);
         restResponse.setData(sampleDispose2Test);
         restResponse.setRestStatus(StringUtil.isNull(sampleDispose2Test) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增SampleDispose2Test
     * @param sampleDispose2Test 实体列表
     * @return RestResponse<DtoSampleDispose2Test>
     */
     @ApiOperation(value = "新增SampleDispose2Test", notes = "新增SampleDispose2Test")
     @PostMapping
     public RestResponse<DtoSampleDispose2Test> create(@RequestBody @Validated DtoSampleDispose2Test sampleDispose2Test) {
         RestResponse<DtoSampleDispose2Test> restResponse = new RestResponse<>();
         restResponse.setData(service.save(sampleDispose2Test));
         return restResponse;
      }

     /**
     * 新增SampleDispose2Test
     * @param sampleDispose2Test 实体列表
     * @return RestResponse<DtoSampleDispose2Test>
     */
     @ApiOperation(value = "修改SampleDispose2Test", notes = "修改SampleDispose2Test")
     @PutMapping
     public RestResponse<DtoSampleDispose2Test> update(@RequestBody @Validated DtoSampleDispose2Test sampleDispose2Test) {
         RestResponse<DtoSampleDispose2Test> restResponse = new RestResponse<>();
         restResponse.setData(service.update(sampleDispose2Test));
         return restResponse;
      }

    /**
     * "根据id批量删除SampleDispose2Test
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除SampleDispose2Test", notes = "根据id批量删除SampleDispose2Test")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }