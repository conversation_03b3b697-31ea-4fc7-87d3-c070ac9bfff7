package com.sinoyd.base.entity;

import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;

/**
 * 企业排污许可证同步实体
 *
 * <AUTHOR>
 * @version V5.2.0 2025/05/06
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "PollutionDischargeSync")
@Data
@EntityListeners(AuditingEntityListener.class)
public class PollutionDischargeSync implements Serializable, Cloneable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(nullable = false)
    @ApiModelProperty("主键")
    private String id = UUIDHelper.NewID();

    /**
     * 企业id
     */
    @Column(nullable = false)
    @ApiModelProperty("企业id")
    private String enterpriseId;

    /**
     * 发起时间
     */
    @Column(nullable = false)
    @ApiModelProperty("发起时间")
    private Date requestTime;

    /**
     * 是否同步成功
     */
    @Column(nullable = false, columnDefinition = "b'0'")
    @ApiModelProperty("是否同步成功")
    private Boolean isSuccess;

    /**
     * 数据内容
     */
    @ApiModelProperty("数据内容")
    private String dataContent;

}