package com.sinoyd.lims.instrument.parse.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * 万维数据实体
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2021/7/26
 */
@MappedSuperclass
@ApiModel(description = "Tcp Data")
@Data
public class TcpData implements BaseEntity, Serializable {


    private static final long serialVersionUID = 1L;

    public TcpData() {
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 万维收数时间
     */
    @Column(nullable = false)
    @ApiModelProperty("万维收数时间")
    private Date wwRevDataTime;

    /**
     * 万维点位地址
     */
    @Column(length = 250)
    @ApiModelProperty("万维点位地址")
    private String wwStationLocation;

    /**
     * 万维点位名称
     */
    @Column(length = 250)
    @ApiModelProperty("万维点位名称")
    private String wwStationName;

    /**
     * 万维点位编码
     */
    @Column(length = 250)
    @ApiModelProperty("万维点位编码")
    private String wwStationId;

    /**
     * 万维经度
     */
    @Column(length = 50)
    @ApiModelProperty("万维经度")
    private String wwLng;

    /**
     * 万维纬度
     */
    @Column(length = 50)
    @ApiModelProperty("万维纬度")
    private String wwLat;

    /**
     * 万维设备编号
     */
    @Column(length = 250)
    @ApiModelProperty("万维设备编号")
    private String wwDevNum;

    /**
     * 万维设备名称
     */
    @Column(length = 250)
    @ApiModelProperty("万维设备名称")
    private String wwDevName;

    /**
     * 万维项目编号
     */
    @Column(length = 250)
    @ApiModelProperty("万维项目编号")
    private String wwProNo;

    /**
     * 万维项目名称
     */
    @Column(length = 250)
    @ApiModelProperty("万维项目名称")
    private String wwProName;

    /**
     * 万维乙方名称（受检单位名称）
     */
    @Column(length = 250)
    @ApiModelProperty("万维乙方名称（受检单位名称）")
    private String wwEnterpriseName;

    /**
     * 万维任务编号
     */
    @Column(length = 250)
    @ApiModelProperty("万维任务编号")
    private String wwTaskId;

    /**
     * 万维采样单总数
     */
    @Column(length = 250)
    @ApiModelProperty("万维采样单总数")
    private String wwSampleSh;

    /**
     * 万维采样单号
     */
    @Column(length = 250)
    @ApiModelProperty("万维采样单号")
    private String wwSampleId;

    /**
     * 参数名称
     */
    @Column(length = 250)
    @ApiModelProperty("参数名称")
    private String paramName;

    /**
     * 参数值
     */
    @Column(length = 250)
    @ApiModelProperty("参数值")
    private String paramvalue;

    /**
     * 参数单位
     */
    @Column(length = 250)
    @ApiModelProperty("参数单位")
    private String paramunit;

    /**
     * 数据解析时间
     */
    @Column(nullable = false)
    @ApiModelProperty("数据解析时间")
    private Date parseDataTime;

    /**
     * 参数单位
     */
    @Column(length = 250)
    @ApiModelProperty("参数单位")
    private String sampleNo;

    /**
     * 假删字段
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删字段")
    private Boolean isDeleted = false;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;
}
