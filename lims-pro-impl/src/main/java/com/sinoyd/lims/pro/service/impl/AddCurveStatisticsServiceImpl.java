package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoWorkSheetFolder;
import com.sinoyd.lims.pro.dto.customer.DtoQCDataDetail;
import com.sinoyd.lims.pro.entity.AnalyseData;
import com.sinoyd.lims.pro.repository.WorkSheetFolderRepository;
import com.sinoyd.lims.pro.service.AddCurveStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 加标曲线的统计
 *
 * <AUTHOR>
 * @version V1.0.0 2020/02/10
 * @since V100R001
 */
@Service
public class AddCurveStatisticsServiceImpl implements AddCurveStatisticsService {


    @Autowired
    private CommonRepository commonRepository;

    @Autowired
    @Lazy
    private PersonService personService;

    @Autowired
    private WorkSheetFolderRepository workSheetFolderRepository;

    @Override
    public Map<String, Object> findAddQCData(BaseCriteria baseCriteria) {
        StringBuilder stringBuilder = new StringBuilder("select a from DtoAnalyseData a,DtoSample b where 1=1 and a.isDeleted = 0 ");
        List<DtoAnalyseData> analyseDataList = commonRepository.find(stringBuilder.toString(), baseCriteria);
        //按分析时间排序
        analyseDataList = analyseDataList.stream().sorted(Comparator.comparing(AnalyseData::getAnalyzeTime)).collect(Collectors.toList());
        //分析人员id
        List<String> personIds = analyseDataList.stream().map(DtoAnalyseData::getAnalystId).distinct().collect(Collectors.toList());

        List<DtoPerson> personList = new ArrayList<>();
        if (personIds.size() > 0) {
            personList = personService.findAll(personIds);
        }
        List<String> workSheetFolderIds=analyseDataList.stream().filter(p->StringUtils.isNotNullAndEmpty(p.getWorkSheetFolderId())
                &&!p.getWorkSheetFolderId().equals(UUIDHelper.GUID_EMPTY)).map(DtoAnalyseData::getWorkSheetFolderId).distinct().collect(Collectors.toList());
        List<DtoWorkSheetFolder> workSheetFolders =new ArrayList<>();
        if (workSheetFolderIds.size()>0) {
            workSheetFolders = workSheetFolderRepository.findAll(workSheetFolderIds);
        }
        List<DtoQCDataDetail> qcDataDetails = new ArrayList<>();
        for (DtoAnalyseData ana : analyseDataList) {
            DtoQCDataDetail qcDataDetail = new DtoQCDataDetail();
            String anaId = ana.getId();
            String analystId = ana.getAnalystId();
            Date analyzeTime = ana.getAnalyzeTime();
            String analystName = ana.getAnalystName();
            DtoPerson dtoPerson = personList.stream().filter(p -> p.getId().equals(analystId)).findFirst().orElse(null);
            if (StringUtil.isNotNull(dtoPerson)) {
                analystName = dtoPerson.getCName();
            }
            String workSheetCode="";
            DtoWorkSheetFolder dtoWorkSheetFolder=workSheetFolders.stream().filter(p->p.getId().equals(ana.getWorkSheetFolderId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(dtoWorkSheetFolder)) {
                workSheetCode = dtoWorkSheetFolder.getWorkSheetCode();
            }
            qcDataDetail.setWorkSheetCode(workSheetCode);
            String testValue = ana.getTestValue();
            qcDataDetail.setId(anaId);
            qcDataDetail.setAnalystId(analystId);
            qcDataDetail.setAnalystName(analystName);
            qcDataDetail.setAnalyzeTime(analyzeTime);
            qcDataDetail.setTestValue(testValue);
            qcDataDetails.add(qcDataDetail);
        }
        Map<String, Object> map = new HashMap<>();
        map.put("person", personList);
        map.put("qcDataDetail", qcDataDetails);
        return map;
    }
}
