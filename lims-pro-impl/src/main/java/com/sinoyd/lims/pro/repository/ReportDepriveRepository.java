package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoReportDeprive;

import java.util.List;

/**
 * ReportDeprive数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2022/4/14
 * @since V100R001
 */
public interface ReportDepriveRepository extends IBaseJpaPhysicalDeleteRepository<DtoReportDeprive, String> {

    List<DtoReportDeprive> findByProjectIdIn(List<String> projectIds);

}
