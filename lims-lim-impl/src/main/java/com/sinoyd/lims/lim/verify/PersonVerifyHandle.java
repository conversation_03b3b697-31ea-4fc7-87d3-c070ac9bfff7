package com.sinoyd.lims.lim.verify;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.dto.customer.DtoImportPerson;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.frame.dto.DtoRole;
import com.sinoyd.lims.lim.dto.customer.DtoImportPersonExtend;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.utils.ImportUtils;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 人员导入数据校验
 *
 * <AUTHOR>
 * @version V1.0.0 2022/3/24
 * @since V100R001
 */
@Data
public class PersonVerifyHandle implements IExcelVerifyHandler<DtoImportPerson> {

    /**
     * 业务请求参数Map
     */
    private final Map<String, Boolean> relationMap;

    /**
     * 职务数据
     */
    private final List<DtoCode> postList;

    /**
     * 职称数据
     */
    private final List<DtoCode> titleList;

    /**
     * 学历数据
     */
    private final List<DtoCode> educationList;

    /**
     * 部门数据
     */
    private final List<DtoDepartment> departmentList;

    /**
     * 角色数据
     */
    private final List<DtoRole> roleList;

    /**
     * 人员数据
     */
    private final List<DtoPerson> personList;

    /**
     * 人员导入关联数据
     */
    private final List<DtoImportPersonExtend> personExtendList;

    /**
     * 导入临时数据，每校验一行存入当前校验的数据，用于判断导入数据重复
     */
    private List<DtoImportPerson> personTempList = new ArrayList<>();

    /**
     * 工具类
     */
    private final ImportUtils importUtils = new ImportUtils();


    /**
     * 构造方法
     *
     * @param relationMap      业务参数Map
     * @param postList         职务数据
     * @param titleList        职称数据
     * @param educationList    学历数据
     * @param departmentList   部门数据
     * @param roleList         角色数据
     * @param personList       人员数据
     * @param personExtendList 人员导入关联数据
     */
    public PersonVerifyHandle(Map<String, Boolean> relationMap, List<DtoCode> postList, List<DtoCode> titleList,
                              List<DtoCode> educationList, List<DtoDepartment> departmentList, List<DtoRole> roleList,
                              List<DtoPerson> personList, List<DtoImportPersonExtend> personExtendList) {
        this.relationMap = relationMap;
        this.postList = postList;
        this.titleList = titleList;
        this.educationList = educationList;
        this.departmentList = departmentList;
        this.roleList = roleList;
        this.personList = personList;
        this.personExtendList = personExtendList;
    }

    /**
     * 人员导入数据校验
     *
     * @param person 导入数据
     * @return 校验结果
     */
    @Override
    public ExcelVerifyHandlerResult verifyHandler(DtoImportPerson person) {
        //region 导入参数处理
        try {
            //去除空行
            if (StringUtil.isNotEmpty(person.getStatus())) {
                if ("null".contains(person.getStatus())) {
                    person.setStatus(null);
                }
            }
            if (importUtils.checkObjectIsNull(person)) {
                return new ExcelVerifyHandlerResult(true);
            }
            //去除首尾空格
            importUtils.strToTrim(person);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }
        defaultValue(person);
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        StringBuilder failStr = new StringBuilder("第" + (person.getRowNum() + 1) + "行数据校验错误");
        //必填校验
        importUtils.checkIsNull(result, person.getChineseName(), "人员名称", failStr);
        importUtils.checkIsNull(result, person.getSex(), "性别", failStr);
        importUtils.checkIsNull(result, person.getStatus(), "人员状态", failStr);
        //导入编号重复判断
        if (StringUtil.isEmpty(personTempList)) {
            personTempList = new ArrayList<>();
        }
        isRepeatData(result, personTempList, person, failStr);
        //判断对于开通账号的字段重复校验
        isRepeatByAccount(result, personTempList, person, failStr);
        //判断性别格式
        sexVerify(result, failStr, person);
        //判断是否存在职务
        isExistPost(result, person, postList, personExtendList, failStr);
        //判断是否存在职称
        isExistTitle(result, person, personExtendList, titleList, failStr);
        //判断是否存在学历
        isExistDegree(result, person, personExtendList, educationList, failStr);
        //判断是否存在部门
        isExistDept(result, person, departmentList, failStr);
        //判断是否存在角色
        isExistRole(result, person, roleList, failStr);
        //判断数据库中是否已经存在数据
        isExistData(result, person, personList, failStr);
        //验证数据格式
        importUtils.checkEmail(result, person.getEmail(), "邮箱", failStr);
        importUtils.checkNumTwo(result, person.getMobile(), "手机号", failStr);
        importUtils.checkIdNum(result, person.getIdCard(), "身份证号", failStr);
        importUtils.checkNumTwo(result, person.getContactMethod(), "联络方式", failStr);
        importUtils.checkDateTwo(result, person.getTechnicalTitleDate(), "职称获得日期", failStr);
        importUtils.checkDateTwo(result, person.getJoinPartyDate(), "入党日期", failStr);
        importUtils.checkDateTwo(result, person.getJoinCompanyTime(), "入职日期", failStr);
        importUtils.checkDateTwo(result, person.getLeaveCompanyTime(), "离职日期", failStr);
        importUtils.checkDateTwo(result, person.getBirthDay(), "出生日期", failStr);
        String failString = failStr.toString().replaceFirst("；", ":");
        result.setMsg(failString);
        personTempList.add(person);
        return result;
    }

    /**
     * 开通账号时判断用户名与邮箱是否重复
     *
     * @param result         校验结果
     * @param personTempList 导入的临时数据
     * @param person         当前校验的数据
     * @param failStr        校验错误信息
     */
    private void isRepeatByAccount(ExcelVerifyHandlerResult result, List<DtoImportPerson> personTempList, DtoImportPerson person, StringBuilder failStr) {
        if (relationMap.get("isCreateAccount")) {
            //用户名的重复校验
            if (StringUtil.isNotEmpty(person.getUserName())) {
                List<Integer> repeatNum = personTempList.stream().filter(p -> person.getUserName().equals(p.getUserName())).map(DtoImportPerson::getRowNum).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(repeatNum)) {
                    result.setSuccess(false);
                    failStr.append("；与第").append(repeatNum).append("行用户名重复");
                }
            }
            //邮箱的重复校验
            if (StringUtil.isNotEmpty(person.getEmail())) {
                List<Integer> repeatNum = personTempList.stream().filter(p -> person.getEmail().equals(p.getEmail())).map(DtoImportPerson::getRowNum).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(repeatNum)) {
                    result.setSuccess(false);
                    failStr.append("；与第").append(repeatNum).append("行邮箱重复重复");
                }
            }
        }
    }

    /**
     * 设置默认数据
     *
     * @param person 导入数据
     */
    private void defaultValue(DtoImportPerson person) {
        person.setTechnicalTitleId(StringUtil.isEmpty(person.getTechnicalTitleId()) ? "" : person.getTechnicalTitleId());
        person.setDegree(StringUtil.isEmpty(person.getDegree()) ? "" : person.getDegree());
    }

    /**
     * 性别格式验证
     *
     * @param result  校验结果
     * @param failStr 校验错误信息
     * @param person  导入数据
     */
    private void sexVerify(ExcelVerifyHandlerResult result, StringBuilder failStr, DtoImportPerson person) {
        if (StringUtil.isNotEmpty(person.getSex())) {
            if (!"男".equals(person.getSex()) && !"女".equals(person.getSex())) {
                result.setSuccess(false);
                failStr.append("；性别格式不正确");
            }
        }
    }

    /**
     * 部门校验
     *
     * @param result       校验结果
     * @param person       实体
     * @param dbDepartment 所有部门
     * @param failStr      校验错误信息
     */
    private void isExistDept(ExcelVerifyHandlerResult result, DtoImportPerson person, List<DtoDepartment> dbDepartment, StringBuilder failStr) {
        if (StringUtil.isEmpty(person.getDeptId())) {
            result.setSuccess(false);
            failStr.append("；部门不能为空");
            return;

        }
        List<DtoDepartment> departments = dbDepartment.stream().filter(p -> person.getDeptId().equals(p.getDeptName())).collect(Collectors.toList());
        if (StringUtil.isEmpty(departments)) {
            result.setSuccess(false);
            failStr.append("；部门不存在");
        }

    }

    /**
     * 职务校验
     *
     * @param result     校验结果
     * @param person     实体
     * @param dbPost     所有职务
     * @param extendList 关联数据
     * @param failStr    校验错误信息
     */
    private void isExistPost(ExcelVerifyHandlerResult result, DtoImportPerson person, List<DtoCode> dbPost, List<DtoImportPersonExtend> extendList, StringBuilder failStr) {
        if (StringUtil.isEmpty(person.getPostId())) {
            result.setSuccess(false);
            failStr.append("；职务不能为空");
            return;
        }
        if (!relationMap.get("isImportPost")) {
            List<DtoCode> postCode = dbPost.stream().filter(p -> person.getPostId().equals(p.getDictName())).collect(Collectors.toList());
            if (StringUtil.isEmpty(postCode)) {
                result.setSuccess(false);
                failStr.append("；职务不存在");
            }
        } else {
            List<String> postImport = extendList.stream().map(DtoImportPersonExtend::getPost).filter(StringUtil::isNotEmpty).collect(Collectors.toList());
            List<String> isExistTypeName = Stream.of(person.getPostId()).filter(p -> !postImport.contains(p)).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(isExistTypeName)) {
                result.setSuccess(false);
                failStr.append("；职务与关联表不对应");
            }
        }
    }


    /**
     * 职称校验
     *
     * @param result  校验结果
     * @param person  实体
     * @param dbTitle 所有职称
     * @param failStr 校验错误信息
     */
    private void isExistTitle(ExcelVerifyHandlerResult result, DtoImportPerson person, List<DtoImportPersonExtend> extendList, List<DtoCode> dbTitle, StringBuilder failStr) {
        if (StringUtil.isNotEmpty(person.getTechnicalTitleId())) {
            if (!relationMap.get("isImportTitle")) {
                List<DtoCode> postCode = dbTitle.stream().filter(p -> person.getTechnicalTitleId().equals(p.getDictName())).collect(Collectors.toList());
                if (StringUtil.isEmpty(postCode)) {
                    result.setSuccess(false);
                    failStr.append("；职称不存在");
                }
            } else {
                List<String> titleImport = extendList.stream().map(DtoImportPersonExtend::getTitle).filter(StringUtil::isNotEmpty).collect(Collectors.toList());
                List<String> isExistTypeName = Stream.of(person.getTechnicalTitleId()).filter(p -> !titleImport.contains(p)).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(isExistTypeName)) {
                    result.setSuccess(false);
                    failStr.append("；职称与关联表不对应");
                }
            }
        }
    }

    /**
     * 学历校验
     *
     * @param result      校验结果
     * @param person      实体
     * @param dbEducation 所有学历
     * @param failStr     校验错误信息
     */
    private void isExistDegree(ExcelVerifyHandlerResult result, DtoImportPerson person, List<DtoImportPersonExtend> extendList, List<DtoCode> dbEducation, StringBuilder failStr) {
        if (StringUtil.isNotEmpty(person.getDegree())) {
            if (!relationMap.get("isImportEducation")) {
                List<DtoCode> postCode = dbEducation.stream().filter(p -> person.getDegree().equals(p.getDictName())).collect(Collectors.toList());
                if (StringUtil.isEmpty(postCode)) {
                    result.setSuccess(false);
                    failStr.append("；学历不存在");
                }
            } else {
                List<String> degreeImport = extendList.stream().map(DtoImportPersonExtend::getEducation).collect(Collectors.toList());
                degreeImport.removeIf(Objects::isNull);
                List<String> isExistTypeName = Stream.of(person.getDegree()).filter(p -> !degreeImport.contains(p)).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(isExistTypeName)) {
                    result.setSuccess(false);
                    failStr.append("；学历与关联表不对应");
                }
            }
        }
    }

    /**
     * 角色检验
     *
     * @param result  校验结果
     * @param person  实体
     * @param dbRole  所有角色
     * @param failStr 检验错误信息
     */
    private void isExistRole(ExcelVerifyHandlerResult result, DtoImportPerson person, List<DtoRole> dbRole, StringBuilder failStr) {
        if (relationMap.get("isCreateAccount") && StringUtil.isNotEmpty(person.getRoleNames())) {
            List<String> roleNames = new ArrayList<>();
            if (person.getRoleNames().contains(",") || person.getRoleNames().contains("，")) {
                if (StringUtil.isNotEmpty(person.getRoleNames())) {
                    String[] roleName = person.getRoleNames().split(",");
                    if (roleName.length <= 1) {
                        roleName = person.getRoleNames().split("，");
                    }
                    for (String name : roleName) {
                        roleNames.add(name.trim());
                    }
                }
            } else {
                roleNames.add(person.getRoleNames());
            }
            List<String> dbRoleNames = dbRole.stream().map(DtoRole::getRoleName).collect(Collectors.toList());
            List<String> isHasRoles = roleNames.stream().filter(p -> !dbRoleNames.contains(p)).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(isHasRoles)) {
                result.setSuccess(false);
                failStr.append("；角色").append(isHasRoles).append("不存在");
            }
        }
    }

    /**
     * 人员基本信息检验
     *
     * @param result    校验结果
     * @param person    实体
     * @param dbPersons 所有人员
     * @param failStr   检验错误信息
     */
    private void isExistData(ExcelVerifyHandlerResult result, DtoImportPerson person, List<DtoPerson> dbPersons, StringBuilder failStr) {
        List<DtoPerson> isExistPerson;
        if (StringUtil.isNotEmpty(person.getUserNo())) {
            isExistPerson = dbPersons.stream().filter(p -> person.getUserNo().equals(p.getUserNo())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(isExistPerson)) {
                result.setSuccess(false);
                failStr.append("；编号在数据库中已存在");
            }
        } else {
            result.setSuccess(false);
            failStr.append("；编号不能为空");
        }
        if (StringUtil.isNotEmpty(person.getMobile())) {
            isExistPerson = dbPersons.stream().filter(p -> person.getMobile().equals(p.getMobile())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(isExistPerson)) {
                result.setSuccess(false);
                failStr.append("；手机号在数据库中已存在");
            }
        } else {
            result.setSuccess(false);
            failStr.append("；手机号不能为空");
        }
    }

    /**
     * 重复数据校验
     *
     * @param result         校验结果
     * @param personTempList 临时数据
     * @param person         实体
     * @param failStr        校验错误信息
     */
    private void isRepeatData(ExcelVerifyHandlerResult result, List<DtoImportPerson> personTempList, DtoImportPerson person, StringBuilder failStr) {
        if (StringUtil.isNotEmpty(person.getUserNo())) {
            List<Integer> repeatNum = personTempList.stream().filter(p -> person.getUserNo().equals(p.getUserNo())).map(DtoImportPerson::getRowNum).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(repeatNum)) {
                result.setSuccess(false);
                failStr.append("；与第").append(repeatNum).append("行编号重复");
            }
        }
        //手机号的重复校验
        if (StringUtil.isNotEmpty(person.getMobile())) {
            List<Integer> repeatNum = personTempList.stream().filter(p -> person.getMobile().equals(p.getMobile())).map(DtoImportPerson::getRowNum).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(repeatNum)) {
                result.setSuccess(false);
                failStr.append("；与第").append(repeatNum).append("行手机号重复");
            }
        }
    }
}
