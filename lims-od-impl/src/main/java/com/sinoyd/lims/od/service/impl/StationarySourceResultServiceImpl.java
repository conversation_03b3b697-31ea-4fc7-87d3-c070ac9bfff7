package com.sinoyd.lims.od.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.common.utils.StationarySourceCalculationUtil;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.od.dto.*;
import com.sinoyd.lims.od.repository.*;
import com.sinoyd.lims.od.service.LabSeqService;
import com.sinoyd.lims.od.service.OdSampleService;
import com.sinoyd.lims.od.service.StationarySourceResultService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.lang.reflect.Array;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 固定源结果接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
@Service
@Slf4j
public class StationarySourceResultServiceImpl extends BaseJpaServiceImpl<DtoStationarySourceResult, String, StationarySourceResultRepository>
        implements StationarySourceResultService {

}
