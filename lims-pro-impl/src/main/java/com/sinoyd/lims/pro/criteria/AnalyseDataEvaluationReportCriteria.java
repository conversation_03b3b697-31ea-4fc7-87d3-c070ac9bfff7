package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * 工作单分析数据评价标准查询条件
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date ：2025/3/14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AnalyseDataEvaluationReportCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    private String sampleCode;

    private String analyseItem;

    private String reportId;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and s.sampleCategory = :sampleCategory");
        values.put("sampleCategory", EnumPRO.EnumSampleCategory.原样.getValue());
        condition.append(" and a.isDeleted = 0 and (s.isDeleted = 0 or s.status = :status)");
        values.put("status", EnumPRO.EnumSampleStatus.样品作废.name());
        condition.append(" and s.id = a.sampleId ");
        condition.append(" and s.id = r.objectId ");
        if (StringUtil.isNotEmpty(this.analyseItem)) {
            condition.append(" and a.redAnalyzeItemName like :analyseItem");
            values.put("analyseItem", "%" + this.analyseItem + "%");
        }
        if (StringUtil.isNotEmpty(this.sampleCode)) {
            condition.append(" and s.code like :sampleCode");
            values.put("sampleCode", "%" + this.sampleCode + "%");
        }
        if (StringUtil.isNotEmpty(reportId)) {
            condition.append(" and r.reportId = :reportId");
            values.put("reportId", this.reportId);
        }
        return condition.toString();
    }
}