package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 参数管理查询条件
 *
 * <AUTHOR>
 * @version v1.0.0 2019/5/14
 * @since v100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ParamsCriteria extends BaseCriteria {


    /**
     * 关键字：参数名称，参数编号,变量名称
     */
    private String key;

    /**
     * 参数名称
     */
    private String names;

    /**
     * 是否精确查询
     */
    private Boolean isPrecision = Boolean.FALSE;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();

        // 关键字模糊查找
        if (StringUtil.isNotNull(isPrecision) && isPrecision) {
            if (StringUtils.isNotNullAndEmpty(this.key)) {
                condition.append(" and (paramName like :key or paramCode = :key or variableName = :key)");
                values.put("key", this.key);
            }

            // 关键字模糊查找
            if (StringUtils.isNotNullAndEmpty(this.names)) {
                condition.append(" and paramName = :paramName");
                values.put("paramName", this.names);
            }
        } else {
            if (StringUtils.isNotNullAndEmpty(this.key)) {
                condition.append(" and (paramName like :key or paramCode like :key or variableName like :key)");
                values.put("key", "%" + this.key + "%");
            }

            // 关键字模糊查找
            if (StringUtils.isNotNullAndEmpty(this.names)) {
                condition.append(" and paramName like :paramName");
                values.put("paramName", "%" + this.names + "%");
            }

        }

        condition.append(" and isDeleted = 0 ");

        return condition.toString();
    }
}