package com.sinoyd.base.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 供应商评价信息实体
 *
 * <AUTHOR>
 * @version V1.0.0 2024/3/14
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "EnterpriseEvaluate")
@Data
@EntityListeners(AuditingEntityListener.class)
public class EnterpriseEvaluate implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public EnterpriseEvaluate() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(name = "id", length = 50)
    @ApiModelProperty(value = "id")
    private String id = UUIDHelper.NewID();

    /**
     * 供应商id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty(value = "供应商id")
    private String entId;

    /**
     * 年度
     */
    @Column(length = 10, nullable = false)
    @ApiModelProperty(value = "年度")
    @Length(message = "年度{validation.message.length}", max = 255)
    private String yearSn;

    /**
     * 资质证书
     */
    @Column(length = 255)
    @ApiModelProperty(value = "资质证书")
    @Length(message = "资质证书{validation.message.length}", max = 255)
    private String certName;

    /**
     * 证书编号
     */
    @Column(length = 255)
    @ApiModelProperty(value = "证书编号")
    @Length(message = "证书编号{validation.message.length}", max = 255)
    private String certCode;

    /**
     * 有效期至
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-01-01 00:00:00'")
    @ApiModelProperty(value = "有效期至")
    private Date certEffectiveTime;

    /**
     * 是否合格供应商
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty(value = "是否合格供应商")
    private Boolean isQualified;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

} 