package com.sinoyd.lims.monitor.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;


/**
 * WaterExpand实体
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="WaterExpand")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class WaterExpand implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  WaterExpand() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
       this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 水体id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("水体id")
	private String waterId;
    
    /**
    * 水功能区类型
    */
    @Column(length=50)
    @ApiModelProperty("水功能区类型")
    @Length(message = "水功能区类型{validation.message.length}", max = 50)
	private String waterFunctionZoneType;
    
    /**
    * 起始位置
    */
    @Column(length=50)
    @ApiModelProperty("起始位置")
    @Length(message = "起始位置{validation.message.length}", max = 50)
	private String startPlaceName;
    
    /**
    * 网管湖库级别
    */
    @Column(length=50)
    @ApiModelProperty("网管湖库级别")
    @Length(message = "网管湖库级别{validation.message.length}", max = 50)
	private String netWaterLevel;
    
    /**
    * 域管湖库级别
    */
    @Column(length=50)
    @ApiModelProperty("域管湖库级别")
    @Length(message = "域管湖库级别{validation.message.length}", max = 50)
	private String areaWaterLevel;
    
    /**
    * 湖库类型代码
    */
    @Column(length=50)
    @ApiModelProperty("湖库类型代码")
    @Length(message = "湖库类型代码{validation.message.length}", max = 50)
	private String lakesTypeCode;
    
    /**
    * 网管河流级别
    */
    @Column(length=50)
    @ApiModelProperty("网管河流级别")
    @Length(message = "网管河流级别{validation.message.length}", max = 50)
	private String waterl;
    
    /**
    * 域管河流级别
    */
    @Column(length=50)
    @ApiModelProperty("域管河流级别")
    @Length(message = "域管河流级别{validation.message.length}", max = 50)
	private String awaterl;
    
    /**
    * 水厂所在地名称
    */
    @Column(length=250)
    @ApiModelProperty("水厂所在地名称")
    @Length(message = "水厂所在地名称{validation.message.length}", max = 250)
	private String locationName;
    
    /**
    * 终止位置
    */
    @Column(length=50)
    @ApiModelProperty("终止位置")
    @Length(message = "终止位置{validation.message.length}", max = 50)
	private String endPlaceName;
    
    /**
    * 水功能区长度
    */
    @Column(length=50)
    @ApiModelProperty("水功能区长度")
    @Length(message = "水功能区长度{validation.message.length}", max = 50)
	private String waterFunctionZoneLen;
    
    /**
    * 年供水量
    */
    @Column(length=50)
    @ApiModelProperty("年供水量")
    @Length(message = "年供水量{validation.message.length}", max = 50)
	private String yswq;
    
    /**
    * 备注
    */
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 255)
	private String remark;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }