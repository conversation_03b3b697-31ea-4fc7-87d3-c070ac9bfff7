package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.EnvironmentalRecord2Test;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * DtoEnvironmentalRecord2Test实体
 * <AUTHOR>
 * @version V1.0.0 2024/03/05
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "tb_lim_environmentalrecord2test")
 @Data
 @DynamicInsert
 public  class DtoEnvironmentalRecord2Test extends EnvironmentalRecord2Test {
}