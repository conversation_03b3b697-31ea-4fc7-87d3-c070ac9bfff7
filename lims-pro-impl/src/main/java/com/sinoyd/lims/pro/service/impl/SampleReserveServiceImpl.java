package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.rcc.AnalyzeItemRepository;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.lim.service.ProjectTypeService;
import com.sinoyd.lims.pro.criteria.SampleReserveCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.entity.AnalyseData;
import com.sinoyd.lims.pro.entity.SampleDispose;
import com.sinoyd.lims.pro.entity.SampleReserve;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.ReceiveSampleRecordService;
import com.sinoyd.lims.pro.service.SampleReserveService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 样品处理操作接口
 *
 * <AUTHOR>
 * @version V5.2.0 2022/6/21
 */
@Service
@Slf4j
public class SampleReserveServiceImpl extends BaseJpaServiceImpl<DtoSampleReserve, String, SampleReserveRepository> implements SampleReserveService {

    //region 依赖注入
    private ReceiveSubSampleRecordRepository receiveSubSampleRecordRepository;

    private ReceiveSubSampleRecord2SampleRepository toSampleRepository;

    private AnalyseDataRepository analyseDataRepository;

    private SampleReserve2TestRepository sampleReserve2TestRepository;

    private SampleRepository sampleRepository;

    private SampleTypeRepository sampleTypeRepository;

    private AnalyzeItemRepository analyzeItemRepository;

    private SampleDispose2TestRepository sampleDispose2TestRepository;

    private SampleDisposeRepository sampleDisposeRepository;

    private ProjectRepository projectRepository;

    private ProjectTypeService projectTypeService;

    private ReceiveSampleRecordService receiveSampleRecordService;
    //endregion

    /**
     * 分页查询数据
     *
     * @param pb       分页数据
     * @param criteria 查询条件
     */
    @Override
    public void findSamplesByPage(PageBean<DtoSample> pb, BaseCriteria criteria) {
        SampleReserveCriteria sampleReserveCriteria = (SampleReserveCriteria) criteria;
        int page = pb.getPageNo(), rowNum = pb.getRowsPerPage();
        List<DtoSample> sampleList = querySampleList(pb, sampleReserveCriteria);
        if (StringUtil.isNotEmpty(sampleList)) {
            sampleList = filterSample(sampleReserveCriteria, sampleList);
            pb.setRowsCount(sampleList.size());
            pb.setData(sampleList.stream().skip((long) (page - 1) * rowNum).limit(rowNum).collect(Collectors.toList()));
        }
//        Date time4 = new Date();
//        log.error("=================================================处理全部样品数据时间：" + (time4.getTime() - time3.getTime()) + "ms================================");
    }

    /**
     * 查询样品
     *
     * @param sampleReserveCriteria 查询条件对象
     * @param pb                    分页对象
     * @return 样品列表
     */
    protected List<DtoSample> querySampleList(PageBean<DtoSample> pb, SampleReserveCriteria sampleReserveCriteria) {
        pb.setEntityName("DtoSample s");
        pb.setSelect("select new com.sinoyd.lims.pro.dto.DtoSample(s.id,s.code,s.redFolderName,s.sampleTypeId,s.projectId,s.associateSampleId,s.status,s.receiveId,s.samplingTimeBegin)");
        //获取页数
        int page = pb.getPageNo();
        //获取每页个数
        int rowNum = pb.getRowsPerPage();
        //设置查询所有
        pb.setPageNo(1);
        pb.setRowsPerPage(Integer.MAX_VALUE);
        comRepository.findByPage(pb, sampleReserveCriteria);
        List<DtoSample> sampleList = pb.getData();
        return sampleList;
    }

    /**
     * 过滤样品
     *
     * @param sampleReserveCriteria 查询条件
     * @param sampleList            样品列表
     * @return 过滤后的样品
     */
    protected List<DtoSample> filterSample(SampleReserveCriteria sampleReserveCriteria, List<DtoSample> sampleList) {
        //获取所有的样品数据
        List<String> sampleIds = new ArrayList<>();
        List<String> yySampleId = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<String> qcSampleId = sampleList.stream().map(DtoSample::getAssociateSampleId).collect(Collectors.toList());
        sampleIds.addAll(yySampleId);
        sampleIds.addAll(qcSampleId);
        sampleIds.removeIf(p -> UUIDHelper.GUID_EMPTY.equals(p));
        sampleIds = sampleIds.stream().distinct().collect(Collectors.toList());
        List<DtoSample> sampleAll = sampleRepository.findAll(sampleIds);
        //所有样品Map
        Map<String, DtoSample> sampleMap = sampleAll.stream().collect(Collectors.toMap(DtoSample::getId, dto -> dto));
        //获取所有的检测类型
        List<String> sampleTypeIds = sampleAll.stream().map(DtoSample::getSampleTypeId).collect(Collectors.toList());
        List<DtoSampleType> sampleTypes = StringUtil.isNotEmpty(sampleTypeIds) ? sampleTypeRepository.findAll(sampleTypeIds) : new ArrayList<>();
        //获取样品关联的测试项目数据
        List<DtoAnalyseData> anaDataList = analyseDataRepository.findBySampleIdIn(sampleIds);
        //过滤现场测试项目与分包测试项目
        if (StringUtil.isNotEmpty(anaDataList)) {
            anaDataList = anaDataList.stream().filter(p -> !p.getIsCompleteField() && !p.getIsDeleted()
                    && !p.getIsOutsourcing() && !p.getIsSamplingOut()).collect(Collectors.toList());
        }
        //测试项目数据Map
        Map<String, List<DtoAnalyseData>> anaMapGroupBySample = anaDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleId));
        //获取样品管理的数据
        List<DtoSampleReserve> reserveList = repository.findBySampleIdIn(sampleIds);
        //样品管理数据按照样品id分组
        Map<String, List<DtoSampleReserve>> reserveGroupBySample = reserveList.stream().collect(Collectors.groupingBy(DtoSampleReserve::getSampleId));

        //获取样品处置数据
        List<DtoSampleDispose> disposeList = sampleDisposeRepository.findBySampleIdIn(sampleIds);
        //获取所有样品领取数据
        List<String> reserveIds = StringUtil.isNotEmpty(reserveList) ? reserveList.stream().map(DtoSampleReserve::getId).collect(Collectors.toList()) : new ArrayList<>();
        List<DtoSampleReserve2Test> toTestList = StringUtil.isNotEmpty(reserveIds) ? sampleReserve2TestRepository.findByReserveIdIn(reserveIds) : new ArrayList<>();
        //获取所有人员
        List<DtoPerson> personList = personRepository.findAll();
        //获取所有项目数据
        List<String> projectIds = sampleAll.stream().map(DtoSample::getProjectId).collect(Collectors.toList());
        List<DtoProject> projectList = StringUtil.isNotEmpty(projectIds) ? projectRepository.findAll(projectIds) : new ArrayList<>();
        //获取所有的项目类型
        List<DtoProjectType> projectTypes = projectTypeService.findAll();
        //获取所有送样单数据
        List<String> receiveIds = sampleAll.stream().map(DtoSample::getReceiveId).distinct().collect(Collectors.toList());
//            List<DtoReceiveSubSampleRecord> subReceiveRecords = receiveSubSampleRecordRepository.findByReceiveIdIn(receiveIds);
        List<DtoReceiveSampleRecord> sampleRecords = receiveSampleRecordService.findAll(receiveIds);
        if (StringUtil.isNotEmpty(sampleList)) {
            for (DtoSample sample : sampleList) {
                String receiveId = sample.getReceiveId();
                if (StringUtil.isNotEmpty(sample.getAssociateSampleId())) {
                    DtoSample yySample = StringUtil.isNotEmpty(sampleMap) ? sampleMap.get(sample.getAssociateSampleId()) : null;
                    receiveId = StringUtil.isNotNull(yySample) ? yySample.getReceiveId() : receiveId;
                }
                //获取检测类型
                String sampleTypeId = sample.getSampleTypeId();
                Optional<DtoSampleType> sampleTypeOptional = sampleTypes.stream().filter(p -> sampleTypeId.equals(p.getId())).findFirst();
                DtoSampleType sampleType = null;
                if (sampleTypeOptional.isPresent()) {
                    sampleType = sampleTypeOptional.get();
                }
                if (StringUtil.isNotNull(sampleType)) {
                    sample.setSampleTypeName(sampleType.getTypeName());
                    sample.setBigSampleTypeId(sampleType.getParentId());
                }
                List<String> anaItemIdsOfCondition = new ArrayList<>();
                //获取样品的测试项目数据
//                    List<DtoAnalyseData> anaDataBySample = anaDataList.stream().filter(p -> sample.getId().equals(p.getSampleId())).collect(Collectors.toList());
                List<DtoAnalyseData> anaDataBySample = StringUtil.isNotEmpty(anaMapGroupBySample) ? anaMapGroupBySample.getOrDefault(sample.getId(), new ArrayList<>()) : new ArrayList<>();
                anaDataBySample.removeIf(DtoAnalyseData::getIsCompleteField);
                //设置样品关联的测试项目总数量
                if (StringUtil.isNotEmpty(anaDataBySample)) {
                    Integer totalCount = (int) anaDataBySample.stream().map(DtoAnalyseData::getAnalyseItemId).distinct().count();
                    //总数跟着查询的分析项目以及分析方法来
                    if (StringUtil.isNotEmpty(sampleReserveCriteria.getAnalyzeItemName())) {
                        anaItemIdsOfCondition = anaDataBySample.stream()
                                .filter(p -> p.getRedAnalyzeItemName().contains(sampleReserveCriteria.getAnalyzeItemName()))
                                .map(DtoAnalyseData::getAnalyseItemId).distinct().collect(Collectors.toList());
                        totalCount = anaItemIdsOfCondition.size();
                    }
                    sample.setReceiveTotalTestNum(totalCount);
                } else {
                    sample.setReceiveTotalTestNum(0);
                }
                //查询操作的数据
                List<DtoSampleReserve> reserves = StringUtil.isNotEmpty(reserveGroupBySample) ? reserveGroupBySample.getOrDefault(sample.getId(), new ArrayList<>()) : new ArrayList<>();
                reserves.removeIf(SampleReserve::getIsDeleted);
                int num = 0;
                String keepStatus = "未留样";
                String reserveStatus = "未处置";
                String reservePersonName = "";
                Date samplingTime = getReceiveTime(receiveId, sampleRecords);
                DtoSampleReserve disposeData = new DtoSampleReserve();
                //设置处置状态
                if (StringUtil.isNotEmpty(reserves)) {
                    //获取处置信息
                    Optional<DtoSampleReserve> temp = reserves.stream()
                            .filter(p -> EnumLIM.EnumReserveType.处置.getValue().toString().equals(p.getReserveType().toString()))
                            .findFirst();
                    if (temp.isPresent()) {
                        reserveStatus = "已处置";
                        Optional<DtoPerson> personOptional = personList.stream().filter(p -> temp.get().getReservePersonId().equals(p.getId())).findFirst();
                        DtoPerson person = null;
                        if (personOptional.isPresent()) {
                            person = personOptional.get();
                        }
                        reservePersonName = StringUtil.isNotNull(person) ? person.getCName() : "";
                        disposeData = temp.get();
                    }
                    disposeData.setReservePersonName(reservePersonName);
                    //获取领取信息
                    List<DtoSampleReserve> reservesSamples = reserves.stream()
                            .filter(p -> EnumLIM.EnumReserveType.领取.getValue().toString().equals(p.getReserveType().toString()))
                            .collect(Collectors.toList());
                    List<String> anaIds = anaDataList.stream().map(DtoAnalyseData::getAnalyseItemId).collect(Collectors.toList());
                    //设置领取数量
                    if (StringUtil.isNotEmpty(reservesSamples)) {
                        List<String> reserveIdsOfSample = reservesSamples.stream().map(DtoSampleReserve::getId).collect(Collectors.toList());
                        List<String> toTest = toTestList.stream()
                                .filter(p -> reserveIdsOfSample.contains(p.getReserveId()))
                                .map(DtoSampleReserve2Test::getAnalyzeItemId)
                                .distinct().collect(Collectors.toList());
                        if (StringUtil.isNotEmpty(anaIds)) {
                            toTest = toTest.stream().filter(anaIds::contains).collect(Collectors.toList());
                        }
                        if (StringUtil.isNotEmpty(sampleReserveCriteria.getAnalyzeItemName())) {
                            toTest = toTestList.stream()
                                    .filter(p -> reserveIdsOfSample.contains(p.getReserveId()))
                                    .map(DtoSampleReserve2Test::getAnalyzeItemId)
                                    .distinct().filter(anaItemIdsOfCondition::contains).collect(Collectors.toList());
                        }
                        num = StringUtil.isNotEmpty(toTest) ? toTest.size() : 0;
                    }

                }
                List<DtoSampleDispose> keepDispose = disposeList.stream().filter(p -> sample.getId().equals(p.getSampleId())).collect(Collectors.toList());
                //设置样品状态
                if (StringUtil.isNotEmpty(keepDispose)) {
                    List<DtoSampleDispose> disposes = keepDispose.stream().filter(SampleDispose::getIsDisposed).collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(disposes)) {
                        keepStatus = "留样中";
                        if (disposes.size() == keepDispose.size()) {
                            keepStatus = "已处置";
                        }
                    } else {
                        keepStatus = "留样中";
                    }
                }
                if (!UUIDHelper.GUID_EMPTY.equals(sample.getProjectId())) {
                    setProjectType(projectList, projectTypes, sample.getProjectId(), sample);
                } else {
                    String YYId = sample.getAssociateSampleId();
                    if (StringUtil.isNotEmpty(YYId)) {
                        Optional<DtoSample> sampleOptional = sampleAll.stream().filter(p -> YYId.equals(p.getId())).findFirst();
                        sampleOptional.ifPresent(p -> setProjectType(projectList, projectTypes, p.getProjectId(), sample));
                    }
                }

                sample.setReceiveTestNum(num);
                sample.setSampleReserve(disposeData);
                sample.setReserveStatus(reserveStatus);
                sample.setKeepStatus(keepStatus);
                sample.setSendTime(samplingTime);
            }
        }
        //处理查询条件以及分页
        //领取状态
        String receiveStatus = sampleReserveCriteria.getReceiveStatus();
        //过滤掉只有现场测试项目数据的样品
        sampleList = sampleList.stream().filter(p -> !p.getReceiveTotalTestNum().equals(0)).collect(Collectors.toList());
        //获取所有的相关的测试项目数据
        if ("未领取".equals(receiveStatus)) {
            sampleList = sampleList.stream().filter(p -> p.getReceiveTestNum() < p.getReceiveTotalTestNum()).collect(Collectors.toList());
        }
        if ("已领取".equals(receiveStatus)) {
            sampleList = sampleList.stream().filter(p -> p.getReceiveTestNum() >= p.getReceiveTotalTestNum()).collect(Collectors.toList());
        }
        return sampleList;
    }

    /**
     * 保存数据
     *
     * @param sampleReserve 保存的数据
     * @return 保存后的数据
     */
    @Override
    @Transactional
    public List<DtoSampleReserve> saveReserve(DtoSampleReserve sampleReserve) {
        //获取所有的样品对应的测试项目数据
        List<DtoAnalyseData> analyseData = new ArrayList<>();
        if (StringUtil.isNotEmpty(sampleReserve.getSampleIds())) {
            analyseData = analyseDataRepository.findBySampleIdIn(sampleReserve.getSampleIds());
            analyseData.removeIf(AnalyseData::getIsCompleteField);
        }
        //批量保存的数据
        List<DtoSampleReserve> batchInsert = new ArrayList<>();
        //批量保存的子表数据
        List<DtoSampleReserve2Test> batchToTest = new ArrayList<>();
        //保存数据
        for (String sampleId : sampleReserve.getSampleIds()) {
            List<String> testIds = new ArrayList<>();
            //设置样品关联测试项目id
            getTest(testIds, sampleId, analyseData, sampleReserve);
            //获取样品下的实验室测试项目数据
            List<DtoAnalyseData> anaDataOfSample = analyseData.stream().filter(p -> sampleId.equals(p.getSampleId())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(testIds)) {
                //添加数据
                addBatchInsert(batchInsert, batchToTest, sampleReserve, testIds, sampleId);
            }
            if (EnumLIM.EnumReserveType.处置.getValue().toString().equals(sampleReserve.getReserveType().toString())) {
                //添加数据
                addBatchInsert(batchInsert, batchToTest, sampleReserve, testIds, sampleId);
            }
        }
        List<DtoSampleReserve> save = new ArrayList<>();
        //保存领取数据
        if (StringUtil.isNotEmpty(batchInsert)) {
            save = repository.save(batchInsert);
            if (StringUtil.isNotEmpty(batchToTest)) {
                //保存领取关联测试项目
                sampleReserve2TestRepository.save(batchToTest);
            }
        }
        return save;
    }


    /**
     * 判断已存在的数据
     *
     * @param sampleReserve 保存的数据
     * @return 已存在的数据
     */
    @Override
    public List<DtoSampleReserve> judgeSaveData(DtoSampleReserve sampleReserve) {
        //已存在的数据集合
        List<DtoSampleReserve> resultData = new ArrayList<>();
        //已存在的数据集合
        List<DtoSampleReserve> result = new ArrayList<>();

        //查询所有关联领取测试项目的信息
        List<DtoSampleReserve2Test> toTests = sampleReserve2TestRepository.findAll();
        //获取所有的测试项目数据
        List<DtoAnalyseData> anaData = analyseDataRepository.findBySampleIdIn(sampleReserve.getSampleIds());
        //获取需要添加的样品Id
        List<String> sampleIds = sampleReserve.getSampleIds();
        //获取所有的领取操作数据
        List<DtoSampleReserve> sampleReserves = repository.findAll();
        //判断数据是否存在
        if (StringUtil.isNotEmpty(sampleReserves)) {
            for (String sampleId : sampleIds) {
                DtoSample sample = sampleRepository.findOne(sampleId);
                //查询操作
                List<DtoSampleReserve> reserveList = repository.findBySampleId(sampleId);
                //移除已删除的数据
                reserveList.removeIf(p -> p.getIsDeleted() && EnumLIM.EnumReserveType.领取.getValue().toString().equals(p.getReserveType().toString()));

                if (StringUtil.isNotEmpty(reserveList)) {
                    //获取到类型为领取的数据
                    List<DtoSampleReserve> reservesData = reserveList.stream()
                            .filter(p -> sampleReserve.getReserveType().toString().equals(p.getReserveType().toString())).collect(Collectors.toList());

                    //获取领取数据
                    for (DtoSampleReserve reserve : reservesData) {
                        if (StringUtil.isNotNull(reserve) && EnumLIM.EnumReserveType.领取.getValue().toString().equals(reserve.getReserveType().toString())) {
                            if (reserve.getReserveType().equals(sampleReserve.getReserveType())) {
                                //获取样品下的所有分析项目ID
                                List<String> anaItemIds = anaData.stream()
                                        .filter(p -> sampleId.equals(p.getSampleId())).map(DtoAnalyseData::getAnalyseItemId)
                                        .collect(Collectors.toList());
                                //获取保存的分析项目Id
                                List<String> saveIds = sampleReserve.getAnalyzeItemIds().stream().filter(anaItemIds::contains).collect(Collectors.toList());
                                //获取到领取的测试项目数据
                                List<DtoSampleReserve2Test> toTestsOfSample = toTests.stream()
                                        .filter(p -> reserve.getId().equals(p.getReserveId()) && saveIds.contains(p.getAnalyzeItemId()))
                                        .collect(Collectors.toList());
                                if (StringUtil.isNotEmpty(toTestsOfSample)) {
                                    reserve.setSampleCode(sample.getCode());
                                    reserve.setSampleReserve2Tests(toTestsOfSample);
                                    resultData.add(reserve);
                                }
                            }
                        }
                    }
                }
            }
        }
        Map<String, List<DtoSampleReserve>> collect = resultData.stream().collect(Collectors.groupingBy(DtoSampleReserve::getSampleId));
        for (String sampleId : sampleIds) {
            List<DtoSampleReserve> reserveList = collect.get(sampleId);
            if (StringUtil.isNotEmpty(reserveList)) {
                DtoSampleReserve temp = new DtoSampleReserve();
                for (DtoSampleReserve reserve : reserveList) {
                    if (StringUtil.isNotEmpty(temp.getSampleReserve2Tests())) {
                        if (reserve.getSampleReserve2Tests().size() > temp.getSampleReserve2Tests().size()) {
                            BeanUtils.copyProperties(reserve, temp);
                        }
                    } else {
                        BeanUtils.copyProperties(reserve, temp);
                    }
                }
                result.add(temp);
            }
        }
        return result;
    }

    /**
     * 根据样品Id查询样品详细数据
     *
     * @param sampleId 样品id
     * @return 查询结果
     */
    @Override
    public DtoSample findSampleDetail(String sampleId) {
        List<DtoAnalyzeItem> analyzeItems = new ArrayList<>();
        List<String> anaItemIds;
        List<DtoSampleDispose> disposes = sampleDisposeRepository.findBySampleId(sampleId);
        String status = "未留样";
        String reserveStatus = "未处置";
        String reservePersonName = "";
        AtomicReference<Integer> testNum = new AtomicReference<>(0);
        int totalTestNum = 0;
        DtoSampleReserve disposeData = new DtoSampleReserve();
        //获取样品下的所有测试项目数据
        List<DtoAnalyseData> anaDataList = analyseDataRepository.findBySampleIdAndIsDeletedFalse(sampleId);
        if (StringUtil.isNotEmpty(anaDataList)) {
            anaDataList = anaDataList.stream().filter(p -> !p.getIsCompleteField() && !p.getIsDeleted()
                    && !p.getIsOutsourcing() && !p.getIsSamplingOut()).collect(Collectors.toList());
        }
        //获取样品关联测试项目
        if (StringUtil.isNotEmpty(anaDataList)) {
            anaItemIds = anaDataList.stream().map(DtoAnalyseData::getAnalyseItemId).distinct().collect(Collectors.toList());
            totalTestNum = anaItemIds.size();
            analyzeItems = analyzeItemRepository.findAll(anaItemIds);
        }

        //设置样品的处置状态与关联测试项目领取数据
        DtoSample sample = sampleRepository.findOne(sampleId);
        String receiveId = sample.getReceiveId();
        if (StringUtil.isNotEmpty(sample.getAssociateSampleId()) && !UUIDHelper.GUID_EMPTY.equals(sample.getAssociateSampleId())) {
            DtoSample yySample = sampleRepository.findOne(sample.getAssociateSampleId());
            receiveId = yySample.getReceiveId();
        }
        //获取所有领养单数据
        List<DtoReceiveSampleRecord> sampleRecords = receiveSampleRecordService.findAll(Collections.singleton(receiveId));
        Date samplingTime = getReceiveTime(receiveId, sampleRecords);
        if (StringUtil.isNotNull(sample)) {
            //获取检测类型
            String sampleTypeId = sample.getSampleTypeId();
            DtoSampleType sampleType = sampleTypeRepository.findOne(sampleTypeId);
            if (StringUtil.isNotNull(sampleType)) {
                sample.setSampleTypeName(sampleType.getTypeName());
                sample.setBigSampleTypeId(sampleType.getParentId());
            }
            List<DtoSampleReserve> bySampleId = repository.findBySampleId(sampleId);
            bySampleId.removeIf(DtoSampleReserve::getIsDeleted);
            if (StringUtil.isNotEmpty(bySampleId)) {
                //获取处置的数据
                Optional<DtoSampleReserve> sampleReserveOptional = bySampleId.stream()
                        .filter(p -> EnumLIM.EnumReserveType.处置.getValue().toString().equals(p.getReserveType().toString())).max(Comparator.comparing(DtoSampleReserve::getCreateDate));
                if (sampleReserveOptional.isPresent()) {
                    reserveStatus = "已处置";
                    DtoSampleReserve reserve = sampleReserveOptional.get();
                    DtoPerson person = personRepository.findOne(reserve.getReservePersonId());
                    reservePersonName = StringUtil.isNotNull(person) ? person.getCName() : "";
                    disposeData = sampleReserveOptional.get();
                }
                disposeData.setReservePersonName(reservePersonName);
                //获取领取的数据
                List<DtoSampleReserve> reserveDataList = bySampleId.stream()
                        .filter(p -> EnumLIM.EnumReserveType.领取.getValue().toString().equals(p.getReserveType().toString()))
                        .collect(Collectors.toList());
                if (StringUtil.isNotEmpty(reserveDataList)) {
                    for (DtoSampleReserve reserve : reserveDataList) {
                        setReserveDetail(analyzeItems, disposes, testNum, reserve);
                    }
                }
            } else {
                //设置留样状态信息
                analyzeItems.forEach(p -> setIsKeepSample(p, disposes));
            }
            sample.setReceiveTest(analyzeItems);
            //设置样品留样状态
            if (StringUtil.isNotEmpty(disposes)) {
                status = "留样中";
                if (disposes.get(0).getIsDisposed()) {
                    status = "已处置";
                }
            }
            sample.setReceiveTestNum(testNum.get());
            sample.setReceiveTotalTestNum(totalTestNum);
            sample.setReserveStatus(reserveStatus);
            sample.setKeepStatus(status);
            sample.setSampleReserve(disposeData);
            sample.setSendTime(samplingTime);
        }
        return sample;
    }

    /**
     * 根据样品Id列表查询样品详细数据
     *
     * @param sampleIds 样品id列表
     * @return 查询结果
     */
    @Override
    public Map<String, Object> findSampleDetails(List<String> sampleIds) {
        Map<String, Object> map = new HashMap<>();
        DtoSample resSample = new DtoSample();
        //获取样品下的所有测试项目数据
        List<DtoAnalyseData> anaDataList = analyseDataRepository.findBySampleIdIn(sampleIds);
//        anaDataList.removeIf(DtoAnalyseData::getIsCompleteField);
//        anaDataList.removeIf(DtoAnalyseData::getIsDeleted);
        List<DtoSample> sampleList = sampleRepository.findAll(sampleIds);
        //获取样品关联测试项目
        List<DtoAnalyzeItem> receiveTest = new ArrayList<>();
        if (StringUtil.isNotEmpty(anaDataList)) {
            List<String> anaItemIdList = anaDataList.stream().map(DtoAnalyseData::getAnalyseItemId).distinct().collect(Collectors.toList());
            if (StringUtil.isNotEmpty(anaItemIdList)) {
                receiveTest = analyzeItemRepository.findAll(anaItemIdList);
            }
        }
        map.put("receiveTest", receiveTest);
//        resSample.setReceiveTest(receiveTest);
        List<String> sampleCodeList = sampleList.stream().map(DtoSample::getCode).distinct().collect(Collectors.toList());
//        resSample.setCode(StringUtil.isNotEmpty(sampleCodeList) ? String.join("、", sampleCodeList) : "");
        map.put("code", StringUtil.isNotEmpty(sampleCodeList) ? String.join("、", sampleCodeList) : "");
        return map;
    }

    /**
     * 设置领取详细信息
     *
     * @param analyzeItems 样品对应的分析项目集合
     * @param disposes     留样数据
     * @param testNum      领取个数
     */
    private void setReserveDetail(List<DtoAnalyzeItem> analyzeItems,
                                  List<DtoSampleDispose> disposes, AtomicReference<Integer> testNum, DtoSampleReserve reserveSelect) {
        for (DtoAnalyzeItem p : analyzeItems) {
            List<DtoSampleReserve2Test> isExistData = sampleReserve2TestRepository.findByReserveIdIn(Collections.singletonList(reserveSelect.getId()));
            isExistData = isExistData.stream().filter(n -> p.getId().equals(n.getAnalyzeItemId())).collect(Collectors.toList());
            //设置是否留样
            setIsKeepSample(p, disposes);
            if (StringUtil.isEmpty(isExistData)) {
                continue;
            }
            String analyzeItemId = p.getId();
            List<DtoSampleReserve2Test> toTests = sampleReserve2TestRepository.findByAnalyzeItemId(analyzeItemId);
            List<String> reserveIds = toTests.stream().map(DtoSampleReserve2Test::getReserveId).collect(Collectors.toList());
            List<DtoSampleReserve> reserves = repository.findAll(reserveIds);
            DtoSampleReserve reserve = selectLastOne(reserves, reserveSelect.getSampleId());
            //获取到此测试项目是否领取
            List<DtoSampleReserve2Test> existData = toTests.stream()
                    .filter(toTest -> analyzeItemId.equals(toTest.getAnalyzeItemId())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(existData)) {
                //领取数量+1
                testNum.getAndSet(testNum.get() + 1);
                //设置领取人
                DtoPerson person = personRepository.findOne(reserve.getReservePersonId());
                p.setReceivePerson(StringUtil.isNotNull(person) ? person.getCName() : "");
                //设置领取日期
                p.setReceiveDate(DateUtil.dateToString(reserve.getReserveDate(), "yyyy-MM-dd HH:mm:ss"));

            }
        }
    }

    /**
     * 获取时间最新的数据
     *
     * @param list 领取数据
     * @return 结果
     */
    public DtoSampleReserve selectLastOne(List<DtoSampleReserve> list, String sampleId) {
        List<DtoSampleReserve> reserveOfNewDate = list.stream().filter(p -> sampleId.equals(p.getSampleId())).sorted(Comparator.comparing(DtoSampleReserve::getCreateDate).reversed()).collect(Collectors.toList());
        DtoSampleReserve reserve = null;
        if (StringUtil.isNotEmpty(reserveOfNewDate)) {
            reserve = reserveOfNewDate.get(0);
        }
        return reserve;
    }

    /**
     * 设置留样状态
     *
     * @param analyzeItem 需要设置的分析项目
     * @param disposes    留样数据集合
     */
    private void setIsKeepSample(DtoAnalyzeItem analyzeItem, List<DtoSampleDispose> disposes) {
        if (StringUtil.isNotEmpty(disposes)) {
            //获取所有的样品留样信息ID
            List<String> disposeIds = disposes.stream().map(DtoSampleDispose::getId).collect(Collectors.toList());
            //根据留样Id获取留样的测试项目的Id
            List<DtoSampleDispose2Test> testOfDispose = sampleDispose2TestRepository.findBySampleDisposeIdIn(disposeIds);
            List<String> analyzeItemIds = testOfDispose.stream().map(DtoSampleDispose2Test::getTestId).collect(Collectors.toList());
            //判断此样品下的分析项目是否留样
            List<String> existDispose = analyzeItemIds.stream()
                    .filter(toTest -> analyzeItem.getId().equals(toTest))
                    .collect(Collectors.toList());
            analyzeItem.setIsKeepSample(StringUtil.isNotEmpty(existDispose));
        } else {
            analyzeItem.setIsKeepSample(false);
        }
    }


    /**
     * 获取样品测试项目的并集
     *
     * @param sampleIds 样品ID
     * @return 查询结果
     */
    @Override
    public List<DtoAnalyzeItem> findAnalyzeItemBySampleIds(List<String> sampleIds) {
        List<DtoAnalyzeItem> analyzeItems = new ArrayList<>();
        if (StringUtil.isNotEmpty(sampleIds)) {
            //根据样品获取所有的测试数据
            List<DtoAnalyseData> analyseData = analyseDataRepository.findBySampleIdIn(sampleIds);
            analyseData.removeIf(DtoAnalyseData::getIsCompleteField);
            analyseData.removeIf(DtoAnalyseData::getIsDeleted);
            analyseData.removeIf(DtoAnalyseData::getIsOutsourcing);
            analyseData.removeIf(DtoAnalyseData::getIsSamplingOut);
            //获取样品的所有的测试项目Id
            List<String> analyzeItemIds = analyseData.stream()
                    .map(DtoAnalyseData::getAnalyseItemId)
                    .distinct().collect(Collectors.toList());
            if (StringUtil.isNotEmpty(analyzeItemIds)) {
                analyzeItems = analyzeItemRepository.findAll(analyzeItemIds);
            }
            List<DtoSampleReserve2Test> receiveAnaItem = new ArrayList<>();
            List<DtoSampleReserve> reserves = repository.findBySampleIdIn(sampleIds);
            if (StringUtil.isNotEmpty(reserves)) {
                List<String> reserveIds = reserves.stream().map(DtoSampleReserve::getId).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(reserveIds)) {
                    receiveAnaItem = sampleReserve2TestRepository.findByReserveIdIn(reserveIds);
                }

            }
            List<String> anaItemIds = analyzeItems.stream().map(DtoAnalyzeItem::getId).collect(Collectors.toList());
            List<DtoSampleReserve2Test> finalReceiveAnaItem = receiveAnaItem;
            analyzeItems.forEach(p -> {
                List<DtoSampleReserve2Test> reserve2Tests = finalReceiveAnaItem.stream().filter(id -> p.getId().equals(id.getAnalyzeItemId())).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(reserve2Tests)) {
                    p.setIsReceive(true);
                } else {
                    p.setIsReceive(false);
                }
            });
        }


        return analyzeItems;
    }

    /**
     * 取消处置
     *
     * @param ids 需要取消处置的Id
     * @return 取消数量
     */
    @Override
    public Integer cancelDispose(List<String> ids) {
        List<DtoSampleReserve> update = new ArrayList<>();
        List<DtoSampleReserve> reserves = repository.findBySampleIdIn(ids);
        if (StringUtil.isNotEmpty(reserves)) {
            List<DtoSampleReserve> dispose = reserves.stream()
                    .filter(p -> EnumLIM.EnumReserveType.处置.getValue().toString().equals(p.getReserveType().toString()))
                    .collect(Collectors.toList());
            if (StringUtil.isNotEmpty(dispose)) {
                dispose.forEach(p -> p.setIsDeleted(true));
            }
            update = repository.save(reserves);
        }
        return update.size();
    }

    /**
     * 获取接样时间
     *
     * @param receiveId     送样单
     * @param sampleRecords 领养单数据
     * @return 接样时间
     */
    private Date getReceiveTime(String receiveId, List<DtoReceiveSampleRecord> sampleRecords) {
        Date samplingTime = null;
        if (StringUtil.isNotEmpty(sampleRecords)) {
            DtoReceiveSampleRecord dtoReceiveSampleRecord = sampleRecords.stream().filter(p -> receiveId.equals(p.getId())).findFirst().orElse(null);
            if (dtoReceiveSampleRecord != null) {
                samplingTime = dtoReceiveSampleRecord.getReceiveSampleDate();
            }
        }
        return samplingTime;
    }

    /**
     * 添加批量数据
     *
     * @param batchInsert   批量添加集合
     * @param batchToTest   批量添加集合
     * @param sampleReserve 添加的数据
     * @param anaItemIds    测试项目Id
     * @param sampleId      样品Id
     */
    private void addBatchInsert(List<DtoSampleReserve> batchInsert, List<DtoSampleReserve2Test> batchToTest,
                                DtoSampleReserve sampleReserve, List<String> anaItemIds, String sampleId) {
        DtoSampleReserve newDto = new DtoSampleReserve();
        //添加需要添加的子表数据
        BeanUtils.copyProperties(sampleReserve, newDto);
        newDto.setId(UUIDHelper.NewID());
        List<DtoSampleReserve2Test> toTest = getToTest(anaItemIds, newDto);
        batchToTest.addAll(toTest);
        newDto.setSampleReserve2Tests(toTest);
        newDto.setSampleId(sampleId);
        newDto.setAnalyzeItemIds(anaItemIds);
        newDto.setCreateDate(new Date());
        newDto.setCreator(PrincipalContextUser.getPrincipal().getUserId());
        newDto.setModifier(UUIDHelper.GUID_EMPTY);
        newDto.setModifyDate(new Date());
        batchInsert.add(newDto);
    }


    /**
     * 获取批量更新的数据
     *
     * @param isExistData       已存在的数据集合
     * @param analyseData       测试项目
     * @param sampleReserve     添加的数据
     * @param batchUpdate       批量更新集合
     * @param batchUpdateToTest 批量更新集合
     */
    private void addBatchUpdate(List<DtoSampleReserve> isExistData, List<DtoAnalyseData> analyseData,
                                DtoSampleReserve sampleReserve, List<DtoSampleReserve> batchUpdate,
                                List<DtoSampleReserve2Test> batchUpdateToTest, List<DtoSampleReserve> batchInsert,
                                List<DtoSampleReserve2Test> batchInsertToTest) {
        for (DtoSampleReserve data : isExistData) {
            DtoSampleReserve reserve = new DtoSampleReserve();
            List<String> anaItemIds = new ArrayList<>();
            //设置样品关联分析项目id
            getTest(anaItemIds, data.getSampleId(), analyseData, sampleReserve);
            //处理已领取的分析项目
            List<DtoSampleReserve2Test> toTests = sampleReserve2TestRepository.findByReserveIdIn(Collections.singletonList(data.getId()));

            List<String> repeatIds = data.getSampleReserve2Tests().stream().map(DtoSampleReserve2Test::getAnalyzeItemId).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(toTests)) {
                //已领取但不重复的分析项目
                List<DtoSampleReserve2Test> notUpdate = toTests.stream().filter(p -> !repeatIds.contains(p.getAnalyzeItemId())).collect(Collectors.toList());
                //如果有则需要将重复的分析项目领取信息新增并删除重复的数据
                if (StringUtil.isNotEmpty(notUpdate)) {
                    //获取重复的数据
                    List<DtoSampleReserve2Test> update = toTests.stream().filter(p -> repeatIds.contains(p.getAnalyzeItemId())).collect(Collectors.toList());
                    List<String> updateAnaId = update.stream().map(DtoSampleReserve2Test::getAnalyzeItemId).collect(Collectors.toList());
                    //讲重复的数据作为新增的领取记录
                    addBatchInsert(batchInsert, batchInsertToTest, sampleReserve, updateAnaId, data.getSampleId());
                    List<String> toTestIds = update.stream().map(DtoSampleReserve2Test::getId).collect(Collectors.toList());
                    sampleReserve2TestRepository.logicDeleteById(toTestIds);
                    continue;
                }
            }
            reserve.setId(data.getId());
            //添加需要更新的子表数据
            batchUpdateToTest.addAll(getToTest(anaItemIds, reserve));
            reserve.setReserveDate(sampleReserve.getReserveDate());
            reserve.setSampleId(data.getSampleId());
            reserve.setReserveType(sampleReserve.getReserveType());
            reserve.setDisposeMethod(sampleReserve.getDisposeMethod());
            reserve.setRemark(sampleReserve.getRemark());
            reserve.setReservePersonId(sampleReserve.getReservePersonId());
            reserve.setCreator(data.getCreator());
            reserve.setCreateDate(data.getCreateDate());
            reserve.setDomainId(data.getDomainId());
            reserve.setModifier(data.getModifier());
            reserve.setOrgId(data.getOrgId());
            reserve.setModifyDate(data.getModifyDate());
            batchUpdate.add(reserve);
        }
    }

    /**
     * 添加子表关联数据
     *
     * @param anaItemIds 测试项目Id
     * @param reserve    对应的父数据
     * @return 子表关联数据
     */
    private List<DtoSampleReserve2Test> getToTest(List<String> anaItemIds, DtoSampleReserve reserve) {
        List<DtoSampleReserve2Test> result = new ArrayList<>();
        List<DtoAnalyzeItem> analyzeItems = new ArrayList<>();
        if (StringUtil.isNotEmpty(anaItemIds)) {
            analyzeItems = analyzeItemRepository.findAll(anaItemIds);
        }
        if (StringUtil.isNotEmpty(analyzeItems)) {
            analyzeItems.forEach(p -> {
                DtoSampleReserve2Test toTest = new DtoSampleReserve2Test();
                toTest.setReserveId(reserve.getId());
                toTest.setAnalyzeItemId(p.getId());
                toTest.setRedAnalyseItemName(p.getAnalyzeItemName());
                result.add(toTest);
            });
        }
        return result;
    }

    /**
     * 获取已选择的测试项目中此样品下的关联测试项目
     *
     * @param anaItemIds    测试项目Id集合
     * @param sampleId      样品Id
     * @param analyseData   测试项目数据
     * @param sampleReserve 领取操作数据
     */
    private void getTest(List<String> anaItemIds, String sampleId, List<DtoAnalyseData> analyseData,
                         DtoSampleReserve sampleReserve) {
        //获取已选择的测试项目中此样品下的关联测试项目
        if (StringUtil.isNotEmpty(analyseData)
                && EnumLIM.EnumReserveType.领取.getValue().toString().equals(sampleReserve.getReserveType().toString())) {
            List<DtoAnalyseData> analyseDataOfSample = analyseData
                    .stream().filter(p -> sampleId.equals(p.getSampleId()))
                    .collect(Collectors.toList());
            if (StringUtil.isNotEmpty(analyseDataOfSample)) {
                List<DtoAnalyseData> anaDataOfAnaItem = analyseDataOfSample
                        .stream().filter(p -> sampleReserve.getAnalyzeItemIds().contains(p.getAnalyseItemId()))
                        .collect(Collectors.toList());
                //获取分析项目Id
                List<String> tempList = anaDataOfAnaItem
                        .stream().map(DtoAnalyseData::getAnalyseItemId)
                        .collect(Collectors.toList());
                anaItemIds.addAll(tempList);
            }
        }
    }

    /**
     * 设置项目类型
     *
     * @param projectList  项目数据
     * @param projectTypes 项目类型数据
     * @param prjectId     项目id
     * @param sample       需要设置id样品
     */
    private void setProjectType(List<DtoProject> projectList, List<DtoProjectType> projectTypes, String prjectId, DtoSample sample) {
        Optional<DtoProject> projectOptional = projectList.stream().filter(p -> prjectId.equals(p.getId())).findFirst();
        projectOptional.ifPresent(p -> {
            Optional<DtoProjectType> projectType = projectTypes.stream().filter(t -> p.getProjectTypeId().equals(t.getId())).findFirst();
            String projectTypeId = "";
            String projectTypeName = "";
            if (projectType.isPresent()) {
                projectTypeId = projectType.get().getId();
                projectTypeName = projectType.get().getName();
            }
            sample.setProjectName(p.getProjectName());
            sample.setProjectCode(p.getProjectCode());
            sample.setProjectTypeName(projectTypeName);
            sample.setProjectTypeId(projectTypeId);
        });
    }

    @Autowired
    public void setReceiveSubSampleRecordRepository(ReceiveSubSampleRecordRepository receiveSubSampleRecordRepository) {
        this.receiveSubSampleRecordRepository = receiveSubSampleRecordRepository;
    }

    @Autowired
    public void setToSampleRepository(ReceiveSubSampleRecord2SampleRepository toSampleRepository) {
        this.toSampleRepository = toSampleRepository;
    }

    @Autowired
    public void setSampleReserve2TestRepository(SampleReserve2TestRepository sampleReserve2TestRepository) {
        this.sampleReserve2TestRepository = sampleReserve2TestRepository;
    }

    @Autowired
    public void setAnalyseDataRepository(AnalyseDataRepository analyseDataRepository) {
        this.analyseDataRepository = analyseDataRepository;
    }

    @Autowired
    public void setSampleDispose2TestRepository(SampleDispose2TestRepository sampleDispose2TestRepository) {
        this.sampleDispose2TestRepository = sampleDispose2TestRepository;
    }

    @Autowired
    public void setSampleDisposeRepository(SampleDisposeRepository sampleDisposeRepository) {
        this.sampleDisposeRepository = sampleDisposeRepository;
    }

    @Autowired
    public void setSampleRepository(SampleRepository sampleRepository) {
        this.sampleRepository = sampleRepository;
    }

    @Autowired
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }

    private PersonRepository personRepository;

    @Autowired
    public void setAnalyzeItemRepository(AnalyzeItemRepository analyzeItemRepository) {
        this.analyzeItemRepository = analyzeItemRepository;
    }

    @Autowired
    public void setProjectRepository(ProjectRepository projectRepository) {
        this.projectRepository = projectRepository;
    }

    @Autowired
    public void setProjectTypeService(ProjectTypeService projectTypeService) {
        this.projectTypeService = projectTypeService;
    }

    @Autowired
    public void setReceiveSampleRecordService(ReceiveSampleRecordService receiveSampleRecordService) {
        this.receiveSampleRecordService = receiveSampleRecordService;
    }
}
