package com.sinoyd.base.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.Data;

/**
 *   测试项目公式导入（新增）easypoi实体
 *   对应模板sheet2
 */
@Data
public class DtoImportTestFormulaParams extends PoiBaseEntity {
    /**
     * id
     */
    @Excel(name = "公式id",orderNum = "100",width = 35)
    private String id;

    /**
     * 检测类型
     */
    @Excel(name = "检测类型",orderNum = "200",width = 15)
    private String sampleType;

    /**
     * 分析项目
     */
    @Excel(name = "分析项目",orderNum = "300",width = 15)
    private String analyzeItem;

    /**
     * 分析方法
     */
    @Excel(name = "分析方法",orderNum = "400",width = 50)
    private String analyzeMethod;

    /**
     * 检测类型（公式）
     */
    @Excel(name = "检测类型（公式）",orderNum = "500",width = 20)
    private String sampleTypeForFormula;

    /**
     * 公式
     */
    @Excel(name = "公式",orderNum = "600",width = 30)
    private String formula;

    /**
     * 参数名称
     */
    @Excel(name = "参数名称",orderNum = "700",width = 10)
    private String paramName;

    /**
     * 默认值
     */
    @Excel(name = "默认值",orderNum = "800",width = 10)
    private String defaultValue;

    /**
     * 排序值
     */
    @Excel(name = "排序值",orderNum = "900",width = 10)
    private Integer sortNo;

    /**
     * 量纲
     */
    @Excel(name = "量纲",orderNum = "1000",width = 10)
    private String dimension;

    /**
     * 公式
     */
    @Excel(name = "公式",groupName = "参数公式",orderNum = "1100",fixedIndex = 10,width = 40)
    private String partFormula;

    /**
     * 有效位数
     */
    @Excel(name = "有效位数",groupName = "参数公式",orderNum = "1200",fixedIndex = 11,width = 10)
    private Integer mostSignificance;

    /**
     * 小数位数
     */
    @Excel(name = "小数位数",groupName = "参数公式",orderNum = "1300",fixedIndex = 12,width = 10)
    private Integer mostDecimal;
}
