package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import com.sinoyd.frame.configuration.PrincipalContextUser;


/**
 * SamplingCarConfig实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="SamplingCarConfig")
 @Data
 public  class SamplingCarConfig implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  SamplingCarConfig() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 对象id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("对象id")
	private String objectId;
    
    /**
    * 对象类型(枚举EnumSamplingCarType：0.任务1.送样单)
    */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("对象类型(枚举EnumSamplingCarType：0.任务1.送样单)")
	private Integer objectType;
    
    /**
    * 车辆的id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("车辆的id")
	private String carId;
    
    /**
    * 驾驶员id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("驾驶员id")
	private String driverId;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
 }