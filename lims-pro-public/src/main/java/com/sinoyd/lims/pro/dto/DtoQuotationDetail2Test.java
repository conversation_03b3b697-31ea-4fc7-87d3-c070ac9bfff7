package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.QuotationDetail2Test;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoQuotationDetail2Test实体
 * <AUTHOR>
 * @version V1.0.0 2022/5/23
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_QuotationDetail2Test")
 @Data
 @DynamicInsert
 public  class DtoQuotationDetail2Test extends QuotationDetail2Test {
   private static final long serialVersionUID = 1L;
 }