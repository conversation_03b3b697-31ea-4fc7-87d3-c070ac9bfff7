package com.sinoyd.lims.od.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.od.dto.DtoOdPersonAnswer;

import java.util.List;

/**
 * OdPersonAnswerRepository
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
public interface OdPersonAnswerRepository extends IBaseJpaRepository<DtoOdPersonAnswer, String> {


    /**
     * 根据实验次序id 查询嗅辨员答案
     *
     * @param labSeqId 实验次序id
     * @return 嗅辨员答案集合
     */
    List<DtoOdPersonAnswer> findByLabSeqId(String labSeqId);

    /**
     * 根据实验次序ids 查询嗅辨员答案
     *
     * @param labSeqIds 实验次序ids
     * @return 嗅辨员答案集合
     */
    List<DtoOdPersonAnswer> findByLabSeqIdIn(List<String> labSeqIds);
}
