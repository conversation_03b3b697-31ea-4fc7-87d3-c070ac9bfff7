package com.sinoyd.lims.pro.dto.customer;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 质控数据的明细
 * <AUTHOR>
 * @version V1.0.0 2020/02/10
 * @since V100R001
 */
@Data
public class DtoQCDataDetail {
    /**
     * 分析数据id
     */
    private String id;

    /**
     * 分析人员id
     */
    private String analystId;

    /**
     * 分析人员名称
     */
    private String analystName;

    /**
     * 分析时间
     */
    @JsonFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date analyzeTime;

    /**
     * 出证结果
     */
    private String testValue;

    /**
     * 参数名称
     */
    private String paramsName;


    /**
     * 检测单号
     */
    private String workSheetCode;
}
