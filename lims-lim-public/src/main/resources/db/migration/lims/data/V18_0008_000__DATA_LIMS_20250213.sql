-- 技术依据信息（采样方法）组件配置
INSERT INTO tb_lim_reportmodule(id, moduleCode, moduleName, tableName, sourceTableName,
                                               sampleCount, testCount, sonTableJson, isCompound, orgId,
                                               creator, createDate, domainId, modifier, modifyDate,
                                               totalTest, auxiliaryInstrument, conversionCalculationMode,
                                               speedCalculationMode, compoundAvgCalculationMode,
                                               gasParamSplitMode)
VALUES ('825b95e9-25a2-4fa9-8e4b-f5edb082c550', 'dtSamplingCriStdTable', '标准版技术依据信息检测结果表组件（采样方法）',
        'dtSamplingCriStdTable', 'dtSamplingCriSource', 10, 0, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-13 10:00:44', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2025-02-13 10:00:44', '0', '0', 0, 0, 0, 1);