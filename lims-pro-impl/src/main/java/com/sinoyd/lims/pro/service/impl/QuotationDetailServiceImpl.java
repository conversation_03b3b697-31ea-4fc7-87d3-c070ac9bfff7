package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.common.utils.SortUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.rcc.DtoCost;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.lims.DtoTestExpand;
import com.sinoyd.lims.lim.dto.rcc.DtoSampleType2Test;
import com.sinoyd.lims.lim.entity.Test;
import com.sinoyd.lims.lim.repository.lims.TestExpandRepository;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.SampleType2TestRepository;
import com.sinoyd.lims.lim.service.CostService;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedPoint2Test;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;
import com.sinoyd.lims.monitor.repository.rcc.FixedPoint2TestRepository;
import com.sinoyd.lims.monitor.repository.rcc.FixedpointRepository;
import com.sinoyd.lims.pro.criteria.QuotationDetailCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoQuotationData;
import com.sinoyd.lims.pro.dto.customer.DtoSampleFolderTemp;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.OrderQuotationRepository;
import com.sinoyd.lims.pro.repository.QuotationDetail2TestRepository;
import com.sinoyd.lims.pro.repository.QuotationDetailRepository;
import com.sinoyd.lims.pro.service.LogForOrderFormService;
import com.sinoyd.lims.pro.service.OrderFormService;
import com.sinoyd.lims.pro.service.ProjectService;
import com.sinoyd.lims.pro.service.QuotationDetailService;
import com.sinoyd.lims.pro.vo.AutoProjectInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;


/**
 * QuotationDetail操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2022/5/18
 * @since V100R001
 */
@Service
public class QuotationDetailServiceImpl extends BaseJpaServiceImpl<DtoQuotationDetail, String, QuotationDetailRepository> implements QuotationDetailService {

    @Autowired
    private TestRepository testRepository;

    @Autowired
    private TestExpandRepository testExpandRepository;

    @Autowired
    private QuotationDetail2TestRepository quotationDetail2TestRepository;

    @Autowired
    private QuotationDetailRepository quotationDetailRepository;

    @Autowired
    private SampleTypeRepository sampleTypeRepository;

    @Autowired
    @Lazy
    private OrderFormService orderFormService;

    @Autowired
    @Lazy
    private CostService costService;

    @Autowired
    @Lazy
    private FixedpointRepository fixedpointRepository;

    @Autowired
    @Lazy
    private FixedPoint2TestRepository fixedPoint2TestRepository;

    @Autowired
    @Lazy
    private OrderQuotationRepository orderQuotationRepository;

    @Autowired
    @Lazy
    private LogForOrderFormService logForOrderFormService;

    @Autowired
    private SampleType2TestRepository sampleType2TestRepository;

    private ProjectService projectService;

    @Override
    public void findByPage(PageBean<DtoQuotationDetail> pb, BaseCriteria quotationDetailCriteria) {
        QuotationDetailCriteria criteria = (QuotationDetailCriteria) quotationDetailCriteria;
        if (StringUtil.isNotEmpty(criteria.getOrderId())) {
            //更新下检测数
            orderFormService.updateCount(criteria.getOrderId());
            comRepository.clear();
        }
        pb.setEntityName("DtoQuotationDetail a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, quotationDetailCriteria);
        List<DtoQuotationDetail> datas = pb.getData();
        List<DtoQuotationDetail> newDatas = new ArrayList<>();
        if (StringUtil.isNotEmpty(datas)) {
            List<String> sampleTypeIds = datas.stream().map(DtoQuotationDetail::getSampleTypeId).distinct().collect(Collectors.toList());
            List<String> testIds = datas.stream().map(DtoQuotationDetail::getTestId).distinct().collect(Collectors.toList());
            List<DtoSampleType> sampleTypeList = sampleTypeRepository.findAll(sampleTypeIds);
            List<DtoTest> testList = StringUtil.isNotEmpty(testIds) ? testRepository.findAll(testIds) : new ArrayList<>();
            Iterator<DtoQuotationDetail> ite = datas.iterator();
            while (ite.hasNext()) {
                Object obj = ite.next();
                DtoQuotationDetail quotationDetail = (DtoQuotationDetail) obj;
                if (StringUtil.isNotEmpty(quotationDetail.getFolderName())) {
                    quotationDetail.setFolderList(quotationDetail.getFolderName().split(","));
                    quotationDetail.setFolderCount(quotationDetail.getFolderList().length);
                } else {
                    quotationDetail.setFolderCount(0);
                }
                Optional<DtoSampleType> sampleType = sampleTypeList.stream().filter(q -> quotationDetail.getSampleTypeId().equals(q.getId())).findFirst();
                sampleType.ifPresent(dtoSampleType -> quotationDetail.setSampleTypeName(dtoSampleType.getTypeName()));
                testList.stream().filter(p-> p.getId().equals(quotationDetail.getTestId())).findFirst().ifPresent(t->{
                    quotationDetail.setRedCountryStandard(t.getRedCountryStandard());
                });
                newDatas.add(quotationDetail);
            }
        }
        pb.setData(newDatas);
    }

    /**
     * 新增订单详情
     *
     * @param testList    测试项目集合
     * @param orderId     订单id
     * @param quotationId 费用id
     */
    @Transactional
    @Override
    public void save(List<DtoTest> testList, String orderId, String quotationId, String sampleTypeId, String testTemplateId) {
        List<DtoQuotationDetail> quotationDetailList = new ArrayList<>();
        List<DtoQuotationDetail2Test> quotationDetail2TestList = new ArrayList<>();
        List<String> totalTestIds = testList.stream().filter(Test::getIsTotalTest).map(DtoTest::getId).collect(Collectors.toList());
        List<DtoTest> subitemTestList = new ArrayList<>();
        if (totalTestIds.size() > 0) {
            subitemTestList = testRepository.findByParentIdIn(totalTestIds);
            if (StringUtils.isNotNullAndEmpty(testTemplateId) && !UUIDHelper.GUID_EMPTY.equals(testTemplateId)) {
                List<DtoSampleType2Test> sampleType2TestList = sampleType2TestRepository.findBySampleTypeIdAndTestIdIn(testTemplateId, totalTestIds);
                List<String> parentIds = sampleType2TestList.stream().map(DtoSampleType2Test::getId).collect(Collectors.toList());
                List<DtoSampleType2Test> childConfigTests = StringUtil.isNotEmpty(parentIds) ? sampleType2TestRepository.findByParentIdIn(parentIds) : new ArrayList<>();
                List<String> childTestIds = childConfigTests.stream().map(DtoSampleType2Test::getTestId).distinct().collect(Collectors.toList());
                subitemTestList.removeIf(t -> !childTestIds.contains(t.getId()));
            }
        }
        List<String> allTestIds = testList.stream().map(DtoTest::getId).collect(Collectors.toList());
        testList.forEach(p -> {
            if (p.getChildTest() != null && !p.getChildTest().isEmpty()) {
                allTestIds.addAll(p.getChildTest().stream().map(DtoTest::getId).collect(Collectors.toList()));
            }
        });
        List<DtoCost> costList = new ArrayList<>();
        List<DtoTestExpand> testExpandList = new ArrayList<>();
        if (allTestIds.size() > 0) {
            costList = costService.findByTestIdIn(allTestIds);
            testExpandList = testExpandRepository.findByTestIdIn(allTestIds);
        }
        List<DtoCost> finalCostList = costList;
        List<DtoTestExpand> finalExpandtList = testExpandList;
        testList.forEach(p -> {
            DtoQuotationDetail quotationDetail = new DtoQuotationDetail();
            quotationDetail.setAnalyseItemId(p.getAnalyzeItemId());
            quotationDetail.setAnalyseMethodId(p.getAnalyzeMethodId());
            quotationDetail.setRedAnalyseItemName(p.getRedAnalyzeItemName());
            quotationDetail.setRedAnalyseMethod(p.getRedAnalyzeMethodName());
            quotationDetail.setTestId(p.getId());
            quotationDetail.setSampleTypeId(p.getSampleTypeId());
            quotationDetail.setSamplingPrice(p.getSamplingCharge());
            quotationDetail.setAnalysePrice(p.getTestingCharge());
            Optional<DtoTestExpand> testExpend = finalExpandtList.stream().filter(c -> c.getSampleTypeId().equals(sampleTypeId) && c.getTestId().equals(p.getId())).findFirst();
            if (testExpend.isPresent()) {
                quotationDetail.setTimesOrder(testExpend.get().getTimesOrder());
                quotationDetail.setSampleCount(testExpend.get().getSamplePeriod());
            } else {
                quotationDetail.setTimesOrder(p.getTimesOrder());
                quotationDetail.setSampleCount(p.getSamplePeriod());
            }
            quotationDetail.setIsTotal(p.getIsTotalTest());
            quotationDetail.setOrderId(orderId);
            quotationDetail.setQuotationId(quotationId);
            quotationDetail.setSampleOrder(0);
            quotationDetail.setResidueCount(0);
            quotationDetailList.add(quotationDetail);
            handleTotalTestPrice(quotationDetail2TestList, quotationDetail,p ,finalCostList, sampleTypeId);
        });
        super.save(quotationDetailList);
        quotationDetail2TestRepository.save(quotationDetail2TestList);
        logForOrderFormService.save(new DtoLogForOrderForm("新增订单详情", orderId, 2, 2,
                PrincipalContextUser.getPrincipal().getUserName() + "于" + DateUtil.dateToString(new Date(), DateUtil.YEAR) + "新增订单详情；" + testList.stream().map(DtoTest::getRedAnalyzeItemName).collect(Collectors.joining(",")), "", ""));
    }

    @Override
    public void handleTotalTestPrice(List<DtoQuotationDetail2Test> quotationDetail2TestList,
                                      DtoQuotationDetail quotationDetail,
                                      DtoTest test, List<DtoCost> costList,
                                      String sampleTypeId){
        //如果是总称的测试项目，需要添加子项测试项目
        String priceTestId = test.getId();
        // 总称配置了优先获取总称的检测费和分析费，如果总称没有配置就去获取子项的分析费和采样费，只要有子项配置了就不能为空
        Optional<DtoCost> cost = costList.stream().filter(c -> c.getSampleTypeId().equals(test.getSampleTypeId()) && c.getTestId().equals(test.getId())).findFirst();
        if (!cost.isPresent() && StringUtil.isNotEmpty(sampleTypeId)) {
            cost = costList.stream().filter(c -> c.getSampleTypeId().equals(sampleTypeId) && c.getTestId().equals(test.getId())).findFirst();
        }
        if (test.getIsTotalTest()) {
            List<DtoTest> stList = test.getChildTest();
            if (stList.size() > 0) {
                stList.forEach(s -> {
                    DtoQuotationDetail2Test quotationDetail2Test = new DtoQuotationDetail2Test();
                    quotationDetail2Test.setTestId(s.getId());
                    quotationDetail2Test.setDetailId(quotationDetail.getId());
                    quotationDetail2TestList.add(quotationDetail2Test);
                });
                //BUG2024060601789 【重要】【2024-6-11】【马川江】【订单管理】96:25001，添加土壤的苯胺，检测费用配置上已经配置了费用，但是新增的检测费用明细上，费用未自动获取。
                // 总成检测费用为空时获取子项检测费用不为空的任意一个
                if (!cost.isPresent()) {
                    List<String> stIds = stList.stream().map(DtoTest::getId).collect(Collectors.toList());
                    cost = costList.stream().filter(c -> stIds.contains(c.getTestId())
                            && StringUtil.isNotNull(c.getSamplingCost()) && (c.getSamplingCost().compareTo(BigDecimal.ZERO) != 0)
                            && StringUtil.isNotNull(c.getAnalyzeCost()) && (c.getAnalyzeCost().compareTo(BigDecimal.ZERO) != 0)).findFirst();
                    if (cost.isPresent()) {
                        priceTestId = cost.get().getTestId();
                    }
                }
            }
        }
        String finalPriceTestId = priceTestId;
        //检查费分析费，先找大类上的，大类没有再找小类上的
        cost.ifPresent(dtoCost -> {
            if (StringUtil.isNotNull(dtoCost.getSamplingCost())) {
                quotationDetail.setSamplingPrice(dtoCost.getSamplingCost());
            } else {
                quotationDetail.setSamplingPrice(BigDecimal.ZERO);
            }
            if (StringUtil.isNotNull(dtoCost.getAnalyzeCost())) {
                quotationDetail.setAnalysePrice(dtoCost.getAnalyzeCost());
            } else {
                quotationDetail.setAnalysePrice(BigDecimal.ZERO);
            }
        });
        if (StringUtil.isNotEmpty(sampleTypeId)) {
            quotationDetail.setSampleTypeId(sampleTypeId);
            cost = costList.stream().filter(c -> c.getSampleTypeId().equals(sampleTypeId) && c.getTestId().equals(finalPriceTestId)).findFirst();
            cost.ifPresent(dtoCost -> {
                if (StringUtil.isNotNull(dtoCost.getSamplingCost()) && (dtoCost.getSamplingCost().compareTo(BigDecimal.ZERO) != 0)) {
                    quotationDetail.setSamplingPrice(dtoCost.getSamplingCost());
                } else if (StringUtil.isNull(dtoCost.getSamplingCost())) {
                    quotationDetail.setSamplingPrice(BigDecimal.ZERO);
                }
                if (StringUtil.isNotNull(dtoCost.getAnalyzeCost()) && (dtoCost.getAnalyzeCost().compareTo(BigDecimal.ZERO) != 0)) {
                    quotationDetail.setAnalysePrice(dtoCost.getAnalyzeCost());
                } else if (StringUtil.isNull(dtoCost.getAnalyzeCost())) {
                    quotationDetail.setAnalysePrice(BigDecimal.ZERO);
                }
            });
        }
    }

    /**
     * 修改费用详情
     *
     * @param detail 修改的费用详情
     * @return 费用详情
     */
    @Transactional
    @Override
    public DtoQuotationDetail update(DtoQuotationDetail detail) {
        detail.setFolderName(String.join(",", detail.getFolderList()));
        Integer folderCount = detail.getFolderList().length;
        //总检数
        int totalCount = detail.getCycleOrder() * folderCount * detail.getTimesOrder() * detail.getProjectCount() * detail.getSampleCount();
        detail.setSampleOrder(totalCount);
        //剩检数
        Integer count = detail.getSampleOrder() - detail.getInspectedCount();
        detail.setResidueCount(count);
        //小计
        detail.setCharge((detail.getSamplingPrice().add(detail.getAnalysePrice())).multiply(BigDecimal.valueOf(totalCount)));
        //客户人为修改的值
        if (!detail.getIsChange()) {
            detail.setQuotationPrice(detail.getCharge());
        }
        DtoQuotationDetail quotationDetail = super.update(detail);
        quotationDetail.setFolderList(detail.getFolderList());
        quotationDetail.setFolderCount(folderCount);
        quotationDetail.setSampleTypeName(detail.getSampleTypeName());
        quotationDetail.setIsChange(false);
        quotationDetail.setRedCountryStandard(detail.getRedCountryStandard());
        logForOrderFormService.save(new DtoLogForOrderForm("修改订单费用详情", quotationDetail.getOrderId(), 2, 2,
                PrincipalContextUser.getPrincipal().getUserName() + "于" + DateUtil.dateToString(new Date(), DateUtil.YEAR) + "修改订单费用详情", "", ""));
        return quotationDetail;
    }

    /**
     * 根据明细获取子项测试项目
     *
     * @param detailId 明细id
     * @return 子项测试项目
     */
    @Override
    public List<DtoTest> findByTestList(String detailId) {
        List<String> testIds = quotationDetail2TestRepository.findByDetailId(detailId).stream()
                .map(DtoQuotationDetail2Test::getTestId).collect(Collectors.toList());
        List<DtoTest> testList = new ArrayList<>();
        if (testIds.size() > 0) {
            testList = testRepository.findAll(testIds);
        }
        return testList;
    }

    /**
     * 新增明细子项
     *
     * @param detailId 明细id
     * @param testIds  测试项目id
     */
    @Transactional
    @Override
    public void addTestList(String detailId, List<String> testIds) {
        List<DtoQuotationDetail2Test> quotationDetail2TestList = new ArrayList<>();
        testIds.forEach(p -> {
            DtoQuotationDetail2Test quotationDetail2Test = new DtoQuotationDetail2Test();
            quotationDetail2Test.setDetailId(detailId);
            quotationDetail2Test.setTestId(p);
            quotationDetail2TestList.add(quotationDetail2Test);
        });
        quotationDetail2TestRepository.save(quotationDetail2TestList);
    }

    /**
     * 删除明细子项
     *
     * @param detailId 明细id
     * @param testIds  测试项目ids
     */
    @Transactional
    @Override
    public void deleteTestList(String detailId, List<String> testIds) {
        List<DtoQuotationDetail2Test> quotationDetail2TestList = quotationDetail2TestRepository.findByDetailIdAndTestIdIn(detailId, testIds);
        quotationDetail2TestRepository.delete(quotationDetail2TestList);
    }

    /**
     * 批量修改明细
     *
     * @param detailIds        明细ids
     * @param dtoQuotationData 修改内容
     */
    @Transactional
    @Override
    public void updateBatch(List<String> detailIds, DtoQuotationData dtoQuotationData) {
        List<DtoQuotationDetail> quotationDetailList = quotationDetailRepository.findAll(detailIds);
        quotationDetailList.forEach(p -> {
            //点位名称
            if (dtoQuotationData.getFolderList().length > 0) {
                p.setFolderName(String.join(",", dtoQuotationData.getFolderList()));
            }
            //任务数
            if (StringUtil.isNotNull(dtoQuotationData.getProjectCount())) {
                p.setProjectCount(dtoQuotationData.getProjectCount());
            }
            //周期
            if (StringUtil.isNotNull(dtoQuotationData.getCycleOrder())) {
                p.setCycleOrder(dtoQuotationData.getCycleOrder());
            }
            //批次
            if (StringUtil.isNotNull(dtoQuotationData.getTimesOrder())) {
                p.setTimesOrder(dtoQuotationData.getTimesOrder());
            }
            //样品数量
            if (StringUtil.isNotNull(dtoQuotationData.getSampleCount())) {
                p.setSampleCount(dtoQuotationData.getSampleCount());
            }
            //采样费
            if (StringUtil.isNotNull(dtoQuotationData.getSamplingPrice())) {
                p.setSamplingPrice(dtoQuotationData.getSamplingPrice());
            }
            //分析费
            if (StringUtil.isNotNull(dtoQuotationData.getAnalysePrice())) {
                p.setAnalysePrice(dtoQuotationData.getAnalysePrice());
            }
            //监测间隔
            if (StringUtil.isNotNull(dtoQuotationData.getProjectInterval())) {
                p.setProjectInterval(dtoQuotationData.getProjectInterval());
            }
            setFolderValue(p);
        });
        quotationDetailRepository.save(quotationDetailList);
    }

    /**
     * 批量添加点位信息
     *
     * @param detailIds        明细ids
     * @param dtoQuotationData 修改内容
     */
    @Override
    public void insertFolderBatch(List<String> detailIds, DtoQuotationData dtoQuotationData) {
        List<DtoQuotationDetail> quotationDetailList = quotationDetailRepository.findAll(detailIds);
        String[] folderList = dtoQuotationData.getFolderList();

        if (folderList.length > 0) {
            List<String> folderNamesToAdd = Arrays.asList(folderList);
            quotationDetailList.forEach(p -> {
                // 点位名称
                if (StringUtil.isNotEmpty(p.getFolderName())) {
                    List<String> folderNames = new ArrayList<>(Arrays.asList(p.getFolderName().split(",")));
                    folderNames.removeAll(folderNamesToAdd);
                    folderNames.addAll(folderNamesToAdd);
                    p.setFolderName(String.join(",", folderNames));
                } else {
                    p.setFolderName(String.join(",", folderList));
                }
                setFolderValue(p);
            });
        }
        logForOrderFormService.save(new DtoLogForOrderForm("批量添加订单点位", quotationDetailList.get(0).getOrderId(), 2, 2,
                PrincipalContextUser.getPrincipal().getUserName() + "于" + DateUtil.dateToString(new Date(), DateUtil.YEAR) + "批量添加订单点位：" + Arrays.stream(folderList).map(str -> str.toString()).collect(Collectors.joining(",")), "", ""));
        quotationDetailRepository.save(quotationDetailList);
    }

    @Override
    public Collection<String> findAllFolder(List<String> detailIds) {
        List<DtoQuotationDetail> quotationDetailList = repository.findAll(detailIds);
        Set<String> set = new HashSet<>();
        quotationDetailList.forEach(detail -> {
            if (StringUtil.isNotEmpty(detail.getFolderName())) {
                set.addAll(Arrays.asList(detail.getFolderName().split(",")));
            }
        });
        return set;
    }

    @Override
    @Transactional
    public void batchDeleteFolder(List<String> detailIds, DtoQuotationData quotationData) {
        List<DtoQuotationDetail> quotationDetailList = quotationDetailRepository.findAll(detailIds);
        String[] folderList = quotationData.getFolderList();
        if (folderList.length > 0) {
            List<String> folderNamesToDel = Arrays.asList(folderList);
            quotationDetailList.forEach(p -> {
                // 点位名称
                if (StringUtil.isNotEmpty(p.getFolderName())) {
                    List<String> folderNames = new ArrayList<>(Arrays.asList(p.getFolderName().split(",")));
                    folderNames.removeAll(folderNamesToDel);
                    p.setFolderName(String.join(",", folderNames));
                }
                setFolderValue(p);
            });
        }
        quotationDetailRepository.save(quotationDetailList);
    }

    /**
     * 根据拆单信息创建项目
     *
     * @param orderId    订单id
     * @param infoVOList 拆单详情
     */
    @Override
    @Transactional
    public void autoProjectByDetails(String orderId, List<AutoProjectInfoVO> infoVOList, Boolean isSampling) {
        DtoOrderForm orderForm = orderFormService.findOne(orderId);
        List<DtoProject> projectList = new ArrayList<>();
        Map<String, List<DtoQuotationDetail>> projectDetailMap = new HashMap<>();
        for (AutoProjectInfoVO infoVO : infoVOList) {
            DtoProject project = new DtoProject();
            //项目类型
            project.setProjectTypeId(orderForm.getProjectTypeId());
            //项目名称
            project.setProjectName(orderForm.getOrderName());
            project.setOrderId(orderId);
            //登记时间
            String time = String.format("%s.01", infoVO.getYmMonitor());
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(DateUtil.stringToDate(time, "yyyy.MM.dd"));
            project.setInputTime(calendar.getTime());
            project.setInceptTime(calendar.getTime());
            project.setLastModifyTime(calendar.getTime());
            project.setSamplingDate(DateUtil.dateToString(calendar.getTime(), DateUtil.FULL));
            //受检单位id
            project.setCustomerId(orderForm.getEnterpriseId());
            project.setCustomerName(orderForm.getEnterpriseName());
            project.setLinkMan(orderForm.getLinkPerson());
            project.setLinkPhone(orderForm.getLinkPhone());
            project.setCustomerAddress(orderForm.getAddress());
            //委托单位id
            project.setInspectedEntId(orderForm.getEnterpriseId());
            project.setInspectedEnt(orderForm.getEnterpriseName());
            project.setInspectedLinkMan(orderForm.getLinkPerson());
            project.setInspectedLinkPhone(orderForm.getLinkPhone());
            project.setInspectedAddress(orderForm.getAddress());
            //登记人id
            project.setInceptPersonId(PrincipalContextUser.getPrincipal().getUserId());
            project.setInceptPersonName(PrincipalContextUser.getPrincipal().getUserName());

            project.setIsCMA(0);
            project.setIsAccredited(true);
            project.setIsMakePlan(true);
            project.setIsStress(false);
            project.setIsWarning(false);
            project.setLeaderId(PrincipalContextUser.getPrincipal().getUserId());
            project.setLeaderName(PrincipalContextUser.getPrincipal().getUserName());
            project.setMonitorMethod(1);
            project.setReportNum("1");
            project.setPostMethod(0);
            project.setGrade(0);
            calendar.add(Calendar.MONTH, 1);
            project.setReportDate(calendar.getTime());
            project.setDeadLine(calendar.getTime());
            projectDetailMap.put(project.getId(), infoVO.getDetailList());
            projectList.add(project);
        }
        if (projectList.size() > 0) {
            projectService.saveBatchProject(projectList, isSampling, projectDetailMap);
        }
    }

    /**
     * 订单自动拆单
     *
     * @param year    年份
     * @param month   月份
     * @param count   拆单数
     * @param orderId 订单id
     * @return 自动拆单集合
     */
    @Override
    public List<AutoProjectInfoVO> autoDisassembleOrderProject(Integer year, Integer month, Integer count, String orderId) {
        // 获取订单明细并按时间间隔分类
        Map<String, List<DtoQuotationDetail>> intervalDetailsMap = getIntervalDetailsMap(orderId);

        // 生成基准时间
        Date baseTime = DateUtil.stringToDate(String.format("%d-%d-%d", year, month, 1), DateUtil.YEAR);

        // 生成所有时间点的项目信息
        List<AutoProjectInfoVO> infoVOList = generateProjectInfos(intervalDetailsMap, baseTime, count);

        // 按时间间隔和监测年月排序
        return infoVOList.stream()
                .sorted(Comparator.comparing(AutoProjectInfoVO::getInterval)
                        .thenComparing(AutoProjectInfoVO::getYmMonitor))
                .collect(Collectors.toList());
    }

    /**
     * 获取按时间间隔分类的订单明细
     */
    private Map<String, List<DtoQuotationDetail>> getIntervalDetailsMap(String orderId) {
        List<DtoQuotationDetail> detailList = repository.findByOrderId(orderId);
        Map<String, List<DtoQuotationDetail>> intervalDetailsMap = new HashMap<>();

        // 使用Stream API按时间间隔分类
        Arrays.stream(EnumPRO.EnumDisassembleType.values()).forEach(type -> intervalDetailsMap.put(type.name(),
                detailList.stream().filter(p -> p.getProjectInterval().equals(type.name())).collect(Collectors.toList())));

        return intervalDetailsMap;
    }

    /**
     * 生成所有时间点的项目信息
     */
    private List<AutoProjectInfoVO> generateProjectInfos(Map<String, List<DtoQuotationDetail>> intervalDetailsMap,
                                                         Date baseTime, Integer count) {

        List<AutoProjectInfoVO> infoVOList = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();

        for (int i = 0; i < count; i++) {
            // 处理年度项目
            processIntervalProjects(intervalDetailsMap.get(EnumPRO.EnumDisassembleType.年度.name()),
                    baseTime, i, calendar, infoVOList, EnumPRO.EnumDisassembleType.年度.name(),
                    (c, offset) -> c.add(Calendar.YEAR, offset));

            // 处理半年度项目
            processIntervalProjects(intervalDetailsMap.get(EnumPRO.EnumDisassembleType.半年度.name()),
                    baseTime, i, calendar, infoVOList, EnumPRO.EnumDisassembleType.半年度.name(),
                    (c, offset) -> c.add(Calendar.MONTH, offset * 6));

            // 处理季度项目
            processIntervalProjects(intervalDetailsMap.get(EnumPRO.EnumDisassembleType.季度.name()),
                    baseTime, i, calendar, infoVOList, EnumPRO.EnumDisassembleType.季度.name(),
                    (c, offset) -> c.add(Calendar.MONTH, offset * 3));

            // 处理双月和单月项目
            processBiMonthlyAndMonthlyProjects(intervalDetailsMap, baseTime, i, calendar, infoVOList);
        }

        return infoVOList;
    }

    /**
     * 处理指定时间间隔的项目
     */
    private void processIntervalProjects(List<DtoQuotationDetail> details, Date baseTime, int offset, Calendar calendar,
                                         List<AutoProjectInfoVO> infoVOList, String interval, BiConsumer<Calendar, Integer> timeAdjuster) {

        if (details == null || details.isEmpty()) {
            return;
        }

        calendar.setTime(baseTime);
        timeAdjuster.accept(calendar, offset);
        String ymMonitor = DateUtil.dateToString(calendar.getTime(), "yyyy.MM");
        calendar.clear();

        createAndAddProjectInfo(details, ymMonitor, interval, infoVOList);
    }

    /**
     * 处理双月和单月项目
     */
    private void processBiMonthlyAndMonthlyProjects(Map<String, List<DtoQuotationDetail>> intervalDetailsMap,
                                                    Date baseTime, int offset, Calendar calendar,
                                 List<AutoProjectInfoVO> infoVOList) {
        calendar.setTime(baseTime);
        boolean isEvenMonth = (calendar.get(Calendar.MONTH) + 1) % 2 == 0;

        // 处理双月项目
        processIntervalProjects(intervalDetailsMap.get(EnumPRO.EnumDisassembleType.双月.name()),
                baseTime, offset, calendar, infoVOList, EnumPRO.EnumDisassembleType.双月.name(),
                (c, o) -> c.add(Calendar.MONTH, isEvenMonth ? o * 2 : (o * 2) + 1));

        // 处理单月项目
        processIntervalProjects(intervalDetailsMap.get(EnumPRO.EnumDisassembleType.单月.name()),
                baseTime, offset, calendar, infoVOList, EnumPRO.EnumDisassembleType.单月.name(),
                (c, o) -> c.add(Calendar.MONTH, isEvenMonth ? (o * 2) + 1 : o * 2));

        // 处理月度项目
        processIntervalProjects(intervalDetailsMap.get(EnumPRO.EnumDisassembleType.月度.name()),
                baseTime, offset, calendar, infoVOList, EnumPRO.EnumDisassembleType.月度.name(),
                (c, o) -> c.add(Calendar.MONTH, o));
    }

    /**
     * 创建并添加项目信息
     */
    private void createAndAddProjectInfo(List<DtoQuotationDetail> details, String ymMonitor,
                                         String interval, List<AutoProjectInfoVO> infoVOList) {

            AutoProjectInfoVO infoVO = new AutoProjectInfoVO();
            infoVO.setYmMonitor(ymMonitor);
        infoVO.setDetailList(details);
            infoVO.setInterval(interval);

        // 处理点位信息
        Set<String> folderNames = details.stream()
                .map(DtoQuotationDetail::getFolderName)
                .filter(StringUtil::isNotEmpty)
                .flatMap(folder -> Arrays.stream(folder.split(",")))
                .collect(Collectors.toSet());

        infoVO.setFolderName(String.join(",", folderNames));
        infoVO.setFolderCount(folderNames.size());

        // 处理分析项目信息
        Set<String> itemNames = details.stream()
                .map(DtoQuotationDetail::getRedAnalyseItemName)
                .collect(Collectors.toSet());

        infoVO.setAnaItemName(String.join(",", itemNames));
        infoVO.setAnaItemCount(itemNames.size());

            infoVOList.add(infoVO);
    }

    /**
     * 设置点位信息
     *
     * @param detail 订单详细数据
     */
    private void setFolderValue(DtoQuotationDetail detail) {
        int totalCount = 0;
        if (StringUtil.isNotEmpty(detail.getFolderName())) {
            Integer folderCount = detail.getFolderName().split(",").length;
            //检查总数
            totalCount = detail.getCycleOrder() * folderCount * detail.getTimesOrder() * detail.getProjectCount() * detail.getSampleCount();
        }
        detail.setSampleOrder(totalCount);
        //剩检数
        Integer count = detail.getSampleOrder() - detail.getInspectedCount();
        detail.setResidueCount(count);
        //小计
        detail.setCharge((detail.getSamplingPrice().add(detail.getAnalysePrice())).multiply(BigDecimal.valueOf(totalCount)));
        //报价
        detail.setQuotationPrice(detail.getCharge());
    }

    /**
     * 更新检测费
     *
     * @param detailIds 明细ids
     */
    @Transactional
    @Override
    public void updatePrice(List<String> detailIds) {
        List<DtoQuotationDetail> quotationDetailList = quotationDetailRepository.findAll(detailIds);
        List<DtoTest> testList = testRepository.findAll(quotationDetailList.stream().map(DtoQuotationDetail::getTestId).collect(Collectors.toList()));
        List<String> allTestIds = testList.stream().map(DtoTest::getId).collect(Collectors.toList());
        List<DtoCost> costList = new ArrayList<>();
        if (allTestIds.size() > 0) {
            costList = costService.findByTestIdIn(allTestIds);
        }
        List<DtoCost> finalCostList = costList;
        quotationDetailList.forEach(p -> {
            //小计
            Optional<DtoTest> test = testList.stream().filter(q -> p.getTestId().equals(q.getId())).findFirst();
            if (test.isPresent()) {
                p.setSamplingPrice(test.get().getSamplingCharge());
                p.setAnalysePrice(test.get().getTestingCharge());
                //检查费分析费，先找大类上的，大类没有再找小类上的
                //测试项目上的样品类型是大类
                Optional<DtoCost> cost = finalCostList.stream().filter(c -> c.getSampleTypeId().equals(test.get().getSampleTypeId()) && c.getTestId().equals(p.getTestId())).findFirst();
                cost.ifPresent(dtoCost -> {
                    if (StringUtil.isNotNull(dtoCost.getSamplingCost())) {
                        p.setSamplingPrice(dtoCost.getSamplingCost());
                    } else {
                        p.setSamplingPrice(BigDecimal.ZERO);
                    }
                    if (StringUtil.isNotNull(dtoCost.getAnalyzeCost())) {
                        p.setAnalysePrice(dtoCost.getAnalyzeCost());
                    } else {
                        p.setAnalysePrice(BigDecimal.ZERO);
                    }
                });
                //费用详情的样品类型是小类
                cost = finalCostList.stream().filter(c -> c.getSampleTypeId().equals(p.getSampleTypeId()) && c.getTestId().equals(p.getTestId())).findFirst();
                cost.ifPresent(dtoCost -> {
                    if (StringUtil.isNotNull(dtoCost.getSamplingCost()) && (dtoCost.getSamplingCost().compareTo(BigDecimal.ZERO) != 0)) {
                        p.setSamplingPrice(dtoCost.getSamplingCost());
                    } else if (StringUtil.isNull(dtoCost.getSamplingCost())) {
                        p.setSamplingPrice(BigDecimal.ZERO);
                    }
                    if (StringUtil.isNotNull(dtoCost.getAnalyzeCost()) && (dtoCost.getAnalyzeCost().compareTo(BigDecimal.ZERO) != 0)) {
                        p.setAnalysePrice(dtoCost.getAnalyzeCost());
                    } else if (StringUtil.isNull(dtoCost.getAnalyzeCost())) {
                        p.setAnalysePrice(BigDecimal.ZERO);
                    }
                });
                int totalCount = 0;
                if (StringUtil.isNotEmpty(p.getFolderName())) {
                    totalCount = p.getCycleOrder() * p.getFolderName().split(",").length * p.getTimesOrder() * p.getProjectCount();
                }
                p.setCharge((p.getSamplingPrice().add(p.getAnalysePrice())).multiply(BigDecimal.valueOf(totalCount)));
                p.setQuotationPrice(p.getCharge());
            }
        });
        logForOrderFormService.save(new DtoLogForOrderForm("更新订单检测费", quotationDetailList.get(0).getOrderId(), 2, 2,
                PrincipalContextUser.getPrincipal().getUserName() + "于" + DateUtil.dateToString(new Date(), DateUtil.YEAR) + "更新订单检测费", "", ""));
        quotationDetailRepository.save(quotationDetailList);
    }

    /**
     * 新增企业点位
     *
     * @param quotationData 实体数据
     * @return
     */
    @Transactional
    @Override
    public void addFixedPoint(DtoQuotationData quotationData) {

        // 获取所选污染源点位信息
        if (StringUtil.isNotEmpty(quotationData.getFixedPointIds())) {
            //费用记录
            DtoOrderQuotation quotation = orderQuotationRepository.findByOrderId(quotationData.getDetailId());
            List<DtoFixedpoint> fixedPointList = fixedpointRepository.findByIdIn(quotationData.getFixedPointIds());
            List<String> pointIds = fixedPointList.stream().map(DtoFixedpoint::getId).collect(Collectors.toList());
            // 获取污染源点位下关联的测试项目数据
            List<DtoFixedPoint2Test> dtoFixedPoint2TestList = fixedPoint2TestRepository.findByFixedPointIdIn(pointIds);
            // 筛选所有点位id，判断是否存在未配置测试项目的点位数据。
            List<String> point2TestPointIds = StringUtil.isNotEmpty(dtoFixedPoint2TestList) ? dtoFixedPoint2TestList.stream().map(DtoFixedPoint2Test::getFixedPointId).distinct().collect(Collectors.toList()) : new ArrayList<>();
            List<String> missingIds = pointIds.stream().filter(id -> !point2TestPointIds.contains(id)).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(missingIds)) {
                throw new BaseException("存在未配置检测能力的点位！");
            }

            if (StringUtil.isNotEmpty(dtoFixedPoint2TestList)) {
                // 获取测试项目
                List<String> testIds = dtoFixedPoint2TestList.stream().map(DtoFixedPoint2Test::getTestId).distinct().collect(Collectors.toList());
                List<DtoTest> testList = testRepository.findByIdIn(testIds);

                // 筛选检测类型数据
                List<String> sampleTypeIds = fixedPointList.stream().map(DtoFixedpoint::getSampleTypeId).distinct().collect(Collectors.toList());
                List<DtoSampleType> sampleTypeList = sampleTypeRepository.findAll(sampleTypeIds);


                // 定义待处理数据-更新
                List<DtoQuotationDetail> updateQuotationList = new ArrayList<>();
                // 定义待处理数据-保存
                Map<String, List<Map<String, Object>>> map = new HashMap();
                // 根据订单id获取的检测费用明细
                List<DtoQuotationDetail> quotationDetailList = repository.findByOrderId(quotationData.getDetailId());
                for (DtoTest test : testList) {
                    // 获取包含测试项目的点位信息
                    List<String> pointIdList = dtoFixedPoint2TestList.stream().filter(p -> p.getTestId().equals(test.getId())).map(DtoFixedPoint2Test::getFixedPointId).collect(Collectors.toList());
                    List<DtoFixedpoint> containPointList = fixedPointList.stream().filter(p -> pointIdList.contains(p.getId())).collect(Collectors.toList());

                    // 点位下的所有的检测类型
                    List<String> pointSampleTypeIds = containPointList.stream().map(DtoFixedpoint::getSampleTypeId).distinct().collect(Collectors.toList());
                    // 根据测项目项目id和检测类型，筛选已经配置的检测明细，
                    List<DtoQuotationDetail> existQuotationDetails = quotationDetailList.stream().filter(p -> test.getId().equals(p.getTestId()) && pointSampleTypeIds.contains(p.getSampleTypeId())).collect(Collectors.toList());

                    if (StringUtil.isNotEmpty(existQuotationDetails)) {
                        for (String pointSampleTypeId : pointSampleTypeIds) {
                            // 点位名称
                            List<String> pointNames = containPointList.stream().filter(p -> p.getSampleTypeId().equals(pointSampleTypeId)).map(DtoFixedpoint::getPointName).collect(Collectors.toList());
                            // 循环已存在的数据。处理需要添加点位名称
                            Optional<DtoQuotationDetail> dtoQuotationDetail = existQuotationDetails.stream().filter(p -> p.getSampleTypeId().equals(pointSampleTypeId)).findFirst();
                            dtoQuotationDetail.ifPresent(dto -> {
                                String folderName = dto.getFolderName();
                                List<String> folderNames = StringUtil.isNotEmpty(folderName) && StringUtil.isNotNull(folderName) ?
                                        new ArrayList<>(Arrays.asList(folderName.split(","))) : new ArrayList<>();
                                folderNames.removeAll(pointNames);
                                folderNames.addAll(pointNames);
                                dto.setFolderName(String.join(",", folderNames));
                                updateQuotationList.add(dto);
                            });
                        }
                    } else {
                        // 筛选需要新增的数据
                        List<Map<String, Object>> list = new ArrayList<>();
                        for (String pointSampleTypeId : pointSampleTypeIds) {
                            // 根据检测类型筛选点位。
                            List<DtoFixedpoint> dtoFixedPoints = containPointList.stream().filter(p -> p.getSampleTypeId().equals(pointSampleTypeId)).collect(Collectors.toList());
                            // 点位名称
                            List<String> pointNames = dtoFixedPoints.stream().map(DtoFixedpoint::getPointName).collect(Collectors.toList());
                            // 创建新的检测明细
                            // 获取当前测试项目所在的所有点位的最大周期和频次
                            Integer cycleOrder = Objects.requireNonNull(dtoFixedPoints.stream().max(Comparator.comparingInt(DtoFixedpoint::getCycleOrder)).orElse(null)).getCycleOrder();
                            Integer timesOrder = Objects.requireNonNull(dtoFixedPoints.stream().max(Comparator.comparingInt(DtoFixedpoint::getTimesOrder)).orElse(null)).getTimesOrder();
                            Map<String, Object> orderNumMap = new HashMap<>();
                            orderNumMap.put("pointSampleTypeId", pointSampleTypeId);
                            orderNumMap.put("cycleOrder", cycleOrder);
                            orderNumMap.put("timesOrder", timesOrder);
                            orderNumMap.put("folderNames", String.join(",", pointNames));
                            list.add(orderNumMap);
                        }
                        map.put(test.getId(), list);
                    }
                }
                if (map.keySet().size() > 0) {
                    for (String sampleTypeId : sampleTypeIds) {
                        // 根据检测类型筛选待新增信息
                        Map<String, List<Map<String, Object>>> resultMap = new HashMap<>();
                        for (Map.Entry<String, List<Map<String, Object>>> entry : map.entrySet()) {
                            String key = entry.getKey();
                            List<Map<String, Object>> value = entry.getValue();
                            List<Map<String, Object>> filteredList = value.stream()
                                    .filter(p -> p.containsKey("pointSampleTypeId") && p.get("pointSampleTypeId").equals(sampleTypeId)).collect(Collectors.toList());
                            if (!filteredList.isEmpty()) {
                                resultMap.put(key, filteredList);
                            }
                        }
                        List<String> instertTestIds = new ArrayList<>(resultMap.keySet());
                        DtoSampleType sampleType = sampleTypeList.stream().filter(p -> p.getId().equals(sampleTypeId)).findFirst().orElse(null);
                        String printIds = sampleTypeId;
                        if (StringUtil.isNotNull(sampleType) && !UUIDHelper.GUID_EMPTY.equals(sampleType.getParentId())) {
                            printIds = sampleType.getParentId();
                        }
                        String finalPrintIds = printIds;
                        List<DtoTest> finalTests = testList.stream().filter(p -> instertTestIds.contains(p.getId()) && finalPrintIds.equals(p.getSampleTypeId())).collect(Collectors.toList());
                        this.save(finalTests, quotationData.getDetailId(), quotation.getId(), sampleTypeId, UUIDHelper.GUID_EMPTY);
                    }
                    // 更新点位，频次数据
                    List<DtoQuotationDetail> allQuotationDetails = repository.findByOrderId(quotationData.getDetailId());
                    for (String key : map.keySet()) {
                        List<Map<String, Object>> list = map.get(key);
                        for (Map<String, Object> orderNumMap : list) {
                            String pointSampleTypeId = (String) orderNumMap.get("pointSampleTypeId");

                            DtoQuotationDetail dtoQuotationDetail = allQuotationDetails.stream().filter(p -> p.getTestId().equals(key) && p.getSampleTypeId().equals(pointSampleTypeId)).findFirst().orElse(null);
                            if (StringUtil.isNotNull(dtoQuotationDetail)) {
                                dtoQuotationDetail.setCycleOrder((Integer) orderNumMap.get("cycleOrder"));
                                dtoQuotationDetail.setTimesOrder((Integer) orderNumMap.get("timesOrder"));
                                dtoQuotationDetail.setFolderName((String) orderNumMap.get("folderNames"));
                                updateQuotationList.add(dtoQuotationDetail);
                            }
                        }
                    }
                }
                quotationDetailRepository.save(updateQuotationList);
            }
        }
    }

    @Override
    public List<DtoSampleType> findSampleTypes(String orderId) {
        List<DtoSampleType> sampleTypeList = new ArrayList<>();
        if (StringUtil.isNotEmpty(orderId)) {
            // 获取检测费用明细数据
            List<DtoQuotationDetail> dtoQuotationDetailList = repository.findByOrderId(orderId);
            List<String> sampleTypeIds = dtoQuotationDetailList.stream().map(DtoQuotationDetail::getSampleTypeId).distinct().collect(Collectors.toList());
            if (StringUtil.isNotEmpty(sampleTypeIds)) {
                sampleTypeList = sampleTypeRepository.findAll(sampleTypeIds);
            }
        }
        return sampleTypeList;
    }

    /**
     * 根据订单id获取详情信息
     *
     * @param orderId 订单id
     * @return 返回详细信息
     */
    @Override
    public List<DtoQuotationDetail> findByOrderId(String orderId) {
        return repository.findByOrderId(orderId);
    }

    @Override
    public List<Map<String, Object>> schemaFolderShow(String orderId) {
        List<Map<String, Object>> rtList = new ArrayList<>();
        List<DtoQuotationDetail> allQuotationDetailList = repository.findByOrderId(orderId).stream().filter(v -> StringUtil.isNotEmpty(v.getFolderName()))
                .collect(Collectors.toList());
        Set<String> folderList = new HashSet<>();
        allQuotationDetailList.forEach(v -> folderList.addAll(Arrays.asList(v.getFolderName().split(","))));
        List<String> allSampleTypeIds = allQuotationDetailList.stream().map(DtoQuotationDetail::getSampleTypeId).collect(Collectors.toList());
        List<DtoSampleType> allSampleTypeList = StringUtil.isNotEmpty(allSampleTypeIds) ? sampleTypeRepository.findAll(allSampleTypeIds) : new ArrayList<>();
        for (String folder : folderList) {
            Map<String, Object> map = new HashMap<>();
            List<DtoQuotationDetail> quotationDetailList = allQuotationDetailList.stream().filter(v -> Arrays.asList(v.getFolderName().split(",")).contains(folder))
                    .collect(Collectors.toList());
            map.put("watchSpot", folder);
            List<String> sampleTypeIds = quotationDetailList.stream().map(DtoQuotationDetail::getSampleTypeId).distinct().collect(Collectors.toList());
            map.put("sampleTypeIds", sampleTypeIds);
            map.put("sampleTypeName", allSampleTypeList.stream().filter(v -> sampleTypeIds.contains(v.getId())).map(DtoSampleType::getTypeName).collect(Collectors.joining("、")));
            map.put("periodCount", quotationDetailList.stream().mapToInt(DtoQuotationDetail::getCycleOrder).max().getAsInt());
            map.put("quotationDetail", quotationDetailList);
            map.put("itemNames", quotationDetailList.stream().map(DtoQuotationDetail::getRedAnalyseItemName).distinct().sorted().collect(Collectors.joining("、")));
            rtList.add(map);
        }

        rtList.sort(Comparator.comparing((Map<String, Object> v) -> (String) v.get("sampleTypeName"))
                .thenComparing((a, b) -> SortUtil.compareString((String) a.get("watchSpot"), (String) b.get("watchSpot")))
                .thenComparing(v -> (Integer) v.get("periodCount")));
        return rtList;
    }

    /**
     * 根据订单信息生成方案集合
     *
     * @param details 订单明细集合信息
     * @return 方案集合
     */
    @Override
    public List<DtoSampleFolderTemp> generateProjectPlans(List<DtoQuotationDetail> details) {
        if (details == null || details.isEmpty()) {
            return new ArrayList<>();
        }
        // 按点位和样品类型分组
        Map<String, Map<String, List<DtoQuotationDetail>>> pointAndTypeGroups = groupByPointAndType(details);
        // 获取样品类型映射
        Map<String, DtoSampleType> sampleTypeMap = getSampleTypeMap();
        // 生成方案集合
        return generateFolderTemps(pointAndTypeGroups, sampleTypeMap);
    }

    /**
     * 按点位和样品类型分组
     */
    private Map<String, Map<String, List<DtoQuotationDetail>>> groupByPointAndType(List<DtoQuotationDetail> details) {
        return details.stream()
                .filter(detail -> StringUtil.isNotEmpty(detail.getFolderName()))
                .flatMap(detail -> {
                    String[] points = detail.getFolderName().split(",");
                    return Arrays.stream(points).map(point -> new AbstractMap.SimpleEntry<>(point, detail));
                }).collect(Collectors.groupingBy(Map.Entry::getKey, Collectors.mapping(
                        Map.Entry::getValue, Collectors.groupingBy(
                                DtoQuotationDetail::getSampleTypeId, Collectors.toList()))));
    }

    /**
     * 获取样品类型映射
     */
    private Map<String, DtoSampleType> getSampleTypeMap() {
        List<DtoSampleType> sampleTypes = sampleTypeRepository.findAll();
        return sampleTypes.stream().collect(Collectors.toMap(DtoSampleType::getId, type -> type));
    }

    /**
     * 生成文件夹模板集合
     */
    private List<DtoSampleFolderTemp> generateFolderTemps(Map<String, Map<String, List<DtoQuotationDetail>>> pointAndTypeGroups,
                                                          Map<String, DtoSampleType> sampleTypeMap) {
        List<DtoSampleFolderTemp> folderTempList = new ArrayList<>();
        pointAndTypeGroups.forEach((pointName, typeDetails) -> {
            typeDetails.forEach((sampleTypeId, quotationDetails) -> {
                DtoSampleFolderTemp folderTemp = createFolderTemp(pointName, sampleTypeId, quotationDetails, sampleTypeMap);
                folderTempList.add(folderTemp);
            });
        });
        return folderTempList.stream()
                .sorted(Comparator.comparing(DtoSampleFolderTemp::getWatchSpot))
                .collect(Collectors.toList());
    }

    /**
     * 创建文件夹模板
     */
    private DtoSampleFolderTemp createFolderTemp(String pointName, String sampleTypeId, List<DtoQuotationDetail> quotationDetails,
                                                 Map<String, DtoSampleType> sampleTypeMap) {

        DtoSampleFolderTemp folderTemp = new DtoSampleFolderTemp();
        folderTemp.setId(UUIDHelper.NewID());
        folderTemp.setSampleFolderId(UUIDHelper.NewID());
        folderTemp.setLabel(pointName);
        folderTemp.setWatchSpot(pointName);
        folderTemp.setDepth(1);

        // 设置样品类型信息
        DtoSampleType type = sampleTypeMap.getOrDefault(sampleTypeId, new DtoSampleType());
        folderTemp.setBigSampleTypeId(type.getParentId());
        folderTemp.setSampleTypeId(sampleTypeId);
        folderTemp.setSampleTypeName(type.getTypeName());
        // 计算最大周期、批次和样品数
        int maxCycle = quotationDetails.stream().mapToInt(DtoQuotationDetail::getCycleOrder).max().orElse(0);
        int maxTime = quotationDetails.stream().mapToInt(DtoQuotationDetail::getTimesOrder).max().orElse(0);
        int maxSample = quotationDetails.stream().mapToInt(DtoQuotationDetail::getSampleCount).max().orElse(0);
        // 设置周期和样品信息
        folderTemp.setPeriodCount(maxCycle);
        folderTemp.setTimePerPeriod(maxTime);
        folderTemp.setSamplePeriod(maxSample);
        folderTemp.setSampleNum(maxSample);
        // 生成子节点
        folderTemp.setChildren(generatePeriodInfo(quotationDetails, maxCycle, maxTime, maxSample));
        return folderTemp;
    }

    /**
     * 生成周期、批次、样次信息
     */
    private List<DtoSampleFolderTemp> generatePeriodInfo(List<DtoQuotationDetail> details, int maxCycle, int maxBatch, int maxSample) {
        List<DtoSampleFolderTemp> tempList = new ArrayList<>();
        for (int i = 1; i <= maxCycle; i++) {
            DtoSampleFolderTemp period = createPeriodTemp(i, details, maxBatch, maxSample);
            tempList.add(period);
        }
        return tempList;
    }

    /**
     * 创建周期模板
     */
    private DtoSampleFolderTemp createPeriodTemp(int periodNo, List<DtoQuotationDetail> details, int maxBatch, int maxSample) {
        DtoSampleFolderTemp period = new DtoSampleFolderTemp();
        period.setDepth(2);
        period.setPeriodCount(periodNo);
        period.setLabel(String.format("第%d周期", periodNo));
        List<DtoSampleFolderTemp> batches = new ArrayList<>();
        for (int j = 1; j <= maxBatch; j++) {
            DtoSampleFolderTemp batch = createBatchTemp(periodNo, j, details, maxSample);
            batches.add(batch);
        }
        period.setChildren(batches);
        return period;
    }

    /**
     * 创建批次模板
     */
    private DtoSampleFolderTemp createBatchTemp(int periodNo, int batchNo, List<DtoQuotationDetail> details, int maxSample) {
        DtoSampleFolderTemp batch = new DtoSampleFolderTemp();
        batch.setDepth(3);
        batch.setPeriodCount(periodNo);
        batch.setTimePerPeriod(batchNo);
        batch.setLabel(String.format("第%d批次", batchNo));
        List<DtoSampleFolderTemp> samples = new ArrayList<>();
        for (int k = 1; k <= maxSample; k++) {
            DtoSampleFolderTemp sample = createSampleTemp(periodNo, batchNo, k, details);
            if (sample != null) {
                samples.add(sample);
            }
        }
        batch.setChildren(samples);
        return batch;
    }

    /**
     * 创建样品模板
     */
    private DtoSampleFolderTemp createSampleTemp(int periodNo, int batchNo, int sampleNo, List<DtoQuotationDetail> details) {
        int finalPeriodNo = periodNo;
        int finalBatchNo = batchNo;
        int finalSampleNo = sampleNo;
        List<DtoQuotationDetail> quotationDataList = details.stream()
                .filter(d -> d.getCycleOrder() >= finalPeriodNo
                        && d.getTimesOrder() >= finalBatchNo
                        && d.getSampleCount() >= finalSampleNo)
                .collect(Collectors.toList());
        if (quotationDataList.isEmpty()) {
            return null;
        }
        DtoSampleFolderTemp sample = new DtoSampleFolderTemp();
        sample.setDepth(4);
        sample.setPeriodCount(periodNo);
        sample.setTimePerPeriod(batchNo);
        sample.setSamplePeriod(sampleNo);
        sample.setLabel(String.format("第%d个样", sampleNo));
        sample.setDetailList(quotationDataList);
        return sample;
    }

    @Autowired
    @Lazy
    public void setProjectService(ProjectService projectService) {
        this.projectService = projectService;
    }
}