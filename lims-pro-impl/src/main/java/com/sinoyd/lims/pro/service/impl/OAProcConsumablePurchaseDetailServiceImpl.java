package com.sinoyd.lims.pro.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.sinoyd.base.dto.lims.DtoConsumable;
import com.sinoyd.base.dto.lims.DtoConsumableDetail;
import com.sinoyd.base.service.ConsumableDetailService;
import com.sinoyd.base.service.ConsumableService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoOAConsumablePurchaseDetail;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.OAConsumablePurchaseDetailService;
import com.sinoyd.lims.pro.dto.DtoOATask;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;
import com.sinoyd.lims.pro.dto.DtoOATaskRelation;
import com.sinoyd.lims.pro.entity.OATask;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumOATaskType;
import com.sinoyd.lims.pro.service.OAProcConsumablePurchaseDetailService;
import com.sinoyd.lims.pro.service.OATaskRelationService;
import com.sinoyd.lims.pro.service.OATaskService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 消耗品采购业务操作接口实现
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-03
 * @since V100R001
 */
@Service
public class OAProcConsumablePurchaseDetailServiceImpl implements OAProcConsumablePurchaseDetailService {
    /**
     * 审批任务服务
     */
    @Autowired
    @Lazy
    private OATaskService oaTaskService;

    /**
     * 审批任务关联服务
     */
    @Autowired
    @Lazy
    private OATaskRelationService oaTaskRelationService;

    /**
     * 采购入库关联服务
     */
    @Autowired
    @Lazy
    private ConsumableDetailService consumableDetailService;


    @Autowired
    private ConsumableService consumableService;

    @Autowired
    private CodeService codeService;

    /**
     * 消耗品采购服务
     */
    @Autowired
    @Lazy
    private OAConsumablePurchaseDetailService consumablePurchaseDetailService;

    @Transactional
    @Override
    public String startProcess(DtoOATaskCreate<List<DtoOAConsumablePurchaseDetail>> taskDto) {
        Map<String, Object> vars = new HashMap<>();
        DtoOATask oaTask = oaTaskService.startProcess(EnumOATaskType.消耗品采购, taskDto, vars);

        List<DtoOAConsumablePurchaseDetail> data = taskDto.getData();
        //查询标准物资类型
        List<DtoCode> standardCategory = codeService.findCodes("LIM_StandardCategory");
        List<DtoOATaskRelation> taskRelations = new ArrayList<>();
        DtoOATaskRelation taskRelation = null;

        for (DtoOAConsumablePurchaseDetail purchase : data) {
            purchase.setId(UUIDHelper.NewID());
            // 设置剩余数量为计划数量
            purchase.setSurplusNum(purchase.getPlanNum());
            //处理物资类型
            Integer materialType = EnumLIM.EnumMaterialType.消耗品.getValue();
            if (standardCategory.stream().anyMatch(p->p.getDictName().equals(purchase.getCategoryId()))){
                materialType = EnumLIM.EnumMaterialType.标样.getValue();
            }
            purchase.setMaterialType(materialType);
            taskRelation = new DtoOATaskRelation();
            taskRelation.setTaskId(oaTask.getId());
            taskRelation.setObjectId(purchase.getId());

            taskRelations.add(taskRelation);
        }

        // 添加采购记录
        consumablePurchaseDetailService.save(data);

        // 添加审批任务关联信息
        oaTaskRelationService.save(taskRelations);

        return oaTask.getId();
    }

    @Override
    public DtoOATaskDetail<List<DtoOAConsumablePurchaseDetail>, String> findOATaskDetail(String taskId) {
        DtoOATask task = oaTaskService.findByTaskId(taskId);

        DtoOATaskDetail<List<DtoOAConsumablePurchaseDetail>, String> taskDetail = new DtoOATaskDetail<>();
        taskDetail.setTask(task);
        taskDetail.setAttach(oaTaskService.getOATaskAttachDTO(task.getProcInstId()));

        List<DtoOATaskRelation> relations = oaTaskRelationService.findListByTaskId(taskId);

        // 采购信息
        List<DtoOAConsumablePurchaseDetail> purchases = new ArrayList<>();

        for (DtoOATaskRelation relation : relations) {
            DtoOAConsumablePurchaseDetail purchase = consumablePurchaseDetailService.findOne(relation.getObjectId());
            if (StringUtil.isNotNull(purchase)){
                String consumableId = purchase.getConsumableId();
                if (StringUtil.isNotEmpty(consumableId)){
                    DtoConsumable consumable = consumableService.findOne(consumableId);
                    purchase.setIsStandard(StringUtil.isNotNull(consumable)?consumable.getIsStandard():false);
                }
            }
            purchases.add(purchase);
        }

        // 设置采购信息
        taskDetail.setDetail(purchases);

        return taskDetail;
    }

    /**
     * 入库
     *
     * @param detail 入库数据
     * @param purchaseDetailId 采购详细id
     * @return 入库后的采购详细
     */
    @Override
    @Transactional
    public DtoOAConsumablePurchaseDetail warehousing(DtoConsumableDetail detail,String purchaseDetailId) {
        //获取本次入库数量
        BigDecimal warehousingNum = detail.getInventory();
        detail.setStorage(warehousingNum);
        //获取设置剩余数量的采购详细
        DtoOAConsumablePurchaseDetail purchaseDetail = setInventory(purchaseDetailId, warehousingNum);
        //保存消耗品入库数据
        consumableDetailService.saveAndChangeConsumable(detail);
        //保存采购详细数据
        return consumablePurchaseDetailService.update(purchaseDetail);
    }

    /**
     * 标准样品入库
     *
     * @param consumable 标准样品数据
     * @param purchaseDetailId 采购详细id
     * @return 采购详细数据
     */
    @Override
    public DtoOAConsumablePurchaseDetail warehousingStandard(DtoConsumable consumable,String purchaseDetailId) {
        //获取本次入库数量
        BigDecimal inventory = consumable.getDetail().getInventory();
        consumable.getDetail().setStorage(inventory);
        //获取设置剩余数量后的采购详细
        DtoOAConsumablePurchaseDetail purchaseDetail = setInventory(purchaseDetailId, inventory);
        //保存新的标准样品数据
        if (StringUtil.isEmpty(purchaseDetail.getConsumableId())){
            List<DtoCode> category = codeService.findCodes("LIM_StandardCategory");
            List<DtoCode> standard = category.stream().filter(p->consumable.getCategoryId().equals(p.getDictName())).collect(Collectors.toList());
            String consumableId;
            if (StringUtil.isNotEmpty(standard)){
                DtoConsumable save = consumableService.createStandard(consumable);
                consumableId = save.getId();
            }else{
                DtoConsumable save = consumableService.save(consumable);
                consumableId = save.getId();
            }
            purchaseDetail.setConsumableId(consumableId);
        }else{
            consumableService.createStandard(consumable);
        }
        //保存采购详细数据
        return consumablePurchaseDetailService.update(purchaseDetail);
    }

    /**
     * 设置采购详细的剩余数量
     *
     * @param purchaseDetailId 采购详细id
     * @param inventory 本次入库数量
     * @return 入库后的剩余数量数据
     */
    private DtoOAConsumablePurchaseDetail setInventory(String purchaseDetailId,BigDecimal inventory){
        //获取采购详细信息
        DtoOAConsumablePurchaseDetail purchaseDetail = consumablePurchaseDetailService.findOne(purchaseDetailId);
        //处理采购剩余数量
        if (StringUtil.isNotNull(purchaseDetail)){
            //采购预计剩余数量
            Integer surplusNum = purchaseDetail.getSurplusNum();
            //入库数量
            Integer inventoryIn = inventory.intValue();
            //实际采购数量
            purchaseDetail.setPurchaseNum(purchaseDetail.getPurchaseNum() + inventoryIn);
            //如果入库数量大于采购数量则采购剩余数量为零
            if (inventoryIn>surplusNum){
                purchaseDetail.setSurplusNum(BigDecimal.ZERO.intValue());
            }else{
                purchaseDetail.setSurplusNum(surplusNum - inventoryIn);
            }
        }
        return purchaseDetail;
    }

    @Transactional
    @Override
    public DtoOATask saveAsDraft(DtoOATaskCreate<List<DtoOAConsumablePurchaseDetail>> taskDto) {
        Map<String, Object> vars = new HashMap<>();
        DtoOATask oaTask = oaTaskService.saveAsDraft(EnumOATaskType.消耗品采购, taskDto, vars);
        saveTaskRelation(taskDto,oaTask);
        return oaTask;
    }

    @Override
    @Transactional
    public OATask draftSave(DtoOATaskCreate<List<DtoOAConsumablePurchaseDetail>> taskDto) {
        //保存审批信息
        DtoOATask oaTask = oaTaskService.DraftSave(taskDto);
        //移除原关联关系
        List<DtoOATaskRelation> relationList = oaTaskRelationService.findListByTaskId(oaTask.getId());
        oaTaskRelationService.delete(relationList);
        //保存新关联关系
        saveTaskRelation(taskDto,oaTask);
        return oaTask;
    }

    private void saveTaskRelation(DtoOATaskCreate<List<DtoOAConsumablePurchaseDetail>> taskDto,DtoOATask oaTask){
        List<DtoOAConsumablePurchaseDetail> data = taskDto.getData();
        List<DtoOATaskRelation> taskRelations = new ArrayList<>();
        DtoOATaskRelation taskRelation = null;
        for (DtoOAConsumablePurchaseDetail purchase : data) {
            // 设置剩余数量为计划数量
            purchase.setSurplusNum(purchase.getPlanNum());
            taskRelation = new DtoOATaskRelation();
            taskRelation.setTaskId(oaTask.getId());
            taskRelation.setObjectId(purchase.getId());
            taskRelations.add(taskRelation);
        }
        // 添加采购记录
        consumablePurchaseDetailService.save(data);
        // 添加审批任务关联信息
        oaTaskRelationService.save(taskRelations);
    }

    @Override
    public String draftSubmit(DtoOATaskCreate<List<DtoOAConsumablePurchaseDetail>> taskDto) {
        return oaTaskService.draftSubmit(taskDto, new HashMap<>());
    }
}