package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoStatusForProject;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.StatusForProjectRepository;
import com.sinoyd.lims.pro.service.StatusForQCProjectService;
import com.sinoyd.lims.probase.service.impl.StatusForProjectBaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 质控项目状态操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/15
 * @since V100R001
 */
@Service
public class StatusForQCProjectServiceImpl extends StatusForProjectBaseServiceImpl implements StatusForQCProjectService {
    @Autowired
    private StatusForProjectRepository statusForProjectRepository;

    /**
     * 修改状态数据
     *
     * @param from    状态起
     * @param to      状态止
     * @param sign    工作流信号对象
     * @param project 项目实体
     */
    @Transactional
    @Override
    public void modifyStatus(String from, String to, DtoWorkflowSign sign, DtoProject project) {
        if (from.equals(to)) {
            return;
        }
        //读取该项目的所有状态数据并转为map
        List<DtoStatusForProject> statusList = statusForProjectRepository.findByProjectId(project.getId());
        Map<String, DtoStatusForProject> statusMap = statusList.stream().collect(Collectors.toMap(DtoStatusForProject::getModule, status -> status));

        EnumPRO.EnumProjectStatus fromStatus = EnumPRO.EnumProjectStatus.getByName(from);
        EnumPRO.EnumProjectStatus toStatus = EnumPRO.EnumProjectStatus.getByName(to);

        String fromModule = StringUtil.isNull(fromStatus) ? "" : this.getModule(fromStatus);
        String toModule = StringUtil.isNull(toStatus) ? "" : this.getModule(toStatus);

        //若状态起在状态止之后，则不需要修改状态起的状态表数据，直接删除即可，否则需将状态起的状态表数据改为已处理
        if (StringUtils.isNotNullAndEmpty(fromModule) && fromStatus.getValue() > toStatus.getValue()) {
            fromModule = "";
        }

        if (StringUtil.isNotEmpty(fromModule)) {
            //默认存在当前状态对应的状态数据
            DtoStatusForProject status = statusMap.getOrDefault(fromModule, null);
            if (StringUtil.isNotNull(status)) {
                status.setLastNewOpinion("");//最新意见置空
                if (StringUtil.isEmpty(toModule)) {
                    status.setLastNewOpinion(sign.getOption());
                }
                status.setNextPersonId(sign.getNextOperatorId());
                if (!UUIDHelper.GUID_EMPTY.equals(sign.getNextOperatorId())) {
                    status.setNextPersonName(sign.getNextOperator());
                }
                status.setStatus(EnumPRO.EnumStatus.已处理.getValue());
                comRepository.merge(status);
            }
        } else {//若来源模块为空，说明为退回，需移除状态
            this.removeStatus(toModule, statusMap);
        }

        if (StringUtil.isNotEmpty(toModule)) {//若存在状态止的待处理模块，需修改对应状态数据
            if (statusMap.containsKey(toModule)) {//若数据库中存在该条数据
                DtoStatusForProject status = statusMap.get(toModule);
                status.setStatus(EnumPRO.EnumStatus.待处理.getValue());
                status.setNextPersonId(UUIDHelper.GUID_EMPTY);
                status.setNextPersonName("");
                status.setLastNewOpinion(sign.getOption());
                this.update(status);
            } else if (StringUtil.isNotEmpty(toModule) && !toModule.equals("--")) {
                DtoStatusForProject status = new DtoStatusForProject();
                status.setProjectId(project.getId());
                status.setModule(toModule);
                status.setStatus(EnumPRO.EnumStatus.待处理.getValue());
                if (!UUIDHelper.GUID_EMPTY.equals(sign.getNextOperatorId())) {
                    status.setCurrentPersonId(sign.getNextOperatorId());
                    status.setCurrentPersonName(sign.getNextOperator());
                }
                status.setLastNewOpinion(sign.getOption());
                repository.save(status);
            }
        }
    }

    /**
     * 根据到达的模块状态删除该状态之后的数据
     *
     * @param toModule  到达的状态模块
     * @param statusMap 状态数据map
     */
    private void removeStatus(String toModule, Map<String, DtoStatusForProject> statusMap) {
        Map<String, Integer> enumMap = new HashMap<>();
        for (EnumLIM.EnumQCProjectModule m : EnumLIM.EnumQCProjectModule.values()) {
            enumMap.put(m.getCode(), m.getValue());
        }
        Integer toModuleVal = enumMap.getOrDefault(toModule, -1);
        for (String key : enumMap.keySet()) {
            if (statusMap.containsKey(key) && enumMap.get(key) > toModuleVal) {
                repository.delete(statusMap.get(key).getId());
            }
        }
    }
    /**
     * 根据项目状态枚举获取对应所处模块（仅获取开展中之前的）
     *
     * @param status  项目状态枚举值
     */
    private String getModule(EnumPRO.EnumProjectStatus status) {
        switch (status) {
            case 项目登记中:
                return EnumLIM.EnumQCProjectModule.项目登记.getCode();

            case 开展中:
                return "--";

            case 数据汇总中:
                return EnumLIM.EnumQCProjectModule.数据汇总.getCode();

            case 结果评价中:
                return EnumLIM.EnumQCProjectModule.评价结果.getCode();

            default:
                return "";
        }
    }
}
