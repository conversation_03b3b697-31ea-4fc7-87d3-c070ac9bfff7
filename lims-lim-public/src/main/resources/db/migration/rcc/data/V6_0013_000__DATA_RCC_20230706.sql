-- 质控任务统计（人员比对）应用配置
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('2edf8bd6-81aa-4dd5-acea-f3c8222112b7', 'c760a3de-f4dc-4e35-9c4d-175f2e7884c4', 'QCTaskState', '质控任务进度查询',
        'QcProjectPersonCompare', '质控任务统计（人员比对）', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-07-03 09:48:37', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-07-03 09:48:37');

-- 质控任务统计（仪器比对）应用配置
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('47235293-2ee3-4290-8669-158581db0487', '11220910-2650-48bf-9c28-bde79efe4266', 'QCTaskState', '质控任务进度查询',
        'QcProjectInstrumentCompare', '质控任务统计（仪器比对）', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-07-03 09:52:48', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-07-03 09:52:48');

-- 质控任务统计（加标样考核）应用配置
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('54b08b5d-a249-42bf-9f8e-ea9582764f27', 'c6b960a3-b0db-4beb-ba55-75ee67e95b7c', 'QCTaskState', '质控任务进度查询',
        'QcProjectMarkExam', '质控任务统计（加标样考核）', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-07-03 09:25:21', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-07-03 09:25:21');

-- 质控任务统计（标样考核）应用配置
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('a129c1fd-c2e4-40d8-b0d0-e5ca191c6132', 'eb924106-a28f-422b-ba10-466704b88545', 'QCTaskState', '质控任务进度查询',
        'QcProjectStandardExam', '质控任务统计（标样考核）', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-07-03 08:43:04', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-07-03 08:43:04');

-- 年度质控考核统计表应用配置
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('b0a102c0-e11c-47c1-9f77-4da744d99a82', 'ee666c5a-4a50-4a20-b351-2972f40d69e5', 'QCTaskState', '质控任务进度查询',
        'QcProjectAnnualStatistical', '年度质控考核统计表', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-07-03 09:58:51', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-07-03 09:58:51');

-- 质控任务统计（手工、仪器比对）应用配置
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('db17b6b7-8202-483c-a315-49d4c04f3f86', 'f378aa4b-0d48-4f94-8670-4b22ce9f0c05', 'QCTaskState', '质控任务进度查询',
        'QcProjectHandPersonCompare', '质控任务统计（手工、仪器比对）', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-07-03 09:56:26', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-07-03 09:56:26');

-- 质控任务统计（盲样考核）应用配置
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('fbefd2df-b8b8-40ac-8931-896579c46e58', '0a80a85f-c9b1-40b7-966c-58c8721150fc', 'QCTaskState', '质控任务进度查询',
        'QcProjectBlindExam', '质控任务统计（盲样考核）.xlsx', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-07-03 09:45:17', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-07-03 09:45:17');