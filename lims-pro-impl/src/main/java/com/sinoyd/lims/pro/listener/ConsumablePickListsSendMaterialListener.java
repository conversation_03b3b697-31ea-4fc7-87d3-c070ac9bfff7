package com.sinoyd.lims.pro.listener;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.dto.lims.DtoConsumableDetail;
import com.sinoyd.base.dto.lims.DtoConsumableLog;
import com.sinoyd.base.entity.ConsumableDetail;
import com.sinoyd.base.service.ConsumableDetailService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.lims.lim.dto.lims.DtoOAConsumablePickListsDetail;
import com.sinoyd.lims.lim.service.OAConsumablePickListsDetailService;
import com.sinoyd.lims.pro.dto.DtoOATask;
import com.sinoyd.lims.pro.dto.DtoOATaskRelation;
import com.sinoyd.lims.pro.service.OATaskRelationService;
import com.sinoyd.lims.pro.service.OATaskService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 消耗品的领料发料
 *
 * <AUTHOR>
 * @version V1.0.0 2020-04-02
 * @since V100R001
 */
@Component
@Slf4j
public class ConsumablePickListsSendMaterialListener  implements ExecutionListener {

    @Override
    public void notify(DelegateExecution delegateExecution) throws Exception {
        //业务数据id
        String businessKey = delegateExecution.getProcessBusinessKey();
        OATaskService oaTaskService = SpringContextAware.getBean(OATaskService.class);
        DtoOATask oaTask = oaTaskService.findOne(businessKey);
        OATaskRelationService taskRelationService = SpringContextAware.getBean(OATaskRelationService.class);
        //获取相关任务的关系数据
        List<DtoOATaskRelation> relations = taskRelationService.findListByTaskId(businessKey);
        List<String> objectIds = relations.stream().map(DtoOATaskRelation::getObjectId).distinct().collect(Collectors.toList());
        OAConsumablePickListsDetailService oaConsumablePickListsDetailService = SpringContextAware.getBean(OAConsumablePickListsDetailService.class);
        ConsumableDetailService consumableDetailService = SpringContextAware.getBean(ConsumableDetailService.class);
        CommonRepository commonRepository = SpringContextAware.getBean(CommonRepository.class);
        if (objectIds.size() > 0) {
            //获取领料的明细数据
            List<DtoOAConsumablePickListsDetail> pickListsDetails = oaConsumablePickListsDetailService.findAll(objectIds);
            //得到消耗品的ids
            List<String> consumableIds = pickListsDetails.stream().map(DtoOAConsumablePickListsDetail::getConsumableId).collect(Collectors.toList());
            //通过消耗品id 找到消耗品的明细数据，暂时按有效时间升序排吧，并且扣除是有效的库存
            List<DtoConsumableDetail> details = consumableDetailService.findByParentIds(consumableIds);
            for (DtoOAConsumablePickListsDetail pickListsDetail : pickListsDetails) {
                List<DtoConsumableDetail> detailList = details.stream().filter(p -> p.getParentId().equals(pickListsDetail.getConsumableId())).collect(Collectors.toList());
                // 计算总库存
                BigDecimal inventory = detailList.stream().map(ConsumableDetail::getStorage).reduce(BigDecimal.ZERO, BigDecimal::add);
                // 领取的数量
                BigDecimal materialNum = pickListsDetail.getMaterialNum();
                // 说明库存充裕，否则提示库存不足
                if (inventory.compareTo(pickListsDetail.getMaterialNum()) >= 0) {
                    BigDecimal balance = inventory.subtract(materialNum);//结存
                    for (DtoConsumableDetail detail : detailList) {
                        //如果是0的库存不用处理
                        if (detail.getStorage().compareTo(BigDecimal.ZERO) == 0) {

                        } else if (materialNum.compareTo(detail.getStorage()) > 0) {
                            materialNum = materialNum.subtract(detail.getStorage());
                            detail.setStorage(BigDecimal.ZERO);
                            commonRepository.merge(detail);
                            DtoConsumableLog dtoConsumableLog = new DtoConsumableLog();
                            dtoConsumableLog.setAmount(detail.getStorage());
                            dtoConsumableLog.setBalance(balance);
                            dtoConsumableLog.setConsumableDetailId(detail.getId());
                            dtoConsumableLog.setConsumableId(detail.getParentId());
                            dtoConsumableLog.setConsumablePickId(businessKey);
                            //暂时以发料的时间为准
                            dtoConsumableLog.setOccurrenceTime(new Date());
                            dtoConsumableLog.setUserId(oaTask.getSponsorId());
                            dtoConsumableLog.setRemark(pickListsDetail.getRemark());
                            commonRepository.insert(dtoConsumableLog);
                        } else {
                            detail.setStorage(detail.getStorage().subtract(materialNum));
                            commonRepository.merge(detail);
                            DtoConsumableLog dtoConsumableLog = new DtoConsumableLog();
                            dtoConsumableLog.setAmount(materialNum);
                            dtoConsumableLog.setBalance(balance);
                            dtoConsumableLog.setConsumableDetailId(detail.getId());
                            dtoConsumableLog.setConsumableId(detail.getParentId());
                            dtoConsumableLog.setConsumablePickId(businessKey);
                            //暂时以发料的时间为准
                            dtoConsumableLog.setOccurrenceTime(new Date());
                            dtoConsumableLog.setUserId(oaTask.getSponsorId());
                            dtoConsumableLog.setRemark(pickListsDetail.getRemark());
                            commonRepository.insert(dtoConsumableLog);
                            break;
                        }
                    }
                }
            }
        }
    }
}
