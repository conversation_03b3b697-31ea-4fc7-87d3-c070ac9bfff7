package com.sinoyd.lims.pro.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;


/**
 * FileAuditStatus实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/05/19
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@MappedSuperclass
@ApiModel(description = "FileAuditStatus")
@Data
public class FileAuditStatus extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 文件审核标识
     */
    @Column(length = 50,nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    private String fileAuditId;

    /**
     * 模块编码（枚举EnumFileAuditStep）
     */
    @Column(length = 50,nullable = false)
    private String stepCode;

    /**
     * 是否处理
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    private Boolean isHandle;

    /**
     * 是否通过
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    private Boolean isPass;

    /**
     * 当前操作人Id
     */
    @Column(length = 50,nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    private String currentPersonId;

    /**
     * 最新一条意见
     */
    @Column(length = 2000)
    private String lastNewOpinion;

    /**
     * 最新一次操作时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    private Date lastOperateDate;
}