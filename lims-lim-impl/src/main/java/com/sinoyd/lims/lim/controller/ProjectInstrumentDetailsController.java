package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.ProjectInstrumentDetailsCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoProjectInstrumentDetails;
import com.sinoyd.lims.lim.dto.customer.DtoInstrumentCheckOut;
import com.sinoyd.lims.lim.service.ProjectInstrumentDetailsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * ProjectInstrumentDetails服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
@Api(tags = "示例: ProjectInstrumentDetails服务")
@RestController
@RequestMapping("api/lim/projectInstrumentDetails")
@Validated
public class ProjectInstrumentDetailsController extends BaseJpaController<DtoProjectInstrumentDetails, String, ProjectInstrumentDetailsService> {


    /**
     * 分页动态条件查询ProjectInstrumentDetails
     *
     * @param projectInstrumentDetailsCriteria 条件参数
     * @return RestResponse<List < ProjectInstrumentDetails>>
     */
    @ApiOperation(value = "分页动态条件查询ProjectInstrumentDetails", notes = "分页动态条件查询ProjectInstrumentDetails")
    @GetMapping
    public RestResponse<List<DtoProjectInstrumentDetails>> findByPage(ProjectInstrumentDetailsCriteria projectInstrumentDetailsCriteria) {
        PageBean<DtoProjectInstrumentDetails> pageBean = super.getPageBean();
        RestResponse<List<DtoProjectInstrumentDetails>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, projectInstrumentDetailsCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 分页动态条件查询ProjectInstrumentDetails
     *
     * @param projectInstrumentDetailsCriteria 条件参数
     * @return RestResponse<List < ProjectInstrumentDetails>>
     */
    @ApiOperation(value = "分页动态条件查询ProjectInstrumentDetails", notes = "分页动态条件查询ProjectInstrumentDetails")
    @GetMapping("/details")
    public RestResponse<List<DtoInstrumentCheckOut>> findDetailsByPage(ProjectInstrumentDetailsCriteria projectInstrumentDetailsCriteria) {
        PageBean<DtoInstrumentCheckOut> pageBean = super.getPageBean();
        RestResponse<List<DtoInstrumentCheckOut>> restResponse = new RestResponse<>();
        service.findInstrumentStorage(pageBean, projectInstrumentDetailsCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询ProjectInstrumentDetails
     *
     * @param id 主键id
     * @return RestResponse<DtoProjectInstrumentDetails>
     */
    @ApiOperation(value = "按主键查询ProjectInstrumentDetails", notes = "按主键查询ProjectInstrumentDetails")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoProjectInstrumentDetails> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoProjectInstrumentDetails> restResponse = new RestResponse<>();
        DtoProjectInstrumentDetails projectInstrumentDetails = service.findOne(id);
        restResponse.setData(projectInstrumentDetails);
        restResponse.setRestStatus(StringUtil.isNull(projectInstrumentDetails) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 批量新增仪器出入库明细
     *
     * @param projectInstrumentDetails 出入库明细实体
     * @return RestResponse<DtoProjectInstrument>
     */
    @ApiOperation(value = "批量新增仪器出入库明细", notes = "批量新增仪器出入库明细")
    @PostMapping
    public RestResponse<Integer> create(@Validated @RequestBody DtoProjectInstrumentDetails projectInstrumentDetails) {
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(service.addDetailsBatch(projectInstrumentDetails));
        return restResponse;
    }

    /**
     * 修改ProjectInstrumentDetails
     *
     * @param projectInstrumentDetails 实体列表
     * @return RestResponse<DtoProjectInstrumentDetails>
     */
    @ApiOperation(value = "修改ProjectInstrumentDetails", notes = "修改ProjectInstrumentDetails")
    @PutMapping
    public RestResponse<DtoProjectInstrumentDetails> update(@Validated @RequestBody DtoProjectInstrumentDetails projectInstrumentDetails) {
        RestResponse<DtoProjectInstrumentDetails> restResponse = new RestResponse<>();
        restResponse.setData(service.updateDetail(projectInstrumentDetails));
        return restResponse;
    }

    /**
     * "根据id批量删除ProjectInstrumentDetails
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除ProjectInstrumentDetails", notes = "根据id批量删除ProjectInstrumentDetails")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.deleteDetailsBatch(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 批量操作确认数据/取消确认数据
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "批量操作确认数据/取消确认数据", notes = "批量操作确认数据/取消确认数据")
    @PutMapping("/batchConfirm")
    public RestResponse<String> batchConfirm(@RequestBody List<String> ids){
        RestResponse<String> restResponse = new RestResponse<>();
        service.batchOperationConfirm(true,ids);
        return restResponse;
    }

    /**
     * 批量操作确认数据/取消确认数据
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "批量操作确认数据/取消确认数据", notes = "批量操作确认数据/取消确认数据")
    @PutMapping("/batchCancel")
    public RestResponse<String> batchCancelConfirm(@RequestBody List<String> ids){
        RestResponse<String> restResponse = new RestResponse<>();
        service.batchOperationConfirm(false,ids);
        return restResponse;
    }

}