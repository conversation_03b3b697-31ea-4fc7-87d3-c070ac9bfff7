package com.sinoyd.base.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;

/**
 * TestQCRange实体
 * <AUTHOR>
 * @version V1.0.0 2022/6/14
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description="QualityControlLimit")
@Data
@EntityListeners(AuditingEntityListener.class)
public class QualityControlLimit implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public  QualityControlLimit() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 测试标识
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("测试项目标识")
    private String testId;

    /**
     * 数值范围
     */
    @Column(length=50)
    @ApiModelProperty("数值范围")
    @Length(message = "数值范围{validation.message.length}", max = 50)
    private String rangeConfig;

    /**
     * 质控等级（枚举EnumQCGrade：1.外部质控  2.内部质控）
     */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("评判方式（枚举EnumJudgingMethod：1.限值判定，2.小于检出限，3.回收率，4.相对偏差，5.相对误差，7.穿透率，6.绝对偏差）")
    private Integer judgingMethod;

    /**
     * 绝对偏差
     */
    @Column(length=50)
    @ApiModelProperty("允许限值")
    @Length(message = "允许限值{validation.message.length}", max = 50)
    private String allowLimit;

    /**
     * 不确定度类型
     */
    @ApiModelProperty("不确定度类型")
    private Integer uncertainType;

    /**
     * 质控等级（枚举EnumQCGrade：1.外部质控  2.内部质控）
     */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("质控等级（枚举EnumQCGrade：1.外部质控  2.内部质控）")
    private Integer qcGrade;

    /**
     * 质控类型（枚举EnumQCType：1.平行 2.空白 4.加标 8.标样）
     */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("质控类型（枚举EnumQCType：1.平行 2.空白 4.加标 8.标样）")
    private Integer qcType;

    /**
     * 质控类型名称
     */
    @Column(length = 50)
    @ApiModelProperty("质控类型名称")
    private String qcTypeName;

    /**
     * 代替物id
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("代替物id")
    private String substituteId;

    /**
     * 代替物名称
     */
    @Column(length = 50)
    @ApiModelProperty("代替物名称")
    private String substituteName;

    /**
     * 公式id
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("公式id")
    private String dispositionId;

    /**
     * 代替物名称
     */
    @Column(length = 50)
    @ApiModelProperty("穿透率公式")
    private String formula;

    /**
     * 组织机构id
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

    /**
     * 检查项（枚举EnumCheckItemType:1.出证结果，2.公式参数）
     */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("检查项（枚举EnumCheckItemType:1.出证结果，2.公式参数）")
    private Integer checkItem;

    /**
     * 检查项内容
     */
    @Column(length = 50)
    @ApiModelProperty("检查项内容")
    @Length(message = "检查项内容{validation.message.length}", max = 50)
    private String checkItemOther;

    /**
     * 是否需要检查项（1.是，2.否）
     */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("是否需要检查项（1.是，2.否）")
    private Integer isCheckItem;

    /**
     * 稀释水是否接种（1.是，2.否）
     */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("稀释水是否接种（1.是，2.否）")
    private Integer isVaccinate;

    /**
     * 技术说明
     */
    @Column(length = 255)
    @ApiModelProperty("技术说明")
    @Length(message = "技术说明{validation.message.length}", max = 50)
    private String description;


    /**
     * 验证状态 0未验证 1已验证
     */
    @ColumnDefault("0")
    @ApiModelProperty("验证状态 0未验证 1已验证")
    private Integer validate;

    /**
     * 使用次数
     */
    @ApiModelProperty("使用次数")
    private Integer usageNum;
}
