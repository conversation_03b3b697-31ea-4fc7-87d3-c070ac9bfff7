package com.sinoyd.lims.pro.service.redis;

import com.jsoniter.JsonIterator;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.dto.DtoUser;
import com.sinoyd.frame.service.UserService;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.customer.DtoPerson2TestChange;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.AnalyseDataRepository;
import com.sinoyd.lims.pro.repository.SampleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  测试人员第一复制人变动之后需要改变未填写数据的人员
 */
@Component
@Service
public class AnalysePersonChangeChannel {

    @Autowired
    private AnalyseDataRepository analyseDataRepository;

    @Autowired
    private SampleRepository sampleRepository;

    @Autowired
    private PersonRepository personRepository;

    @Autowired
    private UserService userService;

    @Async
    public void updateAnalystId(String msg) {
        if (StringUtils.isNotNullAndEmpty(msg)) {
            TypeLiteral<List<DtoPerson2TestChange>> typeLiteral = new TypeLiteral<List<DtoPerson2TestChange>>() {
            };
            List<DtoPerson2TestChange> person2TestChanges = JsonIterator.deserialize(JsonIterator.deserialize(msg).toString(), typeLiteral);

            if (StringUtil.isNotNull(person2TestChanges) && person2TestChanges.size() > 0) {
                List<String> testIds = person2TestChanges.stream().map(DtoPerson2TestChange::getTestId).distinct().collect(Collectors.toList());
                String orgId = person2TestChanges.get(0).getOrgId();
                String creator = person2TestChanges.get(0).getCreator();
                String personId = person2TestChanges.get(0).getPersonId();
                DtoPerson person = personRepository.findOne(personId);
                String personName = "";
                String domainId = UUIDHelper.GUID_EMPTY;
                if (StringUtil.isNotNull(person)) {
                    personName = person.getCName();
                    domainId = person.getDomainId();
                } else {
                    DtoUser dtoUser = userService.findByUserId(personId);
                    if (StringUtil.isNotNull(dtoUser)) {
                        personName = dtoUser.getUserName();
                        domainId = dtoUser.getDeptId();
                    }
                }
                //所有的分析数据
                List<DtoAnalyseData> analyseDataList = analyseDataRepository.findByTestIdInAndOrgIdAAndWorkSheetFolderId(testIds, orgId, UUIDHelper.GUID_EMPTY);
                //排除现场数据以及分包数据
                analyseDataList = analyseDataList.stream().filter(p -> !p.getIsCompleteField() && !p.getIsOutsourcing()
                        && !p.getIsSamplingOut()).collect(Collectors.toList());
                //样品ids
                List<String> sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());

                //所有的样品数据
                List<DtoSample> samples = sampleRepository.findAll(sampleIds);

                //过滤出样品未领取
                samples = samples.stream().filter(p -> p.getInnerReceiveStatus().equals(EnumPRO.EnumInnerReceiveStatus.不能领取.getValue())
                        || p.getInnerReceiveStatus().equals(EnumPRO.EnumInnerReceiveStatus.可以领取.getValue())).collect(Collectors.toList());

                //分析数据ids
                List<String> anaIds = new ArrayList<>();

                for (DtoAnalyseData analyseData : analyseDataList) {
                    DtoSample sample = samples.stream().filter(p -> p.getId().equals(analyseData.getSampleId())).findFirst().orElse(null);
                    //样品类型
                    List<String> sampleTypes = person2TestChanges.stream().filter(p -> p.getTestId().equals(analyseData.getTestId())).map(DtoPerson2TestChange::getSampleTypeIds).findFirst().orElse(null);
                    if (StringUtil.isNotNull(sample) && StringUtil.isNotNull(sampleTypes)) {
                        String sampleType = sample.getSampleTypeId();
                        if (sampleTypes.contains(sampleType)) {
                            //说明这个数据需要进行更改
                            anaIds.add(analyseData.getId());
                        }
                    }
                }
                if (anaIds.size() > 0) {
                    analyseDataRepository.updateAnalyst(anaIds, personId, personName, domainId, EnumPRO.EnumAnalyseDataStatus.未测.toString(), EnumPRO.EnumAnalyseDataStatus.未测.getValue(), creator, new Date());
                }
            }
        }
    }
}
