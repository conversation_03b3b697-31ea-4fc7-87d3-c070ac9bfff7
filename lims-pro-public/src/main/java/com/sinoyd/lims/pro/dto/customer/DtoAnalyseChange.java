package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.ArrayList;
import java.util.List;

/**
 * 分析数据更换分析人的dto
 * <AUTHOR>
 * @version V1.0.0 2020/02/12
 * @since V100R001
 */
@Data
public class DtoAnalyseChange {
    /**
     * 分析人id
     */
    private String analystId;

    /**
     * 分析人名称
     */
    @Length(message = "分析人员{validation.message.length}", max = 50)
    private String analystName;

    /**
     * 分析数据id集合
     */
    private List<String> ids = new ArrayList<>();
}
