package com.sinoyd.lims.lim.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.service.VersionInfoService;
import com.sinoyd.lims.lim.criteria.VersionInfoCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoVersionInfo;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * VersionInfo服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/9
 * @since V100R001
 */
@Api(tags = "示例: VersionInfo服务")
@RestController
@RequestMapping("api/lim/versionInfo")
@Validated
public class VersionInfoController extends BaseJpaController<DtoVersionInfo, String, VersionInfoService> {


    /**
     * 分页动态条件查询VersionInfo
     *
     * @param versionInfoCriteria 条件参数
     * @return RestResponse<List < VersionInfo>>
     */
    @ApiOperation(value = "分页动态条件查询VersionInfo", notes = "分页动态条件查询VersionInfo")
    @GetMapping
    public RestResponse<List<DtoVersionInfo>> findByPage(VersionInfoCriteria versionInfoCriteria) {
        PageBean<DtoVersionInfo> pageBean = super.getPageBean();
        RestResponse<List<DtoVersionInfo>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, versionInfoCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询VersionInfo
     *
     * @param id 主键id
     * @return RestResponse<DtoVersionInfo>
     */
    @ApiOperation(value = "按主键查询VersionInfo", notes = "按主键查询VersionInfo")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoVersionInfo> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoVersionInfo> restResponse = new RestResponse<>();
        DtoVersionInfo versionInfo = service.findOne(id);
        restResponse.setData(versionInfo);
        restResponse.setRestStatus(StringUtil.isNull(versionInfo) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增VersionInfo
     *
     * @param versionInfo 实体列表
     * @return RestResponse<DtoVersionInfo>
     */
    @ApiOperation(value = "新增VersionInfo", notes = "新增VersionInfo")
    @PostMapping
    public RestResponse<DtoVersionInfo> create(@Validated @RequestBody DtoVersionInfo versionInfo) {
        RestResponse<DtoVersionInfo> restResponse = new RestResponse<>();
        restResponse.setData(service.save(versionInfo));
        return restResponse;
    }

    /**
     * 新增VersionInfo
     *
     * @param versionInfo 实体列表
     * @return RestResponse<DtoVersionInfo>
     */
    @ApiOperation(value = "修改VersionInfo", notes = "修改VersionInfo")
    @PutMapping
    public RestResponse<DtoVersionInfo> update(@Validated @RequestBody DtoVersionInfo versionInfo) {
        RestResponse<DtoVersionInfo> restResponse = new RestResponse<>();
        restResponse.setData(service.update(versionInfo));
        return restResponse;
    }

    @ApiOperation(value = "修改VersionInfo", notes = "修改VersionInfo")
    @PostMapping("/createQRCode")
    public RestResponse<DtoVersionInfo> createQrCode(@Validated @RequestBody DtoVersionInfo versionInfo) {
        RestResponse<DtoVersionInfo> restResponse = new RestResponse<>();
        restResponse.setData(service.createQrCode(versionInfo));
        return restResponse;
    }

    /**
     * "根据id批量删除VersionInfo
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除VersionInfo", notes = "根据id批量删除VersionInfo")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
}