package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.DtoQCData;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;


/**
 * QCData数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface QCDataRepository extends IBaseJpaPhysicalDeleteRepository<DtoQCData, String> {

    /**
     * 根据测试项目、人员、类型获取相应的配置数据
     *
     * @param testId     测试项目id
     * @param userId     人员id
     * @param type       类型
     * @param paramsName 参数名称
     * @return 返回质控数据
     */
    DtoQCData findByTestIdAndUserIdAndDataTypeAndParamsName(String testId, String userId, Integer type, String paramsName);
}