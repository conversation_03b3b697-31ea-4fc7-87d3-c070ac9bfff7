package com.sinoyd.lims.monitor.strategy.context;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.common.http.NameValuePair;
import com.sinoyd.lims.monitor.enums.EnumMonitor;
import com.sinoyd.lims.monitor.strategy.pollutantDischargePermit.AbstractPDPRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 排污许可证数据请求上下文
 *
 * <AUTHOR>
 * @version V5.2.0 2025/04/23
 * @since V100R001
 */
@Component
public class PDPRequestContext {

    private Map<String, AbstractPDPRequest> requestMap;

    /**
     * 获取请求后的html数据
     *
     * @param httpMethod 请求方式
     * @param paramList  请求参数
     * @param type       排污许可证数据请求类型
     * @return html数据
     */
    public String getHtml(String httpMethod, List<NameValuePair> paramList, String type) {
        return findStrategy(type).getHtml(httpMethod, paramList);
    }


    /**
     * 根据请求类型获取请求策略
     *
     * @param type 请求类型
     * @return 策略类
     */
    private AbstractPDPRequest findStrategy(String type) {
        EnumMonitor.EnumPollutantDisChargePermitType requestType = EnumMonitor.EnumPollutantDisChargePermitType.getEnumByValue(type);
        if (requestType == null) {
            throw new BaseException(String.format("[%s]数据请求类型不存在！", type));
        }

        AbstractPDPRequest request = requestMap.get(requestType.getBeanName());
        if (request == null) {
            throw new BaseException(String.format("[%s]数据请求类型未实现！", type));
        }
        return request;
    }

    @Autowired
    public void setRequestMap(Map<String, AbstractPDPRequest> requestMap) {
        this.requestMap = requestMap;
    }
}
