-- 质控说明新增曲线校核样组件
DELETE
FROM TB_LIM_ReportModule
where id = '5d6ef0f4-f6e1-4c65-b20d-d805ece314ef';

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode, speedCalculationMode,
                                compoundAvgCalculationMode, gasParamSplitMode)
VALUES ('5d6ef0f4-f6e1-4c65-b20d-d805ece314ef', 'jhCurveStdData', '标准版曲线校核样检测结果表组件', 'jhCurveStdData',
        'dtJhCurveSource', 10, 0, '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2025-06-17 16:45:17', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2025-06-17 16:45:17', '0', '0', 0, 0, 0, 1);

UPDATE TB_LIM_ReportModule
SET sonTableJson = '["standardStdData", "innerBlankStdData", "OuterBlankStdData", "TransportBlankStdData", "SiteBlankStdData", "EquipBlankStdData", "innerParallelStdData", "markStdData", "replaceStdData", "jhCurveStdData"]'
WHERE moduleCode = 'qcStdDataSource';