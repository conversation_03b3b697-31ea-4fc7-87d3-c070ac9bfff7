package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * MpnConfigDetails查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MpnConfigDetailsCriteria extends BaseCriteria implements Serializable {

    /**
     * 配置id
     */
    private String mpnConfigId;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(mpnConfigId)) {
            condition.append(" and m.mpnConfigId = :mpnConfigId");
            values.put("mpnConfigId", mpnConfigId);
        }
        return condition.toString();
    }

}
