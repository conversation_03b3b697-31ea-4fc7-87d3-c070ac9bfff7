package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 项目方案
 * <AUTHOR>
 * @version V1.0.0 2019/11/23
 * @since V100R001
 */
@Data
public class DtoProjectScheme {
    /**
     * 频次id集合（传输用）
     */
    private List<String> samplingFrequencyIds = new ArrayList<>();
    /**
     * 点位信息
     */
    private List<DtoSampleFolderTemp> sampleFolder = new ArrayList<>();

    /**
     * 指标信息
     */
    private List<DtoProjectTest> test = new ArrayList<>();
}
