package com.sinoyd.lims.lim.service;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 原始记录单与测试项目关系模板下载接口
 * <AUTHOR>
 * @version V1.0.0 2023/10/9
 * @since V100R001
 */
public interface DownLoadRecoedConfig2TestTemplateService {
    /**
     * 下载模板
     *
     * @param response   响应流
     * @param sheetNames 需要赋值的sheet名
     * @param fileName   文件名
     */
    void downLoadTestTemplate(HttpServletResponse response, Map<String,String> sheetNames, String fileName);
}
