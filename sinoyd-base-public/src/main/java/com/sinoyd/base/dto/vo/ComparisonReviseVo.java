package com.sinoyd.base.dto.vo;

import javax.xml.bind.annotation.XmlElement;

/**
 * <AUTHOR>
 */
public class ComparisonReviseVo {
    /**
     * 比对类型
     */
    private String code;

    /**
     * 比对修约类型
     */
    private Integer type;

    /**
     * 质控修约有效位
     */
    private Integer sign;

    /**
     * 质控修约小数位
     */
    private Integer scale;

    public void setCode(String code) {
        this.code = code;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public void setSign(Integer sign) {
        this.sign = sign;
    }

    public void setScale(Integer scale) {
        this.scale = scale;
    }

    @XmlElement(name = "code")
    public String getCode() {
        return code;
    }

    @XmlElement(name = "type")
    public Integer getType() {
        return type;
    }

    @XmlElement(name = "sign")
    public Integer getSign() {
        return sign;
    }

    @XmlElement(name = "scale")
    public Integer getScale() {
        return scale;
    }
}
