package com.sinoyd.lims.pro.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SampleJudgeDataVoForWater implements Serializable {

    @Excel(name = "点位名称",needMerge = true,orderNum = "20",width = 11)
    private String folderName;

    @ExcelCollection(name = "", orderNum = "25")
    private List<AnalyzeitemJudgeDataVoForWater> dataVos;

}
