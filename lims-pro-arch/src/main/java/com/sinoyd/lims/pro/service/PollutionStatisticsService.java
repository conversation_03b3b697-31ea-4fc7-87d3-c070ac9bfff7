package com.sinoyd.lims.pro.service;


import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoProject;

import java.util.List;

/**
 * 污染源统计操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/04/27
 * @since V100R001
 */
public interface PollutionStatisticsService extends IBaseJpaService<DtoProject, String> {


    /**
     * 通过environmentStatisticsCriteria条件查询项目集合
     * @param pb
     * @param environmentStatisticsCriteria
     * @return DtoProject集合
     */
    List<DtoProject> findDtoProject(PageBean<DtoProject> pb, BaseCriteria environmentStatisticsCriteria);
}