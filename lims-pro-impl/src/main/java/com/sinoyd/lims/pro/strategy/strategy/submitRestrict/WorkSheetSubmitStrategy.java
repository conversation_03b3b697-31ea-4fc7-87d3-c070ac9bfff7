package com.sinoyd.lims.pro.strategy.strategy.submitRestrict;

import com.jsoniter.JsonIterator;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.base.core.BaseCodeHelper;
import com.sinoyd.base.factory.QualityTaskFactory;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.boot.frame.sys.model.ConfigModel;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoTestQCRemindTemp;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentUseRecord;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoItemRelation;
import com.sinoyd.lims.lim.dto.rcc.DtoItemRelationParams;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.*;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.strategy.IFileNameConstant;
import com.sinoyd.lims.strategy.context.CheckRelationItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 工作单提交
 *
 * <AUTHOR>
 */
@Component(IFileNameConstant.SubmitStrategyKey.SUBMIT_WORKSHEET_DATA)
@Slf4j
public class WorkSheetSubmitStrategy extends AbsSubmitRestrictStrategy {

    private CheckRelationItem checkRelationItem;

    @Override
    public List<DtoSubmitRestrictVo> generateSubmitRestrict(Object objMap, String status) {
        List<DtoSubmitRestrictVo> restrictVoList = new ArrayList<>();
        String workSheetFolderId = objMap.toString();
        DtoWorkSheetFolder workSheetFolder = workSheetFolderService.findOne(workSheetFolderId);
        List<DtoAnalyseData> analyseDataList = analyseDataService.findByWorkSheetFolderId(workSheetFolderId);
        Set<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).collect(Collectors.toSet());
        Set<String> sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).collect(Collectors.toSet());
        List<DtoSample> sampleList = sampleService.findBySampleIds(sampleIds);
        Set<String> recordIds = sampleList.stream().map(DtoSample::getReceiveId)
                .filter(p -> !UUIDHelper.GUID_EMPTY.equals(p)).collect(Collectors.toSet());
        List<DtoReceiveSampleRecord> recordList = receiveSampleRecordService.findAll(recordIds);
        // 根据检测单中的测试项目筛选样品分组数据
        List<DtoSampleGroup> sampleGroups = getSampleGroupList(sampleIds, analyseDataList);
        List<DtoTest> testList = testService.findAll(testIds);
        //总称测试项目
        Set<String> parentIds = testList.stream().map(DtoTest::getParentId)
                .filter(parentId -> !UUIDHelper.GUID_EMPTY.equals(parentId)).collect(Collectors.toSet());
        if (parentIds.size() > 0) {
            testIds.addAll(parentIds);
        }
        List<DtoTestQCRemindTemp> config2TestList = testQCRemindConfig2TestService.findByTestIds(new ArrayList<>(testIds));
        boolean flag = Boolean.FALSE;
        for (DtoTest test : testList) {
            if (test.getIsInsUseRecord()) {
                flag = Boolean.TRUE;
                break;
            }
        }
        Boolean isWorkSheet = Boolean.FALSE;
        if (EnumPRO.EnumWorkSheetStatus.检测单拒绝.getValue().equals(workSheetFolder.getWorkStatus())
                || EnumPRO.EnumWorkSheetStatus.新建.getValue().equals(workSheetFolder.getWorkStatus())
                || EnumPRO.EnumWorkSheetStatus.已经保存.getValue().equals(workSheetFolder.getWorkStatus())) {
            isWorkSheet = Boolean.TRUE;
        }
        if (isWorkSheet) {
            //出证必填验证 -ok
            restrictVoList.add(checkTestValue(analyseDataList));
            //参数必填 -ok
            restrictVoList.add(checkParamsData(analyseDataList, sampleList));
            //仪器使用记录 -ok
            restrictVoList.add(checkInstrumentUseRecord(workSheetFolderId, flag));
            //原始记录单 -ok
            ConfigModel configModel = configService.findConfig("sys.worksheet.form");
            flag = Boolean.FALSE;
            if (StringUtil.isNotNull(configModel) && StringUtil.isNotEmpty(configModel.getConfigValue())) {
                //判断是否需要有工作单
                if (Boolean.parseBoolean(configModel.getConfigValue())) {
                    flag = Boolean.TRUE;
                }
            }
            restrictVoList.add(checkWorksheet(workSheetFolderId, flag));
        }
        //分析项目关系 -ok
        Date time1 = new Date();
        restrictVoList.add(checkRelationItem(analyseDataList, sampleList));
        Date time2 = new Date();
        log.info("=====================" + (time2.getTime() - time1.getTime()) + "=====================");
        //电子签名 -ok
        List<String> personIds = new ArrayList<>();
        if (isWorkSheet) {
            if (StringUtil.isNotEmpty(workSheetFolder.getAnalystId()) &&
                    !UUIDHelper.GUID_EMPTY.equals(workSheetFolder.getAnalystId())) {
                personIds.add(workSheetFolder.getAnalystId());
            }
            if (StringUtil.isNotEmpty(workSheetFolder.getCertificatorId()) &&
                    !UUIDHelper.GUID_EMPTY.equals(workSheetFolder.getCertificatorId())) {
                personIds.add(workSheetFolder.getCertificatorId());
            }
        } else {
            personIds.add(PrincipalContextUser.getPrincipal().getUserId());
        }
        restrictVoList.add(super.checkSigUrl(personIds, EnumPRO.EnumRestrictItem.实验室电子签名));
        //质控比例判定 -ok
        config2TestList = config2TestList.stream().filter(p ->
                EnumLIM.EnumQCGrade.内部质控.getValue().equals(p.getQcGrade())).collect(Collectors.toList());
        if (config2TestList.size() > 0) {
            restrictVoList.add(this.checkRemind(workSheetFolderId));
        }
        //质控比例配置 -ok
        restrictVoList.add(checkRemindConfig(config2TestList, testList, EnumPRO.EnumRestrictItem.实验室质控比例配置));
        //质控评价 -ok
        Date time3 = new Date();
        restrictVoList.addAll(super.checkControlEvaluate(workSheetFolderId, EnumPRO.EnumRestrictItem.实验室质控评价,
                testList, EnumPRO.EnumRestrictItem.实验室质控限制配置, analyseDataList));
        if (analyseDataService.isHistoryValue() || !isWorkSheet) {
            //超标数据 -ok
            restrictVoList.add(checkDataEvaluation(workSheetFolderId));
            //历史数据 -ok
            restrictVoList.add(this.checkHistoryValue(workSheetFolderId));
        }
        Date time4 = new Date();
        log.info("=====================" + (time4.getTime() - time3.getTime()) + "=====================");
        //上岗证检测 -ok
        restrictVoList.add(checkPermit(workSheetFolder, testIds));
        restrictVoList.add(checkTime(recordList, workSheetFolder, sampleGroups));
        return restrictVoList;
    }

    /**
     * 质控比例提醒
     *
     * @param workSheetFolderId 工作单id
     * @return 返回验证结果
     */
    @Override
    protected DtoSubmitRestrictVo checkRemind(String workSheetFolderId) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        List<DtoQualityRemind> remindList = qualityRemindService.findByWorkSheetFolderId(workSheetFolderId);
        Map<String, List<DtoQualityRemind>> remindMap = remindList.stream().collect(Collectors.groupingBy(DtoQualityRemind::getRedAnalyzeItemName));
        List<String> msgList = new ArrayList<>();
        remindMap.forEach((k, v) -> {
            List<String> itemMsg = v.stream().map(p -> String.format("%s质控比例未满足%d%s", QualityTaskFactory.getInstance().getQcSample(p.getQcType())
                    .getSampleProperty(EnumLIM.EnumQCGrade.内部质控.getValue()), p.getQcRemindPercent(), "%")).collect(Collectors.toList());
            String msg = String.join(";", itemMsg);
            msgList.add(String.format("%s：%s", k, msg));
        });
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.实验室质控比例判定.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.实验室质控比例判定.getModuleName());
        if (msgList.size() > 0) {
            restrictVo.setExceptionOption(String.join(";", msgList));
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }

    /**
     * 是否存在原始记录单
     *
     * @param workSheetFolderId 工作单Id
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkWorksheet(String workSheetFolderId, Boolean isDocument) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.原始记录单.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.原始记录单.getModuleName());
        if (super.checkDocument(workSheetFolderId, BaseCodeHelper.DOCUMENT_EXTEND_ORIGINAL_RECORD).size() == 0) {
            restrictVo.setExceptionOption("未生成原始记录单");
            if (isDocument) {
                restrictVo.setIsPass(Boolean.FALSE);
            }
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }

    /**
     * 仪器使用记录验证
     *
     * @param workSheetFolderId 工作单Id
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkInstrumentUseRecord(String workSheetFolderId, Boolean isUseRecord) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.实验室仪器使用记录.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.实验室仪器使用记录.getModuleName());
        List<DtoInstrumentUseRecord> instrumentUseRecords = instrumentUseRecordService
                .findByObjectIdAndObjectType(workSheetFolderId, EnumLIM.EnumEnvRecObjType.实验室分析.getValue());
        if (isUseRecord && instrumentUseRecords.size() == 0) {
            restrictVo.setExceptionOption("未填写仪器使用记录");
            restrictVo.setIsPass(Boolean.FALSE);
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        if (instrumentUseRecords.stream().anyMatch(p -> ("1753".equals(DateUtil.dateToString(p.getStartTime(), DateUtil.YEAR_NO_MONTH)) || StringUtil.isNull(p.getStartTime())))
                || instrumentUseRecords.stream().anyMatch(p -> ("1753".equals(DateUtil.dateToString(p.getStartTime(), DateUtil.YEAR_NO_MONTH)) || StringUtil.isNull(p.getEndTime())))) {
            restrictVo.setExceptionOption("未填写仪器使用时间");
            restrictVo.setIsPass(Boolean.FALSE);
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }

    /**
     * 分析项目关系验证
     *
     * @param analyseDataList 数据集合
     * @param sampleList      样品集合
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkRelationItem(List<DtoAnalyseData> analyseDataList, List<DtoSample> sampleList) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.分析项目关系.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.分析项目关系.getModuleName());
        List<String> msgList = new ArrayList<>();
        Set<String> sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).collect(Collectors.toSet());
        Set<String> itemIds = analyseDataList.stream().map(DtoAnalyseData::getAnalyseItemId).collect(Collectors.toSet());
        List<DtoAnalyseData> analyseDatas = analyseDataService.findDataBySampleIds(sampleIds);
        List<DtoItemRelationParams> paramsList = itemRelationParamsService.findByItemIds(itemIds);
        Set<String> relationIds = paramsList.stream().map(DtoItemRelationParams::getRelationId).collect(Collectors.toSet());
        List<DtoItemRelation> relationList = itemRelationService.findAll(relationIds).stream()
                .filter(p -> EnumLIM.EnumAnalyzeItemRelationType.自检.getValue().equals(p.getType())).collect(Collectors.toList());
        List<DtoItemRelationParams> itemRelationParamsList = itemRelationParamsService.findByRelationIds(relationIds);
        List<Future<List<String>>> asyncResultList = new ArrayList<>();
        List<DtoAnalyseData> list = null;
        final int batchSize = 200;
        for (DtoAnalyseData dto : analyseDataList) {
            if (list == null) {
                list = new ArrayList<>();
            }
            if (list.size() < batchSize) {
                list.add(dto);
            } else if (list.size() == batchSize) {
                //多线程处理排序
                asyncResultList.add(checkRelationItem.initCheckRelationItem(list, sampleList, analyseDatas, paramsList, relationList, itemRelationParamsList));
                list = new ArrayList<>();
                list.add(dto);
            }
        }
        //如果存在最后一批样，需要单独去排序处理
        if (StringUtil.isNotEmpty(list)) {
            asyncResultList.add(checkRelationItem.initCheckRelationItem(list, sampleList, analyseDatas, paramsList, relationList, itemRelationParamsList));
        }
        //处理多线程处理的结果
        try {
            for (Future<List<String>> asyncResult : asyncResultList) {
                while (true) {
                    if (asyncResult.isDone() && !asyncResult.isCancelled()) {
                        msgList.addAll(asyncResult.get());
                        break;
                    } else {
                        //防止CPU高速轮询被耗空
                        Thread.sleep(1);
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("......多线程处理分析项目关系......");
        }
        if (msgList.size() > 0) {
            restrictVo.setExceptionOption(String.join(";", msgList));
            restrictVo.setIsPass(Boolean.FALSE);
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }

    /**
     * 超标数据内容
     *
     * @param workSheetFolderId 工作单Id
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkDataEvaluation(String workSheetFolderId) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.超标数据.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.超标数据.getModuleName());
        PageBean<DtoAnalyseData> pageBean = new PageBean<>();
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        DtoAnalyseDataEvaluationVo evaluationVo = new DtoAnalyseDataEvaluationVo();
        evaluationVo.setWorkSheetFolderId(workSheetFolderId);
        evaluationVo.setQualifiedType(EnumPRO.EnumStatus.已处理.getValue());
        List<DtoAnalyseDataEvaluation> analyseDataEvaluation = workSheetFolderService.findAnalyseDataEvaluation(pageBean, evaluationVo);
        addRestrictMsg(restrictVo, analyseDataEvaluation);
        return restrictVo;
    }

    /**
     * 出证必填验证
     *
     * @param analyseDataList 样品集合
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkTestValue(List<DtoAnalyseData> analyseDataList) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        //排除数据
        analyseDataList = analyseDataList.stream().filter(p -> (!StringUtils.isNotNullAndEmpty(p.getTestOrignValue())
                || !StringUtils.isNotNullAndEmpty(p.getTestValue()))).collect(Collectors.toList());
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.实验室出证必填验证.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.实验室出证必填验证.getModuleName());
        if (analyseDataList.size() > 0) {
            Set<String> itemName = analyseDataList.stream().map(DtoAnalyseData::getRedAnalyzeItemName).collect(Collectors.toSet());
            restrictVo.setExceptionOption(String.format("%s数据未录入完整", String.join("、", itemName)));
            restrictVo.setIsPass(Boolean.FALSE);
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }

    /**
     * 上岗证验证
     *
     * @param workSheetFolder 检测单
     * @param testIds  测试项目ids
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkPermit(DtoWorkSheetFolder workSheetFolder, Collection<String> testIds) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.上岗证检测.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.上岗证检测.getModuleName());
        Map<String,Object> params = new HashMap<>();
        List<String> personIds = new ArrayList<>();
        personIds.add(workSheetFolder.getAnalystId());
        //分析提交时选择双签名，复审时还需提示上岗证人员的上岗证校验
        if(StringUtil.isNotEmpty(workSheetFolder.getCertificatorId())&&!UUIDHelper.GUID_EMPTY.equals(workSheetFolder.getCertificatorId())){
            personIds.add(workSheetFolder.getCertificatorId());
        }
        params.put("personIds", personIds);
        params.put("testIds", new ArrayList<>(testIds));
        String errorMsg = personAbilityService.check(params);
        if(errorMsg.contains("过期")||errorMsg.contains("未持有")){
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        restrictVo.setExceptionOption(errorMsg);
        return restrictVo;
    }

    /**
     * 验证参数是否必填
     *
     * @param analyseDataList 数据集合
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkParamsData(List<DtoAnalyseData> analyseDataList, List<DtoSample> sampleList) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        List<DtoAnalyseOriginalRecord> recordList = analyseOriginalRecordService.findByDataIds(analyseDataList);
        List<String> msgList = new ArrayList<>();
        recordList.forEach(p -> {
            TypeLiteral<List<DtoAnalyseOriginalJson>> typeLiteral = new TypeLiteral<List<DtoAnalyseOriginalJson>>() {
            };
            List<DtoAnalyseOriginalJson> originalJsonList = JsonIterator.deserialize(p.getJson(), typeLiteral);
            //必填和默认值为空
            List<DtoAnalyseOriginalJson> originalJsons = originalJsonList.stream().filter(record -> record.getIsMust()
                    && !StringUtil.isNotEmpty(record.getDefaultValue())).collect(Collectors.toList());
            List<String> paramsNames = originalJsons.stream().map(DtoAnalyseOriginalJson::getAlias).collect(Collectors.toList());
            Optional<DtoSample> sampleOptional = sampleList.stream().filter(s -> p.getSampleId().equals(s.getId())).findFirst();
            if (paramsNames.size() > 0) {
                sampleOptional.ifPresent(s -> {
                    String msg = String.format("%s(%s)参数未填：%s", p.getItemName(), s.getCode(),
                            String.join(",", paramsNames));
                    msgList.add(msg);
                });
            }
        });
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.实验室参数必填验证.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.实验室参数必填验证.getModuleName());
        if (msgList.size() > 0) {
            restrictVo.setExceptionOption(String.join(";", msgList));
            restrictVo.setIsPass(Boolean.FALSE);
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }

    /**
     * 历史数据历史极值判定
     *
     * @param workSheetFolderId 工作单id
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkHistoryValue(String workSheetFolderId) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        //排除数据
        DtoAnalyseDataHistoryVo historyVo = new DtoAnalyseDataHistoryVo();
        historyVo.setWorksheetFolderId(workSheetFolderId);
        List<DtoAnalyseDataHistory> historyList = analyseDataService.getHistoryData(historyVo);
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.历史极值.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.历史极值.getModuleName());
        if (StringUtils.isNotNull(historyList)) {
            historyList = historyList.stream().filter(p -> p.getSampleWarnList().size() > 0).collect(Collectors.toList());
            if (historyList.size() > 0) {
                List<String> msgList = new ArrayList<>();
                for (DtoAnalyseDataHistory dataHistory : historyList) {
                    for (Map<String, String> warnMap : dataHistory.getSampleWarnList()) {
                        String warn = String.format("%s%s%s", dataHistory.getRedAnalyzeItemName(), warnMap.get("sampleCode"), warnMap.get("warn"));
                        msgList.add(warn);
                    }
                }
                restrictVo.setExceptionOption(String.join(",", msgList));
                restrictVo.setIsUnusual(Boolean.FALSE);
            }
        }
        return restrictVo;
    }

    /**
     * 根据检测单分析数据获取样品分组数据
     *
     * @param sampleIds       样品id
     * @param analyseDataList 分析数据
     * @return 样品分组数据
     */
    private List<DtoSampleGroup> getSampleGroupList(Collection<String> sampleIds, List<DtoAnalyseData> analyseDataList) {
        List<DtoSampleGroup> sampleGroups = new ArrayList<>();
        // 根据检测单中的测试项目筛选样品分组数据
        List<DtoSampleGroup> sampleGroupList = sampleGroupRepository.findBySampleIdIn(new ArrayList<>(sampleIds));
        // 筛选接样日期不等于空的数据
        sampleGroupList = sampleGroupList.stream().filter(p -> StringUtil.isNotNull(p.getReceiveSampleDate())).collect(Collectors.toList());
        List<String> sampleGroupIds = sampleGroupList.stream().map(DtoSampleGroup::getId).collect(Collectors.toList());
        List<DtoSampleGroup2Test> sampleGroup2Tests = StringUtil.isNotEmpty(sampleGroupIds) ? sampleGroup2TestRepository.findBySampleGroupIdIn(sampleGroupIds) : new ArrayList<>();
        sampleGroupList.forEach(p -> {
            List<String> testIdList = sampleGroup2Tests.stream().filter(v -> v.getSampleGroupId().equals(p.getId())).map(DtoSampleGroup2Test::getTestId).collect(Collectors.toList());
            if (analyseDataList.stream().anyMatch(analyseData -> testIdList.contains(analyseData.getTestId()) && p.getSampleId().equals(analyseData.getSampleId()))) {
                sampleGroups.add(p);
            }
        });
        return sampleGroups;
    }

    /**
     * 分析日期不能早于接样日期
     *
     * @param recordList      送样单集合
     * @param workSheetFolder 工作单
     * @param sampleGroups    分组数据
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkTime(List<DtoReceiveSampleRecord> recordList, DtoWorkSheetFolder workSheetFolder,
                                          List<DtoSampleGroup> sampleGroups) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.分析日期验证.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.分析日期验证.getModuleName());
        Boolean isTime = false;
        String anaTime = "";
        String receiveTime = "";
        //分析日期不能早于接样日期
        anaTime = StringUtil.isNotNull(workSheetFolder.getFinishTime()) && !DateUtil.dateToString(workSheetFolder.getFinishTime(), DateUtil.YEAR).contains("1753") ?
                DateUtil.dateToString(workSheetFolder.getFinishTime(), DateUtil.YEAR) :
                DateUtil.dateToString(workSheetFolder.getAnalyzeTime(), DateUtil.YEAR);
        if (StringUtil.isNotEmpty(sampleGroups)) {
            for (DtoSampleGroup sampleGroup : sampleGroups) {
                if (sampleGroup.getReceiveSampleDate() != null) {
                    receiveTime = DateUtil.dateToString(sampleGroup.getReceiveSampleDate(), DateUtil.YEAR);
                    if (DateUtil.stringToDate(anaTime, DateUtil.YEAR).before(DateUtil.stringToDate(receiveTime, DateUtil.YEAR))) {
                        isTime = true;
                        break;
                    }
                }
            }
        } else {
            for (DtoReceiveSampleRecord record : recordList) {
                if (record.getReceiveSampleDate() != null) {
                    receiveTime = DateUtil.dateToString(record.getReceiveSampleDate(), DateUtil.YEAR);
                    if (DateUtil.stringToDate(anaTime, DateUtil.YEAR).before(DateUtil.stringToDate(receiveTime, DateUtil.YEAR))) {
                        isTime = true;
                        break;
                    }
                }
            }
        }

        if (isTime) {
            restrictVo.setExceptionOption(String.format("分析日期（%s）不能早于接样日期（%s）", anaTime, receiveTime));
            restrictVo.setIsPass(Boolean.FALSE);
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }

    @Autowired
    @Lazy
    public void setCheckRelationItem(CheckRelationItem checkRelationItem) {
        this.checkRelationItem = checkRelationItem;
    }
}
