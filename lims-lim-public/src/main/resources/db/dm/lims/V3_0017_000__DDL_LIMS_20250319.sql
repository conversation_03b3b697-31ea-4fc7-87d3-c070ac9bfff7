alter table tb_pro_report add column monitorReportConfigId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000';
comment on column tb_pro_report.monitorReportConfigId is '监测报表标识';

-- 报告与监测计划关联表，用来生成监测上报报表
CREATE TABLE TB_PRO_MonitorReport2Property
(
    id       varchar(50) NOT NULL,
    reportId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    propertyId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
);
ALTER TABLE TB_PRO_MonitorReport2Property ADD CONSTRAINT PRIMARY KEY ("id");
comment on table TB_PRO_MonitorReport2Property is '报告与监测计划关联表';
comment on column TB_PRO_MonitorReport2Property.id is '标识';
comment on column TB_PRO_MonitorReport2Property.reportId is '报告标识';
comment on column TB_PRO_MonitorReport2Property.propertyId is '监测计划标识';