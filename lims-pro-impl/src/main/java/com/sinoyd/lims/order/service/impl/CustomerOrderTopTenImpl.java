package com.sinoyd.lims.order.service.impl;

import com.sinoyd.base.utils.CalendarUtil;
import com.sinoyd.lims.order.service.CustomerOrderAnaService;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.vo.BaseOrderCustomerAnalyzeVO;
import com.sinoyd.lims.pro.vo.OrderTopTenVo;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service(CustomerOrderAnaService.CUSTOMER_ORDER_TOP_TEN)
public class CustomerOrderTopTenImpl extends AbsCustomerOrder {


    /**
     * 单笔签订单额top10
     *
     * @param year 年份
     * @return List<OrderTopTenVo>
     */
    @Override
    public List<BaseOrderCustomerAnalyzeVO> statisticsList(Integer year) {
        String currentYearBegin = CalendarUtil.getCurrentYearBegin(year);
        String currentYearEnd = CalendarUtil.getCurrentYearEnd(year);

        //查询符合条件的订单数据
        StringBuilder sbOrder = new StringBuilder();
        sbOrder.append("SELECT id,orderName FROM TB_PRO_OrderForm WHERE isDeleted = 0 and signDate BETWEEN");
        sbOrder.append(" ? ");
        sbOrder.append(" AND ");
        sbOrder.append(" ? ");
        sbOrder.append(" AND ");
        sbOrder.append("grantStatus = ? ");
        Object[] orderParams = new Object[]{currentYearBegin,currentYearEnd,EnumPRO.EnumOrderSignStatus.已签订.getValue()};
        List<Map<String, Object>> orderList = super.getCustomerOrderData(sbOrder.toString(),orderParams);
        if (orderList == null) {
            return null;
        }
        List<String> idList = orderList.stream().map(item -> (String) item.get("id")).collect(Collectors.toList());
        String orderIds = "";
        for (int i = 0; i < idList.size(); i++) {
            if (i == 0) {
                orderIds = "'" + idList.get(i) + "'";
            } else {
                orderIds = String.join(",", orderIds, "'" + idList.get(i) + "'");
            }
        }
        //找出订单金额最大的前十条数据
        StringBuilder sbQuotation = new StringBuilder();
        sbQuotation.append("SELECT orderId,finalQuotation FROM TB_PRO_OrderQuotation WHERE isDeleted = 0 and orderId IN (");
        sbQuotation.append(""+orderIds+"");
        sbQuotation.append(") ORDER BY finalQuotation DESC LIMIT 0,10");
        List<Map<String, Object>> orderQuotationData = super.getCustomerOrderData(sbQuotation.toString());
        if (orderQuotationData == null) {
            return null;
        }
        //对数据进行组装
        List<BaseOrderCustomerAnalyzeVO> orderTopTenVoList = new ArrayList<>();
        for (Map<String, Object> orderQuotationMap : orderQuotationData) {
            OrderTopTenVo topTenVo = new OrderTopTenVo();
            for (Map<String, Object> orderMap : orderList) {
                String orderId =  orderQuotationMap.get("orderId").toString();
                String id = orderMap.get("id").toString();
                if (orderId.equals(id)) {
                    topTenVo.setOrderName(orderMap.get("orderName").toString());
                    String finalQuotation = orderQuotationMap.get("finalQuotation").toString();
                    int total = new BigDecimal(finalQuotation).intValue();
                    topTenVo.setFinalQuotation(total);
                    orderTopTenVoList.add(topTenVo);
                }
            }
        }

        return orderTopTenVoList;
    }


}
