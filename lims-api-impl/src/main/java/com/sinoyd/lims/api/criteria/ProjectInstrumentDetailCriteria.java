package com.sinoyd.lims.api.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;

/**
 * 仪器出入库查询
 *
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectInstrumentDetailCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 项目名称、项目编号
     */
    private String projectNameCode;

    /**
     * 仪器名称编号，出厂编号，规格型号
     */
    private String instrumentKey;

    /**
     * 出入库状态 0：所有  1：待入库 2：已入库
     */
    private Integer storageStatus = EnumLIM.EnumStorageStatus.所有.getValue();

    @Override
    public String getCondition() {
        //清除条件数据
        values.clear();
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.id = b.projectInstrumentId");
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date startDate = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and b.outDate >= :startDate");
            values.put("startDate", startDate);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date endDate = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(endDate);
            c.add(Calendar.DAY_OF_MONTH, 1);
            endDate = c.getTime();
            condition.append(" and b.outDate <= :endDate");
            values.put("endDate", endDate);
        }

        if (StringUtil.isNotEmpty(this.projectNameCode)) {
            condition.append(" and ( a.projectName like :projectNameCode or exists ( select 1 from  " +
                    "DtoProject p where p.id = a.projectId and p.projectCode like :projectNameCode))");
            values.put("projectNameCode", "%" + this.projectNameCode + "%");
        }

        if (StringUtil.isNotEmpty(this.instrumentKey)) {
            condition.append(" and exists (select 1 from DtoInstrument c " +
                    " where b.instrumentId = c.id and c.isDeleted = 0 and (c.instrumentName like :instrumentNameCode " +
                    " or c.instrumentsCode like :instrumentNameCode or c.model like :instrumentNameCode))");
            values.put("instrumentNameCode", "%" + this.instrumentKey + "%");
        }

        if (!this.storageStatus.equals(EnumLIM.EnumStorageStatus.所有.getValue())) {
            //未入库
            if (this.storageStatus.equals(EnumLIM.EnumStorageStatus.待入库.getValue())) {
                condition.append(" and b.isStorage != 1  ");
            }
            //已入库
            else if (this.storageStatus.equals(EnumLIM.EnumStorageStatus.已入库.getValue())) {
                condition.append(" and b.isStorage = 1  ");
            }
        }
        return condition.toString();
    }
}
