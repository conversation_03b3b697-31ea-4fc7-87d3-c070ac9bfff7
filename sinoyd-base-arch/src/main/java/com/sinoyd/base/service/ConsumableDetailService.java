package com.sinoyd.base.service;

import com.sinoyd.base.dto.lims.DtoConsumableDetail;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.List;

/**
 * 消耗品详单管理
 * <AUTHOR>
 * @version v1.0.0 2019/3/5
 * @since V100R001
 */
public interface ConsumableDetailService extends IBaseJpaService<DtoConsumableDetail, String> {

    /***
     * 获取生产厂商
     * @param isStandard 是否标准物质
     * @return 返回列表
     */
    List<DtoConsumableDetail> getManufacturerList(Boolean isStandard);

    /***
     * 保存详情并且更新消耗品总库存
     * @param entity 实体
     * @return 返回入库信息
     */
    DtoConsumableDetail saveAndChangeConsumable(DtoConsumableDetail entity);

    /**
     * 增加供应商
     *
     * @param entity 消耗品或者标准的入库明细
     * @return 返回增加供应商的实体
     */
    DtoConsumableDetail addSupplier(DtoConsumableDetail entity);


    /**
     * 根据消耗品id获取相应的明细数据
     *
     * @param consumableIds 消耗品id
     * @return 返回明细数据
     */
    List<DtoConsumableDetail> findByParentIds(List<String> consumableIds);

    /**
     * 获取上传附件参数
     * @param id 标识
     * @return 实体
     */
    DtoConsumableDetail findAttachment(String id);
}