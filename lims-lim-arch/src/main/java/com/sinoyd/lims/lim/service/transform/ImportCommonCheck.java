package com.sinoyd.lims.lim.service.transform;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;

import java.util.List;

/**
 *  数据迁移导入通用校验接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/9/25
 */
public interface ImportCommonCheck<K,V>{
    /**
     * 校验sheet页数据是否重复
     * @param result   结果容器
     * @param failStr  异常提示信息
     * @param list     原有数据集
     * @param k        校验实体
     * @param fields   校验字段
     * @throws Exception 异常
     */
    void checkSeetDataRepeat(ExcelVerifyHandlerResult result, StringBuilder failStr, List<K> list, K k,List<String> fields) throws Exception;

    /**
     *
     * @param result  结果容器
     * @param failStr 异常提示信息
     * @param list    原有数据集
     * @param k       校验实体
     * @param fields  校验字段
     * @throws Exception 异常
     */
    void checkRepoDataRepeat(ExcelVerifyHandlerResult result,StringBuilder failStr,List<V> list,K k,List<String> fields) throws Exception;
}
