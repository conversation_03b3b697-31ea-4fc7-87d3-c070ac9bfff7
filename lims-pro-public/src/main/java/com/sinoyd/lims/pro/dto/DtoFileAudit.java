package com.sinoyd.lims.pro.dto;

import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.lims.pro.entity.FileAudit;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;


/**
 * FileAudit实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/05/19
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "tb_pro_fileAudit")
@Data
@DynamicInsert
public class DtoFileAudit extends FileAudit {
    //审批类型
    @Transient
    private String typeStr;

    //审批状态
    @Transient
    private String stepStatusStr;

    //修编人
    @Transient
    private String revisePersonStr;

    //修编部门
    @Transient
    private String reviseDeptStr;

    //文件目录名称
    @Transient
    private String folderStr;

    //参与审核人员
    @Transient
    private String auditPeopleStr;

    //技术负责人
    @Transient
    private String techLeaderStr;

    //批准人员
    @Transient
    private String approvePersonStr;

    //接收人员
    @Transient
    private String receivePeopleStr;

    //文件备案人员
    @Transient
    private String registerPersonStr;

    //当前步骤允许操作人
    @Transient
    private String currentPersonId;

    @Transient
    private List<String> fileRelations;

    @Transient
    private List<DtoDocument> documentList;

    /**
     * 是否处理
     */
    @Transient
    private Boolean isHandle;
}