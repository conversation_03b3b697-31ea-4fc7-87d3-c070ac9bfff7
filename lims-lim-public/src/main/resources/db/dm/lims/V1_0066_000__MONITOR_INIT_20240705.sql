CREATE TABLE "TB_MONITOR_FIXEDPOINT"
(
    "id"                VARCHAR(50)   NOT NULL,
    "stationId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "stationName"       VARCHAR(255) NULL,
    "pointName"         VARCHAR(255) NULL,
    "isEnabled"         BIT          DEFAULT 0
                                      NOT NULL,
    "evaluationId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "evaluationLevelId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "remark"            VARCHAR(2000) NULL,
    "cycleOrder"        INT          DEFAULT (-1)
                                      NOT NULL,
    "timesOrder"        INT          DEFAULT (-1)
                                      NOT NULL,
    "internalCode"      VARCHAR(50) NULL,
    "pointCode"         VARCHAR(50) NULL,
    "level"             VARCHAR(50) NULL,
    "lon"               VARCHAR(50) NULL,
    "lat"               VARCHAR(50) NULL,
    "pointType"         INT          DEFAULT 0
                                      NOT NULL,
    "folderType"        VARCHAR(1000) NOT NULL,
    "sampleTypeId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "enterpriseId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "villageCode"       VARCHAR(255) NULL,
    "isDeleted"         BIT          DEFAULT 0
                                      NOT NULL,
    "orgId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                      NOT NULL,
    "creator"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                      NOT NULL,
    "createDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                      NOT NULL,
    "domainId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                      NOT NULL,
    "modifier"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                      NOT NULL,
    "modifyDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                      NOT NULL,
    "orderNum"          INT          DEFAULT 0
                                      NOT NULL,
    "examArea"          VARCHAR(50) NULL,
    "areaId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                      NOT NULL
);
CREATE TABLE "TB_MONITOR_FIXEDPOINT2POINT"
(
    "id"           VARCHAR(50) NOT NULL,
    "fixedPointId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "pointId"      VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL
);
CREATE TABLE "TB_MONITOR_FIXEDPOINT2TEST"
(
    "id"           VARCHAR(50) NOT NULL,
    "fixedPointId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "testId"       VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "timesOrder"   INT         DEFAULT 1
                               NOT NULL,
    "samplePeriod" INT         DEFAULT 1
                               NOT NULL
);
CREATE TABLE "TB_MONITOR_FIXEDPOINTEXPEND"
(
    "id"                        VARCHAR(50) NOT NULL,
    "fixedPointId"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                            NOT NULL,
    "waterId"                   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                            NOT NULL,
    "functionZoneCode"          VARCHAR(255) NULL,
    "watersl"                   VARCHAR(50) NULL,
    "areasl"                    VARCHAR(50) NULL,
    "rchal"                     VARCHAR(50) NULL,
    "ygwq"                      VARCHAR(255) NULL,
    "dayhorbegin"               INT          DEFAULT (-1)
                                            NOT NULL,
    "dayhorend"                 INT          DEFAULT (-1)
                                            NOT NULL,
    "nighthorbegin"             INT          DEFAULT (-1)
                                            NOT NULL,
    "nighthorend"               INT          DEFAULT (-1)
                                            NOT NULL,
    "refer"                     VARCHAR(50) NULL,
    "gridLength"                VARCHAR(255) NULL,
    "gridWidth"                 VARCHAR(255) NULL,
    "noiseSourceCode"           VARCHAR(255) NULL,
    "noiseFunZoneCode"          VARCHAR(255) NULL,
    "gridCoverPeoples"          VARCHAR(255) NULL,
    "rdsecName"                 VARCHAR(255) NULL,
    "rdsecfromto"               VARCHAR(255) NULL,
    "railwayLength"             VARCHAR(255) NULL,
    "railwayWidth"              VARCHAR(255) NULL,
    "rdLevel"                   VARCHAR(50) NULL,
    "weekCalcu"                 VARCHAR(50) NULL,
    "so2pl"                     VARCHAR(50) NULL,
    "acidpl"                    VARCHAR(50) NULL,
    "airpl"                     VARCHAR(50) NULL,
    "acidp"                     VARCHAR(50) NULL,
    "underWaterTypeCode"        VARCHAR(255) NULL,
    "underWaterType"            VARCHAR(255) NULL,
    "exhaustPipeHeight"         VARCHAR(255) NULL,
    "craftFacilityName"         VARCHAR(255) NULL,
    "purificateFacilityName"    VARCHAR(255) NULL,
    "pollutionType"             VARCHAR(255) NULL,
    "craftFacilityUseDate"      TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                            NOT NULL,
    "boilerMakeUnit"            VARCHAR(255) NULL,
    "equipmentTypeName"         VARCHAR(255) NULL,
    "boilerUseDate"             TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                            NOT NULL,
    "chimneyHeight"             VARCHAR(255) NULL,
    "purificateFacilityUnit"    VARCHAR(255) NULL,
    "purificateFacilityType"    VARCHAR(255) NULL,
    "purificateFacilityUseDate" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                            NOT NULL,
    "fuelType"                  VARCHAR(255) NULL,
    "stoveFacilityType"         VARCHAR(255) NULL,
    "stoveFacilityCode"         VARCHAR(255) NULL,
    "emissionFate"              VARCHAR(255) NULL,
    "importAndExport"           VARCHAR(255) NULL,
    "orgId"                     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                            NOT NULL,
    "creator"                   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                            NOT NULL,
    "createDate"                TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                            NOT NULL,
    "domainId"                  VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                            NOT NULL,
    "modifier"                  VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                            NOT NULL,
    "modifyDate"                TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                            NOT NULL,
    "waterworks"                VARCHAR(200) NULL
);
CREATE TABLE "TB_MONITOR_FIXEDPOINTPROPERTY"
(
    "id"           VARCHAR(50) NOT NULL,
    "parentId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "propertyName" VARCHAR(255) NULL,
    "orderNum"     INT          DEFAULT (-1)
                               NOT NULL,
    "year"         INT          DEFAULT (-1)
                               NOT NULL,
    "month"        INT          DEFAULT (-1)
                               NOT NULL,
    "cycleOrder"   INT          DEFAULT 1
                               NOT NULL,
    "timesOrder"   INT          DEFAULT 1
                               NOT NULL,
    "sampleTypeId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "pointType"    VARCHAR(50) NULL,
    "modifyDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                               NOT NULL,
    "isDeleted"    BIT          DEFAULT 0
                               NOT NULL,
    "remark"       VARCHAR(2000) NULL,
    "orgId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "creator"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "createDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                               NOT NULL,
    "domainId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "modifier"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL
);
CREATE TABLE "TB_MONITOR_FIXEDPOINTSORT"
(
    "id"         VARCHAR(50) NOT NULL,
    "sortName"   VARCHAR(255) NULL,
    "orderNum"   INT          DEFAULT (-1)
                             NOT NULL,
    "isDeleted"  BIT          DEFAULT 0
                             NOT NULL,
    "orgId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "creator"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                             NOT NULL,
    "domainId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "modifier"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                             NOT NULL
);
CREATE TABLE "TB_MONITOR_FIXEDPOINTSORTDETIL"
(
    "id"           VARCHAR(50) NOT NULL,
    "sortId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "fixedPointId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "orderNum"     INT          DEFAULT (-1)
                               NOT NULL,
    "orgId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "creator"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "createDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                               NOT NULL,
    "domainId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "modifier"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "modifyDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                               NOT NULL
);
CREATE TABLE "TB_MONITOR_OICINFORMATION"
(
    "id"              VARCHAR(50) NOT NULL,
    "fixedPointId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "instrumentName"  VARCHAR(50) NULL,
    "instrumentModel" VARCHAR(50) NULL,
    "instrumentCode"  VARCHAR(50) NULL,
    "range"           VARCHAR(50) NULL,
    "orgId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "creator"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "createDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                  NOT NULL,
    "domainId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "modifier"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "modifyDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                  NOT NULL
);
CREATE TABLE "TB_MONITOR_OICINFORMATION2TEST"
(
    "id"     VARCHAR(50) NOT NULL,
    "oicId"  VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                         NOT NULL,
    "testId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                         NOT NULL
);
CREATE TABLE "TB_MONITOR_POINTEXTENDCONFIG"
(
    "id"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NOT NULL,
    "pointType"      VARCHAR(50) NULL,
    "paramsId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NOT NULL,
    "filedName"      VARCHAR(50) NULL,
    "filedAlias"     VARCHAR(50) NULL,
    "defaultValue"   VARCHAR(100) NULL,
    "orderNum"       INT NULL,
    "dataSource"     VARCHAR(2000) NULL,
    "codeDataSource" VARCHAR(100) NULL,
    "dataSourceUrl"  VARCHAR(100) NULL,
    "urlReturnKey"   VARCHAR(100) NULL,
    "urlReturnValue" VARCHAR(100) NULL,
    "treeChildFiled" VARCHAR(100) NULL,
    "dataSourceType" INT          DEFAULT 1
        NOT NULL,
    "defaultControl" INT          DEFAULT 1
        NOT NULL,
    "requiredInd"    BIT          DEFAULT 0
        NOT NULL,
    "isDeleted"      BIT          DEFAULT 0
        NOT NULL,
    "orgId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NOT NULL,
    "creator"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NOT NULL,
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
        NOT NULL,
    "domainId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NOT NULL,
    "modifier"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NOT NULL,
    "modifyDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
        NOT NULL
);
CREATE TABLE "TB_MONITOR_POINTEXTENDDATA"
(
    "id"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NOT NULL,
    "pointType"      VARCHAR(50) NULL,
    "extendConfigId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NOT NULL,
    "fixedPointId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NOT NULL,
    "filedName"      VARCHAR(50) NULL,
    "filedAlias"     VARCHAR(50) NULL,
    "filedValue"     VARCHAR(100) NULL,
    "orderNum"       INT NULL,
    "orgId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NOT NULL,
    "creator"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NOT NULL,
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
        NOT NULL,
    "domainId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NOT NULL,
    "modifier"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NOT NULL,
    "modifyDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
        NOT NULL
);
CREATE TABLE "TB_MONITOR_PROPERTY2POINT"
(
    "id"           VARCHAR(50) NOT NULL,
    "fixedPointId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "propertyId"   VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "pointCode"    VARCHAR(100) NULL
);
CREATE TABLE "TB_MONITOR_PROPERTYPOINT2TEST"
(
    "id"              VARCHAR(50) NOT NULL,
    "propertyPointId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "testId"          VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "samplePeriod"    INT         DEFAULT 1
                                  NOT NULL,
    "timesOrder"      INT         DEFAULT 1
                                  NOT NULL
);
CREATE TABLE "TB_MONITOR_STATION"
(
    "id"         VARCHAR(50) NOT NULL,
    "stcode"     VARCHAR(50) NULL,
    "stname"     VARCHAR(50) NULL,
    "staddress"  VARCHAR(255) NULL,
    "isEndable"  BIT          DEFAULT 0
                             NOT NULL,
    "remark"     VARCHAR(2000) NULL,
    "orderNum"   INT          DEFAULT 0
                             NOT NULL,
    "entId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "isDeleted"  BIT          DEFAULT 0
                             NOT NULL,
    "orgId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "creator"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                             NOT NULL,
    "domainId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "modifier"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                             NOT NULL
);
CREATE TABLE "TB_MONITOR_WATER"
(
    "id"         VARCHAR(50) NOT NULL,
    "parentId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "waterName"  VARCHAR(50) NULL,
    "waterCode"  VARCHAR(50) NULL,
    "waterType"  VARCHAR(50) NULL,
    "isEnabled"  BIT          DEFAULT 0
                             NOT NULL,
    "remark"     VARCHAR(2000) NULL,
    "isDeleted"  BIT          DEFAULT 0
        NULL,
    "orgId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "creator"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                             NOT NULL,
    "domainId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "modifier"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                             NOT NULL
);
CREATE TABLE "TB_MONITOR_WATEREXPAND"
(
    "id"                    VARCHAR(50) NOT NULL,
    "waterId"               VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                        NOT NULL,
    "waterFunctionZoneType" VARCHAR(50) NULL,
    "startPlaceName"        VARCHAR(50) NULL,
    "netWaterLevel"         VARCHAR(50) NULL,
    "areaWaterLevel"        VARCHAR(50) NULL,
    "lakesTypeCode"         VARCHAR(50) NULL,
    "waterl"                VARCHAR(50) NULL,
    "awaterl"               VARCHAR(50) NULL,
    "locationName"          VARCHAR(250) NULL,
    "endPlaceName"          VARCHAR(50) NULL,
    "waterFunctionZoneLen"  VARCHAR(50) NULL,
    "yswq"                  VARCHAR(50) NULL,
    "remark"                VARCHAR(255) NULL,
    "orgId"                 VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                        NOT NULL,
    "creator"               VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                        NOT NULL,
    "createDate"            TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                        NOT NULL,
    "domainId"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                        NOT NULL,
    "modifier"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                        NOT NULL,
    "modifyDate"            TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                        NOT NULL
);
INSERT INTO "TB_MONITOR_FIXEDPOINT"("id", "stationId", "stationName", "pointName", "isEnabled", "evaluationId",
                                    "evaluationLevelId", "remark", "cycleOrder", "timesOrder", "internalCode",
                                    "pointCode", "level", "lon", "lat", "pointType", "folderType", "sampleTypeId",
                                    "enterpriseId", "villageCode", "isDeleted", "orgId", "creator", "createDate",
                                    "domainId", "modifier", "modifyDate", "orderNum", "examArea", "areaId")
VALUES ('4f80ef6b-5800-498e-bee6-a1133f8d763e', '3bb943ff-eccc-44dd-8c5e-39141960a22d', '江苏远大', '远大点位', 1, '',
        '', '', 2, 3, null, null, '', '', '', 2, 'LIM_PointType_WasteGas', '099E386B-958F-4645-A2B2-DC572D18E7AE',
        '853b33fd-bd85-4631-9eab-6c91e8a11e1e', '', 0, '5f7bcf90feb545968424b0a872863876',
        '46905a56-f770-42be-8d41-5b6c94f0ed52', '2023-09-15 18:53:09', '5f7bcf90feb545968424b0a872863876',
        '46905a56-f770-42be-8d41-5b6c94f0ed52', '2023-09-15 18:53:09', 0, null, '00000000-0000-0000-0000-000000000000');
INSERT INTO "TB_MONITOR_FIXEDPOINT"("id", "stationId", "stationName", "pointName", "isEnabled", "evaluationId",
                                    "evaluationLevelId", "remark", "cycleOrder", "timesOrder", "internalCode",
                                    "pointCode", "level", "lon", "lat", "pointType", "folderType", "sampleTypeId",
                                    "enterpriseId", "villageCode", "isDeleted", "orgId", "creator", "createDate",
                                    "domainId", "modifier", "modifyDate", "orderNum", "examArea", "areaId")
VALUES ('dce5ce60-c256-4061-a637-14c8a3fc9c7d', '3bb943ff-eccc-44dd-8c5e-39141960a22d', '江苏远大', '远大废水', 1,
        'FF3516ED-ABCE-47C7-A2F7-03241BFDE268', 'c792ee79-ce51-11eb-91d6-0fdbdb41502d', '', 2, 3, null, null, '', '',
        '', 2, 'LIM_PointType_Effluents', '099E386B-958F-4645-A2B2-DC572D18E7AE',
        '853b33fd-bd85-4631-9eab-6c91e8a11e1e', '', 0, '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-09-23 10:03:17', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:43:23', 0, null, '00000000-0000-0000-0000-000000000000');
INSERT INTO "TB_MONITOR_FIXEDPOINT"("id", "stationId", "stationName", "pointName", "isEnabled", "evaluationId",
                                    "evaluationLevelId", "remark", "cycleOrder", "timesOrder", "internalCode",
                                    "pointCode", "level", "lon", "lat", "pointType", "folderType", "sampleTypeId",
                                    "enterpriseId", "villageCode", "isDeleted", "orgId", "creator", "createDate",
                                    "domainId", "modifier", "modifyDate", "orderNum", "examArea", "areaId")
VALUES ('e8a037ee-2461-4f4e-85b7-426a9e7d51e7', '3bb943ff-eccc-44dd-8c5e-39141960a22d', '江苏远大', '一干河东', 1,
        '1D479E26-05F7-4D82-927F-DBEFDD0171B6', '1bbaa2c7-cdb6-11eb-8484-f51ec87d8651', '', 1, 1, null, '', '', null,
        null, 1, 'LIM_PointType_River', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '', 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:44:56',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-14 16:11:31', 0, null, '');

INSERT INTO "TB_MONITOR_FIXEDPOINT2TEST"("id", "fixedPointId", "testId", "timesOrder", "samplePeriod")
VALUES ('293ed8fc-777b-4fa8-bcce-cec4c0b49ea3', '4f80ef6b-5800-498e-bee6-a1133f8d763e',
        '0dc442bb-aeb4-4448-ab15-1c6332184342', 1, 1);
INSERT INTO "TB_MONITOR_FIXEDPOINT2TEST"("id", "fixedPointId", "testId", "timesOrder", "samplePeriod")
VALUES ('296864bc-ace7-42ea-9053-2d54049b5172', '4f80ef6b-5800-498e-bee6-a1133f8d763e',
        '14ce7c7c-7cef-4fe3-b7af-07878c873d23', 1, 1);
INSERT INTO "TB_MONITOR_FIXEDPOINT2TEST"("id", "fixedPointId", "testId", "timesOrder", "samplePeriod")
VALUES ('50b52639-6c00-4018-9e3a-743f0a8811b6', 'dce5ce60-c256-4061-a637-14c8a3fc9c7d',
        '6CD54881-9A72-ABCD-B2F9-01C5118A1576', 1, 1);
INSERT INTO "TB_MONITOR_FIXEDPOINT2TEST"("id", "fixedPointId", "testId", "timesOrder", "samplePeriod")
VALUES ('86dd5893-7455-4383-b31f-3ef94e321f0b', '4f80ef6b-5800-498e-bee6-a1133f8d763e',
        '1986ada9-7a8d-4e1b-8a4e-18fd9e842981', 1, 1);
INSERT INTO "TB_MONITOR_FIXEDPOINT2TEST"("id", "fixedPointId", "testId", "timesOrder", "samplePeriod")
VALUES ('94c00263-dc7c-4d12-b74b-0cc566d73438', 'dce5ce60-c256-4061-a637-14c8a3fc9c7d',
        '076f8d12-62b2-42d0-8e66-7a428446ced1', 1, 1);

INSERT INTO "TB_MONITOR_FIXEDPOINTPROPERTY"("id", "parentId", "propertyName", "orderNum", "year", "month", "cycleOrder",
                                            "timesOrder", "sampleTypeId", "pointType", "modifyDate", "isDeleted",
                                            "remark", "orgId", "creator", "createDate", "domainId", "modifier")
VALUES ('1e9d3ece-ef9c-4dc2-98ac-51d7f2d454bb', '00000000-0000-0000-0000-000000000000', '地表水监测', 0, 2024, -1, 1, 1,
        '00000000-0000-0000-0000-000000000000', null, '2024-05-10 16:45:09', 0, '', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:45:09', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351');
INSERT INTO "TB_MONITOR_FIXEDPOINTPROPERTY"("id", "parentId", "propertyName", "orderNum", "year", "month", "cycleOrder",
                                            "timesOrder", "sampleTypeId", "pointType", "modifyDate", "isDeleted",
                                            "remark", "orgId", "creator", "createDate", "domainId", "modifier")
VALUES ('2a7d04f5-5c88-4677-908d-3d63faa68d7a', '4b42a7fa-f3b8-43a7-8a28-c84dbc71b304', '地表水监测', 0, 2023, 1, 1, 1,
        'B9F67616-347E-11E7-8B36-5F03E36D010C', 'LIM_PointType_River', '2023-12-28 14:00:54', 0, '',
        '5f7bcf90feb545968424b0a872863876', '46905a56-f770-42be-8d41-5b6c94f0ed52', '2023-12-28 14:00:54',
        '5f7bcf90feb545968424b0a872863876', '46905a56-f770-42be-8d41-5b6c94f0ed52');
INSERT INTO "TB_MONITOR_FIXEDPOINTPROPERTY"("id", "parentId", "propertyName", "orderNum", "year", "month", "cycleOrder",
                                            "timesOrder", "sampleTypeId", "pointType", "modifyDate", "isDeleted",
                                            "remark", "orgId", "creator", "createDate", "domainId", "modifier")
VALUES ('3f6c70b7-a8aa-43c4-82a5-402922a6a27f', 'df20f939-cbca-41bf-a3ac-2b147a354cb8', '地表水监测', -1, 2024, 5, 1, 1,
        'B9F67616-347E-11E7-8B36-5F03E36D010C', 'LIM_PointType_River', '2024-05-10 16:47:36', 1, null,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:47:33',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351');
INSERT INTO "TB_MONITOR_FIXEDPOINTPROPERTY"("id", "parentId", "propertyName", "orderNum", "year", "month", "cycleOrder",
                                            "timesOrder", "sampleTypeId", "pointType", "modifyDate", "isDeleted",
                                            "remark", "orgId", "creator", "createDate", "domainId", "modifier")
VALUES ('4b42a7fa-f3b8-43a7-8a28-c84dbc71b304', '00000000-0000-0000-0000-000000000000', '地表水例行监测', 0, 2023, -1,
        1, 1, '00000000-0000-0000-0000-000000000000', null, '2023-12-28 13:49:03', 0, '',
        '5f7bcf90feb545968424b0a872863876', '46905a56-f770-42be-8d41-5b6c94f0ed52', '2023-12-28 13:49:03',
        '5f7bcf90feb545968424b0a872863876', '46905a56-f770-42be-8d41-5b6c94f0ed52');
INSERT INTO "TB_MONITOR_FIXEDPOINTPROPERTY"("id", "parentId", "propertyName", "orderNum", "year", "month", "cycleOrder",
                                            "timesOrder", "sampleTypeId", "pointType", "modifyDate", "isDeleted",
                                            "remark", "orgId", "creator", "createDate", "domainId", "modifier")
VALUES ('610345bb-942b-42e6-ad83-e2c130676b9b', 'df20f939-cbca-41bf-a3ac-2b147a354cb8', '地表水监测', -1, 2024, 7, 1, 1,
        'B9F67616-347E-11E7-8B36-5F03E36D010C', 'LIM_PointType_River', '2024-05-10 16:47:36', 1, null,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:47:33',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351');
INSERT INTO "TB_MONITOR_FIXEDPOINTPROPERTY"("id", "parentId", "propertyName", "orderNum", "year", "month", "cycleOrder",
                                            "timesOrder", "sampleTypeId", "pointType", "modifyDate", "isDeleted",
                                            "remark", "orgId", "creator", "createDate", "domainId", "modifier")
VALUES ('69e61930-496f-46c7-9eff-aa2a7844caf2', 'df20f939-cbca-41bf-a3ac-2b147a354cb8', '地表水监测', -1, 2024, 9, 1, 1,
        'B9F67616-347E-11E7-8B36-5F03E36D010C', 'LIM_PointType_River', '2024-05-10 16:47:36', 1, null,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:47:33',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351');
INSERT INTO "TB_MONITOR_FIXEDPOINTPROPERTY"("id", "parentId", "propertyName", "orderNum", "year", "month", "cycleOrder",
                                            "timesOrder", "sampleTypeId", "pointType", "modifyDate", "isDeleted",
                                            "remark", "orgId", "creator", "createDate", "domainId", "modifier")
VALUES ('7402b1ad-74b7-4a7a-970b-ab5fcd3bf847', '1e9d3ece-ef9c-4dc2-98ac-51d7f2d454bb', '地表水监测', -1, 2024, 11, 1,
        1, 'B9F67616-347E-11E7-8B36-5F03E36D010C', 'LIM_PointType_River', '2024-05-10 16:47:10', 0, null,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:47:10',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351');
INSERT INTO "TB_MONITOR_FIXEDPOINTPROPERTY"("id", "parentId", "propertyName", "orderNum", "year", "month", "cycleOrder",
                                            "timesOrder", "sampleTypeId", "pointType", "modifyDate", "isDeleted",
                                            "remark", "orgId", "creator", "createDate", "domainId", "modifier")
VALUES ('79e2e5ff-8f4c-4fab-9568-f0e6614f7b8d', 'df20f939-cbca-41bf-a3ac-2b147a354cb8', '地表水监测', -1, 2024, 3, 1, 1,
        'B9F67616-347E-11E7-8B36-5F03E36D010C', 'LIM_PointType_River', '2024-05-10 16:47:36', 1, null,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:47:33',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351');
INSERT INTO "TB_MONITOR_FIXEDPOINTPROPERTY"("id", "parentId", "propertyName", "orderNum", "year", "month", "cycleOrder",
                                            "timesOrder", "sampleTypeId", "pointType", "modifyDate", "isDeleted",
                                            "remark", "orgId", "creator", "createDate", "domainId", "modifier")
VALUES ('86215bd8-bb62-4756-890c-969a9ca65272', '1e9d3ece-ef9c-4dc2-98ac-51d7f2d454bb', '地表水监测', 0, 2024, 1, 1, 4,
        'B9F67616-347E-11E7-8B36-5F03E36D010C', 'LIM_PointType_River', '2024-05-10 16:48:40', 0, '',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:46:04',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351');
INSERT INTO "TB_MONITOR_FIXEDPOINTPROPERTY"("id", "parentId", "propertyName", "orderNum", "year", "month", "cycleOrder",
                                            "timesOrder", "sampleTypeId", "pointType", "modifyDate", "isDeleted",
                                            "remark", "orgId", "creator", "createDate", "domainId", "modifier")
VALUES ('8d371250-7369-4c08-b761-ee649f5ada23', 'df20f939-cbca-41bf-a3ac-2b147a354cb8', '地表水监测', -1, 2024, 1, 1, 1,
        'B9F67616-347E-11E7-8B36-5F03E36D010C', 'LIM_PointType_River', '2024-05-10 16:47:36', 1, null,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:47:33',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351');
INSERT INTO "TB_MONITOR_FIXEDPOINTPROPERTY"("id", "parentId", "propertyName", "orderNum", "year", "month", "cycleOrder",
                                            "timesOrder", "sampleTypeId", "pointType", "modifyDate", "isDeleted",
                                            "remark", "orgId", "creator", "createDate", "domainId", "modifier")
VALUES ('906019c7-2fc1-4c2d-9ed9-bf6b12ef39b2', '1e9d3ece-ef9c-4dc2-98ac-51d7f2d454bb', '地表水监测', -1, 2024, 3, 1, 1,
        'B9F67616-347E-11E7-8B36-5F03E36D010C', 'LIM_PointType_River', '2024-05-10 16:47:10', 0, null,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:47:10',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351');
INSERT INTO "TB_MONITOR_FIXEDPOINTPROPERTY"("id", "parentId", "propertyName", "orderNum", "year", "month", "cycleOrder",
                                            "timesOrder", "sampleTypeId", "pointType", "modifyDate", "isDeleted",
                                            "remark", "orgId", "creator", "createDate", "domainId", "modifier")
VALUES ('96d42bca-2f2b-4af8-8fba-2ed5ff27863d', 'df20f939-cbca-41bf-a3ac-2b147a354cb8', '地表水监测', -1, 2024, 11, 1,
        1, 'B9F67616-347E-11E7-8B36-5F03E36D010C', 'LIM_PointType_River', '2024-05-10 16:47:36', 1, null,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:47:33',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351');
INSERT INTO "TB_MONITOR_FIXEDPOINTPROPERTY"("id", "parentId", "propertyName", "orderNum", "year", "month", "cycleOrder",
                                            "timesOrder", "sampleTypeId", "pointType", "modifyDate", "isDeleted",
                                            "remark", "orgId", "creator", "createDate", "domainId", "modifier")
VALUES ('9be6f5a6-e373-41a5-8337-9fbfca355dae', '1e9d3ece-ef9c-4dc2-98ac-51d7f2d454bb', '地表水监测', -1, 2024, 9, 1, 1,
        'B9F67616-347E-11E7-8B36-5F03E36D010C', 'LIM_PointType_River', '2024-05-10 16:46:33', 1, null,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:46:16',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351');
INSERT INTO "TB_MONITOR_FIXEDPOINTPROPERTY"("id", "parentId", "propertyName", "orderNum", "year", "month", "cycleOrder",
                                            "timesOrder", "sampleTypeId", "pointType", "modifyDate", "isDeleted",
                                            "remark", "orgId", "creator", "createDate", "domainId", "modifier")
VALUES ('a392d54b-799a-4c58-992b-3db895108f74', '1e9d3ece-ef9c-4dc2-98ac-51d7f2d454bb', '地表水监测', -1, 2024, 7, 1, 1,
        'B9F67616-347E-11E7-8B36-5F03E36D010C', 'LIM_PointType_River', '2024-05-10 16:47:10', 0, null,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:47:10',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351');
INSERT INTO "TB_MONITOR_FIXEDPOINTPROPERTY"("id", "parentId", "propertyName", "orderNum", "year", "month", "cycleOrder",
                                            "timesOrder", "sampleTypeId", "pointType", "modifyDate", "isDeleted",
                                            "remark", "orgId", "creator", "createDate", "domainId", "modifier")
VALUES ('c74f0834-0c18-47fd-93fa-4a46aa3d3fcc', '1e9d3ece-ef9c-4dc2-98ac-51d7f2d454bb', '地表水监测', -1, 2024, 11, 1,
        1, 'B9F67616-347E-11E7-8B36-5F03E36D010C', 'LIM_PointType_River', '2024-05-10 16:46:36', 1, null,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:46:16',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351');
INSERT INTO "TB_MONITOR_FIXEDPOINTPROPERTY"("id", "parentId", "propertyName", "orderNum", "year", "month", "cycleOrder",
                                            "timesOrder", "sampleTypeId", "pointType", "modifyDate", "isDeleted",
                                            "remark", "orgId", "creator", "createDate", "domainId", "modifier")
VALUES ('cb515eab-3880-414a-8372-19cbe33da6b5', '1e9d3ece-ef9c-4dc2-98ac-51d7f2d454bb', '地表水监测', -1, 2024, 5, 1, 1,
        'B9F67616-347E-11E7-8B36-5F03E36D010C', 'LIM_PointType_River', '2024-05-10 16:47:10', 0, null,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:47:10',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351');
INSERT INTO "TB_MONITOR_FIXEDPOINTPROPERTY"("id", "parentId", "propertyName", "orderNum", "year", "month", "cycleOrder",
                                            "timesOrder", "sampleTypeId", "pointType", "modifyDate", "isDeleted",
                                            "remark", "orgId", "creator", "createDate", "domainId", "modifier")
VALUES ('df20f939-cbca-41bf-a3ac-2b147a354cb8', '00000000-0000-0000-0000-000000000000', '地表水监测', -1, 2024, -1, 1,
        1, '00000000-0000-0000-0000-000000000000', null, '2024-05-10 16:47:36', 1, null,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:47:33',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351');
INSERT INTO "TB_MONITOR_FIXEDPOINTPROPERTY"("id", "parentId", "propertyName", "orderNum", "year", "month", "cycleOrder",
                                            "timesOrder", "sampleTypeId", "pointType", "modifyDate", "isDeleted",
                                            "remark", "orgId", "creator", "createDate", "domainId", "modifier")
VALUES ('df37485f-ab88-482a-9205-b0e42ae82b0c', '1e9d3ece-ef9c-4dc2-98ac-51d7f2d454bb', '地表水监测', 0, 2024, 12, 1, 1,
        'B9F67616-347E-11E7-8B36-5F03E36D010C', 'LIM_PointType_River', '2024-05-10 16:45:51', 1, '',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:45:43',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351');
INSERT INTO "TB_MONITOR_FIXEDPOINTPROPERTY"("id", "parentId", "propertyName", "orderNum", "year", "month", "cycleOrder",
                                            "timesOrder", "sampleTypeId", "pointType", "modifyDate", "isDeleted",
                                            "remark", "orgId", "creator", "createDate", "domainId", "modifier")
VALUES ('e07c6695-1abe-4f56-a453-5d706ee3b98c', '1e9d3ece-ef9c-4dc2-98ac-51d7f2d454bb', '地表水监测', -1, 2024, 7, 1, 1,
        'B9F67616-347E-11E7-8B36-5F03E36D010C', 'LIM_PointType_River', '2024-05-10 16:46:29', 1, null,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:46:16',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351');
INSERT INTO "TB_MONITOR_FIXEDPOINTPROPERTY"("id", "parentId", "propertyName", "orderNum", "year", "month", "cycleOrder",
                                            "timesOrder", "sampleTypeId", "pointType", "modifyDate", "isDeleted",
                                            "remark", "orgId", "creator", "createDate", "domainId", "modifier")
VALUES ('eb9d0320-b355-4093-92b4-2609e473a712', '1e9d3ece-ef9c-4dc2-98ac-51d7f2d454bb', '地表水监测', -1, 2024, 3, 1, 1,
        'B9F67616-347E-11E7-8B36-5F03E36D010C', 'LIM_PointType_River', '2024-05-10 16:46:24', 1, null,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:46:16',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351');
INSERT INTO "TB_MONITOR_FIXEDPOINTPROPERTY"("id", "parentId", "propertyName", "orderNum", "year", "month", "cycleOrder",
                                            "timesOrder", "sampleTypeId", "pointType", "modifyDate", "isDeleted",
                                            "remark", "orgId", "creator", "createDate", "domainId", "modifier")
VALUES ('f27b3a07-c518-45cb-af22-7283486a6450', '1e9d3ece-ef9c-4dc2-98ac-51d7f2d454bb', '地表水监测', -1, 2024, 5, 1, 1,
        'B9F67616-347E-11E7-8B36-5F03E36D010C', 'LIM_PointType_River', '2024-05-10 16:46:27', 1, null,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:46:16',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351');
INSERT INTO "TB_MONITOR_FIXEDPOINTPROPERTY"("id", "parentId", "propertyName", "orderNum", "year", "month", "cycleOrder",
                                            "timesOrder", "sampleTypeId", "pointType", "modifyDate", "isDeleted",
                                            "remark", "orgId", "creator", "createDate", "domainId", "modifier")
VALUES ('fe64c1de-b346-4b8c-9592-96c9b8054153', '1e9d3ece-ef9c-4dc2-98ac-51d7f2d454bb', '地表水监测', -1, 2024, 9, 1, 1,
        'B9F67616-347E-11E7-8B36-5F03E36D010C', 'LIM_PointType_River', '2024-05-10 16:47:10', 0, null,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:47:10',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351');

INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('08da098e-7098-41ea-be91-1296357785a8', 'LIM_PointType_DrinkWater', 'bbb33fb5-b62a-45ab-8777-d52a53190ebc',
        '所属水厂', 'waterworks', '', 200, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:20:02',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:38:22');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('10bffe1b-3671-4eba-9819-40bdd2dff01a', 'LIM_PointType_Boiler', '3e9cb1a5-a5e2-4d37-968d-3ebe707306dc',
        '燃料类型', 'fuelType', '', 300, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:20:10',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:20:10');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('156f72a8-572f-409c-bbb9-c53396aec481', 'LIM_PointType_YQKLW', 'a631e94d-e43b-4db2-8c28-d4e782b9603e',
        '烟囱高度', 'chimneyHeight', '', 100, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:22:55',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:22:55');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('1afded77-897a-4bce-8d98-d2195f62270f', 'LIM_PointType_Boiler', 'f14d4f78-aee4-447d-b8f0-283353c41e98',
        '烟囱高度(米)', 'chimneyHeight', '', 700, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:18:56',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:18:56');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('1be7926e-9c0a-43a0-8cf3-fd3f173570bb', 'LIM_PointType_Boiler', '0072e220-f1bc-404f-9613-856e60b6d4c2',
        '锅炉投运日期', 'boilerUseDate', '', 800, '[{"key":"","value":""}]', '', '', '', '', '', 0, 2, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:18:03',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:18:37');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('1eb2b9a5-4248-4b7f-85d8-bfaca539f34d', 'LIM_PointType_DrinkWater', '27d548df-5f79-41c8-b550-0f35a5f762b0',
        '月取水量', 'ygwq', '', 100, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:20:19',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:38:19');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('32ae10d4-37a3-4e9a-8256-ab38a0ea68f3', 'LIM_PointType_YQKLW', '17309f29-2dd6-4338-ad62-124be709d5fa',
        '炉窑设备编号', 'stoveFacilityCode', '', 400, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:21:18',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:21:18');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('3a765ecd-fa98-4a89-9563-a8d0e6c1afd8', 'LIM_PointType_DXS', '4182bc30-5f1f-4568-9f21-9b9936bde787',
        '地下水类型代码', 'underWaterTypeCode', '', 200, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:21:13',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:38:36');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('51a02fdf-399f-4dce-837a-5cd941f5c561', 'LIM_PointType_GNQZS', 'daae9fc2-e8e9-47ea-8ab9-f5b7b5b4d103',
        '测点参照物', 'refer', '', 100, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:26:08',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:40:18');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('5849a582-d202-4716-8cee-e48ce1adebae', 'LIM_PointType_YQKLW', 'c100aff8-2481-42cf-b433-57e654209bc7',
        '炉窑设备型号', 'stoveFacilityType', '', 300, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:20:59',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:21:27');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('5c0a191d-692a-41a2-822e-e981c62bd7aa', 'LIM_PointType_JTZS', 'ef29ae6f-f481-4a9d-be67-f788e2e272ad',
        '路段起始点', 'rdsecfromto', '', 400, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:27:10',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:41:08');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('602a784a-a9c6-4dfa-ba39-23d80d4d2499', 'LIM_PointType_Lake', 'df906b33-2eeb-4138-ba3a-104381535d99',
        '域管断面级别', 'areasl', '', 700, '[{"key":"","value":""}]', 'LIM_DomainSectionLevel', '', '', '', '', 2, 4, 0,
        0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:18:18',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:37:23');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('6857567c-e198-4324-a7ac-187f672cd3ab', 'LIM_PointType_WasteGas', '1a31c8f8-c81c-45ed-826c-c7812ddbc0f9',
        '净化设备名称', 'purificateFacilityName', '', 300, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:15:44',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:24:44');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('74a3944c-6e39-439f-9c4f-b81257f4ba29', 'LIM_PointType_River', 'ff850282-e04f-489d-807e-36b636bf1fca',
        '所属河流', 'waterId', '', 1000, '[{"key":"","value":""}]', '', 'monitor/water/getTree', 'waterName', 'id',
        'chirdList', 1, 11, 0, 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2023-06-13 14:04:02', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2023-06-14 10:36:27');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('7bef919d-43b0-44b4-bc38-e42baeb4f0d9', 'LIM_PointType_Lake', '06efc1d8-01c4-41a0-b653-879b030f8065',
        '水域功能代码', 'functionZoneCode', '', 900, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:16:02',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:37:08');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('8b6acc03-a12d-4e49-ba6e-4388007570b6', 'LIM_PointType_DXS', 'fb9de5be-f704-4631-8df8-4482c3f8dddf',
        '地下水类型', 'underWaterType', '', 100, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:21:24',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:38:39');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('8c92b2a7-da58-4999-9c5e-d7968d34e628', 'LIM_PointType_JTZS', 'd18d2122-ad54-47c9-a76c-d1e8a41501ec',
        '道路等级', 'rdLevel', '', 100, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:29:23',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:41:28');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('8eaad0ec-a14d-4c91-a3ee-7b450528681b', 'LIM_PointType_JTZS', '3c23f209-de5e-4c33-8398-d95d6dc7dcda',
        '路段长宽', 'railwayLength', '', 300, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:28:53',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:41:14');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('8f615dcd-723c-4cc8-96b2-75c86ef9b447', 'LIM_PointType_QYHJZS', '965aaa1c-3b60-4e6a-9887-d856d807a1fc',
        '网格边宽', 'gridWidth', '', 400, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:31:25',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:41:48');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('921b7934-5c92-4908-acd1-409ba90d8400', 'LIM_PointType_Air', 'b48ab82b-a8ec-4708-8863-4aa485802db8',
        '酸雨管测点级别', 'acidpl', '', 300, '[{"key":"","value":""}]', 'LIM_AcidRainLvl', '', '', '', '', 2, 4, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:23:27',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:39:19');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('9d4cb408-3e52-44e3-bfec-c36a31caef03', 'LIM_PointType_JTZS', '75a5409a-1074-43f1-bad1-b04ffb9c1a39',
        '路幅宽度', 'railwayWidth', '', 200, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:29:09',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:41:23');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('a1c84ec2-5458-46d2-af76-8be3cc2ffe71', 'LIM_PointType_WasteGas', '72dcf71d-52e7-480f-aa9e-365d1fda4831',
        '工艺设备名称', 'craftFacilityName', '', 400, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:15:04',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:24:15');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('a462d288-5d11-429d-a283-622f2d66e820', 'LIM_PointType_QYHJZS', '0fca2612-6e76-4555-8475-7ea313c4cee3',
        '网络覆盖人口', 'gridCoverPeoples', '', 100, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:32:03',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:42:05');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('a92725d2-ec1c-4c6a-9cdc-e978c1499cb7', 'LIM_PointType_River', 'dbe3d412-8bb8-4e40-a3a7-13c06cbd1fda',
        '网管断面级别', 'watersl', '', 800, '[{"key":"","value":""}]', 'LIM_NetSectionLevel', '', '', '', '', 2, 4, 0,
        0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-13 14:06:00',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:37:48');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('ab74aeed-fa4c-4d6f-9383-e044ea658ef8', 'LIM_PointType_GNQZS', 'c695d4e0-dc94-41f9-b9ec-63ad536e3731',
        '昼间结束时', 'dayhorend', '', 400, '[{"key":"","value":""}]', '', '', '', '', '', 0, 3, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:25:29',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:40:03');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('abeda2da-caae-4ebe-8109-e82ee08f5e3a', 'LIM_PointType_Air', '5f5e829b-989f-4327-9361-e91658b1797b',
        '大气网管测点级别', 'airpl', '', 200, '[{"key":"","value":""}]', 'LIM_AirNetLvl', '', '', '', '', 2, 4, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:24:07',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:39:25');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('b193834b-3a3e-41c0-9927-7319c5b72881', 'LIM_PointType_Air', 'ed3c1eb8-9538-499a-b79f-e12e12d5fce7',
        '二氧化硫区管测点级别', 'so2pl', '', 400, '[{"key":"","value":""}]', 'LIM_SO2Lvl', '', '', '', '', 2, 4, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:22:36',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:39:12');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('b1aa3f99-689f-4de7-bd4d-a25015c6a7c5', 'LIM_PointType_GNQZS', '9a740fa2-a360-4159-a8b8-515c4caf5632',
        '夜间起始时', 'nighthorbegin', '', 300, '[{"key":"","value":""}]', '', '', '', '', '', 0, 3, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:25:43',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:40:08');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('b2cef600-966e-49ed-acf1-cc5453c6a8b8', 'LIM_PointType_QYHJZS', '327c7214-2ce7-4173-8f40-62e52d66633b',
        '噪声声源代码', 'noiseSourceCode', '', 200, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:31:36',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:42:00');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('b779031e-8b15-4898-96a9-1149ee001576', 'LIM_PointType_Boiler', 'e4148bbc-cb74-45f8-a1dd-9e32ba131f70',
        '净化设备投运日期', 'purificateFacilityUseDate', '', 400, '[{"key":"","value":""}]', '', '', '', '', '', 0, 2,
        0, 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:19:56',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:19:56');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('b829b231-221d-4888-a772-fad7e5fea34d', 'LIM_PointType_River', '06efc1d8-01c4-41a0-b653-879b030f8065',
        '水域功能代码', 'functionZoneCode', '', 900, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-13 14:04:48',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:37:40');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('c30e9f6b-ba9c-42a3-8bbb-11af23672e17', 'LIM_PointType_GNQZS', '4c521b60-5e0f-459c-8b65-aad32ccfff3d',
        '夜间结束时', 'nighthorend', '', 200, '[{"key":"","value":""}]', '', '', '', '', '', 0, 3, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:25:57',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:40:14');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('c3bd8680-60fc-41cc-b2d6-f4f07582ffec', 'LIM_PointType_Lake', '1bc3f6df-3130-4f79-9b18-46ef9b508a4c',
        '交接断面管辖级别', 'rchal', '', 600, '[{"key":"","value":""}]', 'LIM_JoinSectionLevel', '', '', '', '', 2, 4,
        0, 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:18:40',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:37:27');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('c406f470-3d6a-47e8-b568-4d5286ae0954', 'LIM_PointType_Effluents', '3d404054-997a-42f5-8624-0effa1ef7ad3',
        '进出口', 'importAndExport', '', 200, '[{"key":"进口","value":"进口"},{"key":"出口","value":"出口"}]', '', '',
        '', '', '', 3, 4, 0, 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2023-07-14 13:13:57', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2023-07-14 13:24:00');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('c8f7cf65-d143-49c1-aab2-7257ce2894dd', 'LIM_PointType_GNQZS', '04b0aa02-f2cb-47d2-b182-16985170cc9d',
        '昼间起始时', 'dayhorbegin', '', 500, '[{"key":"","value":""}]', '', '', '', '', '', 0, 3, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:25:03',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:39:56');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('cae83575-1b02-48ff-a504-7f0fab16a9e6', 'LIM_PointType_Boiler', 'ca2ace02-1cdb-4bac-bc95-e38c1c622a8c',
        '锅炉制造单位', 'boilerMakeUnit', '', 1000, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:17:17',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:18:25');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('cbed8be2-f7a6-45b5-a9f3-47f3534afef2', 'LIM_PointType_QYHJZS', 'b3310c3a-e1ff-4910-ae72-0e030c998639',
        '网格边长', 'gridLength', '', 500, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:31:14',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:41:42');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('cc5b6944-a000-44c8-a5aa-3e8e32d227dd', 'LIM_PointType_Lake', 'dbe3d412-8bb8-4e40-a3a7-13c06cbd1fda',
        '网管断面级别', 'watersl', '', 800, '[{"key":"","value":""}]', 'LIM_NetSectionLevel', '', '', '', '', 2, 4, 0,
        0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:17:35',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:37:16');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('ccea21ef-4cab-4720-a7f0-66228cc9d2ab', 'LIM_PointType_Air', '0ebf7ffa-167f-4013-840f-76c56f146ea6',
        '测点空气质量报告级别', 'weekCalcu', '', 500, '[{"key":"","value":""}]', 'LIM_AireQualityLvl', '', '', '', '',
        2, 4, 0, 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:22:07',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:39:02');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('cd1e96b3-dbcc-4b38-9e61-5efdcfe7759d', 'LIM_PointType_Effluents', 'ea0ce619-f29d-4417-a7d3-b7d3f8c3b0e1',
        '排污去向', 'emissionFate', '', 100, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:14:15',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:24:03');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('d34af9bd-69c6-4d81-96fc-8b3fe0bdd020', 'LIM_PointType_Boiler', 'c9ef84e0-b466-48b7-a4a4-af6e60484895',
        '净化设备型号', 'purificateFacilityType', '', 500, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:19:39',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:23:52');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('e2a39304-a3a9-4bd2-b89d-99536b496c3b', 'LIM_PointType_YQKLW', '1a31c8f8-c81c-45ed-826c-c7812ddbc0f9',
        '净化设备名称', 'purificateFacilityName', '', 200, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:22:08',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:22:08');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('e4491bb7-a71d-41b8-872f-e3a4c06b8076', 'LIM_PointType_JTZS', 'f18aa23e-7ba9-4103-a7e6-c000d3fecff0',
        '路段名称', 'rdsecName', '', 500, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:26:50',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:40:45');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('e4f5ce9a-488d-4ea8-b411-2955a7bab8cb', 'LIM_PointType_Lake', '472846de-6b34-4d35-b6f0-d665fa8c9365',
        '所属湖库', 'waterId', '', 1000, '[{"key":"","value":""}]', '', 'monitor/water/getTree', 'waterName', 'id',
        'chirdList', 1, 11, 0, 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2023-06-14 10:15:34', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2023-06-14 10:36:57');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('e5ef1814-fec6-4bed-9837-50124bb420e1', 'LIM_PointType_River', '1bc3f6df-3130-4f79-9b18-46ef9b508a4c',
        '交接断面管辖级别', 'rchal', '', 600, '[{"key":"","value":""}]', 'LIM_JoinSectionLevel', '', '', '', '', 2, 4,
        0, 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-13 14:10:43',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:37:58');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('e7e80983-fbdc-4494-a90e-bb68b0ebf5fd', 'LIM_PointType_River', 'df906b33-2eeb-4138-ba3a-104381535d99',
        '域管断面级别', 'areasl', '', 700, '[{"key":"","value":""}]', 'LIM_DomainSectionLevel', '', '', '', '', 2, 4, 0,
        0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-13 14:07:41',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:38:02');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('e8a91b65-3685-4fb4-a701-bc81834d5c0b', 'LIM_PointType_Boiler', '4e286bad-4595-43fb-a748-be7b8fbc5d3f',
        '净化设备制造单位', 'purificateFacilityUnit', '', 600, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0,
        0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:19:14',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:19:14');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('e9e40c36-ee06-436b-9ef4-5fbe51a54bdf', 'LIM_PointType_Sediment', '29991d50-5fd5-4c69-b5a4-bb2d642157c6',
        '所属水体', 'waterId', '', 100, '[{"key":"","value":""}]', '', 'monitor/water/getTree', 'waterName', 'id',
        'chirdList', 1, 11, 0, 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2023-06-14 10:33:13', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2023-06-14 10:42:12');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('ee07edd0-f9a1-49cb-9a7d-1ed864db2d80', 'LIM_PointType_QYHJZS', '55ad5d79-ddf9-432d-9739-f72ca2123f79',
        '噪声功能区代码', 'noiseFunZoneCode', '', 300, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:31:46',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:41:56');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('eeb270b7-c866-4a1c-ab14-a2ffc96df8f2', 'LIM_PointType_WasteGas', '5146948c-fca9-4d82-b92e-5749217885ad',
        '排气管高度', 'exhaustPipeHeight', '', 500, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:14:49',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:24:31');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('f06e758a-a061-41e4-b81b-be762690f399', 'LIM_PointType_Air', 'a5dcef03-5fcd-421f-b9ac-9e23eb0938e3',
        '降水区管测点级别', 'acidp', '', 100, '[{"key":"","value":""}]', 'LIM_RainLvl', '', '', '', '', 2, 4, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:24:32',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-06-14 10:39:31');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('f71e108f-b559-4765-9d2f-baeb40c69e5b', 'LIM_PointType_WasteGas', '594941c7-694f-48b3-aadc-bfb30df30d9d',
        '污染源种类', 'pollutionType', '', 200, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:16:03',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:24:55');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('f9f1d79f-1c49-4c4c-95b2-4b9242cd1b7f', 'LIM_PointType_WasteGas', '75ceb21a-0797-4805-bb8a-8e0a2d881e79',
        '启用时间', 'craftFacilityUseDate', '', 100, '[{"key":"","value":""}]', '', '', '', '', '', 0, 2, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:16:53',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:25:00');
INSERT INTO "TB_MONITOR_POINTEXTENDCONFIG"("id", "pointType", "paramsId", "filedName", "filedAlias", "defaultValue",
                                           "orderNum", "dataSource", "codeDataSource", "dataSourceUrl", "urlReturnKey",
                                           "urlReturnValue", "treeChildFiled", "dataSourceType", "defaultControl",
                                           "requiredInd", "isDeleted", "orgId", "creator", "createDate", "domainId",
                                           "modifier", "modifyDate")
VALUES ('fc3515f1-25cd-4a7d-b92c-5d5a75fcaab2', 'LIM_PointType_Boiler', 'd71d867e-98c8-408e-994c-7c9e18af6ca2',
        '锅炉名称(型号)', 'equipmentTypeName', '', 900, '[{"key":"","value":""}]', '', '', '', '', '', 0, 1, 0, 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:17:35',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:18:32');

INSERT INTO "TB_MONITOR_POINTEXTENDDATA"("id", "pointType", "extendConfigId", "fixedPointId", "filedName", "filedAlias",
                                         "filedValue", "orderNum", "orgId", "creator", "createDate", "domainId",
                                         "modifier", "modifyDate")
VALUES ('1303ef3f-74fb-4e71-b261-73799609e16d', 'LIM_PointType_River', 'e5ef1814-fec6-4bed-9837-50124bb420e1',
        'e8a037ee-2461-4f4e-85b7-426a9e7d51e7', '交接断面管辖级别', 'rchal', null, 600,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:48:13',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:48:13');
INSERT INTO "TB_MONITOR_POINTEXTENDDATA"("id", "pointType", "extendConfigId", "fixedPointId", "filedName", "filedAlias",
                                         "filedValue", "orderNum", "orgId", "creator", "createDate", "domainId",
                                         "modifier", "modifyDate")
VALUES ('2e867ca0-732c-4ee2-b657-abc187ea53d9', 'LIM_PointType_River', '74a3944c-6e39-439f-9c4f-b81257f4ba29',
        'e8a037ee-2461-4f4e-85b7-426a9e7d51e7', '所属河流', 'waterId', null, 1000, '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:48:13', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:48:13');
INSERT INTO "TB_MONITOR_POINTEXTENDDATA"("id", "pointType", "extendConfigId", "fixedPointId", "filedName", "filedAlias",
                                         "filedValue", "orderNum", "orgId", "creator", "createDate", "domainId",
                                         "modifier", "modifyDate")
VALUES ('4c0523e0-1a26-4a55-8b5a-daf642cfe319', 'LIM_PointType_River', 'a92725d2-ec1c-4c6a-9cdc-e978c1499cb7',
        'e8a037ee-2461-4f4e-85b7-426a9e7d51e7', '网管断面级别', 'watersl', null, 800,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:48:13',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:48:13');
INSERT INTO "TB_MONITOR_POINTEXTENDDATA"("id", "pointType", "extendConfigId", "fixedPointId", "filedName", "filedAlias",
                                         "filedValue", "orderNum", "orgId", "creator", "createDate", "domainId",
                                         "modifier", "modifyDate")
VALUES ('4c9a5420-3691-466f-80fd-e551c3dfd83d', 'LIM_PointType_WasteGas', 'f9f1d79f-1c49-4c4c-95b2-4b9242cd1b7f',
        '4f80ef6b-5800-498e-bee6-a1133f8d763e', '启用时间', 'craftFacilityUseDate', null, 100,
        '5f7bcf90feb545968424b0a872863876', '46905a56-f770-42be-8d41-5b6c94f0ed52', '2023-09-15 18:53:16',
        '5f7bcf90feb545968424b0a872863876', '46905a56-f770-42be-8d41-5b6c94f0ed52', '2023-09-15 18:53:16');
INSERT INTO "TB_MONITOR_POINTEXTENDDATA"("id", "pointType", "extendConfigId", "fixedPointId", "filedName", "filedAlias",
                                         "filedValue", "orderNum", "orgId", "creator", "createDate", "domainId",
                                         "modifier", "modifyDate")
VALUES ('50d8a1ea-8543-4718-b19c-dcdee6b29c11', 'LIM_PointType_WasteGas', '6857567c-e198-4324-a7ac-187f672cd3ab',
        '4f80ef6b-5800-498e-bee6-a1133f8d763e', '净化设备名称', 'purificateFacilityName', null, 300,
        '5f7bcf90feb545968424b0a872863876', '46905a56-f770-42be-8d41-5b6c94f0ed52', '2023-09-15 18:53:16',
        '5f7bcf90feb545968424b0a872863876', '46905a56-f770-42be-8d41-5b6c94f0ed52', '2023-09-15 18:53:16');
INSERT INTO "TB_MONITOR_POINTEXTENDDATA"("id", "pointType", "extendConfigId", "fixedPointId", "filedName", "filedAlias",
                                         "filedValue", "orderNum", "orgId", "creator", "createDate", "domainId",
                                         "modifier", "modifyDate")
VALUES ('5bae3352-68c9-4d76-aeaf-fd8e2065ae26', 'LIM_PointType_WasteGas', 'f71e108f-b559-4765-9d2f-baeb40c69e5b',
        '4f80ef6b-5800-498e-bee6-a1133f8d763e', '污染源种类', 'pollutionType', null, 200,
        '5f7bcf90feb545968424b0a872863876', '46905a56-f770-42be-8d41-5b6c94f0ed52', '2023-09-15 18:53:16',
        '5f7bcf90feb545968424b0a872863876', '46905a56-f770-42be-8d41-5b6c94f0ed52', '2023-09-15 18:53:16');
INSERT INTO "TB_MONITOR_POINTEXTENDDATA"("id", "pointType", "extendConfigId", "fixedPointId", "filedName", "filedAlias",
                                         "filedValue", "orderNum", "orgId", "creator", "createDate", "domainId",
                                         "modifier", "modifyDate")
VALUES ('837b1211-9d9f-495f-bddd-8147c6f75f79', 'LIM_PointType_River', 'b829b231-221d-4888-a772-fad7e5fea34d',
        'e8a037ee-2461-4f4e-85b7-426a9e7d51e7', '水域功能代码', 'functionZoneCode', null, 900,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:48:13',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:48:13');
INSERT INTO "TB_MONITOR_POINTEXTENDDATA"("id", "pointType", "extendConfigId", "fixedPointId", "filedName", "filedAlias",
                                         "filedValue", "orderNum", "orgId", "creator", "createDate", "domainId",
                                         "modifier", "modifyDate")
VALUES ('a0ed29d6-2a21-427a-9a24-1298df22c1c9', 'LIM_PointType_Effluents', 'cd1e96b3-dbcc-4b38-9e61-5efdcfe7759d',
        'dce5ce60-c256-4061-a637-14c8a3fc9c7d', '排污去向', 'emissionFate', null, 100,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:43:23',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:43:23');
INSERT INTO "TB_MONITOR_POINTEXTENDDATA"("id", "pointType", "extendConfigId", "fixedPointId", "filedName", "filedAlias",
                                         "filedValue", "orderNum", "orgId", "creator", "createDate", "domainId",
                                         "modifier", "modifyDate")
VALUES ('b172e229-aca5-4d24-8363-d986daf3da75', 'LIM_PointType_WasteGas', 'a1c84ec2-5458-46d2-af76-8be3cc2ffe71',
        '4f80ef6b-5800-498e-bee6-a1133f8d763e', '工艺设备名称', 'craftFacilityName', null, 400,
        '5f7bcf90feb545968424b0a872863876', '46905a56-f770-42be-8d41-5b6c94f0ed52', '2023-09-15 18:53:16',
        '5f7bcf90feb545968424b0a872863876', '46905a56-f770-42be-8d41-5b6c94f0ed52', '2023-09-15 18:53:16');
INSERT INTO "TB_MONITOR_POINTEXTENDDATA"("id", "pointType", "extendConfigId", "fixedPointId", "filedName", "filedAlias",
                                         "filedValue", "orderNum", "orgId", "creator", "createDate", "domainId",
                                         "modifier", "modifyDate")
VALUES ('baaab1bc-b079-48e0-ad88-fcbe0e1368dc', 'LIM_PointType_WasteGas', 'eeb270b7-c866-4a1c-ab14-a2ffc96df8f2',
        '4f80ef6b-5800-498e-bee6-a1133f8d763e', '排气管高度', 'exhaustPipeHeight', null, 500,
        '5f7bcf90feb545968424b0a872863876', '46905a56-f770-42be-8d41-5b6c94f0ed52', '2023-09-15 18:53:16',
        '5f7bcf90feb545968424b0a872863876', '46905a56-f770-42be-8d41-5b6c94f0ed52', '2023-09-15 18:53:16');
INSERT INTO "TB_MONITOR_POINTEXTENDDATA"("id", "pointType", "extendConfigId", "fixedPointId", "filedName", "filedAlias",
                                         "filedValue", "orderNum", "orgId", "creator", "createDate", "domainId",
                                         "modifier", "modifyDate")
VALUES ('c143211c-0d3e-48f9-a50e-6513d53a1df2', 'LIM_PointType_River', 'e7e80983-fbdc-4494-a90e-bb68b0ebf5fd',
        'e8a037ee-2461-4f4e-85b7-426a9e7d51e7', '域管断面级别', 'areasl', null, 700, '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:48:13', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:48:13');
INSERT INTO "TB_MONITOR_POINTEXTENDDATA"("id", "pointType", "extendConfigId", "fixedPointId", "filedName", "filedAlias",
                                         "filedValue", "orderNum", "orgId", "creator", "createDate", "domainId",
                                         "modifier", "modifyDate")
VALUES ('f863f088-bddf-433b-81f4-3db2d0dce6c8', 'LIM_PointType_Effluents', 'c406f470-3d6a-47e8-b568-4d5286ae0954',
        'dce5ce60-c256-4061-a637-14c8a3fc9c7d', '进出口', 'importAndExport', null, 200,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:43:23',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:43:23');

INSERT INTO "TB_MONITOR_PROPERTY2POINT"("id", "fixedPointId", "propertyId", "pointCode")
VALUES ('0a016b95-57b5-4a85-b34a-975bc397bb81', 'e8a037ee-2461-4f4e-85b7-426a9e7d51e7',
        '86215bd8-bb62-4756-890c-969a9ca65272', null);
INSERT INTO "TB_MONITOR_PROPERTY2POINT"("id", "fixedPointId", "propertyId", "pointCode")
VALUES ('253621eb-746a-4602-918b-3100d996565b', 'e8a037ee-2461-4f4e-85b7-426a9e7d51e7',
        'a392d54b-799a-4c58-992b-3db895108f74', null);
INSERT INTO "TB_MONITOR_PROPERTY2POINT"("id", "fixedPointId", "propertyId", "pointCode")
VALUES ('5bc62b12-4340-4280-9e74-f66863d10ba3', 'e8a037ee-2461-4f4e-85b7-426a9e7d51e7',
        '7402b1ad-74b7-4a7a-970b-ab5fcd3bf847', null);
INSERT INTO "TB_MONITOR_PROPERTY2POINT"("id", "fixedPointId", "propertyId", "pointCode")
VALUES ('802131cb-596a-480a-86bb-454ad20ca210', 'e8a037ee-2461-4f4e-85b7-426a9e7d51e7',
        'cb515eab-3880-414a-8372-19cbe33da6b5', null);
INSERT INTO "TB_MONITOR_PROPERTY2POINT"("id", "fixedPointId", "propertyId", "pointCode")
VALUES ('ee5f17ad-7713-4914-b0d5-f1c628499c63', 'e8a037ee-2461-4f4e-85b7-426a9e7d51e7',
        '906019c7-2fc1-4c2d-9ed9-bf6b12ef39b2', null);
INSERT INTO "TB_MONITOR_PROPERTY2POINT"("id", "fixedPointId", "propertyId", "pointCode")
VALUES ('f41adfb5-ab40-4e87-a1b6-e07b5d22e402', 'e8a037ee-2461-4f4e-85b7-426a9e7d51e7',
        'fe64c1de-b346-4b8c-9592-96c9b8054153', null);

INSERT INTO "TB_MONITOR_PROPERTYPOINT2TEST"("id", "propertyPointId", "testId", "samplePeriod", "timesOrder")
VALUES ('1982d97b-c8e5-42bb-a390-71edccde8d87', '802131cb-596a-480a-86bb-454ad20ca210',
        '076f8d12-62b2-42d0-8e66-7a428446ced1', 1, 1);
INSERT INTO "TB_MONITOR_PROPERTYPOINT2TEST"("id", "propertyPointId", "testId", "samplePeriod", "timesOrder")
VALUES ('1cc89afa-dd66-4112-836d-ad6c28268c90', '5bc62b12-4340-4280-9e74-f66863d10ba3',
        '076f8d12-62b2-42d0-8e66-7a428446ced1', 1, 1);
INSERT INTO "TB_MONITOR_PROPERTYPOINT2TEST"("id", "propertyPointId", "testId", "samplePeriod", "timesOrder")
VALUES ('1ebf95b3-c546-4679-8e3a-6cd16f3881a2', '0a016b95-57b5-4a85-b34a-975bc397bb81',
        '076f8d12-62b2-42d0-8e66-7a428446ced1', 4, 4);
INSERT INTO "TB_MONITOR_PROPERTYPOINT2TEST"("id", "propertyPointId", "testId", "samplePeriod", "timesOrder")
VALUES ('5505d8a8-bb8a-43c4-ae08-ffe7b816756e', '0a016b95-57b5-4a85-b34a-975bc397bb81',
        '6CD54881-9A72-ABCD-B2F9-01C5118A1576', 1, 1);
INSERT INTO "TB_MONITOR_PROPERTYPOINT2TEST"("id", "propertyPointId", "testId", "samplePeriod", "timesOrder")
VALUES ('69dcc3d2-6685-4a8f-8768-0631a975a367', 'f41adfb5-ab40-4e87-a1b6-e07b5d22e402',
        '076f8d12-62b2-42d0-8e66-7a428446ced1', 1, 1);
INSERT INTO "TB_MONITOR_PROPERTYPOINT2TEST"("id", "propertyPointId", "testId", "samplePeriod", "timesOrder")
VALUES ('6c50cc3b-2922-4894-a896-e659fd8bb73f', 'f41adfb5-ab40-4e87-a1b6-e07b5d22e402',
        '6CD54881-9A72-ABCD-B2F9-01C5118A1576', 1, 1);
INSERT INTO "TB_MONITOR_PROPERTYPOINT2TEST"("id", "propertyPointId", "testId", "samplePeriod", "timesOrder")
VALUES ('795ddedd-d1ca-4155-9fb1-00ed794db9d7', '253621eb-746a-4602-918b-3100d996565b',
        '6CD54881-9A72-ABCD-B2F9-01C5118A1576', 1, 1);
INSERT INTO "TB_MONITOR_PROPERTYPOINT2TEST"("id", "propertyPointId", "testId", "samplePeriod", "timesOrder")
VALUES ('81f6cab5-9035-4f9a-a5c6-2278a5f698c4', 'ee5f17ad-7713-4914-b0d5-f1c628499c63',
        '076f8d12-62b2-42d0-8e66-7a428446ced1', 1, 1);
INSERT INTO "TB_MONITOR_PROPERTYPOINT2TEST"("id", "propertyPointId", "testId", "samplePeriod", "timesOrder")
VALUES ('92fb1171-b51d-43f7-90a4-31044e62fc85', '5bc62b12-4340-4280-9e74-f66863d10ba3',
        '6CD54881-9A72-ABCD-B2F9-01C5118A1576', 1, 1);
INSERT INTO "TB_MONITOR_PROPERTYPOINT2TEST"("id", "propertyPointId", "testId", "samplePeriod", "timesOrder")
VALUES ('a1e2f792-aa6d-432c-bc8a-e0f73dc190e4', 'ee5f17ad-7713-4914-b0d5-f1c628499c63',
        '6CD54881-9A72-ABCD-B2F9-01C5118A1576', 1, 1);
INSERT INTO "TB_MONITOR_PROPERTYPOINT2TEST"("id", "propertyPointId", "testId", "samplePeriod", "timesOrder")
VALUES ('c37de949-72d2-4b48-8f28-d2b44d291ee5', '253621eb-746a-4602-918b-3100d996565b',
        '076f8d12-62b2-42d0-8e66-7a428446ced1', 1, 1);
INSERT INTO "TB_MONITOR_PROPERTYPOINT2TEST"("id", "propertyPointId", "testId", "samplePeriod", "timesOrder")
VALUES ('e009de0e-6d51-4407-b198-df06fd935589', '802131cb-596a-480a-86bb-454ad20ca210',
        '6CD54881-9A72-ABCD-B2F9-01C5118A1576', 1, 1);

INSERT INTO "TB_MONITOR_STATION"("id", "stcode", "stname", "staddress", "isEndable", "remark", "orderNum", "entId",
                                 "isDeleted", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate")
VALUES ('3bb943ff-eccc-44dd-8c5e-39141960a22d', '', '讯飞智元', '', 1, '', 0, '5f7bcf90feb545968424b0a872863876', 0,
        '5f7bcf90feb545968424b0a872863876', '46905a56-f770-42be-8d41-5b6c94f0ed52', '2023-09-15 18:52:36',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-07-04 08:27:59');
INSERT INTO "TB_MONITOR_STATION"("id", "stcode", "stname", "staddress", "isEndable", "remark", "orderNum", "entId",
                                 "isDeleted", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate")
VALUES ('d861a79f-3d11-41c0-b650-d4e93e7c2f7b', '', '讯飞空气监测点', '', 1, '', 0, '5f7bcf90feb545968424b0a872863876',
        0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-05-10 16:44:16',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-07-04 08:28:38');

ALTER TABLE "TB_MONITOR_FIXEDPOINT"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_MONITOR_FIXEDPOINT2POINT"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_MONITOR_FIXEDPOINT2TEST"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_MONITOR_FIXEDPOINTEXPEND"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_MONITOR_FIXEDPOINTPROPERTY"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_MONITOR_FIXEDPOINTSORT"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_MONITOR_FIXEDPOINTSORTDETIL"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_MONITOR_OICINFORMATION"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_MONITOR_OICINFORMATION2TEST"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_MONITOR_POINTEXTENDCONFIG"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_MONITOR_POINTEXTENDDATA"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_MONITOR_PROPERTY2POINT"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_MONITOR_PROPERTYPOINT2TEST"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_MONITOR_STATION"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_MONITOR_WATER"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_MONITOR_WATEREXPAND"
    ADD CONSTRAINT PRIMARY KEY ("id");

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."stationId" IS '测站id';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."stationName" IS '测站名称';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."pointName" IS '点位名称';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."isEnabled" IS '是否启用';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."evaluationId" IS '评价标准';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."evaluationLevelId" IS '评价等级';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."remark" IS '备注';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."cycleOrder" IS '周期';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."timesOrder" IS '次数';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."internalCode" IS '内部编码';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."pointCode" IS '点位编号';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."level" IS '等级 ： 常量';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."lon" IS '经度';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."lat" IS '纬度';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."pointType" IS '类型（枚举：环境质量 1 污染源 2）';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."folderType" IS '点位类型（常量），多个类型以逗号隔开';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."enterpriseId" IS '所属企业';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."villageCode" IS '地址';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."isDeleted" IS '假删';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."examArea" IS '考核区域';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT"."areaId" IS '所在地区Id（Guid）（lims）';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT2POINT"."id" IS '主键';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT2POINT"."fixedPointId" IS '点位id';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT2POINT"."pointId" IS '关联点位Id';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT2TEST"."id" IS '主键';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT2TEST"."fixedPointId" IS '点位id';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT2TEST"."testId" IS '测试项目id';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT2TEST"."timesOrder" IS '频次';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINT2TEST"."samplePeriod" IS '样次';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."id" IS '主键';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."fixedPointId" IS '点位id';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."waterId" IS '水体、河流id';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."functionZoneCode" IS '水域功能代码';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."watersl" IS '网管断面级别';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."areasl" IS '域管断面级别';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."rchal" IS '交接断面管辖级别';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."ygwq" IS '月取水量';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."dayhorbegin" IS '功能区噪声_昼间起始时';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."dayhorend" IS '功能区噪声_昼间结束时';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."nighthorbegin" IS '功能区噪声_夜间起始时';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."nighthorend" IS '功能区噪声_夜间结束时';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."refer" IS '测点参照物';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."gridLength" IS '区域噪声_网格边长';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."gridWidth" IS '区域噪声_网格边宽';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."noiseSourceCode" IS '区域噪声_噪声声源代码';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."noiseFunZoneCode" IS '区域噪声_噪声功能区代码';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."gridCoverPeoples" IS '区域噪声_网格覆盖人口';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."rdsecName" IS '道路噪声_路段名称';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."rdsecfromto" IS '道路噪声_路段起始点';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."railwayLength" IS '道路噪声_路段长度';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."railwayWidth" IS '道路噪声_路幅长度';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."rdLevel" IS '道路噪声_道路等级';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."weekCalcu" IS '大气_测点空气质量报告级别';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."so2pl" IS '大气_二氧化硫区管测点级别';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."acidpl" IS '大气_酸雨区管测点级别';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."airpl" IS '大气_大气网管测点级别';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."acidp" IS '大气_降水网管测点级别';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."underWaterTypeCode" IS '地下水_地下水类型代码';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."underWaterType" IS '地下水_地下水类型';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."exhaustPipeHeight" IS '排气管高度';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."craftFacilityName" IS '工艺设备名称';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."purificateFacilityName" IS '净化设备名称';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."pollutionType" IS '污染源种类';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."craftFacilityUseDate" IS '工艺设备/启用时间';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."boilerMakeUnit" IS '锅炉制造单位';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."equipmentTypeName" IS '名称(型号)';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."boilerUseDate" IS '锅炉投运日期';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."chimneyHeight" IS '烟囱高度';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."purificateFacilityUnit" IS '净化设备制造单位';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."purificateFacilityType" IS '净化设备型号';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."purificateFacilityUseDate" IS '净化设备投运日期';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."fuelType" IS '燃料类型';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."stoveFacilityType" IS '炉窖设备型号';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."stoveFacilityCode" IS '炉窖设备编号';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."emissionFate" IS '排放去向';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."importAndExport" IS '进出口';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTEXPEND"."waterworks" IS '饮用水-所属水厂';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTPROPERTY"."id" IS '主键';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTPROPERTY"."parentId" IS '关联id';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTPROPERTY"."propertyName" IS '监测计划名称';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTPROPERTY"."orderNum" IS '排序值';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTPROPERTY"."year" IS '年份';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTPROPERTY"."month" IS '月份';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTPROPERTY"."cycleOrder" IS '周期';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTPROPERTY"."timesOrder" IS '次数';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTPROPERTY"."sampleTypeId" IS '样品类型';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTPROPERTY"."pointType" IS '点位类型：常量（河流、湖库、饮用水、功能区噪声、区域环境噪声、交通噪声、底泥、大气、地下水）';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTPROPERTY"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTPROPERTY"."isDeleted" IS '假删';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTPROPERTY"."remark" IS '备注';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTPROPERTY"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTPROPERTY"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTPROPERTY"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTPROPERTY"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTPROPERTY"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTSORT"."id" IS '主键';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTSORT"."sortName" IS '排序名称';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTSORT"."orderNum" IS '排序值';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTSORT"."isDeleted" IS '假删';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTSORT"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTSORT"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTSORT"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTSORT"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTSORT"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTSORTDETIL"."sortId" IS '排序id';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTSORTDETIL"."fixedPointId" IS '点位id';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTSORTDETIL"."orderNum" IS '排序值';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTSORTDETIL"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTSORTDETIL"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTSORTDETIL"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTSORTDETIL"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTSORTDETIL"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_MONITOR_FIXEDPOINTSORTDETIL"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_MONITOR_OICINFORMATION"."id" IS '主键';

COMMENT
ON COLUMN "TB_MONITOR_OICINFORMATION"."fixedPointId" IS '所属点位';

COMMENT
ON COLUMN "TB_MONITOR_OICINFORMATION"."instrumentName" IS '仪器名称';

COMMENT
ON COLUMN "TB_MONITOR_OICINFORMATION"."instrumentModel" IS '仪器型号';

COMMENT
ON COLUMN "TB_MONITOR_OICINFORMATION"."instrumentCode" IS '出厂编号';

COMMENT
ON COLUMN "TB_MONITOR_OICINFORMATION"."range" IS '量程';

COMMENT
ON COLUMN "TB_MONITOR_OICINFORMATION"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_MONITOR_OICINFORMATION"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_MONITOR_OICINFORMATION"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_MONITOR_OICINFORMATION"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_MONITOR_OICINFORMATION"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_MONITOR_OICINFORMATION"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_MONITOR_OICINFORMATION2TEST"."id" IS '主键';

COMMENT
ON COLUMN "TB_MONITOR_OICINFORMATION2TEST"."oicId" IS '在线仪器id';

COMMENT
ON COLUMN "TB_MONITOR_OICINFORMATION2TEST"."testId" IS '测试项目id';

COMMENT
ON TABLE "TB_MONITOR_POINTEXTENDCONFIG" IS '点位类型拓展字段配置表';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDCONFIG"."id" IS '标识';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDCONFIG"."pointType" IS '点位类型(常量控制)';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDCONFIG"."paramsId" IS '参数Id';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDCONFIG"."filedName" IS '拓展字段名称';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDCONFIG"."filedAlias" IS '拓展字段别名';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDCONFIG"."defaultValue" IS '默认值';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDCONFIG"."orderNum" IS '排序值';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDCONFIG"."dataSource" IS '数据源';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDCONFIG"."codeDataSource" IS '常量数据源(常量类型)';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDCONFIG"."dataSourceUrl" IS '数据源请求接口';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDCONFIG"."urlReturnKey" IS 'Key对应字段';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDCONFIG"."urlReturnValue" IS 'Value对应字段';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDCONFIG"."treeChildFiled" IS '下拉树对应子数据字段';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDCONFIG"."dataSourceType" IS '下拉框数据源类型（枚举EnumControlDataSourceType:0.无 1.接口请求 2.常量数据源 3.自定义数据源）';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDCONFIG"."defaultControl" IS '默认控件（枚举EnumDefaultControl:1.文本控件 2.日期控件 3.数字控件 4.下拉框控件 5.RadioGroup控件 6.CheckBoxGroup控件 7.日期时间控件 8.文本区域控件 9.时间控件）';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDCONFIG"."requiredInd" IS '是否必填';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDCONFIG"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDCONFIG"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDCONFIG"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDCONFIG"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDCONFIG"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDCONFIG"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDCONFIG"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_MONITOR_POINTEXTENDDATA" IS '点位拓展字段数据表';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDDATA"."id" IS '标识';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDDATA"."pointType" IS '点位类型(常量控制)';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDDATA"."extendConfigId" IS '点位拓展配置Id';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDDATA"."fixedPointId" IS '点位Id';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDDATA"."filedName" IS '拓展字段名称';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDDATA"."filedAlias" IS '拓展字段别名';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDDATA"."filedValue" IS '拓展字段值';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDDATA"."orderNum" IS '排序值';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDDATA"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDDATA"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDDATA"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDDATA"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDDATA"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_MONITOR_POINTEXTENDDATA"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_MONITOR_PROPERTY2POINT"."id" IS '主键';

COMMENT
ON COLUMN "TB_MONITOR_PROPERTY2POINT"."fixedPointId" IS '点位id';

COMMENT
ON COLUMN "TB_MONITOR_PROPERTY2POINT"."propertyId" IS '监测计划id';

COMMENT
ON COLUMN "TB_MONITOR_PROPERTY2POINT"."pointCode" IS '监测计划上关联的点位编号';

COMMENT
ON COLUMN "TB_MONITOR_PROPERTYPOINT2TEST"."id" IS '主键';

COMMENT
ON COLUMN "TB_MONITOR_PROPERTYPOINT2TEST"."propertyPointId" IS '检测计划和点位关联id';

COMMENT
ON COLUMN "TB_MONITOR_PROPERTYPOINT2TEST"."testId" IS '测试项目id';

COMMENT
ON COLUMN "TB_MONITOR_PROPERTYPOINT2TEST"."samplePeriod" IS '样次';

COMMENT
ON COLUMN "TB_MONITOR_PROPERTYPOINT2TEST"."timesOrder" IS '批次';

COMMENT
ON COLUMN "TB_MONITOR_STATION"."id" IS '主键';

COMMENT
ON COLUMN "TB_MONITOR_STATION"."stcode" IS '测站代码';

COMMENT
ON COLUMN "TB_MONITOR_STATION"."stname" IS '测站名称';

COMMENT
ON COLUMN "TB_MONITOR_STATION"."staddress" IS '测站位置';

COMMENT
ON COLUMN "TB_MONITOR_STATION"."isEndable" IS '是否启用';

COMMENT
ON COLUMN "TB_MONITOR_STATION"."remark" IS '备注';

COMMENT
ON COLUMN "TB_MONITOR_STATION"."orderNum" IS '排序值';

COMMENT
ON COLUMN "TB_MONITOR_STATION"."entId" IS '所属单位';

COMMENT
ON COLUMN "TB_MONITOR_STATION"."isDeleted" IS '假删';

COMMENT
ON COLUMN "TB_MONITOR_STATION"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_MONITOR_STATION"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_MONITOR_STATION"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_MONITOR_STATION"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_MONITOR_STATION"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_MONITOR_STATION"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_MONITOR_WATER"."id" IS '主键';

COMMENT
ON COLUMN "TB_MONITOR_WATER"."parentId" IS '关联id';

COMMENT
ON COLUMN "TB_MONITOR_WATER"."waterName" IS '水体名称';

COMMENT
ON COLUMN "TB_MONITOR_WATER"."waterCode" IS '水体编码';

COMMENT
ON COLUMN "TB_MONITOR_WATER"."waterType" IS '水体类型：常量（河流、饮用水水厂、水系、水功能区、应用水源地、流域、湖库）';

COMMENT
ON COLUMN "TB_MONITOR_WATER"."isEnabled" IS '是否启用';

COMMENT
ON COLUMN "TB_MONITOR_WATER"."remark" IS '备注';

COMMENT
ON COLUMN "TB_MONITOR_WATER"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_MONITOR_WATER"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_MONITOR_WATER"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_MONITOR_WATER"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_MONITOR_WATER"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_MONITOR_WATER"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_MONITOR_WATEREXPAND"."id" IS '主键';

COMMENT
ON COLUMN "TB_MONITOR_WATEREXPAND"."waterId" IS '水体id';

COMMENT
ON COLUMN "TB_MONITOR_WATEREXPAND"."waterFunctionZoneType" IS '水功能区类型';

COMMENT
ON COLUMN "TB_MONITOR_WATEREXPAND"."startPlaceName" IS '起始位置';

COMMENT
ON COLUMN "TB_MONITOR_WATEREXPAND"."netWaterLevel" IS '网管湖库级别';

COMMENT
ON COLUMN "TB_MONITOR_WATEREXPAND"."areaWaterLevel" IS '域管湖库级别';

COMMENT
ON COLUMN "TB_MONITOR_WATEREXPAND"."lakesTypeCode" IS '湖库类型代码';

COMMENT
ON COLUMN "TB_MONITOR_WATEREXPAND"."waterl" IS '网管河流级别';

COMMENT
ON COLUMN "TB_MONITOR_WATEREXPAND"."awaterl" IS '域管河流级别';

COMMENT
ON COLUMN "TB_MONITOR_WATEREXPAND"."locationName" IS '水厂所在地名称';

COMMENT
ON COLUMN "TB_MONITOR_WATEREXPAND"."endPlaceName" IS '终止位置';

COMMENT
ON COLUMN "TB_MONITOR_WATEREXPAND"."waterFunctionZoneLen" IS '水功能区长度';

COMMENT
ON COLUMN "TB_MONITOR_WATEREXPAND"."yswq" IS '年供水量';

COMMENT
ON COLUMN "TB_MONITOR_WATEREXPAND"."remark" IS '备注';

COMMENT
ON COLUMN "TB_MONITOR_WATEREXPAND"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_MONITOR_WATEREXPAND"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_MONITOR_WATEREXPAND"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_MONITOR_WATEREXPAND"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_MONITOR_WATEREXPAND"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_MONITOR_WATEREXPAND"."modifyDate" IS '修改时间';

