package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 分析数据剔除操作
 * <AUTHOR>
 * @version V1.0.0 2019/12/02
 * @since V100R001
 */
@Data
public class DtoAnalyseRemove {

    /**
     * 检测单id
     */
    private String workSheetFolderId;

    /**
     * 分析数据ids
     */
    private List<String> analyseDataIds = new ArrayList<>();

    /**
     * 选定数据行集合
     */
    private List<Map<String, Object>> analyseDatas;
}
