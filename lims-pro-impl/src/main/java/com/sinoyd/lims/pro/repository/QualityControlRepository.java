package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoQualityControl;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;


/**
 * QualityControl数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
public interface QualityControlRepository extends IBaseJpaRepository<DtoQualityControl, String> {
    /**
     * 按质控ids查询相应的信息
     *
     * @param ids 质控id主键
     * @return 返回相应的质控数据
     */
    @Query("select s from DtoQualityControl s where s.isDeleted = 0 and s.id in :ids")
    List<DtoQualityControl> findByIds(@Param("ids") List<String> ids);

    /**
     * 按照关联样品id获取质控信息
     * @param sampleId 关联样品id
     * @return 质控信息
     */
    List<DtoQualityControl> findByAssociateSampleIdIn(List<String> sampleId);
}