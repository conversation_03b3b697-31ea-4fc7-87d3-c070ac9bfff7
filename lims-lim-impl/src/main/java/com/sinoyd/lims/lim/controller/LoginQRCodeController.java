package com.sinoyd.lims.lim.controller;


import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.lim.dto.rcc.DtoVersionInfo;
import com.sinoyd.lims.lim.service.LoginQRCodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
* 登陆页二维码接口定义
* <AUTHOR>
* @version V1.0.0 2023/7/19
* @since V100R001
*/
@Api(tags = "登陆页二维码")
@RestController
@RequestMapping("/api/lim/loginQRCode")
public class LoginQRCodeController extends ExceptionHandlerController<LoginQRCodeService>{

    @ApiOperation(value = "获取最新二维码信息", notes = "获取最新二维码信息")
    @GetMapping("/versionInfo")
    public RestResponse<String> findNewVersionInfo() {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setData(service.findNewVersionInfo());
        return restResp;
    }

    @ApiOperation(value = "是否启用登录验证码", notes = "是否启用登录验证码")
    @GetMapping("/captcha")
    public RestResponse<String> getCaptchaConfig() {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setData(service.getCaptchaConfig());
        return restResp;
    }
}
