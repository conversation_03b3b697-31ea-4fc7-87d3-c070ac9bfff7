package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.ItemRelationParams;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import java.util.List;


/**
 * DtoItemRelationParams实体
 * <AUTHOR>
 * @version V1.0.0 2023/11/1
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_ItemRelationParams")
 @Data
 @DynamicInsert
 public  class DtoItemRelationParams extends ItemRelationParams {
    private static final long serialVersionUID = 1L;

    /**
     * 分析项目ids
     */
    @Transient
    private List<String> analyzeItemIds;
}