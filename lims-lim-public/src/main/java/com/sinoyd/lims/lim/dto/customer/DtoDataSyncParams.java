package com.sinoyd.lims.lim.dto.customer;

import lombok.Data;

import java.util.List;

/**
 * 数据同步传参实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/4/24
 */
@Data
public class DtoDataSyncParams {

    /**
     * 导入数据源redisKey
     */
    private String dataSourceRedisKey;

    /**
     * 导入数据临时保存redisKey
     */
    private String importDataTempRedisKey;

    /**
     * 检测类型绑定
     */
    private List<DtoBaseData> sampleTypeBinds;

    /**
     * 替代物绑定
     */
    private List<DtoBaseData> substituteBinds;

    /**
     * 导入前检查数据集合
     */
    private List<DtoImportCheck> importChecks;
}