package com.sinoyd.lims.monitor.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;


/**
 * FixedPointProperty实体
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="FixedPointProperty")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class FixedPointProperty extends LimsBaseEntity {

   private static final long serialVersionUID = 1L;

    public  FixedPointProperty() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
       this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 关联id
    */
    @Column(length=50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("关联id")
	private String parentId;
    
    /**
    * 年份
    */
    @Column(nullable=false)
    @ApiModelProperty("年份")
	private Integer year;
    
    /**
    * 监测计划名称
    */
    @ApiModelProperty("监测计划名称")
    @Length(message = "监测计划名称{validation.message.length}", max = 255)
	private String propertyName;
    
    /**
    * 排序值
    */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("排序值")
	private Integer orderNum;
    
    /**
    * 月份
    */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("月份")
	private Integer month;
    
    /**
    * 周期
    */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("周期")
	private Integer cycleOrder;
    
    /**
    * 次数
    */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("次数")
	private Integer timesOrder;
    
    /**
    * 样品类型
    */
    @Column(length=50)
    @ApiModelProperty("样品类型")
	private String sampleTypeId;
    
    /**
    * 点位类型：常量（河流、湖库、饮用水、功能区噪声、区域环境噪声、交通噪声、底泥、大气、地下水）
    */
    @Column(length=50)
    @ApiModelProperty("点位类型：常量（河流、湖库、饮用水、功能区噪声、区域环境噪声、交通噪声、底泥、大气、地下水）")
    @Length(message = "点位类型：常量（河流、湖库、饮用水、功能区噪声、区域环境噪声、交通噪声、底泥、大气、地下水）{validation.message.length}", max = 50)
	private String pointType;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
    /**
    * 假删
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
	private Boolean isDeleted=false;
    
    /**
    * 备注
    */
    @Column(length=2000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 2000)
	private String remark;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
 }