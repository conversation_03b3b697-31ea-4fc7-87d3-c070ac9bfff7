package com.sinoyd.lims.pro.controller;

import com.sinoyd.base.dto.customer.DtoComplexQuery;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.dto.customer.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.ReceiveSubSampleRecordService;
import com.sinoyd.lims.pro.criteria.ReceiveSubSampleRecordCriteria;
import com.sinoyd.lims.pro.dto.DtoReceiveSubSampleRecord;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * ReceiveSubSampleRecord服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: ReceiveSubSampleRecord服务")
 @RestController
 @RequestMapping("api/pro/receiveSubSampleRecord")
 public class ReceiveSubSampleRecordController extends BaseJpaController<DtoReceiveSubSampleRecord, String,ReceiveSubSampleRecordService> {

    /**
     * 分页动态条件查询样品分配数据
     *
     * @param receiveSubSampleRecordCriteria 条件参数
     * @return RestResponse<List < R e ceiveSubSampleRecord>>
     */
    @ApiOperation(value = "分页动态条件查询样品分配数据", notes = "分页动态条件查询样品分配数据")
    @GetMapping(path = "/assign")
    public RestResponse<List<DtoReceiveSubSampleRecord>> findAssignByPage(ReceiveSubSampleRecordCriteria receiveSubSampleRecordCriteria) {
        PageBean<DtoReceiveSubSampleRecord> pageBean = super.getPageBean();
        RestResponse<List<DtoReceiveSubSampleRecord>> restResponse = new RestResponse<>();
        service.findAssignByPage(pageBean, receiveSubSampleRecordCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 根据送样单id及类型获取领样单
     *
     * @param receiveId 送样单id
     * @param type      领样单类型
     * @return RestResponse<DtoReceiveSubSampleRecord>
     */
    @ApiOperation(value = "根据现场领样单id获取现场指标", notes = "根据现场领样单id获取现场指标")
    @GetMapping("/{receiveId}/{type}")
    public RestResponse<DtoReceiveSubSampleRecord> findByReceiveIdAndType(@PathVariable(name = "receiveId") String receiveId,
                                                                          @PathVariable(name = "type") String type) {
        RestResponse<DtoReceiveSubSampleRecord> restResponse = new RestResponse<>();
        restResponse.setData(service.findByReceiveIdAndType(receiveId, type));
        return restResponse;
    }

    /**
     * 更换公式
     *
     * @param dtoAnalyseDataChangeFormula 传输
     * @return RestResponse<DtoLocalDataProperty>
     */
    @ApiOperation(value = "更换公式", notes = "更换公式")
    @PostMapping("/formulaChange")
    public RestResponse<DtoLocalDataProperty> changeAnalyseDataFormula(@RequestBody DtoAnalyseDataChangeFormula dtoAnalyseDataChangeFormula) {
        RestResponse<DtoLocalDataProperty> restResponse = new RestResponse<>();
        DtoLocalDataProperty localDataProperty = service.changeAnalyseDataFormula(dtoAnalyseDataChangeFormula);
        restResponse.setData(localDataProperty);
        return restResponse;
    }

    /**
     * 根据现场领样单id获取现场数据
     *
     * @param subId 现场领样单id
     * @return RestResponse<List < D t o LocalDataProperty>>
     */
    @ApiOperation(value = "根据现场领样单id获取现场数据", notes = "根据现场领样单id获取现场数据")
    @GetMapping("/localDataOrder")
    public RestResponse<List<DtoLocalDataProperty>> findLocalDataOrder(@RequestParam(name = "subId") String subId,
                                                                  @RequestParam(name = "module") String module,
                                                                  @RequestParam(name = "type") Integer type,
                                                                  @RequestParam(name = "sortId") String sortId, @RequestParam(name = "key") String key) {
        RestResponse<List<DtoLocalDataProperty>> restResponse = new RestResponse<>();
        List<DtoLocalDataProperty> dataList = service.findLocalData(subId, module, type, sortId, key);
        restResponse.setData(dataList);
        restResponse.setRestStatus(dataList.size() > 0 ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        return restResponse;
    }

    /**
     * 根据现场领样单id获取现场数据
     *
     * @param subId 现场领样单id
     * @return RestResponse<List < D t o LocalDataProperty>>
     */
    @ApiOperation(value = "根据现场领样单id获取现场数据", notes = "根据现场领样单id获取现场数据")
    @GetMapping("/localDataOrder/v2")
    public RestResponse<List<DtoLocalDataProperty>> findLocalDataOrderNew(@RequestParam(name = "subId") String subId,
                                                                       @RequestParam(name = "module") String module,
                                                                       @RequestParam(name = "type") Integer type,
                                                                       @RequestParam(name = "sortId") String sortId, @RequestParam(name = "key") String key) {
        RestResponse<List<DtoLocalDataProperty>> restResponse = new RestResponse<>();
        List<DtoLocalDataProperty> dataList = service.findLocalDataOrderNew(subId, module, type, sortId, key);
        restResponse.setData(dataList);
        restResponse.setRestStatus(dataList.size() > 0 ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        return restResponse;
    }

    /**
     * 根据现场领样单id获取现场数据
     *
     * @param subId 现场领样单id
     * @return RestResponse<List <  D t o LocalDataProperty>>
     */
    @ApiOperation(value = "根据现场领样单id获取现场数据", notes = "根据现场领样单id获取现场数据")
    @GetMapping("/localData")
    public RestResponse<List<DtoLocalDataProperty>> findLocalData(@RequestParam(name = "subId") String subId,
                                                                  @RequestParam(name = "module") String module,
                                                                  @RequestParam(name = "type") Integer type) {
        RestResponse<List<DtoLocalDataProperty>> restResponse = new RestResponse<>();
        List<DtoLocalDataProperty> dataList = service.findLocalData(subId, module, type, UUIDHelper.GUID_EMPTY, "");
        restResponse.setData(dataList);
        restResponse.setRestStatus(dataList.size() > 0 ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        return restResponse;
    }

    /**
     * 根据现场领样单id获取现场指标
     *
     * @param subId 现场领样单id
     * @return RestResponse<L t <  D t o Test>>
     */
    @ApiOperation(value = "根据现场领样单id获取现场指标", notes = "根据现场领样单id获取现场指标")
    @GetMapping("/localTests")
    public RestResponse<List<DtoTest>> findLocalTests(@RequestParam(name = "subId") String subId) {
        RestResponse<List<DtoTest>> restResponse = new RestResponse<>();
        List<DtoTest> testList = service.findLocalTest(subId);
        restResponse.setData(testList);
        restResponse.setRestStatus(testList.size() > 0 ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        return restResponse;
    }

    /**
     * 按主键查询ReceiveSubSampleRecord
     *
     * @param id 主键id
     * @return RestResponse<DtoReceiveSubSampleRecord>
     */
    @ApiOperation(value = "按主键查询ReceiveSubSampleRecord", notes = "按主键查询ReceiveSubSampleRecord")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoReceiveSubSampleRecord> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoReceiveSubSampleRecord> restResponse = new RestResponse<>();
        DtoReceiveSubSampleRecord receiveSubSampleRecord = service.findOne(id);
        restResponse.setData(receiveSubSampleRecord);
        restResponse.setRestStatus(StringUtil.isNull(receiveSubSampleRecord) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 核查能否分配领样单
     *
     * @param dto
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "核查能否分配领样单", notes = "核查能否分配领样单")
    @PostMapping(path = "/checkCondition")
    public RestResponse<Boolean> checkCondition(@RequestBody DtoRecordSubmitTemp dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.canAssignReceiveSubRecord(dto.getIds());
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 批量分配
     *
     * @param dto 主键id
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "批量分配", notes = "批量分配")
    @PostMapping(path = "/assign")
    public RestResponse<Boolean> assign(@RequestBody DtoComplexQuery dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.assign(dto.getIds());
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 获取样品分配人员列表
     *
     * @param subId 领样单id
     * @return RestResponse<DtoSampleAssignTemp>
     */
    @ApiOperation(value = "获取样品分配人员列表", notes = "获取样品分配人员列表")
    @GetMapping("/person")
    public RestResponse<DtoSampleAssignTemp> findPerson(@RequestParam(name = "subId") String subId) {
        RestResponse<DtoSampleAssignTemp> restResponse = new RestResponse<>();
        restResponse.setData(service.findSampleAssignInfoById(subId, true));
        return restResponse;
    }

    /**
     * 修改分析人员
     *
     * @param params 传参
     * @return RestResponse<DtoSampleAssignTemp>
     */
    @ApiOperation(value = "修改分析人员", notes = "修改分析人员")
    @PostMapping("/person")
    public RestResponse<Boolean> changePerson(@RequestBody List<DtoSampleAssignParam> params) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.changePerson(params);
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 获取样品分配方法列表
     *
     * @param subId 领样单id
     * @return RestResponse<DtoSampleAssignTemp>
     */
    @ApiOperation(value = "获取样品分配方法列表", notes = "获取样品分配方法列表")
    @GetMapping("/method")
    public RestResponse<DtoSampleAssignTemp> findMethod(@RequestParam(name = "subId") String subId) {
        RestResponse<DtoSampleAssignTemp> restResponse = new RestResponse<>();
        restResponse.setData(service.findSampleAssignInfoById(subId, false));
        return restResponse;
    }

    /**
     * 修改分析方法
     *
     * @param param 传参
     * @return RestResponse<DtoSampleAssignTemp>
     */
    @ApiOperation(value = "修改分析方法", notes = "修改分析方法")
    @PostMapping("/method")
    public RestResponse<Boolean> savePerson(@RequestBody DtoSampleAssignParam param) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.changeMethod(param);
        restResponse.setData(true);
        return restResponse;
    }
}