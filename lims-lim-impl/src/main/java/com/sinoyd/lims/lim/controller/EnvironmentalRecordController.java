package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.EnvironmentalRecordCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoEnvironmentalRecord;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.service.EnvironmentalRecordService;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 环境管理-环境信息 
 * <AUTHOR> 修改：xuxb
 * @version V1.0.0 2019/3/12
 * @since V100R001
 */
@RestController
@RequestMapping("/api/lim/environmentalRecord")
@Validated
public class EnvironmentalRecordController
        extends BaseJpaController<DtoEnvironmentalRecord, String, EnvironmentalRecordService> {

    /**
     * 分页动态条件查询环境记录
     * 
     * @param criteria 条件参数
     */
    @ApiOperation(value = "分页动态条件查询环境记录", notes = "分页动态条件查询环境记录")
    @GetMapping
    public RestResponse<List<DtoEnvironmentalRecord>> findByPage(EnvironmentalRecordCriteria criteria) {
        RestResponse<List<DtoEnvironmentalRecord>> restResp = new RestResponse<>();

        PageBean<DtoEnvironmentalRecord> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 行编辑保存现场仪器使用记录/批量设置
     *
     * @param environmentalRecord  仪器使用记录
     * @return RestResponse<DtoEnvironmentalRecord> 响应
     */
    @ApiOperation(value = "行编辑保存现场仪器使用记录", notes = "行编辑保存现场仪器使用记录")
    @PutMapping("/newBatchSave")
    public RestResponse<DtoEnvironmentalRecord> newBatchSave(@Validated @RequestBody DtoEnvironmentalRecord environmentalRecord) {
        RestResponse<DtoEnvironmentalRecord> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.newBatchSave(environmentalRecord));
        return restResp;
    }

    /**
     * 新增现场仪器使用记录(新）
     *
     * @param testList  testList
     * @return RestResponse<DtoEnvironmentalRecord> 响应
     */
    @ApiOperation(value = "新增现场仪器使用记录(新）", notes = "新增现场仪器使用记录(新）")
    @PostMapping("/addByLocalTests/{subId}")
    public RestResponse<List<DtoEnvironmentalRecord>> addByLocalTests(@PathVariable String subId, @RequestBody List<DtoTest> testList) {
        RestResponse<List<DtoEnvironmentalRecord>> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.addByLocalTests(subId,testList));
        return restResp;
    }

    /**
     * 删除环境记录
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "删除环境记录", notes = "删除环境记录")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable(name = "id") String id) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 批量删除环境记录
     *
     * @param ids
     * @return
     */
    @ApiOperation(value = "批量删除环境记录", notes = "批量删除环境记录")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 现场分析仪器使用记录复制
     *
     * @param environmentalRecord  仪器使用记录
     * @return RestResponse<DtoEnvironmentalRecord> 响应
     */
    @ApiOperation(value = "现场分析仪器使用记录复制", notes = "现场分析仪器使用记录复制")
    @PostMapping("/copy")
    public RestResponse<DtoEnvironmentalRecord> copy(@Validated @RequestBody DtoEnvironmentalRecord environmentalRecord) {
        RestResponse<DtoEnvironmentalRecord> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.copy(environmentalRecord));
        return restResp;
    }


}