package com.sinoyd.lims.lim.service.impl;


import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.utils.base.DivationUtils;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.service.CalculationService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.repository.rcc.QualityControlLimitRepository;
import com.sinoyd.lims.lim.service.QualityControlLimitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 质控评价
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2022/11/9
 */
@Service
public class QualityControlLimitServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoQualityControlLimit, String, QualityControlLimitRepository> implements QualityControlLimitService {

    private CalculationService calculationService;

    /**
     * 根据testIds获取质控评价集合
     *
     * @param testIds 测试项目ids
     * @return 质控评价集合
     */
    @Override
    public List<DtoQualityControlLimit> findByTestIdIn(Collection<String> testIds) {
        return repository.findByTestIdIn(testIds);
    }

    /**
     * 根据testId获取质控评价集合
     *
     * @param testId 测试项目id
     * @return 质控评价集合
     */
    @Override
    public List<DtoQualityControlLimit> findByTestId(String testId) {
        return repository.findByTestId(testId);
    }

    /**
     * 质控评价处理信息
     *
     * @param controlLimitList 质控评价集合
     * @param limitValue       检出限
     * @param lowerLimit       测定下限
     * @return 质控评价集合
     */
    @Override
    public List<DtoQualityControlLimit> fillControlMsg(List<DtoQualityControlLimit> controlLimitList, String limitValue, String lowerLimit) {
        controlLimitList.forEach(limit -> {
            String allowData = rangeLimitData(limit.getAllowLimit(), limitValue, lowerLimit);
            limit.setAllowLimitData(allowData);
            String rangeData = rangeLimitData(limit.getRangeConfig(), limitValue, lowerLimit);
            limit.setRangeConfigData(rangeData);
        });
        return controlLimitList;
    }

    private String rangeLimitData(String data, String limitValue, String lowerLimit) {
        return validateFormat(data, limitValue, lowerLimit);
    }

    private String validateFormat(String s, String limitValue, String lowerLimit) {
        Map<String, String> replaceMap = new HashMap<>();
        if (StringUtil.isNotEmpty(s)) {
            if (s.contains("c") || s.contains("d")) {
                s = s.replace(" ", "");
                if (StringUtil.isNotEmpty(s)) {
                    if (s.contains("and")) {
                        String[] arr = s.split("and");
                        if (arr.length == 2) {
                            formatReplace(replaceMap, arr[0], limitValue, lowerLimit);
                            formatReplace(replaceMap, arr[1], limitValue, lowerLimit);
                        }
                    } else {
                        if (s.startsWith("[x]") && DivationUtils.cntSubStr(s, "[x]") == 1) {
                            formatReplace(replaceMap, s, limitValue, lowerLimit);
                        }
                    }
                }
                for (String key : replaceMap.keySet()) {
                    s = s.replace(key, replaceMap.get(key));
                }
            }
        }
        return s;
    }

    private void formatReplace(Map<String, String> replaceMap, String s, String limitValue, String lowerLimit) {
        s = s.replace("[x]", "");
        if (s.startsWith("<=") || s.startsWith(">=")) {
            s = s.substring(2);
        } else if (s.startsWith("<") || s.startsWith(">")) {
            s = s.substring(1);
        }
        Map<String, Object> map = new HashMap<>();
        if (s.contains("c")) {
            map.put("c", limitValue);
        } else if (s.contains("d")) {
            map.put("d", lowerLimit);
        }
        String result = calculationService.calculationExpression(s, map).toString();
        replaceMap.put(s, result);
    }

    @Autowired
    @Lazy
    public void setCalculationService(CalculationService calculationService) {
        this.calculationService = calculationService;
    }
}
