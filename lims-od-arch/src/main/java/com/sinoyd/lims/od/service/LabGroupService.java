package com.sinoyd.lims.od.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.od.dto.DtoLabGroup;

import java.util.List;

/**
 * 实验组操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
public interface LabGroupService extends IBaseJpaService<DtoLabGroup, String> {

    /**
     * 根据样品ids 查询实验组集合
     *
     * @param sampleIds 样品ids
     * @return 实验组集合
     */
    List<DtoLabGroup> findBySampleIdIn(List<String> sampleIds);

}
