package com.sinoyd.lims.lim.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 节假日vo
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023/1/28
 **/
@Data
public class HolidayConfigVO {

    /**
     * 节假日配置id
     */
    private String id;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginDate;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;


}
