package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.dto.DtoCostInfo;
import com.sinoyd.lims.pro.dto.DtoStatusForCostInfo;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumCostInfoModule;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumCostInfoStatus;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumStatus;
import com.sinoyd.lims.pro.repository.StatusForCostInfoRepository;
import com.sinoyd.lims.pro.service.StatusForCostInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 费用状态操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/8
 * @since V100R001
 */
 @Service
public class StatusForCostInfoServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoStatusForCostInfo,String,StatusForCostInfoRepository> implements StatusForCostInfoService {

    @Autowired
    private StatusForCostInfoRepository statusForCostInfoRepository;

    /**
     * 创建状态
     *
     * @param costInfoId 费用id
     */
    @Transactional
    @Override
    public void createStatus(String costInfoId) {
        DtoStatusForCostInfo status = new DtoStatusForCostInfo();
        status.setModule(EnumCostInfoModule.费用核算.getCode());
        status.setCostInfoId(costInfoId);
        status.setStatus(EnumStatus.待处理.getValue());
        repository.save(status);
    }

    /**
     * 修改状态数据
     *
     * @param from     状态起
     * @param to       状态止
     * @param sign     工作流信号对象
     * @param costInfo 费用实体
     */
    @Transactional
    @Override
    public void modifyStatus(String from, String to, DtoWorkflowSign sign, DtoCostInfo costInfo) {
        if (from.equals(to)) {
            return;
        }
        //读取该费用的所有状态数据
        List<DtoStatusForCostInfo> statusList = statusForCostInfoRepository.findByCostInfoId(costInfo.getId());
        //将list转为map      
        Map<String, DtoStatusForCostInfo> statusMap = statusList.stream().collect(Collectors.toMap(DtoStatusForCostInfo::getModule, status -> status));

        EnumCostInfoStatus fromStatus = EnumCostInfoStatus.getByName(from);
        String fromModule = StringUtil.isNull(fromStatus) ? "" : this.getModule(fromStatus);
        EnumCostInfoStatus toStatus = EnumCostInfoStatus.getByName(to);
        String toModule = StringUtil.isNull(toStatus) ? "" : this.getModule(toStatus);
        //若状态起在状态止之后，则不需要修改状态起的状态表数据，直接删除即可，否则需将状态起的状态表数据改为已处理
        if (StringUtils.isNotNullAndEmpty(fromModule) && fromStatus.getValue() > toStatus.getValue()) {
            fromModule = "";
        }

        if (StringUtil.isNotEmpty(fromModule)) {
            //默认存在当前状态对应的状态数据
            DtoStatusForCostInfo status = statusMap.get(fromModule);
            status.setStatus(EnumStatus.已处理.getValue());
            status.setNextPersonId(sign.getNextOperatorId());
            status.setNextPersonName(sign.getNextOperator());
            status.setLastNewOpinion(sign.getOption());
            comRepository.merge(status);
        } else {//若来源模块为空，说明为退回，需移除状态
            removeStatus(toModule, statusMap);
        }

        if (StringUtil.isNotEmpty(toModule)) {//若存在状态止的待处理模块，需修改对应状态数据
            if (statusMap.containsKey(toModule)) {//若数据库中存在该条数据
                DtoStatusForCostInfo status = statusMap.get(toModule);
                status.setStatus(EnumStatus.待处理.getValue());
                status.setNextPersonId(UUIDHelper.GUID_EMPTY);
                status.setNextPersonName("");
                status.setLastNewOpinion(sign.getOption());
                comRepository.merge(status);
            } else {
                DtoStatusForCostInfo status = new DtoStatusForCostInfo();
                status.setCostInfoId(costInfo.getId());
                status.setModule(toModule);
                status.setStatus(EnumStatus.待处理.getValue());
                status.setNextPersonId(UUIDHelper.GUID_EMPTY);
                status.setNextPersonName("");
                if (!UUIDHelper.GUID_EMPTY.equals(sign.getNextOperatorId())) {
                    status.setCurrentPersonId(sign.getNextOperatorId());
                    status.setCurrentPersonName(sign.getNextOperator());
                }
                status.setLastNewOpinion(sign.getOption());
                repository.save(status);
            }
        }
    }

    /**
     * 根据到达的模块状态删除该状态之后的数据
     *
     * @param toModule  到达的状态模块
     * @param statusMap 状态数据map
     */
    private void removeStatus(String toModule, Map<String, DtoStatusForCostInfo> statusMap) {
        Map<String, Integer> enumMap = new HashMap<>();
        for (EnumCostInfoModule m : EnumCostInfoModule.values()) {
            enumMap.put(m.getCode(), m.getValue());
        }
        Integer toModuleVal = enumMap.getOrDefault(toModule, -1);
        for (String key : enumMap.keySet()) {
            if (statusMap.containsKey(key) && enumMap.get(key) > toModuleVal) {
                repository.logicDeleteById(statusMap.get(key).getId());
            }
        }
    }

    /**
     * 根据费用状态枚举获取对应所处模块
     *
     * @param status 费用状态枚举值
     */
    private String getModule(EnumCostInfoStatus status) {
        switch (status) {
            case 新建:
            case 审核不通过:
            case 审批不通过:
                return EnumCostInfoModule.费用核算.getCode();

            case 审核中:
                return EnumCostInfoModule.费用审核.getCode();

            case 审批中:
                return EnumCostInfoModule.费用审批.getCode();

            case 已完成:
                return "";

            default:
                return "";
        }
    }
}