package com.sinoyd.base.service;

import com.sinoyd.base.dto.lims.DtoConsumableLog;
import com.sinoyd.frame.service.IBaseJpaService;

/**
 * 领用记录管理
 * <AUTHOR>
 * @version v1.0.0 2019/3/5
 * @since V100R001
 */
public interface ConsumableLogService extends IBaseJpaService<DtoConsumableLog, String> {
    /**
     * 根据Id删除领用记录
     *
     * @param id 领用id
     * @return 删除的条数
     */
    Integer logicDeleteById(String id);

    /**
     * 领用确认
     *
     * @param id 领用记录id
     */
    void confirm(String id);

    /**
     * 领用撤销
     *
     * @param consumableLog 领用记录
     */
    void cancel(DtoConsumableLog consumableLog);
}