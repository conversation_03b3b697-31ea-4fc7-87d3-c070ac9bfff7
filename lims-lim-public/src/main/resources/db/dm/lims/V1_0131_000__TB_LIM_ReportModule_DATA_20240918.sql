-- 新增水类报告空白样数据表组件
INSERT INTO tb_lim_reportmodule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode)
VALUES ('46f9e82c-a02f-41ca-960b-57a509a72d89', 'dtKbStdTable', '标准版空白样检测结果表组件', 'dtKbStdTable', 'dtKbSource', 4, 14, '',
        0, '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2024-09-18 15:40:48',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2024-09-18 15:40:48', '1', '0',
        0, 0, 0);


-- 常规水报告模板配置调整
update TB_LIM_ReportModule
set sonTableJson = '["dtWaterHeadStdTable", "dtNormalWaterStdTable", "dtCompoundStdNewTable", "outParallelStdDataSource", "dtKbStdTable"]'
where moduleCode = 'normalWaterStdDataSource';

-- 炉渣报告模板配置调整
update TB_LIM_ReportModule
set sonTableJson = '["dtWaterHeadStdTable", "dtSolidStdTable", "dtCompoundStdNewTable", "outParallelStdDataSource", "dtKbStdTable"]'
where moduleCode = 'solidStdDataSource';

-- 地表水报告模板配置调整
update TB_LIM_ReportModule
set sonTableJson = '["dtWaterHeadStdTable", "dtGroundWaterStdTable", "dtCompoundStdNewTable", "outParallelStdDataSource", "dtKbStdTable"]'
where moduleCode = 'groundWaterStdDataSource';


-- 废水报告模板配置调整
update TB_LIM_ReportModule
set sonTableJson = '["dtWaterHeadStdTable", "dtWasteWaterStdTable", "dtCompoundStdNewTable", "outParallelStdDataSource", "dtKbStdTable"]'
where moduleCode = 'wasteWaterStdDataSource';