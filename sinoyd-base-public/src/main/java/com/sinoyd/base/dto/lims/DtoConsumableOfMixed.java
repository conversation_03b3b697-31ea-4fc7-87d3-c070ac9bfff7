package com.sinoyd.base.dto.lims;

import com.sinoyd.base.entity.ConsumableOfMixed;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoConsumableOfMixed实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_BASE_ConsumableOfMixed")
@Data
@DynamicInsert
public class DtoConsumableOfMixed extends ConsumableOfMixed {

    @Transient
    private String analyzeItemName;

    @Transient
    private String dimensionName;
}