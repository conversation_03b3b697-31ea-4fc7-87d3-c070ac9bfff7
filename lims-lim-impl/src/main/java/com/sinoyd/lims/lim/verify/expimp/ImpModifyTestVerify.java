package com.sinoyd.lims.lim.verify.expimp;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.dto.customer.DtoImportTest;
import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoPerson2Test;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.utils.ImportUtils;
import lombok.Data;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * 测试项目导入更新数据校验器
 *
 * <AUTHOR>
 * @version V1.0.0 2023/12/12
 * @since V100R001
 */
@Data
public class ImpModifyTestVerify implements IExcelVerifyHandler<DtoImportTest> {
    /**
     * 工具类
     */
    private final ImportUtils importUtils = new ImportUtils();

    /**
     * 测试项目数据
     */
    private final List<DtoTest> testDeletedList;

    /**
     * 量纲数据
     */
    private final List<DtoDimension> dimensionList;

    /**
     * 检测类型数据
     */
    private final List<DtoSampleType> sampleTypeList;

    /**
     * 分析因子数据
     */
    private final List<DtoAnalyzeItem> anaItemList;

    /**
     * 分析方法数据
     */
    private final List<DtoAnalyzeMethod> anaMethodList;

    /**
     * 人员数据
     */
    private final List<DtoPerson> personList;

    /**
     * 测试项目检测人员数据
     */
    private final List<DtoPerson2Test> personToTestList;

    /**
     * 临时数据【每次校验后会将当前校验行传入，用于判断excel中重复数据】
     */
    private List<DtoImportTest> tempTestList = new ArrayList<>();

    /**
     * 业务参数
     */
    private final Map<String, Boolean> relationMap;

    /**
     * 构造方法
     *
     * @param relationMap      业务参数
     * @param testDeletedList  测试项目数据
     * @param dimensionList    量纲数据
     * @param sampleTypeList   检测类型数据
     * @param anaItemList      分析因子数据
     * @param anaMethodList    分析方法数据
     * @param personList       人员数据
     * @param personToTestList 检测人员数据
     */
    public ImpModifyTestVerify(Map<String, Boolean> relationMap, List<DtoTest> testDeletedList, List<DtoDimension> dimensionList,
                               List<DtoSampleType> sampleTypeList, List<DtoAnalyzeItem> anaItemList,
                               List<DtoAnalyzeMethod> anaMethodList, List<DtoPerson> personList,
                               List<DtoPerson2Test> personToTestList) {
        this.relationMap = relationMap;
        this.testDeletedList = testDeletedList;
        this.dimensionList = dimensionList;
        this.sampleTypeList = sampleTypeList;
        this.anaItemList = anaItemList;
        this.anaMethodList = anaMethodList;
        this.personList = personList;
        this.personToTestList = personToTestList;
    }

    /**
     * 数据校验
     *
     * @param importTest 导入的实体
     * @return 导入结果
     */
    @Override
    public ExcelVerifyHandlerResult verifyHandler(DtoImportTest importTest) {

        //region 导入数据处理
        try {
            //跳过空行
            if (importUtils.checkObjectIsNull(importTest)) {
                return new ExcelVerifyHandlerResult(true);
            }
            //数据去除前后空格
            importUtils.strToTrim(importTest);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }
        //处理默认值
        defaultValue(importTest);
        //校验结果
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        //总校验错误信息
        StringBuilder failStr = new StringBuilder("第" + importTest.getRowNum() + "行数据校验有误");
        //获取此检测类型Id
        String sampleTypeId = "";
        if (StringUtil.isNotEmpty(importTest.getSampleTypeId())) {
            DtoSampleType sampleType = sampleTypeList.stream()
                    .filter(p -> importTest.getSampleTypeId().equals(p.getTypeName())).findFirst().orElse(null);
            if (sampleType != null) {
                sampleTypeId = UUIDHelper.GUID_EMPTY.equals(sampleType.getParentId()) ? sampleType.getId() : sampleType.getParentId();
            }
        }
        //获取临时数据
        if (StringUtil.isEmpty(tempTestList)) {
            tempTestList = new ArrayList<>();
        }
        //非空校验
        nullValVerify(result, importTest, failStr);
        //业务校验规则
        businessVerify(result, importTest, failStr, tempTestList, sampleTypeId);
        //数据格式校验
        valFormatVerify(result, importTest, failStr);
        //替换校验结果的最后一个分号
        String failString = failStr.toString().replaceFirst("；", ":");
        result.setMsg(failString);
        //临时数据中添加当前行
        tempTestList.add(importTest);
        return result;
    }

    /**
     * 非空校验
     *
     * @param result     校验结果
     * @param importTest 当前数据
     * @param failStr    校验结果字符串
     */
    private void nullValVerify(ExcelVerifyHandlerResult result, DtoImportTest importTest, StringBuilder failStr) {
        //校验编号不能为空
        importUtils.checkIsNull(result, importTest.getId(), "编号", failStr);
        //校验分析因子不能为空
        importUtils.checkIsNull(result, importTest.getRedAnalyzeItemName(), "分析因子", failStr);
        //校验分析方法不能为空
        importUtils.checkIsNull(result, importTest.getRedAnalyzeMethodName(), "分析方法", failStr);
        //校验检测类型不能为空
        importUtils.checkIsNull(result, importTest.getSampleTypeId(), "检测类型", failStr);
        //校验是否现场不能为空
        importUtils.checkIsNull(result, importTest.getIsCompleteField(), "是否现场", failStr);
    }

    /**
     * 业务方面校验
     *
     * @param result       校验结果
     * @param importTest   当前数据
     * @param failStr      校验结果字符串
     * @param tempList     用于校验重复的导入数据集
     * @param sampleTypeId 当前导入的检测类型id
     */
    private void businessVerify(ExcelVerifyHandlerResult result, DtoImportTest importTest, StringBuilder failStr,
                                List<DtoImportTest> tempList, String sampleTypeId) {
        //excel重复数据判断
        isRepeatData(result, failStr, importTest, tempList);
        //数据库重复数据判断
        isExistData(result, failStr, importTest, sampleTypeId);
        //判断是否存在分析方法
        isExistAnaItem(result, failStr, importTest);
        //判断是否存在分析方法
        isExistAnaMethod(result, failStr, importTest);
        //判断是否存在量纲
        isExistDimension(result, failStr, importTest);
        //判断是否存在检测类型
        isExistSampleType(result, failStr, importTest);
        //判断人员是否存在（同步导入测试人员时）
        isExistPerson(result, failStr, importTest);
        //判断公式参数格式
        formulaFormat(result, failStr, importTest.getTestFormula());
    }

    /**
     * 数据的格式校验
     *
     * @param result     校验结果
     * @param importTest 当前数据
     * @param failStr    校验结果字符串
     */
    private void valFormatVerify(ExcelVerifyHandlerResult result, DtoImportTest importTest, StringBuilder failStr) {
        importUtils.checkNumTwo(result, importTest.getExamLimitValue(), "检出限", failStr);
        importUtils.checkNumTwo(result, importTest.getMostSignificance(), "最大有效", failStr);
        importUtils.checkNumTwo(result, importTest.getMostDecimal(), "最大小数", failStr);
        importUtils.checkNumTwo(result, importTest.getValidTime(), "有效期", failStr);
    }

    /**
     * 设置默认数据
     *
     * @param test 导入数据
     */
    private void defaultValue(DtoImportTest test) {
        test.setExamLimitValue("/".equals(test.getExamLimitValue()) ? null : test.getExamLimitValue());
        test.setIsQCP("是".equals(test.getIsQCP()) || "1".equals(test.getIsQCP()) ? "1" : "0");
        test.setIsQCB("是".equals(test.getIsQCB()) || "1".equals(test.getIsQCB()) ? "1" : "0");
        test.setIsCompleteField("是".equals(test.getIsCompleteField()) || "1".equals(test.getIsCompleteField()) ? "1" : "0");
        test.setIsOutsourcing("是".equals(test.getIsOutsourcing()) || "1".equals(test.getIsOutsourcing()) ? "1" : "0");
        test.setIsSamplingOut("是".equals(test.getIsSamplingOut()) || "1".equals(test.getIsSamplingOut()) ? "1" : "0");
        test.setIsSeries("是".equals(test.getIsSeries()) || "1".equals(test.getIsSeries()) ? "1" : "0");
        test.setIsInsUseRecord("是".equals(test.getIsInsUseRecord()) || "1".equals(test.getIsInsUseRecord()) ? "1" : "0");
    }


    /**
     * 判断公式参数格式
     *
     * @param result  校验结果
     * @param failStr 校验错误数据
     * @param formula 导入数据
     */
    private void formulaFormat(ExcelVerifyHandlerResult result, StringBuilder failStr, String formula) {
        if (relationMap.get("isImportFormula")) {
            if (StringUtil.isNotEmpty(formula)) {
                List<String> verifyFailStr = new ArrayList<>();
                String[] strings = formula.split("\\+|-|\\*|/|%|×|<|>|<=|>=|=|==|!=");
                List<String> allParams = new ArrayList<>();
                Collections.addAll(allParams, strings);
                allParams.removeIf(p -> p.equals("") || (!p.contains("[") && !p.contains("]")));
                for (String string : allParams) {
                    String regex = "(?<=\\[).*?(?=])";
                    Pattern pattern = Pattern.compile(regex);
                    Matcher matcher = pattern.matcher(string);
                    if (!matcher.find()) {
                        verifyFailStr.add(string);
                    }
                }
                if (StringUtil.isNotEmpty(verifyFailStr)) {
                    result.setSuccess(false);
                    failStr.append("；公式参数格式错误");
                }
            }
        }
    }

    /**
     * 判断分析项目是否存在
     *
     * @param result     校验结果
     * @param failStr    校验错误信息
     * @param importTest 导入的实体
     */
    private void isExistAnaItem(ExcelVerifyHandlerResult result, StringBuilder failStr, DtoImportTest importTest) {
        List<String> dbAnalyzeItemNames = anaItemList.stream().map(DtoAnalyzeItem::getAnalyzeItemName).collect(Collectors.toList());
        if (relationMap.get("isImportAnaItem")) {
            return;
        }
        if (StringUtil.isNotEmpty(dbAnalyzeItemNames) && StringUtil.isNotEmpty(importTest.getRedAnalyzeItemName())) {
            List<String> isExistItem = dbAnalyzeItemNames.stream().distinct().filter(p -> importTest.getRedAnalyzeItemName().equals(p)).collect(Collectors.toList());
            if (StringUtil.isEmpty(isExistItem)) {
                result.setSuccess(false);
                failStr.append("；分析项目在系统中不存在");
            }
        }
    }

    /**
     * 判断分析方法是否存在
     *
     * @param result     校验结果
     * @param failStr    校验错误信息
     * @param importTest 导入的实体
     */
    private void isExistAnaMethod(ExcelVerifyHandlerResult result, StringBuilder failStr, DtoImportTest importTest) {
        List<String> dbAnalyzeMethodNames = anaMethodList.stream().map(DtoAnalyzeMethod::getMethodName).collect(Collectors.toList());
        if (relationMap.get("isImportAnaMethod")) {
            return;
        }
        if (StringUtil.isNotEmpty(dbAnalyzeMethodNames) && StringUtil.isNotEmpty(importTest.getRedAnalyzeMethodName())) {
            List<String> isExistItem = dbAnalyzeMethodNames.stream().distinct().filter(p -> importTest.getRedAnalyzeMethodName().equals(p)).collect(Collectors.toList());
            if (StringUtil.isEmpty(isExistItem)) {
                result.setSuccess(false);
                failStr.append("；分析方法在系统中不存在");
            }
        }
    }

    /**
     * 判断量纲是否存在
     *
     * @param result     校验结果
     * @param failStr    校验错误信息
     * @param importTest 导入的实体
     */
    private void isExistDimension(ExcelVerifyHandlerResult result, StringBuilder failStr, DtoImportTest importTest) {
        List<String> dbDimensionNames = dimensionList.stream().map(DtoDimension::getDimensionName).collect(Collectors.toList());
        if (relationMap.get("isImportDimension")) {
            return;
        }
        if (StringUtil.isNotEmpty(dbDimensionNames) && StringUtil.isNotEmpty(importTest.getDimensionId())) {
            List<String> isExistDimension = dbDimensionNames.stream().distinct().filter(p -> importTest.getDimensionId().equals(p)).collect(Collectors.toList());
            if (StringUtil.isEmpty(isExistDimension)) {
                result.setSuccess(false);
                failStr.append("；量纲在系统中不存在");
            }
        }
    }

    /**
     * 判断检测类型是否存在
     *
     * @param result     校验结果
     * @param failStr    校验错误信息
     * @param importTest 导入的实体
     */
    private void isExistSampleType(ExcelVerifyHandlerResult result, StringBuilder failStr, DtoImportTest importTest) {
        List<String> dbSampleType = sampleTypeList.stream().map(DtoSampleType::getTypeName).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(dbSampleType) && StringUtil.isNotEmpty(importTest.getSampleTypeId())) {
            List<String> isExistDimension = dbSampleType.stream().distinct().filter(p -> importTest.getSampleTypeId().equals(p)).collect(Collectors.toList());
            if (StringUtil.isEmpty(isExistDimension)) {
                result.setSuccess(false);
                failStr.append("；检测类型在系统中不存在");
            }
        }
    }

    /**
     * 判断人员是否存在
     *
     * @param result     校验结果
     * @param failStr    校验错误信息
     * @param importTest 导入数据
     */
    private void isExistPerson(ExcelVerifyHandlerResult result, StringBuilder failStr, DtoImportTest importTest) {
        List<String> allPersonNames = new ArrayList<>();
        if (relationMap.get("isImportTestPerson")) {
            if (StringUtil.isNotEmpty(importTest.getDefaultPerson())) {
                String defaultPerson = importTest.getDefaultPerson();
                allPersonNames.add(defaultPerson);
            }
            if (StringUtil.isNotEmpty(importTest.getAbilityPerson())) {
                String abilityPerson = importTest.getAbilityPerson();
                allPersonNames.addAll(importUtils.personStrToList(abilityPerson));
            }
            if (StringUtil.isNotEmpty(allPersonNames)) {
                List<String> dbPersons = personList.stream().map(DtoPerson::getCName).collect(Collectors.toList());
                List<String> notHavePerson = allPersonNames.stream().filter(p -> !dbPersons.contains(p)).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(notHavePerson)) {
                    result.setSuccess(false);
                    failStr.append("；无法找到人员：").append(notHavePerson);
                }
            }
        }
    }

    /**
     * 判断excel是否有重复数据
     *
     * @param result     校验结果
     * @param failStr    校验错误数据
     * @param importTest 导入数据
     * @param tempList   临时数据
     */
    private void isRepeatData(ExcelVerifyHandlerResult result, StringBuilder failStr, DtoImportTest importTest, List<DtoImportTest> tempList) {
        if (StringUtil.isNotEmpty(importTest.getRedAnalyzeItemName()) && StringUtil.isNotEmpty(importTest.getRedAnalyzeMethodName()) && StringUtil.isNotEmpty(importTest.getSampleTypeId())) {
            tempList.removeIf(p -> StringUtil.isEmpty(p.getRedAnalyzeItemName()) || StringUtil.isEmpty(p.getRedAnalyzeMethodName()) || StringUtil.isEmpty(p.getSampleTypeId()));
            List<Integer> isRepeatRowNum = tempList.stream()
                    .filter(p -> importTest.getRedAnalyzeItemName().equals(p.getRedAnalyzeItemName()) && importTest.getRedAnalyzeMethodName().equals(p.getRedAnalyzeMethodName()) && importTest.getSampleTypeId().equals(p.getSampleTypeId()))
                    .map(DtoImportTest::getRowNum)
                    .collect(Collectors.toList());
            if (StringUtil.isNotEmpty(isRepeatRowNum)) {
                result.setSuccess(false);
                failStr.append("；").append("测试项目与第").append(isRepeatRowNum).append("条重复");
            }
        }
    }

    /**
     * 判断数据库中是否已存在测试项目
     *
     * @param result       检验结果
     * @param failStr      校验错误数据
     * @param importTest   导入数据
     * @param sampleTypeId 检测类型Id
     */
    public void isExistData(ExcelVerifyHandlerResult result, StringBuilder failStr, DtoImportTest importTest, String sampleTypeId) {
        List<DtoTest> dbTest = testDeletedList.stream().filter(p -> !p.getIsDeleted()).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(importTest.getRedAnalyzeItemName()) && StringUtil.isNotEmpty(importTest.getRedAnalyzeMethodName()) && StringUtil.isNotEmpty(importTest.getSampleTypeId())) {
            // 根据基本信息和id进行判断，系统中是否存在测试项目
            List<DtoTest> isExistData = dbTest.stream()
                    .filter(p -> importTest.getRedAnalyzeItemName().equals(p.getRedAnalyzeItemName())
                            && importTest.getRedAnalyzeMethodName().equals(p.getRedAnalyzeMethodName())
                            && sampleTypeId.equals(p.getSampleTypeId())
                            && !importTest.getId().equals(p.getId()))
                    .collect(Collectors.toList());
            if (StringUtil.isNotEmpty(isExistData)) {
                result.setSuccess(false);
                failStr.append("；").append("在系统中此测试项目已存在");
            }
        }
    }
}
