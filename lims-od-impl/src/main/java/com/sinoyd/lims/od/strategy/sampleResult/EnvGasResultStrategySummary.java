package com.sinoyd.lims.od.strategy.sampleResult;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.common.utils.EnvGasCalculationUtil;
import com.sinoyd.common.utils.MathUtil;
import com.sinoyd.lims.od.constants.OdsConstants;
import com.sinoyd.lims.od.dto.*;
import com.sinoyd.lims.od.dto.customer.DtoGroupSummaryTemp;
import com.sinoyd.lims.od.entity.EnvGasResult;
import com.sinoyd.lims.od.enums.EnumOds;
import org.springframework.stereotype.Component;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 环境空气结果汇总实现
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/11
 * @since V100R001
 */
@Component(OdsConstants.SampleResultName.ENV_GAS_RESULT)
public class EnvGasResultStrategySummary extends AbsSampleResultStrategy {

    /**
     * M值校验数值
     */
    private static final BigDecimal M_CHECK_VAL = new BigDecimal("0.58");

    /**
     * 过滤默认实验组
     *
     * @param labGroupList 实验组集合
     * @return 默认实验组
     */
    @Override
    protected List<DtoLabGroup> getDefaultGroup(List<DtoLabGroup> labGroupList) {
        List<String> groupIds = labGroupList.stream().map(DtoLabGroup::getId).collect(Collectors.toList());
        // 根据实验次序，筛选已完成三轮的实验组
        List<DtoLabSeq> labSeqList = StringUtil.isNotEmpty(groupIds) ? labSeqRepository.findByGroupIdIn(groupIds) : new ArrayList<>();
        Map<String, Long> groupCompletedCount = labSeqList.stream()
                .filter(p -> p.getLabState().equals(EnumOds.EnumLabState.已完成.getValue()))
                .collect(Collectors.groupingBy(
                        DtoLabSeq::getGroupId,
                        Collectors.counting()
                ));
        labGroupList = labGroupList.stream().filter(labGroup -> groupCompletedCount.getOrDefault(labGroup.getId(), 0L) == 3).collect(Collectors.toList());
        groupIds = labGroupList.stream().map(DtoLabGroup::getId).collect(Collectors.toList());
        List<DtoOdPersonAnswer> odPersonAnswers = StringUtil.isNotEmpty(groupIds) ? odPersonAnswerService.findByGroupIdIn(groupIds) : new ArrayList<>();
        if (odPersonAnswers.size() < 18) {
            throw new BaseException("实验数量不足，无法进行汇总");
        }
        // 默认最后两组，倒序取前两个
        List<DtoLabGroup> labGroups = labGroupList.stream().sorted(Comparator.comparing(DtoLabGroup::getSn).reversed())
                .limit(2).sorted(Comparator.comparing(DtoLabGroup::getSn)).collect(Collectors.toList());
        int i = 1;
        for (DtoLabGroup group : labGroups) {
            group.setCalculateSn(i);
            i++;
        }
        return labGroups;
    }

//    /**
//     * 初始化实验组结果汇总
//     *
//     * @param labGroupList 实验组数据集合
//     * @param labSeqList   实验次序数据集合
//     */
//    @Override
//    protected void initGroupSummary(List<DtoLabGroup> labGroupList, List<DtoLabSeq> labSeqList) {
//        // 因环境质量的实验组会在第三轮结束时创建结果数据，可以直接查询
//        List<String> groupIds = labGroupList.stream().map(DtoLabGroup::getId).collect(Collectors.toList());
//        List<DtoEnvGasResult> envGasResults = envGasResultRepository.findByGroupIdIn(groupIds);
//        Map<String, DtoEnvGasResult> envGasResultMap = envGasResults.stream().collect(Collectors.toMap(DtoEnvGasResult::getGroupId, p -> p));
//        // 判断M值是否通过
//        judgeM(labGroupList, envGasResultMap);
//    }


    @Override
    public List<DtoLabGroup> findGroupSummary(List<DtoGroupSummaryTemp> groupSummaryTemps) {
        List<String> groupIds = groupSummaryTemps.stream().map(DtoGroupSummaryTemp::getGroupId).collect(Collectors.toList());
        List<DtoLabGroup> labGroups = StringUtil.isNotEmpty(groupIds) ? labGroupRepository.findAll(groupIds) : new ArrayList<>();
        List<DtoOdPersonAnswer> odPersonAnswers = StringUtil.isNotEmpty(groupIds) ? odPersonAnswerService.findByGroupIdIn(groupIds) : new ArrayList<>();
        if (odPersonAnswers.size() < 18) {
            throw new BaseException("实验数量不足，无法进行汇总");
        }

        List<DtoEnvGasResult> envGasResults = StringUtil.isNotEmpty(groupIds) ? envGasResultRepository.findByGroupIdIn(groupIds) : new ArrayList<>();
        if (envGasResults.size() < groupIds.size()) {
            throw new BaseException("实验数量不足，无法进行汇总");
        }
        Map<String, DtoEnvGasResult> envGasResultMap = envGasResults.stream().collect(Collectors.toMap(DtoEnvGasResult::getGroupId, p -> p));
        labGroups.forEach(p -> groupSummaryTemps.stream().filter(t -> p.getId().equals(t.getGroupId())).findFirst().ifPresent(temp -> p.setCalculateSn(temp.getCalculateSn())));
        judgeM(labGroups, envGasResultMap);
        return labGroups;
    }

    /**
     * 计算样品实验结果
     *
     * @param sampleResult 样品结果实体
     * @param labGroupList 汇总实验组集合
     */
    @Override
    @Transactional
    protected void calculateSampleResult(DtoSampleResult sampleResult, List<DtoLabGroup> labGroupList) {
        // 获取待计算的环境空气汇总结果
        List<DtoEnvGasResult> envGasResultList = labGroupList.stream().map(DtoLabGroup::getEnvGasResult).collect(Collectors.toList());
        // 当所有实验组M都不通过时，无法参与计算
        if (StringUtil.isNull(envGasResultList) || envGasResultList.stream().noneMatch(EnvGasResult::getIsPassed)) {
            throw new BaseException("M值不满足计算规则，无法计算");
        }
        try {
            String mValue1 = "";
            String mValue2 = "";
            int dilutionRate1 = 0;
            int dilutionRate2 = 0;
            for (DtoLabGroup labGroup : labGroupList) {
                DtoEnvGasResult envGasResult = labGroup.getEnvGasResult();
                if (labGroup.getCalculateSn() == 1) {
                    mValue1 = envGasResult.getMValue();
                    dilutionRate1 = envGasResult.getDilutionRate();
                } else if (labGroup.getCalculateSn() == 2) {
                    mValue2 = envGasResult.getMValue();
                    dilutionRate2 = envGasResult.getDilutionRate();
                }
            }
            String aParam = EnvGasCalculationUtil.calculateAParam(mValue1, mValue2, 2);
            String bParam = EnvGasCalculationUtil.calculateBParam(dilutionRate1, dilutionRate2, 2);
            String density = EnvGasCalculationUtil.calculateStenchDensity(dilutionRate1, aParam, bParam, 2);
            sampleResult.setAValue(aParam);
            sampleResult.setBValue(bParam);
            sampleResult.setOdourConsistence(density);
            sampleResult.setIsPassed(true);
        } catch (Exception e) {
            throw new BaseException("环境空气结果计算错误！" + e.getMessage());
        }
    }


    /**
     * 判断M值是否通过
     *
     * @param labGroupList    实验组集合
     * @param envGasResultMap 环境空气结果
     */
    private void judgeM(List<DtoLabGroup> labGroupList, Map<String, DtoEnvGasResult> envGasResultMap) {
        if (StringUtil.isNotEmpty(labGroupList)) {
            labGroupList.sort(Comparator.comparing(DtoLabGroup::getCalculateSn));
            DtoLabGroup ontLabGroup = labGroupList.get(0);
            if (envGasResultMap.containsKey(ontLabGroup.getId())) {
                // 实验组1 结果数据
                DtoEnvGasResult oneEnvGasResult = envGasResultMap.get(ontLabGroup.getId());
                BigDecimal oneMValue = MathUtil.isNumber(oneEnvGasResult.getMValue()) ? new BigDecimal(oneEnvGasResult.getMValue()) : null;
                if (null != oneMValue) {
                    // 为两组数据时的比较
                    if (labGroupList.size() == 2) {
                        DtoLabGroup twoLabGroup = labGroupList.get(1);
                        DtoEnvGasResult twoEnvGasResult = envGasResultMap.get(twoLabGroup.getId());
                        BigDecimal twoMValue = MathUtil.isNumber(twoEnvGasResult.getMValue()) ? new BigDecimal(twoEnvGasResult.getMValue()) : null;
                        if (null != twoMValue) {
                            if (oneMValue.compareTo(M_CHECK_VAL) <= 0) {
                                oneEnvGasResult.setIsPassed(true);
                            } else if (oneMValue.compareTo(M_CHECK_VAL) > 0) {
                                oneEnvGasResult.setIsPassed(false);
                                oneEnvGasResult.setIsShow(false);
                                // 当M1>0.58情况下，M2>=0.58，M2显示不通过；
                                if (twoMValue.compareTo(M_CHECK_VAL) >= 0) {
                                    // 不通过
                                    twoEnvGasResult.setIsPassed(false);
                                } else {
                                    twoEnvGasResult.setIsPassed(true);
                                }
                            }
                        }
                        twoLabGroup.setEnvGasResult(twoEnvGasResult);
                    } else if (labGroupList.size() == 1) {
                        // 当M1>0.58情况下，M2未开始，M1显示不通过；
                        if (oneMValue.compareTo(M_CHECK_VAL) > 0) {
                            oneEnvGasResult.setIsPassed(false);
                        }
                    }
                    ontLabGroup.setEnvGasResult(oneEnvGasResult);
                }
            }
        }
    }
}
