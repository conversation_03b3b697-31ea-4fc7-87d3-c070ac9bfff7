package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.ProjectPushInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;


/**
 * DtoProjectPushInfo实体
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/06
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_ProjectPushInfo")
@Data
@DynamicInsert
public class DtoProjectPushInfo extends ProjectPushInfo {

    public DtoProjectPushInfo() {
    }

    public DtoProjectPushInfo(String id, String projectId, Date pushTime) {
        this.setId(id);
        this.setProjectId(projectId);
        this.setPushTime(pushTime);
    }
}