package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.dto.customer.DtoImportPerson;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.frame.dto.DtoRole;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.DepartmentService;
import com.sinoyd.frame.service.impl.RoleInfoServiceImpl;
import com.sinoyd.lims.lim.dto.customer.DtoImportPersonExtend;
import com.sinoyd.lims.lim.service.DownLoadPersonTemplateService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class DownLoadPersonTemplateServiceImpl implements DownLoadPersonTemplateService {
    //region 注入
    @Autowired
    private CodeService codeService;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private RoleInfoServiceImpl roleInfoService;

    @Autowired
    private ImportUtils importUtils;
    //endregion


    /**
     * 人员导入模板下载
     * @param response   响应流
     * @param sheetNames 需要赋值的sheet名
     * @param fileName   文件名
     */
    @Override
    public void downLoadPersonTemplate(HttpServletResponse response, Map<String, String> sheetNames, String fileName) {
        //region 获取关联参数
        //获取所有的职务
        List<String> postNames = codeService.findCodes("LIM_Post").stream().map(DtoCode::getDictName).collect(Collectors.toList());

        //获取所有的职务
        List<String> titleNames = codeService.findCodes("LIM_TechnicalTitle").stream().map(DtoCode::getDictName).collect(Collectors.toList());

        //获取所有的职务
        List<String> educationNames = codeService.findCodes("LIM_Degree").stream().map(DtoCode::getDictName).collect(Collectors.toList());

        //获取所有的角色
        List<String> roleNames = roleInfoService.findAll().stream().map(DtoRole::getRoleName).collect(Collectors.toList());

        // 获取所有的部门
        List<String> deptNames = departmentService.findAll().stream().map(DtoDepartment::getDeptName).collect(Collectors.toList());
        //endregion

        //region 获取关联数据集合
        // 获取关联数据
        List<DtoImportPersonExtend> personExtendList = getExtendData(deptNames,roleNames,postNames,titleNames,educationNames);
        // 获取仪器空数据
        List<DtoImportPerson> persons = new ArrayList<>();
        DtoImportPerson person = new DtoImportPerson();
        persons.add(person);
        //endregion

        //region 赋值工作单并下载
        // 获取需要下载的工作单
        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoImportPerson.class, DtoImportPersonExtend.class, persons, personExtendList);
        // 下载Excel返回响应流
        PoiExcelUtils.downLoadExcel(fileName,response,workBook);
        //endregion
    }

    /**
     * 获取关联数据
     *
     * @param deptNames         部门
     * @param roleNames         角色
     * @param postNames         职务
     * @param titleNames        职称
     * @param educationNames    学历
     * @return 关联数据
     */
    private List<DtoImportPersonExtend> getExtendData(List<String> deptNames, List<String> roleNames, List<String> postNames, List<String> titleNames, List<String> educationNames) {
        //返回的数据集合
        List<DtoImportPersonExtend> personExtendList = new ArrayList<>();

        // 获取最长
        List<Integer> size = new ArrayList<>();
        size.add(deptNames.size());
        size.add(roleNames.size());
        size.add(postNames.size());
        size.add(titleNames.size());
        size.add(educationNames.size());
        Integer max = Collections.max(size);
        // 循环赋值第二种Sheet内容
        for (int i = 0; i < max; i++) {
            DtoImportPersonExtend personExtend = new DtoImportPersonExtend();
            personExtend.setDeptName(deptNames.size()<i+1 ? null : deptNames.get(i));
            personExtend.setRoleName(roleNames.size()<i+1? null : roleNames.get(i));
            personExtend.setPost(postNames.size()<i+1 ? null : postNames.get(i));
            personExtend.setTitle(titleNames.size()<i+1 ? null : titleNames.get(i));
            personExtend.setEducation(educationNames.size()<i+1 ? null : educationNames.get(i));
            personExtendList.add(personExtend);
        }
        return personExtendList;
    }
}
