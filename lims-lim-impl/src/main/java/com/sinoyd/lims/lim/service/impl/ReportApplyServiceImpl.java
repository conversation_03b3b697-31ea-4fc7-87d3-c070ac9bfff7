package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoReportApply;
import com.sinoyd.lims.lim.repository.lims.ReportApplyRepository;
import com.sinoyd.lims.lim.service.ReportApplyService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;


/**
 * ReportApply操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2022/4/21
 * @since V100R001
 */
@Service
public class ReportApplyServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoReportApply, String, ReportApplyRepository> implements ReportApplyService {

    @Override
    public void findByPage(PageBean<DtoReportApply> pb, BaseCriteria reportApplyCriteria) {
        pb.setEntityName("DtoReportApply a, DtoReportConfig c");
        pb.setSelect("select a,c.orderNum");
        comRepository.findByPage(pb, reportApplyCriteria);
        List<DtoReportApply> dataList = pb.getData();
        List<DtoReportApply> newDataList = new ArrayList<>();

        Iterator<DtoReportApply> iterator = dataList.iterator();
        // 循环迭代获取JPQL中查询返回的属性

        while (iterator.hasNext()) {
            Object obj = iterator.next();
            Object[] objData = (Object[]) obj;
            // 按查询顺序下标一一获取
            DtoReportApply reportApply = (DtoReportApply) objData[0];
            reportApply.setOrderNum((Integer) objData[1]);
            newDataList.add(reportApply);
        }
        newDataList = newDataList.stream().sorted(Comparator.comparing(DtoReportApply::getOrderNum).reversed().thenComparing(DtoReportApply::getName)).collect(Collectors.toList());
        pb.setData(newDataList);
    }
}