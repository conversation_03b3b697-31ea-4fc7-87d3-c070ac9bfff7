package com.sinoyd.lims.pro.listener;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.core.BaseCodeHelper;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.lims.pro.dto.DtoWorkSheetFolder;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.service.SignatureService;
import com.sinoyd.lims.pro.service.WorkSheetFolderService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;

import java.util.Collections;

/**
 * 表单签名监听
 * <AUTHOR>
 * @version V1.0.0 2019/11/11
 * @since V100R001
 */
@Slf4j
public class SignatureListener implements ExecutionListener {

    @Override
    public void notify(DelegateExecution delegateExecution) throws Exception {
        String businessKey = delegateExecution.getProcessBusinessKey();

        WorkSheetFolderService workSheetFolderService = SpringContextAware.getBean(WorkSheetFolderService.class);

        CommonRepository commonRepository = SpringContextAware.getBean(CommonRepository.class);

        //签名
        SignatureService signatureService = SpringContextAware.getBean(SignatureService.class);
        try {
            //获取工作单信息
            DtoWorkSheetFolder oldWorkSheet = workSheetFolderService.findOne(businessKey);
            commonRepository.clear();
            DtoWorkSheetFolder workSheetFolder = workSheetFolderService.findOne(businessKey);
            Integer type = EnumPRO.EnumSigType.分析者.getValue();
            //分析日期获取检测单的分析日期
            String nowTime = DateUtil.nowTime("yyyy.MM.dd");
            String analyzeTime = StringUtil.isNotNull(workSheetFolder.getAnalyzeTime()) ? DateUtil.dateToString(workSheetFolder.getAnalyzeTime(), "yyyy.MM.dd") : "";
            String timeStr = (StringUtil.isNotEmpty(analyzeTime) && !analyzeTime.contains("1753")) ? analyzeTime : nowTime;
            if (EnumPRO.EnumWorkSheetStatus.已经提交.getValue().equals(workSheetFolder.getWorkStatus())) {
                type = EnumPRO.EnumSigType.复核者.getValue();
                timeStr = nowTime;
            } else if (EnumPRO.EnumWorkSheetStatus.复核通过.getValue().equals(workSheetFolder.getWorkStatus())) {
                type = EnumPRO.EnumSigType.审核者.getValue();
                timeStr = nowTime;
            }
            //原始记录单根据工作单状态签名   确认检测单状态提交无需调整签名
            if(!EnumPRO.EnumWorkSheetStatus.确认检测单.getValue().equals(workSheetFolder.getWorkStatus())){
                signatureService.sig(businessKey, Collections.singletonList(PrincipalContextUser.getPrincipal().getUserId()), type,
                        BaseCodeHelper.DOCUMENT_EXTEND_ORIGINAL_RECORD, timeStr);
            }

            if (!oldWorkSheet.getStatus().equals(workSheetFolder.getStatus())) {
                //保存工作单状态
                workSheetFolderService.save(oldWorkSheet);
            }
        } catch (Exception ex) {
            log.info("签名发生异常:" + ex.getMessage());
            throw new BaseException(ex.getMessage());
        }
    }
}
