package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;

/**
 * 考核管理实体
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023/09/14
 **/
@MappedSuperclass
@ApiModel(description="Examine")
@Data
@EntityListeners(AuditingEntityListener.class)
public class Examine implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public  Examine() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 考核目标
     */
    @Column(length = 255, nullable = false)
    @ApiModelProperty("考核目标")
    @Length(message = "考核目标{validation.message.length}", max = 255)
    private String title;


    /**
     * 考核部门标识
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("考核部门标识")
    private String deptId;

    /**
     * 考核部门
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("考核部门")
    private String deptName;

    /**
     * 登记人标识
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("登记人标识")
    private String addPersonId;

    /**
     * 登记人名称
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("登记人名称")
    private String addPersonName;

    /**
     * 受考核人员标识
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("受考核人员标识")
    private String inspectedPersonId;

    /**
     * 受考核人员名称
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("受考核人员名称")
    private String inspectedPerson;


    /**
     * 登记日期
     */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("登记日期")
    private Date addDate;

    /**
     * 考核开始时间
     */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("考核开始时间")
    private Date registeDate;

    /**
     * 考核截止时间
     */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("考核截止时间")
    private Date endDate;

    /**
     * 状态
     */
    @Column(length=50)
    @ApiModelProperty("状态")
    private Integer status;


    /**
     * 审核意见
     */
    @Column(length=1000)
    @ApiModelProperty("审核意见")
    @Length(message = "审核意见{validation.message.length}", max = 1000)
    private String auditOpinion;

    /**
     *  假删状态   0/1  未假删/已假删
     */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("假删状态")
    private Boolean isDeleted=false;

    /**
     * 组织机构id
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;



    /**
     * 创建人
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;
}
