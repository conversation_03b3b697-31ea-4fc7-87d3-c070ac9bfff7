package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoExamineType;

import java.util.List;

/**
 *  考核类型repository
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2023/09/14
 */
public interface ExamineTypeRepository extends IBaseJpaRepository<DtoExamineType, String> {
    /**
     * 根据考核标识获取下属大小项
     * @param examineId 考核标识
     * @return 考核大小项列表
     */
    List<DtoExamineType> findAllByExamineId(String examineId);

    /**
     * 根据大项标识查找所有子项
     * @param id 大项id
     * @return 子项列表
     */
    List<DtoExamineType> findAllByParentId(String id);

    /**
     * 根据考核标识列表获取所有考核项目
     * @param examineIdList 考核标识列表
     * @return 考核项目列表
     */
    List<DtoExamineType> findAllByExamineIdIn(List<String> examineIdList);
}
