package com.sinoyd.lims.foreign.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.foreign.DataInstrumentUseRecordVO;
import com.sinoyd.lims.foreign.DataOutSorceDataVO;
import com.sinoyd.lims.foreign.DataReportFolderSortInfoVO;
import com.sinoyd.lims.foreign.service.DataOutSorceDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 分包信息接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Service
public class DataOutSorceDataServiceImpl extends AbsQueryDataServiceImpl<DataOutSorceDataVO> implements DataOutSorceDataService {

    @Override
    public StringBuilder getSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("select analyseDataId,analyzeMethodName,analyzeMethodId,");
        sql.append("testValue,dimensionName,dimensionId,state,");
        sql.append("orgId, domainId,analyzeEndTime,subcontractor,");
        sql.append("analyzeTime, cmaCode,outSourceReportCode");
        sql.append(" from TB_PRO_OutSourceData ");
        sql.append(" WHERE 1 = 1 ");
        return sql;
    }

    @Override
    protected void appendCondition(StringBuilder sql, Map<String, Object> map, String condition, String paramName) {
        super.appendCondition(sql, map, "AND modifyDate > :syncTime ", "syncTime");
        super.appendCondition(sql, map, "AND analyseDataId in (:analyseDataIds)", "analyseDataIds");
    }

    @Override
    protected RowMapper<DataOutSorceDataVO> getEntityRowMapper() {
        return (rs, i) -> {
            DataOutSorceDataVO outSorceDataVO = new DataOutSorceDataVO();
            outSorceDataVO.setId(UUIDHelper.NewID());
            outSorceDataVO.setAnalyseDataId(rs.getString("analyseDataId"));
            outSorceDataVO.setAnalyzeMethodName(rs.getString("analyzeMethodName"));
            outSorceDataVO.setTestValue(rs.getString("testValue"));
            outSorceDataVO.setDimensionName(rs.getString("dimensionName"));
            outSorceDataVO.setAnalyzeTime(rs.getDate("analyzeTime"));
            outSorceDataVO.setAnalyzeEndTime(rs.getDate("analyzeEndTime"));
            outSorceDataVO.setSubcontractor(rs.getString("subcontractor"));
            outSorceDataVO.setCmaCode(rs.getString("cmaCode"));
            outSorceDataVO.setOutSourceReportCode(rs.getString("outSourceReportCode"));
            outSorceDataVO.setOrgId(rs.getString("orgId"));
            outSorceDataVO.setDomainId(rs.getString("domainId"));
            outSorceDataVO.setSyncTime(new Date());
            return outSorceDataVO;
        };
    }
}