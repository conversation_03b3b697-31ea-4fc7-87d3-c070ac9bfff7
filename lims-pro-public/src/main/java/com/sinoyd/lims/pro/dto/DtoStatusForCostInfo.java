package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.StatusForCostInfo;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoStatusForCostInfo实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/8
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_StatusForCostInfo")
 @Data
 @DynamicInsert
 public  class DtoStatusForCostInfo extends StatusForCostInfo {
   private static final long serialVersionUID = 1L;
 }