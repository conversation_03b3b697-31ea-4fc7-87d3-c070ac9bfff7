package com.sinoyd.lims.api.dto.vo.offLine;

import lombok.Data;

/**
 * 离线数据录入（离线数据表）：样品表实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/09/23
 */
@Data
public class OffLineSampleItemListVO {

    /**
     * 主键ID，UUID格式
     */
    private String id;

    /**
     * 采样单ID，关联采样记录表
     */
    private String receiveId;

    /**
     * 点位ID，关联点位表
     */
    private String folderId;

    /**
     * 点位ID
     */
    private String pointId;

    /**
     * 样品编码
     */
    private String code;

    /**
     * 点位名称
     */
    private String redFolderName;

    /**
     * 检测类型
     */
    private String sampleTypeId;

    /**
     * 周期
     */
    private Integer periodCount;

    /**
     * 批次
     */
    private Integer timePerPeriod;

    /**
     * 样次
     */
    private Integer samplePerTime;

    /**
     * 所属机构
     */
    private String orgId;
}
