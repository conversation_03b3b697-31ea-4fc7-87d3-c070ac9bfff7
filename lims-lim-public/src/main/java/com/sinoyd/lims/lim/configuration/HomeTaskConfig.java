package com.sinoyd.lims.lim.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@ConfigurationProperties(
        prefix = "projectTask"
)
@Data
public class HomeTaskConfig {
    private List<HomeModule> projectModules = new ArrayList();
}
