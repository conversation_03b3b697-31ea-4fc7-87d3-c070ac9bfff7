package com.sinoyd.base.dto.rcc;

import com.sinoyd.base.entity.EvaluationCriteria;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.*;

import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoEvaluationCriteria实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_BASE_EvaluationCriteria") 
 @Where(clause = "isDeleted = 0")
 @Data
 @DynamicInsert
 public  class DtoEvaluationCriteria extends EvaluationCriteria {
    private static final long serialVersionUID = 1L;

    /**
     * 检测类型大类（名称）
     */
    @Transient
    private String sampleTypeName;


    /**
     * 标准类型名称
     */
    @Transient
    private String categoryName;

    /**
     * 实施年份
     */
    @Transient
    private String ye = StringUtil.isNull(super.getStartTime()) ? "1753" : DateUtil.dateToString(super.getStartTime(), DateUtil.YEAR_NO_MONTH);

    /**
     * 评价等级
     */
    @Transient
    private List<DtoEvaluationLevel> evaluationLevel = new ArrayList<>();

    /**
     * 评价因子
     */
    @Transient
    private List<DtoEvaluationAnalyzeItem> evaluationAnalyzeItem = new ArrayList<>();

}