package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoNotice;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * 公告管理仓储
 * <AUTHOR>
 * @version v1.0.0 2019/1/31
 * @since V100R001
 */
public interface NoticeRepository extends IBaseJpaPhysicalDeleteRepository<DtoNotice,String>{

    /**
     * 公告置顶
     */
    @Modifying
    @Query("update DtoNotice p set p.isTop = 1 where p.id = :id")
    Integer makeTop(@Param("id") String id);

    /**
     * 取消置顶
     */
    @Modifying
    @Query("update DtoNotice p set p.isTop = 0 where p.id = :id")
    Integer cancelTop(@Param("id") String id);

}