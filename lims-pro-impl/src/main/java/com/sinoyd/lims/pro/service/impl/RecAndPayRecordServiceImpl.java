package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.pro.criteria.RecAndPayRecordCriteria;
import com.sinoyd.lims.pro.dto.DtoRecAndPayRecord;
import com.sinoyd.lims.pro.repository.RecAndPayRecordRepository;
import com.sinoyd.lims.pro.dto.DtoOrderContract;
import com.sinoyd.lims.pro.repository.OrderContractRepository;
import com.sinoyd.lims.pro.service.RecAndPayRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 收付款记录接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2019-05-10
 * @since V100R001
 */
@Service
public class RecAndPayRecordServiceImpl extends
        BaseJpaServiceImpl<DtoRecAndPayRecord, String, RecAndPayRecordRepository> implements RecAndPayRecordService {

    private OrderContractRepository orderContractRepository;

    private PersonService personService;

    @Override
    public void findByPage(PageBean pageBean, BaseCriteria criteria) {
        //分页查询
        pageBean.setEntityName("DtoRecAndPayRecord p,DtoOrderContract o");
        pageBean.setSelect("select p");
        super.findByPage(pageBean, criteria);
        //拓展关联数据
        List<DtoRecAndPayRecord> list = pageBean.getData();
        List<String> orderContractIdList = list.stream().map(DtoRecAndPayRecord::getContractId).distinct().collect(Collectors.toList());
        List<DtoOrderContract> dtoOrderContractList = orderContractIdList.isEmpty() ? new ArrayList<>() : orderContractRepository.findAll(orderContractIdList);
        List<DtoPerson> people = personService.findAll();
        //填充列表附加数据
        for (DtoRecAndPayRecord recAndPayRecord : list) {
            DtoOrderContract dtoOrderContract = dtoOrderContractList.stream().filter(c -> c.getId().equals(recAndPayRecord.getContractId())).findFirst().orElse(null);
            if (dtoOrderContract != null) {
                //合同名称 合同编号
                recAndPayRecord.setContractCode(dtoOrderContract.getContractCode());
                recAndPayRecord.setContractName(dtoOrderContract.getContractName());
                recAndPayRecord.setFirstEntName(dtoOrderContract.getFirstEntName());
                //业务员
                List<String> signPersonIds = Arrays.asList(dtoOrderContract.getSignPersonId().split(","));
                String signPerson = people.stream().filter(p -> signPersonIds.contains(p.getId())).map(DtoPerson::getCName).collect(Collectors.joining(","));
                recAndPayRecord.setSalesPersonName(signPerson);
            }
        }
        pageBean.setData(list);
    }

    @Override
    public Map<String, Object> analyzeProfileData(PageBean<DtoRecAndPayRecord> page, BaseCriteria criteria) {
        //结果容器
        Map<String, Object> resMap = new HashMap<>();
        //原始数据集
        page.setRowsPerPage(9999);
        findByPage(page, criteria);
        List<DtoRecAndPayRecord> origeinDataList = page.getData();
        List<String> orderContractIdList = origeinDataList.stream().map(DtoRecAndPayRecord::getContractId).distinct().collect(Collectors.toList());
        List<DtoOrderContract> dtoOrderContractList = orderContractIdList.isEmpty() ? new ArrayList<>() : orderContractRepository.findAll(orderContractIdList);
        //开票还是到款
        RecAndPayRecordCriteria recAndPayRecordCriteria = (RecAndPayRecordCriteria) criteria;
        boolean isInvoice = recAndPayRecordCriteria.getIsInvoice();
        //列表汇总
        calProfileData(resMap, origeinDataList, dtoOrderContractList);
        //每月数据
        calPerMonthData(resMap, origeinDataList, isInvoice);
        return resMap;
    }

    @Override
    public List<DtoRecAndPayRecord> findByContractIds(Collection<String> contractIds) {
        return repository.findAllByContractIdIn(contractIds);
    }


    /**
     * 计算汇总数据
     *
     * @param resMap               结果容器
     * @param origeinDataList      原始数据集
     * @param dtoOrderContractList 合同数据
     */
    private void calProfileData(Map<String, Object> resMap, List<DtoRecAndPayRecord> origeinDataList, List<DtoOrderContract> dtoOrderContractList) {
        //开票
        BigDecimal invoiceTotal = new BigDecimal("0.00");
        //收款
        BigDecimal recTotal = new BigDecimal("0.00");
        //坏账
        BigDecimal badTotal = new BigDecimal("0.00");
        for (DtoRecAndPayRecord record : origeinDataList) {
            //BUG2023121298916 开票的金额不应该算到开票总额
            if (Boolean.TRUE.equals(record.getHasInvoice())) {
                invoiceTotal = invoiceTotal.add(record.getAmount());
            }
            if (EnumLIM.EnumMoneyType.收款.getValue().equals(record.getMoneyType()) && Boolean.TRUE.equals(record.getIsReceive())) {
                recTotal = recTotal.add(record.getAmount());
            }
            if (EnumLIM.EnumMoneyType.坏账.getValue().equals(record.getMoneyType())) {
                badTotal = badTotal.add(record.getAmount());
            }
        }
        //合同总额
        BigDecimal htTotal = new BigDecimal("0.00");
        for (DtoOrderContract orderContract : dtoOrderContractList) {
            htTotal = htTotal.add(orderContract.getTotalAmount());
        }
        resMap.put("invoiceTotal", invoiceTotal.doubleValue());
        resMap.put("recTotal", recTotal.doubleValue());
        resMap.put("badTotal", badTotal.doubleValue());
        resMap.put("contractTotal", htTotal.doubleValue());
    }

    /**
     * 分组计算每月数据
     *
     * @param resMap          结果容器
     * @param origeinDataList 原始数据
     * @param isInvoice       开票还是到款
     */
    private void calPerMonthData(Map<String, Object> resMap, List<DtoRecAndPayRecord> origeinDataList, boolean isInvoice) {
        if (isInvoice) {
            origeinDataList.forEach(o -> o.setBelongMonth(DateUtil.dateToString(o.getInvoiceDate(), "yyyy.MM")));
        } else {
            origeinDataList.forEach(o -> o.setBelongMonth(DateUtil.dateToString(o.getReceiveDate(), "yyyy.MM")));
        }
        Map<String, List<DtoRecAndPayRecord>> groupMap = origeinDataList.stream().collect(Collectors.groupingBy(DtoRecAndPayRecord::getBelongMonth));
        Map<String, Object> perMonthRecMap = new HashMap<>();
        for (Map.Entry<String, List<DtoRecAndPayRecord>> entry : groupMap.entrySet()) {
            List<DtoRecAndPayRecord> monthRec = entry.getValue();
            BigDecimal monthRecAmount = new BigDecimal("0.00");
            for (DtoRecAndPayRecord rec : monthRec) {
                monthRecAmount = monthRecAmount.add(rec.getAmount());
            }
            perMonthRecMap.put(entry.getKey(), monthRecAmount.doubleValue());
        }
        resMap.put("perMonthData", perMonthRecMap);
    }

    @Override
    public DtoRecAndPayRecord findAttachPath(String id) {
        return repository.findOne(id);
    }


    @Autowired
    public void setOrderContractRepository(OrderContractRepository orderContractRepository) {
        this.orderContractRepository = orderContractRepository;
    }

    @Autowired
    @Lazy
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }
}