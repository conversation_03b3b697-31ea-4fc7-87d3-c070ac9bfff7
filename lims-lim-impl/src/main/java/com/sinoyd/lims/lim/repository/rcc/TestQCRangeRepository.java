package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoTestQCRange;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * TestQCRange数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/10/8
 * @since V100R001
 */
public interface TestQCRangeRepository extends IBaseJpaPhysicalDeleteRepository<DtoTestQCRange, String> {

    /**
     * 根据testIds数组获取相关
     *
     * @param testIds 测试项目
     * @return DtoTestQCRange集合
     */
    @Query("select p from DtoTestQCRange p where p.testId in :testIds")
    List<DtoTestQCRange> getByTestIds(@Param("testIds") List<String> testIds);


    /**
     * 根据testId获取相关
     *
     * @param testId 测试项目id
     * @return DtoTestQCRange集合
     */
    @Query("select p from DtoTestQCRange p where p.testId = :testId")
    List<DtoTestQCRange> getByTestId(@Param("testId") String testId);


    /**
     * 根据testId删除
     *
     * @param testIds
     * @return 待删除的质控限值
     */
    @Transactional
    @Modifying
    @Query("delete from DtoTestQCRange d  where  d.testId in :testIds")
    Integer clearAll(@Param("testIds") List<String> testIds);


    /**
     * 根据测试项目id及质控类型获取先那个要的质控限值
     *
     * @param testId 测试项目id
     * @param qcType 质控类型
     * @return 返回相应的质控限值
     */
    List<DtoTestQCRange> findByTestIdAndQcType(String testId, Integer qcType);

    /**
     * 根据测试项目id获取质控限值
     *
     * @param testIds 测试项目id
     * @return 返回相应的质控限值
     */
    List<DtoTestQCRange> findByTestIdIn(List<String> testIds);

}