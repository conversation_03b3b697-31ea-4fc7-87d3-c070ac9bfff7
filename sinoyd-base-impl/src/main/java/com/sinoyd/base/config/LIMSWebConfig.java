package com.sinoyd.base.config;

import com.sinoyd.base.interceptor.AvoidResubmitInterceptor;
import com.sinoyd.boot.common.config.WebConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

/**
 * web配置类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/10/31
 **/
@Configuration
public class LIMSWebConfig extends WebMvcConfigurerAdapter {

    private AvoidResubmitInterceptor avoidResubmitInterceptor;


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(avoidResubmitInterceptor).addPathPatterns("/**");
        super.addInterceptors(registry);
    }

    @Autowired
    public void setAvoidResubmitInterceptor(AvoidResubmitInterceptor avoidResubmitInterceptor) {
        this.avoidResubmitInterceptor = avoidResubmitInterceptor;
    }
}
