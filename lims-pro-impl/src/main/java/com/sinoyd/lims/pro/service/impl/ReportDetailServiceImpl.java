package com.sinoyd.lims.pro.service.impl;

import com.jsoniter.output.JsonStream;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.lims.DocumentRepository;
import com.sinoyd.base.repository.lims.InstrumentRepository;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoAnalyzeCertHistory;
import com.sinoyd.lims.lim.dto.customer.DtoInstrumentHistory;
import com.sinoyd.lims.lim.dto.customer.DtoSamplingCertHistory;
import com.sinoyd.lims.lim.dto.lims.*;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.*;
import com.sinoyd.lims.lim.repository.rcc.AnalyzeMethodRepository;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoReportDetailQuery;
import com.sinoyd.lims.pro.dto.customer.DtoSampleTemp;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumCertificateType;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumMonitorType;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumSampleStatus;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.ProService;
import com.sinoyd.lims.pro.service.ReportDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;


/**
 * 报告明细操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/12
 * @since V100R001
 */
@Service
@Slf4j
public class ReportDetailServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoReportDetail, String, ReportDetailRepository> implements ReportDetailService {
    @Autowired
    private ReportRepository reportRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    @Lazy
    private ProService proService;

    @Autowired
    private SampleRepository sampleRepository;

    @Autowired
    private ReceiveSampleRecordRepository receiveSampleRecordRepository;

    private AnalyseDataRepository analyseDataRepository;

    private TestService testService;

    @Autowired
    private PersonAbilityRepository personAbilityRepository;

    @Autowired
    private PersonCertRepository personCertRepository;

    @Autowired
    private InstrumentUseRecord2SampleRepository instrumentUseRecord2SampleRepository;

    @Autowired
    private InstrumentUseRecordRepository instrumentUseRecordRepository;

    @Autowired
    private DocumentRepository documentRepository;

    @Autowired
    private CertHistoryFileRepository certHistoryFileRepository;

    @Autowired
    private CertHistoryInfoRepository certHistoryInfoRepository;

    @Autowired
    private PersonRepository personRepository;

    @Autowired
    private SampleTypeRepository sampleTypeRepository;

    @Autowired
    private AnalyzeMethodRepository analyzeMethodRepository;

    @Autowired
    private InstrumentRepository instrumentRepository;

    @Autowired
    @Lazy
    private DocumentService documentService;

    @Autowired
    private ParamsDataRepository paramsDataRepository;

    @Override
    public void findByPage(PageBean<DtoReportDetail> pb, BaseCriteria reportDetailCriteria) {
        pb.setEntityName("DtoReportDetail a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, reportDetailCriteria);
    }

    /**
     * 查询样品
     *
     * @param queryDto 查询dto
     */
    @Override
    public List<DtoSampleTemp> query(DtoReportDetailQuery queryDto) {
        Map<String, DtoSampleTemp> samMap = querySample(queryDto);
        List<DtoSampleTemp> samList = new ArrayList<>(samMap.values());
        samList.sort(Comparator.comparing(DtoSampleTemp::getStatus).thenComparing(DtoSampleTemp::getCode));
        return samList;
    }

    /**
     * 查询样品
     *
     * @param queryDto 查询dto
     */
    @Override
    public List<DtoSampleTemp> queryMergeItemName(DtoReportDetailQuery queryDto) {
        Map<String, DtoSampleTemp> samMap = querySample(queryDto);
        List<DtoSampleTemp> samList = mergeItemName(samMap);
        samList.sort(Comparator.comparing(DtoSampleTemp::getStatus).thenComparing(DtoSampleTemp::getCode));
        return samList;
    }

    /**
     * 查询样品
     *
     * @param queryDto 查询条件对象
     * @return 样品列表
     */
    private Map<String, DtoSampleTemp> querySample(DtoReportDetailQuery queryDto) {
        /*
        新增报告时，关联样品默认勾选规则：样品检毕但未编制过报告的样品
        样品已检毕的样品能够选择，未检毕的样品无法勾选，
        能够通过状态筛选样品是否已在其他报告中出具（未出证、已出证），默认为未出证。
        默认排序：先检毕的然后再显示未检毕的。
        */
        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.pro.dto.customer.DtoSampleTemp(");
        stringBuilder.append("s.id,s.code,s.sampleTypeId,st.typeName,s.redFolderName,s.samplingTimeBegin,s.redAnalyzeItems,s.samplingStatus,s.status,s.projectId,s.receiveId)");
        stringBuilder.append(" from DtoSample s, DtoSampleType st where 1=1");
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            stringBuilder.append(" and s.orgId = :orgId");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        stringBuilder.append(" and s.isDeleted = 0");
        stringBuilder.append(" and s.sampleTypeId = st.id");
        stringBuilder.append(" and s.projectId = :projectId");
        if (StringUtil.isNotEmpty(queryDto.getSampleTypeId()) && !UUIDHelper.GUID_EMPTY.equals(queryDto.getSampleTypeId())) {
            stringBuilder.append(" and s.sampleTypeId = :sampleTypeId");
            values.put("sampleTypeId", queryDto.getSampleTypeId());
        }
        if (StringUtil.isNotEmpty(queryDto.getKey())) {
            stringBuilder.append(" and (s.code like :key or s.redFolderName like :key or s.inspectedEnt like :key)");
            values.put("key", "%" + queryDto.getKey() + "%");
        }

        values.put("projectId", queryDto.getProjectId());
        List<DtoSampleTemp> sampleList = comRepository.find(stringBuilder.toString(), values);
        //设置项目编号
        List<String> projectIdList = sampleList.stream().map(DtoSampleTemp::getProjectId).distinct().collect(Collectors.toList());
        List<DtoProject> projectList = StringUtil.isNotEmpty(projectIdList) ? projectRepository.findAll(projectIdList) : new ArrayList<>();
        Map<String, DtoProject> projectMap = projectList.stream().collect(Collectors.toMap(DtoProject::getId, dto -> dto));
        List<String> receiveIds = sampleList.stream().map(DtoSampleTemp::getReceiveId).distinct().collect(Collectors.toList());
        List<DtoReceiveSampleRecord> receiveSampleRecords = StringUtil.isNotEmpty(receiveIds) ?
                receiveSampleRecordRepository.findAll(receiveIds) : new ArrayList<>();
        Map<String, DtoReceiveSampleRecord> receiveSampleRecordMap = receiveSampleRecords.stream().collect(Collectors.toMap(DtoReceiveSampleRecord::getId, p -> p));
        for (DtoSampleTemp sampleTemp : sampleList) {
            if (projectMap.containsKey(sampleTemp.getProjectId())) {
                sampleTemp.setProjectCode(projectMap.get(sampleTemp.getProjectId()).getProjectCode());
            }
            // 送样单字段
            if (receiveSampleRecordMap.containsKey(sampleTemp.getReceiveId())){
                DtoReceiveSampleRecord receiveSampleRecord = receiveSampleRecordMap.get(sampleTemp.getReceiveId());
                sampleTemp.setInfoStatus(receiveSampleRecord.getInfoStatus());
                sampleTemp.setCheckerId(receiveSampleRecord.getCheckerId());
            }
        }
        sampleList = addPxSampleList(sampleList);

        //若传了报告id，则表明查询该报告下的关联样品或项目下默认可关联的样品，且需显示指标变动信息
        List<DtoReport> reports = new ArrayList<>();
        if (StringUtils.isNotNullAndEmpty(queryDto.getReportId())) {
            if (UUIDHelper.GUID_EMPTY.equals(queryDto.getReportId())) {
                //传了空报告id的代表新增报告，过滤已检毕但未出证的样品
                queryDto.setCertificateStatus(EnumCertificateType.未出证.getValue());
                queryDto.setStatus(EnumMonitorType.已检毕.getValue());
                reports = reportRepository.findByProjectId(queryDto.getProjectId());
            } else {
                //传了对应报告id，过滤为已出证（即该报告下）的样品
                queryDto.setCertificateStatus(EnumCertificateType.已出证.getValue());
                queryDto.getReportIds().add(queryDto.getReportId());
            }
        } else {//否则查询待选样品
            reports = reportRepository.findByProjectId(queryDto.getProjectId());
        }

        if (reports.size() > 0) {
            queryDto.getReportIds().addAll(reports.stream().map(DtoReport::getId).collect(Collectors.toList()));
        }

        //将list转为map
        Map<String, DtoSampleTemp> samMap = sampleList.stream().collect(Collectors.toMap(DtoSampleTemp::getId, sam -> sam));
        this.filter(samMap, queryDto);
        return samMap;
    }

    /**
     * 样品分析项目名称按总称合并
     *
     * @param smpMap 样品映射
     * @return 样品列表
     */
    private List<DtoSampleTemp> mergeItemName(Map<String, DtoSampleTemp> smpMap) {
        System.out.println("...................." + System.currentTimeMillis());
        log.error("==================项目名称按总称合并开始=============");
        List<String> idList = new ArrayList<>(smpMap.keySet());
        log.error("==================key值：" + String.join("；", smpMap.keySet()) + "=============");
        List<DtoSampleTemp> samList = new ArrayList<>(smpMap.values());
        log.error("==================value值：" + smpMap.values().stream().map(DtoSampleTemp::getId).collect(Collectors.joining("；")) + "=============");
        if (StringUtil.isNotEmpty(idList)) {
            String sql = "select a.sampleId, a.testId from DtoAnalyseData a where a.sampleId in :ids and a.isDeleted = 0";
            List<Object[]> objectList = comRepository.find(sql, Collections.singletonMap("ids", idList));
            if (StringUtil.isNotEmpty(objectList)) {
                try {
                    List<String> testIdList = objectList.stream().map(p -> p[1].toString()).distinct().collect(Collectors.toList());
                    log.error("==================测试项目ids：" + String.join("；", testIdList) + "=============");
                    List<DtoTest> testList = testService.findRedisByIds(testIdList);
                    List<String> parentTestIdList = testList.stream().map(DtoTest::getParentId).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
                    log.error("==================测试项目parentIds：" + String.join("；", parentTestIdList) + "=============");
                    List<DtoTest> parentTestList = StringUtil.isNotEmpty(parentTestIdList) ? testService.findRedisByIds(parentTestIdList) : new ArrayList<>();
                    Map<String, DtoTest> parentTestMap = parentTestList.stream().collect(Collectors.toMap(DtoTest::getId, dto -> dto));
                    Map<String, List<Object[]>> objectMap = objectList.stream().collect(Collectors.groupingBy(p -> p[0].toString()));
                    for (DtoSampleTemp sam : samList) {
                        if (objectMap.containsKey(sam.getId())) {
                            List<Object[]> objects = objectMap.get(sam.getId());
                            List<String> testIdForSmp = objects.stream().map(p -> p[1].toString()).distinct().collect(Collectors.toList());
                            List<DtoTest> testForSmp = testList.stream().filter(p -> testIdForSmp.contains(p.getId())).collect(Collectors.toList());
                            if (StringUtil.isNotEmpty(testForSmp)) {
                                Map<String, List<DtoTest>> testMapForSmp = testForSmp.stream().collect(Collectors.groupingBy(DtoTest::getParentId));
                                List<DtoTest> groupTestForSmp = new ArrayList<>();
                                for (Map.Entry<String, List<DtoTest>> entry : testMapForSmp.entrySet()) {
                                    List<DtoTest> loopTestList = entry.getValue();
                                    DtoTest parentTest = parentTestMap.get(entry.getKey());
                                    if (StringUtil.isNotNull(parentTest) && parentTest.getIsTotalTest() && loopTestList.size() > parentTest.getMergeBase()) {
                                        groupTestForSmp.add(parentTest);
                                    } else {
                                        groupTestForSmp.addAll(loopTestList);
                                    }
                                }
                                // 非认证认可的测试项目列表中标红显示
                                groupTestForSmp = groupTestForSmp.stream().peek(p -> {
                                    if (p.getCert().equals(EnumLIM.EnumTestCert.非认证认可.getValue())) {
                                        p.setRedAnalyzeItemName(p.getRedAnalyzeItemName() + "##red");
                                    }
                                }).collect(Collectors.toList());
                                List<String> itemNameForSmp = groupTestForSmp.stream().map(DtoTest::getRedAnalyzeItemName).collect(Collectors.toList());
                                sam.setRedAnalyzeItems(String.join(",", itemNameForSmp));
                            }
                        }
                    }
                } catch (Exception ex) {
                    log.error("==================报错信息：" + ex.getMessage() + "=============");
                }
            }
        }
        System.out.println("...................." + System.currentTimeMillis());
        return samList;
    }

    /**
     * 查询样品--报告关联样品
     *
     * @param queryDto 查询dto
     */
    @Override
    public List<DtoSampleTemp> queryReport(DtoReportDetailQuery queryDto) {

        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.pro.dto.customer.DtoSampleTemp(");
        stringBuilder.append("s.id,s.code,s.sampleTypeId,st.typeName,s.redFolderName,s.samplingTimeBegin,s.redAnalyzeItems,s.samplingStatus,s.status,s.projectId)");
        stringBuilder.append(" from DtoSample s, DtoSampleType st, DtoReportDetail rd where 1=1");
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            stringBuilder.append(" and s.orgId = :orgId");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        stringBuilder.append(" and s.isDeleted = 0");
        stringBuilder.append(" and s.id = rd.objectId ");
        stringBuilder.append(" and s.sampleTypeId = st.id");
        stringBuilder.append(" and rd.reportId = :reportId");

        values.put("reportId", queryDto.getReportId());
        List<DtoSampleTemp> sampleList = comRepository.find(stringBuilder.toString(), values);
        List<String> proIds = sampleList.stream().map(DtoSampleTemp::getProjectId).distinct().collect(Collectors.toList());
        List<DtoProject> projectList = new ArrayList<>();
        if (StringUtil.isNotEmpty(proIds)) {
            projectList = projectRepository.findAll(proIds);
        }
        List<String> samIds = sampleList.stream().map(DtoSampleTemp::getId).distinct().collect(Collectors.toList());
        List<DtoSample> samples = sampleRepository.findAll(samIds);
        List<String> recIds = samples.stream().map(DtoSample::getReceiveId).distinct().collect(Collectors.toList());
        //嘉兴增加了平行样，需要查到对应平行样的项目编号
        List<DtoReceiveSampleRecord> receiveSampleRecordList = new ArrayList<>();
        if (StringUtil.isNotEmpty(recIds)) {
            receiveSampleRecordList = receiveSampleRecordRepository.findAll(recIds);
        }
        for (DtoSampleTemp sam : sampleList) {
            DtoProject project = projectList.stream().filter(p -> p.getId().equals(sam.getProjectId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(project)) {
                sam.setProjectCode(project.getProjectCode());
            } else {
                //找到对应的样品
                DtoSample sample = samples.stream().filter(p -> p.getId().equals(sam.getId())).findFirst().orElse(null);
                if (StringUtil.isNotNull(sample)) {
                    //找到送样单
                    DtoReceiveSampleRecord record = receiveSampleRecordList.stream().filter(p -> p.getId().equals(sample.getReceiveId())).findFirst().orElse(null);
                    //找到对应送样单的项目
                    if (StringUtil.isNotNull(record)) {
                        project = projectList.stream().filter(p -> p.getId().equals(record.getProjectId())).findFirst().orElse(null);
                        if (StringUtil.isNotNull(project)) {
                            sam.setProjectCode(project.getProjectCode());
                        }
                    }
                }
            }
        }
        Map<String, DtoSampleTemp> samMap = sampleList.stream().collect(Collectors.toMap(DtoSampleTemp::getId, sam -> sam));
        sampleList = mergeItemName(samMap);
        sampleList.sort(Comparator.comparing(DtoSampleTemp::getStatus).thenComparing(DtoSampleTemp::getCode));
        return sampleList;
    }

    @Transactional
    @Override
    public List<DtoReportDetail> batchUpdate(DtoReport report) {
        DtoReport dtoReport = reportRepository.findOne(report.getId());
        if (StringUtil.isNull(dtoReport)) {
            throw new BaseException("报告不存在!");
        }
        List<DtoReportDetail> oldDetails = repository.findByReportId(report.getId());
        List<String> oldObjectIds = oldDetails.stream().map(DtoReportDetail::getObjectId).collect(Collectors.toList());
        List<String> newObjectIds = new ArrayList<>(report.getObjectIds());
        //新加的关联id，用于插入
        newObjectIds.removeAll(oldObjectIds);
        //保留下的老的关联id，用于移除（现在选取报告不移除原有报告中的样品 2021-12-21）
        //oldObjectIds.removeAll(report.getObjectIds());
        StringBuilder builder = new StringBuilder();
        List<DtoReportDetail> resList = new ArrayList<>();
        if (newObjectIds.size() > 0) {
            List<DtoSample> samples = sampleRepository.findAll(newObjectIds);
            builder.append("</br>添加样品关联:").append(String.join("、", samples.stream().map(DtoSample::getCode).collect(Collectors.toList())));
            List<DtoReportDetail> details = new ArrayList<>();
            for (String objectId : newObjectIds) {
                DtoReportDetail detail = new DtoReportDetail();
                detail.setObjectId(objectId);
                detail.setReportId(report.getId());
                detail.setObjectType(report.getObjectType());
                details.add(detail);
            }
            resList = super.save(details);
            //异步记录证书历史信息
            CurrentPrincipalUser user = PrincipalContextUser.getPrincipal();
            UsernamePasswordAuthenticationToken token = new UsernamePasswordAuthenticationToken(user, null);
            CompletableFuture.runAsync(() -> {
                SecurityContextHolder.getContext().setAuthentication(token);
                createCertHistoryInfo(dtoReport.getProjectId(), newObjectIds);
            });
        }
        //现在选取报告不移除原有报告中的样品 2021-12-21
//        if (oldObjectIds.size() > 0) {
//            List<DtoSample> samples = sampleRepository.findAll(oldObjectIds);
//            builder.append("</br>删除样品关联:").append(String.join("、", samples.stream().map(DtoSample::getCode).collect(Collectors.toList())));
//            oldDetails = oldDetails.stream().filter(p -> oldObjectIds.contains(p.getObjectId())).collect(Collectors.toList());
//            super.delete(oldDetails);
//        }

        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumPRO.EnumProAction.报告关联样品, dtoReport.getProjectId());
                    }
                }
        );

        return resList;

    }

    @Transactional
    @Override
    public int DeleteByRepIdTypeAndObjIds(DtoReport report) {
        DtoReport dtoReport = reportRepository.findOne(report.getId());
        if (StringUtil.isNull(dtoReport)) {
            throw new BaseException("报告不存在！");
        }
        if (StringUtil.isNull(report.getObjectType())) {
            throw new BaseException("报告类型不存在！");
        }
        List<String> delObjectIds = report.getObjectIds();
        if (StringUtil.isEmpty(delObjectIds)) {
            return 0;
        }
        int delCnt = repository.deleteByReportIdAndObjectTypeAndObjectIdIn(report.getId(), report.getObjectType(), delObjectIds);
        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumPRO.EnumProAction.报告关联样品, dtoReport.getProjectId());
                    }
                }
        );
        return delCnt;
    }

    /**
     * 退回送样单判断样品
     *
     * @param recIds 送样单ids
     * @param isPass 是否判断
     * @return 判断样品
     */
    @Override
    public Boolean isBackSampleByReceiveIds(List<String> recIds, Boolean isPass) {
        Boolean isBack = Boolean.FALSE;
        if (Boolean.FALSE.equals(isPass)) {
            // 判断所有送样单状态是否为数据已确认
            List<DtoReceiveSampleRecord> receiveSampleRecords = StringUtil.isNotEmpty(recIds) ? receiveSampleRecordRepository.findAll(recIds) : new ArrayList<>();
            if (receiveSampleRecords.stream().anyMatch(p -> EnumPRO.EnumReceiveInfoStatus.已确认.getValue().equals(p.getInfoStatus()))) {
                List<String> samIds = sampleRepository.findByReceiveIdIn(recIds).stream().map(DtoSample::getId)
                        .collect(Collectors.toList());
                isBack = (repository.countByObjectIdIn(samIds) > 0);
            }
        }
        return isBack;
    }

    /**
     * 退回工作单判断样品
     *
     * @param workSheetFolderIds 工作单ids
     * @return 判断样品
     */
    @Override
    public Boolean isBackSampleByWorkSheetFolderIds(List<String> workSheetFolderIds) {
        List<DtoAnalyseData> anaList = analyseDataRepository.findByWorkSheetFolderIdIn(workSheetFolderIds);
        Set<String> samIds = anaList.stream().map(DtoAnalyseData::getSampleId).collect(Collectors.toSet());
        return repository.countByObjectIdIn(samIds) > 0;
    }

    /**
     * 添加平行样显示
     *
     * @param sampleList 原样列表
     * @return 显示样品列表
     */
    protected List<DtoSampleTemp> addPxSampleList(List<DtoSampleTemp> sampleList) {
        return sampleList;
    }

    /**
     * 过滤样品
     *
     * @param samMap   样品Map
     * @param queryDto 查询结构体
     */
    private void filter(Map<String, DtoSampleTemp> samMap, DtoReportDetailQuery queryDto) {
        List<DtoReportDetail> reportDetails = repository.findByReportIdIn(queryDto.getReportIds());
        Set<String> objectIds = reportDetails.stream().map(DtoReportDetail::getObjectId).collect(Collectors.toSet());
        List<String> removeIds = new ArrayList<>();
        for (String sampleId : samMap.keySet()) {
            //出证赋值
            samMap.get(sampleId).setCertificateStatus(objectIds.contains(sampleId) ? EnumCertificateType.已出证.toString() : EnumCertificateType.未出证.toString());
            //是否检毕赋值
            samMap.get(sampleId).setStatus(samMap.get(sampleId).getStatus().equals(EnumSampleStatus.样品检毕.toString()) ? EnumMonitorType.已检毕.toString() : EnumMonitorType.未检毕.toString());
            //传了报告id的需默认勾选
            samMap.get(sampleId).setIsChecked(StringUtils.isNotNullAndEmpty(queryDto.getReportId()));
            DtoSampleTemp sam = samMap.get(sampleId);
            if (StringUtil.isNotNull(queryDto.getCertificateStatus())) {
                if ((queryDto.getCertificateStatus().equals(EnumCertificateType.未出证.getValue()) && sam.getCertificateStatus().equals(EnumCertificateType.已出证.toString())) ||
                        (queryDto.getCertificateStatus().equals(EnumCertificateType.已出证.getValue()) && sam.getCertificateStatus().equals(EnumCertificateType.未出证.toString()))) {
                    removeIds.add(sampleId);
                    continue;
                }
            }
            if (StringUtil.isNotNull(queryDto.getStatus())) {
                if ((queryDto.getStatus().equals(EnumMonitorType.未检毕.getValue()) && sam.getStatus().equals(EnumMonitorType.已检毕.toString())) ||
                        (queryDto.getStatus().equals(EnumMonitorType.已检毕.getValue()) && sam.getStatus().equals(EnumMonitorType.未检毕.toString()))) {
                    removeIds.add(sampleId);
                }
            }
        }

        for (String removeId : removeIds) {
            samMap.remove(removeId);
        }
    }

    /**
     * 异步记录证书历史信息
     *
     * @param sampleIds 样品标识
     * @param projectId 项目标识
     */
    private void createCertHistoryInfo(String projectId, List<String> sampleIds) {
        List<DtoSample> samples = sampleRepository.findAll(sampleIds);
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdIn(sampleIds);
        List<DtoParamsData> paramsDataList = paramsDataRepository.findByObjectIdIn(sampleIds);
        List<String> instrumentUseRecordIds = instrumentUseRecord2SampleRepository.findBySampleIdIn(sampleIds)
                .stream().map(DtoInstrumentUseRecord2Sample::getInstrumentUseRecordId).collect(Collectors.toList());
        List<DtoInstrumentUseRecord> instrumentUseRecordList = StringUtil.isNotEmpty(instrumentUseRecordIds) ?
                instrumentUseRecordRepository.findAll(instrumentUseRecordIds) : new ArrayList<>();
        //附件备份  上岗证附件+仪器附件
        List<String> folderIds = new ArrayList<>();
        List<String> analystIds = analyseDataList.stream().map(DtoAnalyseData::getAnalystId).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        List<String> samplingPersonIds = samples.stream().map(DtoSample::getSamplingPersonId).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        List<String> personIds = new ArrayList<>(analystIds);
        personIds.addAll(samplingPersonIds);
        List<DtoPersonCert> personCertList = StringUtil.isNotEmpty(personIds) ? personCertRepository.findByPersonIds(personIds) : new ArrayList<>();
        folderIds.addAll(personCertList.stream().map(DtoPersonCert::getId).collect(Collectors.toList()));
        List<String> instrumentIds = instrumentUseRecordList.stream().map(DtoInstrumentUseRecord::getInstrumentId)
                .filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        folderIds.addAll(instrumentIds);
        List<DtoDocument> documentList = documentRepository.findByFolderIdIn(folderIds);
        List<String> existFolderIds = certHistoryFileRepository.findAll().stream().map(DtoCertHistoryFile::getReferenceId).collect(Collectors.toList());
        List<DtoDocument> waitSaveDocumentList = documentList.stream().filter(v -> !existFolderIds.contains(v.getId())).collect(Collectors.toList());
        List<DtoCertHistoryFile> waitSaveFileList = new ArrayList<>();
        waitSaveDocumentList.forEach(v -> {
            DtoCertHistoryFile historyFile = new DtoCertHistoryFile();
            historyFile.setReferenceId(v.getId());
            waitSaveFileList.add(historyFile);
        });
        certHistoryFileRepository.save(waitSaveFileList);
        waitSaveFileList.forEach(v -> documentService.copyFile("certHistory", v.getId(), v.getReferenceId()));
        //历史信息存储
        List<DtoCertHistoryInfo> waitSaveHistoryInfoList = new ArrayList<>();
        List<DtoSampleType> sampleTypeList = sampleTypeRepository.findAll();
        List<DtoPerson> personList = personRepository.findAll();
        List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).collect(Collectors.toList());
        List<DtoTest> testList = StringUtil.isNotEmpty(testIds) ? testService.findAll(testIds) : new ArrayList<>();
        List<DtoAnalyzeMethod> analyzeMethodList = analyzeMethodRepository.findAll();
        List<String> samplingMethodNames = paramsDataList.stream().filter(v -> "采样方法及依据".equals(v.getParamsName())).map(DtoParamsData::getParamsValue).collect(Collectors.toList());
        List<String> samplingMethodIds = analyzeMethodList.stream().filter(v -> samplingMethodNames.contains(v.getMethodName())).map(DtoAnalyzeMethod::getId).collect(Collectors.toList());
        List<DtoCertHistoryFile> certHistoryFileList = certHistoryFileRepository.findAll();
        List<DtoPersonAbility> abilityList = personAbilityRepository.findByPersonIdIn(personIds);
        Date now = new Date();
        if (StringUtil.isNotEmpty(abilityList)) {
            //采样能力
            //样品上类型是小类,现在采样检测能力上是大类
            List<String> sampleTypeIds = samples.stream().map(DtoSample::getSampleTypeId).collect(Collectors.toList());
            List<String> bigTypeIds = sampleTypeList.stream().filter(v -> sampleTypeIds.contains(v.getId())).map(DtoSampleType::getParentId).collect(Collectors.toList());
            abilityList.stream().filter(v -> EnumLIM.EnumPersonCertType.采样.getValue().equals(v.getAbilityType()) && bigTypeIds.contains(v.getSampleTypeId())
                    && samplingMethodIds.contains(v.getSamplingMethodId()))
                    .forEach(v -> {
                        DtoSampleType sampleType = sampleTypeList.stream().filter(s -> s.getId().equals(v.getSampleTypeId())).findFirst().orElse(null);
                        DtoPerson person = personList.stream().filter(s -> s.getId().equals(v.getPersonId())).findFirst().orElse(null);
                        DtoPersonCert personCert = personCertList.stream().filter(s -> s.getId().equals(v.getPersonCertId())).findFirst().orElse(null);
                        DtoAnalyzeMethod analyzeMethod = analyzeMethodList.stream().filter(s -> s.getId().equals(v.getSamplingMethodId())).findFirst().orElse(null);
                        if (sampleType != null && person != null && personCert != null && analyzeMethod != null) {
                            DtoCertHistoryInfo historyInfo = new DtoCertHistoryInfo();
                            historyInfo.setInfoType(EnumLIM.EnumCertHistoryInfoType.采样人员持证信息.getValue());
                            DtoSamplingCertHistory detail = new DtoSamplingCertHistory();
                            detail.setSampleType(sampleType.getTypeName());
                            detail.setSamplingPeople(person.getCName());
                            detail.setItem(v.getRedAnalyzeItemName());
                            detail.setMethod(analyzeMethod.getMethodName());
                            detail.setStandardNo(analyzeMethod.getCountryStandard());
                            detail.setCertCode(personCert.getCertCode());
                            detail.setCertEffectiveTime(DateUtil.dateToString(v.getCertEffectiveTime(), DateUtil.YEAR));
                            boolean isEffective = DateUtil.dateToString(now, DateUtil.YEAR).equals(detail.getCertEffectiveTime())
                                    || now.before(DateUtil.stringToDate(detail.getCertEffectiveTime(), DateUtil.YEAR));
                            detail.setStatus(isEffective ? "正常" : "已过期");
                            List<String> documentIds = documentList.stream().filter(s -> personCert.getId().equals(s.getFolderId()))
                                    .map(DtoDocument::getId).collect(Collectors.toList());
                            detail.setHistoryFileIds(certHistoryFileList.stream().filter(s -> documentIds.contains(s.getReferenceId()))
                                    .map(DtoCertHistoryFile::getId).collect(Collectors.toList()));
                            historyInfo.setDetail(JsonStream.serialize(detail));
                            waitSaveHistoryInfoList.add(historyInfo);
                        }
                    });
            //分析能力
            abilityList.stream().filter(v -> EnumLIM.EnumPersonCertType.分析.getValue().equals(v.getAbilityType()) && testIds.contains(v.getTestId()))
                    .forEach(v -> {
                        DtoPerson person = personList.stream().filter(s -> s.getId().equals(v.getPersonId())).findFirst().orElse(null);
                        DtoPersonCert personCert = personCertList.stream().filter(s -> s.getId().equals(v.getPersonCertId())).findFirst().orElse(null);
                        DtoTest test = testList.stream().filter(s -> s.getId().equals(v.getTestId())).findFirst().orElse(null);
                        DtoSampleType sampleType = sampleTypeList.stream().filter(s -> s.getId().equals(test.getSampleTypeId())).findFirst().orElse(null);
                        if (sampleType != null && person != null && personCert != null && test != null) {
                            DtoCertHistoryInfo historyInfo = new DtoCertHistoryInfo();
                            historyInfo.setInfoType(EnumLIM.EnumCertHistoryInfoType.分析人员持证信息.getValue());
                            DtoAnalyzeCertHistory detail = new DtoAnalyzeCertHistory();
                            detail.setSampleType(sampleType.getTypeName());
                            detail.setAnalystPeople(person.getCName());
                            detail.setAnalyzeItem(test.getRedAnalyzeItemName());
                            detail.setAnalyzeMethod(test.getRedAnalyzeMethodName());
                            detail.setCountryStandard(test.getRedCountryStandard());
                            detail.setCertCode(personCert.getCertCode());
                            detail.setCertEffectiveTime(DateUtil.dateToString(v.getCertEffectiveTime(), DateUtil.YEAR));
                            boolean isEffective = DateUtil.dateToString(now, DateUtil.YEAR).equals(detail.getCertEffectiveTime())
                                    || now.before(DateUtil.stringToDate(detail.getCertEffectiveTime(), DateUtil.YEAR));
                            detail.setStatus(isEffective ? "正常" : "已过期");
                            List<String> documentIds = documentList.stream().filter(s -> personCert.getId().equals(s.getFolderId()))
                                    .map(DtoDocument::getId).collect(Collectors.toList());
                            detail.setHistoryFileIds(certHistoryFileList.stream().filter(s -> documentIds.contains(s.getReferenceId()))
                                    .map(DtoCertHistoryFile::getId).collect(Collectors.toList()));
                            historyInfo.setDetail(JsonStream.serialize(detail));
                            waitSaveHistoryInfoList.add(historyInfo);
                        }
                    });
            //仪器
            List<DtoInstrument> instrumentList = StringUtil.isNotEmpty(instrumentIds) ? instrumentRepository.findAll(instrumentIds) : new ArrayList<>();
            instrumentList.forEach(v -> {
                DtoCertHistoryInfo historyInfo = new DtoCertHistoryInfo();
                historyInfo.setInfoType(EnumLIM.EnumCertHistoryInfoType.仪器有效期信息.getValue());
                DtoInstrumentHistory detail = new DtoInstrumentHistory();
                detail.setInstrumentsCode(v.getInstrumentsCode());
                detail.setInstrumentName(v.getInstrumentName());
                detail.setSerialNo(v.getSerialNo());
                detail.setModel(v.getModel());
                detail.setManagePeople(v.getManagerName());
                detail.setEffectiveTime(DateUtil.dateToString(v.getOriginEndDate(), DateUtil.YEAR));
                boolean isEffective = DateUtil.dateToString(now, DateUtil.YEAR).equals(DateUtil.dateToString(v.getOriginEndDate(), DateUtil.YEAR))
                        || now.before(v.getOriginEndDate());
                detail.setStatus(isEffective ? "正常" : "已过期");
                List<String> documentIds = documentList.stream().filter(s -> v.getId().equals(s.getFolderId()))
                        .map(DtoDocument::getId).collect(Collectors.toList());
                detail.setHistoryFileIds(certHistoryFileList.stream().filter(s -> documentIds.contains(s.getReferenceId()))
                        .map(DtoCertHistoryFile::getId).collect(Collectors.toList()));
                historyInfo.setDetail(JsonStream.serialize(detail));
                waitSaveHistoryInfoList.add(historyInfo);
            });
        }
        waitSaveHistoryInfoList.forEach(v -> v.setProjectId(projectId));
        certHistoryInfoRepository.save(waitSaveHistoryInfoList);
    }

    @Autowired
    public void setAnalyseDataRepository(AnalyseDataRepository analyseDataRepository) {
        this.analyseDataRepository = analyseDataRepository;
    }

    @Autowired
    @Lazy
    public void setTestService(TestService testService) {
        this.testService = testService;
    }
}