package com.sinoyd.lims.pro.service;

import com.sinoyd.base.dto.vo.OrderReviseVO;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.customer.DtoAnalyseDataTemp;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

/**
 * 样品排序相关业务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/3/4
 */
public interface SampleOrderService {

    /**
     * 初始化样品排序值
     *
     * @param samples   样品集合集合
     * @param tempList 分析数据实体集合
     * @return 排序值 Map<样品id, 排序值>
     */
    Future<Map<String, String>> initSampleOrderNum(List<DtoSample> samples, List<DtoAnalyseDataTemp> tempList, List<OrderReviseVO> orderReviseVOList);

    /**
     * 初始化样品排序值
     *
     * @param mapList   数据集合
     * @param tempList  分析数据实体集合
     * @return 排序值 Map<样品id, 排序值>
     */
    Future<Map<String, String>> generateSampleOrderNum(List<Map<String, Object>> mapList, List<DtoAnalyseDataTemp> tempList, List<OrderReviseVO> orderReviseVOList);
}
