package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.pro.dto.customer.DtoProjectCountStatistic;
import com.sinoyd.lims.pro.dto.customer.DtoProjectSampleCount;
import com.sinoyd.lims.pro.service.ProjectCountStatisticsService;
import com.sinoyd.lims.pro.view.VProjectCountStatistic;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Api(tags = "示例: 项目数量统计服务")
@RestController
@RequestMapping("api/pro/projectCountStatistics")
public class ProjectCountStatisticsController extends ExceptionHandlerController<ProjectCountStatisticsService> {


    @ApiOperation(value = "分页动态条件查询项目数据", notes = "分页动态条件查询项目数据")
    @GetMapping
    public RestResponse<Map<String, Object>> findProjectCountList(@RequestParam("type") Integer type,
                                                                  @RequestParam("startTime") String startTime,
                                                                  @RequestParam("endTime") String endTime) {
        RestResponse<Map<String, Object>> restResponse = new RestResponse<>();
        Map<String, Object> resultMap = service.findProjectCountList(type, startTime, endTime);
        List<DtoProjectCountStatistic> list = (List<DtoProjectCountStatistic>) resultMap.get("detail");
        restResponse.setRestStatus(StringUtil.isEmpty(list) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(resultMap);
        restResponse.setCount(list.size());
        return restResponse;
    }


    @ApiOperation(value = "分页动态条件查询样品数量", notes = "分页动态条件查询样品数量")
    @GetMapping("/sample")
    public RestResponse<List<DtoProjectSampleCount>> findProjectSampleCountList(@RequestParam("type") Integer type,
                                                                                @RequestParam("startTime") String startTime,
                                                                                @RequestParam("endTime") String endTime,
                                                                                @RequestParam("projectTypeId") String projectTypeId) {
        RestResponse<List<DtoProjectSampleCount>> restResponse = new RestResponse<>();
        List<DtoProjectSampleCount> list = service.findProjectSampleCountList(type, startTime, endTime, projectTypeId);
        restResponse.setRestStatus(StringUtil.isEmpty(list) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(list);
        restResponse.setCount(list.size());
        return restResponse;
    }

    /**
     * 根据年份查询每个月的数据数量
     *
     * @param year 采样时间结束时间
     * @return 查询的列表数据
     */
    @ApiOperation(value = "根据年份查询每个月的数据数量", notes = "根据年份查询每个月的数据数量")
    @GetMapping("/count")
    public RestResponse<Map<String, Object>> findDataList(@RequestParam("year") Integer year) {
        RestResponse<Map<String, Object>> restResponse = new RestResponse<>();
        Map<String, Object> resultMap = service.findCountByYear(year);
        restResponse.setRestStatus(StringUtil.isEmpty(resultMap) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(resultMap);
        return restResponse;
    }
}
