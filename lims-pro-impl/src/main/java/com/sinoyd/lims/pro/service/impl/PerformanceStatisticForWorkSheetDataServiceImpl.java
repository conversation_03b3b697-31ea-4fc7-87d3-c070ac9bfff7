package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.factory.quality.QualityBlank;
import com.sinoyd.base.factory.quality.QualityMark;
import com.sinoyd.base.factory.quality.QualityParallel;
import com.sinoyd.base.factory.quality.QualityStandard;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoCurveDetail;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.CurveDetailService;
import com.sinoyd.lims.pro.criteria.PerformanceStatisticForWorkSheetDataCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.pro.service.PerformanceStatisticForWorkSheetDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * PerformanceStatisticForWorkSheetData操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/2/19
 * @since V100R001
 */
 @Service
public class PerformanceStatisticForWorkSheetDataServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoPerformanceStatisticForWorkSheetData,String,PerformanceStatisticForWorkSheetDataRepository> implements PerformanceStatisticForWorkSheetDataService {

    @Autowired
    private SampleRepository sampleRepository;

    @Autowired
    private AnalyseDataRepository analyseDataRepository;

    @Autowired
    private WorkSheetFolderRepository workSheetFolderRepository;

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;


    @Autowired
    private WorkSheetCalibrationCurveRepository workSheetCalibrationCurveRepository;

    @Autowired
    private WorkSheetCalibrationCurveDetailRepository workSheetCalibrationCurveDetailRepository;

    @Autowired
    @Lazy
    private CurveDetailService curveDetailService;
    /**
     * 创建检测单相关的绩效
     *
     * @param workSheetFolderId 检测单id
     */
    @Async
    @Override
    public void createAnalyseStatistic(String workSheetFolderId) {
        List<DtoWorkSheetFolder> workSheetFolders = new ArrayList<>();
        DtoWorkSheetFolder workSheetFolder = workSheetFolderRepository.findOne(workSheetFolderId);
        workSheetFolders.add(workSheetFolder);
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findByWorkSheetFolderIdAndIsDeletedFalse(workSheetFolderId);
        createAnalyseStatistic(workSheetFolders, analyseDataList);
    }

    @Async
    @Override
    public void createAnalyseStatistic(List<DtoWorkSheetFolder> workSheetFolders,
                                       List<DtoAnalyseData> analyseDataList) {

        List<String> workSheetFolderIds = workSheetFolders.stream().map(DtoWorkSheetFolder::getId).distinct().collect(Collectors.toList());
        List<String> sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
        //小的检测单ids
        List<String> workSheetIds = analyseDataList.stream().map(DtoAnalyseData::getWorkSheetId).distinct().collect(Collectors.toList());
        List<DtoSample> samples = new ArrayList<>();
        if (sampleIds.size() > 0) {
            samples = sampleRepository.findByIds(sampleIds);
        }
        //检测单相关的曲线
        List<DtoWorkSheetCalibrationCurve> workSheetCalibrationCurves = new ArrayList<>();
        List<DtoWorkSheetCalibrationCurveDetail> workSheetCalibrationCurveDetails = new ArrayList<>();
        List<DtoCurveDetail> curveDetails = new ArrayList<>();
        if (workSheetIds.size() > 0) {
            workSheetCalibrationCurves = workSheetCalibrationCurveRepository.findByWorksheetIdIn(workSheetIds);
            List<String> curveIds = workSheetCalibrationCurves.stream().map(DtoWorkSheetCalibrationCurve::getId).collect(Collectors.toList());
            List<String> standardCurveIds = workSheetCalibrationCurves.stream().map(DtoWorkSheetCalibrationCurve::getStandardCurveId).collect(Collectors.toList());
            if (curveIds.size() > 0) {
                workSheetCalibrationCurveDetails = workSheetCalibrationCurveDetailRepository.findByWorkSheetCalibrationCurveIdIn(curveIds);
            }
            if (standardCurveIds.size() > 0) {
                curveDetails = curveDetailService.findCurveDetailByCurveIds(standardCurveIds);
            }
        }
        //按检测类型+测试项目id进行分组处理
        List<DtoAnalyseData> newDataList = new ArrayList<>();
        for (DtoAnalyseData analyseData : analyseDataList) {
            //样品id
            String sampleId = analyseData.getSampleId();
            //检测类型id
            String sampleTypeId = UUIDHelper.GUID_EMPTY;
            DtoSample sample = samples.stream().filter(p -> p.getId().equals(sampleId)).findFirst().orElse(null);
            if (StringUtil.isNotNull(sample)) {
                sampleTypeId = sample.getSampleTypeId();
            }
            DtoAnalyseData newData = analyseData;
            newData.setSampleTypeId(sampleTypeId);
            newDataList.add(newData);
        }
        List<DtoPerformanceStatisticForWorkSheetData> list = new ArrayList<>();
        List<DtoWorkSheetCalibrationCurve> finalWorkSheetCalibrationCurves = workSheetCalibrationCurves;
        List<DtoWorkSheetCalibrationCurveDetail> finalWorkSheetCalibrationCurveDetails = workSheetCalibrationCurveDetails;
        List<DtoCurveDetail> finalCurveDetails = curveDetails;
        newDataList.stream().collect(Collectors.groupingBy(PerformanceStatisticForWorkSheetDataServiceImpl::fetchGroupKey, Collectors.toList())).forEach((key, dataList) -> {
            List<String> yKeys = Arrays.asList(key.split(","));
            //检测类型id
            String sampleTypeId = yKeys.get(0);
            //测试项目id
            String testId = yKeys.get(1);
            //检测单id
            String workSheetFolderId = yKeys.get(2);
            DtoWorkSheetFolder workSheetFolder = workSheetFolders.stream().filter(p -> p.getId().equals(workSheetFolderId)).findFirst().orElse(null);

            if (StringUtil.isNotNull(workSheetFolder)) {
                List<String> wIds = newDataList.stream().filter(p -> p.getWorkSheetFolderId().equals(workSheetFolderId)
                        && p.getTestId().equals(testId)
                        && p.getSampleTypeId().equals(sampleTypeId)).map(DtoAnalyseData::getWorkSheetId).distinct().collect(Collectors.toList());
                String analystId = workSheetFolder.getAnalystId();
                String redAnalyzeMethodName = dataList.get(0).getRedAnalyzeMethodName();
                String analyzeMethodId = workSheetFolder.getAnalyzeMethodId();
                String workSheetFolderCode = workSheetFolder.getWorkSheetCode();
                Date analyzeTime = workSheetFolder.getAnalyzeTime();
                DtoPerformanceStatisticForWorkSheetData performanceStatisticForWorkSheetData = new DtoPerformanceStatisticForWorkSheetData();
                performanceStatisticForWorkSheetData.setSampleTypeId(sampleTypeId);
                performanceStatisticForWorkSheetData.setTestId(testId);
                performanceStatisticForWorkSheetData.setAnalystId(analystId);
                performanceStatisticForWorkSheetData.setWorkSheetFolderId(workSheetFolderId);
                performanceStatisticForWorkSheetData.setAnalyzeTime(analyzeTime);
                performanceStatisticForWorkSheetData.setRedAnalyzeItemName(dataList.get(0).getRedAnalyzeItemName());
                performanceStatisticForWorkSheetData.setRedAnalyzeMethodName(redAnalyzeMethodName);
                performanceStatisticForWorkSheetData.setAnalyseItemId(dataList.get(0).getAnalyseItemId());
                performanceStatisticForWorkSheetData.setAnalyzeMethodId(analyzeMethodId);
                performanceStatisticForWorkSheetData.setWorkSheetCode(workSheetFolderCode);
                //样品数
                Integer sampleNum = ((Long) dataList.stream().map(DtoAnalyseData::getSampleId).distinct().count()).intValue();
                //全程序空白数
                Integer localeGapNum = ((Long) dataList.stream().filter(p -> p.getQcGrade().equals(EnumLIM.EnumQCGrade.外部质控.getValue())
                        && p.getQcType().equals(new QualityBlank().qcTypeValue())).map(DtoAnalyseData::getSampleId).distinct().count()).intValue();
                //室内空白样品数
                Integer interiorGapNum = ((Long) dataList.stream().filter(p -> p.getQcGrade().equals(EnumLIM.EnumQCGrade.内部质控.getValue())
                        && p.getQcType().equals(new QualityBlank().qcTypeValue())).map(DtoAnalyseData::getSampleId).distinct().count()).intValue();
                //平行样品数
                Integer pxSampleNum = ((Long) dataList.stream().filter(p -> p.getQcType().equals(new QualityParallel().qcTypeValue())).map(DtoAnalyseData::getSampleId).distinct().count()).intValue();
                //加标样品数
                Integer additionNum = ((Long) dataList.stream().filter(p -> p.getQcGrade().equals(EnumLIM.EnumQCGrade.内部质控.getValue())
                        && p.getQcType().equals(new QualityMark().qcTypeValue())).map(DtoAnalyseData::getSampleId).distinct().count()).intValue();
                //曲线条数
                List<String> sIds = finalWorkSheetCalibrationCurves.stream().filter(p -> wIds.contains(p.getWorksheetId())).map(DtoWorkSheetCalibrationCurve::getStandardCurveId).distinct().collect(Collectors.toList());
                Integer curveItemNum = sIds.size();
                //曲线个数
                Integer curveEntriesNum = ((Long) finalCurveDetails.stream().filter(p -> sIds.contains(p.getCurveId())).map(DtoCurveDetail::getId).distinct().count()).intValue();

                List<String> cIds = finalWorkSheetCalibrationCurves.stream().filter(p -> wIds.contains(p.getWorksheetId())).map(DtoWorkSheetCalibrationCurve::getId).distinct().collect(Collectors.toList());
                //带点个数
                Integer pointNum = ((Long) finalWorkSheetCalibrationCurveDetails.stream().filter(p -> cIds.contains(p.getWorkSheetCalibrationCurveId())
                        && StringUtils.isNotNullAndEmpty(p.getRelativeDeviation())).map(DtoWorkSheetCalibrationCurveDetail::getId).distinct().count()).intValue();
                //带质控数
                Integer qcSampleNum = ((Long) dataList.stream().filter(p -> p.getQcGrade().equals(EnumLIM.EnumQCGrade.内部质控.getValue())
                        && p.getQcType().equals(new QualityStandard().qcTypeValue())).map(DtoAnalyseData::getSampleId).distinct().count()).intValue();
                //有效数据
                Integer validNum = sampleNum + localeGapNum + interiorGapNum + pxSampleNum + additionNum + curveItemNum + curveEntriesNum + pointNum + qcSampleNum;
                performanceStatisticForWorkSheetData.setSample(sampleNum);
                performanceStatisticForWorkSheetData.setLocaleGap(localeGapNum);
                performanceStatisticForWorkSheetData.setInteriorGap(interiorGapNum);
                performanceStatisticForWorkSheetData.setParallel(pxSampleNum);
                performanceStatisticForWorkSheetData.setAddition(additionNum);
                performanceStatisticForWorkSheetData.setCurveItem(curveItemNum);
                performanceStatisticForWorkSheetData.setCurveEntries(curveEntriesNum);
                performanceStatisticForWorkSheetData.setPoint(pointNum);
                performanceStatisticForWorkSheetData.setQcSample(qcSampleNum);
                performanceStatisticForWorkSheetData.setValid(validNum);
                performanceStatisticForWorkSheetData.setOrgId(workSheetFolder.getOrgId());
                list.add(performanceStatisticForWorkSheetData);
            }
        });
        if (workSheetFolderIds.size() > 0) {
            //要清除原先检测单相关的数据
            repository.deleteByWorkSheetFolderIdIn(workSheetFolderIds);
        }
        if (list.size() > 0) {
            repository.save(list);
        }
    }

    @Override
    public  void findByPage(PageBean<DtoPerformanceStatisticForWorkSheetData> pb, BaseCriteria baseCriteria) {
        PerformanceStatisticForWorkSheetDataCriteria performanceStatisticForWorkSheetDataCriteria = (PerformanceStatisticForWorkSheetDataCriteria) baseCriteria;
        pb.setSelect("select p ");
        pb.setEntityName("DtoPerformanceStatisticForWorkSheetData p");
        pb.setSort(performanceStatisticForWorkSheetDataCriteria.getSort());
        pb.setCondition(performanceStatisticForWorkSheetDataCriteria.getCondition());
        comRepository.findByPage(pb, performanceStatisticForWorkSheetDataCriteria.getValues());
        List<String> sampleTypeIds = pb.getData().stream().map(DtoPerformanceStatisticForWorkSheetData::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypes = new ArrayList<>();
        if (sampleTypeIds.size() > 0) {
            sampleTypes = sampleTypeService.findRedisByIds(sampleTypeIds);
        }
        Iterator<DtoPerformanceStatisticForWorkSheetData> iterable = pb.getData().iterator();
        List<DtoPerformanceStatisticForWorkSheetData> newList = new ArrayList<>();
        while (iterable.hasNext()) {
            DtoPerformanceStatisticForWorkSheetData performanceStatisticForWorkSheetData = iterable.next();
            String sampleTypeId = performanceStatisticForWorkSheetData.getSampleTypeId();
            String sampleTypeName = "";
            DtoSampleType sampleType = sampleTypes.stream().filter(p -> p.getId().equals(sampleTypeId)).findFirst().orElse(null);
            if (StringUtil.isNotNull(sampleType)) {
                sampleTypeName = sampleType.getTypeName();
            }
            performanceStatisticForWorkSheetData.setSampleTypeName(sampleTypeName);
            newList.add(performanceStatisticForWorkSheetData);
        }
        pb.setData(newList);
    }

    /**
     * 获取总计行
     * @param baseCriteria
     * @return
     */
    @Override
    public  DtoPerformanceStatisticForWorkSheetData findSumPerformanceStatistic(BaseCriteria baseCriteria) {
        PerformanceStatisticForWorkSheetDataCriteria performanceStatisticForWorkSheetDataCriteria = (PerformanceStatisticForWorkSheetDataCriteria) baseCriteria;
        PageBean<Object[]> pb = new PageBean<>();
        pb.setSelect("select sum(p.sample),sum(p.localeGap),sum(p.interiorGap),sum(p.parallel),sum(p.addition)," +
                "sum(p.curveItem),sum(p.curveEntries),sum(p.point),sum(p.qcSample),sum(p.valid) ");
        pb.setEntityName("DtoPerformanceStatisticForWorkSheetData p");
        pb.setCondition(performanceStatisticForWorkSheetDataCriteria.getCondition());
        List<Object[]> list = comRepository.find(pb.getAutoQuery(), performanceStatisticForWorkSheetDataCriteria.getValues());
        if (list.size() > 0) {
            Object[] objArray = list.get(0);
            return new DtoPerformanceStatisticForWorkSheetData(Integer.valueOf(String.valueOf(objArray[0])),
                    Integer.valueOf(String.valueOf(objArray[1])),
                    Integer.valueOf(String.valueOf(objArray[2])),
                    Integer.valueOf(String.valueOf(objArray[3])),
                    Integer.valueOf(String.valueOf(objArray[4])),
                    Integer.valueOf(String.valueOf(objArray[5])),
                    Integer.valueOf(String.valueOf(objArray[6])),
                    Integer.valueOf(String.valueOf(objArray[7])),
                    Integer.valueOf(String.valueOf(objArray[8])),
                    Integer.valueOf(String.valueOf(objArray[9]))
            );
        }
        return new DtoPerformanceStatisticForWorkSheetData();
    }

    /**
     * 进行数据的分组
     *
     * @param analyseData 分析数据
     * @return 得到分组的主键
     */
    private static String fetchGroupKey(DtoAnalyseData analyseData) {
        return analyseData.getSampleTypeId() + "," + analyseData.getTestId() + "," + analyseData.getWorkSheetFolderId();
    }
}