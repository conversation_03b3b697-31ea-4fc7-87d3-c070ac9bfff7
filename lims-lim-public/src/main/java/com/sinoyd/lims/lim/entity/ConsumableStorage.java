package com.sinoyd.lims.lim.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.math.BigDecimal;
import java.util.Date;


/**
 * ConsumableStorage实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="ConsumableStorage")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class ConsumableStorage extends LimsBaseEntity {

   private static final long serialVersionUID = 1L;

    public  ConsumableStorage() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 消耗品采购明细标识（Guid）
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("消耗品采购明细标识（Guid）")
	private String purchaseDetailId;
    
    /**
    * 消耗品id（Guid）
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("消耗品id（Guid）")
	private String consumableId;
    
    /**
    * 单位Id（冗余）（Guid）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("单位Id（冗余）（Guid）")
    private String dimensionId;
    
    /**
    * 单位名称（冗余）
    */
    @Column(length=100)
    @ApiModelProperty("单位名称（冗余）")
	private String dimensionName;
    
    /**
    * 生产批号
    */
    @Column(length=20)
    @ApiModelProperty("生产批号")
    @Length(message = "生产批号{validation.message.length}", max = 50)
    private String productionCode;
    
    /**
    * 验收人Id（Guid）
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("验收人Id（Guid）")
    @Length(message = "验收人Id{validation.message.length}", max = 50)
	private String checkerId;
    
    /**
    * 验收人名字
    */
    @Column(length=50)
    @ApiModelProperty("验收人名字")
	private String checkerName;
    
    /**
    * 验收日期
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("验收日期")
    private Date checkerDate;
    
    /**
    * 验收结论(枚举EnumCheckerResult：1:合格，0：不合格)
    */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("验收结论(枚举EnumCheckerResult：1:合格，0：不合格)")
    private Integer checkerResult;
    
    /**
    * 生产厂商名字
    */
    @Column(length=100)
    @ApiModelProperty("生产厂商名字")
    @Length(message = "生产厂商名字{validation.message.length}", max = 100)
    private String manufacturerName;
    
    /**
    * 有效日期
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("有效日期")
    private Date expiryDate;
    
    /**
    * 入库数量
    */
    @Column(nullable=false)
    @ApiModelProperty("入库数量")
    private BigDecimal storageNum;
    
    /**
    * 入库时间
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("入库时间")
    private Date storageTime;
    
    /**
    * 入库结存
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("入库结存")
    private BigDecimal balance;
    
    /**
    * 单价
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("单价")
    private BigDecimal unitPrice;
    
    /**
    * 总价
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("总价")
    private BigDecimal totalPrice;
    
    /**
    * 供应商Id（Guid）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("供应商Id（Guid）")
    private String supplyCompanyId;
    
    /**
    * 供应商名称
    */
    @Column(length=100)
    @ApiModelProperty("供应商名称")
	private String supplyCompanyName;
    
    /**
    * 入库人Id（Guid）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("入库人Id（Guid）")
    private String operatorId;
    
    /**
    * 入库人名称
    */
    @Column(length=50)
    @ApiModelProperty("入库人名称")
	private String operatorName;
    
    /**
    * 外观、状态
    */
    @Column(length=100)
    @ApiModelProperty("外观、状态")
    @Length(message = "外观、状态{validation.message.length}", max = 100)
    private String appearance;
    
    /**
    * 检验/验证项目
    */
    @Column(length=100)
    @ApiModelProperty("检验/验证项目")
    @Length(message = "验证项目{validation.message.length}", max = 100)
    private String checkItem;
    
    /**
    * 购买原因
    */
    @Column(length=1000)
    @ApiModelProperty("购买原因")
    @Length(message = "购买原因{validation.message.length}", max = 1000)
    private String buyReason;
    
    /**
    * 存放位置
    */
    @Column(length=100)
    @ApiModelProperty("存放位置")
    @Length(message = "存放位置{validation.message.length}", max = 100)
    private String keepPlace;
    
    /**
    * 备注
    */
    @Column(length=1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
    private String remark;
    
    /**
    * 假删
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
	private Boolean isDeleted=false;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }