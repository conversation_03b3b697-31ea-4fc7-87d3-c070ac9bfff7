package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.pro.dto.DtoWorkSheet;
import com.sinoyd.lims.pro.dto.customer.DtoWorkSheetCalibrationCurveTemp;
import com.sinoyd.lims.pro.repository.WorkSheetRepository;
import com.sinoyd.lims.pro.service.WorkSheetCalibrationCurveService;
import com.sinoyd.lims.pro.service.WorkSheetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;


/**
 * WorkSheet操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@Service
public class WorkSheetServiceImpl extends BaseJpaServiceImpl<DtoWorkSheet, String, WorkSheetRepository> implements WorkSheetService {

    @Autowired
    private WorkSheetCalibrationCurveService workSheetCalibrationCurveService;

    @Override
    public void findByPage(PageBean<DtoWorkSheet> pb, BaseCriteria workSheetCriteria) {
        pb.setEntityName("DtoWorkSheet a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, workSheetCriteria);
    }

    /**
     * 找到检测单下的子检测单
     *
     * @param parentId 父级id
     * @return 返回子检测单数据
     */
    @Override
    public List<DtoWorkSheet> findByParentId(String parentId) {
        List<DtoWorkSheet> workSheets = repository.findByParentId(parentId);
        if (StringUtil.isNotEmpty(workSheets)) {
            for (DtoWorkSheet workSheet : workSheets) {
                DtoWorkSheetCalibrationCurveTemp temp = workSheetCalibrationCurveService.findByTestIdAndWorkSheetId(workSheet.getId(), workSheet.getTestId());
                if (StringUtil.isNotNull(temp)) {
                    StringBuilder str = new StringBuilder();
                    String equation = temp.getEquation();
                    String anaName = workSheet.getRedAnalyzeItemName();
                    str.append(anaName).append("(").append(equation).append(")");
                    workSheet.setDisplayAnaName(str.toString());
                    workSheet.setWorksheetCurveId(temp.getId());
                } else {
                    workSheet.setDisplayAnaName(workSheet.getRedAnalyzeItemName());
                }

            }
        }
        workSheets.sort(Comparator.comparing(DtoWorkSheet::getRedAnalyzeItemName));
        return workSheets;
    }
}