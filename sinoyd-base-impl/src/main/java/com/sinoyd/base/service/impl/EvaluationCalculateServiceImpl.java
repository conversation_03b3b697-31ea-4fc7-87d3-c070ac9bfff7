package com.sinoyd.base.service.impl;

import com.sinoyd.base.dto.customer.EvaluationIntervalVO;
import com.sinoyd.base.service.EvaluationCalculateService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.common.utils.MathUtil;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 评价标准计算服务实现
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2024/03/13
 */
@Service
public class EvaluationCalculateServiceImpl implements EvaluationCalculateService {


    /**
     * 判断数值是否满足区间限值
     *
     * @param value       需要判断的值
     * @param evaInterval 限值数据
     * @return 是否满足
     */
    @Override
    public Boolean judgeIsPass(String value, EvaluationIntervalVO evaInterval) {
        //转换为数学区间
        String interval = MathUtil.parseAsInterval(
                evaInterval.getLowerLimit(), evaInterval.getLowerLimitSymbol(),
                evaInterval.getUpperLimit(), evaInterval.getUpperLimitSymbol());
        //判断是否在区间内，再区间内就合格
        return MathUtil.isInInterval(handleTestVal(value), interval);
    }

    /**
     * 获取评价限值集合下满足值区间的评价条件ID
     *
     * @param analyzeItemId 当前判断的分析项目id
     * @param value         当前判断的数据值
     * @param evaInterval   评价限值区间数据
     * @return 满足值区间的评价条件ID
     */
    @Override
    public String findIntervalEvaLevelId(String analyzeItemId, String value, List<EvaluationIntervalVO> evaInterval) {
        //获取到分析项目下的评价限值,按照限值等级从好到差排序
        List<EvaluationIntervalVO> intervalOfAnalyseItem = evaInterval.stream()
                .filter(p -> analyzeItemId.equals(p.getAnalyzeItemId()))
                .sorted(Comparator.comparing(EvaluationIntervalVO::getOrderNum, Comparator.reverseOrder()))
                .collect(Collectors.toList());
        //处理评价条件对应的值区间
        if (StringUtil.isNotEmpty(intervalOfAnalyseItem)) {
            for (EvaluationIntervalVO evaValue : intervalOfAnalyseItem) {
                String interval = MathUtil.parseAsInterval(
                        evaValue.getLowerLimit(), evaValue.getLowerLimitSymbol(),
                        evaValue.getUpperLimit(), evaValue.getUpperLimitSymbol());
                if (MathUtil.isInInterval(handleTestVal(value), interval)) {
                    return evaValue.getEvaLevelId();
                }
            }
        }
        return "";
    }

    /**
     * 处理出证结果
     *
     * @param testValue 判断的值
     * @return 评价等级id
     */
    private String handleTestVal(String testValue) {
        String value = testValue;
        //统计表中一些标红值，标红标识替换为空
        if (testValue.contains("Red:")) {
            value = testValue.replace("Red:", "");
        }
        //当数值小于检出限，当0评判
        if (testValue.contains("ND") || testValue.contains("L") || testValue.contains("＞") || testValue.contains("＜")
                || testValue.contains("<") || testValue.contains(">")) {
            value = "0";
        }
        return value;
    }
}
