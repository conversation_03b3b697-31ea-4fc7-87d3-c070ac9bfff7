package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.pro.dto.DtoComment;
import com.sinoyd.frame.service.IBaseJpaService;


/**
 * Comment操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface CommentService extends IBaseJpaService<DtoComment, String> {
    /**
     * 返回评论与日志
     *
     * @param objectId   关联id
     * @param objectType 关联类型
     * @return 评论与日志
     */
    DtoComment[] findAllComment(String objectId, Integer objectType);
}