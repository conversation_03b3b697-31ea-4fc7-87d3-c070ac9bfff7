package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.NewSearchTaskCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoNewSearchResult;
import com.sinoyd.lims.lim.dto.lims.DtoNewSearchTask;
import com.sinoyd.lims.lim.service.NewSearchTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 查新任务接口服务
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
@Api(tags = "查新任务接口服务")
@RestController
@RequestMapping("api/lim/newSearchTask")
@Validated
public class NewSearchTaskController extends BaseJpaController<DtoNewSearchTask, String, NewSearchTaskService> {
    /**
     * 分页动态条件查询NewSearchTask
     *
     * @param newSearchTaskCriteria 条件参数
     * @return RestResponse<List < NewSearchTask>>
     */
    @ApiOperation(value = "分页动态条件查询", notes = "分页动态条件查询")
    @GetMapping
    public RestResponse<List<DtoNewSearchTask>> findByPage(NewSearchTaskCriteria newSearchTaskCriteria) {
        PageBean<DtoNewSearchTask> pageBean = super.getPageBean();
        RestResponse<List<DtoNewSearchTask>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, newSearchTaskCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 根据id查询查新任务
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id查询查新任务", notes = "根据id查询查新任务")
    @GetMapping("/{id}")
    public RestResponse<DtoNewSearchTask> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoNewSearchTask> restResp = new RestResponse<>();
        DtoNewSearchTask entity = service.findOne(id);
        restResp.setData(entity);
        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }


    /**
     * 新增查新任务
     *
     * @param newSearchTask 查新任务实体
     * @return 新增的查新任务实体
     */
    @ApiOperation(value = "新增查新任务", notes = "新增查新任务")
    @PostMapping("")
    public RestResponse<DtoNewSearchTask> create(@Validated @RequestBody DtoNewSearchTask newSearchTask) {
        RestResponse<DtoNewSearchTask> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoNewSearchTask data = service.save(newSearchTask);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 更新查新任务
     *
     * @param newSearchTask 查新任务实体
     * @return 更新后的查新任务实体
     */
    @ApiOperation(value = "更新查新任务", notes = "更新查新任务")
    @PutMapping("")
    public RestResponse<DtoNewSearchTask> update(@Validated @RequestBody DtoNewSearchTask newSearchTask) {
        RestResponse<DtoNewSearchTask> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoNewSearchTask data = service.update(newSearchTask);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 提交查新任务
     *
     * @param submitMap 提交参数
     * @return 查新计划实体
     */
    @ApiOperation(value = "提交查新任务", notes = "提交查新任务")
    @PostMapping("/submit")
    public RestResponse<DtoNewSearchTask> submit(@RequestBody Map<String, Object> submitMap) {
        RestResponse<DtoNewSearchTask> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.submit(submitMap));
        return restResponse;
    }


    /**
     * 单个假删
     *
     * @param id 参数id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.logicDeleteById(id);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 批量假删
     *
     * @param ids 参数ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResponse.setCount(count);

        return restResponse;
    }


}
