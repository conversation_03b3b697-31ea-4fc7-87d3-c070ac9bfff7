package com.sinoyd.lims.lim.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;

/**
 * 查新结果
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "NewSearchResult")
@Data
@EntityListeners(AuditingEntityListener.class)
public class NewSearchResult implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public NewSearchResult() {
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 查新任务id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("查新任务id")
    private String taskId;

    /**
     * 是否新标准
     */
    @Column()
    @ApiModelProperty("是否新标准")
    private Boolean isNewStandard;

    /**
     * 标准名称
     */
    @Column(length = 100)
    @ApiModelProperty("标准名称")
    @Length(message = "标准名称{validation.message.length}", max = 100)
    private String standardName;

    /**
     * 标准编号
     */
    @Column(length = 50)
    @ApiModelProperty("标准编号")
    @Length(message = "标准编号{validation.message.length}", max = 50)
    private String standardNum;

    /**
     * 年份
     */
    @Column(length = 50)
    @ApiModelProperty("年份")
    @Length(message = "年份{validation.message.length}", max = 50)
    private String year;

    /**
     * 发布日期
     */
    @ApiModelProperty("发布日期")
    private Date releaseDate;

    /**
     * 实施日期
     */
    @ApiModelProperty("实施日期")
    private Date effectiveDate;

    /**
     * 查新日期
     */
    @ApiModelProperty("查新日期")
    private Date newSearchDate;

    /**
     * 替代标准号
     */
    @Column(length = 50)
    @ApiModelProperty("替代标准号")
    @Length(message = "替代标准号{validation.message.length}", max = 50)
    private String replaceNum;

    /**
     * 任务状态1:新建 2已提交 3已审核
     */
    @Column(nullable = false)
    @ApiModelProperty("任务状态0:新建 1已提交 2已审核")
    private Integer status;

    /**
     * 是否确认
     */
    @Column
    @ApiModelProperty("是否确认")
    private Boolean isConfirm;

    /**
     * 确认状况
     */
    @Column(nullable = false)
    @ApiModelProperty("确认状况")
    private Boolean confirmation;


    /**
     * 是否宣贯
     */
    @Column
    @ApiModelProperty("是否宣贯")
    private Boolean isPropagate;

    /**
     * 宣贯情况
     */
    @Column(nullable = false)
    @ApiModelProperty("宣贯情况")
    private Boolean propagate;

    /**
     * 确认人Id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("确认人Id")
    private String confirmId;

    /**
     * 确认任务确认人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("确认任务确认人")
    private String executeConfirmId;

    /**
     * 确认任务确认日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("确认任务确认日期")
    private String executeConfirmDate;

    /**
     * 确认任务确认内容
     */
    @Column(length = 1000)
    @ApiModelProperty("宣贯任务宣贯内容")
    private String executeConfirmContent;

    /**
     * 宣贯任务宣贯人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("宣贯任务宣贯人")
    private String executePropagateId;

    /**
     * 宣贯任务宣贯日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("宣贯任务宣贯日期")
    private String executePropagateDate;

    /**
     * 宣贯任务宣贯内容
     */
    @Column(length = 1000)
    @ApiModelProperty("宣贯任务宣贯内容")
    private String executePropagateContent;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}
