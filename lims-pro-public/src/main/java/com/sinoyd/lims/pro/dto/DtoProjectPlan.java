package com.sinoyd.lims.pro.dto;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.entity.ProjectPlan;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;


/**
 * DtoProjectPlan实体
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_ProjectPlan")
 @Data
 @DynamicInsert
 public  class DtoProjectPlan extends ProjectPlan {
 private static final long serialVersionUID = 1L;

 /**
  * 现场质控-考核人员
  */
 @Transient
 private List<String> assessPersonIds;

 /**
  * 加载项目dto
  *
  * @param project
  */
 public void loadFromProject(DtoProject project) {
  this.setProjectId(project.getId());
  if (StringUtil.isNotNull(project.getLeaderId())) {
   this.setLeaderId(project.getLeaderId());
  }
  if (StringUtil.isNotNull(project.getReportMakerId())) {
   this.setReportMakerId(project.getReportMakerId());
  }
  if (StringUtil.isNotNull(project.getSchemeMakerId())) {
   this.setSchemeMakerId(project.getSchemeMakerId());
  }
  if (StringUtil.isNotNull(project.getSpotPersonId())) {
   this.setSpotPersonId(project.getSpotPersonId());
  }
  if (StringUtil.isNotNull(project.getSupervisorId())) {
   this.setSupervisorId(project.getSupervisorId());
  }
  if (StringUtil.isNotNull(project.getDeadLine())) {
   this.setDeadLine(project.getDeadLine());
  }
  if (StringUtil.isNotNull(project.getReportDate())) {
   this.setReportDate(project.getReportDate());
  }
  if (StringUtil.isNotNull(project.getRequireAnalyzeDate())) {
   this.setRequireAnalyzeDate(project.getRequireAnalyzeDate());
  }
  if (StringUtil.isNotNull(project.getRequireSamplingDate())) {
   this.setRequireSamplingDate(project.getRequireSamplingDate());
  }
  if (StringUtil.isNotNull(project.getResponsePerson())) {
   this.setResponsePerson(project.getResponsePerson());
  }
  if (StringUtil.isNotNull(project.getRequires())) {
   this.setRequires(project.getRequires());
  }
  if (StringUtil.isNotNull(project.getTestMethodRequires())) {
   this.setTestMethodRequires(project.getTestMethodRequires());
  }
  if (StringUtil.isNotNull(project.getRemark())) {
   this.setRemark(project.getRemark());
  }
  if (StringUtil.isNotNull(project.getIsUseMethod())) {
   this.setIsUseMethod(project.getIsUseMethod());
  }
  if (StringUtil.isNotNull(project.getIsEvaluate())) {
   this.setIsEvaluate(project.getIsEvaluate());
  }
  if (StringUtil.isNotNull(project.getIsWarning())) {
   this.setIsWarning(project.getIsWarning());
  }
  if (StringUtil.isNotNull(project.getWarningDay())) {
   this.setWarningDay(project.getWarningDay());
  }
  if (StringUtil.isNotNull(project.getIsFeedback())) {
   this.setIsFeedback(project.getIsFeedback());
  }
  if (StringUtil.isNotNull(project.getIsContract())) {
   this.setIsContract(project.getIsContract());
  }
  if (StringUtil.isNotNull(project.getSubName())) {
   this.setSubName(project.getSubName());
  }
  if (StringUtil.isNotNull(project.getSubItems())) {
   this.setSubItems(project.getSubItems());
  }
  if (StringUtil.isNotNull(project.getSubMethod())) {
   this.setSubMethod(project.getSubMethod());
  }
  if (StringUtil.isNotNull(project.getIsOutsourcing())) {
   this.setIsOutsourcing(project.getIsOutsourcing());
  }
  if (StringUtil.isNotNull(project.getIsMakePlan())) {
   this.setIsMakePlan(project.getIsMakePlan());
  }
  if (StringUtil.isNotNull(project.getReportMakerIIId())) {
   this.setReportMakerIIId(project.getReportMakerIIId());
  }
  if (StringUtil.isNotNull(project.getQcGrade())) {
   this.setQcGrade(project.getQcGrade());
  }
  if (StringUtil.isNotNull(project.getQcType())) {
   this.setQcType(project.getQcType());
  }
  if (StringUtil.isNotNull(project.getQcSource())) {
   this.setQcSource(project.getQcSource());
  }
  if (StringUtil.isNotNull(project.getJudgment())) {
   this.setJudgment(project.getJudgment());
  }
  if (StringUtil.isNotEmpty(project.getAssessPersonIds())) {
   this.setAssessPersonIds(project.getAssessPersonIds());
  }
 }
}