-- 新增标准版土壤报告模板及组件配置
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('44a04c0a-d4fe-4a29-b513-5f6a6de3a27b', '96e515b0-6196-47af-a8eb-78bd018d44de', 'ReportEditNew', '报告编制',
        'SoilStd', '土壤报告', 1, 1, 1, '', '', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2024-03-13 14:55:50', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2024-03-13 14:55:50', NULL);

INSERT INTO TB_LIM_RecordConfig(id, recordName, recordType, reportConfigId, sampleTypeId,
                                remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate)
VALUES ('0e25fdfe-f3b5-42e4-ab9e-aba25130e5f2', '土壤报告', 3, '96e515b0-6196-47af-a8eb-78bd018d44de',
        '00000000-0000-0000-0000-000000000000', '', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2024-03-13 15:01:01', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2024-03-13 15:01:01');

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate)
VALUES ('d5166dca-0b26-451f-85d7-17f027cb44c5', 'dtSoilStdTable', '标准版土壤检测结果表组件', 'dtSoilStdTable', 'dtSoilSource', 4,
        14, '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2024-03-13 15:12:04',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2024-03-13 15:12:04');

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate)
VALUES ('5593eed0-245d-43a3-8ade-6c8e5a5e0e6c', 'soilStdDataSource', '标准版土壤报告检测数据主表', 'dtDataSource', '', 0, 0,
        '{\"headModule\":\"dtWaterHeadStdTable\", \"bodyModule\":\"dtSoilStdTable\", \"secondBodyModule\":\"dtCompoundStdNewTable\", \"thirdBodyModule\":\"\"}',
        b'1', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2024-03-13 15:15:37',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2024-03-13 15:15:37');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('23d95b74-a363-4888-a38a-249c92ec7ba0', '96e515b0-6196-47af-a8eb-78bd018d44de',
        '9b557e2c-bd2c-4c73-8a6c-4a55e38f52ba');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('388ec660-ffc1-4240-b3d7-244f89227242', '96e515b0-6196-47af-a8eb-78bd018d44de',
        '6130da13-013a-4a25-a856-bd1f16fc691b');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('3f489ee9-72c8-4675-bdec-621ba96ceea2', '96e515b0-6196-47af-a8eb-78bd018d44de',
        '55fe89e1-dc8c-4b86-b03b-e6dbf3143340');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('481d6b30-fd2b-4678-b6a7-caac880c628e', '96e515b0-6196-47af-a8eb-78bd018d44de',
        'fe05546e-47f5-436a-a99f-970a96bcd590');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('64579b8b-2f2a-4cf3-9ad3-8059675b2895', '96e515b0-6196-47af-a8eb-78bd018d44de',
        'fff21a0d-5e6f-4608-b966-d09bf86cc5f9');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('812d8df2-d600-4675-9781-249b93bea730', '96e515b0-6196-47af-a8eb-78bd018d44de',
        '5593eed0-245d-43a3-8ade-6c8e5a5e0e6c');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('85cb5acc-a67f-4047-a028-1cbf05a5d0bb', '96e515b0-6196-47af-a8eb-78bd018d44de',
        '44c0ef90-1cee-4379-bdec-e502a875c0a7');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('8bbeaa22-967e-4840-b96b-f117d52d4e44', '96e515b0-6196-47af-a8eb-78bd018d44de',
        'ba52a4c7-960f-4ede-ad34-6a4ea0335ba4');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('b7ed0ec5-c3c4-4d01-b704-18b7c5433475', '96e515b0-6196-47af-a8eb-78bd018d44de',
        'f33aa5b0-7ddd-4a62-8d7a-2b219f574bad');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('d3fffdf3-480c-4e1f-b127-aedc978df254', '96e515b0-6196-47af-a8eb-78bd018d44de',
        '7abbfd3f-7f71-4d2c-9bd3-9b4fc08b20cf');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('e2ea6c02-80ea-4140-9782-3c1cdde6d0aa', '96e515b0-6196-47af-a8eb-78bd018d44de',
        '3477d948-c1eb-418f-8fbd-81fc1295cee6');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('fa4d100a-5f4d-4387-9025-aec830cdc881', '96e515b0-6196-47af-a8eb-78bd018d44de',
        '76d9545f-8eb1-4ab2-8aa4-f9decfe2a54e');

INSERT INTO TB_LIM_ReportModule2GroupType(id, reportConfigModuleId, groupTypeName, priority)
VALUES ('103e89f6-10b5-4658-8a73-acac9065d7f4', '812d8df2-d600-4675-9781-249b93bea730',
        'sampleData_sampleFolderId_fieldGroupType', 10);
INSERT INTO TB_LIM_ReportModule2GroupType(id, reportConfigModuleId, groupTypeName, priority)
VALUES ('2e4cac8f-3800-4927-867b-a95dd9db220d', '812d8df2-d600-4675-9781-249b93bea730',
        'sampleData_samplingTimeBegin_dateGroupType', 9);

update TB_LIM_ReportModule
set sonTableJson = '["normalWaterStdDataSource", "wasteWaterStdDataSource", "orgGasStdDataSource", "unOrgGasStdDataSource", "solidStdDataSource", "noiseDataSource", "soilStdDataSource"]'
where moduleCode = 'comprehensiveStdDataSource';