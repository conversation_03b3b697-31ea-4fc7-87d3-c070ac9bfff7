package com.sinoyd.lims.lim.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.rcc.DtoCostRuleForEnt;
import com.sinoyd.lims.lim.repository.rcc.CostRuleForEntRepository;
import com.sinoyd.lims.lim.service.CostRuleForEntService;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 企业费用规则操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/15
 * @since V100R001
 */
 @Service
public class CostRuleForEntServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoCostRuleForEnt, String, CostRuleForEntRepository>
        implements CostRuleForEntService {

    /**
     * 分页查询
     */
    @Override
    public void findByPage(PageBean<DtoCostRuleForEnt> pageBean, BaseCriteria criteria) {
        // 设置查询的实体类名及别名
        pageBean.setEntityName("DtoCostRuleForEnt p,DtoEnterprise e");
        // 设置查询返回的字段、实体别名表示所有字段
        pageBean.setSelect("select p,e.name as entName");

        super.findByPage(pageBean, criteria);

        List<DtoCostRuleForEnt> datas = pageBean.getData();
        List<DtoCostRuleForEnt> newDatas = new ArrayList<>();

        Iterator<DtoCostRuleForEnt> ite = datas.iterator();
        // 循环迭代获取JPQL中查询返回的属性

        while (ite.hasNext()) {
            Object obj = ite.next();
            Object[] objs = (Object[]) obj;
            // 按查询顺序下标一一获取
            DtoCostRuleForEnt dto = (DtoCostRuleForEnt) objs[0];
            dto.setEntName((String) objs[1]);

            newDatas.add(dto);
        }

        pageBean.setData(newDatas);
    }

    /**
     * 获取客户的费用规则
     *
     * @param entId 客户id
     * @return 对应客户的费用规则
     */
    @Override
    public DtoCostRuleForEnt getByEntId(String entId) {
        return repository.getByEntId(entId);
    }

    @Transactional
    @Override
    public List<DtoCostRuleForEnt> save(Collection<DtoCostRuleForEnt> entities) {
        List<DtoCostRuleForEnt> list = new ArrayList<>();

        //获取去重的企业
        Set<String> entIds = entities.stream().map(DtoCostRuleForEnt::getEntId).collect(Collectors.toSet());
        if (entities.size() != entIds.size()) {
            throw new BaseException("同一家企业存在多条规则配置，请核查");
        }
        // 若是新增,则进行企业唯一性验证
        List<String> ids = entities.stream().filter(e -> StringUtil.isEmpty(e.getId())).map(e -> e.getEntId()).collect(Collectors.toList());
        List<DtoCostRuleForEnt> costRuleForEntList = repository.findByEntIdIn(ids);
        if (StringUtil.isNotEmpty(costRuleForEntList)) {
            throw new BaseException("此客户已经配置过折扣信息,不能再进行配置");
        }
        for (DtoCostRuleForEnt entity : entities) {
            if (!StringUtil.isNotNull(entity.getLaborCost())) {
                entity.setLaborCost(BigDecimal.ZERO);
            }
            //直接对所传数据进行遍历，保证返回的数据顺序与前端一致
            if (StringUtils.isNotNullAndEmpty(entity.getId())) {
                //主键id存在，表明用于更新
                DtoCostRuleForEnt ruleForEnt = super.update(entity);
                list.add(ruleForEnt);
            } else {
                //主键id为空或null，表明用于新增，前端需将id置为空传参
                entity.setId(UUIDHelper.NewID());
                DtoCostRuleForEnt ruleForEnt = super.save(entity);
                list.add(ruleForEnt);
            }
        }

        return list;
    }
}