package com.sinoyd.lims.pro.dto;

import java.io.Serializable;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import lombok.Data;

/**
 * 审批任务查询传输实体
 * <AUTHOR>
 * @version V1.0.0 2019-03-28
 * @since V100R001
 */
@Data
public class DtoOATaskQuery implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 流程类型标识
     */
    private String procTypeId;

    /**
     * 部门标识
     */
    private String deptId;

    /**
     * 发起人标识
     */
    private String sponsorId;

    /**
     * 标题
     */
    private String title;

    /**
     * 状态
     */
    private Integer dataStatus;

    /**
     * 发起开始时间
     */
    private String submitBeginTime;

    /**
     * 发起结束时间
     */
    private String submitEndTime;

    /**
     * 当前页
     */
    private Integer pageNo = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 20;
}