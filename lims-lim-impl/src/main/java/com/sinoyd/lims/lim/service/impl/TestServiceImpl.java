package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.base.core.BaseCodeHelper;
import com.sinoyd.base.dto.rcc.*;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.rcc.DimensionRepository;
import com.sinoyd.base.repository.rcc.IndustryTypeRepository;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.service.AnalyzeItemService;
import com.sinoyd.base.service.DimensionService;
import com.sinoyd.base.service.IndustryTypeService;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.base.utils.poi.ExcelStyle;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoUser;
import com.sinoyd.frame.service.UserService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.PinYinUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.criteria.TestCriteria;
import com.sinoyd.lims.lim.data.mapper.*;
import com.sinoyd.lims.lim.dto.customer.*;
import com.sinoyd.lims.lim.dto.lims.DtoPerson2Test;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.lims.DtoTestExpand;
import com.sinoyd.lims.lim.dto.rcc.*;
import com.sinoyd.lims.lim.entity.Test;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.Person2TestRepository;
import com.sinoyd.lims.lim.repository.lims.TestExpandRepository;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.*;
import com.sinoyd.lims.lim.service.AnalyzeMethodService;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import com.sinoyd.lims.lim.vo.TestItemCombineVO;
import com.sinoyd.lims.lim.vo.TestMethodCombineVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;


/**
 * 测试项目管理
 *
 * <AUTHOR>
 * @version V1.0.0 2019/1/30
 * @since V100R001
 */
@Service
@Slf4j
public class TestServiceImpl extends BaseJpaServiceImpl<DtoTest, String, TestRepository> implements TestService {

    @Autowired
    @Lazy
    private AnalyzeItemService analyzeItemService;

    @Autowired
    @Lazy
    private AnalyzeMethodService analyzeMethodService;

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    @Autowired
    @Lazy
    private IndustryTypeService industryTypeService;

    @Autowired
    private QualityControlLimitRepository qualityControlLimitRepository;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private Person2TestRepository person2TestRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private ParamsFormulaRepository paramsFormulaRepository;

    @Autowired
    private ParamsTestFormulaRepository paramsTestFormulaRepository;

    @Autowired
    private ParamsPartFormulaRepository paramsPartFormulaRepository;

    @Autowired
    private ImportUtils importUtils;

    @Autowired
    private MarkersDataListenerImpl markersDataListener;

    @Autowired
    private ParamsRepository paramsRepository;

    @Autowired
    private DimensionRepository dimensionRepository;
    @Autowired
    private DimensionMapper dimensionMapper;
    @Autowired
    private ParamsMapper paramsMapper;
    @Autowired
    private ParamsFormulaMapper paramsFormulaMapper;
    @Autowired
    private ParamsPartFormulaMapper paramsPartFormulaMapper;
    @Autowired
    private ParamsTestFormulaMapper paramsTestFormulaMapper;

    @Autowired
    @Lazy
    private DimensionService dimensionService;

    private SampleType2TestRepository sampleType2TestRepository;

    private TestExpandRepository testExpandRepository;

    private AnalyzeMethodRepository analyzeMethodRepository;

    @Autowired
    private SampleTypeRepository sampleTypeRepository;

    @Autowired
    private IndustryTypeRepository industryTypeRepository;

    @Override
    public void initRedis() {
        List<DtoTest> dataList = repository.findAll();
        for (DtoTest dtoTest : dataList) {
            saveRedis(dtoTest);
        }
    }

    /**
     * 新增测试项目
     */
    @Transactional
    @Override
    public DtoTest save(DtoTest entity) {

        if (!StringUtils.isNotNullAndEmpty(entity.getRedAnalyzeItemName())) {
            throw new BaseException("分析项目名称不能为空！");
        }
        if (!StringUtils.isNotNullAndEmpty(entity.getRedAnalyzeMethodName())) {
            throw new BaseException("分析方法名称不能为空！");
        }
        if (repository.getCountByName(entity.getRedAnalyzeItemName(), entity.getRedAnalyzeMethodName(), entity.getSampleTypeId()) > 0) {
            throw new BaseException("已存在相同名称的测试项目！");
        }

        if (StringUtils.isNotNullAndEmpty(entity.getDimensionId()) && !UUIDHelper.GUID_EMPTY.equals(entity.getDimensionId())) {
            dimensionService.incrementOrderNum(Collections.singletonList(entity.getDimensionId()));
        }
        // 根据名称查找分析项目、分析方法
        DtoAnalyzeItem analyzeItem;
        if (StringUtils.isNotNullAndEmpty(entity.getAnalyzeItemId()) && !entity.getAnalyzeItemId().equals(UUIDHelper.GUID_EMPTY)) {
            analyzeItem = analyzeItemService.findOne(entity.getAnalyzeItemId());
        } else {
            analyzeItem = analyzeItemService.getByAnalyzeItemName(entity.getRedAnalyzeItemName());
        }
        DtoAnalyzeMethod analyzeMethod = null;
        if (StringUtils.isNotNullAndEmpty(entity.getAnalyzeMethodId()) && !entity.getAnalyzeMethodId().equals(UUIDHelper.GUID_EMPTY)) {
            //跟据id查询分析方法
            analyzeMethod = analyzeMethodService.findOne(entity.getAnalyzeMethodId());
        }
        // 判断是否需要新增分析项目
        analyzeItem = getDtoAnalyzeItem(entity, analyzeItem);
        if (!StringUtils.isNotNullAndEmpty(entity.getAnalyzeItemId()) || entity.getAnalyzeItemId().equals(UUIDHelper.GUID_EMPTY)) {
            entity.setAnalyzeItemId(analyzeItem.getId());
        }
        // 判断是否需要新增分析方法
        analyzeMethod = getDtoAnalyzeMethod(entity, analyzeMethod);
        if (!StringUtils.isNotNullAndEmpty(entity.getAnalyzeMethodId()) || entity.getAnalyzeMethodId().equals(UUIDHelper.GUID_EMPTY)) {
            entity.setAnalyzeMethodId(analyzeMethod.getId());
        }
        if (StringUtils.isNotNullAndEmpty(entity.getRedAnalyzeItemName())) {
            entity.setFullPinYin(PinYinUtil.getFullSpell(entity.getRedAnalyzeItemName()));
            entity.setPinYin(PinYinUtil.getFirstSpell(entity.getRedAnalyzeItemName()));
        }
        entity.setTestName(entity.getRedAnalyzeItemName() + '-' + entity.getRedAnalyzeMethodName() + ' ' + (StringUtil.isNull(entity.getRedCountryStandard()) ? "" : entity.getRedCountryStandard()));
        if (!StringUtil.isNotNull(entity.getSamplePeriod())) {
            entity.setSamplePeriod(1);
        }
        DtoTest dtoTest = super.save(entity);
        saveRedis(dtoTest);
        return dtoTest;
    }

    /**
     * 修改测试项目
     */
    @Transactional
    @Override
    public DtoTest update(DtoTest entity) {
        if (!StringUtils.isNotNullAndEmpty(entity.getRedAnalyzeItemName())) {
            throw new BaseException("分析项目名称不能为空！");
        }
        if (!StringUtils.isNotNullAndEmpty(entity.getRedAnalyzeMethodName())) {
            throw new BaseException("分析方法名称不能为空！");
        }
        List<DtoTest> dupTestList = repository.findByRedAnalyzeItemNameAndRedAnalyzeMethodNameAndSampleTypeIdAndIdNotInAndIsDeletedFalse(entity.getRedAnalyzeItemName(),
                entity.getRedAnalyzeMethodName(), entity.getSampleTypeId(), Collections.singletonList(entity.getId()));
        if (StringUtil.isNotEmpty(dupTestList) && dupTestList.stream().anyMatch(p -> p.getRedCountryStandard().equals(entity.getRedCountryStandard()))) {
            throw new BaseException("已存在相同名称的测试项目！");
        }

        DtoTest orginalTest = repository.findOne(entity.getId());
        if (!orginalTest.getDimensionId().equals(entity.getDimensionId()) && !UUIDHelper.GUID_EMPTY.equals(entity.getDimensionId())) {
            dimensionService.incrementOrderNum(Collections.singletonList(entity.getDimensionId()));
        }
        // 根据名称查找分析项目、分析方法
        DtoAnalyzeItem analyzeItem = analyzeItemService.getByAnalyzeItemName(entity.getRedAnalyzeItemName());
        // 根据分析方法名称和分析方法标准编号查找
        DtoAnalyzeMethod analyzeMethod = analyzeMethodService.getByAnalyzeMethodNameAndCountryStandard(entity.getRedAnalyzeMethodName(), entity.getRedCountryStandard());
        // 判断是否需要新增分析项目
        analyzeItem = getDtoAnalyzeItem(entity, analyzeItem);
        if (!StringUtils.isNotNullAndEmpty(entity.getAnalyzeItemId()) || entity.getAnalyzeItemId().equals(UUIDHelper.GUID_EMPTY)) {
            entity.setAnalyzeItemId(analyzeItem.getId());
        }
        // 判断是否需要新增分析方法
        analyzeMethod = getDtoAnalyzeMethod(entity, analyzeMethod);
        if (!StringUtils.isNotNullAndEmpty(entity.getAnalyzeMethodId()) || entity.getAnalyzeMethodId().equals(UUIDHelper.GUID_EMPTY)) {
            entity.setAnalyzeMethodId(analyzeMethod.getId());
        }
        if (StringUtils.isNotNullAndEmpty(entity.getRedAnalyzeItemName())) {
            entity.setFullPinYin(PinYinUtil.getFullSpell(entity.getRedAnalyzeItemName()));
            entity.setPinYin(PinYinUtil.getFirstSpell(entity.getRedAnalyzeItemName()));
        }
        entity.setTestName(entity.getRedAnalyzeItemName() + '-' + entity.getRedAnalyzeMethodName() + ' ' + (StringUtil.isNull(entity.getRedCountryStandard()) ? "" : entity.getRedCountryStandard()));
        DtoTest dtoTest = super.update(entity);
        saveRedis(dtoTest);
        return dtoTest;
    }

    /**
     * 自增排序值
     */
    @Transactional
    @Override
    public void incrementOrderNum(List<String> ids) {
        List<DtoTest> testList = findRedisByIds(ids);
        for (DtoTest test : testList) {
            DtoTest dto = new DtoTest();
            BeanUtils.copyProperties(test, dto);
            dto.setOrderNum(dto.getOrderNum() + 1);
            saveRedis(dto);
        }
        repository.incrementOrderNum(ids);
    }

    @Transactional
    @Override
    public void decrementOrderNum(List<String> ids) {
        if (ids.size() > 0) {
            List<DtoTest> testList = findRedisByIds(ids);
            for (DtoTest test : testList) {
                DtoTest dto = new DtoTest();
                BeanUtils.copyProperties(test, dto);
                dto.setOrderNum(dto.getOrderNum() - 1);
                saveRedis(dto);
            }
            repository.decrementOrderNum(ids);
        }
    }

    @Override
    public void loadTest(List<DtoTest> testList) {
        List<String> sampleTypeIds = testList.parallelStream().map(DtoTest::getSampleTypeId).collect(Collectors.toList());
        List<DtoSampleType> sampleTypeList = sampleTypeService.findRedisByIds(sampleTypeIds);
        for (DtoTest test : testList) {
            Optional<DtoSampleType> sampleTypeOptional = sampleTypeList.parallelStream()
                    .filter(p -> p.getId().equals(test.getSampleTypeId())).findFirst();
            sampleTypeOptional.ifPresent(sampleType -> test.setSampleTypeName(sampleType.getTypeName()));
        }
    }

    /**
     * 删除测试项目
     */
    @Transactional
    @Override
    public <K extends Serializable> Integer logicDeleteById(K id) {
        List<String> ids = new ArrayList<>();
        ids.add((String) id);
        return this.logicDeleteById(ids);
    }

    /**
     * 批量删除测试项目
     */
    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        // 根据父节点获取子测试项目
        Set<DtoTest> testList = repository.getListByParentIds((List<String>) ids);
        if (StringUtil.isNotNull(testList) && testList.size() > 0) {
            List<String> childIds = testList.stream().map(DtoTest::getId).collect(Collectors.toList());
            repository.updateChildTestNull(childIds);
        }
        return super.logicDeleteById(ids);
    }

    /**
     * 设置查询实体名
     *
     * @param pageBean 分页参数
     * @param criteria 查询条件
     */
    protected void setSelectEntity(PageBean<DtoTest> pageBean, BaseCriteria criteria) {
        // 设置查询的实体类名及别名
        pageBean.setEntityName("DtoTest x");
        // 设置查询返回的字段、实体别名表示所有字段
        pageBean.setSelect("select x");
    }

    /**
     * 分页查询
     */
    @Override
    public void findByPage(PageBean<DtoTest> pageBean, BaseCriteria criteria) {

        TestCriteria testCriteria = (TestCriteria) criteria;
        if (StringUtil.isNotEmpty(testCriteria.getSort())) {
            pageBean.setSort(testCriteria.getSort().replace(",", ""));
        }
        //设置查询实体名
        setSelectEntity(pageBean, criteria);

        testCriteria.setSampleTypeIdsUnderIndustry(sampleTypeService.loadSampleTypeIds((testCriteria.getIndustryTypeId())));

        super.findByPage(pageBean, criteria);

        List<DtoTest> list = pageBean.getData();
        int rowValue = list.size();
        //根据前端传的每页条数来判断是否需要返回额外信息
        //2024-09-20 BUG2024091900600【项目登记-方法标准中，修改方法的下拉框中，显示的还是启用】
        if (pageBean.getRowsPerPage() >= BaseCodeHelper.MAX_ROWS && rowValue > 100) {
            return;
        }
        List<DtoSampleType2Test> type2TestList = new ArrayList<>();
        if (StringUtil.isNotEmpty(testCriteria.getTestTemplate())) {
            type2TestList = sampleType2TestRepository.findBySampleTypeId(testCriteria.getTestTemplate());
        }
        if (StringUtil.isNotEmpty(list)) {

            List<String> testIds = list.stream().map(DtoTest::getId).distinct().collect(Collectors.toList());

            List<String> sampleTypeIds = list.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getSampleTypeId())
                    && !p.getSampleTypeId().equals(UUIDHelper.GUID_EMPTY)).map(DtoTest::getSampleTypeId).distinct().collect(Collectors.toList());

            List<DtoTestExpand> testExpandList = new ArrayList<>();
            if (StringUtil.isNotEmpty(testCriteria.getSmallSampleTypeId())) {
                testExpandList = testExpandRepository.findByTestIdInAndSampleTypeId(testIds, testCriteria.getSmallSampleTypeId());
            }

            List<DtoIndustryType> industryTypeList = new ArrayList<>();

            List<DtoSampleType> sampleTypeList = new ArrayList<>();

            if (sampleTypeIds.size() > 0) {
                sampleTypeList = sampleTypeService.findRedisByIds(sampleTypeIds);
                List<String> industryIds = sampleTypeList.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getIndustryTypeId())
                        && !p.getIndustryTypeId().equals(UUIDHelper.GUID_EMPTY)).map(DtoSampleType::getIndustryTypeId).distinct().collect(Collectors.toList());
                industryTypeList = industryTypeService.findAll(industryIds);
            }

            Boolean isShowDefaultPerson = testCriteria.getIsShowDefaultPerson();

            List<DtoPerson2Test> dtoPerson2Tests = new ArrayList<>();
            List<DtoUser> userList = new ArrayList<>();
            if (StringUtil.isNotNull(isShowDefaultPerson) && isShowDefaultPerson) {
                if (testIds.size() > 0 && sampleTypeIds.size() > 0) {
                    dtoPerson2Tests = person2TestRepository.findByTestIdInAndSampleTypeIdIn(testIds, sampleTypeIds);
                    List<String> personIds = dtoPerson2Tests.stream().map(DtoPerson2Test::getPersonId).distinct().collect(Collectors.toList());
                    if (personIds.size() > 0) {
                        userList = userService.findByIds(personIds);
                    }
                }
            }
            //查询所有已配置的质控限值
            List<DtoQualityControlLimit> qcRanges = qualityControlLimitRepository.findByTestIdIn(testIds);
            List<String> anaMethodIds = list.stream().map(DtoTest::getAnalyzeMethodId).distinct().collect(Collectors.toList());
            List<DtoAnalyzeMethod> analyzeMethodList = StringUtil.isNotEmpty(anaMethodIds) ? analyzeMethodService.findAllDeleted(anaMethodIds) : new ArrayList<>();

            List<DtoTest> allChildTests = StringUtil.isNotEmpty(testIds) ? repository.findByParentIdIn(testIds) : new ArrayList<>();
            List<DtoSampleType2Test> sampleType2TestList = StringUtil.isNotEmpty(testCriteria.getTestTemplate()) ? sampleType2TestRepository.findBySampleTypeId(testCriteria.getTestTemplate()) : new ArrayList<>();
            List<String> sampleType2TestIds = sampleType2TestList.stream().map(DtoSampleType2Test::getTestId).distinct().collect(Collectors.toList());

            for (DtoTest test : list) {
                Optional<DtoSampleType2Test> type2Test = type2TestList.stream().filter(p -> test.getId().equals(p.getTestId())).findFirst();
                if (type2Test.isPresent()) {
                    test.setTimesOrder(type2Test.get().getTimesOrder());
                    test.setSamplePeriod(type2Test.get().getSamplePeriod());
                } else {
                    Optional<DtoTestExpand> testExpandOptional = testExpandList.stream().filter(p -> test.getId().equals(p.getTestId())).findFirst();
                    testExpandOptional.ifPresent(expand -> {
                        test.setTimesOrder(expand.getTimesOrder());
                        test.setSamplePeriod(expand.getSamplePeriod());
                    });
                }
                if (test.getIsTotalTest() && testCriteria.getIsTotal()) {
                    List<DtoTest> childTestsOfTest = allChildTests.stream().filter(t -> t.getParentId().equals(test.getId())).collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(testCriteria.getTestTemplate())) {
                        childTestsOfTest.removeIf(t -> !sampleType2TestIds.contains(t.getId()));
                    }

                    if (StringUtil.isNotEmpty(testCriteria.getAnalyzeItemKey())) {
                        String analyzeItemKey = testCriteria.getAnalyzeItemKey();
                        childTestsOfTest = childTestsOfTest.stream().filter(p -> (StringUtil.isNotEmpty(p.getRedAnalyzeItemName())
                                && p.getRedAnalyzeItemName().contains(analyzeItemKey)) ||
                                (StringUtil.isNotEmpty(p.getFullPinYin()) && p.getFullPinYin().contains(analyzeItemKey)) ||
                                (StringUtil.isNotEmpty(p.getPinYin()) && p.getPinYin().contains(analyzeItemKey))).collect(toList());
                    }
                    test.setChildTest(childTestsOfTest);
                }
                //所属行业类型
                String industryTypeName = "";
                //所属检测类型
                String sampleTypeName = "";
                //质控限制配置状态
                String qcRangeStatus = "";
                String methodRemark = "";

                DtoAnalyzeMethod analyzeMethod = analyzeMethodList.stream().filter(p -> test.getAnalyzeMethodId().equals(p.getId())).findFirst().orElse(null);
                if (StringUtil.isNotNull(analyzeMethod)) {
                    methodRemark = analyzeMethod.getRemark();
                    //是否停用状态标记
                    test.setIsDeactivate(EnumLIM.EnumAnalyzeMethodStatus.停用.getValue().equals(analyzeMethod.getStatus()));

                    //BUG2024110601383 【紧急】【2024-11-6】【lims】【马川江】【年度监测计划】年度监测计划，新增点位，添加测试项目后，列表上“测试项目状态”显示异常。添加成功后，点击编辑，显示正常
                    String testStatus = "";
                    if ((!analyzeMethod.getIsDeleted() && !test.getIsDeleted())
                            || (EnumLIM.EnumAnalyzeMethodStatus.废止.getValue().equals(analyzeMethod.getStatus()) && test.getIsDeleted())) {
                        testStatus = EnumLIM.EnumAnalyzeMethodStatus.getByValue(analyzeMethod.getStatus());
                    } else {
                        testStatus = "删除";
                    }
                    test.setTestStatus(testStatus);
                }

                Optional<DtoSampleType> optionalSampleType = sampleTypeList.stream().filter(p -> p.getId().equals(test.getSampleTypeId())).findFirst();

                if (StringUtil.isNotNull(optionalSampleType) && optionalSampleType.isPresent()) {
                    DtoSampleType sam = optionalSampleType.get();
                    sampleTypeName = sam.getTypeName();
                    String industryTypeId = sam.getIndustryTypeId();
                    Optional<DtoIndustryType> optionalIndustryType = industryTypeList.stream().filter(p -> p.getId().equals(industryTypeId)).findFirst();
                    if (StringUtil.isNotNull(optionalIndustryType) && optionalIndustryType.isPresent()) {
                        industryTypeName = optionalIndustryType.get().getIndustryName();
                    }
                }

                Long count = qcRanges.stream().filter(p -> test.getId().equals(p.getTestId())).count();
                if (count > 0) {
                    qcRangeStatus = EnumLIM.EnumQCRangeStatus.已配置.toString();
                } else {
                    qcRangeStatus = EnumLIM.EnumQCRangeStatus.未配置.toString();
                }
                if (StringUtil.isNotNull(isShowDefaultPerson) && isShowDefaultPerson && StringUtil.isNotNull(dtoPerson2Tests)) {
                    DtoPerson2Test dtoPerson2Test = dtoPerson2Tests.stream().filter(p -> p.getTestId().equals(test.getId()) && p.getSampleTypeId().equals(test.getSampleTypeId()) && p.getIsDefaultPerson()).findFirst().orElse(null);
                    if (StringUtil.isNotNull(dtoPerson2Test) && StringUtil.isNotNull(userList)) {
                        DtoUser user = userList.stream().filter(p -> p.getId().equals(dtoPerson2Test.getPersonId())).findFirst().orElse(null);
                        if (StringUtil.isNotNull(user)) {
                            test.setDefaultPerson(user.getUserName());
                        }
                    }
                }
                test.setIndustryTypeName(industryTypeName);
                test.setSampleTypeName(sampleTypeName);
                test.setQcRangeStatus(qcRangeStatus);
                test.setMethodRemark(methodRemark);
                test.setTestName(test.getRedAnalyzeItemName() + '-' + test.getRedAnalyzeMethodName() + ' ' + (StringUtil.isNull(test.getRedCountryStandard()) ? "" : test.getRedCountryStandard()));
            }
        }
        // 重新封装到pageBean返回到上层
        pageBean.setData(list);
    }

    /**
     * 新增子测试项目
     */
    @Transactional
    @Override
    public Integer addSonTests(String id, Collection<String> idList) {
        List<DtoTest> list = repository.findAll(idList);
        list.forEach(t -> t.setParentId(id));
        return repository.save(list).size();
    }

    /**
     * 删除子测试项目
     */
    @Transactional
    @Override
    public Integer deleteSonTests(String id, Collection<String> idList) {
        List<DtoTest> list = repository.findAll(idList);
        list = list.stream().filter(t -> id.equals(t.getParentId()) && Boolean.FALSE.equals(t.getIsDeleted())).collect(Collectors.toList());
        list.forEach(t -> t.setParentId(UUIDHelper.GUID_EMPTY));
        return repository.save(list).size();
    }

    /**
     * 获取子测试项目列表
     */
    @Transactional
    @Override
    public Set<DtoTest> getSonTestList(String id) {
        List<String> ids = new ArrayList<>();
        ids.add(id);
        return repository.getListByParentIds(ids);
    }

    /**
     * 根据分析方法id删除测试项目
     */
    @Override
    @Transactional
    public Integer deleteByAnalyzeMethodIds(Collection<String> ids) {
        List<DtoTest> testList = repository.getListByAnalyzeMethodId(ids);
        List<String> testIds = testList.stream().map(DtoTest::getId).collect(Collectors.toList());
        if (testIds.size() > 0) {
            return super.logicDeleteById(testIds);
        }
        return 0;
    }

    @Override
    public List<DtoTest> findRedisByIds(List<String> ids) {
        String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_Test.getValue());
        List<Object> dataList = redisTemplate.opsForHash().multiGet(key, ids);
        return setMaps(dataList, ids);
    }

    @Override
    public DtoTest findOne(String id) {
        DtoTest dtoTest = super.findOne(id);
        saveRedis(dtoTest);
        getTestExpand(Collections.singletonList(dtoTest));
        // 一体化项目查询，可能机构下不存在当前测试项目，所以判空
        if (StringUtil.isNotNull(dtoTest) && !StringUtils.isNotNull(dtoTest.getSamplePeriod())) {
            dtoTest.setSamplePeriod(1);
        }
        return dtoTest;
    }

    /**
     * 根据分析方法id获取测试项目
     */
    @Override
    public List<DtoTest> getListByAnalyzeMethodId(String analyzeMethodId) {
        Collection<String> ids = new ArrayList<>();
        ids.add(analyzeMethodId);
        return repository.getListByAnalyzeMethodId(ids);
    }

    /**
     * 获取默认的测试项目
     *
     * @param sampleTypeId   检测类型id
     * @param analyseItemIds 分析项目id集合
     * @return 测试项目集合
     */
    @Override
    public List<DtoTest> findCommonTestBySampleTypeIdAndAnalyzeItemIdIn(String sampleTypeId, List<String> analyseItemIds) {
        List<DtoTest> tests = repository.findBySampleTypeIdAndAnalyzeItemIdIn(sampleTypeId, analyseItemIds);
        return new ArrayList<>(tests.stream().sorted(Comparator.comparing(DtoTest::getOrderNum).reversed()).
                collect(Collectors.groupingBy(DtoTest::getAnalyzeItemId, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0)))).values());
    }

    @Override
    public List<DtoAnalyzeItem> findAnalyzeItemBySampleTypeId(String sampleTypeId) {
        Map<String, Object> values = new HashMap<>();
        StringBuilder condition = new StringBuilder("select a from DtoAnalyzeItem a where 1=1 and a.isDeleted=0 ");
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            condition.append(" and a.orgId=:orgId");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        if (StringUtils.isNotNullAndEmpty(sampleTypeId)
                && !sampleTypeId.equals(UUIDHelper.GUID_EMPTY)) {
            condition.append(" and exists (select 1 from DtoTest b where b.isDeleted = 0 and b.analyzeItemId = a.id ");
            condition.append(" and b.sampleTypeId=:sampleTypeId)");
            values.put("sampleTypeId", sampleTypeId);
        }
        condition.append(" order by analyzeItemName");
        return comRepository.find(condition.toString(), values);
    }

    @Override
    public List<DtoTest> findAllDeleted(Collection<String> ids) {
        return repository.findAllDeleted(ids);
    }

    @Override
    public List<DtoTest> findAllDeleted() {
        return repository.findAllDeleted();
    }

    /**
     * 根据分析项目id获取测试项目
     *
     * @param itemId 分析项目id
     * @return 测试项目集合
     */
    @Override
    public List<DtoTest> findByAnalyzeItemId(String itemId) {
        return repository.findByAnalyzeItemId(itemId);
    }

    /**
     * 修改分析项目信息
     *
     * @param id         测试项目id
     * @param tName      测试项目名称
     * @param aName      分析项目名称
     * @param fullPinYin 全拼
     * @param pinYin     拼音
     * @return 修改个数
     */
    @Override
    public Integer updateAnalyzeItemInfo(String id, String tName, String aName, String fullPinYin, String pinYin) {
        return repository.updateAnalyzeItemInfo(id, tName, aName, fullPinYin, pinYin);
    }

    @Override
    public List<DtoTest> findByAnalyzeItemNames(Collection<String> analyzeItemNames) {
        return repository.findByRedAnalyzeItemNameIn(analyzeItemNames);
    }

    /**
     * 修改分析项目信息
     *
     * @param id                   测试项目id
     * @param itemStatisticalAlias 分析项目统计别名
     * @return 修改个数
     */
    @Override
    public Integer updateAnalyzeItemStatisticalAlias(String id, String itemStatisticalAlias) {
        return repository.updateAnalyzeItemStatisticalAlias(id, itemStatisticalAlias);
    }

    /**
     * 根据检测类型id获取测试项目
     *
     * @param typeId 检测类型id
     * @return 测试项目集合
     */
    @Override
    public List<DtoTest> findBySampleTypeId(String typeId) {
        return repository.findBySampleTypeId(typeId);
    }

    /**
     * 检出限类型查询
     *
     * @return 检出限类型
     */
    @Override
    public List<Object> examLimitType() {
        List<Object> limitTypes = new ArrayList<>();
        for (EnumLIM.EnumExamLimitType c : EnumLIM.EnumExamLimitType.values()) {
            Map<String, String> typeMap = new HashMap<>();
            typeMap.put("key", c.getValue());
            typeMap.put("value", c.getKey());
            limitTypes.add(typeMap);
        }
        return limitTypes;
    }

    @Override
    public List<DtoTest> findAll(Collection<String> keys) {
        List<DtoTest> dataList = new ArrayList<>();
        if (StringUtil.isNotEmpty(keys)) {
            dataList = repository.findAll(keys);
        }
        if (StringUtil.isNotEmpty(dataList)) {
            Set<String> sampleTypeIds = dataList.stream().map(DtoTest::getSampleTypeId).collect(Collectors.toSet());
            List<DtoSampleType> sampleTypeList = sampleTypeService.findAll(sampleTypeIds);
            for (DtoTest dtoTest : dataList) {
                Optional<DtoSampleType> sampleTypeOptional = sampleTypeList.stream()
                        .filter(s -> s.getId().equals(dtoTest.getSampleTypeId())).findFirst();
                sampleTypeOptional.ifPresent(p -> dtoTest.setSampleTypeName(p.getTypeName()));
            }
        }
        getTestExpand(dataList);
        return dataList;
    }

    /**
     * 迁移导出
     *
     * @param criteria 筛选条件
     * @param response 相应流
     */
    @Override
    public void migrationExport(BaseCriteria criteria, HttpServletResponse response) {
        PageBean<DtoTest> page = new PageBean<>();
        page.setPageNo(1);
        page.setRowsPerPage(Integer.MAX_VALUE);
        TestCriteria testCriteria = (TestCriteria) criteria;
        testCriteria.setValidate(EnumLIM.EnumCodeValidate.已验证.getValue());
        findByPage(page, testCriteria);
        List<DtoTest> dtoTests = page.getData();
        DtoTestDependentData dtoTestDependentData = new DtoTestDependentData();
        List<DtoExportTest> testList = new ArrayList<>();
        List<DtoExportAnalyzeItem> analyzeItemList = new ArrayList<>();
        List<DtoExportAnalyzeMethod> analyzeMethodList = new ArrayList<>();
        List<DtoExportDimension> dimensionList = new ArrayList<>();
        List<DtoExportParams> paramsList = new ArrayList<>();
        List<DtoExportTestExpand> testExpandList = new ArrayList<>();
        List<DtoExportQualityControlLimit> qualityControlLimits = new ArrayList<>();
        List<DtoExportParamsFormula> paramsFormulaList = new ArrayList<>();
        List<DtoExportParamsTestFormula> paramsTestFormulaList = new ArrayList<>();
        List<DtoExportParamsPartFormula> paramsPartFormulaList = new ArrayList<>();
        if (StringUtil.isNotEmpty(dtoTests)) {
            //处理存在总称的数据
            List<String> parentIds = dtoTests.stream().filter(DtoTest::getIsTotalTest).map(DtoTest::getId).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(parentIds)) {
                dtoTests.addAll(repository.findByParentIdIn(parentIds));
            }
            List<String> testIds = dtoTests.stream().map(DtoTest::getId).collect(Collectors.toList());
            // 获取公式数据
            List<DtoParamsFormula> paramsFormulas = paramsFormulaRepository.findByObjectIds(testIds);
            paramsFormulas = paramsFormulas.stream().filter(p -> p.getValidate().equals(EnumLIM.EnumCodeValidate.已验证.getValue())).collect(Collectors.toList());
            // 获取公式参数数据
            List<String> paramsFormulaIds = paramsFormulas.stream().map(DtoParamsFormula::getId).collect(Collectors.toList());
            List<DtoParamsTestFormula> dtoParamsTestFormulas = paramsTestFormulaRepository.findByObjIdIn(paramsFormulaIds);
            //获取测试项目其他公式
            List<DtoParamsPartFormula> partFormulas = paramsPartFormulaRepository.findByFormulaIdIn(paramsFormulaIds);

            // 公式参数内的量纲
            List<String> testFormulasDimensionIds = dtoParamsTestFormulas.stream().map(DtoParamsTestFormula::getDimension).distinct().collect(Collectors.toList());
            testFormulasDimensionIds.removeIf(StringUtil::isEmpty);
            List<DtoDimension> testFormulasDimensionList = StringUtil.isNotEmpty(testFormulasDimensionIds) ? dimensionRepository.findByDimensionNameIn(testFormulasDimensionIds) : new ArrayList<>();
            dimensionList = dimensionMapper.toExportDimensionList(testFormulasDimensionList);
            // 公式参数数据
            List<String> paramsIds = dtoParamsTestFormulas.stream().map(DtoParamsTestFormula::getParamsId).distinct().collect(Collectors.toList());
            List<DtoParams> params = StringUtil.isNotEmpty(paramsIds) ? paramsRepository.findAll(paramsIds) : new ArrayList<>();
            // 检测类型数据
            List<String> sampleTypeIds = paramsFormulas.stream().map(DtoParamsFormula::getSampleTypeId).distinct().collect(Collectors.toList());
            List<DtoSampleType> sampleTypeList = sampleTypeService.findRedisByIds(sampleTypeIds);
            Map<String, String> sampleTypeMap = sampleTypeList.stream().collect(Collectors.toMap(DtoSampleType::getId, DtoSampleType::getTypeName));

            String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_TestDependentData.getValue());
            List<Object> dataList = redisTemplate.opsForHash().multiGet(key, testIds);
            dataList = dataList.stream().filter(Objects::nonNull).collect(Collectors.toList());

            if (StringUtil.isNotEmpty(dataList) && dataList.size() == dtoTests.size()) {
                TypeLiteral<DtoTestDependentData> typeLiteral = new TypeLiteral<DtoTestDependentData>() {
                };
                for (Object s : dataList) {
                    try {
                        DtoTestDependentData dependentData = JsonIterator.deserialize(s.toString(), typeLiteral);
                        if (StringUtil.isNotNull(dependentData)) {
                            testList.addAll(dependentData.getTestList());
                            dimensionList.addAll(dependentData.getDimensionList());
                            analyzeItemList.addAll(dependentData.getAnalyzeItemList());
                            analyzeMethodList.addAll(dependentData.getAnalyzeMethodList());
                            testExpandList.addAll(dependentData.getTestExpandList());
                            qualityControlLimits.addAll(dependentData.getQualityControlLimitList());
                        }
                    } catch (Exception ex) {
                        log.error(ex.getMessage(), ex);
                    }
                }

            } else {
                // 获取质控限值
                List<DtoQualityControlLimit> qualityControlLimitList = qualityControlLimitRepository.findByTestIdIn(testIds);
                List<DtoTestDependentData> dependentDatas = markersDataListener.processMarkersData(dtoTests, qualityControlLimitList);
                for (DtoTestDependentData dependentData : dependentDatas) {
                    testList.addAll(dependentData.getTestList());
                    dimensionList.addAll(dependentData.getDimensionList());
                    analyzeItemList.addAll(dependentData.getAnalyzeItemList());
                    analyzeMethodList.addAll(dependentData.getAnalyzeMethodList());
                    testExpandList.addAll(dependentData.getTestExpandList());
                    qualityControlLimits.addAll(dependentData.getQualityControlLimitList());
                }
            }
            if (StringUtil.isNotEmpty(paramsFormulas)) {
                paramsFormulas.forEach(p -> {
                    DtoExportParamsFormula dtoExportParamsFormula = paramsFormulaMapper.toExportParamsFormula(p);
//                    BeanUtils.copyProperties(p, dtoExportParamsFormula);
                    dtoExportParamsFormula.setSampleTypeId(sampleTypeMap.getOrDefault(dtoExportParamsFormula.getSampleTypeId(), ""));
                    paramsFormulaList.add(dtoExportParamsFormula);
                });
            }
            if (StringUtil.isNotEmpty(dtoParamsTestFormulas)) {
                paramsTestFormulaList = paramsTestFormulaMapper.toExportParamsTestFormulaList(dtoParamsTestFormulas);
            }
            if (StringUtils.isNotNullAndEmpty(partFormulas)) {
                paramsPartFormulaList = paramsPartFormulaMapper.toExportParamsPartFormulaList(partFormulas);
            }

            if (StringUtils.isNotNullAndEmpty(params)) {
                paramsList = paramsMapper.toExportParamsList(params);
            }
        }
        dtoTestDependentData.setTestList(testList.stream().distinct().collect(Collectors.toList()));
        dtoTestDependentData.setDimensionList(dimensionList.stream().distinct().collect(Collectors.toList()));
        dtoTestDependentData.setParamsList(paramsList);
        dtoTestDependentData.setAnalyzeItemList(analyzeItemList.stream().distinct().collect(Collectors.toList()));
        dtoTestDependentData.setAnalyzeMethodList(analyzeMethodList.stream().distinct().collect(Collectors.toList()));
        dtoTestDependentData.setTestExpandList(testExpandList);
        dtoTestDependentData.setQualityControlLimitList(qualityControlLimits);
        dtoTestDependentData.setParamsFormulaList(paramsFormulaList);
        dtoTestDependentData.setParamsTestFormulaList(paramsTestFormulaList);
        dtoTestDependentData.setParamsPartFormulaList(paramsPartFormulaList);
        try {
            Workbook workbook = importUtils.multiSheetWorkbook(dtoTestDependentData);
            PoiExcelUtils.downLoadExcel("测试项目迁移数据", response, workbook);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    /**
     * 重置验证
     *
     * @param ids 测试项目ids
     */
    @Transactional
    @Override
    public void resetValidate(List<String> ids) {
        // 删除redis缓存
        String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_TestDependentData.getValue());
        redisTemplate.opsForHash().delete(key, ids.toArray());
        List<DtoTest> tests = repository.findAll(ids);
        // 获取公式数据
        List<DtoParamsFormula> paramsFormulas = paramsFormulaRepository.findByObjectIds(ids);
        paramsFormulas = paramsFormulas.stream().filter(p -> p.getValidate().equals(EnumLIM.EnumCodeValidate.已验证.getValue())).collect(Collectors.toList());
        // 质控限值
        List<DtoQualityControlLimit> qualityControlLimitList = qualityControlLimitRepository.findByTestIdIn(ids);
        // 重置验证状态
        tests.forEach(p -> {
            p.setUsageNum(0);
            p.setValidate(EnumLIM.EnumCodeValidate.未验证.getValue());
        });
        paramsFormulas.forEach(p -> {
            p.setUsageNum(0);
            p.setValidate(EnumLIM.EnumCodeValidate.未验证.getValue());
        });
        qualityControlLimitList.forEach(p -> {
            p.setUsageNum(0);
            p.setValidate(EnumLIM.EnumCodeValidate.未验证.getValue());
        });
        repository.batchUpdate(tests);
        paramsFormulaRepository.save(paramsFormulas);
        qualityControlLimitRepository.save(qualityControlLimitList);
    }

    @Override
    @Transactional
    public void batchSetTest(Map<String, Object> test) {
        List<String> testIds = StringUtil.isNotNull(test.get("testIds")) ? (List<String>) test.get("testIds") : new ArrayList<>();
        List<DtoTest> tests = StringUtil.isNotEmpty(testIds) ? findAll(testIds) : new ArrayList<>();
        tests.forEach(t -> {
            if (StringUtil.isNotNull(test.get("cert"))) {
                t.setCert(StringUtils.isNotNullAndEmpty(test.get("cert")) ? (Integer) test.get("cert") : -1);
            }
            if (StringUtil.isNotNull(test.get("airPollution"))) {
                t.setAirPollution(test.get("airPollution").toString());
            }
            if (StringUtil.isNotNull(test.get("examLimitValueLess"))) {
                t.setExamLimitValueLess(test.get("examLimitValueLess").toString());
            }
            if (StringUtil.isNotNull(test.get("isQCB"))) {
                t.setIsQCB((Boolean) test.get("isQCB"));
            }
            if (StringUtil.isNotNull(test.get("isQCInstrument"))) {
                t.setIsQCInstrument((Boolean) test.get("isQCInstrument"));
            }
            if (StringUtil.isNotNull(test.get("isQCTransport"))) {
                t.setIsQCTransport((Boolean) test.get("isQCTransport"));
            }
            if (StringUtil.isNotNull(test.get("isQCP"))) {
                t.setIsQCP((Boolean) test.get("isQCP"));
            }
            if (StringUtil.isNotNull(test.get("isMMP"))) {
                t.setIsMMP((Boolean) test.get("isMMP"));
            }
            if (StringUtil.isNotNull(test.get("isSeries"))) {
                t.setIsSeries((Boolean) test.get("isSeries"));
            }
            if (StringUtil.isNotNull(test.get("isOutsourcing"))) {
                t.setIsOutsourcing((Boolean) test.get("isOutsourcing"));
            }
            if (StringUtil.isNotNull(test.get("isSamplingOut"))) {
                t.setIsSamplingOut((Boolean) test.get("isSamplingOut"));
            }
            if (StringUtil.isNotNull(test.get("isQCLocal"))) {
                t.setIsQCLocal((Boolean) test.get("isQCLocal"));
            }
        });
        List<DtoTest> result = save(tests);
        result.forEach(this::saveRedis);
    }

    @Override
    public Map<String, Integer> findComputeMode() {
        Map<String, Integer> result = new HashMap<>();
        for (EnumBase.EnumComputeMode mode : EnumBase.EnumComputeMode.values()) {
            result.put(mode.name(), mode.getValue());
        }
        return result;
    }

    /**
     * 根据分析方法状态剔除测试项目（停用、废止）
     *
     * @param testList 测试项目集合
     */
    @Override
    public void removeByMethodStatus(List<DtoTest> testList) {
        // 根据分析方法状态，剔除停用、废止的方法对应的测试项目
        List<String> analyzeMethodIds = testList.stream().map(DtoTest::getAnalyzeMethodId).distinct().collect(Collectors.toList());
        if (StringUtil.isNotEmpty(analyzeMethodIds)) {
            List<String> filterMethodIds = analyzeMethodRepository.findAllDeleted(analyzeMethodIds).stream().filter(p -> EnumLIM.EnumAnalyzeMethodStatus.停用.getValue().equals(p.getStatus())
                    || EnumLIM.EnumAnalyzeMethodStatus.废止.getValue().equals(p.getStatus())).map(DtoAnalyzeMethod::getId).collect(toList());
            testList.removeIf(p -> filterMethodIds.contains(p.getAnalyzeMethodId()));
        }
    }

    /**
     * 根据分析方法状态（停用、废止）过滤出相应测试项目
     *
     * @param testList 测试项目集合
     * @return 满足条件的测试项目集合
     */
    @Override
    public List<DtoTest> filterByMethodStatus(List<DtoTest> testList) {
        List<DtoTest> fltTestList = new ArrayList<>();
        List<String> analyzeMethodIds = testList.stream().map(DtoTest::getAnalyzeMethodId).distinct().collect(Collectors.toList());
        if (StringUtil.isNotEmpty(analyzeMethodIds)) {
            List<String> filterMethodIds = analyzeMethodRepository.findAllDeleted(analyzeMethodIds).stream().filter(p -> EnumLIM.EnumAnalyzeMethodStatus.停用.getValue().equals(p.getStatus())
                    || EnumLIM.EnumAnalyzeMethodStatus.废止.getValue().equals(p.getStatus())).map(DtoAnalyzeMethod::getId).collect(toList());
            fltTestList = testList.stream().filter(p -> filterMethodIds.contains(p.getAnalyzeMethodId())).collect(toList());
        }
        return fltTestList;
    }

    @Override
    public List<DtoTest> getTestTotal(List<DtoTest> testList, List<DtoTest> parentTestList) {
        //按照父id分组
        Map<String, List<DtoTest>> parentId2TestListMap = testList.stream().collect(Collectors.groupingBy(DtoTest::getParentId));
        List<DtoTest> mergedTestList = new ArrayList<>();
        for (Map.Entry<String, List<DtoTest>> entry : parentId2TestListMap.entrySet()) {
            String parentId = entry.getKey();
            List<DtoTest> loopTestList = entry.getValue();
            //获取父测试项目
            DtoTest parentTest = parentTestList.stream().filter(p -> parentId.equals(p.getId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(parentTest)) {
                if (parentTest.getIsTotalTest() && loopTestList.size() > parentTest.getMergeBase()) {
                    //合并显示
                    mergedTestList.add(parentTest);
                } else {
                    //存在父测试项目，是否总称开关关闭时，多个因子放在一起展示
                    mergedTestList.addAll(loopTestList);
                }
            } else {
                mergedTestList.addAll(loopTestList);
            }
        }
        return mergedTestList;
    }


    @Override
    public void combineStatistical(PageBean<DtoTest> pageBean, BaseCriteria criteria) {
        long t1 = System.currentTimeMillis();
        List<DtoTest> list = new ArrayList<>();
        //排除非认证认可原始数据
        List<DtoTest> data = repository.findByCertInAndIsDeletedFalse(Stream.of(1, 2, 4).collect(Collectors.toList()));
        long t2 = System.currentTimeMillis();
        log.info("测试项目查询耗时：" + (t2 - t1));
        // 按方法合并：别名合并
        List<DtoTest> methodCopyData = new ArrayList<>(data);
        List<DtoTest> dataWithMethodAliasList = methodCopyData.stream().filter(v -> StringUtil.isNotEmpty(v.getMethodStatisticalAlias())).collect(Collectors.toList());
        Map<String, List<DtoTest>> methodGroupMap = dataWithMethodAliasList.stream().collect(Collectors.groupingBy(DtoTest::getMethodStatisticalAlias));
        for (Map.Entry<String, List<DtoTest>> entry : methodGroupMap.entrySet()) {
            DtoTest entity = entry.getValue().get(0);
            List<DtoTest> copyList = new ArrayList<>();
            entry.getValue().forEach(v -> {
                DtoTest copy = new DtoTest();
                BeanUtils.copyProperties(v, copy, "childTest");
                copyList.add(copy);
            });
            DtoTest copy = new DtoTest();
            BeanUtils.copyProperties(entity, copy, "childTest");
            copy.setChildTest(copyList);
            list.add(copy);
        }
        methodCopyData.removeAll(dataWithMethodAliasList);
        //没有配方法别名也要按照分析项目名称合并
        Map<String, List<DtoTest>> methodGroupMap2 = methodCopyData.stream().collect(Collectors.groupingBy(DtoTest::getRedAnalyzeMethodName));
        for (Map.Entry<String, List<DtoTest>> entry : methodGroupMap2.entrySet()) {
            DtoTest entity = entry.getValue().get(0);
            List<DtoTest> copyList = new ArrayList<>();
            entry.getValue().forEach(v -> {
                DtoTest copy = new DtoTest();
                BeanUtils.copyProperties(v, copy, "childTest");
                copyList.add(copy);
            });
            DtoTest copy = new DtoTest();
            BeanUtils.copyProperties(entity, copy, "childTest");
            copy.setChildTest(copyList);
            list.add(copy);
        }
        long t3 = System.currentTimeMillis();
        log.info("方法分组耗时：" + (t3 - t2));
        // 按项目统计：方法别名合并后，处理总称后合并项目
        TestCriteria testCriteria = (TestCriteria) criteria;
        if (testCriteria.getIsCombineByItem()) {
            List<DtoTest> tempList = new ArrayList<>();
            List<DtoTest> itemCopyData = new ArrayList<>(data);
            for (DtoTest entity : list) {
                List<DtoTest> childTest = entity.getChildTest();
                List<String> methodIds = childTest.stream().map(DtoTest::getAnalyzeMethodId).collect(Collectors.toList());
                //排除掉子测试项目
                List<String> parentIds = childTest.stream().filter(Test::getIsTotalTest).map(DtoTest::getId).collect(Collectors.toList());
                childTest.removeIf(v -> !v.getIsTotalTest() && parentIds.contains(v.getParentId()));
                List<DtoTest> waitItemCombineList = new ArrayList<>();
                for (DtoTest child : childTest) {
                    //非合并统计的，把子项拆出来然后按照项目别名合并
                    if (child.getIsTotalTest() && !child.getIsStatisticalCombine()) {
                        waitItemCombineList.addAll(itemCopyData.stream().filter(v -> child.getId().equals(v.getParentId())).collect(Collectors.toList()));
                    } else {
                        waitItemCombineList.add(child);
                    }
                }
                List<DtoTest> dataWithItemAliasList = waitItemCombineList.stream().filter(v -> StringUtil.isNotEmpty(v.getItemStatisticalAlias())).collect(Collectors.toList());
                Map<String, List<DtoTest>> itemGroupMap = dataWithItemAliasList.stream().collect(Collectors.groupingBy(DtoTest::getItemStatisticalAlias));
                List<DtoTest> itemGroupList = new ArrayList<>();
                for (Map.Entry<String, List<DtoTest>> entry : itemGroupMap.entrySet()) {
                    DtoTest GroupEntity = entry.getValue().get(0);
                    List<DtoTest> copyList = new ArrayList<>();
                    entry.getValue().forEach(v -> {
                        DtoTest copy = new DtoTest();
                        BeanUtils.copyProperties(v, copy, "childTest");
                        copyList.add(copy);
                    });
                    DtoTest copy = new DtoTest();
                    BeanUtils.copyProperties(GroupEntity, copy, "childTest");
                    copy.setChildTest(copyList);
                    itemGroupList.add(copy);
                }
                waitItemCombineList.removeAll(dataWithItemAliasList);
                Map<String, List<DtoTest>> itemGroupMap2 = waitItemCombineList.stream().collect(Collectors.groupingBy(DtoTest::getRedAnalyzeItemName));
                for (Map.Entry<String, List<DtoTest>> entry : itemGroupMap2.entrySet()) {
                    DtoTest GroupEntity = entry.getValue().get(0);
                    List<DtoTest> copyList = new ArrayList<>();
                    entry.getValue().forEach(v -> {
                        DtoTest copy = new DtoTest();
                        BeanUtils.copyProperties(v, copy, "childTest");
                        copyList.add(copy);
                        //总称及合并显示的，child要把子项带过来
                        if (v.getIsTotalTest() && v.getIsStatisticalCombine()) {
                            List<DtoTest> extraChildList = itemCopyData.stream().filter(e -> v.getId().equals(e.getParentId())).collect(Collectors.toList());
                            extraChildList.forEach(e -> {
                                DtoTest copy2 = new DtoTest();
                                BeanUtils.copyProperties(e, copy2, "childTest");
                                copyList.add(copy2);
                            });
                        }
                    });
                    DtoTest copy = new DtoTest();
                    BeanUtils.copyProperties(GroupEntity, copy, "childTest");
                    copy.setChildTest(copyList);
                    itemGroupList.add(copy);
                }
                // 合并组
                for (DtoTest itemGroup : itemGroupList) {
                    List<String> itemIds = itemGroup.getChildTest().stream().map(DtoTest::getAnalyzeItemId).collect(Collectors.toList());
                    List<DtoTest> childList = data.stream().filter(v -> methodIds.contains(v.getAnalyzeMethodId())
                            && itemIds.contains(v.getAnalyzeItemId())).collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(childList)) {
                        List<DtoTest> copyList = new ArrayList<>();
                        childList.forEach(v -> {
                            DtoTest copy = new DtoTest();
                            BeanUtils.copyProperties(v, copy, "childTest");
                            copyList.add(copy);
                        });
                        DtoTest copy = new DtoTest();
                        BeanUtils.copyProperties(itemGroup, copy, "childTest");
                        copy.setChildTest(copyList);
                        tempList.add(copy);
                    }
                }
            }
            list = tempList;
        }
        long t4 = System.currentTimeMillis();
        log.info("因子分组耗时：" + (t4 - t3));
        list.forEach(v -> {
            v.setItemAliasText(StringUtil.isNotEmpty(v.getItemStatisticalAlias()) ? v.getItemStatisticalAlias() : v.getRedAnalyzeItemName());
            v.setMethodAliasText(StringUtil.isNotEmpty(v.getMethodStatisticalAlias()) ? v.getMethodStatisticalAlias() : v.getRedAnalyzeMethodName());
        });
        //检索  检测类型    分析方法/分析方法别名/标准编号   分析项目/分析项目别名
        if (StringUtil.isNotEmpty(testCriteria.getAnalyzeItemKey())) {
            list = list.stream().filter(v -> StringUtil.isNotEmpty(v.getItemAliasText()) && v.getItemAliasText().contains(testCriteria.getAnalyzeItemKey()))
                    .collect(Collectors.toList());
        }
        if (StringUtil.isNotEmpty(testCriteria.getAnalyzeMethodKey())) {
            list = list.stream().filter(v -> (StringUtil.isNotEmpty(v.getMethodAliasText()) && v.getMethodAliasText().contains(testCriteria.getAnalyzeMethodKey()))
                    || (StringUtil.isNotEmpty(v.getRedCountryStandard()) && v.getRedCountryStandard().contains(testCriteria.getAnalyzeMethodKey())))
                    .collect(Collectors.toList());
        }
        if (StringUtil.isNotEmpty(testCriteria.getSampleTypeId())) {
            list = list.stream().filter(v -> testCriteria.getSampleTypeId().equals(v.getSampleTypeId())).collect(Collectors.toList());
        }
        if (testCriteria.getCert() != null) {
            list = list.stream().filter(v -> testCriteria.getCert().equals(v.getCert())).collect(Collectors.toList());
        }
        pageBean.setRowsCount(list.size());
        // 排序分页
        if (testCriteria.getIsCombineByItem()) {
            list.sort(Comparator.comparing(DtoTest::getSampleTypeId).thenComparing(DtoTest::getItemAliasText).thenComparing(DtoTest::getMethodAliasText));
        } else {
            list.sort(Comparator.comparing(DtoTest::getSampleTypeId).thenComparing(DtoTest::getMethodAliasText));
        }
        list = list.stream().skip((long) (pageBean.getPageNo() - 1) * pageBean.getRowsPerPage()).limit(pageBean.getRowsPerPage()).collect(Collectors.toList());
        long t5 = System.currentTimeMillis();
        log.info("检索及分页耗时：" + (t5 - t4));
        //附加字段
        if (StringUtil.isNotEmpty(list)) {
            List<String> sampleTypeIds = list.stream().map(DtoTest::getSampleTypeId).collect(Collectors.toList());
            List<DtoSampleType> sampleTypeList = sampleTypeRepository.findAll(sampleTypeIds);
            Map<String, DtoSampleType> sampleTypeMap = sampleTypeList.stream().collect(Collectors.toMap(DtoSampleType::getId, p -> p));
            List<String> industryTypeIds = sampleTypeList.stream().map(DtoSampleType::getIndustryTypeId).collect(Collectors.toList());
            List<DtoIndustryType> industryTypeList = industryTypeRepository.findAll(industryTypeIds);
            Map<String, String> industryNameMap = industryTypeList.stream().collect(Collectors.toMap(DtoIndustryType::getId, DtoIndustryType::getIndustryName));
            for (DtoTest test : list) {
                DtoSampleType sampleType = sampleTypeMap.get(test.getSampleTypeId());
                if (sampleType != null) {
                    test.setSampleTypeName(sampleType.getTypeName());
                    test.setIndustryTypeName(industryNameMap.getOrDefault(sampleType.getIndustryTypeId(), ""));
                }
                test.getChildTest().forEach(v -> v.setSampleTypeName(sampleTypeMap.containsKey(v.getSampleTypeId()) ? sampleTypeMap.get(v.getSampleTypeId()).getTypeName() : ""));
            }
        }
        pageBean.setData(list);
        long t6 = System.currentTimeMillis();
        log.info("附加字段查询及填充耗时：" + (t6 - t5));
    }

    @Override
    public void combineStatisticalExport(BaseCriteria criteria, HttpServletResponse response) {
        PageBean<DtoTest> pageBean = new PageBean<>();
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        combineStatistical(pageBean, criteria);
        ExportParams exportParams = new ExportParams("检测能力清单", "检测能力清单");
        exportParams.setStyle(ExcelStyle.class);
        List<DtoTest> list = pageBean.getData();
        TestCriteria testCriteria = (TestCriteria) criteria;
        if (testCriteria.getIsCombineByItem()) {
            List<TestItemCombineVO> exportList = new ArrayList<>();
            for (DtoTest test : list) {
                TestItemCombineVO entity = new TestItemCombineVO();
                entity.setItemAliasText(test.getItemAliasText());
                entity.setMethodAliasText(test.getMethodAliasText());
                entity.setRedCountryStandard(test.getRedCountryStandard());
                entity.setSampleTypeName(test.getSampleTypeName());
                entity.setIndustryTypeName(test.getIndustryTypeName());
                entity.setCert(EnumLIM.EnumTestCert.EnumTestCert(test.getCert()));
                entity.setRelatedTestCount(test.getChildTest().size());
                exportList.add(entity);
            }
            PoiExcelUtils.exportExcel(exportList, TestItemCombineVO.class, exportParams.getTitle(), exportParams, response);
        } else {
            List<TestMethodCombineVO> exportList = new ArrayList<>();
            for (DtoTest test : list) {
                TestMethodCombineVO entity = new TestMethodCombineVO();
                entity.setMethodAliasText(test.getMethodAliasText());
                entity.setRedCountryStandard(test.getRedCountryStandard());
                entity.setSampleTypeName(test.getSampleTypeName());
                entity.setIndustryTypeName(test.getIndustryTypeName());
                entity.setCert(EnumLIM.EnumTestCert.EnumTestCert(test.getCert()));
                entity.setRelatedTestCount(test.getChildTest().size());
                exportList.add(entity);
            }
            PoiExcelUtils.exportExcel(exportList, TestMethodCombineVO.class, exportParams.getTitle(), exportParams, response);
        }
    }

    /**
     * 测试扩展信息
     *
     * @param testList 测试项目集合
     */
    private void getTestExpand(List<DtoTest> testList) {
        testList = testList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(testList)) {
            List<String> testIds = testList.stream().map(DtoTest::getId).collect(Collectors.toList());
            List<DtoTestExpand> testExpandList = testExpandRepository.findByTestIdIn(testIds);
            testList.forEach(p -> {
                List<DtoTestExpand> expandList = testExpandList.stream().filter(t -> p.getId().equals(t.getTestId())).collect(Collectors.toList());
                p.setTestExpandList(expandList);
            });
        }
    }

    /**
     * 保存相应的redis数据
     *
     * @param item 测试项目的实体对象
     */
    private void saveRedis(DtoTest item) {
        String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_Test.getValue());
        if (StringUtil.isNotNull(item)) {
            redisTemplate.opsForHash().put(key, item.getId(), JsonStream.serialize(item));
        }
    }

    /**
     * 从redis中获取相应的数据
     *
     * @param dataList 数据集合
     * @return 返回测试项目数据
     */
    private List<DtoTest> setMaps(List<Object> dataList, List<String> ids) {
        List<DtoTest> itemList = new ArrayList<>();
        TypeLiteral<DtoTest> typeLiteral = new TypeLiteral<DtoTest>() {
        };
        List<String> existIds = new ArrayList<>();
        for (Object s : dataList) {
            if (StringUtil.isNotNull(s)) {
                try {
                    DtoTest item = JsonIterator.deserialize(s.toString(), typeLiteral);
                    if (StringUtil.isNotNull(item)) {
                        existIds.add(item.getId());
                        itemList.add(item);
                    }
                } catch (Exception ex) {
                    System.out.println(ex.getMessage());
                }
            }
        }
        //将未缓存到redis的数据缓存起来（需要假删数据，防止业务库调用之后找不到这些数据）
        List<String> newIds = ids.stream().filter(p -> !existIds.contains(p)).collect(Collectors.toList());
        if (newIds.size() > 0) {
            List<DtoTest> dtoTests = repository.findAllDeleted(newIds);
            itemList.addAll(dtoTests);
            Map<String, Object> map = new HashMap<>();
            for (DtoTest dtoTest : dtoTests) {
                map.put(dtoTest.getId(), dtoTests);
                map.put(dtoTest.getId(), JsonStream.serialize(dtoTest));
            }
            String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_Test.getValue());
            redisTemplate.opsForHash().putAll(key, map);

        }
        getTestExpand(itemList);
        return itemList;
    }

    /**
     * 判断测试项目新增的时候是否需要保存分析方法
     *
     * @param entity        实体
     * @param analyzeMethod 分析方法
     * @return 返回相应的数据
     */
    private DtoAnalyzeMethod getDtoAnalyzeMethod(DtoTest entity, DtoAnalyzeMethod analyzeMethod) {
        if (StringUtil.isNull(analyzeMethod)) {
            analyzeMethod = new DtoAnalyzeMethod();
            analyzeMethod.setMethodName(entity.getRedAnalyzeMethodName());
            analyzeMethod.setCountryStandard(entity.getRedCountryStandard());
            analyzeMethodService.save(analyzeMethod);
        }
        return analyzeMethod;
    }

    /**
     * 判断测试项目新增的时候是否需要保存分析项目
     *
     * @param entity      实体
     * @param analyzeItem 分析项目
     * @return 返回相应的数据
     */
    private DtoAnalyzeItem getDtoAnalyzeItem(DtoTest entity, DtoAnalyzeItem analyzeItem) {
        if (StringUtil.isNull(analyzeItem)) {
            analyzeItem = new DtoAnalyzeItem();
            analyzeItem.setAnalyzeItemName(entity.getRedAnalyzeItemName());
            analyzeItem.setOrderNum(0);
            analyzeItemService.save(analyzeItem);
        }
        return analyzeItem;
    }

    @Autowired
    public void setSampleType2TestRepository(SampleType2TestRepository sampleType2TestRepository) {
        this.sampleType2TestRepository = sampleType2TestRepository;
    }

    @Autowired
    public void setTestExpandRepository(TestExpandRepository testExpandRepository) {
        this.testExpandRepository = testExpandRepository;
    }

    @Autowired
    public void setAnalyzeMethodRepository(AnalyzeMethodRepository analyzeMethodRepository) {
        this.analyzeMethodRepository = analyzeMethodRepository;
    }
}