package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;


/**
 * ProjectInstrumentDetails查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectInstrumentDetailsCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private String id;

    /**
     * 检索开始时间
     */
    private String dtBegin;
    /**
     * 检索结束时间
     */
    private String dtEnd;

    /**
     * 出库人
     */
    private String outPerson;

    /**
     * 是否确认
     */
    private Boolean isConfirm;

    /**
     * 项目查询关键字
     */
    private String projectKey;

    /**
     * 仪器查询关键字
     */
    private String instrumentKey;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.id)) {
            condition.append(" and id = :id");
            values.put("id", this.id);
        }
        if (StringUtils.isNotNullAndEmpty(dtBegin)) {
            Date from = DateUtil.stringToDate(this.dtBegin, DateUtil.YEAR);
            condition.append(" and a.outDate >= :from ");
            values.put("from", DateUtil.dateToString(from, DateUtil.FULL));
        }
        if (StringUtils.isNotNullAndEmpty(dtEnd)) {
            Date to = DateUtil.stringToDate(this.dtEnd, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and a.outDate < :to ");
            values.put("to", DateUtil.dateToString(c.getTime(), DateUtil.FULL));
        }
        if (StringUtil.isNotEmpty(this.projectKey)) {
            condition.append(" and (c.projectName like :projectKey or c.projectCode like :projectKey  or b.projectName like :projectKey)");
            values.put("projectKey", "%" + this.projectKey + "%");
        }
        if (StringUtil.isNotEmpty(this.instrumentKey)) {
            condition.append(" and (d.instrumentName like :instrumentKey  or d.model like :instrumentKey) ");
            values.put("instrumentKey", "%" + this.instrumentKey + "%");
        }
        if (StringUtils.isNotNullAndEmpty(this.isConfirm)) {
            condition.append(" and a.isConfirm = :isConfirm ");
            values.put("isConfirm", this.isConfirm);
        }
        if (StringUtils.isNotNullAndEmpty(this.outPerson)) {
            condition.append(" and a.outPerson = :outPerson");
            values.put("outPerson", this.outPerson);
        }
        return condition.toString();
    }
}