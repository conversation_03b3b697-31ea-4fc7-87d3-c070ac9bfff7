package com.sinoyd.lims.lim.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;

/**
 * RecordConfig2ParamsConfig实体
 * <AUTHOR>
 * @version V1.0.0 2022/6/27
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description="RecordConfig2ParamsConfig")
@Data
@EntityListeners(AuditingEntityListener.class)
public class RecordConfig2ParamsConfig implements BaseEntity, Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 报表模板Id
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("''")
    @ApiModelProperty("采样单配置id")
    private String recordConfigId;

    /**
     * 报表模板Id
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("''")
    @ApiModelProperty("参数配置id")
    private String paramsConfigId;
}
