package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoWorkSheetCalibrationCurve;

import java.util.List;


/**
 * WorkSheetCalibrationCurve数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface WorkSheetCalibrationCurveRepository extends IBaseJpaRepository<DtoWorkSheetCalibrationCurve, String> {
    /**
     * 根据检测单ids查询相应的检测单曲线
     *
     * @param workSheetIds 检测单ids
     * @return 返回相应的检测单曲线
     */
    List<DtoWorkSheetCalibrationCurve> findByWorksheetIdIn(List<String> workSheetIds);
}