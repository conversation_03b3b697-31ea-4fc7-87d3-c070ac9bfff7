package com.sinoyd.lims.pro.strategy.context.schemeSynchronization;

import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTestTemp;
import com.sinoyd.lims.pro.strategy.strategy.schemeSynchronization.test.AbsTestStrategy;
import com.sinoyd.lims.strategy.context.schemeSynchronization.TestContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Component
public class TestContextImpl implements TestContext {

    /**
     * 所有具体生成策略字典
     */
    private final Map<String, AbsTestStrategy> testStrategyMap = new ConcurrentHashMap<>();

    @Autowired
    public TestContextImpl(Map<String, AbsTestStrategy> testStrategyMap) {
        this.testStrategyMap.putAll(testStrategyMap);
    }

    @Override
    public void synchronizationTest(Integer operateType, List<DtoSamplingFrequencyTestTemp> samplingFrequencyTestTempList) {
        String beanName = EnumBase.EnumSchemeSynchronization.getBeanName(operateType);
        if (!StringUtil.isNotNull(this.testStrategyMap.get(beanName + "Test"))) {
            throw new BaseException("调用方法不合法");
        }
        this.testStrategyMap.get(beanName + "Test").synchronizationTest(samplingFrequencyTestTempList);
    }
}
