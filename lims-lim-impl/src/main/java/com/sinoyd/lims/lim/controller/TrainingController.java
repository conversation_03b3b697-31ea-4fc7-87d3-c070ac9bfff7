package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.TrainingCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoTraining;
import com.sinoyd.lims.lim.service.TrainingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 培训服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/6
 * @since V100R001
 */
@Api(tags = "示例: 培训服务")
@RestController
@RequestMapping("api/lim/training")
@Validated
public class TrainingController extends BaseJpaController<DtoTraining, String, TrainingService> {

    /**
     * 分页动态条件查询Training
     *
     * @param trainingCriteria 条件参数
     * @return RestResponse<List < Training>>
     */
    @ApiOperation(value = "分页动态条件查询ReportApply", notes = "分页动态条件查询ReportApply")
    @GetMapping
    public RestResponse<List<DtoTraining>> findByPage(TrainingCriteria trainingCriteria) {
        PageBean<DtoTraining> pageBean = super.getPageBean();
        RestResponse<List<DtoTraining>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, trainingCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 根据id查询培训
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id查询培训", notes = "根据id查询培训")
    @GetMapping("/{id}")
    public RestResponse<DtoTraining> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoTraining> restResp = new RestResponse<>();
        DtoTraining entity = service.findOne(id);
        restResp.setData(entity);
        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }


    /**
     * 新增培训
     *
     * @param training 培训实体
     * @return 新增的培训实体
     */
    @ApiOperation(value = "新增培训", notes = "新增培训")
    @PostMapping("")
    public RestResponse<DtoTraining> create(@Validated @RequestBody DtoTraining training) {
        RestResponse<DtoTraining> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoTraining data = service.save(training);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 更新培训
     *
     * @param training 培训实体
     * @return 更新后的培训实体
     */
    @ApiOperation(value = "更新培训", notes = "更新培训")
    @PutMapping("")
    public RestResponse<DtoTraining> update(@Validated @RequestBody DtoTraining training) {
        RestResponse<DtoTraining> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoTraining data = service.update(training);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 单个假删
     *
     * @param id 参数id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.logicDeleteById(id);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 批量假删
     *
     * @param ids 参数ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResponse.setCount(count);

        return restResponse;
    }


}
