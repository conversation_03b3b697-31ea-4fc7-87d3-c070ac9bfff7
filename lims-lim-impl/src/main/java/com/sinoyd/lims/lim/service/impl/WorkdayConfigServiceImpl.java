package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.rcc.DtoWorkdayConfig;
import com.sinoyd.lims.lim.repository.rcc.WorkdayConfigRepository;
import com.sinoyd.lims.lim.service.WorkdayConfigService;
import org.springframework.stereotype.Service;

/**
 * 工作休息日管理配置实现类
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/1/18
 */
@Service
public class WorkdayConfigServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoWorkdayConfig, String, WorkdayConfigRepository> implements WorkdayConfigService {


    @Override
    public DtoWorkdayConfig findByYear(Integer year) {
        return repository.findByYear(year);
    }

}