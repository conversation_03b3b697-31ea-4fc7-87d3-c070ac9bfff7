package com.sinoyd.lims.pro.dto;

import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.entity.SamplingFrequencyTest;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import java.util.ArrayList;
import java.util.List;


/**
 * DtoSamplingFrequencyTest实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_SamplingFrequencyTest")
 @Data
 @DynamicInsert
 public  class DtoSamplingFrequencyTest extends SamplingFrequencyTest {
    private static final long serialVersionUID = 1L;

    public DtoSamplingFrequencyTest() {

    }

    public DtoSamplingFrequencyTest(DtoTest test) {
        this.setTestId(test.getId());
        this.setAnalyseItemId(test.getAnalyzeItemId());
        this.setRedAnalyzeItemName(test.getRedAnalyzeItemName());
        this.setAnalyzeMethodId(test.getAnalyzeMethodId());
        this.setRedAnalyzeMethodName(test.getRedAnalyzeMethodName());
        this.setRedCountryStandard(test.getRedCountryStandard());
        this.setIsCompleteField(test.getIsCompleteField());
        this.setIsOutsourcing(test.getIsOutsourcing());
        this.setIsSamplingOut(test.getIsSamplingOut());
        // 采样方法id
        this.setSamplingMethodId(test.getSamplingMethodId());
    }

    /**
     * 检测类型（小类）id
     */
    @Transient
    private String sampleTypeId;

    /**
     * 总称测试项目id
     */
    @Transient
    private String allTestId = UUIDHelper.GUID_EMPTY;

    /**
     * 频次Id集合（传输用）
     */
    @Transient
    private List<String> samplingFrequencyIds = new ArrayList<>();

    /**
     * 分析项目Id集合（传输用）
     */
    @Transient
    private List<String> analyseItemIds = new ArrayList<>();

    /**
     * 测试项目id集合
     */
    @Transient
    private List<String> testIds;

    /**
     * 样品id集合
     */
    @Transient
    private List<String> sampleIds;
}