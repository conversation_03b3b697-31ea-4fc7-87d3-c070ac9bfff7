package com.sinoyd.lims.foreign.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.foreign.DataProjectVO;
import com.sinoyd.lims.foreign.DataQualityControlevaluateVO;
import com.sinoyd.lims.foreign.DataReportFolderSortInfoVO;
import com.sinoyd.lims.foreign.service.DataQualityControlevaluateService;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 质控评价信息接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Service
public class DataQualityControlevaluateServiceImpl extends AbsQueryDataServiceImpl<DataQualityControlevaluateVO> implements DataQualityControlevaluateService {

    @Override
    public StringBuilder getSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("select id,objectId,qcId,checkItem,judgingMethod,isPass,");
        sql.append("checkItemValue,allowLimit,dimensionName,orgId,domainId ");
        sql.append("from TB_PRO_QualityControlEvaluate ");
        sql.append(" WHERE 1 = 1 ");
        return sql;
    }

    @Override
    protected void appendCondition(StringBuilder sql, Map<String, Object> map, String condition, String paramName) {
        super.appendCondition(sql, map, "AND modifyDate > :syncTime", "syncTime");
        super.appendCondition(sql, map, "AND objectId in (:objectIds)", "objectIds");
    }

    @Override
    protected RowMapper<DataQualityControlevaluateVO> getEntityRowMapper() {
        return (rs, i) -> {
            DataQualityControlevaluateVO qualityControlevaluateVO = new DataQualityControlevaluateVO();
            qualityControlevaluateVO.setId(UUIDHelper.NewID());
            qualityControlevaluateVO.setObjectId(rs.getString("objectId"));
            qualityControlevaluateVO.setQcId(rs.getString("qcId"));
            qualityControlevaluateVO.setCheckItem(rs.getString("checkItem"));
            qualityControlevaluateVO.setJudgingMethod(rs.getInt("judgingMethod"));
            qualityControlevaluateVO.setIsPass(rs.getBoolean("isPass"));
            qualityControlevaluateVO.setCheckItemValue(rs.getString("checkItemValue"));
            qualityControlevaluateVO.setAllowLimit(rs.getString("allowLimit"));
            qualityControlevaluateVO.setDimensionName(rs.getString("dimensionName"));
            qualityControlevaluateVO.setOrgId(rs.getString("orgId"));
            qualityControlevaluateVO.setDomainId(rs.getString("domainId"));
            qualityControlevaluateVO.setSyncTime(new Date());
            return qualityControlevaluateVO;
        };
    }

}
