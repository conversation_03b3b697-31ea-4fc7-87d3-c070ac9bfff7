package com.sinoyd.base.service.impl.divationMethod;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.service.QualityDivationService;
import com.sinoyd.base.utils.base.DivationUtils;
import com.sinoyd.frame.service.CalculationService;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 限值判定
 * <AUTHOR>
 * @version V1.0.0 2019/1/30
 * @since V100R001
 */
public class JudgeLimitServiceImpl implements QualityDivationService {

    @Override
    public void deviationValue(DtoQualityControlLimit controlLimit, List<String> valueList, Map<String, Object> map) {
        String retStr = "";
        String samValue = valueList.get(0);
        CalculationService calculationService = SpringContextAware.getBean(CalculationService.class);
        if (DivationUtils.isNumber(samValue)) {
            //允许质控限值
            String allowLimit = controlLimit.getAllowLimitData();//controlLimit.getAllowLimit();
            if (DivationUtils.calculationResult(allowLimit, new BigDecimal(samValue), calculationService)) {
                retStr = "合格";
            } else {
                retStr = "不合格";
            }
        }
        map.put("qcRate", retStr);
    }
}
