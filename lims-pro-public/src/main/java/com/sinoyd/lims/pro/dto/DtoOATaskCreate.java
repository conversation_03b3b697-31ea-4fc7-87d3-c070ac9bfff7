package com.sinoyd.lims.pro.dto;

import java.io.Serializable;

import lombok.Data;

/**
 * 发起任务传输实体
 * <AUTHOR>
 * @version V1.0.0 2019-03-26
 * @since V100R001
 */
@Data
public class DtoOATaskCreate<T> implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 标题 */
    private String title;

    /** 描述 */
    private String description;

    /** 流程类型标识 */
    private String procTypeId;

    /** 流程类型名称 */
    private String procTypeName;
    
    /** 下一步办理人账号标识 */
    private String nextAssignee;

    /** 下一步办理人Guid标识 */
	private String nextAssigneeId;

    /** 下一步办理人名称 */
	private String nextAssigneeName;

    /** 关联的任务标识 */
    private String oaTaskId;

    /** 数据参数 */
    private T data;    
}