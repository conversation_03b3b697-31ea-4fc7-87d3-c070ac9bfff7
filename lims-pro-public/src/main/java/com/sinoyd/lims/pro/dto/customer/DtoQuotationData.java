package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单详情
 * <AUTHOR>
 * @version V1.0.0 2020/04/10
 * @since V100R001
 */
@Data
public class DtoQuotationData {

    /**
     * 点位名称
     */
    private String[] folderList = new String[]{};

    /**
     * 任务数
     */
    private Integer projectCount;

    /**
     * 周期
     */
    private Integer cycleOrder;

    /**
     * 次数
     */
    private Integer timesOrder;

    /**
     * 样品数量
     */
    private Integer sampleCount;

    /**
     * 采样费
     */
    private BigDecimal samplingPrice;

    /**
     * 分析费
     */
    private BigDecimal analysePrice;

    /**
     * 监测间隔
     */
    private String projectInterval;

    /**
     * 订单id
     */
    private String detailId;

    /**
     * 订单ids
     */
    private List<String> detailIds;

    /**
     * 测试项目ids
     */
    private List<String> testIds;

    /**
     *  污染源点位ids
     */
    private List<String> fixedPointIds;

}
