package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.base.service.WorkflowService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.AuthorizeService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.rcc.DtoCost;
import com.sinoyd.lims.lim.dto.rcc.DtoCostRule;
import com.sinoyd.lims.lim.dto.rcc.DtoCostRuleForEnt;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.rcc.*;
import com.sinoyd.lims.lim.service.*;
import com.sinoyd.lims.pro.core.ProCodeHelper;
import com.sinoyd.lims.pro.criteria.CostInfoCriteria;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoCostInfo;
import com.sinoyd.lims.pro.dto.DtoCostInfoDetail;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.enums.EnumPRO.*;
import com.sinoyd.lims.pro.repository.CostInfoDetailRepository;
import com.sinoyd.lims.pro.repository.CostInfoRepository;
import com.sinoyd.lims.pro.repository.ProjectRepository;
import com.sinoyd.lims.pro.service.*;
import com.sinoyd.lims.pro.util.MathUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 费用操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Service
public class CostInfoServiceImpl extends BaseJpaServiceImpl<DtoCostInfo,String,CostInfoRepository> implements CostInfoService {

    //#region 注入
    @Autowired
    private AuthorizeService authorizeService;

    @Autowired
    @Lazy
    private CostService costService;

    @Autowired
    @Lazy
    private CostRuleService costRuleService;

    @Autowired
    @Lazy
    private CostRuleForEntService costRuleForEntService;

    @Autowired
    private CostInfoDetailRepository costInfoDetailRepository;

    @Autowired
    @Lazy
    private CostInfoDetailService costInfoDetailService;

    @Autowired
    @Lazy
    private StatusForCostInfoService statusForCostInfoService;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    @Lazy
    private AnalyseDataService analyseDataService;

    @Autowired
    @Lazy
    private ProjectTypeService projectTypeService;

    @Autowired
    @Lazy
    private PersonService personService;

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    @Autowired
    @Lazy
    private WorkflowService workflowService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    @Lazy
    private HomeService homeService;

    @Autowired
    @Lazy
    private NewLogService newLogService;
    //#endregion

    @Override
    public void findByPage(PageBean<DtoCostInfo> pb, BaseCriteria costInfoCriteria) {
        CostInfoCriteria criteria = (CostInfoCriteria) costInfoCriteria;
        String userId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getUserId() : "";
        if (criteria.getModule().equals(EnumCostInfoModule.费用审批.getCode())) {
            if (!StringUtils.isNotNullAndEmpty(userId) || !authorizeService.haveActionPermission(userId, ProCodeHelper.COST_APPROVE_AUTH)) {
                return;
            }
        }
        // 多表关联查询返回自定义字段
        pb.setEntityName("DtoCostInfo c, DtoProject p, DtoStatusForCostInfo s");
        pb.setSelect("select c, p.projectCode, p.projectName, p.projectTypeId, p.grade, p.customerId, p.customerName, p.inceptPersonId, p.inceptTime,s.lastNewOpinion");

        super.findByPage(pb, costInfoCriteria);

        List<DtoCostInfo> datas = pb.getData();
        List<DtoCostInfo> newDatas = new ArrayList<>();

        Iterator<DtoCostInfo> costInfoIte = datas.iterator();
        // 循环迭代获取JPQL中查询返回的属性

        while (costInfoIte.hasNext()) {
            Object obj = costInfoIte.next();
            Object[] objs = (Object[]) obj;
            // 按查询顺序下标一一获取
            DtoCostInfo costInfo = (DtoCostInfo) objs[0];
            costInfo.setProjectCode((String) objs[1]);
            costInfo.setProjectName((String) objs[2]);
            costInfo.setProjectTypeId((String) objs[3]);
            costInfo.setGrade((Integer) objs[4]);
            costInfo.setCustomerId((String) objs[5]);
            costInfo.setCustomerName((String) objs[6]);
            costInfo.setInceptPersonId((String) objs[7]);
            costInfo.setInceptTime((Date) objs[8]);
            costInfo.setComment((String) objs[9]);
            newDatas.add(costInfo);
        }
        List<String> projectTypeIds = newDatas.stream().map(DtoCostInfo::getProjectTypeId).distinct().collect(Collectors.toList());
        List<DtoProjectType> projectTypes = projectTypeService.findRedisByIds(projectTypeIds);
        //将list转为map
        Map<String, String> projectTypeMap = projectTypes.stream().collect(Collectors.toMap(DtoProjectType::getId, DtoProjectType::getName));

        Map<String, String> inceptPersonMap = new HashMap<>();
        List<String> inceptPersonIds = newDatas.stream().map(DtoCostInfo::getInceptPersonId).distinct().collect(Collectors.toList());
        if (inceptPersonIds.size() > 0) {
            List<DtoPerson> inceptPersons = personService.findAllDeleted(inceptPersonIds);
            //将list转为map
            inceptPersonMap = inceptPersons.stream().collect(Collectors.toMap(DtoPerson::getId, DtoPerson::getCName));
        }

        for (DtoCostInfo cost : newDatas) {
            cost.setProjectTypeName(projectTypeMap.getOrDefault(cost.getProjectTypeId(), ""));
            cost.setInceptPersonName(inceptPersonMap.getOrDefault(cost.getInceptPersonId(), ""));
        }
        pb.setData(newDatas);
    }

    @Override
    public DtoCostInfo findOne(String key) {
        DtoCostInfo costInfo = repository.findOne(key);
        List<DtoCostInfoDetail> details = costInfoDetailRepository.findByCostInfoIdOrderBySampleTypeIdAscRedAnalyzeItemNameAsc(key);
        List<String> sampleTypeIds = details.stream().map(DtoCostInfoDetail::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypes = sampleTypeService.findRedisByIds(sampleTypeIds);
        Map<String, String> sampleTypeMap = sampleTypes.stream().collect(Collectors.toMap(DtoSampleType::getId, DtoSampleType::getTypeName));
        for (DtoCostInfoDetail detail : details) {
            detail.setSampleTypeName(sampleTypeMap.getOrDefault(detail.getSampleTypeId(), ""));
        }
        costInfo.setCostInfoDetail(details);
        return costInfo;
    }

    @Transactional
    @Override
    public DtoCostInfo update(DtoCostInfo entity) {
        //保存子表信息
        costInfoDetailService.update(entity.getCostInfoDetail());
        //保存主表信息
        return super.update(entity);
    }

    /**
     * 生成项目的费用
     *
     * @param projectId 项目id
     */
    @Transactional
    @Override
    public void generate(String projectId) {
        Integer count = repository.countByProjectId(projectId);
        //不存在该项目的费用时才生成
        if (count == 0) {
            DtoCostInfo costInfo = new DtoCostInfo();
            costInfo.setStatus(EnumCostInfoStatus.新建.toString());
            costInfo.setProjectId(projectId);

            DtoProject project = projectRepository.findOne(projectId);
            BigDecimal testRate = BigDecimal.valueOf(0);

            List<DtoCostRule> costRules = costRuleService.findAll();
            if (StringUtil.isNotNull(costRules) && costRules.size() > 0) {
                DtoCostRule costRule = costRules.get(0);
                costInfo.setCarUnit(costRule.getCarCost());
                costInfo.setLaborUnit(costRule.getLaborCost());
                costInfo.setOfferRate(costRule.getOfferRate());
                costInfo.setReportRate(costRule.getReportRate());
                testRate = costRule.getTestRate();
            }
            String customerId = StringUtils.isNotNullAndEmpty(project.getCustomerId()) ? project.getCustomerId() : "";

            if (StringUtils.isNotNullAndEmpty(customerId)) {
                DtoCostRuleForEnt entRule = costRuleForEntService.getByEntId(customerId);
                if (StringUtil.isNotNull(entRule)) {
                    //costInfo.setLaborUnit(entRule.getLaborCost()); 系统不维护企业人工费
                    costInfo.setOfferRate(entRule.getOfferRate());
                    testRate = entRule.getTestRate();
                }
            }

            //根据项目id获取方案并转换成费用明细的预处理数据
            List<DtoCostInfoDetail> details = this.getPreCostInfoDetailByProjectId(projectId);

            //对费用明细的预处理数据覆盖详细信息
            this.setCostInfoDetail(details, costInfo.getId(), testRate);

            //根据覆盖的详细明细和费用实体进行金额计算
            this.calculate(costInfo, details);

            this.save(costInfo);
            workflowService.createInstance(EnumWorkflowCode.费用.getValue(),costInfo.getId());
            costInfoDetailService.save(details);
            //添加状态记录
            statusForCostInfoService.createStatus(costInfo.getId());
        }
    }

    /**
     * 刷新费用信息并返回
     *
     * @param id 费用id
     */
    @Transactional
    @Override
    public DtoCostInfo reload(String id) {
        DtoCostInfo costInfo = this.findOne(id);
        if (costInfo.getSchemeChangeStatus().equals(EnumSchemeChangeStatus.未变更.getValue())) {
            throw new BaseException("方案未发生变动，不需要刷新费用");
        }
        EnumCostInfoStatus status = Enum.valueOf(EnumCostInfoStatus.class, costInfo.getStatus());
        if (status.getValue() >= EnumCostInfoStatus.审核中.getValue()) {
            throw new BaseException("费用已提交，无法进行刷新");
        }
        costInfo.setSchemeChangeStatus(EnumSchemeChangeStatus.未变更.getValue());

        //根据项目id获取方案并转换成费用明细的预处理数据
        List<DtoCostInfoDetail> details = this.getPreCostInfoDetailByProjectId(costInfo.getProjectId());
        //现有的老的费用明细数据
        List<DtoCostInfoDetail> oldDetails = costInfo.getCostInfoDetail();

        //获取检测折扣率
        BigDecimal testRate = BigDecimal.valueOf(0);
        if (StringUtil.isNotNull(oldDetails) && oldDetails.size() > 0) {
            testRate = oldDetails.get(0).getTestRate();
            //将老的数据和新的数据并在一起
            details.addAll(costInfo.getCostInfoDetail());
        } else {
            DtoProject proj = projectRepository.findOne(costInfo.getProjectId());
            String customerId = StringUtils.isNotNullAndEmpty(proj.getCustomerId()) ? proj.getCustomerId() : "";
            Boolean flag = true;
            if (StringUtils.isNotNullAndEmpty(customerId)) {
                DtoCostRuleForEnt entRule = costRuleForEntService.getByEntId(customerId);
                if (StringUtil.isNotNull(entRule)) {
                    costInfo.setLaborUnit(entRule.getLaborCost());
                    costInfo.setOfferRate(entRule.getOfferRate());
                    testRate = entRule.getTestRate();
                    flag = false;
                }
            }

            if (flag) {
                List<DtoCostRule> costRules = costRuleService.findAll();
                if (StringUtil.isNotNull(costRules) && costRules.size() > 0) {
                    DtoCostRule costRule = costRules.get(0);
                    testRate = costRule.getTestRate();
                }
            }
        }

        //对预处理数据排序
        details.sort(Comparator.comparing(DtoCostInfoDetail::getCostInfoId));

        List<DtoCostInfoDetail> forDel = new ArrayList<>();
        List<DtoCostInfoDetail> forUpt = new ArrayList<>();
        List<DtoCostInfoDetail> forAdd = new ArrayList<>();
        List<DtoCostInfoDetail> newDetails = new ArrayList<>();
        //存储新增或修改的费用id
        Map<String, Boolean> funcMap = new HashMap<>();
        details.stream().collect(Collectors.groupingBy(p -> p.getTestId() + p.getSampleTypeId(), Collectors.toList())).forEach((key, list) -> {
            //根据检测id分组遍历，同一个分组下只可能为1条数据或2条数据
            if (list.size() == 1) {
                DtoCostInfoDetail detail = list.get(0);
                if (StringUtils.isNotNullAndEmpty(detail.getCostInfoId())) {//表明是原来的费用，此情形不存在新的，说明方案里已经删了该指标
                    //需移除
                    forDel.add(detail);
                } else {//表明是新的费用，故需要添加该指标的费用
                    //需新增
                    forAdd.add(detail);
                    funcMap.put(detail.getId(), true);
                }
            } else {
                //上面经过排序后第一个为新的，第二个为老的
                DtoCostInfoDetail newDetail = list.get(0);
                DtoCostInfoDetail oldDetail = list.get(1);
                if (!newDetail.getSampleNum().equals(oldDetail.getSampleNum())) {//样品个数不一致需进行修改
                    oldDetail.setSampleNum(newDetail.getSampleNum());
                    forUpt.add(oldDetail);
                    funcMap.put(oldDetail.getId(), false);
                } else {
                    newDetails.add(oldDetail);
                }
            }
        });

        if (forUpt.size() > 0) {
            newDetails.addAll(forUpt);
        }
        if (forAdd.size() > 0) {
            this.setCostInfoDetail(forAdd, id, testRate);
            newDetails.addAll(forAdd);
        }

        //根据详细明细和费用实体进行金额计算
        this.calculate(costInfo, newDetails);

        Comparator<DtoCostInfoDetail> comparator = (a, b) -> {
            if (a.getSampleTypeId().equals(b.getSampleTypeId())) {
                return a.getRedAnalyzeItemName().compareTo(b.getRedAnalyzeItemName());
            }
            return a.getSampleTypeId().compareTo(b.getSampleTypeId());
        };
        newDetails.sort(comparator);
        costInfo.setCostInfoDetail(newDetails);
        this.save(costInfo);//保存费用实体
        //删除需要删除的费用明细
        if (forDel.size() > 0) {
            costInfoDetailService.delete(forDel);
        }
        for (DtoCostInfoDetail detail : newDetails) {
            if (funcMap.containsKey(detail.getId())) {
                if (funcMap.get(detail.getId())) {//表示新增
                    costInfoDetailService.save(detail);
                } else {//表示更新
                    costInfoDetailService.update(detail);
                }
            }
        }

        //移除redis对应项目的的方案变动key，即该费用不存在方案变动了
        String key = EnumPRORedis.getRedisKey(EnumPRORedis.PRO_OrgId_SchemeChangeProject.getValue()) + costInfo.getProjectId();
        redisTemplate.delete(key);

        return costInfo;
    }

    /**
     * 工作流操作
     *
     * @param dtoWorkflowSign 实体
     */
    @Transactional
    @Override
    public void costInfoSignal(DtoWorkflowSign dtoWorkflowSign) {
        try {
            if (StringUtils.isNotNullAndEmpty(dtoWorkflowSign.getNextOperatorId()) && !UUIDHelper.GUID_EMPTY.equals(dtoWorkflowSign.getNextOperatorId())) {
                DtoPerson per = personService.findOne(dtoWorkflowSign.getNextOperatorId());
                dtoWorkflowSign.setNextOperator(StringUtil.isNotNull(per) ? per.getCName() : "");
            } else {
                dtoWorkflowSign.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            }
            if (dtoWorkflowSign.getObjectIds().size() > 0) {
                dtoWorkflowSign.setIsAutoStatus(false);
                List<DtoCostInfo> infoList = this.findAll(dtoWorkflowSign.getObjectIds());
                Map<String, String> statusMap = infoList.stream().collect(Collectors.toMap(DtoCostInfo::getId, DtoCostInfo::getStatus));
                String from = infoList.get(0).getStatus();
                String to = workflowService.submitSign(dtoWorkflowSign);
                if (!StringUtils.isNotNullAndEmpty(to)) {
                    to = EnumCostInfoStatus.已完成.toString();
                }
                for (DtoCostInfo info : infoList) {
                    statusForCostInfoService.modifyStatus(statusMap.get(info.getId()), to, dtoWorkflowSign, info);
                }
                repository.updateCostStatus(dtoWorkflowSign.getObjectIds(), to, PrincipalContextUser.getPrincipal().getUserId(), new Date());

                //计算首页数据缓存
                String currentModule = EnumCostInfoStatus.getModuleCode(from);
                String nextModule = EnumCostInfoStatus.getModuleCode(to);
                homeService.clearProjectTask(PrincipalContextUser.getPrincipal().getUserId(),
                        PrincipalContextUser.getPrincipal().getOrgId()
                        , currentModule, nextModule);
                //保证事务提交之后才执行
                TransactionSynchronizationManager.registerSynchronization(
                        new TransactionSynchronizationAdapter() {
                            @Override
                            public void afterCommit() {
                                if (dtoWorkflowSign.getSignal().contains("auditNoPass")) {
                                    newLogService.createCostStatusBackLog(dtoWorkflowSign.getObjectIds(),
                                            dtoWorkflowSign.getOption(), dtoWorkflowSign.getNextOperatorId(), dtoWorkflowSign.getNextOperator());
                                } else {
                                    newLogService.createCostStatusUpdateLog(dtoWorkflowSign.getObjectIds(),
                                            dtoWorkflowSign.getOption(), dtoWorkflowSign.getNextOperatorId(), dtoWorkflowSign.getNextOperator());
                                }
                            }
                        }
                );

            }
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
            throw new BaseException("异常错误");
        }
    }

    //#region 私有方法

    /**
     * 获取项目的预费用明细
     *
     * @param projectId 项目id
     */
    private List<DtoCostInfoDetail> getPreCostInfoDetailByProjectId(String projectId) {
        List<DtoAnalyseData> analyseDatas = analyseDataService.findByProjectId(projectId);
        List<DtoCostInfoDetail> details = new ArrayList<>();

        analyseDatas.stream().collect(Collectors.groupingBy(DtoAnalyseData::getTestId, Collectors.toList())).forEach((testId, dataList) -> {
            dataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleTypeId, Collectors.toList())).forEach((sampleTypeId, list) -> {
                DtoAnalyseData ana = list.get(0);
                Integer sampleNum = list.size();

                DtoCostInfoDetail detail = new DtoCostInfoDetail();
                detail.setSampleNum(sampleNum);
                detail.setRedAnalyzeItemName(ana.getRedAnalyzeItemName());
                detail.setRedAnalyzeMethodName(ana.getRedAnalyzeMethodName());
                detail.setRedCountryStandard(ana.getRedCountryStandard());
                detail.setTestId(ana.getTestId());
                detail.setSampleTypeId(ana.getSampleTypeId());
                details.add(detail);
            });
        });

        return details;
    }

    /**
     * 设置费用明细信息
     *
     * @param details    费用明细列表
     * @param costInfoId 费用Id
     * @param testRate   折扣率
     */
    private void setCostInfoDetail(List<DtoCostInfoDetail> details, String costInfoId, BigDecimal testRate) {
        Map<String, List<String>> test2SampleType = details.stream().collect(Collectors.groupingBy(DtoCostInfoDetail::getTestId,
                Collectors.collectingAndThen(Collectors.toList(), value -> value.stream().map(DtoCostInfoDetail::getSampleTypeId).distinct().collect(Collectors.toList()))));
        //Map<String, List<String>> test2SampleType = details.stream().collect(Collectors.toMap(DtoCostInfoDetail::getTestId, DtoCostInfoDetail::getSampleTypeId));
        List<DtoCost> costs = costService.findByTestIdIn(test2SampleType.keySet());
        Map<String, DtoCost> costMap = new HashMap<>();
        costs.stream().collect(Collectors.groupingBy(DtoCost::getTestId, Collectors.toList())).forEach((testId, list) -> {
            if (test2SampleType.containsKey(testId)) {
                for (String sampleTypeId : test2SampleType.get(testId)) {
                    DtoCost cost = this.getCost(list, sampleTypeId);
                    if (StringUtil.isNotNull(cost)) {
                        costMap.put(testId + sampleTypeId, cost);
                    }
                }
            }
        });

        for (DtoCostInfoDetail detail : details) {
            detail.setCostInfoId(costInfoId);
            detail.setTestRate(testRate);
            if (costMap.containsKey(detail.getTestId() + detail.getSampleTypeId())) {
                DtoCost cost = costMap.get(detail.getTestId() + detail.getSampleTypeId());
                detail.setAnalyzeConfigCost(cost.getAnalyzeCost());
                detail.setAnalyzeCost(cost.getAnalyzeCost());
                detail.setSamplingConfigCost(cost.getSamplingCost());
                detail.setSamplingCost(cost.getSamplingCost());
            }
        }
    }

    /**
     * 根据检测小类获取对应测试项目下的费用配置
     *
     * @param costList     对应测试项目下的费用配置列表
     * @param sampleTypeId 检测小类id
     */
    private DtoCost getCost(List<DtoCost> costList, String sampleTypeId) {
        Optional<DtoCost> cost = costList.stream().filter(p -> p.getSampleTypeId().equals(sampleTypeId)).findFirst();
        if (cost.isPresent()) {
            //都是-1表示没有配置
            if (cost.get().getAnalyzeCost().compareTo(BigDecimal.valueOf(-1)) != 0 && cost.get().getSamplingCost().compareTo(BigDecimal.valueOf(-1)) != 0) {
                return cost.get();
            }
        }
        if (!UUIDHelper.GUID_EMPTY.equals(sampleTypeId)) {
            DtoSampleType samType = sampleTypeService.findOne(sampleTypeId);
            if (StringUtil.isNotNull(samType)) {
                Optional<DtoCost> bigCost = costList.stream().filter(p -> p.getSampleTypeId().equals(samType.getParentId())).findFirst();
                return bigCost.orElse(null);
            }
        }
        return null;
    }

    /**
     * 计算费用
     *
     * @param costInfo 费用
     * @param details  明细
     */
    private void calculate(DtoCostInfo costInfo, List<DtoCostInfoDetail> details) {
        this.calculateDetail(details);
        BigDecimal analyzeCost = BigDecimal.valueOf(0);
        BigDecimal samplingCost = BigDecimal.valueOf(0);

        if (details.size() > 0) {
            analyzeCost = details.stream().map(DtoCostInfoDetail::getAnalyzeTotalCost).reduce(BigDecimal.ZERO, BigDecimal::add);
            samplingCost = details.stream().map(DtoCostInfoDetail::getSamplingTotalCost).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        costInfo.setAnalyzeCost(analyzeCost);
        costInfo.setSamplingCost(samplingCost);

        //人工费=人 * 天 *人工费单价（元/人天）
        BigDecimal laborCost = MathUtil.multiplyBigDecimal(costInfo.getLaborUnit(), costInfo.getLaborDay(), costInfo.getLaborNum());
        costInfo.setLaborCost(laborCost);
        //车辆费=辆 * 天 * 车辆费单价（元/辆天）
        BigDecimal carCost = MathUtil.multiplyBigDecimal(costInfo.getCarUnit(), costInfo.getCarDay(), costInfo.getCarNum());
        costInfo.setCarCost(carCost);
        //报告费=（采样总费+分析综费）*报告折扣率（%）
        BigDecimal reportCost = MathUtil.multiplyBigDecimal(MathUtil.sumBigDecimal(samplingCost, analyzeCost), costInfo.getReportRate(), 0.01);
        costInfo.setReportCost(reportCost);

        //总价=采样总费+分析总费+人工费+车辆费+报告费+登高费+专家费+其他费用
        BigDecimal expectedTotalCost = MathUtil.sumBigDecimal(analyzeCost, samplingCost, laborCost, carCost,
                reportCost, costInfo.getClimbCost(), costInfo.getExpertCost(), costInfo.getOtherCost());
        costInfo.setExpectedTotalCost(expectedTotalCost);

        //报价=总价*折扣率+税收管理费
        BigDecimal acturalTotalCost = MathUtil.sumBigDecimal(MathUtil.multiplyBigDecimal(expectedTotalCost, costInfo.getOfferRate(), 0.01), costInfo.getTaxManageCost());
        costInfo.setActuralTotalCost(acturalTotalCost);
    }

    /**
     * 计算费用明细
     *
     * @param details 明细
     */
    private void calculateDetail(List<DtoCostInfoDetail> details) {
        //合计（即单个测试项目的检测费用）=（（采样单价*样品数）+（分析单价*样品数））*折扣率
        for (DtoCostInfoDetail detail : details) {
            BigDecimal analyzeTotal = BigDecimal.valueOf(0);
            BigDecimal samplingTotal = BigDecimal.valueOf(0);
            if (detail.getSampleNum() > 0) {
                analyzeTotal = MathUtil.multiplyBigDecimal(detail.getSampleNum(), detail.getAnalyzeCost(), detail.getTestRate(), 0.01);
                samplingTotal = MathUtil.multiplyBigDecimal(detail.getSampleNum(), detail.getSamplingCost(), detail.getTestRate(), 0.01);
            }

            detail.setAnalyzeTotalCost(analyzeTotal);
            detail.setSamplingTotalCost(samplingTotal);
            detail.setTotalCost(MathUtil.sumBigDecimal(analyzeTotal, samplingTotal));
        }
    }

    //#endregion
}