package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;


/**
 * QCData实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="QCData")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class QCData implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  QCData() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
       this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 用户id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("用户id")
	private String userId;
    
    /**
    * 测试id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("测试id")
	private String testId;

   /**
   * 公式参数名称
   */
   @ApiModelProperty("公式参数名称")
   private String paramsName;

    /**
    * 最新配置时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @ApiModelProperty("最新配置时间")
	private Date lastTime;


    /**
    * 方差值
    */
    @ApiModelProperty("方差值")
    private String stdValue;

    /**
    * 平均值
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("平均值")
	private double avgValue;
    
    /**
    * 上辅助线
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("上辅助线")
	private double upAssist;
    
    /**
    * 下辅助线
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("下辅助线")
	private double downAssist;
    
    /**
    * 上警告线
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("上警告线")
	private double upWarning;
    
    /**
    * 下警告线
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("下警告线")
	private double downWarning;
    
    /**
    * 上控制线
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("上控制线")
	private double upControl;
    
    /**
    * 下控制线
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("下控制线")
	private double downControl;
    
    /**
    * 数据类型(枚举EnumDataType：1.空白 2.加标)
    */
    @Column(nullable=false)
    @ApiModelProperty("数据类型(枚举EnumDataType：1.空白 2.加标)")
	private Integer dataType;
    
    /**
    * 数据字符串
    */
    @ApiModelProperty("数据字符串")
	private String dataStr;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }