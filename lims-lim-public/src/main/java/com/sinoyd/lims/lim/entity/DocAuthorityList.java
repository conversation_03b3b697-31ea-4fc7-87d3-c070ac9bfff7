package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;

/**
 * DocAuthorityList实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/12/19
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "DocAuthorityList")
@Data
public class DocAuthorityList implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public DocAuthorityList() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 文件夹，文件Id（Guid）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("''")
    @ApiModelProperty("文件夹，文件Id（Guid）")
    private String objectId;

    /**
     * 权限编码（常量Guid，常量名称 LIM_AuthType）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("''")
    @ApiModelProperty("权限编码（常量Guid，常量名称 LIM_AuthType）")
    @Length(message = "权限编码{validation.message.length}", max = 50)
    private String authCode;

    /**
     * 权限名称
     */
    @Column(length = 250)
    @ApiModelProperty("权限名称")
    @Length(message = "权限名称{validation.message.length}", max = 250)
    private String authName;


    /**
     * 是否默认开启 1：是  0：否
     */
    @Column(nullable = false)
    @ApiModelProperty("是否默认开启 1：是  0：否")
    private Boolean defaultOpenInd = false;


    /**
     * 排序号
     */
    @Column(length = 11, nullable = false)
    @ApiModelProperty("排序号")
    private Integer sortNum;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

}
