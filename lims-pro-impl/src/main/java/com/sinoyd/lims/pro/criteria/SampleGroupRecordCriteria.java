package com.sinoyd.lims.pro.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 送样单样品分组信息查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2023年6月14日
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SampleGroupRecordCriteria extends BaseCriteria implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 排序类型（1：分组名称排序  2: 样品编号排序）
     */
    private String sortType;

    /**
     * 样品编号/点位名称
     */
    private String sampleCodeFolderName;

    /**
     * 样品编号
     */
    private String sampleCode;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 分析项目
     */
    private String analyzeItemName;

    /**
     * 样品分组id
     */
    private List<String> groupIds;

    /**
     * 1:按分组  2:全因子  3:单因子
     */
    private Integer isGroup;

    /**
     * 样品id
     */
    private List<String> sampleIds;

    /**
     * 样品编码/分析项目
     */
    private String mobileKey;

    @Override
    public String getCondition() {
        values.clear();
        return "";
    }
}
