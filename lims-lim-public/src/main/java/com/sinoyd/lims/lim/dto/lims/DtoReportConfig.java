package com.sinoyd.lims.lim.dto.lims;

import javax.persistence.*;

import com.sinoyd.lims.lim.entity.ReportConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import java.util.List;


/**
 * DtoReportConfig实体
 *
 * <AUTHOR>
 * @version V1.0.0 2020/10/13
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_ReportConfig")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoReportConfig extends ReportConfig {
    private static final long serialVersionUID = 1L;

    /**
     * 返回报表名称
     */
    @Transient
    private String fileName;

    /**
     * 返回报表名称
     */
    @Transient
    private List<String> reportNameList;

    /**
     * 模版文档id(对应document表id)
     */
    @Transient
    private String templateDocId;

    /**
     * 报表显示名称
     */
    @Transient
    private String name;

}