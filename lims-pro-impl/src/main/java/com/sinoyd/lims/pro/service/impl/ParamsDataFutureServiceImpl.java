package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.dto.DtoParamsData;
import com.sinoyd.lims.pro.repository.ParamsDataRepository;
import com.sinoyd.lims.pro.service.ParamsDataFutureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncExecutionAspectSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V100R001
 * @date V1.0.0 2024/05/21
 */
@Service
@Slf4j
public class ParamsDataFutureServiceImpl implements ParamsDataFutureService {

    private ParamsDataRepository paramsDataRepository;

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    /**
     * 多线程获取参数
     *
     * @param objectIds 数据id
     * @return 日志集合
     */
    @Override
    @Async(AsyncExecutionAspectSupport.DEFAULT_TASK_EXECUTOR_BEAN_NAME)
    public Future<List<DtoParamsData>> getListByObjectIdIn(List<String> objectIds) {
//        List<DtoParamsData> paramsDataList = paramsDataRepository.findByObjectIdIn(objectIds);
        StringBuilder sql = new StringBuilder("select id,objectId,objectType,paramsConfigId,paramsName,paramsValue,dimension,dimensionId,")
                .append(" orderNum,orgId,groupId,isDeleted,creator,createDate,domainId,modifier,modifyDate")
                .append(" from TB_PRO_ParamsData where isDeleted = 0 and  objectId in (:objectIds)");
        Map<String, Object> params = new HashMap<>();
        params.put("objectIds", objectIds);
        List<DtoParamsData> paramsDataList = namedParameterJdbcTemplate.query(sql.toString(), params, (rs, rowNum) -> {
            DtoParamsData dtoParamsData = new DtoParamsData();
            dtoParamsData.setId(rs.getString("id"));
            dtoParamsData.setObjectId(rs.getString("objectId"));
            dtoParamsData.setObjectType(rs.getInt("objectType"));
            dtoParamsData.setParamsConfigId(rs.getString("paramsConfigId"));
            dtoParamsData.setParamsName(rs.getString("paramsName"));
            dtoParamsData.setParamsValue(rs.getString("paramsValue"));
            dtoParamsData.setDimension(rs.getString("dimension"));
            dtoParamsData.setDimensionId(rs.getString("dimensionId"));
            dtoParamsData.setOrderNum(rs.getInt("orderNum"));
            dtoParamsData.setOrgId(rs.getString("orgId"));
            dtoParamsData.setGroupId(rs.getString("groupId"));
            dtoParamsData.setIsDeleted(rs.getBoolean("isDeleted"));
            dtoParamsData.setCreator(rs.getString("creator"));
            dtoParamsData.setCreateDate(rs.getDate("createDate"));
            dtoParamsData.setDomainId(rs.getString("domainId"));
            dtoParamsData.setModifier(rs.getString("modifier"));
            dtoParamsData.setModifyDate(rs.getDate("modifyDate"));
            return dtoParamsData;
        });
        log.info("===============子线程名称 {}======================", Thread.currentThread().getName());
        return new AsyncResult<>(paramsDataList);
    }


    /**
     * 多线程获取删除数据
     *
     * @param mapList        异常数据Map集合
     * @param paramsDataList 异常数据所有数据
     * @return 日志集合
     */
    @Override
    @Async(AsyncExecutionAspectSupport.DEFAULT_TASK_EXECUTOR_BEAN_NAME)
    public Future<List<DtoParamsData>> getDelParamsDataList(List<Map<String, Object>> mapList, List<DtoParamsData> paramsDataList) {
        List<DtoParamsData> deleteList = new ArrayList<>();
        for (Map<String, Object> map : mapList) {
            String objectId = map.get("objectId").toString();
            Integer objectType = Integer.valueOf(map.get("objectType").toString());
            String paramsConfigId = map.get("paramsConfigId").toString();
            String groupId = map.get("groupId").toString();
            // 筛选数据
            List<DtoParamsData> dataList = paramsDataList.stream().filter(p -> p.getObjectId().equals(objectId) &&
                    p.getObjectType().equals(objectType) &&
                    p.getParamsConfigId().equals(paramsConfigId) &&
                    p.getGroupId().equals(groupId)).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(dataList)) {
//                log.info("============ paramsData size {} ============", dataList.size());
                // 根据参数值处理需要保留的参数数据
                List<DtoParamsData> nullParamsValues = dataList.stream().filter(p -> StringUtil.isEmpty(p.getParamsValue())).collect(Collectors.toList());
                // 当所有参数值都为空
                if (nullParamsValues.size() == dataList.size()) {
                    DtoParamsData paramsData = nullParamsValues.stream().max(Comparator.comparing(DtoParamsData::getModifyDate)).orElse(null);
                    nullParamsValues.remove(paramsData);
                    deleteList.addAll(nullParamsValues);
                } else {
                    DtoParamsData paramsData = dataList.stream().filter(p -> StringUtil.isNotEmpty(p.getParamsValue())).max(Comparator.comparing(DtoParamsData::getModifyDate)).orElse(null);
                    dataList.remove(paramsData);
                    deleteList.addAll(dataList);
                }
            }
        }
        log.info("===============子线程名称 {}======================", Thread.currentThread().getName());
        return new AsyncResult<>(deleteList);
    }

    @Override
    @Async(AsyncExecutionAspectSupport.DEFAULT_TASK_EXECUTOR_BEAN_NAME)
    public Future<Void> delete(List<DtoParamsData> paramsDataList) {
        paramsDataRepository.delete(paramsDataList);
        log.info("===============子线程名称 {}======================", Thread.currentThread().getName());
        return new AsyncResult<Void>(null);
    }

    @Autowired
    public void setParamsDataRepository(ParamsDataRepository paramsDataRepository) {
        this.paramsDataRepository = paramsDataRepository;
    }
}
