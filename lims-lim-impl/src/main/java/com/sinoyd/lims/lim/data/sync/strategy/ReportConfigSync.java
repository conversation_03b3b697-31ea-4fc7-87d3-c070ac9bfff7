package com.sinoyd.lims.lim.data.sync.strategy;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.lim.criteria.ReportConfigCriteria;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.lims.lim.data.sync.dto.DtoDataCompareResult;
import com.sinoyd.lims.lim.data.sync.strategy.base.AbsDataSync;
import com.sinoyd.lims.lim.dto.lims.DtoReportConfig;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.ReportConfigRepository;
import com.sinoyd.lims.lim.service.ReportConfigService;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 报表模板配置同步
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/5/23
 */
@Component
@DependsOn({"springContextAware"})
@Order(14)
@Slf4j
public class ReportConfigSync extends AbsDataSync<DtoReportConfig> {

    private ReportConfigService reportConfigService;

    private ReportConfigRepository reportConfigRepository;

    /**
     * 数据比较
     * @param reportConfigIds 需要同步的数据id
     * @return List<DtoDataCompareResult<DtoReportConfig>>
     */
    @Override
    public List<DtoDataCompareResult<DtoReportConfig>> compareData(List<String> reportConfigIds) {
        //获取项目上所有报表模板配置
        List<DtoReportConfig> projectDataList=reportConfigService.findAll();
        //公共库中的报表模板配置
        List<DtoReportConfig> standardDataList=queryStandardData();
        //比较数据
        if (StringUtil.isNotEmpty(reportConfigIds)) {
            standardDataList = standardDataList.parallelStream().filter(p -> reportConfigIds.contains(p.getId()))
                    .collect(Collectors.toList());
        }
        return compareData(standardDataList,projectDataList);
    }

    /**
     * 同步数据
     *
     * @param dataIds         需要同步的测试项目id集合
     * @param webSocketServer websockets服务端
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncData(List<String> dataIds, WebSocketServer webSocketServer) {
        List<DtoDataCompareResult<DtoReportConfig>> compareResult = compareData(dataIds);
        Optional<DtoDataCompareResult<DtoReportConfig>> resultOptional = compareResult.parallelStream().filter(p -> COMPARE_CATEGORY[0].equals(p.getCategory())
                && p.getCount() > 0).findFirst();
        resultOptional.ifPresent(r -> {
            //已同步记录数
            int i = 0;
            DtoReportConfig errorDto = null;
            try {
                for (DtoReportConfig dtoReportConfig : r.getAddDataList()) {
                    errorDto = dtoReportConfig;
                    //防止公共库未被假删，但项目库被假删，此种情况直接更新
                    if (reportConfigRepository.findOne(dtoReportConfig.getId()) != null) {
                        reportConfigService.update(dtoReportConfig);
                    } else {
                        reportConfigService.save(dtoReportConfig);
                    }
                    webSocketServer.sendMessage(String.format("同步" + getItemName() + "(%d/%d)", ++i, r.getCount()));
                }
            } catch (Exception e) {
                if (errorDto != null) {
                    webSocketServer.sendMessage("同步" + getItemName() + "发生错误, ID = " + errorDto.getId() + ", 错误信息: "
                            + e.getMessage());
                }
                log.error(e.getMessage(), e);
                throw new BaseException("同步" + getItemName() + "发生错误");
            }
        });
    }

    /**
     * 是否必须同步
     *
     * @return true: 必须同步， false：不用必须同步
     */
    @Override
    public boolean mustSync() {
        return true;
    }

    /**
     * 同步的数据项名称
     *
     * @return 数据项名称
     */
    @Override
    public String getItemName() {
        return EnumLIM.EnumDataSyncType.报表配置.name();
    }

    /**
     * 获取排序值
     *
     * @return 排序值
     */
    @Override
    public Integer getOrderNum() {
        return EnumLIM.EnumDataSyncType.报表配置.getValue();
    }

    /**
     * 获取标准库数据查询url
     *
     * @return url
     */
    @Override
    public String getStandardDataQueryUrl() {
        return "/api/sinoyd-lims/lim/reportConfig";
    }

    @Override
    public ResponseEntity<JSONObject> queryStandardData(BaseCriteria baseCriteria, int page, int rows, String sort) {
        ReportConfigCriteria criteria = (ReportConfigCriteria) baseCriteria;
        String url = standardDataConfig.getHost() + getStandardDataQueryUrl() +
                "?page=" + page + "&rows=" + rows + "&type=" + criteria.getType() +
                "&reportCode=" + criteria.getReportCode() + "&templateName=" + criteria.getTemplateName() + "&sort=" + sort;
        return queryStandardData(url);
    }

    @Override
    public List<Integer> getDependDataType() {
        return Arrays.asList(
                EnumLIM.EnumDataSyncType.报表配置.getValue(),
                EnumLIM.EnumDataSyncType.报表配置应用.getValue()
        );
    }

    @Override
    public Integer getSyncDataType() {
        return EnumLIM.EnumDataSyncType.报表配置.getValue();
    }

    @Autowired
    @Lazy
    public void setReportConfigService(ReportConfigService reportConfigService) {
        this.reportConfigService = reportConfigService;
    }

    @Autowired
    public void setReportConfigRepository(ReportConfigRepository reportConfigRepository) {
        this.reportConfigRepository = reportConfigRepository;
    }
}
