package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.ProjectApprovalCriteria;
import com.sinoyd.lims.pro.dto.DtoProjectApproval;
import com.sinoyd.lims.pro.service.ProjectApprovalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ProjectApproval服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/04
 * @since V100R001
 */
@Api(tags = "示例: ProjectApproval服务")
@RestController
@RequestMapping("api/pro/projectApproval")
public class ProjectApprovalController extends BaseJpaController<DtoProjectApproval, String, ProjectApprovalService> {

    @ApiOperation(value = "选择项目", notes = "选择项目")
    @PostMapping("/selectProject")
    public RestResponse<DtoProjectApproval> selectProject(@RequestBody DtoProjectApproval projectApproval) {
        RestResponse<DtoProjectApproval> response = new RestResponse<>();
        response.setData(service.selectProject(projectApproval.getProjectId()));
        return response;
    }

    @ApiOperation(value = "分页查询方案申请列表", notes = "分页查询方案申请列表")
    @GetMapping
    public RestResponse<List<DtoProjectApproval>> findByPage(ProjectApprovalCriteria criteria) {
        RestResponse<List<DtoProjectApproval>> response = new RestResponse<>();
        PageBean<DtoProjectApproval> pb = super.getPageBean();
        service.findByPage(pb, criteria);
        response.setRestStatus(StringUtil.isEmpty(pb.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        response.setData(pb.getData());
        response.setCount(pb.getRowsCount());
        return response;
    }

    @ApiOperation(value = "提交方案", notes = "提交方案")
    @PostMapping("/commit")
    public RestResponse<Void> commit(@RequestBody DtoProjectApproval projectApproval) {
        RestResponse<Void> response = new RestResponse<>();
        service.commit(projectApproval.getApproveIds(), projectApproval.getApprovePersonId(), projectApproval.getComment());
        return response;
    }

    @ApiOperation(value = "方案審核", notes = "方案審核")
    @PostMapping("/audit/{isPass}")
    public RestResponse<Void> audit(@RequestBody DtoProjectApproval projectApproval, @PathVariable(name = "isPass") Boolean isPass) {
        RestResponse<Void> response = new RestResponse<>();
        service.audit(isPass, projectApproval.getApproveIds(), projectApproval.getComment());
        return response;
    }

    @ApiOperation(value = "删除", notes = "删除")
    @DeleteMapping("")
    public RestResponse<Integer> delete(@RequestBody List<String> ids) {
        RestResponse<Integer> response = new RestResponse<>();
        response.setData(service.logicDeleteById(ids));
        return response;
    }

    @ApiOperation(value = "提交方案校验", notes = "提交方案校验")
    @PostMapping("/verifyCommit")
    public RestResponse<Boolean> verifyCommit(@RequestBody DtoProjectApproval projectApproval) {
        RestResponse<Boolean> response = new RestResponse<>();
        response.setData(service.verifyCommit(projectApproval.getApproveIds()));
        return response;
    }

}
