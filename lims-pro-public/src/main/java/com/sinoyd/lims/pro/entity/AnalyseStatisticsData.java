package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;

/**
 * 检测情况统计实体
 * <AUTHOR>
 * @version V1.0.0 2025/5/14
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "AnalyseStatisticsData")
@Data
@EntityListeners(AuditingEntityListener.class)
public class AnalyseStatisticsData {


    /**
     * 检测数据主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 测试项目id
     */
    @ApiModelProperty("测试项目id")
    private String testId;

    /**
     * 项目类型名称
     */
    @ApiModelProperty("项目类型名称")
    private String projectTypeName;

    /**
     * 项目类型id
     */
    @ApiModelProperty("项目类型id")
    private String projectTypeId;

    /**
     * 样品id
     */
    @ApiModelProperty("样品id")
    private String sampleId;

    /**
     * 样品编号
     */
    @ApiModelProperty("样品编号")
    private String sampleCode;

    /**
     * 样品类型名称
     */
    @ApiModelProperty("样品类型名称")
    private String sampleTypeName;

    /**
     * 样品类型id
     */
    @ApiModelProperty("样品类型id")
    private String sampleTypeId;

    /**
     * 工作单id
     */
    @ApiModelProperty("工作单id")
    private String workSheetFolderId;

    /**
     * 分析项目名称
     */
    @ApiModelProperty("分析项目名称")
    private String redAnalyzeItemName;

    /**
     * 送样单id
     */
    @ApiModelProperty("送样单id")
    private String receiveId;

    /**
     * 接样时间
     */
    @ApiModelProperty("接样时间")
    private Date receiveTime;


    /**
     * 数据检测状态
     */
    @ApiModelProperty("数据检测状态")
    private String analyseDataStatus;

    /**
     * 分析人名称
     */
    @ApiModelProperty("分析人名称")
    private String analystName;

    /**
     * 分析人id
     */
    @ApiModelProperty("分析人id")
    private String analystId;


    /**
     * 采样时间
     */
    @ApiModelProperty("采样时间")
    private Date samplingTimeBegin;

    /**
     * 分析时长
     */
    @ApiModelProperty("分析时长")
    private Integer analyseDayLen;

    /**
     * 分析时间
     */
    @ApiModelProperty("分析时间")
    private Date analyzeTime;

    /**
     * 分析数据状态
     */
    @ApiModelProperty("分析数据状态")
    private Integer dataStatus;

    /**
     * 分析数据状态字符串
     */
    @ApiModelProperty("分析数据状态")
    private String status;



    /**
     * 领样单状态
     */
    @ApiModelProperty("领样单状态")
    private String receiveStatus;

    /**
     * 交接时间
     */
    @ApiModelProperty("交接时间")
    private Date receiveSampleDate;


    /**
     * 质控等级
     */
    @ApiModelProperty("质控等级")
    private Integer qcGrade;

    /**
     * 质控类型
     */
    @ApiModelProperty("质控类型")
    private Integer qcType;

    /**
     * 测试项目假删字段
     */
    @ApiModelProperty("测试项目假删字段")
    private Boolean testIsDeleted;

    /**
     * 分析数据假删字段
     */
    @ApiModelProperty("分析数据假删字段")
    private Boolean isDeleted;


    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建时间
     */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 修改时间
     */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}
