package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 点位周期查询条件
 * <AUTHOR>
 * @version V1.0.0
 * @since   2024/11/18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FolderPeriodCriteria extends BaseCriteria implements Serializable {

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 检测类型标识
     */
    private String sampleTypeId;

    /**
     *  项目名称/项目编号
     */
    private String key;

    /**
     * 点位名称
     */
    private String watchSpot;

    /**
     * 送样单号
     */
    private String recordCode;


    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if(StringUtil.isNotEmpty(sampleTypeId)){
            condition.append(" and a.sampleTypeId = :sampleTypeId");
            values.put("sampleTypeId", sampleTypeId);
        }
        if(StringUtil.isNotEmpty(watchSpot)){
            condition.append(" and exists(select 1 from DtoSampleFolder f where a.sampleFolderId = f.id and f.watchSpot like :watchSpot)");
            values.put("watchSpot","%"+ watchSpot+"%");
        }
        if(StringUtil.isNotEmpty(key)){
            condition.append(" and exists(select 1 from DtoProject p where a.projectId = p.id and (p.projectName like :key or p.projectCode like :key))");
            values.put("key", "%"+key+"%");
        }
        return condition.toString();
    }
}
