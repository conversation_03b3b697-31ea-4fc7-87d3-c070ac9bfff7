package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.pro.dto.DtoAnalyseBiologyData;
import com.sinoyd.lims.pro.repository.AnalyseBiologyDataRepository;
import com.sinoyd.lims.pro.service.AnalyseBiologyDataService;
import org.springframework.stereotype.Service;


/**
 * AnalyseBiologyData操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Service
public class AnalyseBiologyDataServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoAnalyseBiologyData,String,AnalyseBiologyDataRepository> implements AnalyseBiologyDataService {

    @Override
    public void findByPage(PageBean<DtoAnalyseBiologyData> pb, BaseCriteria analyseBiologyDataCriteria) {
        pb.setEntityName("DtoAnalyseBiologyData a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, analyseBiologyDataCriteria);
    }
}