package com.sinoyd.lims.foreign;

import lombok.Data;

import java.util.Date;

/**
 * 曲线明细详情
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Data
public class DataCurveVO {
    /**
     * id
     */
    private String id;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 工作单id
     */
    private String workSheetFolderId;

    /**
     * 校准日期
     */
    private Date checkDate;

    /**
     * 相关系数
     */
    private String coefficient;

    /**
     * 配置日期
     */
    private Date configDate;

    /**
     * 曲线零点
     */
    private String zeroPoint;

    /**
     * 斜率a
     */
    private String kValue;

    /**
     * 截距b
     */
    private String bValue;

    /**
     * 实数c
     */
    private String cValue;

    /**
     * 是否双曲线
     */
    private Boolean isDouble;

    /**
     * 曲线类型
     */
    private String curveType;

    /**
     * 曲线模型
     */
    private String curveMode;

    /**
     * 曲线信息
     */
    private String curveInfo;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;
}
