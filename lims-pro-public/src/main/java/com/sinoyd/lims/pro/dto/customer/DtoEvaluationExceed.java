package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.io.Serializable;
import java.util.*;

/**
 * 报告评价超标情况
 * <AUTHOR>
 * @version V1.0.0 2019/11/12
 * @since V100R001
 */
@Data
public class DtoEvaluationExceed implements Serializable  {
    private static final long serialVersionUID = 1L;
    public DtoEvaluationExceed(String value){
        this.testValue = value;
    }
    
    /**
     * 点位实际值
     */
    private String testValue;

    /**
     * 是否超标
     */
    private Boolean isExceed = false;

    /**
     * 超标倍数
     */
    private String overProofTimes = "--";
}