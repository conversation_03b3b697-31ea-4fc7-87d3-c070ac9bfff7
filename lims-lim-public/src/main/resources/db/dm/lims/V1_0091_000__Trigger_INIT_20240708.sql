CREATE  TRIGGER "AFTER_UPDATE_RECORDCON<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>NFIG"
    AFTER  UPDATE
    ON "TB_LIM_RECORDCONFIG2PARAMSCONFIG"
    referencing OLD ROW AS "OLD" NEW ROW AS "NEW"

 for each row

BEGIN

    if new.recordConfigId != old.recordConfigId or (new.recordConfigId is null and old.recordConfigId is not null) or
       (new.recordConfigId is not null and old.recordConfigId is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_recordconfig2paramsconfig', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'recordConfigId',
        old.recordConfigId, new.recordConfigId, '00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000');
END IF;
if new.paramsConfigId != old.paramsConfigId or (new.paramsConfigId is null and old.paramsConfigId is not null) or
       (new.paramsConfigId is not null and old.paramsConfigId is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_recordconfig2paramsconfig', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'paramsConfigId',
        old.paramsConfigId, new.paramsConfigId, '00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000');
END IF;
end;
