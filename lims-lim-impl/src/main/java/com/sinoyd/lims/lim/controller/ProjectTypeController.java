package com.sinoyd.lims.lim.controller;

import com.sinoyd.base.dto.customer.TreeNode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.service.ProjectTypeService;
import com.sinoyd.lims.lim.criteria.ProjectTypeCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;
import java.util.Map;


/**
 * 项目类型服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/10/23
 * @since V100R001
 */
 @Api(tags = "项目类型: 项目类型服务")
 @RestController
 @RequestMapping("api/lim/projectType")
 @Validated
 public class ProjectTypeController extends BaseJpaController<DtoProjectType, String,ProjectTypeService> {

    /**
     * 分页动态条件查询项目类型
     *
     * @param projectTypeCriteria 条件参数
     * @return RestResponse<List < ProjectType>>
     */
    @ApiOperation(value = "分页动态条件查询项目类型", notes = "分页动态条件查询项目类型")
    @GetMapping
    public RestResponse<List<DtoProjectType>> findByPage(ProjectTypeCriteria projectTypeCriteria) {
        PageBean<DtoProjectType> pageBean = super.getPageBean();
        RestResponse<List<DtoProjectType>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, projectTypeCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 根据编码获取项目类型
     *
     * @return
     */
    @ApiOperation(value = "根据编码获取项目类型", notes = "根据编码获取项目类型")
    @GetMapping("/code")
    public RestResponse<List<Map<String, Object>>> findByCode(@RequestParam(name = "code") String code, @RequestParam(name = "values") String[] values) {
        RestResponse<List<Map<String, Object>>> restResponse = new RestResponse<List<Map<String, Object>>>();
        restResponse.setData(service.getProjectTypeByCode(code, values));

        return restResponse;
    }

    /**
     * 项目编号配置根据编码获取项目类型
     *
     * @return 项目类型信息
     */
    @ApiOperation(value = "项目编号配置根据编码获取项目类型", notes = "项目编号配置根据编码获取项目类型")
    @GetMapping("/serialIdentifierConfig/code")
    public RestResponse<List<Map<String, Object>>> findByCodeForSerialIdentifierConfig(@RequestParam(name = "code") String code,
                                                                                       @RequestParam(name = "values") String[] values,
                                                                                       @RequestParam(name = "projectTypeIdList") String[] projectTypeIds,
                                                                                       @RequestParam(name = "configType") Integer configType,
                                                                                       @RequestParam(name = "qcType") Integer qcType,
                                                                                       @RequestParam(name = "qcGrade") Integer qcGrade) {
        RestResponse<List<Map<String, Object>>> restResponse = new RestResponse<List<Map<String, Object>>>();
        restResponse.setData(service.getProjectTypeForSerialIdentifierConfig(code, values, projectTypeIds, configType, qcType, qcGrade));

        return restResponse;
    }

    /**
     * 项目编号配置根据编码获取项目类型
     *
     * @return 项目类型信息
     */
    @ApiOperation(value = "项目编号配置根据编码获取项目类型", notes = "项目编号配置根据编码获取项目类型")
    @GetMapping("/serialIdentifierConfig/code/tree")
    public RestResponse<List<Map<String, Object>>> getProjectTypeForSerialIdentifierConfigTree(@RequestParam(name = "code") String code,
                                                                                       @RequestParam(name = "values") String[] values,
                                                                                       @RequestParam(name = "projectTypeIdList") String[] projectTypeIds,
                                                                                       @RequestParam(name = "configType") Integer configType,
                                                                                       @RequestParam(name = "qcType") Integer qcType,
                                                                                       @RequestParam(name = "qcGrade") Integer qcGrade) {
        RestResponse<List<Map<String, Object>>> restResponse = new RestResponse<List<Map<String, Object>>>();
        restResponse.setData(service.getProjectTypeForSerialIdentifierConfigTree(code, values, projectTypeIds, configType, qcType, qcGrade));

        return restResponse;
    }

    /**
     * 返回项目类型具体编码的值
     *
     * @return
     */
    @ApiOperation(value = "返回项目类型具体编码的值", notes = "返回项目类型具体编码的值")
    @GetMapping("/config")
    public RestResponse<String> getConfigValue(@RequestParam(name = "projectTypeId") String projectTypeId, @RequestParam(name = "code") String code) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setData(service.getConfigValue(projectTypeId, code));

        return restResponse;
    }

    /**
     * 返回项目类型的树
     *
     * @return
     */
    @ApiOperation(value = "获取项目类型树结构", notes = "获取项目类型树结构")
    @GetMapping("/tree")
    public RestResponse<List<TreeNode>> tree() {
        RestResponse<List<TreeNode>> restResponse = new RestResponse<>();
        restResponse.setData(service.getProjectTree());

        return restResponse;
    }

    /**
     * 按主键查询项目类型
     *
     * @param id 主键id
     * @return RestResponse<DtoProjectType>
     */
    @ApiOperation(value = "按主键查询项目类型", notes = "按主键查询项目类型")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoProjectType> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoProjectType> restResponse = new RestResponse<>();
        DtoProjectType projectType = service.findOne(id);
        restResponse.setData(projectType);
        restResponse.setRestStatus(StringUtil.isNull(projectType) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增项目类型
     *
     * @param projectType 实体列表
     * @return RestResponse<DtoProjectType>
     */
    @ApiOperation(value = "新增ProjectType", notes = "新增ProjectType")
    @PostMapping
    public RestResponse<DtoProjectType> create(@Validated @RequestBody DtoProjectType projectType) {
        RestResponse<DtoProjectType> restResponse = new RestResponse<>();
        restResponse.setData(service.save(projectType));
        return restResponse;
    }

    /**
     * 修改项目类型
     *
     * @param projectType 实体列表
     * @return RestResponse<DtoProjectType>
     */
    @ApiOperation(value = "修改项目类型", notes = "修改项目类型")
    @PutMapping
    public RestResponse<DtoProjectType> update(@Validated @RequestBody DtoProjectType projectType) {
        RestResponse<DtoProjectType> restResponse = new RestResponse<>();
        restResponse.setData(service.update(projectType));
        return restResponse;
    }

    /**
     * 根据id删除项目类型
     *
     * @param id
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id删除项目类型", notes = "根据id删除项目类型")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);

        return restResp;
    }

    /**
     * 通过页面编码获取项目类型
     * @param typeCode 项目类型编码
     * @return 项目类型
     */
    @ApiOperation(value = "通过页面编码获取项目类型", notes = "通过页面编码获取项目类型")
    @GetMapping("/typeCode")
    public RestResponse<List<DtoProjectType>> findByTypeCode(@RequestParam(name = "typeCode") String typeCode) {
        RestResponse<List<DtoProjectType>> restResponse = new RestResponse<>();
        List<DtoProjectType> projectTypeList = service.findByTypeCode(typeCode);
        restResponse.setRestStatus(StringUtil.isEmpty(projectTypeList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(projectTypeList);
        return restResponse;
    }

    /**
     * 根据项目类型id获取项目类型列表
     * @param typeId 项目类型编码
     * @return 项目类型
     */
    @ApiOperation(value = "根据项目类型id获取项目类型列表", notes = "根据项目类型id获取项目类型列表")
    @GetMapping("/typeList")
    public RestResponse<List<DtoProjectType>> findByTypeId(@RequestParam(name = "typeId") String typeId) {
        RestResponse<List<DtoProjectType>> restResponse = new RestResponse<>();
        List<DtoProjectType> projectTypeList = service.findByTypeId(typeId);
        restResponse.setRestStatus(StringUtil.isEmpty(projectTypeList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(projectTypeList);
        return restResponse;
    }

    /**
     * 返回项目类型的树
     *
     * @return
     */
    @ApiOperation(value = "根据编码获取项目类型树结构", notes = "获取项目类型树结构")
    @GetMapping("/tree/code")
    public RestResponse<List<TreeNode>> treeByCode(@RequestParam(name = "code") String code, @RequestParam(name = "values") String[] values) {
        RestResponse<List<TreeNode>> restResponse = new RestResponse<>();
        restResponse.setData(service.getProjectTreeByCode(code,values));

        return restResponse;
    }
}