package com.sinoyd.lims.pro.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 及时率统计sql查询vo
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/1/31
 */
@Data
@Accessors(chain = true)
public class AnalyzeTimelinessMapVO {

    /**
     * 分析人员id
     */
    private String analystId;

    /**
     * 分析人员名称
     */
    private String analystName;

    /**
     * 采样时间
     */
    private Date samplingTime;

    /**
     * 分析时长
     */
    private Integer analyseDayLen;

    /**
     * 分析时间
     */
    private Date analyzeTime;

    /**
     * 数据状态
     */
    private Integer dataStatus;

}

