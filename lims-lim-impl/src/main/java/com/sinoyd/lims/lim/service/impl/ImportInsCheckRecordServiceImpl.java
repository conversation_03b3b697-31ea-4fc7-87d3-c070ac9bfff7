package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.service.InstrumentService;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentCheckRecord;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.customer.DtoImportInstrumentCheckRecord;
import com.sinoyd.lims.lim.repository.lims.InstrumentCheckRecordRepository;
import com.sinoyd.lims.lim.service.ImportInsCheckRecordService;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import com.sinoyd.lims.lim.verify.InstrumentCheckRecordVerifyHandler;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 仪器检定校准导入实现类
 *
 * <AUTHOR>
 * @version V1.0.0 2022/9/13
 * @since V100R001
 */
@Service
public class ImportInsCheckRecordServiceImpl implements ImportInsCheckRecordService {

    private InstrumentCheckRecordRepository instrumentCheckRecordRepository;

    private InstrumentService instrumentService;

    private PersonService personService;

    private InstrumentCheckRecordVerifyHandler instrumentCheckRecordVerifyHandler;

    private ImportUtils importUtils;

    /**
     * 仪器检定校准导入
     *
     * @param file      传入的文件
     * @param objectMap 业务数据Map
     * @return List<DtoPerson>
     * @throws Exception 异常抛出
     */
    @Override
    @Transactional
    public List<DtoInstrumentCheckRecord> importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception {
        PoiExcelUtils.verifyFileType(file);
        //region 数据参数
        //获取仪器id
        String instrumentId = (String) objectMap.get(0);

        //获取所有的仪器信息
        List<DtoInstrument> instrumentList = instrumentService.findAll();
        //获取所有的人员信息
        List<DtoPerson> personList = personService.findAll();
        instrumentCheckRecordVerifyHandler.getDbInstrumentTl().set(instrumentList);
        instrumentCheckRecordVerifyHandler.getDbPersonTl().set(personList);
        instrumentCheckRecordVerifyHandler.getInstrumentIdTl().set(instrumentId);


        ExcelImportResult<DtoImportInstrumentCheckRecord> importResult = getExcelData(file, response);

        //获取校验成功导入数据
        List<DtoImportInstrumentCheckRecord> importList = importResult.getList();
        instrumentCheckRecordVerifyHandler.getDbInstrumentTl().remove();
        instrumentCheckRecordVerifyHandler.getDbPersonTl().remove();
        instrumentCheckRecordVerifyHandler.getDbInstrumentTl().remove();


        //移除空行
        importList.removeIf(p -> StringUtil.isNull(p.getOriginType()));

        if (StringUtil.isEmpty(importList)) {
            throw new BaseException("文件中无数据，请检查后导入");
        }
        //endregion

        //region 添加数据
        List<DtoInstrumentCheckRecord> checkRecordList = importToEntity(instrumentList, importList, personList, instrumentId);
        // 更新仪器信息
        updateInstrument(instrumentList, checkRecordList);
        addData(checkRecordList);
        //endregion

        return instrumentCheckRecordRepository.findAll();
    }

    /**
     * 添加数据库数据
     *
     * @param data 需要导入的数据
     */
    @Override
    public void addData(List<DtoInstrumentCheckRecord> data) {
        if (StringUtil.isNotEmpty(data)) {
            instrumentCheckRecordRepository.save(data);
        }
    }

    @Transactional
    protected void updateInstrument(List<DtoInstrument> instrumentList, List<DtoInstrumentCheckRecord> instrumentCheckRecordList) {
        List<DtoInstrument> updateInstrumentList = new ArrayList<>();
        instrumentCheckRecordList.forEach(entity -> {
            Optional<DtoInstrument> instrumentOptional = instrumentList.stream().filter(p -> entity.getInstrumentId().contains(p.getId())).findFirst();
            instrumentOptional.ifPresent(instrument -> {
                if (StringUtil.isNotNull(entity.getCheckTime()) && entity.getCheckEndDate().compareTo(instrument.getOriginEndDate()) > 0) {
                    instrument.setOriginEndDate(entity.getCheckEndDate());//有效期
                    if (entity.getCheckEndDate().compareTo(new Date()) >= 0) {
                        instrument.setState(EnumBase.EnumInstrumentStatus.正常.getValue());
                    }
                }
                instrument.setOriginType(entity.getOriginType()); // 溯源方式
                instrument.setOriginUnit(entity.getCheckDeptName()); // 溯源单位
                instrument.setOriginCyc(entity.getOriginCyc()); // 溯源周期
                instrument.setOriginResult(entity.getCheckResult()); // 溯源结果
                instrument.setOriginDate(entity.getCheckTime());//溯源日期
                instrument.setOriginRemark(entity.getRemark());//备注
                updateInstrumentList.add(instrument);
            });
        });
        instrumentService.updateBatch(updateInstrumentList);
    }


    /**
     * 获取导入数据
     *
     * @param file 传入的文件
     * @return ExcelImportResult
     * @throws Exception 异常信息
     */
    @Override
    public ExcelImportResult<DtoImportInstrumentCheckRecord> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        //设置表头区域
        params.setTitleRows(0);
        //设置表头开始行
        params.setHeadRows(1);
        //设置开始工作簿
        params.setStartSheetIndex(0);
        //设置是否校验
        params.setNeedVerify(true);
        //设置校验handle
        params.setVerifyHandler(instrumentCheckRecordVerifyHandler);
        ExcelImportResult<DtoImportInstrumentCheckRecord> result = ExcelImportUtil.importExcelMore(file.getInputStream(), DtoImportInstrumentCheckRecord.class, params);
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "仪器检定导入错误信息");
            PoiExcelUtils.downLoadExcel("仪器检定导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }


    /**
     * 导入实体转换为仪器校准信息实体
     *
     * @param importCheckRecords 导入数据
     * @return 仪器校准信息实体
     */
    private List<DtoInstrumentCheckRecord> importToEntity(List<DtoInstrument> instrumentList, List<DtoImportInstrumentCheckRecord> importCheckRecords,
                                                          List<DtoPerson> personList, String instrumentId) {
        //region 参数
        List<DtoInstrumentCheckRecord> checkRecordList = new ArrayList<>();
        //endregion

        for (DtoImportInstrumentCheckRecord importCheckRecord : importCheckRecords) {
            //获取校准人
            if (StringUtil.isNotEmpty(importCheckRecord.getCheckPerson())) {
                importCheckRecord.setCheckPersonId(UUIDHelper.GUID_EMPTY);
                importCheckRecord.setCheckPerson(importCheckRecord.getCheckPerson());
            }
            if (StringUtil.isNotEmpty(instrumentId)) {
                DtoInstrumentCheckRecord checkRecord = new DtoInstrumentCheckRecord();
                BeanUtils.copyProperties(importCheckRecord, checkRecord);
                checkRecord.setInstrumentId(instrumentId);
                checkRecord.setCheckTime(importUtils.stringToDateAllFormat(importCheckRecord.getCheckTime()));
                checkRecord.setCheckEndDate(importUtils.stringToDateAllFormat(importCheckRecord.getCheckEndDate()));
                checkRecord.setCost(BigDecimal.ZERO);
                checkRecordList.add(checkRecord);
            }
            //仪器信息
            if (StringUtil.isNotEmpty(importCheckRecord.getInstrumentCode())) {
                List<String> instrumentCodes = importUtils.getSplitList(importCheckRecord.getInstrumentCode());
                //获取到关联仪器信息
                for (String instrumentCode : instrumentCodes) {
                    DtoInstrumentCheckRecord checkRecord = new DtoInstrumentCheckRecord();
                    Optional<DtoInstrument> dtoInstrument = instrumentList.stream().filter(p -> instrumentCode.equals(p.getInstrumentsCode())).findFirst();
                    dtoInstrument.ifPresent(p -> importCheckRecord.setInstrumentId(p.getId()));
                    //赋值
                    BeanUtils.copyProperties(importCheckRecord, checkRecord);
                    checkRecord.setCheckTime(importUtils.stringToDateAllFormat(importCheckRecord.getCheckTime()));
                    checkRecord.setCheckEndDate(importUtils.stringToDateAllFormat(importCheckRecord.getCheckEndDate()));
                    checkRecord.setCost(BigDecimal.ZERO);
                    checkRecordList.add(checkRecord);
                }
            }
        }
        return checkRecordList;
    }

    @Autowired
    public void setInstrumentCheckRecordRepository(InstrumentCheckRecordRepository instrumentCheckRecordRepository) {
        this.instrumentCheckRecordRepository = instrumentCheckRecordRepository;
    }

    @Autowired
    public void setInstrumentService(InstrumentService instrumentService) {
        this.instrumentService = instrumentService;
    }

    @Autowired
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }

    @Autowired
    public void setInstrumentCheckRecordVerifyHandler(InstrumentCheckRecordVerifyHandler instrumentCheckRecordVerifyHandler) {
        this.instrumentCheckRecordVerifyHandler = instrumentCheckRecordVerifyHandler;
    }

    @Autowired
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }
}
