package com.sinoyd.lims.pro.strategy.strategy.submitRestrict;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoProjectPlan;
import com.sinoyd.lims.pro.dto.customer.DtoSubmitRestrictVo;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.strategy.IFileNameConstant;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目提交
 * <AUTHOR>
 */
@Component(IFileNameConstant.SubmitStrategyKey.SUBMIT_PROJECT)
public class ProjectSubmitStrategy extends AbsSubmitRestrictStrategy {

    @Override
    public List<DtoSubmitRestrictVo> generateSubmitRestrict(Object objMap, String status) {
        List<DtoSubmitRestrictVo> restrictVoList = new ArrayList<>();
        List<String> projectIds = (List<String>) objMap;
        List<DtoProject> projectList = projectService.findAll(projectIds);
        List<DtoProjectPlan> planList = projectPlanService.findByProjectIds(projectIds);
        projectList.forEach(p -> {
            Optional<DtoProjectPlan> planOptional = planList.stream()
                    .filter(plan -> p.getId().equals(plan.getProjectId())).findFirst();
            planOptional.ifPresent(plan -> p.setIsMakePlan(plan.getIsMakePlan()));
        });
        Set<String> projectTypeIds = projectList.stream().map(DtoProject::getProjectTypeId).collect(Collectors.toSet());
        Map<String, String> projectTypeCode = projectTypeService.getConfigValueByIds(projectTypeIds, "projectRegisterPage");
        //项目方案
        restrictVoList.add(checkProjectSample(projectList));
        //送样类参数判断
        if (projectTypeCode.size() > 0 && projectTypeCode.containsValue(EnumPRO.EnumProjectType.送样类.getValue())) {
            restrictVoList.add(checkSampleParams(projectList, projectTypeCode));
        }
        return restrictVoList;
    }

    /**
     * 验证项目方案
     *
     * @param projectList 项目集合
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkProjectSample(List<DtoProject> projectList) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.监测方案.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.监测方案.getModuleName());
        List<String> msgList = new ArrayList<>();
        projectList.forEach(p -> {
            if (p.getIsMakePlan() && findByProjectId(p.getId()).size() == 0) {
                msgList.add(String.format("项目%s未添加方案", p.getProjectCode()));
            }
        });
        if (msgList.size() > 0) {
            restrictVo.setExceptionOption(String.join(";", msgList));
            restrictVo.setIsPass(Boolean.FALSE);
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }

    /**
     * 验证送样类项目参数填写
     *
     * @param projectList     项目集合
     * @param projectTypeCode 项目类型
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkSampleParams(List<DtoProject> projectList, Map<String, String> projectTypeCode) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.登记参数必填验证.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.登记参数必填验证.getModuleName());
        List<String> msgList = new ArrayList<>();
        projectList.forEach(p -> {
            String configType = projectTypeCode.getOrDefault(p.getProjectTypeId(), "");
            if (StringUtil.isNotEmpty(configType)) {
                if (EnumPRO.EnumProjectType.送样类.getValue().equals(configType)) {
                    if (projectService.checkSampleParamRequired(Collections.singletonList(p.getId()))) {
                        msgList.add(String.format("项目%s样品中存在必填项的参数未填写", p.getProjectCode()));
                    }
                }
            }
        });
        if (msgList.size() > 0) {
            restrictVo.setExceptionOption(String.join(";", msgList));
            restrictVo.setIsPass(Boolean.FALSE);
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }
}
