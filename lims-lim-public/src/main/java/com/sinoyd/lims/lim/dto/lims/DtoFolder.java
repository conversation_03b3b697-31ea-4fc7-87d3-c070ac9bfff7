package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.Folder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


/**
 * DtoFolder实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_BASE_Folder")
 @Data
 @DynamicInsert
 public  class DtoFolder extends Folder {

	@Transient
	private boolean isLeaf;
	
 }