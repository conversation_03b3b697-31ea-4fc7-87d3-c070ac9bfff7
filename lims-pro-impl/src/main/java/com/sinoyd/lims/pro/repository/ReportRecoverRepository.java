package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoReportRecover;

import java.util.List;


/**
 * reportrecover数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2022/4/14
 * @since V100R001
 */
public interface ReportRecoverRepository extends IBaseJpaPhysicalDeleteRepository<DtoReportRecover, String> {
    List<DtoReportRecover> findByProjectId(String projectId);
}