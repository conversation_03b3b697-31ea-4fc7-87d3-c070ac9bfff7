package com.sinoyd.lims.foreign;

import lombok.Data;

import java.util.Date;

/**
 * 曲线信息
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Data
public class DataCurveDetailVO {
    /**
     * id
     */
    private String id;

    /**
     * 曲线id
     */
    private String curveId;

    /**
     * 分析编号
     */
    private String analyseCode;

    /**
     * 标准溶液加入体积
     */
    private String addVolume;

    /**
     * 标准物加入量
     */
    private String addAmount;

    /**
     * 吸光度A
     */
    private String absorbance;

    /**
     * 减空白吸光度
     */
    private String lessBlankAbsorbance;

    /**
     * 吸光度B
     */
    private String absorbanceB;

    /**
     * 相对偏差
     */
    private String relativeDeviation;

    /**
     * 220吸光度
     */
    private String aValueTTZ;

    /**
     * 275吸光度
     */
    private String aValueTSF;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;
}
