DROP VIEW VI_PRO_PROJECTCOUNTSTATISTICVIEW;
CREATE VIEW "VI_PRO_PROJECTCOUNTSTATISTICVIEW"
            ("id","projectId","projectTypeId","projectTypeName","parentName","inceptTime","samplingTime","orgId")
AS
select NEWID()                 AS "id",
       "P"."id"                AS "projectId",
       "PT"."id"               AS "projectTypeId",
       "PT"."name"             AS "projectTypeName",
       "PTP"."name"            AS "parentName",
       "P"."inceptTime"        AS "inceptTime",
       "S"."samplingTimeBegin" AS "samplingTime",
       "P"."orgId"             AS "orgId"
from ((("TB_PRO_PROJECT" "P" join "TB_LIM_PROJECTTYPE" "PT" on ((("P"."isDeleted" = 0) and
                                                                 ("P"."projectTypeId" = "PT"."id") and
                                                                 (("P"."parentId" <> '00000000-0000-0000-0000-000000000000' or
                                                                   "p".projectTypeId <> '5a171aec-e30f-4191-9580-2318b55d63de')))))
    left join "TB_LIM_PROJECTTYPE" "PTP" on (("PTP"."id" = "PT"."parentId")))
         left join "TB_PRO_SAMPLE" "S" on (("S"."projectId" = "P"."id")));


COMMENT ON VIEW "VI_PRO_PROJECTCOUNTSTATISTICVIEW" IS 'VIEW';

COMMENT ON COLUMN "VI_PRO_PROJECTCOUNTSTATISTICVIEW"."inceptTime" IS '委托时间';

COMMENT ON COLUMN "VI_PRO_PROJECTCOUNTSTATISTICVIEW"."orgId" IS '组织机构id';

COMMENT ON COLUMN "VI_PRO_PROJECTCOUNTSTATISTICVIEW"."parentName" IS '项目类型名称';

COMMENT ON COLUMN "VI_PRO_PROJECTCOUNTSTATISTICVIEW"."projectId" IS 'id';

COMMENT ON COLUMN "VI_PRO_PROJECTCOUNTSTATISTICVIEW"."projectTypeId" IS 'id';

COMMENT ON COLUMN "VI_PRO_PROJECTCOUNTSTATISTICVIEW"."projectTypeName" IS '项目类型名称';

COMMENT ON COLUMN "VI_PRO_PROJECTCOUNTSTATISTICVIEW"."samplingTime" IS '采样开始时间';
