package com.sinoyd.lims.api.controller;

import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.dto.lims.DtoReportConfig;
import com.sinoyd.lims.pro.criteria.ReportCriteria;
import com.sinoyd.lims.pro.dto.DtoReport;
import com.sinoyd.lims.pro.service.ReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 报告服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/4
 * @since V100R001
 */
@Api(tags = "示例: Report服务")
@RestController
@RequestMapping("api/mobile/report")
public class MobileNewReportController extends BaseJpaController<DtoReport, String, ReportService> {
    /**
     * 分页动态条件查询报告
     *
     * @param reportCriteria 条件参数
     * @return RestResponse<List < Map < String, Object>>>
     */
    @ApiOperation(value = "分页动态条件查询报告", notes = "分页动态条件查询报告")
    @GetMapping
    public RestResponse<List<DtoReport>> findByPage(ReportCriteria reportCriteria) {
        PageBean<DtoReport> pageBean = super.getPageBean();
        RestResponse<List<DtoReport>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, reportCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * "信号操作
     *
     * @param dto 实体
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "信号操作", notes = "信号操作")
    @PostMapping("/signal")
    public RestResponse<Boolean> signal(@RequestBody DtoWorkflowSign dto) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.reportSignal(dto);
        restResp.setData(true);
        return restResp;
    }

    /**
     * 按主键查询报告
     *
     * @param id 主键id
     * @return RestResponse<Map < String, Object>>
     */
    @ApiOperation(value = "按主键查询报告", notes = "按主键查询报告")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoReport> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoReport> restResponse = new RestResponse<>();
        DtoReport report = service.findOne(id);
        restResponse.setData(report);
        restResponse.setRestStatus(StringUtil.isNull(report) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 获取报表名称
     *
     * @param map      参数信息
     * @param configId 配置id
     * @return 报表名称
     */
    @ApiOperation(value = "获取报表名称", notes = "获取报表名称")
    @PostMapping("/documentFileName/{configId}")
    public RestResponse<DtoReportConfig> documentFileName(@RequestBody Map<String, Object> map,
                                                          @PathVariable(name = "configId") String configId) {
        RestResponse<DtoReportConfig> restResp = new RestResponse<>();
        restResp.setData(service.documentFileName(map, configId));
        return restResp;
    }
}
