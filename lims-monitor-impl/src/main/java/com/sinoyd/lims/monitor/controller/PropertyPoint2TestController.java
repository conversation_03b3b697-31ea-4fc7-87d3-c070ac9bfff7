package com.sinoyd.lims.monitor.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.monitor.criteria.PropertyPoint2TestCriteria;
import com.sinoyd.lims.monitor.dto.lims.DtoPropertyPoint2Test;
import com.sinoyd.lims.monitor.service.PropertyPoint2TestService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * PropertyPoint2Test服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@Api(tags = "示例: PropertyPoint2Test服务")
@RestController
@RequestMapping("api/monitor/propertyPoint2Test")
public class PropertyPoint2TestController extends BaseJpaController<DtoPropertyPoint2Test, String, PropertyPoint2TestService> {


    /**
     * 分页动态条件查询PropertyPoint2Test
     *
     * @param propertyPoint2TestCriteria 条件参数
     * @return RestResponse<List < PropertyPoint2Test>>
     */
    @ApiOperation(value = "分页动态条件查询PropertyPoint2Test", notes = "分页动态条件查询PropertyPoint2Test")
    @GetMapping
    public RestResponse<List<DtoPropertyPoint2Test>> findByPage(PropertyPoint2TestCriteria propertyPoint2TestCriteria) {
        PageBean<DtoPropertyPoint2Test> pageBean = super.getPageBean();
        RestResponse<List<DtoPropertyPoint2Test>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, propertyPoint2TestCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询PropertyPoint2Test
     *
     * @param id 主键id
     * @return RestResponse<DtoPropertyPoint2Test>
     */
    @ApiOperation(value = "按主键查询PropertyPoint2Test", notes = "按主键查询PropertyPoint2Test")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoPropertyPoint2Test> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoPropertyPoint2Test> restResponse = new RestResponse<>();
        DtoPropertyPoint2Test propertyPoint2Test = service.findOne(id);
        restResponse.setData(propertyPoint2Test);
        restResponse.setRestStatus(StringUtil.isNull(propertyPoint2Test) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增PropertyPoint2Test
     *
     * @param propertyPoint2Test 实体列表
     * @return RestResponse<DtoPropertyPoint2Test>
     */
    @ApiOperation(value = "新增PropertyPoint2Test", notes = "新增PropertyPoint2Test")
    @PostMapping
    public RestResponse<DtoPropertyPoint2Test> create(@RequestBody DtoPropertyPoint2Test propertyPoint2Test) {
        RestResponse<DtoPropertyPoint2Test> restResponse = new RestResponse<>();
        restResponse.setData(service.save(propertyPoint2Test));
        return restResponse;
    }

    /**
     * 新增PropertyPoint2Test
     *
     * @param propertyPoint2Test 实体列表
     * @return RestResponse<DtoPropertyPoint2Test>
     */
    @ApiOperation(value = "修改PropertyPoint2Test", notes = "修改PropertyPoint2Test")
    @PutMapping
    public RestResponse<DtoPropertyPoint2Test> update(@RequestBody DtoPropertyPoint2Test propertyPoint2Test) {
        RestResponse<DtoPropertyPoint2Test> restResponse = new RestResponse<>();
        restResponse.setData(service.update(propertyPoint2Test));
        return restResponse;
    }

    /**
     * "根据id批量删除PropertyPoint2Test
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除PropertyPoint2Test", notes = "根据id批量删除PropertyPoint2Test")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 分页动态条件查询已关联的测试项目
     *
     * @param propertyPoint2TestCriteria 条件参数
     * @return RestResponse<List < PropertyPoint2Test>>
     */
    @ApiOperation(value = "分页动态条件查询已关联的测试项目", notes = "分页动态条件查询已关联的测试项目")
    @GetMapping("/tests")
    public RestResponse<List<DtoTest>> findTest(PropertyPoint2TestCriteria propertyPoint2TestCriteria) {
        PageBean<DtoTest> pageBean = super.getPageBean();
        RestResponse<List<DtoTest>> restResponse = new RestResponse<>();
        service.findTest(pageBean, propertyPoint2TestCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(StringUtil.isEmpty(pageBean.getData()) ? 0 : pageBean.getData().size());
        return restResponse;
    }
}