package com.sinoyd.lims.foreign.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.foreign.DataSampleGroupVO;
import com.sinoyd.lims.foreign.DataSamplePreparationVO;
import com.sinoyd.lims.foreign.DataSampleVO;
import com.sinoyd.lims.foreign.service.DataSamplePreparationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 样品制备信息接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Service
public class DataSamplePreparationServiceImpl extends AbsQueryDataServiceImpl<DataSamplePreparationVO> implements DataSamplePreparationService {

    @Override
    public StringBuilder getSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("select id, sampleId,analyzeItemNames, preparationBeginTime, preparationEndTime,");
        sql.append("preparedPersonName, method, content, instrumentId, orgId, domainId");
        sql.append(" from TB_PRO_SamplePreparation ");
        sql.append(" WHERE 1 = 1 ");
        return sql;
    }

    @Override
    protected void appendCondition(StringBuilder sql, Map<String, Object> map, String condition, String paramName) {
        super.appendCondition(sql, map, "AND modifyDate > :syncTime ", "syncTime");
        super.appendCondition(sql, map, "AND sampleId in (:sampleIds)", "sampleIds");
    }

    @Override
    protected RowMapper<DataSamplePreparationVO> getEntityRowMapper() {
        return (rs, i) -> {
            DataSamplePreparationVO samplePreparationVO = new DataSamplePreparationVO();
            samplePreparationVO.setId(UUIDHelper.NewID());
            samplePreparationVO.setSampleId(rs.getString("sampleId"));
            samplePreparationVO.setAnalyzeItemNames(rs.getString("analyzeItemNames"));
            samplePreparationVO.setPreparationBeginTime(rs.getDate("preparationBeginTime"));
            samplePreparationVO.setPreparationEndTime(rs.getDate("preparationEndTime"));
            samplePreparationVO.setPreparedPersonName(rs.getString("preparedPersonName"));
            samplePreparationVO.setMethod(rs.getString("method"));
            samplePreparationVO.setContent(rs.getString("content"));
            samplePreparationVO.setInstrumentId(rs.getString("instrumentId"));
            samplePreparationVO.setOrgId(rs.getString("orgId"));
            samplePreparationVO.setDomainId(rs.getString("domainId"));
            samplePreparationVO.setSyncTime(new Date());
            return samplePreparationVO;
        };
    }

}
