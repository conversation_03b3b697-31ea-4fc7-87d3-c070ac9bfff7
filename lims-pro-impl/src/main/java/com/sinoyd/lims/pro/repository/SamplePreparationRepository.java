package com.sinoyd.lims.pro.repository;


import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoSamplePreparation;

import java.util.Collection;
import java.util.List;

/**
 * 样品制备数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/03/17
 * @since V100R001
 */
public interface SamplePreparationRepository extends IBaseJpaRepository<DtoSamplePreparation,String>, LimsRepository<DtoSamplePreparation,String> {

    /**
     * 根据id集合获取样品制备信息
     *
     * @param sampleIds 样品id集合
     * @return 样品制备信息
     */
    List<DtoSamplePreparation> findBySampleIdIn(Collection<String> sampleIds);

    /**
     * 根据id获取样品制备信息
     *
     * @param sampleId 样品id
     * @return 样品制备信息
     */
    List<DtoSamplePreparation> findBySampleId(String sampleId);
}
