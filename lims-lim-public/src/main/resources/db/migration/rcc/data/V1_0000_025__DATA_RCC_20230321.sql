-- ----------------------------------------------
-- -------- 归属于RCC部分的表相关初始化数据脚本 -------
-- -------- TB_Lim_AppConfig --------------------
-- ----------------------------------------------

INSERT INTO TB_Lim_AppConfig (id, name, code, linkAddress, roleId, status, orderNum, remark, orgId, creator, createDate, domainId, modifier, modifyDate, type, typeOrderNum) VALUES ('0809e20e-bf35-4f5d-bf73-db68c8dabe28', '现场监测', 'deliveryProject', '', '', true, 0, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-01 13:07:16', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-01 13:31:04', '现场监测', 1);
INSERT INTO TB_Lim_AppConfig (id, name, code, linkAddress, roleId, status, orderNum, remark, orgId, creator, createDate, domainId, modifier, modifyDate, type, typeOrderNum) VALUES ('81cf3686-090e-4cef-a0d5-d9681722ad19', '委托监测', 'entrustProject', '', '', true, 0, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-01 13:07:16', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-01 13:31:04', '现场监测', 1);
INSERT INTO TB_Lim_AppConfig (id, name, code, linkAddress, roleId, status, orderNum, remark, orgId, creator, createDate, domainId, modifier, modifyDate, type, typeOrderNum) VALUES ('90e96905-2c73-49fa-984d-86f826368ad4', '数据查询', 'dataQuery', '', '', true, 0, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-01 13:07:16', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-01 13:31:04', '现场监测', 1);
INSERT INTO TB_Lim_AppConfig (id, name, code, linkAddress, roleId, status, orderNum, remark, orgId, creator, createDate, domainId, modifier, modifyDate, type, typeOrderNum) VALUES ('9f3f8939-a154-47cd-92e0-a5c471e379a3', '仪器出库', 'deliveryInstrument', '', '', true, 0, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-01 13:07:16', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-01 13:31:04', '仪器出入库', 0);
INSERT INTO TB_Lim_AppConfig (id, name, code, linkAddress, roleId, status, orderNum, remark, orgId, creator, createDate, domainId, modifier, modifyDate, type, typeOrderNum) VALUES ('a2344a6c-6047-48e8-b7d2-248906a77298', '应急监测', 'emergencyProject', '', '', true, 0, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-01 13:07:16', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-01 13:31:04', '现场监测', 1);
INSERT INTO TB_Lim_AppConfig (id, name, code, linkAddress, roleId, status, orderNum, remark, orgId, creator, createDate, domainId, modifier, modifyDate, type, typeOrderNum) VALUES ('ab9f045a-2ed8-4068-af85-702b0b15f90f', '仪器入库', 'stockInstrument', '', '', true, 0, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-01 13:07:16', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-01 13:31:04', '仪器出入库', 0);
