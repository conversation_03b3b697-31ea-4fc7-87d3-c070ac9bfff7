alter table tb_base_document add column auditStatus int(10) not null default -1 comment '状态';
alter table tb_base_document add column operateDate datetime comment '操作时间';
alter table tb_base_document add column operatePersonId varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '操作人Id';
alter table tb_base_document add column operatePerson varchar(50) comment '操作人名称';
update tb_base_document set auditStatus = 3, operateDate = abolishDate, operatePersonId = abolishPersonId, operatePerson = abolishPerson where isAbolish = 1;
alter table tb_base_document drop column  isAbolish;
alter table tb_base_document drop column  abolishDate;
alter table tb_base_document drop column  abolishPersonId;
alter table tb_base_document drop column  abolishPerson;
