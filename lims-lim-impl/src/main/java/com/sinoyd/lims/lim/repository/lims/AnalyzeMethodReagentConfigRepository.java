package com.sinoyd.lims.lim.repository.lims;

import java.util.List;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoAnalyzeMethodReagentConfig;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * 试剂配置记录仓储
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-05-14
 * @since V100R001
 */
public interface AnalyzeMethodReagentConfigRepository
        extends IBaseJpaPhysicalDeleteRepository<DtoAnalyzeMethodReagentConfig, String> {

    /**
     * 根据analyzeMethodId获取试剂配置记录
     * 
     * @param analyzeMethodId 分析方法analyzeMethodId
     * @return 试剂配置记录集合
     */
    @Query("select p from DtoAnalyzeMethodReagentConfig p where p.analyzeMethodId = :analyzeMethodId")
    List<DtoAnalyzeMethodReagentConfig> getListByAnalyzeMethodId(@Param("analyzeMethodId") String analyzeMethodId);
}