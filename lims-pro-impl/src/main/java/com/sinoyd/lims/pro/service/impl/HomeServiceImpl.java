package com.sinoyd.lims.pro.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.AuthorizeService;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.lims.lim.criteria.FastNavigationTemplateCriteria;
import com.sinoyd.lims.lim.dto.customer.DtoSchemaVersionTemp;
import com.sinoyd.lims.lim.dto.lims.DtoFastNavigationTemplate;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.FastNavigationTemplateService;
import com.sinoyd.lims.lim.service.MessageSendRecordService;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.lim.service.ProjectTypeService;
import com.sinoyd.lims.pro.configuration.ProjectModule;
import com.sinoyd.lims.pro.configuration.ProjectTaskConfig;
import com.sinoyd.lims.pro.core.ProCodeHelper;
import com.sinoyd.lims.pro.criteria.HomeKeyProjectCriteria;
import com.sinoyd.lims.pro.criteria.HomeNoticeCriteria;
import com.sinoyd.lims.pro.criteria.OATaskCriteria;
import com.sinoyd.lims.pro.dto.DtoHomePendingNo;
import com.sinoyd.lims.pro.dto.DtoOATask;
import com.sinoyd.lims.pro.dto.DtoOATaskQuery;
import com.sinoyd.lims.pro.dto.customer.*;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.HomePendingNoRepository;
import com.sinoyd.lims.pro.repository.ProjectRepository;
import com.sinoyd.lims.pro.repository.SampleRepository;
import com.sinoyd.lims.pro.service.HomeService;
import com.sinoyd.lims.pro.util.EnumProUtil;
import com.sinoyd.lims.pro.util.MathUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class HomeServiceImpl implements HomeService {

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private ProjectTaskConfig projectTaskConfig;

    @Autowired
    private CommonRepository commonRepository;

    @Autowired
    @Lazy
    private ProjectTypeService projectTypeService;

    @Autowired
    @Lazy
    private PersonService personService;

    @Autowired
    private SampleRepository sampleRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private FastNavigationTemplateService fastNavigationTemplateService;

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    @Autowired
    private CodeService codeService;

    @Autowired
    private AuthorizeService authorizeService;

    @Autowired
    private MessageSendRecordService messageSendRecordService;

    @Autowired
    private HomePendingNoRepository homePendingNoRepository;

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @Async
    @Override
    public Future<Integer> cacheProjectTask(String userId,
                                            String orgId,
                                            String moduleCode) {
        Integer num = 0;
        //读取到配置的模块
        List<ProjectModule> projectModules = projectTaskConfig.getProjectModules();
        ProjectModule projectModule = projectModules.stream().filter(p -> p.getModuleCode().equals(moduleCode)).findFirst().orElse(null);
        if (StringUtil.isNotNull(projectModule)) {
            //得到绑定的模块
            List<String> bindingModules = projectModule.getBindingModules();
            //获取包括绑定模块的所有模块
            List<String> newModules = getNewModules(bindingModules, moduleCode, userId);
            //对相关的及本身的数据进行查询处理
            for (String newModule : newModules) {
                ProjectModule module = projectModules.stream().filter(p -> p.getModuleCode().equals(newModule)).findFirst().orElse(null);
                Integer value = module.getValue();
                Map<String, Integer> map = null;
                String redisKey = getRedisKey(userId, orgId, value, newModule);
                Object object = redisTemplate.opsForValue().get(redisKey);
                if (object != null && !object.equals("")) {
                    map = JsonIterator.deserialize(object.toString(), Map.class);
                }
                //说明没有缓存的key，代表需要重新从数据库中拉取数据，否则从缓存中获取数据
                if (StringUtil.isNull(map)) {
                    map = new HashMap<>();//重新初始化
                    Integer l = find(userId, orgId, module);
                    num += l;
                    map.put("taskNum", l);
                    redisTemplate.opsForValue().set(redisKey, JsonStream.serialize(map), module.getCacheTimeout(), TimeUnit.SECONDS);
                } else {
                    num += map.get("taskNum");
                }
            }
        } else if (moduleCode.equals(EnumLIM.EnumHomeTaskModule.实验室分析.getValue())) {
            //对相关的及本身的数据进行查询处理
            List<String> modules = Arrays.asList(EnumLIM.EnumHomeTaskModule.实验室待检.getValue(), EnumLIM.EnumHomeTaskModule.实验室检测.getValue(), EnumLIM.EnumHomeTaskModule.实验室审核.getValue());
            for (String module : modules) {
                String redisKey = String.format("PRO:%s:%s:%s", orgId, userId, module);
                Map<String, Integer> map = null;
                Object object = redisTemplate.opsForValue().get(redisKey);
                if (object != null && !object.equals("")) {
                    map = JsonIterator.deserialize(object.toString(), Map.class);
                }
                if (StringUtil.isNotNull(map)) {
                    num += map.get("taskNum");
                }
            }
        }
        return new AsyncResult<>(num);
    }

    @Override
    public void clearProjectTask(String userId, String orgId, String moduleCode, String nextModuleCode) {
        List<ProjectModule> projectModules = projectTaskConfig.getProjectModules();
        ProjectModule projectModule = projectModules.stream().filter(p -> StringUtils.isNotNullAndEmpty(moduleCode) &&
                p.getModuleCode().equals(moduleCode)).findFirst().orElse(null);
        ProjectModule nextProjectModule = projectModules.stream().filter(p -> StringUtils.isNotNullAndEmpty(nextModuleCode) &&
                p.getModuleCode().equals(nextModuleCode)).findFirst().orElse(null);
        clearProjectTask(userId, orgId, projectModule);
        clearProjectTask(userId, orgId, nextProjectModule);
    }

    @Override
    public void clearProjectTask(String userId, String orgId, String moduleCode, List<String> nextModuleCodes) {
        for (String nextModuleCode : nextModuleCodes) {
            clearProjectTask(userId, orgId, moduleCode, nextModuleCode);
        }
    }

    /**
     * 清除缓存并重新计算
     *
     * @param userId        用户id
     * @param orgId         组织机构id
     * @param projectModule 模块信息
     */
    private void clearProjectTask(String userId, String orgId, ProjectModule projectModule) {
        if (StringUtil.isNotNull(projectModule)) {
            String redisKey = getRedisKey(userId, orgId, projectModule.getValue(), projectModule.getModuleCode());
            //清除指定key
            redisTemplate.delete(redisKey);
            cacheProjectTask(userId, orgId, projectModule, redisKey);
        }
    }


    /**
     * 重新缓存数据
     *
     * @param userId        项目id
     * @param orgId         组织机构id
     * @param projectModule 首页任务编号
     * @param redisKey      指定rediskey
     */
    @Async
    public void cacheProjectTask(String userId, String orgId, ProjectModule projectModule, String redisKey) {
        Integer num = find(userId, orgId, projectModule);
        Map<String, Object> map = new HashMap<>();//重新初始化
        map.put("taskNum", num);
        //对指定key重新赋值
        redisTemplate.opsForValue().set(redisKey, JsonStream.serialize(map), projectModule.getCacheTimeout(), TimeUnit.SECONDS);
    }

    /**
     * 进行实验室分析的任务数量缓存
     *
     * @param userId  用户id
     * @param orgId   组织机构id
     * @param module  模块
     * @param taskNum 任务数
     */
    @Override
    public void cacheAnalysTask(String userId, String orgId, String module, Integer taskNum) {
        String redisKey = String.format("PRO:%s:%s:%s", orgId, userId, module);
        Map<String, Object> map = new HashMap<>();//重新初始化
        map.put("taskNum", taskNum);
        //对指定key进行赋值
        redisTemplate.opsForValue().set(redisKey, JsonStream.serialize(map));
    }

    @Override
    public void findCacheKeyProjects(PageBean<Map<String, Object>> pageBean) {
        pageBean.setSort("inputTime-");
        HomeKeyProjectCriteria projectCriteria = new HomeKeyProjectCriteria();
        if (pageBean.getPageNo() == 1) { //第一页从缓存中加载，后面的从数据库中查询
            String moduleCode = EnumLIM.EnumHomeTaskModule.重点项目.getValue();
            //读取到配置的模块
            List<ProjectModule> projectModules = projectTaskConfig.getProjectModules();
            List<Map<String, Object>> maps = null;
            ProjectModule projectModule = projectModules.stream().filter(p -> p.getModuleCode().equals(moduleCode)).findFirst().orElse(null);
            if (StringUtil.isNotNull(projectModule)) {
                String userId = PrincipalContextUser.getPrincipal().getUserId();
                String orgId = PrincipalContextUser.getPrincipal().getOrgId();
                String redisKey = getRedisKey(userId,
                        orgId,
                        EnumLIM.EnumProjectTaskCache.权限.getValue(), projectModule.getModuleCode());
                Object object = redisTemplate.opsForValue().get(redisKey);
                if (object != null && !object.equals("")) {
                    maps = JsonIterator.deserialize(object.toString(), List.class);
                    pageBean.setData(maps);
                }
                //说明没有缓存的key，代表需要重新从数据库中拉取数据，否则从缓存中获取数据
                if (StringUtil.isNull(maps)) {
                    findKeyProjects(pageBean, projectCriteria);
                    redisTemplate.opsForValue().set(redisKey, JsonStream.serialize(pageBean.getData()),
                            projectModule.getCacheTimeout(), TimeUnit.SECONDS);
                }
            }
        } else {
            findKeyProjects(pageBean, projectCriteria);
        }
    }


    /**
     * 查询重点项目
     *
     * @param pageBean     分页条件
     * @param baseCriteria 查询条件
     */
    private void findKeyProjects(PageBean<Map<String, Object>> pageBean, BaseCriteria baseCriteria) {
        PageBean<Object[]> pb = new PageBean<>();
        pb.setRowsPerPage(pageBean.getRowsPerPage());
        pb.setPageNo(pageBean.getPageNo());
        pb.setSort(pageBean.getSort());
        pb.setEntityName("DtoProject p,DtoProjectPlan pl");
        pb.setSelect("select p.id,p.projectCode,p.grade,p.projectTypeId,p.projectName,p.status,p.json,p.customerName,pl.leaderId,pl.isMakePlan,p.inceptTime");
        commonRepository.findByPage(pb, baseCriteria);
        List<Object[]> list = pb.getData();
        //取出项目类型
        List<String> projectTypeIds = list.stream().map(p -> (String) p[3]).distinct().collect(Collectors.toList());
        //项目负责人ids
        List<String> leaderIds = list.stream().map(p -> (String) p[8]).distinct().collect(Collectors.toList());
        List<DtoProjectType> projectTypes = new ArrayList<>();
        if (projectTypeIds.size() > 0) {
            projectTypes = projectTypeService.findRedisByIds(projectTypeIds);
        }
        List<DtoPerson> persons = new ArrayList<>();
        if (leaderIds.size() > 0) {
            persons = personService.findAllDeleted(leaderIds);
        }
        List<Map<String, Object>> maps = new ArrayList<>();
        Iterator<Object[]> projectIte = list.iterator();
        while (projectIte.hasNext()) {
            Map<String, Object> map = new HashMap<>();
            Object[] obj = projectIte.next();
            String projectId = (String) obj[0]; //项目id
            String projectCode = (String) obj[1]; //项目编号
            Integer grade = (Integer) obj[2]; //等级
            String projectTypeId = (String) obj[3];//项目类型
            String projectName = (String) obj[4]; //项目名称
            String status = (String) obj[5]; //项目状态
            String json = (String) obj[6]; //存储的json数据
            String customerName = (String) obj[7]; //委托方
            String leaderId = (String) obj[8]; //项目负责人
            Boolean isMakePlan = (Boolean) obj[9]; //是否编制方案
            Date inceptTime = (Date) obj[10]; //项目委托时间
            String leaderName = ""; //项目负责人名称
            String projectTypeName = "";
            List<BigDecimal> collectionDetail = new ArrayList<>();
            if (StringUtils.isNotNullAndEmpty(json)) {
                Map jsonMap = JsonIterator.deserialize(json, Map.class);
                if (jsonMap.containsKey("collectionDetail") && StringUtil.isNotNull(jsonMap.get("collectionDetail"))) {
                    collectionDetail = (List<BigDecimal>) jsonMap.get("collectionDetail");
                }
            }
            DtoProjectType projectType = projectTypes.stream().filter(p -> p.getId().equals(projectTypeId)).findFirst().orElse(null);
            if (StringUtil.isNotNull(projectType)) {
                projectTypeName = projectType.getName();
            }

            DtoPerson person = persons.stream().filter(p -> p.getId().equals(leaderId)).findFirst().orElse(null);
            if (StringUtil.isNotNull(person)) {
                leaderName = person.getCName();
            }
            map.put("id", projectId);
            map.put("projectCode", projectCode);
            map.put("grade", grade);
            map.put("projectTypeId", projectTypeId);
            map.put("projectTypeName", projectTypeName);
            map.put("projectName", projectName);
            map.put("status", status);
            map.put("collectionDetail", collectionDetail);
            map.put("customerName", customerName);
            map.put("leaderName", leaderName);
            map.put("isMakePlan", isMakePlan);
            map.put("inceptTime", inceptTime);
            maps.add(map);
        }
        pageBean.setData(maps);
        pageBean.setRowsCount(pb.getRowsCount());
    }


    @Override
    public void findCacheNotices(PageBean<Map<String, Object>> pageBean) {
        pageBean.setSort("releaseTime-");
        HomeNoticeCriteria noticeCriteria = new HomeNoticeCriteria();
        if (pageBean.getPageNo() == 1) { //第一页从缓存中加载，后面的从数据库中查询
            String moduleCode = EnumLIM.EnumHomeTaskModule.公告.getValue();
            //读取到配置的模块
            List<ProjectModule> projectModules = projectTaskConfig.getProjectModules();
            List<Map<String, Object>> maps = null;
            ProjectModule projectModule = projectModules.stream().filter(p -> p.getModuleCode().equals(moduleCode)).findFirst().orElse(null);
            if (StringUtil.isNotNull(projectModule)) {
                String userId = PrincipalContextUser.getPrincipal().getUserId();
                String orgId = PrincipalContextUser.getPrincipal().getOrgId();
                String redisKey = getRedisKey(userId,
                        orgId,
                        EnumLIM.EnumProjectTaskCache.权限.getValue(), projectModule.getModuleCode());
                Object object = redisTemplate.opsForValue().get(redisKey);
                if (object != null && !object.equals("")) {
                    maps = JsonIterator.deserialize(object.toString(), List.class);
                    pageBean.setData(maps);
                }
                //说明没有缓存的key，代表需要重新从数据库中拉取数据，否则从缓存中获取数据
                if (StringUtil.isNull(maps)) {
                    findNotices(pageBean, noticeCriteria);
                    redisTemplate.opsForValue().set(redisKey, JsonStream.serialize(pageBean.getData()),
                            projectModule.getCacheTimeout(), TimeUnit.SECONDS);
                }
            }
        } else {
            findNotices(pageBean, noticeCriteria);
        }
    }


    @Async
    @Override
    public void clearCacheNotices(String userId, String orgId) {
        String redisKey = getRedisKey(userId,
                orgId,
                EnumLIM.EnumProjectTaskCache.权限.getValue(), EnumLIM.EnumHomeTaskModule.公告.getValue());
        //清除指定key
        redisTemplate.delete(redisKey);
    }


    @Override
    public void findOATaskCache(PageBean<DtoOATask> pageBean, String moduleCode) {
        String userId = PrincipalContextUser.getPrincipal().getUserId();
        String orgId = PrincipalContextUser.getPrincipal().getOrgId();
        String redisKey = getRedisKey(userId,
                orgId,
                EnumLIM.EnumProjectTaskCache.人员.getValue(), moduleCode);
        String oaTaskModuleCode = EnumLIM.EnumHomeTaskModule.我的审批.getValue();
        List<ProjectModule> projectModules = projectTaskConfig.getProjectModules();
        ProjectModule projectModule = projectModules.stream().filter(p -> p.getModuleCode().equals(oaTaskModuleCode)).findFirst().orElse(null);
        DtoOATaskQuery param = new DtoOATaskQuery();
        Map<String, Object> map = null;

        Object object = redisTemplate.opsForValue().get(redisKey);
        if (object != null && !object.equals("")) {
            map = JsonIterator.deserialize(object.toString(), Map.class);
            pageBean.setRowsCount((Integer) map.get("totalCount"));
            pageBean.setData((List<DtoOATask>) map.get("data"));
        }
        //说明没有缓存的key，代表需要重新从数据库中拉取数据，否则从缓存中获取数据
        if (StringUtil.isNull(map)) {
            findOaTask(pageBean, param, moduleCode);
            map = new HashMap<>();
            map.put("totalCount", pageBean.getRowsCount());
            map.put("data", pageBean.getData());
            if (StringUtil.isNotNull(projectModule)) {
                redisTemplate.opsForValue().set(redisKey, JsonStream.serialize(map),
                        projectModule.getCacheTimeout(), TimeUnit.SECONDS);
            }
        }
    }


    @Override
    public void findFastNavigationCache(PageBean<DtoFastNavigationTemplate> pageBean) {
        String userId = PrincipalContextUser.getPrincipal().getUserId();
        String orgId = PrincipalContextUser.getPrincipal().getOrgId();
        String moduleCode = EnumLIM.EnumHomeTaskModule.快速导航.getValue();
        String redisKey = getRedisKey(userId,
                orgId,
                EnumLIM.EnumProjectTaskCache.人员.getValue(), moduleCode);
        List<ProjectModule> projectModules = projectTaskConfig.getProjectModules();
        ProjectModule projectModule = projectModules.stream().filter(p -> p.getModuleCode().equals(moduleCode)).findFirst().orElse(null);
        DtoOATaskQuery param = new DtoOATaskQuery();
        List<DtoFastNavigationTemplate> list = null;
        Object object = redisTemplate.opsForValue().get(redisKey);
        if (object != null && !object.equals("")) {
            list = JsonIterator.deserialize(object.toString(), List.class);
            list = list.stream().sorted(Comparator.comparing(DtoFastNavigationTemplate::getOrderNum).reversed()).collect(Collectors.toList());
            pageBean.setData(list);
        }
        //说明没有缓存的key，代表需要重新从数据库中拉取数据，否则从缓存中获取数据
        if (StringUtil.isNull(list)) {
            FastNavigationTemplateCriteria fastNavigationTemplateCriteria = new FastNavigationTemplateCriteria();
            pageBean.setSort("orderNum-");
            fastNavigationTemplateService.findByPage(pageBean, fastNavigationTemplateCriteria);
            if (StringUtil.isNotNull(projectModule)) {
                redisTemplate.opsForValue().set(redisKey, JsonStream.serialize(pageBean.getData()),
                        projectModule.getCacheTimeout(), TimeUnit.SECONDS);
            }
        }
    }

    @Async
    @Override
    public void clearFastNavigationCache(String userId, String orgId) {
        String redisKey = getRedisKey(userId,
                orgId,
                EnumLIM.EnumProjectTaskCache.人员.getValue(), EnumLIM.EnumHomeTaskModule.快速导航.getValue());
        //清除指定key
        redisTemplate.delete(redisKey);
    }

    @Override
    public Map<String, Integer> findSampleCountByMonth(int year, int month) {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, year);
        //设置月份
        cal.set(Calendar.MONTH, month - 1);
        //获取某月最小天数
        int firstDay = cal.getActualMinimum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最小天数
        cal.set(Calendar.DAY_OF_MONTH, firstDay);
        //格式化日期
        Date firstDayOfMont = cal.getTime();
        //获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        //当月加一天
        cal.add(Calendar.DATE, 1);
        //格式化日期
        Date lastDayOfMonth = cal.getTime();

        //通过日期获取样品
        Map<String, Object> values = new HashMap<>();
        StringBuilder select = new StringBuilder();
        select.append("select s.inceptTime from ");
        select.append(" DtoSample s");
        select.append(" where s.isDeleted = 0 and s.inceptTime >= :firstDay and s.inceptTime < :lastDay ");
        select.append(" and s.sampleCategory = 0 and s.status != :status ");
        values.put("firstDay", firstDayOfMont);
        values.put("lastDay", lastDayOfMonth);
        values.put("status", EnumPRO.EnumSampleStatus.样品未采样.name());
        List<Object> dataList = commonRepository.find(select.toString(), values);
        List<String> samList = new ArrayList<>();
        Iterator<Object> dataIte = dataList.iterator();
        while (dataIte.hasNext()) {
            Object obj = dataIte.next();
            Date samTime = (Date) obj; //项目id
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String time = sdf.format(samTime);
            samList.add(time);
        }
        Map<String, List<String>> map = samList.stream().collect(Collectors.groupingBy(p -> p));
        Map<String, Integer> retMap = new HashMap<>();
        for (String str : map.keySet()) {
            retMap.put(str, map.get(str).size());
        }
        return retMap;
    }

    /**
     * 清除带我审批的任务
     *
     * @param userId     用户ID
     * @param orgId      组织机构ID
     * @param moduleCode 模块编码
     */
    @Async
    @Override
    public void clearOATaskCache(String userId, String orgId, String moduleCode) {
        //说明按人员进行过滤
        String redisKey = getRedisKey(userId,
                orgId,
                EnumLIM.EnumProjectTaskCache.人员.getValue(), moduleCode);
        //清除指定key
        redisTemplate.delete(redisKey);
    }

    //#region 首页统计

    /**
     * @param code 统计编码
     */
    @Override
    public List findStatCache(String code) {
        EnumPRO.EnumHomeStat homeStat = EnumPRO.EnumHomeStat.getValueByCode(code);
        String redisKey = EnumPRO.EnumPRORedis.getRedisKey(homeStat.getValue().getValue());
        if (!redisTemplate.hasKey(redisKey)) {
            List<ProjectModule> projectModules = projectTaskConfig.getProjectModules();
            ProjectModule module = projectModules.stream().filter(p -> p.getModuleCode().equals(code)).findFirst().orElse(null);
            Integer cacheTimeout = StringUtil.isNotNull(module) ? module.getCacheTimeout() : 0;
            //基于方便阅读，放弃使用反射调用方法缓存
            switch (homeStat) {
                case 委托统计:
                    this.cacheCustomer(cacheTimeout, redisKey);
                    break;

                case 样品统计:
                    this.cacheSample(cacheTimeout, redisKey);
                    break;

                case 检测状态统计:
                    this.cacheAnalyseStatus(cacheTimeout, redisKey);
                    break;

                case 检测及时率:
                    this.cacheAnalysePromptness(cacheTimeout, redisKey);
                    break;

                case 业务量趋势:
                    this.cacheBusiness(cacheTimeout, redisKey);
                    break;

                case 样品数趋势:
                    this.cacheSampleNum(cacheTimeout, redisKey);
                    break;

                default:
                    break;
            }
        }
        Object jsonValue = redisTemplate.opsForValue().get(redisKey);
        return JsonIterator.deserialize(jsonValue.toString(), List.class);
    }

    /**
     * 缓存委托统计数据
     *
     * @param cacheTimeout 过期秒数
     */
    private void cacheCustomer(Integer cacheTimeout, String redisKey) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        Date yesterday = DateUtil.stringToDate(DateUtil.dateToString(calendar.getTime(), DateUtil.YEAR), DateUtil.YEAR);
        Date today = DateUtil.stringToDate(DateUtil.dateToString(new Date(), DateUtil.YEAR), DateUtil.YEAR);

        DtoHomeCustomerStat stat = new DtoHomeCustomerStat();
        List<Object[]> samples = countSampleNumGroupBySamplingTimeBegin(yesterday, PrincipalContextUser.getPrincipal().getOrgId());
        for (Object[] sample : samples) {
            String yyyyMMdd = sample[0].toString() + "-" + sample[1].toString() + "-" + sample[2].toString();
            Date date = DateUtil.stringToDate(yyyyMMdd, DateUtil.YEAR);
            if (date.compareTo(yesterday) == 0) {
                stat.setYesterdaySample(((Long) sample[3]).intValue());
            } else if (date.compareTo(today) == 0) {
                stat.setTodaySample(((Long) sample[3]).intValue());
            }
        }
        List<Object[]> projects = projectRepository.countProjectNumGroupByInceptTime(yesterday);
        for (Object[] project : projects) {
            Date date = DateUtil.stringToDate(DateUtil.dateToString((Date) project[0], DateUtil.YEAR), DateUtil.YEAR);
            if (date.compareTo(yesterday) == 0) {
                stat.setYesterdayProject(((Long) project[1]).intValue());
            } else if (date.compareTo(today) == 0) {
                stat.setTodayProject(((Long) project[1]).intValue());
            }
        }
        String jsonValue = JsonStream.serialize(Collections.singletonList(stat));
        if (cacheTimeout > 0) {
            redisTemplate.opsForValue().set(redisKey, jsonValue, cacheTimeout, TimeUnit.SECONDS);
        } else {
            redisTemplate.opsForValue().set(redisKey, jsonValue);
        }
    }

    /**
     * 缓存样品统计数据
     *
     * @param cacheTimeout 过期秒数
     * @param redisKey     key名
     */
    private void cacheSample(Integer cacheTimeout, String redisKey) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.MONTH, -3);
        String startTime = DateUtil.dateToString(calendar.getTime(), DateUtil.YEAR);

        List<DtoHomeSampleStat> statList = new ArrayList<>();
        List<Object[]> samples = sampleRepository.countSampleNumGroupByStatus(DateUtil.stringToDate(startTime, DateUtil.YEAR));
        Integer testCount = 0;
        for (Object[] sample : samples) {
            String status = (String) sample[0];
            Integer num = ((Long) sample[1]).intValue();
            if (status.equals(EnumPRO.EnumSampleStatus.样品未采样.toString())) {
                statList.add(new DtoHomeSampleStat("待采样", num, 1));
            } else if (status.equals(EnumPRO.EnumSampleStatus.样品未领样.toString())) {
                statList.add(new DtoHomeSampleStat("待领样", num, 2));
            } else if (status.equals(EnumPRO.EnumSampleStatus.样品在检.toString()) || status.equals(EnumPRO.EnumSampleStatus.样品待检.toString())) {
                testCount += num;

            } else if (status.equals(EnumPRO.EnumSampleStatus.样品检毕.toString())) {
                statList.add(new DtoHomeSampleStat("已检毕", num, 4));
            }
        }
        statList.add(new DtoHomeSampleStat("检测中", testCount, 3));
        statList.sort(Comparator.comparing(DtoHomeSampleStat::getOrderNum));
        String jsonValue = JsonStream.serialize(statList);
        if (cacheTimeout > 0) {
            redisTemplate.opsForValue().set(redisKey, jsonValue, cacheTimeout, TimeUnit.SECONDS);
        } else {
            redisTemplate.opsForValue().set(redisKey, jsonValue);
        }
    }

    /**
     * 缓存检测状态统计数据
     *
     * @param cacheTimeout 过期秒数
     * @param redisKey     key名
     */
    private void cacheAnalyseStatus(Integer cacheTimeout, String redisKey) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.MONTH, -3);
        String startTime = DateUtil.dateToString(calendar.getTime(), DateUtil.YEAR);
        List<Object[]> analyseDatas = countAnalyseDataNumGroupBySampleTypeIdAndDataStatus(DateUtil.stringToDate(startTime, DateUtil.YEAR), PrincipalContextUser.getPrincipal().getOrgId());
        Map<String, List<Object[]>> analyseDataMap = analyseDatas.stream().collect(Collectors.groupingBy(p -> (String) p[1]));

        List<DtoHomeAnalyseStatus> statList = new ArrayList<>();
        for (String sampleTypeId : analyseDataMap.keySet()) {
            DtoSampleType sampleType = sampleTypeService.findOne(sampleTypeId);
            DtoHomeAnalyseStatus stat = new DtoHomeAnalyseStatus();
            stat.setSampleTypeId(sampleTypeId);
            stat.setSampleTypeName(sampleType.getTypeName());
            stat.setOrderNum(sampleType.getOrderNum());

            for (Object[] analyseData : analyseDataMap.get(sampleTypeId)) {
                Integer dataStatus = (Integer) analyseData[0];
                Integer times = Integer.valueOf(analyseData[2].toString());
                if (dataStatus.equals(EnumPRO.EnumAnalyseDataStatus.未测.getValue())) {
                    stat.setWait(stat.getWait() + times);
                } else if (dataStatus.equals(EnumPRO.EnumAnalyseDataStatus.在测.getValue()) || dataStatus.equals(EnumPRO.EnumAnalyseDataStatus.拒绝.getValue())) {
                    stat.setTest(stat.getTest() + times);
                } else if (dataStatus.equals(EnumPRO.EnumAnalyseDataStatus.已测.getValue()) || dataStatus.equals(EnumPRO.EnumAnalyseDataStatus.复核通过.getValue())) {
                    stat.setCheck(stat.getCheck() + times);
                }
            }
            statList.add(stat);
        }

        statList.sort(Comparator.comparing(DtoHomeAnalyseStatus::getOrderNum).reversed());
        String jsonValue = JsonStream.serialize(statList);
        if (cacheTimeout > 0) {
            redisTemplate.opsForValue().set(redisKey, jsonValue, cacheTimeout, TimeUnit.SECONDS);
        } else {
            redisTemplate.opsForValue().set(redisKey, jsonValue);
        }
    }

    /**
     * 缓存检测及时率数据
     *
     * @param cacheTimeout 过期秒数
     * @param redisKey     key名
     */
    private void cacheAnalysePromptness(Integer cacheTimeout, String redisKey) {
        DtoCode code = codeService.findByCode(ProCodeHelper.ANALYSE_OVERDUE_DAYS);
        Integer overDueDays = StringUtil.isNotNull(code) ? (MathUtil.isInteger(code.getDictValue()) ? Integer.valueOf(code.getDictValue()) : 0) : 0;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.MONTH, -1);
        String startTime = DateUtil.dateToString(calendar.getTime(), DateUtil.YEAR);
        List<Object[]> samples = findSamplingTimeBeginAndLastNewSubmitTime(DateUtil.stringToDate(startTime, DateUtil.YEAR), PrincipalContextUser.getPrincipal().getOrgId());

        DtoHomeAnalysePromptness stat = new DtoHomeAnalysePromptness();
        Integer promptness = 0;
        for (Object[] sample : samples) {
            Date samplingTimeBegin = (Date) sample[0];
            samplingTimeBegin = DateUtil.stringToDate(DateUtil.dateToString(samplingTimeBegin, DateUtil.YEAR), DateUtil.YEAR);
            Date lastNewSubmitTime = (Date) sample[1];
            Long tstamp = lastNewSubmitTime.getTime() - samplingTimeBegin.getTime();
            if (tstamp < overDueDays * 24 * 60 * 60 * 1000) {
                promptness++;
            }
        }

        stat.setTotal(samples.size());
        stat.setPromptness(promptness);
        if (samples.size() > 0) {
            BigDecimal rate = BigDecimal.valueOf(promptness).divide(BigDecimal.valueOf(samples.size()), 2, BigDecimal.ROUND_HALF_EVEN).multiply(new BigDecimal(100));
            stat.setRate(rate.toString() + "%");
        }

        String jsonValue = JsonStream.serialize(Collections.singletonList(stat));
        if (cacheTimeout > 0) {
            redisTemplate.opsForValue().set(redisKey, jsonValue, cacheTimeout, TimeUnit.SECONDS);
        } else {
            redisTemplate.opsForValue().set(redisKey, jsonValue);
        }
    }

    /**
     * 缓存业务量趋势数据
     *
     * @param cacheTimeout 过期秒数
     * @param redisKey     key名
     */
    private void cacheBusiness(Integer cacheTimeout, String redisKey) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.MONTH, -12);
        String startTime = DateUtil.dateToString(calendar.getTime(), DateUtil.YEAR);
        Date start = DateUtil.stringToDate(startTime, DateUtil.YEAR);
        List<Object[]> projects = countProjectNumGroupByInceptTimeAndProjectTypeId(start, PrincipalContextUser.getPrincipal().getOrgId());

        String endTime = DateUtil.dateToString(new Date(), DateUtil.YEAR).substring(0, 7) + "-01";
        Date end = DateUtil.stringToDate(endTime, DateUtil.YEAR);

        Map<String, List<Object[]>> projectMap = projects.stream().collect(Collectors.groupingBy(p -> (String) p[2]));
        Set<String> projectTypeIdSet = projectMap.keySet();
        List<DtoProjectType> projectTypeList = projectTypeService.findRedisByIds(new ArrayList<>(projectTypeIdSet));
        List<DtoHomeBusinessTrend> trendList = new ArrayList<>();
        for (String projectTypeId : projectMap.keySet()) {
            DtoProjectType projectType = projectTypeService.findOne(projectTypeId);
            DtoHomeBusinessTrend trend = new DtoHomeBusinessTrend();
            trend.setProjectTypeId(projectTypeId);
            if (projectType == null) {
                continue;
            }

            List<DtoHomeTrendDetail> detailList = new ArrayList<>();
            for (Object[] project : projectMap.get(projectTypeId)) {
                String month = project[0].toString() + "-" + project[1].toString();
                Date monthTime = DateUtil.stringToDate(month + "-1", DateUtil.YEAR);
                Integer times = Integer.valueOf(project[3].toString());

                DtoHomeTrendDetail detail = new DtoHomeTrendDetail(monthTime, times);
                detailList.add(detail);
            }
            trend.setProjectCount(detailList.stream().map(DtoHomeTrendDetail::getCount).reduce(0, (sum, item) -> sum + item));
            trend.setDetail(detailList);
            trend.fillDetail(start, end);
            Optional<DtoProjectType> projectTypeOptional = projectTypeList.parallelStream().filter(p -> projectTypeId.equals(p.getId()))
                    .findFirst();
            projectTypeOptional.ifPresent(p -> trend.setProjectTypeName(p.getName()));
            trend.getDetail().sort(Comparator.comparing(DtoHomeTrendDetail::getTime));
            trendList.add(trend);
        }
        trendList.sort(Comparator.comparing(DtoHomeBusinessTrend::getProjectCount).reversed());
        String jsonValue = JsonStream.serialize(trendList);
        if (cacheTimeout > 0) {
            redisTemplate.opsForValue().set(redisKey, jsonValue, cacheTimeout, TimeUnit.SECONDS);
        } else {
            redisTemplate.opsForValue().set(redisKey, jsonValue);
        }
    }

    /**
     * 缓存样品数趋势数据
     *
     * @param cacheTimeout 过期秒数
     * @param redisKey     key名
     */
    private void cacheSampleNum(Integer cacheTimeout, String redisKey) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.MONTH, -12);
        String startTime = DateUtil.dateToString(calendar.getTime(), DateUtil.YEAR);
        Date start = DateUtil.stringToDate(startTime, DateUtil.YEAR);
        List<Object[]> samples = countSampleNumGroupBySamplingTimeBeginAndSampleTypeId(start, PrincipalContextUser.getPrincipal().getOrgId());

        String endTime = DateUtil.dateToString(new Date(), DateUtil.YEAR).substring(0, 7) + "-01";
        Date end = DateUtil.stringToDate(endTime, DateUtil.YEAR);

        List<String> sampleTypeIds = samples.stream().map(p -> (String) p[2]).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypes = sampleTypeIds.size() > 0 ? sampleTypeService.findRedisByIds(sampleTypeIds) : new ArrayList<>();
        Map<String, String> sampleTypeMap = sampleTypes.stream().collect(Collectors.toMap(DtoSampleType::getId, DtoSampleType::getParentId));

        Map<String, List<Object[]>> sampleMap = samples.stream().collect(Collectors.groupingBy(p -> sampleTypeMap.getOrDefault(p[2], "")));

        List<DtoHomeSampleNumTrend> trendList = new ArrayList<>();
        for (String sampleTypeId : sampleMap.keySet()) {
            if (StringUtils.isNotNullAndEmpty(sampleTypeId)) {
                DtoSampleType sampleType = sampleTypeService.findOne(sampleTypeId);
                if (sampleType == null) {
                    continue;
                }
                DtoHomeSampleNumTrend trend = new DtoHomeSampleNumTrend();
                trend.setSampleTypeId(sampleTypeId);
                trend.setSampleTypeName(sampleType.getTypeName());

                List<DtoHomeTrendDetail> detailList = new ArrayList<>();

                Map<String, List<Object[]>> timeMap = sampleMap.get(sampleTypeId).stream().collect(Collectors.groupingBy(p -> p[0].toString() + "-" + p[1].toString()));
                for (String month : timeMap.keySet()) {
                    Date monthTime = DateUtil.stringToDate(month + "-01", DateUtil.YEAR);
                    Integer times = timeMap.get(month).stream().map(p -> Integer.valueOf(p[3].toString())).reduce(0, (sum, item) -> sum + item);

                    DtoHomeTrendDetail detail = new DtoHomeTrendDetail(monthTime, times);
                    detailList.add(detail);
                }
                trend.setSampleCount(detailList.stream().map(DtoHomeTrendDetail::getCount).reduce(0, (sum, item) -> sum + item));
                trend.setDetail(detailList);
                trend.fillDetail(start, end);
                trend.getDetail().sort(Comparator.comparing(DtoHomeTrendDetail::getTime));
                trendList.add(trend);
            }
        }
        trendList.sort(Comparator.comparing(DtoHomeSampleNumTrend::getSampleCount).reversed());
        String jsonValue = JsonStream.serialize(trendList);
        if (cacheTimeout > 0) {
            redisTemplate.opsForValue().set(redisKey, jsonValue, cacheTimeout, TimeUnit.SECONDS);
        } else {
            redisTemplate.opsForValue().set(redisKey, jsonValue);
        }
    }

    //#endregion

    @Override
    public Integer findCacheMessageTotal(String dtBegin, String dtEnd) {
        String moduleCode = EnumLIM.EnumHomeTaskModule.消息.getValue();
        //读取到配置的模块
        List<ProjectModule> projectModules = projectTaskConfig.getProjectModules();
        Integer total = null;
        ProjectModule projectModule = projectModules.stream().filter(p -> p.getModuleCode().equals(moduleCode)).findFirst().orElse(null);
        String userId = PrincipalContextUser.getPrincipal().getUserId();
        String orgId = PrincipalContextUser.getPrincipal().getOrgId();
        String redisKey = getRedisKey(userId,
                orgId,
                projectModule.getValue(), projectModule.getModuleCode());
        Object object = redisTemplate.opsForValue().get(redisKey);
        if (object != null && !object.equals("")) {
            total = JsonIterator.deserialize(object.toString(), Integer.class);
        }
        return total;
    }


    /**
     * 首页待办数量
     *
     * @param orgId  组织机构id
     * @param userId 用户id
     * @return 首页待办
     */
    @Override
    public List<DtoHomePendingNo> findHomePendingNo(String orgId, String userId) {
        List<DtoHomePendingNo> homePendingNoList = homePendingNoRepository.findByOrgIdAndUserId(orgId, userId);
        Map<String, DtoHomePendingNo> homePendingNoMap = homePendingNoList.stream().collect(Collectors.toMap(DtoHomePendingNo::getModuleCode, p -> p));
        List<ProjectModule> projectModules = projectTaskConfig.getProjectModules();

        List<DtoHomePendingNo> resHomePendingNoList = new ArrayList<>();
        for (ProjectModule projectModule : projectModules) {
            List<String> bindingModules = projectModule.getBindingModules();
            DtoHomePendingNo dtoHomePendingNo = homePendingNoMap.get(projectModule.getModuleCode());
            if (StringUtil.isNull(dtoHomePendingNo)) {
                dtoHomePendingNo = new DtoHomePendingNo();
            }
            int nums = 0;
            if (StringUtil.isNotEmpty(bindingModules)) {
                for (String bindingModule : bindingModules) {
                    DtoHomePendingNo pendingNo = homePendingNoMap.get(bindingModule);
                    if (StringUtil.isNotNull(pendingNo)) {
                        nums += pendingNo.getNums();
                    }
                }
            }
            dtoHomePendingNo.setNums(StringUtil.isNotNull(dtoHomePendingNo.getNums()) ? dtoHomePendingNo.getNums() + nums : nums);
            dtoHomePendingNo.setModuleCode(projectModule.getModuleCode());
            resHomePendingNoList.add(dtoHomePendingNo);
        }
        // 筛选实验室分析数据
        List<DtoHomePendingNo> homePendingNos = homePendingNoList.stream().filter(p -> EnumLIM.EnumHomeTaskModule.实验室审核.getValue().equals(p.getModuleCode()) ||
                EnumLIM.EnumHomeTaskModule.实验室待检.getValue().equals(p.getModuleCode()) ||
                EnumLIM.EnumHomeTaskModule.实验室检测.getValue().equals(p.getModuleCode())).collect(Collectors.toList());
        int count = homePendingNos.stream().mapToInt(DtoHomePendingNo::getNums).sum();
        DtoHomePendingNo dtoHomePendingNo = new DtoHomePendingNo();
        dtoHomePendingNo.setNums(count);
        dtoHomePendingNo.setModuleCode(EnumLIM.EnumHomeTaskModule.实验室分析.getValue());
        resHomePendingNoList.add(dtoHomePendingNo);
        return resHomePendingNoList;
    }

    @Override
    public void findFlyway(PageBean<DtoSchemaVersionTemp> pageBean, DtoSchemaVersionTemp schemaVersionTemp) {
        int pageNo = pageBean.getPageNo();
        int rowsPerPage = pageBean.getRowsPerPage();
        StringBuilder sql = new StringBuilder("select installed_rank, version, script, installed_on, success from schema_version where 1 = 1");
        Map<String, Object> map = new HashMap<>();
        if (StringUtil.isNotEmpty(schemaVersionTemp.getInstalledOn())) {
            sql.append(" and  DATE_FORMAT(installed_on, '%Y-%m-%d') = :installedOn");
            map.put("installedOn", schemaVersionTemp.getInstalledOn());
        }
        if (StringUtil.isNotNull(schemaVersionTemp.getSuccess())) {
            sql.append(" and  success = :success");
            map.put("success", schemaVersionTemp.getSuccess());
        }
        sql.append(" order by installed_on desc,installed_rank");
        List<DtoSchemaVersionTemp> mapList = namedParameterJdbcTemplate.query(sql.toString(), map, (res, i) -> {
            DtoSchemaVersionTemp versionTemp = new DtoSchemaVersionTemp();
            versionTemp.setInstalledRank(res.getInt("installed_rank"));
            versionTemp.setVersion(res.getString("version"));
            String script = res.getString("script");
            versionTemp.setScript(script.contains(".") ? script.substring(0, script.indexOf(".")) : script);
            versionTemp.setInstalledOn(DateUtil.dateToString(res.getTimestamp("installed_on"), DateUtil.FULL));
            versionTemp.setSuccess(res.getInt("success"));
            return versionTemp;
        });
        // 处理分页
        pageBean.setRowsCount(mapList.size());
        if (StringUtil.isNotNull(pageNo) && StringUtil.isNotNull(rowsPerPage)) {
            mapList = mapList.stream().skip((long) (pageNo - 1) * rowsPerPage).limit(rowsPerPage).collect(Collectors.toList());
        }
        pageBean.setData(mapList);
    }

    /**
     * 查询公告数据
     *
     * @param pageBean     分页条件
     * @param baseCriteria 查询条件
     */
    private void findNotices(PageBean<Map<String, Object>> pageBean, BaseCriteria baseCriteria) {
        PageBean<Object[]> pb = new PageBean<>();
        pb.setRowsPerPage(pageBean.getRowsPerPage());
        pb.setPageNo(pageBean.getPageNo());
        pb.setSort(pageBean.getSort());
        pb.setEntityName("DtoNotice n");
        pb.setSelect("select n.id,n.title,n.isTop,n.releaseTime");
        commonRepository.findByPage(pb, baseCriteria);
        List<Map<String, Object>> maps = new ArrayList<>();
        List<Object[]> list = pb.getData();
        Iterator<Object[]> projectIte = list.iterator();
        //当前日期
        Calendar nowDateCalendar = Calendar.getInstance();
        nowDateCalendar.setTime(new Date());
        while (projectIte.hasNext()) {
            Map<String, Object> map = new HashMap<>();
            Object[] obj = projectIte.next();
            String id = (String) obj[0]; //公告id
            String title = (String) obj[1]; //标题
            Boolean isTop = (Boolean) obj[2]; //是否置顶
            Date releaseTime = (Date) obj[3];//发布时间
            Boolean isNew = false;//是否最新消息
            Calendar dd = Calendar.getInstance();//定义日期实例
            dd.setTime(releaseTime);//设置发布日期
            dd.add(Calendar.DAY_OF_YEAR, 2);
            if (dd.compareTo(nowDateCalendar) >= 0) {
                isNew = true;
            }
            map.put("id", id);
            map.put("title", title);
            map.put("isTop", isTop);
            map.put("releaseTime", releaseTime);
            map.put("isNew", isNew);
            maps.add(map);
        }
        pageBean.setData(maps);
        pageBean.setRowsCount(pb.getRowsCount());
    }

    /**
     * 查询我的审批任务
     *
     * @param page       分页条件
     * @param param      查询参数
     * @param moduleCode 模块编码
     */
    private void findOaTask(PageBean<DtoOATask> page, DtoOATaskQuery param, String moduleCode) {
        page.setSort("submitTime-");
        page.setEntityName("DtoOATask task");
        page.setSelect("select task");
        page.setRowsPerPage(Integer.MAX_VALUE);
        OATaskCriteria criteria = new OATaskCriteria();
        criteria.setParam(param);
        if (moduleCode.equals(EnumLIM.EnumHomeTaskModule.我已发起.getValue())) {
            criteria.setQueryType(EnumPRO.EnumOATaskQueryType.我已发起);
        } else if (moduleCode.equals(EnumLIM.EnumHomeTaskModule.待我审批.getValue())) {
            criteria.setNotStatus(Collections.singletonList(EnumPRO.EnumOATaskStatus.新建.getValue()));
            criteria.setQueryType(EnumPRO.EnumOATaskQueryType.待我审批);
        }
        if (moduleCode.equals(EnumLIM.EnumHomeTaskModule.我已审批.getValue())) {
            page.setEntityName("DtoOATask task, DtoOATaskHandleLog log");
            criteria.setNotStatus(Collections.singletonList(EnumPRO.EnumOATaskStatus.新建.getValue()));
            criteria.setQueryType(EnumPRO.EnumOATaskQueryType.我已审批);
        }
        commonRepository.findByPage(page, criteria);
    }

    /**
     * 通用的查询
     *
     * @param userId        用户id
     * @param orgId         组织机构id
     * @param projectModule 模块
     * @return 返回相应的数据
     */
    private Integer find(String userId,
                         String orgId,
                         ProjectModule projectModule) {

        List<String> outTypeIds = new ArrayList<>();
        List<DtoProjectType> projectTypeList = projectTypeService.findByTypeCode("HJ");
        projectTypeList.addAll(projectTypeService.findByTypeCode("WR"));

        String moduleCode = projectModule.getModuleCode();
        Map<String, Object> values = new HashMap<>();

        String[] conditionExtend = conditionExtend(values, moduleCode, projectModule, userId, outTypeIds, projectTypeList);
        String entityName = conditionExtend[0];
        String condition = conditionExtend[1];
        StringBuilder stringBuilder = new StringBuilder("select count(a.id) from " + entityName);
        stringBuilder.append(" where 1=1");
        stringBuilder.append(" and a.orgId=:orgId");
        if (StringUtils.isNotNullAndEmpty(condition)) {
            stringBuilder.append(" and " + condition);
        }
        values.put("orgId", orgId);

        List<Long> list = commonRepository.find(stringBuilder.toString(), values);

        Integer num = 0;
        if (list.size() > 0) {
            num = Integer.valueOf(list.get(0).toString());
        }
        return num;
    }

    /**
     * 个性化获取查询条件以及实体
     *
     * @param values          sql参数
     * @param moduleCode      模块编码
     * @param projectModule   模块
     * @param userId          用户id
     * @param outTypeIds      项目类型id集合
     * @param projectTypeList 项目类型list
     * @return 查询条件以及实体
     */
    protected String[] conditionExtend(Map<String, Object> values, String moduleCode, ProjectModule projectModule, String userId, List<String> outTypeIds, List<DtoProjectType> projectTypeList) {

        String condition = "";

        EnumLIM.EnumHomeTaskModule enumProjectModule = EnumLIM.EnumHomeTaskModule.getByValue(moduleCode);

        String entityName = enumProjectModule.getEntityName();

        if (projectTypeList.size() > 0) {
            outTypeIds.addAll(projectTypeList.stream().map(DtoProjectType::getId).distinct().collect(Collectors.toList()));
        }

        condition = EnumProUtil.findHomeTaskQueryCondition(enumProjectModule, projectModule, userId, values, outTypeIds);


        return new String[]{entityName, condition};
    }

    /**
     * 获取包括绑定模块的所有模块
     *
     * @param bindingModules 绑定的其他模块
     * @param moduleCode     当前模块
     * @param userId         当前用户id
     * @return 所有模块
     */
    protected List<String> getNewModules(List<String> bindingModules, String moduleCode, String userId) {
        List<String> newModules = new ArrayList<>();
        for (String bindingModule : bindingModules) {
            EnumLIM.EnumHomeTaskModule module = EnumLIM.EnumHomeTaskModule.getByValue(bindingModule);
            if (StringUtil.isNotNull(module)) {
                if (!StringUtils.isNotNullAndEmpty(module.getAuthCode()) || authorizeService.haveActionPermission(userId, module.getAuthCode())) {
                    newModules.add(bindingModule);
                }
            }
        }
        if (!newModules.contains(moduleCode)) {
            newModules.add(moduleCode);
        }
        return newModules;
    }

    /**
     * 获取指定的rediskey
     *
     * @param userId     用户id
     * @param orgId      组织机构id
     * @param value      value 是按人员还是按权限
     * @param moduleCode 模块编码
     * @return 返回指定key
     */
    protected String getRedisKey(String userId, String orgId, Integer value, String moduleCode) {
        String redisKey;
        if (value.equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
            //说明按人员进行过滤
            redisKey = String.format("PRO:%s:%s:%s", orgId, userId, moduleCode);
        } else {
            redisKey = String.format("PRO:%s:%s", orgId, moduleCode);
        }
        return redisKey;
    }

    /**
     * 按登记日期获取不同类型的项目个数分组
     *
     * @param inceptTime 登记日期起
     * @return 进行分组得到相应的项目数
     */
    private List<Object[]> countProjectNumGroupByInceptTimeAndProjectTypeId(Date inceptTime, String orgId) {
        StringBuilder stringBuilder = new StringBuilder("select YEAR(inceptTime),MONTH(inceptTime),projectTypeId,");
        stringBuilder.append("count(id) as times  from DtoProject ");
        stringBuilder.append(" where orgId = :orgId ");
        stringBuilder.append(" and inceptTime >= :inceptTime and isDeleted = 0");
        stringBuilder.append(" group by YEAR(inceptTime),MONTH(inceptTime),projectTypeId");
        Map<String, Object> values = new HashMap<>();
        values.put("orgId", orgId);
        values.put("inceptTime", inceptTime);
        return commonRepository.find(stringBuilder.toString(), values);
    }

    /**
     * 按采样日期获取不同类型的样品个数分组
     *
     * @param samplingTimeBegin 采样日期起
     * @param orgId             组织机构id
     * @return 进行分组得到相应的样品数
     */
    private List<Object[]> countSampleNumGroupBySamplingTimeBeginAndSampleTypeId(Date samplingTimeBegin, String orgId) {
        StringBuilder stringBuilder = new StringBuilder("select YEAR(samplingTimeBegin),MONTH(samplingTimeBegin),sampleTypeId,");
        stringBuilder.append("count(id) as times  from DtoSample ");
        stringBuilder.append(" where orgId = :orgId ");
        stringBuilder.append(" and samplingTimeBegin >= :samplingTimeBegin and isDeleted = 0");
        stringBuilder.append(" and sampleCategory=0");
        stringBuilder.append(" group by YEAR(samplingTimeBegin),MONTH(samplingTimeBegin),sampleTypeId");
        Map<String, Object> values = new HashMap<>();
        values.put("orgId", orgId);
        values.put("samplingTimeBegin", samplingTimeBegin);
        return commonRepository.find(stringBuilder.toString(), values);
    }

    /**
     * 按采样日期获取对应日期的原样个数
     *
     * @param samplingTimeBegin 采样时间
     * @param orgId             组织机构id
     * @return 进行分组得到相应的样品数
     */
    private List<Object[]> countSampleNumGroupBySamplingTimeBegin(Date samplingTimeBegin, String orgId) {
        StringBuilder stringBuilder = new StringBuilder("select YEAR(samplingTimeBegin),MONTH(samplingTimeBegin),DAY(samplingTimeBegin),");
        stringBuilder.append("count(id) as times  from DtoSample as a");
        stringBuilder.append(" where a.orgId = :orgId ");
        stringBuilder.append(" and a.samplingTimeBegin >= :samplingTimeBegin and isDeleted = 0");
        stringBuilder.append(" and a.sampleCategory=0");
        stringBuilder.append(" group by YEAR(samplingTimeBegin),MONTH(samplingTimeBegin),DAY(samplingTimeBegin)");
        Map<String, Object> values = new HashMap<>();
        values.put("orgId", orgId);
        values.put("samplingTimeBegin", samplingTimeBegin);
        return commonRepository.find(stringBuilder.toString(), values);
    }

    private List<Object[]> findSamplingTimeBeginAndLastNewSubmitTime(Date samplingTimeBegin, String orgId) {

        StringBuilder stringBuilder = new StringBuilder("select s.samplingTimeBegin,s.lastNewSubmitTime ");
        stringBuilder.append(" from DtoSample as s,");
        stringBuilder.append(" DtoReceiveSubSampleRecord2Sample as r2s,");
        stringBuilder.append("DtoReceiveSubSampleRecord as rs");
        stringBuilder.append(" where s.id = r2s.sampleId and r2s.receiveSubSampleRecordId = rs.id ");
        stringBuilder.append(" and s.orgId = :orgId and  rs.orgId = :orgId");
        stringBuilder.append(" and s.samplingTimeBegin >= :samplingTimeBegin and s.isDeleted = 0 and s.sampleCategory = 0");
        stringBuilder.append(" and  s.status=:status");
        stringBuilder.append(" and  r2s.isDeleted = 0");
        stringBuilder.append(" and  rs.isDeleted = 0");
        stringBuilder.append(" and bitand(rs.subStatus,1)>0");
        Map<String, Object> values = new HashMap<>();
        values.put("orgId", orgId);
        values.put("samplingTimeBegin", samplingTimeBegin);
        values.put("status", EnumPRO.EnumSampleStatus.样品检毕.toString());
        return commonRepository.find(stringBuilder.toString(), values);
    }

    /**
     * 按采样日期获取对应日期的原样个数
     *
     * @param samplingTimeBegin 采样时间
     * @return 进行分组得到相应的样品数
     */
    public List<Object[]> countAnalyseDataNumGroupBySampleTypeIdAndDataStatus(Date samplingTimeBegin, String orgId) {
        StringBuilder stringBuilder = new StringBuilder("select  a.dataStatus ,t.sampleTypeId ,count(a.id) as times ");
        stringBuilder.append(" from DtoAnalyseData as a,");
        stringBuilder.append("DtoSample as s,");
        stringBuilder.append("DtoTest as t");
        stringBuilder.append(" where a.sampleId = s.id and  a.testId = t.id");
        //stringBuilder.append(" and a.orgId = :orgId  and s.orgId = :orgId and t.orgId = :orgId ");
        stringBuilder.append(" and s.samplingTimeBegin >= :samplingTimeBegin and s.isDeleted = 0 and a.isDeleted = 0");
        stringBuilder.append(" group by a.dataStatus,t.sampleTypeId");
        Map<String, Object> values = new HashMap<>();
        values.put("samplingTimeBegin", samplingTimeBegin);
        return commonRepository.find(stringBuilder.toString(), values);
    }
}