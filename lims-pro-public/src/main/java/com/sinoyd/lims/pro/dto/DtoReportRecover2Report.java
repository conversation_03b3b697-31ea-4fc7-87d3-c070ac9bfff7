package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.ReportRecover2Report;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * Dtoreportrecover2report实体
 * <AUTHOR>
 * @version V1.0.0 2022/4/14
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_ReportRecover2Report")
 @Data
 @DynamicInsert
 public  class DtoReportRecover2Report extends ReportRecover2Report {
   private static final long serialVersionUID = 1L;
 }