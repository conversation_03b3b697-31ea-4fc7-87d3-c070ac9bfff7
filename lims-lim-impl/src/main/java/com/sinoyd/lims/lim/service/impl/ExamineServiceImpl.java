package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;

import com.sinoyd.lims.lim.dto.lims.DtoExamine;
import com.sinoyd.lims.lim.dto.lims.DtoExamineType;
import com.sinoyd.lims.lim.dto.lims.DtoExamineTypeRecord;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.ExamineRepository;
import com.sinoyd.lims.lim.repository.lims.ExamineTypeRecordRepository;
import com.sinoyd.lims.lim.repository.lims.ExamineTypeRepository;
import com.sinoyd.lims.lim.service.ExamineService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 考核管理操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/09/14
 * @since V100R001
 */
@Service
public class ExamineServiceImpl extends BaseJpaServiceImpl<DtoExamine, String, ExamineRepository> implements ExamineService {

    @Autowired
    private ExamineTypeRepository examineTypeRepository;

    @Autowired
    private ExamineTypeRecordRepository examineTypeRecordRepository;

    @Override
    public DtoExamine save(DtoExamine entity) {
        if(StringUtil.isEmpty(entity.getFullChargePeopleName())){
            entity.setFullChargePeopleName(entity.getInspectedPerson());
        }
        if(StringUtil.isNull(entity.getStatus())){
            entity.setStatus(EnumLIM.EnumExamineStatus.编辑中.getValue());
        }
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoExamine updateExamineStatus(String id, Integer status,String opinion) {
        DtoExamine entity = repository.findOne(id);
        if(StringUtil.isNull(entity)){
            throw new BaseException("未查询到考核数据！");
        }
        if(StringUtil.isNotEmpty(opinion)){
            entity.setAuditOpinion(opinion);
        }
        entity.setStatus(status);
        return repository.save(entity);
    }

    @Override
    @Transactional
    public DtoExamine update(DtoExamine entity) {
        //当任务审核通过之后，该考核任务无法删除、无法修改
        if(EnumLIM.EnumExamineStatus.审核通过.getValue().equals(entity.getStatus())){
            throw new BaseException("任务已审核通过，无法修改！");
        }
        return super.update(entity);
    }

    @Override
    @Transactional
    public DtoExamine copyExamine(String id) {
        DtoExamine origin = repository.findOne(id);
        if(StringUtil.isNull(origin)){
            throw new BaseException("未查询到考核数据！");
        }
        DtoExamine entity = new DtoExamine();
        BeanUtils.copyProperties(origin,entity);
        String newId = UUIDHelper.NewID();
        entity.setId(newId);
        entity.setStatus(EnumLIM.EnumExamineStatus.编辑中.getValue());
        entity.setAuditOpinion(null);
        entity.setCreateDate(new Date());
        entity.setCreator(null);
        entity.setModifier(null);
        entity.setModifyDate(new Date());

        List<DtoExamineType> waitSaveTypeList = new ArrayList<>();
        List<DtoExamineType> allRelatedTypeList = examineTypeRepository.findAllByExamineId(id);
        List<DtoExamineType> primaryTypeList = allRelatedTypeList.stream().filter(t->UUIDHelper.GUID_EMPTY.equals(t.getParentId())).collect(Collectors.toList());
        List<DtoExamineType> secondaryTypeList = allRelatedTypeList.stream().filter(t->!UUIDHelper.GUID_EMPTY.equals(t.getParentId())).collect(Collectors.toList());

        for (DtoExamineType primary:primaryTypeList) {
            DtoExamineType primaryCopy = new DtoExamineType();
            BeanUtils.copyProperties(primary,primaryCopy);
            String newPrimaryTypeId = UUIDHelper.NewID();
            primaryCopy.setExamineId(newId);
            primaryCopy.setCreateDate(new Date());
            primaryCopy.setCreator(null);
            primaryCopy.setModifier(null);
            primaryCopy.setModifyDate(new Date());

            List<DtoExamineType> tempList = secondaryTypeList.stream().filter(s->primary.getId().equals(s.getParentId())).collect(Collectors.toList());
            for (DtoExamineType secondary:tempList ) {
                DtoExamineType secondaryCopy = new DtoExamineType();
                BeanUtils.copyProperties(secondary,secondaryCopy);
                secondaryCopy.setId(UUIDHelper.NewID());
                secondaryCopy.setParentId(newPrimaryTypeId);
                secondaryCopy.setExamineId(newId);
                secondaryCopy.setCreateDate(new Date());
                secondaryCopy.setCreator(null);
                secondaryCopy.setModifier(null);
                secondaryCopy.setModifyDate(new Date());
                waitSaveTypeList.add(secondaryCopy);
            }

            primaryCopy.setId(newPrimaryTypeId);
            waitSaveTypeList.add(primaryCopy);
        }
        examineTypeRepository.save(waitSaveTypeList);
        return repository.save(entity);
    }

    @Override
    public DtoExamine findAttachPath(String id) {
        return repository.findOne(id);
    }



    @Override
    public DtoExamine findOne(String id) {
        DtoExamine entity = repository.findOne(id);
        if(StringUtil.isNull(entity)){
            throw new BaseException("未查询到考核数据！");
        }
        List<DtoExamineType> typeList = examineTypeRepository.findAllByExamineId(id);
        List<DtoExamineType> parentList = typeList.stream().filter(t->UUIDHelper.GUID_EMPTY.equals(t.getParentId())).collect(Collectors.toList());
        for (DtoExamineType pType:parentList) {
            List<DtoExamineType> children = typeList.stream().filter(t->pType.getId().equals(t.getParentId())).collect(Collectors.toList());
            pType.setChildren(children);
        }
        entity.setTypeList(parentList);
        return entity;
    }

    @Override
    @Transactional
    public <K extends Serializable> Integer logicDeleteById(K id) {
        DtoExamine entity = repository.findOne((String)id);
        if(StringUtil.isNull(entity)){
            throw new BaseException("未查询到考核数据！");
        }
        //当任务审核通过之后，该考核任务无法删除、无法修改
        if(EnumLIM.EnumExamineStatus.审核通过.getValue().equals(entity.getStatus())){
            throw new BaseException("任务已审核通过，无法删除！");
        }
        List<DtoExamineType> typeList = examineTypeRepository.findAllByExamineId((String)id);
        List<String> typeIdList = typeList.stream().map(DtoExamineType::getId).collect(Collectors.toList());
        List<DtoExamineTypeRecord> examineTypeRecordRepositoryList = examineTypeRecordRepository.findAllByExamineTypeIdIn(typeIdList);
        for (DtoExamineTypeRecord examineTypeRecord:examineTypeRecordRepositoryList) {
            examineTypeRecord.setIsDeleted(true);
        }
        examineTypeRecordRepository.save(examineTypeRecordRepositoryList);
        for (DtoExamineType examineType:typeList) {
            examineType.setIsDeleted(true);
        }
        examineTypeRepository.save(typeList);
        return super.logicDeleteById(id);
    }

    @Override
    public void findByPage(PageBean<DtoExamine> page, BaseCriteria criteria) {
        page.setEntityName("DtoExamine e ");
        page.setSelect("select e ");
        super.findByPage(page, criteria);
        List<DtoExamine> list = page.getData();
        //所有考核项目
        List<DtoExamineType> dtoExamineTypeList = examineTypeRepository.findAll();
        //拼接受考核人员
        for (DtoExamine examine:list ) {
            List<DtoExamineType> examineTypeList = dtoExamineTypeList.stream().filter(e->examine.getId().equals(e.getExamineId())).collect(Collectors.toList());
            String fullChargePeopleName = buildFullChargePeopleName(examineTypeList,examine);
            examine.setFullChargePeopleName(fullChargePeopleName);
        }
    }

    /**
     * 拼接受考核人全名称
     * @param examineTypeList 考核项目列表
     * @return          全名称
     */
    private String buildFullChargePeopleName(List<DtoExamineType> examineTypeList,DtoExamine examine) {
        List<String> nameList = examineTypeList.stream().map(DtoExamineType::getInspectedPerson).distinct().collect(Collectors.toList());
        nameList.add(examine.getInspectedPerson());
        nameList = nameList.stream().distinct().collect(Collectors.toList());
        return String.join("、",nameList);
    }


    @Override
    public Boolean auditCheck(String id) {
        List<DtoExamineType> typeList = examineTypeRepository.findAllByExamineId(id);
        int count = (int) typeList.stream().filter(t->t.getProgress()<100).count();
        return count==0;
    }

    @Override
    @Transactional
    public int batchDelExamineById(List<String> examineIdList) {
        if(!examineIdList.isEmpty()){
            List<DtoExamine> examineList = repository.findAll(examineIdList);
            for (DtoExamine examine:examineList) {
                //当任务审核通过之后，该考核任务无法删除、无法修改
                if(EnumLIM.EnumExamineStatus.审核通过.getValue().equals(examine.getStatus())){
                    throw new BaseException("任务已审核通过，无法删除！");
                }
            }
            List<DtoExamineType> dtoExamineTypeList = examineTypeRepository.findAllByExamineIdIn(examineIdList);
            List<String> dtoExamineTypeIdList = dtoExamineTypeList.stream().map(DtoExamineType::getId).collect(Collectors.toList());
            if(!dtoExamineTypeIdList.isEmpty()){
                List<DtoExamineTypeRecord> examineTypeRecordRepositoryList = examineTypeRecordRepository.findAllByExamineTypeIdIn(dtoExamineTypeIdList);
                for (DtoExamineTypeRecord examineTypeRecord:examineTypeRecordRepositoryList) {
                    examineTypeRecord.setIsDeleted(true);
                }
                examineTypeRecordRepository.save(examineTypeRecordRepositoryList);
                for (DtoExamineType examineType:dtoExamineTypeList) {
                    examineType.setIsDeleted(true);
                }
                examineTypeRepository.save(dtoExamineTypeList);
            }
        }
        return super.logicDeleteById(examineIdList);
    }
}
