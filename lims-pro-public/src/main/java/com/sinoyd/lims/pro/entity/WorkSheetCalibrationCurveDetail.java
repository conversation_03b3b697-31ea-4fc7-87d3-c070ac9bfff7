package com.sinoyd.lims.pro.entity;

import com.sinoyd.boot.common.util.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;


/**
 * WorkSheetCalibrationCurveDetail实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="WorkSheetCalibrationCurveDetail")
 @Data
 public  class WorkSheetCalibrationCurveDetail implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  WorkSheetCalibrationCurveDetail() {
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
    * 检测单校准曲线id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("检测单校准曲线id")
	private String workSheetCalibrationCurveId;

    /**
    * 分析编号
    */
    @Column(length=50)
    @ApiModelProperty("分析编号")
    @Length(message = "分析编号{validation.message.length}", max = 50)
	private String analyseCode;

    /**
    * 标准溶液加入体积
    */
    @Column(length=50)
    @ApiModelProperty("标准溶液加入体积")
    @Length(message = "标准溶液加入体积{validation.message.length}", max = 50)
	private String addVolume;

    /**
    * 标准物加入量
    */
    @Column(length=50)
    @ApiModelProperty("标准物加入量")
    @Length(message = "标准物加入量{validation.message.length}", max = 50)
	private String addAmount;

    /**
    * 吸光度A
    */
    @Column(length=50)
    @ApiModelProperty("吸光度A")
    @Length(message = "吸光度A{validation.message.length}", max = 50)
	private String absorbance;

    /**
    * 减空白吸光度
    */
    @Column(length=50)
    @ApiModelProperty("减空白吸光度")
    @Length(message = "减空白吸光度{validation.message.length}", max = 50)
	private String lessBlankAbsorbance;

    /**
    * 吸光度B
    */
    @Column(length=50)
    @ApiModelProperty("吸光度B")
    @Length(message = "吸光度B{validation.message.length}", max = 50)
	private String absorbanceB;

    /**
    * 相对偏差
    */
    @Column(length=50)
    @ApiModelProperty("相对偏差")
    @Length(message = "相对偏差{validation.message.length}", max = 50)
	private String relativeDeviation;

    /**
     * 220吸光度
     */
    @Column(length=50)
    @ApiModelProperty("220吸光度")
    @Length(message = "220吸光度{validation.message.length}", max = 50)
    private String aValueTTZ;

    /**
     * 275吸光度
     */
    @Column(length=50)
    @ApiModelProperty("275吸光度")
    @Length(message = "275吸光度{validation.message.length}", max = 50)
    private String aValueTSF;

    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;

    /**
     * 创建人
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

    /**
     * 假删
     */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted=false;
} 