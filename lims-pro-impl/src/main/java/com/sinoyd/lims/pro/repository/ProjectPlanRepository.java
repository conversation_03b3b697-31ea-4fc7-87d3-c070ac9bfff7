package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoProjectPlan;

import java.util.List;


/**
 * ProjectPlan数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
public interface ProjectPlanRepository extends IBaseJpaPhysicalDeleteRepository<DtoProjectPlan, String> {

    /**
     * 根据项目id查询项目计划
     *
     * @param projectId 项目id
     * @return 返回相应的项目计划
     */
    DtoProjectPlan findByProjectId(String projectId);

    /**
     * 根据项目id查询项目计划
     *
     * @param projectIds 项目id集合
     * @return 返回相应的项目计划
     */
    List<DtoProjectPlan> findByProjectIdIn(List<String> projectIds);
}