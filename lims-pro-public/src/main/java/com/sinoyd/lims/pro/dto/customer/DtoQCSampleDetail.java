package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

/**
 * 质控样品详细数据结构
 * <AUTHOR>
 * @version V1.0.0 2020/1/15
 * @since V100R001
 */
@Data
public class DtoQCSampleDetail {

    /**
     * 分析项目名称
     */
    private String reaAnalyzeItemName;


    /**
     * 质控的分析数据id
     */
    private String qcAnalyseDataId= UUIDHelper.GUID_EMPTY;


    /**
     * 原样的分析数据id
     */
    private String assAnalyseDataId= UUIDHelper.GUID_EMPTY;


    /**
     * 质控的分析数据出证结果
     */
    private String qcTestValue;


    /**
     * 原样的分析数据结果
     */
    private String assTestValue;

    /**
     * 检测结果修约后的值
     */
    private String qcTestValueDstr;


    /**
     * 原样的修约后的值
     */
    private String assTestValueDstr;

    /**
     * 质控样检测结果未修约的值
     */
    private String qcTestOrignValue;

    /**
     * 原样的检测结果未修约的值
     */
    private String assTestOrignValue;

    /**
     * 质控的计量单位
     */
    private String qcDimensionName;


    /**
     * 原样的计量单位
     */
    private String assDimensionName;


    /**
     * 质控的分析人员姓名
     */
    private String qcAnalystName;

    /**
     * 原样的分析人员姓名
     */
    private String assAnalystName;


    /**
     * 质控样的分析时间
     */
    private String qcAnalyzeTime;


    /**
     * 原样的分析时间
     */
    private String assAnalyzeTime;

    /**
     *偏差率
     */
    private String deviationRate;

    /**
     * 偏差值
     */
    private String deviation;

    /**
     * 偏差类型
     */
    private String deviationType;

    /**
     * 标准编号
     */
    private String qcCode;

    /**
     * 标准值
     */
    private String standardValue;


    /**
     * 范围
     */
    private String rangeConfig;

    /**
     * 范围要求
     */
    private String rangeLimit;

    /**
     * 是否合格
     */
    private Boolean isPass;


    /**
     * 加入量
     */
    private String qcValue;

    /**
     * 加标体积
     */
    private String qcVolume;

    /**
     *  加标的测得值
     */
    private String qcJBTestValue;

    /**
     * 样值
     */
    private String realSampleTestValue;

    /**
     * 增值
     */
    private String qcAddValue;
}
