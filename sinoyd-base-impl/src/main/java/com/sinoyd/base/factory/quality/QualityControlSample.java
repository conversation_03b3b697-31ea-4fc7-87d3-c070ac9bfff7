package com.sinoyd.base.factory.quality;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.dto.customer.DtoQualityConfig;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.dto.vo.OrderReviseVO;
import com.sinoyd.base.factory.task.QualityControlKind;
import com.sinoyd.frame.service.CalculationService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class QualityControlSample extends QualityControlKind {


    @Override
    public Integer qcTypeValue(){
        return 262144;
    }


    @Override
    public String qcTypeName() {
        return "质控";
    }

    /**
     * 样品类别
     *
     * @param qcGrade 质控类型
     * @return 质控类别
     */
    @Override
    public String getSampleProperty(Integer qcGrade) {
        String sampleProperty = "";
        if (qcGrade.equals(1)) {
            sampleProperty = "现场质控样";
        } else {
            sampleProperty = "实验室质控样";
        }
        return sampleProperty;
    }

    /**
     * 记录
     *
     * @param map 参数
     * @return 记录内容
     */
    @Override
    public String getFormt(Map<String, String> map) {
        return "增加了质控样qcCode";
    }

    /**
     * 样品名称
     * @param folderName 原样名称
     * @param qcGrade 质控类型
     * @return 样品名称
     */
    @Override
    public String getRedFolderName(String folderName, Integer qcGrade) {
        String redFolderName = "";
        if (qcGrade.equals(1)) {
            redFolderName = "质控样";
        } else {
            redFolderName = "室内质控样";
        }
        return redFolderName;
    }

    @Override
    public DtoQualityConfig getQualityConfig(){
        return getConfig("QualityControlSample");
    }

    @Override
    public DtoQualityConfig getQualityConfig(List<OrderReviseVO> orderReviseVOList) {
        return getConfig("QualityControlSample", orderReviseVOList);
    }

    @Override
    public Boolean deviationQualified(DtoQualityControlLimit controlLimit, String value) {
        CalculationService calculationService = SpringContextAware.getBean(CalculationService.class);
        return deviationQualified(controlLimit, value, calculationService);
    }

}
