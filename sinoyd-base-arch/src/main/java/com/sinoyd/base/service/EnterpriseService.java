package com.sinoyd.base.service;

import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.lims.DtoEnterpriseExtend;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 客户管理
 *
 * <AUTHOR>
 * @version v1.0.0 2019/3/5
 * @since V100R001
 */
public interface EnterpriseService extends IBaseJpaService<DtoEnterprise, String> {

    /**
     * 根据用户Id获取有假删的数据
     *
     * @param ids 主键ids
     * @return 返回带有假删的数据
     */
    List<DtoEnterprise> findAllDeleted(List<String> ids);


    /**
     * 含假删的信息
     *
     * @return 返回假删信息
     */
    List<DtoEnterprise> findAllDeleted();

    /**
     * 修改拼音
     *
     * @return 修改拼音
     */
    List<DtoEnterprise> changePinYin();

    DtoEnterprise findAttachPath(String id);

    /**
     * 查询所有的客户，企业类型是客户
     *
     * @return 客户集合
     */
    List<DtoEnterprise> findAllForCustomer();

    /**
     * 查询企业的扩展信息
     *
     * @param entIds 企业id集合
     * @return 企业扩展信息集合
     */
    List<DtoEnterpriseExtend> findEnterpriseExtend(Collection<String> entIds);

    /**
     * 根据选择的企业名称查询最新关联的监管平台甲方
     *
     * @param entName 企业名称
     * @return Map<String, String>
     */
    Map<String, String> findEntName(String entName);


    /**
     * 分页查询供应商V2
     *
     * @param pageBean 分页条件
     * @param criteria 查询条件
     * @return 供应商集合
     */
    List<DtoEnterprise> findEntByPage(PageBean<DtoEnterprise> pageBean, BaseCriteria criteria);

    /**
     * 获取所有排污许可证不为空的企业
     *
     * @return 企业数据
     */
    List<DtoEnterprise> findPollutionDischargeCodeList();
}